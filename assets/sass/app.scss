body {
  margin: 83px 0 2rem 0;
}

img#nav-logo {
  height: 37px;
}

$gray: #55595c !default;
$gray-lighter: #eceeef !default;
$gray-lightest: #f7f7f9 !default;

footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  border-top: 1px solid $gray-lighter;
  background-color: $gray-lightest;
  color: $gray;
  text-align: center;
  z-index: 1000;
}

/**-------Utility CSS Begins--------**/

.cursor-pointer {
  cursor: pointer;
}

/**-------Utility CSS Ends--------**/

/**-------Page Header CSS Begins--------**/

.page-header {
  margin-top: 80px;
  .page-title {
    position: fixed;
    right: 0;
    left: 0;
    top: 60px;
    
    &--banner-top {
      top:155px !important;
    };

    z-index: 998;
    background-color: #fff;
    border-bottom: 1px solid #f5f5f5;
    padding: 5px 5px 2px 10px;
  }
  h2 {
    padding-top: 8px;
    font-weight: 200;
  }
}

.fixed-content {
  &--banner {
    position: relative;
    top: 100px;
  }
}

/**-------<PERSON> Header CSS Ends--------**/

/**-------Gap Claim Dashboard CSS Begins--------**/

.gap-dashboard {
  position: relative;
  .claim-list {
    .__react_component_tooltip.place-top {
      width: 200px !important;
      word-wrap: break-word;
      white-space: normal;
    }
  }
  .claim-list {
    .table td,
    .table th {
      font-size: 13px;
    }
    .table th {
      white-space: nowrap;
      background-color: #f9f9f9;
    }
    .table-striped tbody tr:nth-of-type(odd) {
      background-color: #FFFFFF;
    }
    .table-striped tbody tr:nth-of-type(even) {
      background-color: #f9f9f9;
    }
    .table td.long-text {
      max-width: 130px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

/**-------Gap Claim Dashboard CSS Ends--------**/

/*** This style is added to fix date picker height to keep it uniform with all other input box **/
.date-field {
  height: 100%;
  width: 100% !important;
}

/**------------GAP worksheet attachment stylesheet----------**/
/**This style is added to handle show/hide of delete document icon on hover**/
.document-link {
  margin: 0 0.6rem;
  .delete-document {
    display: none;
  }
}

.document-link:hover {
  border: 1px solid #ccc;
  border-radius: 0.25rem;
  padding: 0 0.15rem;
  .delete-document {
    display: inline-block;
  }
}

/*** This CSS is added to reset the basic Loader position to fix children element's node content alignment issue ***/
.Loader {
  position: static !important;
}

/**------------GAP worksheet attachment stylesheet----------**/

/*** This CSS is added to put alert box at the top of all other html element ***/
.s-alert-box {
  z-index:100000
}

/**-------------Pattern validation for input-----------------**/
/*** This css is added to put text in red color to show that input is not valid ***/
input[pattern]:invalid{
  color:red;
}

/**** This css is added to show long text in notes tab as ellipsis then when user do hover it will expand *****/
.expando-texto {
  overflow:hidden;
  text-overflow:ellipsis;
  white-space:nowrap;
}

.expando-texto:hover {
  overflow:visible;
  white-space: normal;
}

.react-datepicker-popper {
  z-index: 3 !important;
}

.horizentalLineHeaderStyle {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: -30px;
  font-size: 16px;
}

.horizentalLineSpanStyle {
  background: #fff;
  margin: 0 15px;
}

.horizentalLineHeaderStyle::after {
  background: black;
  height: 2px;
  flex: 1;
  content: '';
}

.horizentalLineHeaderStyle.horizentalLineSpanStyle::before {
  background: none;
}
