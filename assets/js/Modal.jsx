import React from "react";
import PropTypes from "prop-types";

export default class Modal extends React.Component {
  static propTypes = {
    visible: PropTypes.bool.isRequired,
    close: PropTypes.func,
    children: PropTypes.node,
    avoidMaxHeight: PropTypes.bool,
    size: PropTypes.oneOf(['extra-large', 'large', 'small', 'medium']),
    hideContentWrapper: PropTypes.bool,
    title: PropTypes.string
  };

  componentDidUpdate = (prevProps, prevState) => {
    this.fixBody();
  };

  componentDidMount = () => {
    this.fixBody();
  };

  componentWillUmount = () => {
    document.body.style.overflow = 'auto';
  };

  fixBody = () => {
    if (this.props.visible) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'auto';
    }
  };

  canClose = () => {
    return typeof this.props.close === 'function';
  };

  onCloseModal = (e) => {
    e.preventDefault();
    this.props.close();
  };

  getModalWidth = () => {
    if(this.props.size && this.props.size === 'extra-large'){
      return 1200;
    }
    else if (this.props.size && this.props.size == 'large') {
      return 900;
    } else if (this.props.size && this.props.size == 'small') {
      return 300;
    } else if (this.props.size && this.props.size == 'medium') {
      return 500;
    } else {
      return 700;
    }
  };

  getModalBackgroundColor = () => {
    if (this.props.hideContentWrapper) {
      return 'rgba(0, 0, 0, 0)';
    } else {
      return 'rgba(255, 255, 255, 1)';
    }
  };

  render = () => {
    if (!this.props.visible) {
      return null;
    }
    var modalStyles = {
      background: this.getModalBackgroundColor(),
      borderWidth: 1,
      display: 'block',
      padding: 10,
      color: '#1a1a1a',
      maxHeight: 'calc(100% - 100px)',
      position: 'fixed',
      top: '50%',
      left: '50%',
      right: 'auto',
      bottom: 'auto',
      transform: 'translate(-50%, -50%)',
      overflow: 'auto',
      zIndex: 10001,
      minWidth: this.getModalWidth()
    };

    return (
      <div>
        { this.renderOverlay() }
        <div ref =  { (modal) => {this.modal = modal;} } style={ modalStyles }>
          { this.renderCloseButton() }
          { this.props.children }
        </div>
      </div>
    );
  };

  renderOverlay = () => {
    var styles = {
      display: 'block',
      position: 'fixed',
      top: 0,
      left: 0,
      bottom: 0,
      right: 0,
      background: 'rgba(50, 50, 50, 0.8)',
      zIndex: 10000
    };
    if (this.canClose()) {
      return <div style={ styles } onClick={ this.onCloseModal }/>;
    } else {
      return <div style={ styles }/>;
    }
  };

  renderTitle = () => {
    if(this.props.title) {
      return (
        <p className="d-inline-block mr-auto ml-4">
          <strong>
            {this.props.title}
          </strong>
        </p>
      );
    }
  };

  renderCloseButton = () => {
    if (this.canClose()) {
      return (
        <div className='row justify-content-end'>
          {this.renderTitle()}
          <div className={
            `d-inline-block
            ${this.props.size === 'extra-large' ? " pr-4" : " col-1"}
            ${this.props.size === 'medium' ? " mr-3" : ""}
           `}>
            <button type="button" className="btn btn-secondary btn-sm cursor-pointer" onClick={ this.onCloseModal }>
              <i className="fa fa-times"/>
            </button>
          </div>
        </div>
      );
    } else {
      return (
        <div className='row'>
          {this.renderTitle()}
        </div>
      );
    }
  };
}

