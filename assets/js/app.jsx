/**
 * Rollbar.js version 2.4.2
 * @type {
 * {accessToken: string - client token,
 * captureUncaught: boolean - uncaught exceptions to Rollbar,
 * captureUnhandledRejections: boolean - uncaught rejections to Rollbar,
 * payload: {
 *     environment: *|string - env of application,
 *     source-map
 *     client: {javascript: {source_map_enabled: boolean, guess_uncaught_frames: boolean}}
 *    }
 *  }
 * }
 */
/* eslint-disable no-undef */
const rollbarConfig = {
  accessToken: 'd0914fe7b61745e1951300a99aab2e08',
  captureUncaught: true,
  captureUnhandledRejections: false,
  payload: {
    environment: process.env.NODE_ENV || 'development',
    client: {
      javascript: {
        source_map_enabled: true,
        guess_uncaught_frames: true
      }
    }
  }
};
/* eslint-enable no-undef */

require('./polyfills.js'); // required for effect

import React from 'react';
import ReactDOM from 'react-dom';
import { Router } from 'react-router';
import routes from './app/routes.jsx';
import gaHistory from './gaHistory.js';
import AppComponent from './AppComponent.jsx';
import Rollbar from 'rollbar';
import 'bootstrap/js/dist/dropdown';
import 'bootstrap/js/dist/tooltip';
import 'bootstrap/js/dist/popover';
import "core-js/stable";
import "regenerator-runtime/runtime";

Rollbar.init(rollbarConfig);

const router = <Router history={ gaHistory('') }>
  <Router components={ AppComponent } path="/">
    { routes }
  </Router>
</Router>;

ReactDOM.render(router, window.document.getElementById('app'));
