import moment from 'moment';
import React from 'react';
import ReactTooltip from 'react-tooltip';
import dateFormat from "./../reusable/Utilities/dateFormat.js";
import accounting from "accounting";
import PropTypes from 'prop-types';

export default class ClaimsList extends React.Component {

  constructor(props) {
    super(props);
    this.renderTableHeader = this.renderTableHeader.bind(this);
    this.renderTableBody = this.renderTableBody.bind(this);
    this.redirectToWorksheet = this.redirectToWorksheet.bind(this);
    this.renderTableBodyRow = this.renderTableBodyRow.bind(this);
  }

  static propTypes = {
    claimList: PropTypes.array.isRequired,
    sortClaimListByName: PropTypes.func.isRequired,
    insuredNameSortOrder: PropTypes.oneOf(['asc', 'desc'])
  };

  static contextTypes = {
    router: PropTypes.object.isRequired
  };

  renderTableHeader() {
    return (
      <tr>
        <th className="cursor-pointer"
          onClick={this.props.sortClaimListByName}>
          Insured Name&nbsp;
          <i className={this.props.insuredNameSortOrder == 'asc' ? "fa fa-caret-up" : "fa fa-caret-down"}/>
        </th>
        <th>Contract #</th>
        <th>Status</th>
        <th>Opened</th>
        <th>$ Amount</th>
        <th>DOL</th>
        <th>Bank</th>
        <th>Loan #</th>
      </tr>
    );
  }

  renderTableBody() {
    return this.props.claimList.map(this.renderTableBodyRow);
  }

  renderTableBodyRow(claimData, index) {
    return (
      <tr key={index}>
        <td>
          <a href="#!"
            onClick={ this.redirectToWorksheet.bind(this, claimData) }
            className="users">
            {claimData['insured_name']}
          </a>
        </td>
        <td>
          <a href="#!"
            onClick={ this.redirectToWorksheet.bind(this, claimData) }>
            {claimData['contract_number']}
          </a>
        </td>
        <td>
          {claimData['status']}
        </td>
        <td>
          {moment(claimData['opened']).format(dateFormat.displayDateFormat)}
        </td>
        <td>
          { accounting.formatMoney(claimData['amount'], '$', 2) }
        </td>
        <td>
          {moment(claimData['date_of_loss']).format(dateFormat.displayDateFormat)}
        </td>
        <td className="cursor-pointer">
          <span data-tip data-for={`bank${index}`}>
            <span className="text-primary">{claimData['bank']}</span>
          </span>
          <ReactTooltip id={`bank${index}`} aria-haspopup='true'>
            <p className="mb-0"><strong>{claimData['bank']}</strong></p>
            <p className="small mb-0">{claimData['bank_address_street1']}</p>
            <p className="small mb-0">{claimData['bank_address_street2']}</p>
            <div className="small">
              <span>{claimData['bank_address_city']}</span>&nbsp;
              <span>{claimData['bank_address_state']}</span>&nbsp;
              <span>{claimData['bank_address_zip']}</span>
            </div>
            <p className="small mb-0">{claimData['bank_vendor_id']}</p>
          </ReactTooltip>
        </td>
        <td>
          {claimData['loan_number']}
        </td>
      </tr>
    );
  }

  redirectToWorksheet(claimData) {
    if (claimData.contract_number && claimData.id) {
      const query = {
        id: claimData.id,
        contract_number: claimData.contract_number
      };
      const route = { pathname: "/gap-claims", query };
      if (!this.context.router.isActive(route)) {
        this.context.router.push(route);
      }
    }
  }

  render() {
    return (
      <div className="claim-list">
        <table className="table table-striped">
          <thead>
            {this.renderTableHeader()}
          </thead>
          <tbody>
            {this.renderTableBody()}
          </tbody>
        </table>
      </div>
    );
  }
}