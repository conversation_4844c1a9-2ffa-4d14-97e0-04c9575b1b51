import React from 'react';
import PageHeader from '../pageHeader/PageHeader.jsx';
import SearchBox from '../reusable/SearchBox/Search.jsx';
import Alert from 'react-s-alert';
import accounting from "accounting";
import ClaimsList from './ClaimsList.jsx';
import { json as ajax } from './../../ajax.js';
import Pagination from './../../Pagination.jsx';
import Loader from 'react-loader-advanced';
import { URLS as apiUrls } from "./../reusable/Utilities/urls";
import { userHasRole } from "./../reusable/Utilities/userHasRole";
import { CONSTANTS } from "./../reusable/Constants/constants";
import PropTypes from 'prop-types';

export default class GapClaimsFinanceDashboard extends React.Component {

  constructor(props) {
    super(props);
    this.state = {
      claimList: [],
      numberOfClaims: undefined,
      totalEstimate: undefined,
      pageLimit: 20,
      userList: [],
      showLoader: false
    };
    this.loadClaimData = this.loadClaimData.bind(this);
    this.currentPage = this.currentPage.bind(this);
    this.currentQ = this.currentQ.bind(this);
    this.currentUserId = this.currentUserId.bind(this);
    this.currentNameSortOrder = this.currentNameSortOrder.bind(this);
    this.currentStatus = this.currentStatus.bind(this);
    this.getClaimOwnersList = this.getClaimOwnersList.bind(this);
    this.setPage = this.setPage.bind(this);
    this.onSearch = this.onSearch.bind(this);
    this.getClaimsByStatus = this.getClaimsByStatus.bind(this);
    this.sortClaimListByName = this.sortClaimListByName.bind(this);
    this.handleClaimOwnerChange = this.handleClaimOwnerChange.bind(this);
    this.renderUserListOptions = this.renderUserListOptions.bind(this);
    this.renderClaimListFilters = this.renderClaimListFilters.bind(this);
    this.renderClaimList = this.renderClaimList.bind(this);
    this.renderPagination = this.renderPagination.bind(this);
  }

  static contextTypes = {
    router: PropTypes.object.isRequired
  };

  static propTypes = {
    user: PropTypes.shape({
      id: PropTypes.number.isRequired,
      first_name: PropTypes.string.isRequired,
      last_name: PropTypes.string.isRequired,
      email: PropTypes.string.isRequired,
      stores: PropTypes.arrayOf(PropTypes.string.isRequired),
      roles: PropTypes.shape({
        Map: PropTypes.object,
      }).isRequired,
    }),
    location: PropTypes.shape({
      query: PropTypes.shape({
        page: PropTypes.string,
        q: PropTypes.string,
        userId: PropTypes.string,
        NameSortOrder: PropTypes.string,
        status: PropTypes.string
      }).isRequired,
    }).isRequired,
  };

  componentDidMount() {
    document.title = 'TCA Portal - GAP Claims';
    this.loadClaimData();
    this.getClaimOwnersList();
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (this.currentPage(nextProps) !== this.currentPage(this.props) ||
      this.currentQ(nextProps) !== this.currentQ(this.props) ||
      this.currentStatus(nextProps) !== this.currentStatus(this.props) ||
      this.currentNameSortOrder(nextProps) !== this.currentNameSortOrder(this.props) ||
      this.currentUserId(nextProps) !== this.currentUserId(this.props)) {
      this.setState({ q: nextProps.location.query.q || "" });
      this.loadClaimData(nextProps);
    }
  }

  loadClaimData(props) {
    if (!props) {
      props = this.props;
    }
    let url = `${apiUrls.gapClaimsFinance}?page=${window.encodeURIComponent(this.currentPage(props))}`;
    if (this.currentQ(props)) {
      url += (`&q=${window.encodeURIComponent(this.currentQ(props).trim())}`);
    }
    if (this.currentStatus(props)) {
      url += (`&status=${window.encodeURIComponent(this.currentStatus(props))}`);
    }
    if (this.currentNameSortOrder(props)) {
      url += (`&order_by_name=${window.encodeURIComponent(this.currentNameSortOrder(props))}`);
    }
    if (this.currentUserId(props)) {
      url += (`&user_id=${window.encodeURIComponent(this.currentUserId(props))}`);
    }

    this.setState({ showLoader: true }, () => {
      ajax(url, {}, {}, (data, status) => {
        if (status === 200) {
          this.setState({
            claimList: data.claims,
            numberOfClaims: data.count,
            totalEstimate:data.totalEstimate,
            showLoader: false
          });
        } else {
          this.setState({ showLoader: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  }

  currentPage(props) {
    if (!props) {
      props = this.props;
    }
    return parseInt(props.location.query.page, 10) || 1;
  }

  currentQ(props) {
    if (!props) {
      props = this.props;
    }
    return props.location.query.q;
  }

  currentUserId(props) {
    if (!props) {
      props = this.props;
    }
    return props.location.query.userId ? props.location.query.userId : 'all';
  }

  currentNameSortOrder(props) {
    if (!props) {
      props = this.props;
    }
    return props.location.query.NameSortOrder || 'asc';
  }

  currentStatus(props) {
    if (!props) {
      props = this.props;
    }
    return props.location.query.status ? props.location.query.status : 'All Active';
  }

  getClaimOwnersList() {
    ajax(apiUrls.userList, {}, {}, (data, status) => {
      if (status === 200) {
        this.setState({ userList: data.users });
      } else {
        Alert.error("Click the browser's Refresh button to reload the user list. If the error continues, contact your system administrator.");
      }
    });
  }

  setPage(page) {
    const query = { page };
    if (this.currentQ()) {
      query.q = this.currentQ();
    }
    if (this.currentStatus()) {
      query.status = this.currentStatus();
    }
    if (this.currentUserId()) {
      query.userId = this.currentUserId();
    }
    if (this.currentNameSortOrder()) {
      query.NameSortOrder = this.currentNameSortOrder();
    }
    const route = { pathname: "/gap-finance-list", query };
    if (!this.context.router.isActive(route)) {
      this.context.router.push(route);
    }
  }

  onSearch(q) {
    if (this.currentQ() !== q) {
      const query = { q, page: 1 };
      if (this.currentStatus()) {
        query.status = this.currentStatus();
      }
      if (this.currentUserId()) {
        query.userId = this.currentUserId();
      }
      if (this.currentNameSortOrder()) {
        query.NameSortOrder = this.currentNameSortOrder();
      }
      const route = { pathname: "/gap-finance-list", query };
      if (!this.context.router.isActive(route)) {
        this.context.router.push(route);
      }
    }
  }

  getClaimsByStatus(event) {
    const query = { status: event.target.value, page: 1 };
    if (this.currentQ()) {
      query.q = this.currentQ();
    }
    if (this.currentUserId()) {
      query.userId = this.currentUserId();
    }
    if (this.currentNameSortOrder()) {
      query.NameSortOrder = this.currentNameSortOrder();
    }
    const route = { pathname: "/gap-finance-list", query };
    if (!this.context.router.isActive(route)) {
      this.context.router.push(route);
    }
  }

  sortClaimListByName() {
    let query;
    let route;
    if (this.currentNameSortOrder() == 'asc') {
      query = { page: 1, NameSortOrder: 'desc' };
      if (this.currentQ()) {
        query.q = this.currentQ();
      }
      if (this.currentStatus()) {
        query.status = this.currentStatus();
      }
      if (this.currentUserId()) {
        query.userId = this.currentUserId();
      }
      route = { pathname: "/gap-finance-list", query };
      if (!this.context.router.isActive(route)) {
        this.context.router.push(route);
      }
    } else {
      query = { page: 1, NameSortOrder: 'asc' };
      if (this.currentQ()) {
        query.q = this.currentQ();
      }
      if (this.currentStatus()) {
        query.status = this.currentStatus();
      }
      if (this.currentUserId()) {
        query.userId = this.currentUserId();
      }
      route = { pathname: "/gap-finance-list", query };
      if (!this.context.router.isActive(route)) {
        this.context.router.push(route);
      }
    }
  }

  handleClaimOwnerChange(e) {
    const query = { page: 1, userId: e.target.value };
    if (this.currentQ()) {
      query.q = this.currentQ();
    }
    if (this.currentStatus()) {
      query.status = this.currentStatus();
    }
    if (this.currentNameSortOrder()) {
      query.NameSortOrder = this.currentNameSortOrder();
    }
    const route = { pathname: "/gap-finance-list", query };
    if (!this.context.router.isActive(route)) {
      this.context.router.push(route);
    }
  }

  renderUserListOptions() {
    if (this.state.userList.length > 0) {
      return this.state.userList.map((user, index) => {
        if (userHasRole(user, CONSTANTS.USER_ROLES.gapClaims) || userHasRole(user, CONSTANTS.USER_ROLES.gapClaimsManager)) {
          return (<option value={user.id} key={user.id}>{`${user.first_name} ${user.last_name}`}</option>);
        }
      });
    }
  }

  renderClaimListFilters() {
    return (
      <div className="d-flex justify-content-center my-4">
        <div className="form-inline row text-center">
          <div className="form-group px-4">
            <label htmlFor="claim-owner" className="pr-1">View: </label>
            <select id='claim-owner' className="form-control" value={ this.currentStatus() }
              onChange={this.getClaimsByStatus}>
              <option value='All Active'>All Active</option>
              <option value='In Inquiry'>In Inquiry</option>
              <option value='In Process'>In Process</option>
              <option value='In Review'>In Review</option>
              <option value='In Finance'>In Finance</option>
              <option value='Closed'>Closed</option>
            </select>
          </div>
          <div className="form-group">
            <label htmlFor="claim-owner" className="pr-1">Assigned to: </label>
            <select id='claim-owner'
              className="form-control"
              value={ this.currentUserId() }
              onChange={this.handleClaimOwnerChange}>
              <option value='all'>All</option>
              {this.renderUserListOptions()}
            </select>
          </div>
          <div className="form-group px-4">
            <strong className="text-muted">{this.state.numberOfClaims ? `${this.state.numberOfClaims} Claims - Total Estimate: ${accounting.formatMoney(this.state.totalEstimate, '$', 2)}` : ""}</strong>
          </div>
        </div>
      </div>
    );
  }

  renderClaimList() {
    if (this.state.claimList.length > 0) {
      return (<ClaimsList claimList={this.state.claimList}
        sortClaimListByName={this.sortClaimListByName}
        insuredNameSortOrder={this.currentNameSortOrder()}/>);
    } else if (this.currentQ()) {
      return (
        <div className="text-center">
          <p>No Results available for this search query</p>
        </div>
      );
    }

  }

  renderPagination() {
    if (this.state.numberOfClaims > this.state.pageLimit) {
      return (
        <div className="pagination-container clearfix col-12 px-0">
          <div className="pull-left my-1">
            <p>Showing {this.currentPage() * this.state.pageLimit - this.state.pageLimit + 1}&nbsp;
              to {this.currentPage() * this.state.pageLimit > this.state.numberOfClaims ? this.state.numberOfClaims : this.currentPage() * this.state.pageLimit}&nbsp;
              of {this.state.numberOfClaims} items
            </p>
          </div>
          <div className="float-right">
            <Pagination page={this.currentPage()} count={this.state.numberOfClaims}
              limit={this.state.pageLimit} setPage={this.setPage}/>
          </div>
        </div>
      );
    }
  }

  render() {
    const spinnerMessage = <p className='text-center'><i className="fa fa-refresh fa-spin"/> Loading...</p>;
    return (
      <Loader show={this.state.showLoader} message={spinnerMessage}>
        <section className="gap-dashboard clearfix">
          <div className="row">
            <div className="col-12">
              <PageHeader pageTitle="GAP Claims - Finance View" user={this.props.user}/>
              <div className="row">
                <div className="form-inline my-2 col-12">
                  <div className="pull-left col-6 px-0">
                    <SearchBox onSearch={this.onSearch}
                      placeholder="Search Name, Contract #, VIN (10)"
                      value={ this.currentQ() }/>
                  </div>
                </div>
              </div>
              {this.renderClaimListFilters()}
              {this.renderClaimList()}
              {this.renderPagination()}
            </div>
          </div>
        </section>
      </Loader>
    );
  }
}