import React from 'react';
import Modal from "../../Modal.jsx";
import PropTypes from 'prop-types';
import { userHasRole } from "./../reusable/Utilities/userHasRole";
import { CONSTANTS } from "./../reusable/Constants/constants";
import dateFormat from "./../reusable/Utilities/dateFormat.js";
import DatePicker from "react-datepicker";
import moment from "moment";

export default class HoldZoneModal extends React.Component {

  static propTypes = {
    displayModal: PropTypes.bool.isRequired,
    onClose: PropTypes.func.isRequired,
    getZones: PropTypes.func.isRequired,
    agentList: PropTypes.array,
    targetAgentId: PropTypes.number,
    handleHoldZone: PropTypes.func.isRequired,
  };

  constructor(props) {
    super(props);
    this.state = {
      targetAgentId: '',
      startDate: '',
      endDate: ''
    };
    this.renderAgentOptions = this.renderAgentOptions.bind(this);
    this.handleDateChange = this.handleDateChange.bind(this);
  }

  renderAgentOptions() {
    if (this.props.agentList && this.props.agentList.length > 0) {
      return this.props.agentList.map((user) => {
        if (userHasRole(user, CONSTANTS.USER_ROLES.autoClaims) && this.props.targetAgentId !== user.id) {
          return (<option value={user.id} key={user.id}>{`${user.first_name} ${user.last_name}`}</option>);
        }
      });
    }
  }

  handleDateChange(key, date) {
    if(date && !(key === 'startDate' &&
      this.state.endDate &&
      moment.utc(date, dateFormat.displayDateFormat) > moment.utc(this.state.endDate, dateFormat.backendDateFormat))) {
      date = date.format(dateFormat.displayDateFormat);
      const toBeUpdated = {};
      toBeUpdated[key] = moment.utc(date, dateFormat.displayDateFormat).format(dateFormat.backendDateFormat);
      this.setState(toBeUpdated);
    }
  }

  render() {
    return (
      <section>
        <Modal visible={ this.props.displayModal }
          close={this.props.onClose}
          title="Hold New Claims">
          <div className="form-group row col-12">
            <div className="col-6 text-right">
              <label className="col-form-label">Start Date:</label>
            </div>
            <span className="input-group col-3">
              <DatePicker
                selected={ this.state.startDate ? moment(this.state.startDate) : '' }
                id={ "hold_zone_start_date" }
                dateFormat={dateFormat.displayDateFormat}
                onChange={ this.handleDateChange.bind(null, "startDate") }
                className="form-control form-control-sm date-field"/>
            </span>
          </div>
          <div className="form-group row col-12">
            <div className="col-6 text-right">
              <label className="col-form-label">End Date:</label>
            </div>
            <span className="input-group col-3">
              <DatePicker
                selected={ this.state.endDate ? moment(this.state.endDate) : '' }
                id={ "hold_zone_end_date" }
                minDate={ moment(this.state.startDate) }
                dateFormat={dateFormat.displayDateFormat}
                onChange={ this.handleDateChange.bind(null, "endDate") }
                className="form-control form-control-sm date-field"/>
            </span>
          </div>
          <div className="form-group row col-12">
            <div className="col-6 text-right">
              <label className="col-form-label">Target Agent:</label>
            </div>
            <div className="col-3">
              <select id='agent-dropdown_addZone'
                className="form-control"
                value={ this.state.targetAgentId }
                onChange={(event)=> {
                  this.setState({ targetAgentId: event.target.value });
                }}>
                <option value=''>Select agent</option>
                {this.renderAgentOptions()}
              </select>
            </div>
          </div>
          <div className="row justify-content-center my-4 mr-3">
            <button className="btn btn-secondary mr-2"
              onClick={() => {
                this.setState({
                  targetAgentId: '',
                  startDate: moment(),
                  endDate: moment()
                }, ()=> {
                  this.props.onClose();
                });
              }}>
              Cancel
            </button>
            <button className="btn btn-primary"
              onClick={() => {
                this.props.handleHoldZone(this.state.targetAgentId, this.state.startDate, this.state.endDate);
                this.setState({
                  targetAgentId: '',
                  startDate: moment(),
                  endDate: moment()
                });
              }}
              disabled={!(this.state.targetAgentId && this.state.startDate && this.state.endDate)}>
              <i className="fa fa-check"/> Save
            </button>
          </div>
        </Modal>
      </section>
    );
  }
}