import React from 'react';
import Modal from "../../Modal.jsx";
import PropTypes from 'prop-types';
import InputBox from "../reusable/InputBox/InputBox.jsx";
import If from './../reusable/If/If.jsx';
import { userHasRole } from "./../reusable/Utilities/userHasRole";
import { CONSTANTS } from "./../reusable/Constants/constants";

export default class AddZoneModal extends React.Component {

  static propTypes = {
    displayModal: PropTypes.bool.isRequired,
    onClose: PropTypes.func.isRequired,
    agentList: PropTypes.array,
    onSubmit: PropTypes.func,
    operationType: PropTypes.oneOf(['addZone', 'updateZone']),
    targetAgentId: PropTypes.number,
    zoneName: PropTypes.string,
    zoneId: PropTypes.number,
    onHold: PropTypes.bool
  };

  constructor(props) {
    super(props);
    this.state = {
      zoneName: this.props.zoneName || '',
      targetAgentId: this.props.targetAgentId || '',
      zoneId: this.props.zoneId || '',
      showUpdateZoneWarning: false
    };
    this.renderAgentDropdown = this.renderAgentDropdown.bind(this);
    this.onSubmit = this.onSubmit.bind(this);
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if ((nextProps.zoneName !== this.state.zoneName || nextProps.targetAgentId !== this.state.targetAgentId) && nextProps.zoneId && nextProps.zoneId !== this.state.zoneId) {
      this.setState({ zoneName: nextProps.zoneName, targetAgentId: nextProps.targetAgentId, zoneId: nextProps.zoneId });
    } else if (nextProps.zoneName !== this.state.zoneName || nextProps.targetAgentId !== this.state.targetAgentId) {
      this.setState({ zoneName: nextProps.zoneName, targetAgentId: nextProps.targetAgentId });
    }
  }

  renderAgentDropdown() {
    if (this.props.agentList.length > 0) {
      return this.props.agentList.map((user) => {
        if (userHasRole(user, CONSTANTS.USER_ROLES.autoClaims) && this.props.targetAgentId !== user.id) {
          return (<option value={user.id} key={user.id}>{`${user.first_name} ${user.last_name}`}</option>);
        }
      });
    }
  }

  onSubmit(type) {
    if (this.props.operationType !== 'addZone' && this.props.onHold && type !== 'updateCancelHold' && parseInt(this.props.targetAgentId) !== parseInt(this.state.targetAgentId)) {
      this.setState({ showUpdateZoneWarning: true });
    } else {
      this.props.onSubmit(this.state.zoneName, this.state.targetAgentId, this.state.zoneId);
      this.setState({
        zoneName: '',
        targetAgentId: '',
        zoneId: '',
        showUpdateZoneWarning: false
      });
    }
  }

  render() {
    return (
      <section>
        <Modal visible={ this.props.displayModal }
          close={this.props.onClose}
          title={this.props.operationType === 'addZone' ? "New Zone" : "Edit Zone" }>
          <If condition={!this.state.showUpdateZoneWarning}>
            <div>
              <div className="col-12">
                <div className="form-group row col-12">
                  <div className="col-6">
                    <label className="col-form-label">Zone Name:</label>
                  </div>
                  <InputBox type="Text"
                    customClass="col-6"
                    value={ this.state.zoneName }
                    onChange={ (value)=> {
                      this.setState({ zoneName: value });
                    }}/>
                </div>
              </div>
              <div className="col-12">
                <div className="form-group row col-12">
                  <div className="col-6">
                    <label className="col-form-label">Target Agent:</label>
                  </div>
                  <div className="col-6">
                    <select id='agent-dropdown_addZone'
                      className="form-control"
                      value={ this.state.targetAgentId }
                      onChange={(event)=> {
                        this.setState({ targetAgentId: event.target.value });
                      }}>
                      <option value=''>Select agent</option>
                      {this.renderAgentDropdown()}
                    </select>
                  </div>
                </div>
              </div>
              <div className="row justify-content-center my-4 mr-3">
                <button className="btn btn-secondary mr-2"
                  onClick={ this.props.onClose }>
                  Cancel
                </button>
                <button className="btn btn-primary"
                  onClick={this.onSubmit.bind(this, 'update')}
                  disabled={!(this.state.zoneName && this.state.targetAgentId)}>
                  <i className="fa fa-check"/> Save
                </button>
              </div>
            </div>
          </If>
          <If condition={this.state.showUpdateZoneWarning}>
            <div>
              <div className="row">
                <div className="col-12 px-4">
                  <p>Updating the owner will remove the current hold. Are you sure you want to continue?</p>
                </div>
              </div>
              <div className="row justify-content-center my-4 mr-3">
                <button className="btn btn-secondary mr-2"
                  onClick={ this.props.onClose }>
                  Cancel
                </button>
                <button className="btn btn-primary"
                  onClick={this.onSubmit.bind(this, 'updateCancelHold')}
                  disabled={!(this.state.zoneName && this.state.targetAgentId)}>
                  <i className="fa fa-check"/> Update
                </button>
              </div>
            </div>
          </If>
        </Modal>
      </section>
    );
  }
}