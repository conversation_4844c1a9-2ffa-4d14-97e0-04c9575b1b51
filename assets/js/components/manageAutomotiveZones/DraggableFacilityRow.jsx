import React from 'react';
import PropTypes from 'prop-types';
import If from '../reusable/If/If.jsx';

export default class DraggableFacilityRow extends React.Component {

  static propTypes = {
    element: PropTypes.string.isRequired,
    index: PropTypes.number.isRequired,
    isSelected: PropTypes.bool.isRequired,
    hasRemoveButton: PropTypes.bool.isRequired,
    onDrag: PropTypes.func,
    updateZoneByFacilities: PropTypes.func,
    selectFacility: PropTypes.func,
    handleDragStart: PropTypes.func
  };

  constructor(props) {
    super(props);
    this.deleteFacility = this.deleteFacility.bind(this);
  }

  deleteFacility() {
    let facilities = [];
    facilities.push(this.props.element);
    this.props.updateZoneByFacilities({ facilities });
  }

  render() {
    return (
      <div id={ this.props.element }
        className={ this.props.isSelected ? "row card card-info border-0 card-primary rounded-0 p-1" : "row card border-0 rounded-0 p-1" }
        key={ this.props.index }
        onDrag={ this.props.onDrag }
        onDragStart={ (e)=> {
          if (e.stopPropagation) {
            e.stopPropagation();
          }
          //e.dataTransfer event handling has been added to support firefox.
          e.dataTransfer.setData('text', 'dragStart');
          this.props.handleDragStart(this.props.element, e);
        } }
        onDragEnd={ (e)=> {
          if (e.stopPropagation) {
            e.stopPropagation();
          }
        } }
        onClick={ this.props.selectFacility.bind(null, this.props.element) }
        draggable="true">
        <div className="col-12">
          <div className="col-1 d-inline-block">
            <i className="fa fa-bars mr-2"/>
          </div>
          <div className="col-8 d-inline-block">
            {this.props.element}
          </div>
          <If condition={ this.props.hasRemoveButton }>
            <div className="col-1 ml-auto d-inline-block">
              <i className="fa fa-times ml-auto cursor-pointer"
                onClick={ this.deleteFacility }/>
            </div>
          </If>
        </div>
      </div>
    );
  }
}