import React from 'react';
import Modal from "../../Modal.jsx";
import PropTypes from 'prop-types';

export default class DeleteZoneModal extends React.Component {

  static propTypes = {
    displayModal: PropTypes.bool.isRequired,
    onClose: PropTypes.func.isRequired,
    zoneList: PropTypes.array,
    onSubmit: PropTypes.func,
  };

  constructor(props) {
    super(props);
    this.state = {
      targetZoneId: ''
    };
    this.renderZoneDropdown = this.renderZoneDropdown.bind(this);
  }

  renderZoneDropdown() {
    if (this.props.zoneList.length > 0) {
      return this.props.zoneList.map((zone) => {
        return (<option value={zone.id} key={zone.id}>{zone.name}</option>);
      });
    }
  }

  render() {
    return (
      <section>
        <Modal visible={ this.props.displayModal }
          close={this.props.onClose}
          title="Delete Zone">
          <div className="col-12">
            <div className="form-group row col-12">
              <div className="col-4 text-right">
                <label className="col-form-label">Zone:</label>
              </div>
              <div className="col-8">
                <select id='agent-dropdown_addZone'
                  className="form-control"
                  value={ this.state.targetZoneId }
                  onChange={(event)=> {
                    this.setState({ targetZoneId: event.target.value });
                  }}>
                  <option value=''>Select Zone</option>
                  {this.renderZoneDropdown()}
                </select>
              </div>
            </div>
          </div>
          <p className="col-12 px-4">
            Deleting this zone will move its facility to the unassigned facility list. Are you sure you want to delete
            this zone?
          </p>
          <div className="row justify-content-center my-4 mr-3">
            <button className="btn btn-secondary mr-2"
              onClick={ this.props.onClose }>
              Cancel
            </button>
            <button className="btn btn-primary"
              onClick={()=> {
                this.props.onSubmit(this.state.targetZoneId);
                this.setState({ targetZoneId: '' });
              }}
              disabled={!this.state.targetZoneId}>
              <i className="fa fa-check"/> Delete Zone
            </button>
          </div>
        </Modal>
      </section>
    );
  }
}