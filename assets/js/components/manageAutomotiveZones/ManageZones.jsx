import React from 'react';
import PageHeader from '../pageHeader/PageHeader.jsx';
import Loader from 'react-loader-advanced';
import PropTypes from 'prop-types';
import AddZoneModal from './AddZoneModal.jsx';
import DeleteZoneModal from './DeleteZoneModal.jsx';
import DraggableFacilityRow from './DraggableFacilityRow.jsx';
import HoldZoneModal from './HoldZoneModal.jsx';
import If from './../reusable/If/If.jsx';
import moment from "moment";
import Alert from 'react-s-alert';
import { json as ajax } from './../../ajax.js';
import { URLS as apiUrls } from "./../reusable/Utilities/urls";
import { userHasRole } from "./../reusable/Utilities/userHasRole";
import { CONSTANTS } from "./../reusable/Constants/constants";
import dateFormat from "./../reusable/Utilities/dateFormat.js";
import InputBox from "./../reusable/InputBox/InputBox.jsx";

export default class ManageZones extends React.Component {

  static contextTypes = {
    router: PropTypes.object.isRequired
  };

  static propTypes = {
    user: PropTypes.shape({
      id: PropTypes.number.isRequired,
      first_name: PropTypes.string.isRequired,
      last_name: PropTypes.string.isRequired,
      email: PropTypes.string.isRequired,
      stores: PropTypes.arrayOf(PropTypes.string.isRequired),
      roles: PropTypes.shape({
        Map: PropTypes.object,
      }).isRequired
    }),
    location: PropTypes.shape({
      query: PropTypes.shape({
        page: PropTypes.string,
        q: PropTypes.string,
        userId: PropTypes.string,
        status: PropTypes.string,
        age: PropTypes.string
      }).isRequired,
    }).isRequired,
  };

  constructor(props) {
    super(props);
    this.state = {
      showLoader: false,
      showUnAssignedFacilitiesLoader: false,
      showUnAssignedAgentsLoader: false,
      showAddZoneModal: false,
      showDeleteZoneModal: false,
      agentList: [],
      unAssignedAgentList: [],
      unAssignedFacilities: [],
      facilitySearchCode: '',
      operationType: '',
      zoneList: [],
      targetAgentId: '',
      zoneName: '',
      zoneId: '',
      selectedFacilities: [],
      sourceZoneId: '',
      selectedHoldZoneId: '',
      displayHoldZone: false,
      onHold: false
    };
    this.renderManageZoneButtons = this.renderManageZoneButtons.bind(this);
    this.onCreateZone = this.onCreateZone.bind(this);
    this.getAgentList = this.getAgentList.bind(this);
    this.onUpdateZone = this.onUpdateZone.bind(this);
    this.onDeleteZone = this.onDeleteZone.bind(this);
    this.getZones = this.getZones.bind(this);
    this.renderZones = this.renderZones.bind(this);
    this.getUnassignedAgents = this.getUnassignedAgents.bind(this);
    this.getUnassignedFacilities = this.getUnassignedFacilities.bind(this);
    this.selectFacility = this.selectFacility.bind(this);
    this.renderFacilities = this.renderFacilities.bind(this);
    this.handleHoldZone = this.handleHoldZone.bind(this);
    this.handleUnholdZone = this.handleUnholdZone.bind(this);
  }

  componentDidMount() {
    document.title = 'TCA Portal - Manage Zones';
    if (!userHasRole(this.props.user, CONSTANTS.USER_ROLES.autoClaimsManager) && userHasRole(this.props.user, CONSTANTS.USER_ROLES.autoClaims)) {
      this.context.router.push('/automotive-claims-list');
    } else if (!userHasRole(this.props.user, CONSTANTS.USER_ROLES.autoClaimsManager)) {
      this.context.router.push('/404-page');
    } else {
      this.getAgentList();
      this.getZones();
      this.getUnassignedAgents();
      this.getUnassignedFacilities();
    }
  }

  scrollOnDrag(e) {
    if (window.innerHeight - e.clientY < 100) {
      window.scrollBy(0, 10);
    } else if (e.clientY < 200) {
      window.scrollBy(0, -10);
    }
  }

  getZones() {
    this.setState({ showLoader: true }, ()=> {
      ajax(apiUrls.zones, {}, {}, (data, status) => {
        if (status === 200) {
          this.setState({ zoneList: data.zones, showLoader: false });
        } else {
          this.setState({ showLoader: false }, ()=> {
            Alert.error("Click the browser's Refresh button to reload the user list. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  }

  getAgentList() {
    ajax(apiUrls.userList, {}, {}, (data, status) => {
      if (status === 200) {
        this.setState({ agentList: data.users });
      } else {
        Alert.error("Click the browser's Refresh button to reload the user list. If the error continues, contact your system administrator.");
      }
    });
  }

  getUnassignedAgents() {
    this.setState({ showUnAssignedAgentsLoader: true }, ()=> {
      ajax(apiUrls.unassignedAgents, {}, {}, (data, status) => {
        if (status === 200) {
          this.setState({ unAssignedAgentList: data.agents, showUnAssignedAgentsLoader: false });
        } else {
          this.setState({ showUnAssignedAgentsLoader: false }, ()=> {
            Alert.error("Click the browser's Refresh button to reload the user list. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  }

  getUnassignedFacilities() {
    this.setState({ showUnAssignedFacilitiesLoader: true }, ()=> {
      let url = apiUrls.zoneFacilities.replace("__zoneId__", 0);
      ajax(`${url}?search=${this.state.facilitySearchCode}`, {}, {}, (data, status) => {
        if (status === 200) {
          this.setState({ unAssignedFacilities: data.facilities, showUnAssignedFacilitiesLoader: false });
        } else {
          this.setState({ showUnAssignedFacilitiesLoader: false }, ()=> {
            Alert.error("Click the browser's Refresh button to reload the user list. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  }

  onCreateZone(zoneName, agentId) {
    let zoneObject = { "name": zoneName, "owner_id": parseInt(agentId) };
    this.setState({ showLoader: true, showAddZoneModal: false }, ()=> {
      ajax(apiUrls.zones, zoneObject, { method: 'POST' }, (data, status) => {
        if (status === 200) {
          this.getZones();
          this.getUnassignedAgents();
          Alert.success("Zone created Successfully.");
        } else {
          this.setState({ showLoader: false }, ()=> {
            Alert.error("Click the browser's Refresh button to reload the user list. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  }

  onUpdateZone(zoneName, agentId, zoneId) {
    let zoneObject = { "name": zoneName, "owner_id": parseInt(agentId) };
    let url = apiUrls.zoneUpdate.replace("__zoneId__", zoneId);
    this.setState({ showLoader: true, showAddZoneModal: false, selectedFacilities: [], sourceZoneId: '' }, ()=> {
      ajax(url, zoneObject, { method: 'PUT' }, (data, status) => {
        if (status === 200) {
          this.getZones();
          this.getUnassignedAgents();
          Alert.success("Zone Updated Successfully.");
        } else {
          this.setState({ showLoader: false }, ()=> {
            Alert.error("Click the browser's Refresh button to reload the user list. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  }

  onDeleteZone(zoneId) {
    let url = apiUrls.zoneUpdate.replace("__zoneId__", zoneId);
    this.setState({ showLoader: true, showDeleteZoneModal: false, selectedFacilities: [], sourceZoneId: '' }, ()=> {
      ajax(url, {}, { method: 'DELETE' }, (data, status) => {
        if (status === 200) {
          this.getZones();
          this.getUnassignedAgents();
          this.getUnassignedFacilities();
          Alert.success("Zone Deleted Successfully.");
        } else {
          this.setState({ showLoader: false }, ()=> {
            Alert.error("Click the browser's Refresh button to reload the user list. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  }

  selectFacility(zoneId, facilityName, e) {
    let selectedFacilities = this.state.selectedFacilities && this.state.selectedFacilities.length > 0 ? this.state.selectedFacilities.slice(0) : [],
      sourceZoneId = this.state.sourceZoneId;
    if (!this.state.sourceZoneId) {
      sourceZoneId = zoneId;
      selectedFacilities.push(facilityName);
    } else if (sourceZoneId !== zoneId) {
      sourceZoneId = zoneId;
      selectedFacilities = [];
      selectedFacilities.push(facilityName);
    } else if (sourceZoneId === zoneId) {
      if (selectedFacilities.indexOf(facilityName) === -1) {
        selectedFacilities.push(facilityName);
      } else {
        selectedFacilities.splice(selectedFacilities.indexOf(facilityName), 1);
      }
    }
    this.setState({ selectedFacilities, sourceZoneId: sourceZoneId });
  }

  handleDragStart(zoneId, facilityName, e) {
    let selectedFacilities = [];
    if (this.state.selectedFacilities && this.state.selectedFacilities.length === 0) {
      selectedFacilities.push(facilityName);
      this.setState({ selectedFacilities, sourceZoneId: zoneId });
    }
  }

  drop(zoneId, e) {
    e.preventDefault();
    if (zoneId !== this.state.sourceZoneId) {
      let facilityObject = { facilities: this.state.selectedFacilities };
      zoneId = zoneId === 'unAssignedZone' ? 0 : zoneId;
      this.updateZoneByFacilities(zoneId, facilityObject);
    }
  }

  updateZoneByFacilities(zoneId, facilityObject) {
    let url = apiUrls.zoneFacilities.replace("__zoneId__", zoneId);
    this.setState({ showLoader: true }, ()=> {
      ajax(url, facilityObject, { method: 'PUT' }, (data, status) => {
        if (status === 200) {
          this.setState({ selectedFacilities: [], sourceZoneId: '' }, () => {
            this.getZones();
            this.getUnassignedFacilities();
            Alert.success("Zone Updated Successfully.");
          });
        } else {
          this.setState({ showLoader: false }, ()=> {
            Alert.error("Click the browser's Refresh button to reload the user list. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  }

  handleHoldZone(targetAgentId, startDate, endDate) {
    let zoneObject = {
      "hold_start_date": startDate,
      "hold_end_date": endDate,
      "temporary_owner_id": parseInt(targetAgentId)
    };
    let url = apiUrls.holdZone.replace('__zoneId__', this.state.selectedHoldZoneId);
    this.setState({ showLoader: true }, ()=> {
      ajax(url, zoneObject, { method: 'PUT' }, (data, status) => {
        if (status === 200) {
          this.setState({
            selectedHoldZoneId: '',
            displayHoldZone: false
          }, ()=> {
            this.getZones();
          });
        } else {
          this.setState({
            showLoader: false,
            selectedHoldZoneId: '',
            displayHoldZone: false
          }, ()=> {
            Alert.error("Click the browser's Refresh button to reload the user list. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  }

  handleUnholdZone(zoneId) {
    let url = apiUrls.unHoldZone.replace('__zoneId__', zoneId);
    this.setState({ showLoader: true }, ()=> {
      ajax(url, {}, { method: 'PUT' }, (data, status) => {
        if (status === 200) {
          this.getZones();
        } else {
          this.setState({
            showLoader: false
          }, ()=> {
            Alert.error("Click the browser's Refresh button to reload the user list. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  }

  renderManageZoneButtons() {
    return (
      <div className="d-inline-block">
        <button className="btn btn-secondary ml-4" onClick={ ()=> {
          this.setState({ showAddZoneModal: true, operationType: 'addZone' });
        } }>
          New Zone
        </button>
        <button className="btn btn-secondary ml-4" onClick={ ()=> {
          this.setState({ showDeleteZoneModal: true });
        } }>
          Delete Zone
        </button>
      </div>
    );
  }

  renderAddUpdateZoneModal() {
    if (this.state.operationType === 'addZone') {
      return (
        <AddZoneModal displayModal={ this.state.showAddZoneModal }
          onClose={ ()=> {
            this.setState({ showAddZoneModal: false });
          } }
          onSubmit={ this.onCreateZone }
          operationType={ this.state.operationType }
          agentList={ this.state.agentList }/>
      );
    } else if (this.state.operationType === 'updateZone') {
      return (
        <AddZoneModal displayModal={ this.state.showAddZoneModal }
          onClose={ ()=> {
            this.setState({ showAddZoneModal: false, targetAgentId: '' });
          } }
          onSubmit={ this.onUpdateZone }
          zoneName={ this.state.zoneName }
          zoneId={ this.state.zoneId }
          onHold={ this.state.onHold }
          targetAgentId={ this.state.targetAgentId }
          operationType={ this.state.operationType }
          agentList={ this.state.agentList }/>
      );
    }
  }

  renderZones() {
    if (this.state.zoneList.length > 0) {
      return this.state.zoneList.map((element, index) => {
        return (
          <div className="row mt-4" key={ element.id }>
            <div className="col-6">
              <p className="text-primary cursor-pointer"
                onClick={ ()=> {
                  this.setState({
                    showAddZoneModal: true,
                    operationType: 'updateZone',
                    targetAgentId: element.owner_id,
                    zoneName: element.name,
                    zoneId: element.id,
                    onHold: element.on_hold
                  });
                } }>
                {element.name}
              </p>
              <p className="pl-2">
                {element.owner_name}
              </p>
              <If condition={ element.on_hold }>
                <div className="pl-2">
                  <p className="small">
                    On hold to {element.temporary_owner_name}
                  </p>
                  <p className="small">
                    Start: {moment(element.on_hold_start, dateFormat.backendDateFormat).format(dateFormat.displayDateFormat)}
                  </p>
                  <p className="small">
                    End: {moment(element.on_hold_end, dateFormat.backendDateFormat).format(dateFormat.displayDateFormat)}
                  </p>
                  <button className="btn btn-secondary ml-4"
                    onClick={ this.handleUnholdZone.bind(this, element.id) }>
                    Cancel Hold
                  </button>
                </div>
              </If>
              <If condition={ !element.on_hold }>
                <button className="btn btn-secondary ml-4"
                  onClick={ ()=> {
                    this.setState({
                      selectedHoldZoneId: element.id,
                      displayHoldZone: true,
                      targetAgentId: element.owner_id,
                    });
                  } }>
                  Hold
                </button>
              </If>
            </div>
            <div className="col-6 card"
              onDragEnter={ (e)=> {
                if (e.stopPropagation) {
                  e.stopPropagation();
                }
              } }
              onDragLeave={ (e)=> {
                if (e.stopPropagation) {
                  e.stopPropagation();
                }
              } }
              onDragOver={ (e)=> {
                if (e.stopPropagation) {
                  e.stopPropagation();
                }
                e.preventDefault();
                return false;
              } }
              onDrop={ this.drop.bind(this, element.id) }>
              {this.renderFacilities(element.facilities, element.id, true)}
            </div>
          </div>
        );
      });
    }
  }

  renderUnAssignedAgents() {
    if (this.state.unAssignedAgentList.length > 0) {
      return this.state.unAssignedAgentList.map((element, index) => {
        return (
          <p className="my-1" key={ index }>
            {element.agent_name}
          </p>
        );
      });
    } else {
      return (
        <p className="my-1">
          No agents available.
        </p>
      );
    }
  }

  renderFacilities(facilityArray, zoneId, hasRemoveButton) {
    if (facilityArray && Array.isArray(facilityArray) && facilityArray.length > 0) {
      return facilityArray.map((element, index) => {
        let isSelected = false;
        if (this.state.selectedFacilities.indexOf(element) !== -1) {
          isSelected = true;
        }
        return (
          <DraggableFacilityRow element={ element }
            key={ index }
            index={ index }
            isSelected={ isSelected }
            onDrag={ this.scrollOnDrag }
            hasRemoveButton={ hasRemoveButton }
            updateZoneByFacilities={ this.updateZoneByFacilities.bind(this, 0) }
            selectFacility={ this.selectFacility.bind(this, zoneId) }
            handleDragStart={ this.handleDragStart.bind(this, zoneId) }/>
        );
      });
    } else {
      return (
        <p className="my-1">
          No facility available.
        </p>
      );
    }
  }

  handleOnFacilityChange = (value) => {
    this.setState({ facilitySearchCode: value });
  };

  onFacilitySearchEnter = (e) => {
    const enterKey = 13;
    if ((e.keyCode === enterKey || e.which === enterKey) && (this.state.facilitySearchCode.length >= 2 || this.state.facilitySearchCode.length === 0)) {
      this.getUnassignedFacilities();
    }
  };

  render() {
    const spinnerMessage = <p className='text-center'><i className="fa fa-refresh fa-spin"/>Loading...</p>;
    const facilityCardStyle = { "overflowY": "auto", "height": "350px" };
    return (
      <Loader show={ this.state.showLoader } message={ spinnerMessage }>
        <section className="claim-worksheet">
          <div className="row gap-dashboard">
            <div className="col-12">
              <PageHeader pageTitle="Manage Zones"
                backButtonText="Back"
                user={this.props.user}
                onBackButtonClick={ ()=> {
                  this.context.router.goBack();
                } }
                renderTemplate={ this.renderManageZoneButtons }/>
              <div className="row mt-3">
                <div className="col-6">
                  {this.renderZones()}
                </div>
                <div className="col-6 mt-4">
                  <div className="row">
                    <div className="col-6">
                      <p className="text-right"><strong>Unassigned Facilities</strong></p>
                    </div>
                    <div className="col-6">
                      <InputBox type="Text"
                        customClass="mb-1"
                        id="facilityCodeSearch_inputBox"
                        placeholder="Search by facility code"
                        value={ this.state.facilitySearchCode ? this.state.facilitySearchCode : "" }
                        onBlur={ this.handleOnFacilityChange }
                        onChange={ this.handleOnFacilityChange }
                        onKeyUp={ this.onFacilitySearchEnter }/>
                      <Loader show={ this.state.showUnAssignedFacilitiesLoader } message={ spinnerMessage }>
                        <div className="card mb-4" style={ facilityCardStyle }
                          onDragEnter={ (e)=> {
                            if (e.stopPropagation) {
                              e.stopPropagation();
                            }
                          } }
                          onDragLeave={ (e)=> {
                            if (e.stopPropagation) {
                              e.stopPropagation();
                            }
                          } }
                          onDragOver={ (e)=> {
                            if (e.stopPropagation) {
                              e.stopPropagation();
                            }
                            e.preventDefault();
                            return false;
                          } }
                          onDrop={ this.drop.bind(this, 'unAssignedZone') }>
                          <div>
                            {this.renderFacilities(this.state.unAssignedFacilities, 'unAssignedZone', false)}
                          </div>
                        </div>
                      </Loader>
                    </div>
                  </div>
                  <div className="row mt-4">
                    <div className="col-6">
                      <p className="text-right"><strong>Unassigned Agents</strong></p>
                    </div>
                    <div className="card col-6 mb-4">
                      {this.renderUnAssignedAgents()}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          {this.renderAddUpdateZoneModal()}
          <DeleteZoneModal displayModal={ this.state.showDeleteZoneModal }
            onClose={ ()=> {
              this.setState({ showDeleteZoneModal: false });
            } }
            onSubmit={ this.onDeleteZone }
            zoneList={ this.state.zoneList }/>
          <HoldZoneModal displayModal={ this.state.displayHoldZone }
            agentList={ this.state.agentList }
            getZones={ this.getZones }
            handleHoldZone={ this.handleHoldZone }
            targetAgentId={ this.state.targetAgentId ? parseInt(this.state.targetAgentId) : 0 }
            onClose={ ()=> {
              this.setState({
                selectedHoldZoneId: '',
                displayHoldZone: false,
                targetAgentId: ''
              });
            } }/>
        </section>
      </Loader>
    );
  }
}
