import React from 'react';
import { json as ajax } from "./../../ajax.js";
import Modal from "../../Modal.jsx";
import Alert from "react-s-alert";
import { URLS as apiUrls } from "./../reusable/Utilities/urls";
import PropTypes from 'prop-types';

export default class AddInsuranceCompanyModal extends React.Component {
  static propTypes = {
    onCloseModal: PropTypes.func.isRequired,
    displayModal: PropTypes.bool.isRequired
  };

  constructor(props) {
    super(props);
    this.state = {
      insuranceCompanyName: '',
    };
  }

  saveInsuranceCompanyName = (event) => {
    event.preventDefault();
    const insuranceCompanyName = this.state.insuranceCompanyName && this.state.insuranceCompanyName.trim();
    if (insuranceCompanyName) {
      ajax(apiUrls.insuranceCompanies, { name: insuranceCompanyName }, { method: 'POST' }, (data, status) => {
        if (status === 200) {
          const companyName = insuranceCompanyName;
          this.setState({ displayModal: false, insuranceCompanyName: '' });
          this.props.onCloseModal(companyName);
        } else if (status === 409) { // if record is duplicate
          Alert.error(data.message);
        } else {
          Alert.error("Click the browser's Refresh button to reload the data. If the error continues, contact your system administrator.");
        }
      });
    }
  };

  onInsuranceNameChange = (event) => {
    this.setState({
      insuranceCompanyName: event.target.value
    });
  };

  onModalClose = () => {
    this.setState({ insuranceCompanyName: '' });
    this.props.onCloseModal();
  };

  render() {
    const { insuranceCompanyName } = this.state;
    const { displayModal } = this.props;
    const name = this.state.insuranceCompanyName && this.state.insuranceCompanyName.trim();
    return (
      <section>
        <Modal visible={ displayModal } close={ this.onModalClose }>
          <div className="row justify-content-center">
            <div className="col-12 my-2">
              <form className="form-inline row justify-content-center">
                <div className="input-group col-8">
                  <input
                    type="text"
                    className="form-control"
                    value={ insuranceCompanyName }
                    onChange={ this.onInsuranceNameChange }
                    placeholder="Add insurance company name"/>
                  <span className="input-group-btn">
                    <button
                      type="submit"
                      className="btn btn-primary"
                      disabled={ !name }
                      onClick={ this.saveInsuranceCompanyName }>
                      Save
                    </button>
                  </span>
                </div>
              </form>
            </div>
          </div>
        </Modal>
      </section>
    );
  }
}