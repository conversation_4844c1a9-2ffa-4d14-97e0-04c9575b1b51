import React from "react";
import { json as ajax } from "./../../ajax.js";
import { URLS as apiUrls } from "./../reusable/Utilities/urls";
import Alert from "react-s-alert";
import PropTypes from "prop-types";
import VoidCheckFinanceInfo from "./VoidCheckFinanceInfo.jsx";
import Modal from "./../../Modal.jsx";
import { CONSTANTS } from "./../reusable/Constants/constants.js";
import ConfirmationModal from "../reusable/ConfirmationModal/ConfirmationModal";

export default class VoidCheck extends React.Component {

  static propTypes = {
    initiateLoader: PropTypes.func,
    onVoidCheckSuccess: PropTypes.func,
    onVoidCheckError: PropTypes.func,
    claimObject: PropTypes.object,
    onDeclineVoid: PropTypes.func,
    showVoidCheckConfirmationModal: PropTypes.bool
  };

  constructor(props) {
    super(props);
    this.state = {
      showConfirmationModal: false,
      reason: '',
      selectedClaimId: '',
      checkNumber: ''
    };
    this.voidCheck = this.voidCheck.bind(this);
    this.onSelect = this.onSelect.bind(this);
    this.renderChildClaimsDetails = this.renderChildClaimsDetails.bind(this);
  }

  voidCheck() {
    this.props.initiateLoader();
    this.props.onDeclineVoid();

    this.setState({ showLoader: true }, () => {
      let url = apiUrls.gapclaimsVoid;
      url = url.replace('__claimId__', this.state.selectedClaimId);
      url = url + "?reason="+this.state.reason;
      ajax(url, {}, {method: 'GET'}, (data, status) => {
        if (status === 200) {
          if (data.checkNumber > 0) {
            this.setState({ showConfirmationModal: true, checkNumber: data.checkNumber, });
          } else {
            // if given check is not reversed in intacct then void a claim in intacct
            this.voidClaim();
          }

        } else {
          this.props.onVoidCheckError();
          Alert.error("Click the Void Check button again. If the error continues, contact your system administrator.");
        }
      });
    });
  }

  voidClaim = () => {
    let url = apiUrls.gapclaimsVoid;
    url = url.replace('__claimId__', this.state.selectedClaimId);
    ajax(url, { 'reason': this.state.reason }, { method: 'PUT' }, (data, status) => {
      if (status === 200) {
        this.props.onVoidCheckSuccess();
      } else {
        this.props.onVoidCheckError();
        Alert.error("Click the Void Check button again. If the error continues, contact your system administrator.");
      }
    });
    this.setState({ showConfirmationModal: false });
  }

  disableLoader = () => {
    this.setState({ showConfirmationModal: false }, this.props.onVoidCheckError);
  }
  onSelect(id) {
    this.setState({ selectedClaimId: id });
  }

  renderChildClaimsDetails() {
    if (this.props.claimObject.child_claims && this.props.claimObject.child_claims.length > 0) {
      return this.props.claimObject.child_claims.map((element, index)=> {
        if (element.status === CONSTANTS.STATUS_MAP.checkVoided || element.status === CONSTANTS.STATUS_MAP.checkWritten) {
          return (
            <VoidCheckFinanceInfo claimId={element.id}
              key={element.id}
              claimStatus={element.status}
              selectedClaimId={this.state.selectedClaimId}
              onSelect={this.onSelect.bind(this, element.id)}/>
          );
        }
      });
    }
  }

  render() {
    return (
      <div>
        <Modal visible={this.props.showVoidCheckConfirmationModal}
          size="small">
          <div className="row">
            <div className="col-12 my-4">
              <p className="text-center">
              Select the check you want to void:
              </p>
            </div>
            <VoidCheckFinanceInfo claimId={this.props.claimObject.id}
              claimStatus={this.props.claimObject.status}
              selectedClaimId={this.state.selectedClaimId}
              onSelect={this.onSelect.bind(this, this.props.claimObject.id)}/>
            {this.renderChildClaimsDetails()}
            <div className="row col-12 justify-content-center">
              <div className="col-10 justify-content-start form-group row">
                <label className="col-4">
                Reason:
                </label>
                <textarea type="text"
                  id="void_textBox"
                  className="form-control form-control-sm col-8"
                  placeholder="Reason"
                  value={this.state.reason}
                  onChange={(event) => {
                    this.setState({ reason: event.target.value });
                  }}/>
              </div>
            </div>
            <div className="col-12 text-center my-4">
              <button type="button"
                className="btn btn-secondary cursor-pointer  mr-3"
                onClick={this.props.onDeclineVoid}>
              Cancel
              </button>
              <button type="button"
                className="btn btn-primary cursor-pointer"
                onClick={this.voidCheck}
                disabled={!(this.state.selectedClaimId && this.state.reason)}>
              Void Check
              </button>
            </div>
          </div>

        </Modal>
        <ConfirmationModal
          confirmButtonText="Yes"
          declineButtonText="No"
          displayConfirmationModal={ this.state.showConfirmationModal }
          displayMessage={`Check ${this.state.checkNumber} shows as voided in IntAcct. Would you like to update Connect to match IntAcct?`}
          onConfirm={ this.voidClaim }
          onDecline={ this.disableLoader }
        />
      </div>
    );
  }
}