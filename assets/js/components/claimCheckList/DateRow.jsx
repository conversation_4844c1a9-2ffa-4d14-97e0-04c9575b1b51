import React from 'react';
import ItemNote from "./ItemNote.jsx";
import DocumentLinkList from "./../reusable/DocumentLinkList/DocumentLinkList.jsx";
import DatePicker from "react-datepicker";
import moment from "moment";
import PropTypes from 'prop-types';

export default class DateRow extends React.Component {

  static propTypes = {
    containerClass: PropTypes.string,
    managerCheckBoxAttribute: PropTypes.string,
    showManagerCheckbox: PropTypes.bool,
    isDisabled: PropTypes.bool,
    labelContainerClass: PropTypes.string.isRequired,
    datePickerContainerClass: PropTypes.string.isRequired,
    labelClass: PropTypes.string,
    hasCheckbox: PropTypes.bool.isRequired,
    checkBoxAttribute: PropTypes.string,
    label: PropTypes.string.isRequired,
    dateAttribute: PropTypes.string.isRequired,
    claimObject: PropTypes.object.isRequired,
    onCursorToggle: PropTypes.func,
    handleDateChange: PropTypes.func.isRequired,
    dateFormat: PropTypes.string.isRequired,
    fieldNotes: PropTypes.any,
    selectedFieldNote: PropTypes.object,
    handleItemNoteClick: PropTypes.func,
    handleMouseEnter: PropTypes.func,
    handleMouseLeave: PropTypes.func,
    handleAttachmentClick: PropTypes.func.isRequired,
    deleteAttachment: PropTypes.func,
    fieldId: PropTypes.number,
    rowType: PropTypes.string.isRequired,
    rowStyle: PropTypes.object.isRequired,
    rowId: PropTypes.string,
    fieldDocs: PropTypes.array,
    isFinanceUser: PropTypes.bool
  };

  constructor(props) {
    super(props);
    this.state = {
      isItemNotesSelected: false
    };
  }

  UNSAFE_componentWillReceiveProps = (nextProps) => {
    if (nextProps.fieldId === nextProps.selectedFieldNote.fieldId) {
      this.setState({
        isItemNotesSelected: true
      });
    } else if (nextProps.fieldId !== nextProps.selectedFieldNote.fieldId) {
      this.setState({
        isItemNotesSelected: false
      });
    }
  };

  getContainerClass = () => {
    let className = "form-group row";
    if (this.props.containerClass) {
      className += ` ${this.props.containerClass}`;
    }
    if (this.state.isItemNotesSelected) {
      className += " bg-faded";
    }
    return className;
  };

  getContainerStyle = () => {
    if (this.state.isItemNotesSelected) {
      return this.props.rowStyle;
    }
  };

  renderLabel = () => {
    if (this.props.hasCheckbox) {
      return this.getLabelWithCheckbox();
    } else {
      return this.getLabel();
    }
  };

  getLabel = () => {
    return (
      <label
        className={ this.props.labelClass ? `col-form-label col-form-label-sm ${this.props.labelClass}` : "col-form-label col-form-label-sm" }>
        { this.props.label }
      </label>
    );
  };

  getLabelWithCheckbox = () => {
    return (
      <label
        className={ this.props.labelClass ? `form-check form-check-label col-form-label col-form-label-sm ${this.props.labelClass}` : "form-check form-check-label col-form-label col-form-label-sm" }>
        <input type="checkbox"
          className="form-check-input"
          id={ `${this.props.rowId}_checkBox` }
          checked={ this.props.claimObject[this.props.checkBoxAttribute] }
          onChange={ this.props.onCursorToggle.bind(null, this.props.checkBoxAttribute) }
          disabled={ this.props.isDisabled }/>
        <span>{ this.props.label }</span>
      </label>
    );
  };

  renderDatePicker = () => {
    const date = this.props.claimObject[this.props.dateAttribute] ? moment.utc(this.props.claimObject[this.props.dateAttribute]).format(this.props.dateFormat) : '';
    return (
      <span className={ `input-group input-group-sm ${this.props.datePickerContainerClass}` }>
        <DatePicker
          selected={ moment(date, this.props.dateFormat) }
          id={ `${this.props.rowId}_dateInputBox` }
          dateFormat={ this.props.dateFormat }
          onChange={ this.props.handleDateChange.bind(null, this.props.dateAttribute) }
          className="form-control form-control-sm date-field" disabled={ this.props.isDisabled }/>
      </span>
    );
  };

  renderManagerCheckbox = (attribute_name) => {
    if (this.props.showManagerCheckbox) {
      return (
        <input type="checkbox"
          id={ `${this.props.rowId}_managerCheckBox` }
          checked={ this.props.claimObject[attribute_name] }
          onChange={ this.props.onCursorToggle.bind(null, attribute_name) }/>
      );
    }
  };

  render() {
    return (
      <div className={ this.getContainerClass() } style={ this.getContainerStyle() }>
        <div className={ this.props.labelContainerClass }>
          { this.renderLabel() }
        </div>
        { this.renderDatePicker() }
        <div className="col-1 px-0">
          { this.renderManagerCheckbox(this.props.managerCheckBoxAttribute) }
          <ItemNote onClick={ this.props.handleItemNoteClick }
            handleMouseEnter={ this.props.handleMouseEnter }
            handleMouseLeave={ this.props.handleMouseLeave }
            fieldNotes={ this.props.fieldNotes }
            rowType={ this.props.rowType }
            rowId={ this.props.rowId }
            fieldId={ this.props.fieldId }
            handleAttachmentClick={ this.props.handleAttachmentClick }/>
        </div>
        <div className="col-6">
          <DocumentLinkList fieldDocs={ this.props.fieldDocs }
            deleteAttachment={ this.props.deleteAttachment }
            isFinanceUser={ this.props.isFinanceUser }/>
        </div>
      </div>
    );
  }
}