import React from 'react';
import InputBoxRow from "./InputBoxRow.jsx";
import If from "./../reusable/If/If.jsx";
import PropTypes from 'prop-types';
import { CONSTANTS } from '../reusable/Constants/constants';
import { URLS as apiUrls } from '../reusable/Utilities/urls';
import Alert from 'react-s-alert';
import { json as ajax } from '../../ajax';
import moment from "moment";
import Loader from "react-loader-advanced";
import accounting from 'accounting';
import dateFormat from "./../reusable/Utilities/dateFormat.js";
import RecoverySubmitModal from "./RecoverySubmitModal.jsx";

export default class RecoveryData extends React.Component {

  static propTypes = {
    claimObject: PropTypes.object.isRequired,
    loadNotes: PropTypes.func.isRequired,
    getFieldNotes: PropTypes.func.isRequired,
    getFieldDocs: PropTypes.func.isRequired,
    selectedFieldNote: PropTypes.object.isRequired,
    handleItemNoteClick: PropTypes.func.isRequired,
    handleMouseEnter: PropTypes.func.isRequired,
    handleMouseLeave: PropTypes.func.isRequired,
    handleAttachmentClick: PropTypes.func.isRequired,
    deleteAttachment: PropTypes.func.isRequired,
    rowType: PropTypes.string.isRequired,
    rowStyle: PropTypes.object.isRequired,
    attachments: PropTypes.object.isRequired,
    notes: PropTypes.object.isRequired,
    isFinanceUser: PropTypes.bool,
    enableRecoveryCase: PropTypes.func.isRequired
  };

  constructor(props) {
    super(props);
    this.state = {
      recoveryData: {
        id: '',
        status: '',
        has_recovery: false,
        recovery_added: '',
        recovery_removed: '',
        check_amount: ''
      },
      showLoader: false,
      showModal: false,
      recoveryReason: ''
    };
  }

  componentDidMount = () => {
    if(this.props.claimObject && this.props.claimObject.id) {
      this.loadRecovery(this.props);
    }
  };

  UNSAFE_componentWillReceiveProps = (nextProps) => {
    if(nextProps.claimObject && nextProps.claimObject.id && nextProps.claimObject.id !== this.props.claimObject.id) {
      this.loadRecovery(nextProps);
    }
  };

  loadRecovery = ({ claimObject }) => {
    this.setState({ showLoader: true }, function () {
      ajax(apiUrls.gapClaimRecovery.replace("__claimId__", claimObject.id), {}, {}, (data, status) => {
        if (status === 200) {
          this.setState({ recoveryData: data.gap_claim, showLoader: false });

          // enable recovery case button
          if(data.gap_claim && data.gap_claim.status) {
            this.props.enableRecoveryCase();
          }
        } else {
          this.setState({ showLoader: false }, ()=> {
            Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  getData = () => {
    const { recoveryData, recoveryReason } = this.state;
    const data = {
      recovery_reason: recoveryReason
    };
    if(recoveryData && !recoveryData.has_recovery) {
      data['has_recovery'] = true;
      data['status'] = CONSTANTS.GAP_RECOVERY_STATUS_MAP.recoveryInquiry.code;
    }
    if(recoveryData && recoveryData.has_recovery && recoveryData.status === CONSTANTS.GAP_RECOVERY_STATUS_MAP.recoveryInquiry.code) {
      data['has_recovery'] = false;
      data['status'] = CONSTANTS.GAP_RECOVERY_STATUS_MAP.noRecovery.code;
    }
    return data;
  };

  onSubmit = () => {
    const data = this.getData();
    this.setState({ showLoader: true, showModal: false, recoveryReason: '' }, function () {
      ajax(apiUrls.gapClaimRecovery.replace("__claimId__", this.props.claimObject.id), data, { method: 'PUT' }, (data, status) => {
        if (status === 200) {
          this.loadRecovery(this.props);
          this.props.loadNotes();
        } else {
          this.setState({ showLoader: false }, ()=> {
            Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  handleReasonChange = (e) => {
    this.setState({ recoveryReason: e.target.value });
  };

  render() {
    const spinnerMessage = <p className='text-center'><i className="fa fa-refresh fa-spin"/> Loading...</p>;
    const COMPONENT_MAP = CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP;
    const RECOVERY_COMPONENT_MAP = CONSTANTS.RECOVERY_WORKSHEET_COMPONENT_MAP;
    return (
      <span>
        <Loader show={ this.state.showLoader } message={ spinnerMessage }>
          <InputBoxRow labelContainerClass="col-5"
            containerClass="justify-content-end"
            hasLabel={ true }
            hasCheckbox={ true }
            isFinanceUser={ this.props.isFinanceUser }
            checkBoxAttribute="has_recovery"
            showManagerCheckbox={ false }
            isDisabled={ this.props.isFinanceUser ||
              (this.state.recoveryData.status === CONSTANTS.GAP_RECOVERY_STATUS_MAP.inRecovery.code ||
                this.state.recoveryData.status === CONSTANTS.GAP_RECOVERY_STATUS_MAP.recovered.code) }
            label="Recovery Review:"
            claimObject={ this.state.recoveryData }
            onCursorToggle={ () => {this.setState({ showModal: true });} }
            rowId={ COMPONENT_MAP['recoveryReview']['rowId'] }
            fieldId={ COMPONENT_MAP['recoveryReview']['id'] }
            fieldNotes={ this.props.getFieldNotes(this.props.notes, COMPONENT_MAP['recoveryReview']['id']) }
            fieldDocs={ this.props.getFieldDocs(this.props.attachments, COMPONENT_MAP['recoveryReview']['id']) }
            selectedFieldNote={ this.props.selectedFieldNote }
            handleItemNoteClick={ this.props.handleItemNoteClick }
            handleMouseEnter={ this.props.handleMouseEnter }
            handleMouseLeave={ this.props.handleMouseLeave }
            handleAttachmentClick={ this.props.handleAttachmentClick }
            deleteAttachment={ this.props.deleteAttachment }
            rowType={ this.props.rowType }
            rowStyle={ this.props.rowStyle }/>
          <If condition={ this.state.recoveryData && this.state.recoveryData.has_recovery }>
            <InputBoxRow labelContainerClass="col-3"
              inputBoxContainerClass="col-2"
              containerClass="justify-content-end"
              hasLabel={ true }
              hasTextValue={ true }
              isFinanceUser={ this.props.isFinanceUser }
              showManagerCheckbox={ false }
              isDisabled={ false }
              label="Added:"
              textValue={ moment(this.state.recoveryData.recovery_added, dateFormat.backendDateFormat).format(dateFormat.displayDateFormat) }
              claimObject={ this.state.recoveryData }
              onCursorToggle={ this.onCursorToggle }
              rowId={ COMPONENT_MAP['recoveryAdded']['rowId'] }
              fieldId={ COMPONENT_MAP['recoveryAdded']['id'] }
              fieldNotes={ this.props.getFieldNotes(this.props.notes, COMPONENT_MAP['recoveryAdded']['id']) }
              fieldDocs={ this.props.getFieldDocs(this.props.attachments, COMPONENT_MAP['recoveryAdded']['id']) }
              selectedFieldNote={ this.props.selectedFieldNote }
              handleItemNoteClick={ this.props.handleItemNoteClick }
              handleMouseEnter={ this.props.handleMouseEnter }
              handleMouseLeave={ this.props.handleMouseLeave }
              handleAttachmentClick={ this.props.handleAttachmentClick }
              deleteAttachment={ this.props.deleteAttachment }
              rowType={ this.props.rowType }
              rowStyle={ this.props.rowStyle }/>
          </If>
          <If condition={ this.state.recoveryData && this.state.recoveryData.status && !this.state.recoveryData.has_recovery }>
            <InputBoxRow labelContainerClass="col-3"
              inputBoxContainerClass="col-2"
              containerClass="justify-content-end"
              hasLabel={ true }
              hasTextValue={ true }
              isFinanceUser={ this.props.isFinanceUser }
              showManagerCheckbox={ false }
              isDisabled={ false }
              label="Removed:"
              textValue={ moment(this.state.recoveryData.recovery_removed, dateFormat.backendDateFormat).format(dateFormat.displayDateFormat) }
              claimObject={ this.state.recoveryData }
              rowId={ COMPONENT_MAP['recoveryRemoved']['rowId'] }
              fieldId={ COMPONENT_MAP['recoveryRemoved']['id'] }
              fieldNotes={ this.props.getFieldNotes(this.props.notes, COMPONENT_MAP['recoveryRemoved']['id']) }
              fieldDocs={ this.props.getFieldDocs(this.props.attachments, COMPONENT_MAP['recoveryRemoved']['id']) }
              selectedFieldNote={ this.props.selectedFieldNote }
              handleItemNoteClick={ this.props.handleItemNoteClick }
              handleMouseEnter={ this.props.handleMouseEnter }
              handleMouseLeave={ this.props.handleMouseLeave }
              handleAttachmentClick={ this.props.handleAttachmentClick }
              deleteAttachment={ this.props.deleteAttachment }
              rowType={ this.props.rowType }
              rowStyle={ this.props.rowStyle }/>
          </If>
          <If condition={ this.state.recoveryData && this.state.recoveryData.status }>
            <InputBoxRow labelContainerClass="col-3"
              inputBoxContainerClass="col-2"
              containerClass="justify-content-end"
              hasLabel={ true }
              hasTextValue={ true }
              isFinanceUser={ this.props.isFinanceUser }
              showManagerCheckbox={ false }
              isDisabled={ false }
              label="Recovery Status:"
              textValue={ CONSTANTS.GAP_RECOVERY_READABLE_STATUS_MAP[this.state.recoveryData.status] }
              claimObject={ this.state.recoveryData }
              rowId={ COMPONENT_MAP['recoveryStatus']['rowId'] }
              fieldId={ COMPONENT_MAP['recoveryStatus']['id'] }
              fieldNotes={ this.props.getFieldNotes(this.props.notes, COMPONENT_MAP['recoveryStatus']['id']) }
              fieldDocs={ this.props.getFieldDocs(this.props.attachments, COMPONENT_MAP['recoveryStatus']['id']) }
              selectedFieldNote={ this.props.selectedFieldNote }
              handleItemNoteClick={ this.props.handleItemNoteClick }
              handleMouseEnter={ this.props.handleMouseEnter }
              handleMouseLeave={ this.props.handleMouseLeave }
              handleAttachmentClick={ this.props.handleAttachmentClick }
              deleteAttachment={ this.props.deleteAttachment }
              rowType={ this.props.rowType }
              rowStyle={ this.props.rowStyle }/>
          </If>
          <If condition={ this.state.recoveryData && this.state.recoveryData.status === CONSTANTS.GAP_RECOVERY_STATUS_MAP.recovered.code }>
            <InputBoxRow labelContainerClass="col-3"
              inputBoxContainerClass="col-2"
              containerClass="justify-content-end"
              hasLabel={ true }
              hasTextValue={ true }
              isFinanceUser={ this.props.isFinanceUser }
              showManagerCheckbox={ false }
              isDisabled={ false }
              label="Recovery Check:"
              textValue={ accounting.formatMoney(this.state.recoveryData.check_amount, '$', 2) }
              claimObject={ this.state.recoveryData }
              rowId={ RECOVERY_COMPONENT_MAP['checkAmount']['rowId'] }
              fieldId={ RECOVERY_COMPONENT_MAP['checkAmount']['id'] }
              fieldNotes={ this.props.getFieldNotes(this.props.notes, RECOVERY_COMPONENT_MAP['checkAmount']['id']) }
              fieldDocs={ this.props.getFieldDocs(this.props.attachments, RECOVERY_COMPONENT_MAP['checkAmount']['id']) }
              selectedFieldNote={ this.props.selectedFieldNote }
              handleItemNoteClick={ this.props.handleItemNoteClick }
              handleMouseEnter={ this.props.handleMouseEnter }
              handleMouseLeave={ this.props.handleMouseLeave }
              handleAttachmentClick={ this.props.handleAttachmentClick }
              deleteAttachment={ this.props.deleteAttachment }
              claimType="GAP_RECOVERY"
              rowType={ this.props.rowType }
              rowStyle={ this.props.rowStyle }/>
          </If>
        </Loader>
        <RecoverySubmitModal
          displayModal={ this.state.showModal }
          onChange={ this.handleReasonChange }
          hasRecovery={ this.state.recoveryData && !this.state.recoveryData.has_recovery }
          recoveryReason={ this.state.recoveryReason }
          onDecline={ () => {this.setState({ showModal: false, recoveryReason: '' });} }
          onSubmit={ this.onSubmit }/>
      </span>
    );
  }
}
