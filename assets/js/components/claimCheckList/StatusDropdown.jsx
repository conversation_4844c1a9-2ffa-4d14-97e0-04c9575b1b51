import React from 'react';
import InputBoxRow from "./InputBoxRow.jsx";
import { CONSTANTS } from "./../reusable/Constants/constants.js";
import PropTypes from 'prop-types';

export default class StatusDropdown extends React.Component {

  static propTypes = {
    claimObject: PropTypes.object.isRequired,
    statusType: PropTypes.oneOf(['status', 'child_status']).isRequired,
    statusChangeDescription: PropTypes.oneOf(['status_change_description', 'child_status_change_description']).isRequired,
    userHasRole: PropTypes.func.isRequired,
    handleStatusChange: PropTypes.func.isRequired,
    onCursorChange: PropTypes.func.isRequired,
    status: PropTypes.string,
    initialStatus: PropTypes.string,
    statusMap: PropTypes.object.isRequired,
    labelCustomClass: PropTypes.string.isRequired,
    inputCustomClass: PropTypes.string.isRequired,
    user: PropTypes.shape({
      id: PropTypes.number.isRequired,
      first_name: PropTypes.string.isRequired,
      last_name: PropTypes.string.isRequired,
      email: PropTypes.string.isRequired,
      stores: PropTypes.arrayOf(PropTypes.string.isRequired),
      gap_approval_limit: PropTypes.string.isRequired,
      roles: PropTypes.shape({
        Map: PropTypes.object,
      }).isRequired
    }),
    fieldType: PropTypes.oneOf(['parent', 'child']),
    getFieldNotes: PropTypes.func.isRequired,
    getFieldDocs: PropTypes.func.isRequired,
    selectedFieldNote: PropTypes.object.isRequired,
    handleItemNoteClick: PropTypes.func.isRequired,
    handleMouseEnter: PropTypes.func.isRequired,
    handleMouseLeave: PropTypes.func.isRequired,
    handleAttachmentClick: PropTypes.func.isRequired,
    deleteAttachment: PropTypes.func,
    rowType: PropTypes.string.isRequired,
    rowStyle: PropTypes.object.isRequired,
    attachments: PropTypes.object.isRequired,
    notes: PropTypes.object.isRequired,
    isFinanceUser: PropTypes.bool,
    childIndex: PropTypes.number,
    isCsClaim: PropTypes.bool,
    getCaseReserve: PropTypes.func,
  };

  constructor(props) {
    super(props);
  }

  handleStatusChange = (event) => {
    this.props.handleStatusChange(this.props.statusType, event);
  };

  onCursorChange = (event) => {
    this.props.onCursorChange(this.props.statusChangeDescription, event);
  };

  /**
   * This function will render the status based on logged in user role which can be;
   * 'gap_claims' or 'gap_claims_manager'
   * @param claimObject
   * gap claim object
   *
   * - statusType
   * This can have one of the two values 'status'(Parent claim status) or 'child_status'(Child claim status).
   * */

  renderStatusDropdown = (claimObject) => {
    if (this.props.isCsClaim) {
      return this.renderCSClaimStatusDropDown(claimObject);
    } else if (this.props.userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaims) && !this.props.userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaimsManager)) {
      return this.returnProcessorStatusDropdown(claimObject);
    } else if (this.props.userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaimsManager)) {
      return this.returnManagerStatusDropdown(claimObject);
    }
  };

  renderCSClaimStatusDropDown = (claimObject) => {
    let statusValue = '';
    if (claimObject['status'] === this.props.statusMap.pending) {
      statusValue = 'pending';
    } else if (claimObject['status'] === this.props.statusMap.authorization || claimObject['status'] === this.props.statusMap.noGap) {
      statusValue = 'authorization';
    } else if (claimObject['status'] === this.props.statusMap.checkWritten) {
      statusValue = 'checkWritten';
    } else if (claimObject['status'] === this.props.statusMap.checkVoided) {
      statusValue = 'checkVoided';
    }

    return (
      <InputBoxRow labelContainerClass={ this.props.labelCustomClass }
        inputBoxContainerClass={ this.props.inputCustomClass }
        containerClass="justify-content-end"
        hasLabel={ true }
        hasNode={ true }
        isFinanceUser={ this.props.isFinanceUser }
        label="Status:"
        renderNode={ this.getCSStatusDropdown.bind(this, claimObject, statusValue) }
        rowId={ CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP[`${this.props.fieldType}Status`]['rowId'] }
        fieldId={ this.props.fieldType === 'parent' ? CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP[`${this.props.fieldType}Status`]['id'] : (this.props.childIndex * 100) + CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP[`${this.props.fieldType}Status`]['id'] }
        fieldNotes={ this.props.getFieldNotes(this.props.notes, this.props.fieldType === 'parent' ? CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP[`${this.props.fieldType}Status`]['id'] : (this.props.childIndex * 100) + CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP[`${this.props.fieldType}Status`]['id']) }
        fieldDocs={ this.props.getFieldDocs(this.props.attachments, this.props.fieldType === 'parent' ? CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP[`${this.props.fieldType}Status`]['id'] : (this.props.childIndex * 100) + CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP[`${this.props.fieldType}Status`]['id']) }
        selectedFieldNote={ this.props.selectedFieldNote }
        handleItemNoteClick={ this.props.handleItemNoteClick }
        handleMouseEnter={ this.props.handleMouseEnter }
        handleMouseLeave={ this.props.handleMouseLeave }
        handleAttachmentClick={ this.props.handleAttachmentClick }
        deleteAttachment={ this.props.deleteAttachment }
        rowType={ this.props.rowType }
        rowStyle={ this.props.rowStyle }/>
    );
  };

  getCSStatusDropdown = (claimObject, statusValue) => {
    return (
      <select className="form-control form-control-sm"
        id={ `${CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP[this.props.fieldType + 'Status']['rowId']}_dropdown` }
        value={ statusValue }
        onChange={ this.handleStatusChange }
        disabled={ (this.props.initialStatus === this.props.statusMap.authorization ||
              this.props.initialStatus === this.props.statusMap.checkWritten ||
              this.props.initialStatus === this.props.statusMap.checkVoided) && true }>
        <option value=''>Select status</option>
        <option value='pending'>In Process</option>
        { this.renderWaitingForCheckOption() }
        { this.renderCheckWrittenOption(claimObject) }
        { this.renderCheckVoidedOption(claimObject) }
      </select>
    );
  };

  renderWaitingForCheckOption = () => {
    if (this.props.initialStatus === this.props.statusMap.authorization) {
      return <option value='authorization'>Waiting for check</option>;
    } else {
      return <option value='authorization'>Submit to CS</option>;
    }
  };

  returnProcessorStatusDropdown = (claimObject) => {
    let statusValue = '';
    if ((claimObject['status'] === this.props.statusMap.pending ||
      claimObject['status'] === this.props.statusMap.pendingReopened ||
      claimObject['status'] === this.props.statusMap.returnedForCorrection) &&
      (this.props.status === 'pending' || this.props.status === '')) {
      statusValue = 'pending';
    } else if (claimObject['status'] === this.props.statusMap.closedNoResponse) {
      statusValue = 'closedNoResponse';
    } else if (claimObject['status'] === this.props.statusMap.authorization) {
      statusValue = 'authorization';
    } else if (claimObject['status'] === this.props.statusMap.waitingForAuthorization) {
      statusValue = 'waitingForAuthorization';
    } else if (claimObject['status'] === this.props.statusMap.readyToProcess) {
      statusValue = 'readyToProcess';
    } else if (claimObject['status'] === this.props.statusMap.noGap) {
      statusValue = 'noGap';
    } else if (claimObject['status'] === this.props.statusMap.pendingDenial) {
      statusValue = 'pendingDenial';
    } else if (claimObject['status'] === this.props.statusMap.pending && this.props.status === 'extend2Weeks') {
      statusValue = 'extend2Weeks';
    }
    return (
      <InputBoxRow labelContainerClass={ this.props.labelCustomClass }
        inputBoxContainerClass={ this.props.inputCustomClass }
        containerClass="justify-content-end"
        hasLabel={ true }
        hasNode={ true }
        isFinanceUser={ this.props.isFinanceUser }
        label="Status:"
        renderNode={ this.getProcessorDropdown.bind(this, claimObject, statusValue) }
        rowId={ CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP[`${this.props.fieldType}Status`]['rowId'] }
        fieldId={ this.props.fieldType === 'parent' ? CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP[`${this.props.fieldType}Status`]['id'] : (this.props.childIndex * 100) + CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP[`${this.props.fieldType}Status`]['id'] }
        fieldNotes={ this.props.getFieldNotes(this.props.notes, this.props.fieldType === 'parent' ? CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP[`${this.props.fieldType}Status`]['id'] : (this.props.childIndex * 100) + CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP[`${this.props.fieldType}Status`]['id']) }
        fieldDocs={ this.props.getFieldDocs(this.props.attachments, this.props.fieldType === 'parent' ? CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP[`${this.props.fieldType}Status`]['id'] : (this.props.childIndex * 100) + CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP[`${this.props.fieldType}Status`]['id']) }
        selectedFieldNote={ this.props.selectedFieldNote }
        handleItemNoteClick={ this.props.handleItemNoteClick }
        handleMouseEnter={ this.props.handleMouseEnter }
        handleMouseLeave={ this.props.handleMouseLeave }
        handleAttachmentClick={ this.props.handleAttachmentClick }
        deleteAttachment={ this.props.deleteAttachment }
        rowType={ this.props.rowType }
        rowStyle={ this.props.rowStyle }/>
    );
  };

  getProcessorDropdown = (claimObject, statusValue) => {
    const {
      user : {
        gap_approval_limit: approvalLimit,
      },
      getCaseReserve,
    } = this.props;

    let approvalAvailable = true;
    if (getCaseReserve && parseFloat(approvalLimit) >= getCaseReserve(claimObject)) {
      approvalAvailable = false;
    }

    return (
      <select className="form-control form-control-sm"
        id={ `${CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP[this.props.fieldType + 'Status']['rowId']}_dropdown` }
        value={ statusValue }
        onChange={ this.handleStatusChange }
        disabled={ ((claimObject['status'] === this.props.statusMap.authorization && approvalAvailable)||
              claimObject['status'] === this.props.statusMap.checkWritten ||
              claimObject['status'] === this.props.statusMap.pendingDenial ||
              claimObject['status'] === this.props.statusMap.waitingForAuthorization ||
              claimObject['status'] === this.props.statusMap.checkVoided) }>
        <option value=''>Select status</option>
        <option value='pending'>Pending</option>
        <option value='readyToProcess'>Ready to process</option>
        <option value='pendingDenial'>Pending denial</option>
        <option value='waitingForAuthorization'>Waiting for authorization</option>
        <option value='extend2Weeks'>Extend 2 weeks</option>
        <option value='noGap'>Closed-No GAP</option>
        <option value='authorization' disabled={approvalAvailable}>Authorization</option>
        <option value='closedNoResponse'>Close - No Response</option>
      </select>
    );
  };

  returnManagerStatusDropdown = (claimObject) => {
    let statusValue = '';
    if (claimObject['status'] === this.props.statusMap.waitingForAuthorization) {
      statusValue = 'waitingForAuthorization';
    } else if (claimObject['status'] === this.props.statusMap.pendingDenial) {
      statusValue = 'pendingDenial';
    } else if (claimObject['status'] === this.props.statusMap.authorization) {
      statusValue = 'authorization';
    } else if (claimObject['status'] === this.props.statusMap.returnedForCorrection || claimObject['status'] === this.props.statusMap.pendingReopened) {
      statusValue = 'returnedForCorrection';
    } else if (claimObject['status'] === this.props.statusMap.denied) {
      statusValue = 'denied';
    } else if (claimObject['status'] === this.props.statusMap.noGap) {
      statusValue = 'noGap';
    } else if (claimObject['status'] === this.props.statusMap.closedNoResponse) {
      statusValue = 'closedNoResponse';
    } else if (claimObject['status'] === this.props.statusMap.checkWritten) {
      statusValue = 'checkWritten';
    } else if (claimObject['status'] === this.props.statusMap.checkVoided) {
      statusValue = 'checkVoided';
    }
    return (
      <InputBoxRow labelContainerClass={ this.props.labelCustomClass }
        inputBoxContainerClass={ this.props.inputCustomClass }
        containerClass="justify-content-end"
        hasLabel={ true }
        hasNode={ true }
        isFinanceUser={ this.props.isFinanceUser }
        label="Status:"
        renderNode={ this.getManagerStatusDropdown.bind(this, claimObject, statusValue) }
        rowId={ CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP[`${this.props.fieldType}Status`]['rowId'] }
        fieldId={ this.props.fieldType === 'parent' ? CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP[`${this.props.fieldType}Status`]['id'] : (this.props.childIndex * 100) + CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP[`${this.props.fieldType}Status`]['id'] }
        fieldNotes={ this.props.getFieldNotes(this.props.notes, this.props.fieldType === 'parent' ? CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP[`${this.props.fieldType}Status`]['id'] : (this.props.childIndex * 100) + CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP[`${this.props.fieldType}Status`]['id']) }
        fieldDocs={ this.props.getFieldDocs(this.props.attachments, this.props.fieldType === 'parent' ? CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP[`${this.props.fieldType}Status`]['id'] : (this.props.childIndex * 100) + CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP[`${this.props.fieldType}Status`]['id']) }
        selectedFieldNote={ this.props.selectedFieldNote }
        handleItemNoteClick={ this.props.handleItemNoteClick }
        handleMouseEnter={ this.props.handleMouseEnter }
        handleMouseLeave={ this.props.handleMouseLeave }
        handleAttachmentClick={ this.props.handleAttachmentClick }
        deleteAttachment={ this.props.deleteAttachment }
        rowType={ this.props.rowType }
        rowStyle={ this.props.rowStyle }/>
    );
  };

  getManagerStatusDropdown = (claimObject, statusValue) => {
    if (this.props.initialStatus !== CONSTANTS.STATUS_MAP.noGap && this.props.initialStatus !== CONSTANTS.STATUS_MAP.closedNoResponse) {
      return (
        <select className="form-control form-control-sm"
          id={ `${CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP[this.props.fieldType + 'Status']['rowId']}_dropdown` }
          value={ statusValue }
          onChange={ this.handleStatusChange }
          disabled={ (this.props.initialStatus === this.props.statusMap.authorization ||
                this.props.initialStatus === this.props.statusMap.checkWritten ||
                this.props.initialStatus === this.props.statusMap.checkVoided) && true }>
          <option value=''>Select status</option>
          <option value='waitingForAuthorization'>Waiting for authorization</option>
          <option value='pendingDenial'>Pending denial</option>
          <option value='authorization'>Authorization</option>
          <option value='returnedForCorrection'>Returned</option>
          <option value='denied'>Denied</option>
          <option value='noGap'>Closed-No GAP</option>
          <option value='closedNoResponse'>Close - No Response</option>
          { this.renderCheckWrittenOption(claimObject) }
          { this.renderCheckVoidedOption(claimObject) }
        </select>
      );
    } else {
      return (
        <select className="form-control form-control-sm"
          id={ `${CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP[this.props.fieldType + 'Status']['rowId']}_dropdown` }
          value={ statusValue }
          onChange={ this.handleStatusChange }
          disabled={ (this.props.initialStatus === this.props.statusMap.authorization ||
                this.props.initialStatus === this.props.statusMap.checkWritten ||
                this.props.initialStatus === this.props.statusMap.checkVoided) && true }>
          <option value=''>Select status</option>
          <option value='returnedForCorrection'>Returned</option>
          <option value='noGap'>Closed-No GAP</option>
          <option value='closedNoResponse'>Close - No Response</option>
          { this.renderCheckWrittenOption(claimObject) }
          { this.renderCheckVoidedOption(claimObject) }
        </select>
      );
    }
  };

  /**
   * This function will render check written option for dropdown based on the status code, This won't be visible all the time.
   * It will be displayed only when the status is 'C - Check written'
   *
   * @param claimObject
   * */

  renderCheckWrittenOption = (claimObject) => {
    if (claimObject['status'] === this.props.statusMap.checkWritten) {
      return (
        <option value='checkWritten'>Check Written</option>
      );
    }
  };

  renderCheckVoidedOption = (claimObject) => {
    if (claimObject['status'] === this.props.statusMap.checkVoided) {
      return (
        <option value='checkVoided'>Check Voided</option>
      );
    }
  };

  renderClaimDeniedReason = () => {
    if (this.props.claimObject['status'] === this.props.statusMap.denied) {
      return (
        <InputBoxRow labelContainerClass={ this.props.labelCustomClass }
          inputBoxContainerClass={ this.props.inputCustomClass }
          containerClass="justify-content-end"
          hasLabel={ true }
          hasNode={ true }
          isFinanceUser={ this.props.isFinanceUser }
          label="Reason:"
          renderNode={ this.getDeniedReasonDropdown }
          rowId={ CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP[`${this.props.fieldType}DeniedReason`]['rowId'] }
          fieldId={ this.props.fieldType === 'parent' ? CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP[`${this.props.fieldType}DeniedReason`]['id'] : (this.props.childIndex * 100) + CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP[`${this.props.fieldType}DeniedReason`]['id'] }
          fieldNotes={ this.props.getFieldNotes(this.props.notes, this.props.fieldType === 'parent' ? CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP[`${this.props.fieldType}DeniedReason`]['id'] : (this.props.childIndex * 100) + CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP[`${this.props.fieldType}DeniedReason`]['id']) }
          fieldDocs={ this.props.getFieldDocs(this.props.attachments, this.props.fieldType === 'parent' ? CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP[`${this.props.fieldType}DeniedReason`]['id'] : (this.props.childIndex * 100) + CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP[`${this.props.fieldType}DeniedReason`]['id']) }
          selectedFieldNote={ this.props.selectedFieldNote }
          handleItemNoteClick={ this.props.handleItemNoteClick }
          handleMouseEnter={ this.props.handleMouseEnter }
          handleMouseLeave={ this.props.handleMouseLeave }
          handleAttachmentClick={ this.props.handleAttachmentClick }
          deleteAttachment={ this.props.deleteAttachment }
          rowType={ this.props.rowType }
          rowStyle={ this.props.rowStyle }/>
      );
    }
  };

  getDeniedReasonDropdown = () => {
    return (
      <select className="form-control form-control-sm"
        value={ this.props.claimObject[this.props.statusChangeDescription] }
        onChange={ this.onCursorChange }>
        <option value=''>Select reason</option>
        <option value='Illegal Act'>Illegal Act</option>
        <option value='family'>Family</option>
        <option value='intentional'>Intentional</option>
        <option value='refinanced'>Refinanced</option>
        <option value='fraud'>Fraud</option>
        <option value='commercialUse'>Commercial Use</option>
        <option value='other'>Other</option>
      </select>
    );
  };

  render() {
    return (
      <div>
        { this.renderStatusDropdown(this.props.claimObject) }
        { this.renderClaimDeniedReason() }
      </div>
    );
  }
}
