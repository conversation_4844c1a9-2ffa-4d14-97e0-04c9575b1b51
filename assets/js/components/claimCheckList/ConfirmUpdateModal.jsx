import React from 'react';
import PropTypes from 'prop-types';
import If from '../reusable/If/If.jsx';
import Modal from "../../Modal.jsx";

export default class ConfirmationModal extends React.Component {

  static propTypes = {
    displayModal: PropTypes.bool.isRequired,
    handleYesClick: PropTypes.func.isRequired,
    handleCloseClick: PropTypes.func.isRequired,
    customTextMessage: PropTypes.string
  };

  constructor(props) {
    super(props);
  }

  render() {
    return (
      <section>
        <Modal visible={ this.props.displayModal } size="small">
          <div>
            <If condition={this.props.customTextMessage}>
              <p className="text-center"> {this.props.customTextMessage}</p>
            </If>
            <p className="text-center"> Do you wish to continue?</p>
            <div className="row text-center clearfix">
              <div className="text-center mt-2 col">
                <button className="btn btn-secondary" onClick={ this.props.handleCloseClick }>
                  <i className="fa fa-times"></i>&nbsp;No
                </button>
              </div>
              <div className="text-center mt-2 col">
                <button className="btn btn-primary" onClick={ this.props.handleYesClick }>
                  <i className="fa fa-check"></i>&nbsp;Yes
                </button>
              </div>
            </div>
          </div>
        </Modal>
      </section>
    );
  }
}