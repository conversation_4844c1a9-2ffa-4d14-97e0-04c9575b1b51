import React from 'react';
import InputBox from "../reusable/InputBox/InputBox.jsx";
import ReactTooltip from 'react-tooltip';
import ItemNote from "./ItemNote.jsx";
const FIELD_TYPE = require("./../reusable/Constants/constants.js").CONSTANTS.FIELD_TYPE;
import DocumentLinkList from "./../reusable/DocumentLinkList/DocumentLinkList.jsx";
import PropTypes from 'prop-types';

export default class ContractListRow extends React.Component {

  static propTypes = {
    contractDetails: PropTypes.object.isRequired,
    showManagerCheckbox: PropTypes.bool.isRequired,
    isDisabled: PropTypes.bool,
    labelCustomClass: PropTypes.string.isRequired,
    inputBoxCustomClass: PropTypes.string.isRequired,
    onCheckBoxChange: PropTypes.func.isRequired,
    displayTooltip: PropTypes.bool.isRequired,
    onInputBoxChange: PropTypes.func.isRequired,
    onInputBoxBlur: PropTypes.func.isRequired,
    index: PropTypes.number.isRequired,
    selectedFieldNote: PropTypes.object,
    handleItemNoteClick: PropTypes.func,
    handleMouseEnter: PropTypes.func,
    handleMouseLeave: PropTypes.func,
    handleAttachmentClick: PropTypes.func.isRequired,
    deleteAttachment: PropTypes.func,
    fieldDocs: PropTypes.array,
    fieldNotes: PropTypes.array,
    rowStyle: PropTypes.object.isRequired,
    isFinanceUser: PropTypes.bool
  };

  constructor(props) {
    super(props);
    this.state = {
      isItemNotesSelected: false
    };
  }

  UNSAFE_componentWillReceiveProps = (nextProps) => {
    if (nextProps.selectedFieldNote.contractDetails &&
      nextProps.contractDetails['contract_number'] === nextProps.selectedFieldNote.contractDetails.contractNumber &&
      nextProps.contractDetails['contract_code'] === nextProps.selectedFieldNote.contractDetails.contractCode) {
      this.setState({
        isItemNotesSelected: true
      });
    } else if (!nextProps.selectedFieldNote.contractDetails ||
      nextProps.contractDetails['contract_number'] !== nextProps.selectedFieldNote.contractDetails.contractNumber ||
      nextProps.contractDetails['contract_code'] !== nextProps.selectedFieldNote.contractDetails.contractCode) {
      this.setState({
        isItemNotesSelected: false
      });
    }
  };

  getContainerStyle = () => {
    if (this.state.isItemNotesSelected) {
      return this.props.rowStyle;
    }
  };

  renderTooltipIcon = () => {
    if (this.props.displayTooltip) {
      return (
        <span className="col-1">
          <i className="fa fa-info-circle text-primary"
            data-tip
            data-for={ `contractNumber${this.props.contractDetails['contract_number']}` }/>
          <ReactTooltip id={ `contractNumber${this.props.contractDetails['contract_number']}` } aria-haspopup='true'>
            <p className="text-center">{ this.props.contractDetails['contract_number'] }</p>
          </ReactTooltip>
        </span>
      );
    }
  };

  getContractDetails = () => {
    return {
      contractNumber: this.props.contractDetails['contract_number'],
      contractCode: this.props.contractDetails['contract_code'],
      gapContractNotes: this.props.fieldNotes,
      contractName: this.props.contractDetails['contract_name']
    };
  };

  renderManagerCheckbox = (attribute_index) => {
    if (this.props.showManagerCheckbox) {
      return (
        <input type="checkbox"
          id={ `dynamicContract_${this.props.index}_managerCheckBox` }
          checked={ this.props.contractDetails['manager_flag'] }
          onChange={ this.props.onCheckBoxChange.bind(null, attribute_index, "manager_flag") }/>
      );
    }
  };

  render() {
    return (
      <div
        className={ this.state.isItemNotesSelected ? "form-group row justify-content-end bg-faded" : "form-group row justify-content-end" }
        style={ this.getContainerStyle() }>
        <div className={ this.props.labelCustomClass }>
          <label className="form-check form-check-label col-form-label col-form-label-sm">
            <input type="checkbox"
              id={ `dynamicContract_${this.props.index}_checkBox` }
              className="form-check-input"
              checked={ this.props.contractDetails['contract_flag'] }
              onChange={ this.props.onCheckBoxChange.bind(null, this.props.index, "contract_flag") }
              disabled={ this.props.isDisabled }/>
            <span>{ `${this.props.contractDetails['contract_name']}:` }</span>
          </label>
          { this.renderTooltipIcon() }
        </div>
        <InputBox type='Currency'
          id={ `dynamicContract_${this.props.index}_inputBox` }
          customClass={ this.props.inputBoxCustomClass }
          hasDefaultValue={ true }
          value={ this.props.contractDetails['contract_value'] }
          onChange={ this.props.onInputBoxChange.bind(null, this.props.index) }
          onBlur={ this.props.onInputBoxBlur.bind(null, this.props.index) }
          isDisabled={ this.props.isDisabled }/>
        <div className="col-1 px-0">
          { this.renderManagerCheckbox(this.props.index) }
          <ItemNote onClick={ this.props.handleItemNoteClick }
            rowId={ `dynamicContract_${this.props.index}` }
            handleMouseEnter={ this.props.handleMouseEnter }
            handleMouseLeave={ this.props.handleMouseLeave }
            contractDetails={ this.getContractDetails() }
            rowType={ FIELD_TYPE.dynamic }
            handleAttachmentClick={ this.props.handleAttachmentClick }/>
        </div>
        <div className="col-6">
          <DocumentLinkList fieldDocs={ this.props.fieldDocs }
            deleteAttachment={ this.props.deleteAttachment }
            isFinanceUser={ this.props.isFinanceUser }/>
        </div>
      </div>
    );
  }
}