import React from 'react';
import Modal from "../../Modal.jsx";
import dateFormat from "./../reusable/Utilities/dateFormat.js";
import InputBox from "../reusable/InputBox/InputBox.jsx";
import DatePicker from "react-datepicker";
import moment from "moment";
import PropTypes from 'prop-types';

export default class AmortizationModal extends React.Component {

  constructor(props) {
    super(props);
    this.state = {
      contractDealDate: this.props.loanHistory.contract_deal_date,
      contractTermMonths: this.props.loanHistory.contract_term_months,
      interestRate: this.props.loanHistory.interest_rate,
      originalFinancingContractValue: this.props.loanHistory.original_financing_contract_value,
      paymentAmount: this.props.loanHistory.payment_amount,
      firstPaymentDate: this.props.loanHistory.first_payment_date,
      totalDelinquentPaymentsCovered: this.props.loanHistory.totalDelinquentPaymentsCovered,
      totalExtensionsCovered: this.props.loanHistory.totalExtensionsCovered,
      selectedOption: "calculate",
      submitButtonName: "Calculate",
      manualAmortizationValue: 0,
      manualAmortizationReason: ""
    };
    this.onChange = this.onChange.bind(this);
    this.onAmortizationReasonChange = this.onAmortizationReasonChange.bind(this);
    this.handleDateChange = this.handleDateChange.bind(this);
    this.updateAmortization = this.updateAmortization.bind(this);
    this.handleRadioOptionClick = this.handleRadioOptionClick.bind(this);
    this.renderLoanHistory = this.renderLoanHistory.bind(this);
    this.renderAmortizationTextBox = this.renderAmortizationTextBox.bind(this);
    this.isSubmitButtonDisabled = this.isSubmitButtonDisabled.bind(this);
  }

  static propTypes = {
    displayModal: PropTypes.bool.isRequired,
    closeModal: PropTypes.func.isRequired,
    calculateAmortization: PropTypes.func.isRequired,
    updateAmortization: PropTypes.func.isRequired,
    loanHistory: PropTypes.object.isRequired
  };

  UNSAFE_componentWillReceiveProps(props) {
    this.setState({
      contractDealDate: props.loanHistory.contract_deal_date,
      contractTermMonths: props.loanHistory.contract_term_months,
      interestRate: props.loanHistory.interest_rate,
      originalFinancingContractValue: props.loanHistory.original_financing_contract_value,
      paymentAmount: props.loanHistory.payment_amount,
      firstPaymentDate: props.loanHistory.first_payment_date,
      totalDelinquentPaymentsCovered: props.loanHistory.total_delinquent_payments_covered,
      totalExtensionsCovered: props.loanHistory.total_extensions_covered,
    });
  }

  onChange(key, value) {
    const toBeUpdated = {};
    toBeUpdated[key] = value;
    this.setState(toBeUpdated);
  }

  onAmortizationReasonChange(e) {
    this.setState({ manualAmortizationReason: e.target.value });
  }

  handleDateChange(key, date) {
    if (date) {
      date = date.format(dateFormat.displayDateFormat);
      const toBeUpdated = {};
      toBeUpdated[key] = moment.utc(date, dateFormat.displayDateFormat).format(dateFormat.backendDateFormat);
      this.setState(toBeUpdated);
    }
  }

  updateAmortization() {
    if (this.state.selectedOption === "calculate") {
      this.props.calculateAmortization({
        contract_deal_date: this.state.contractDealDate,
        contract_term_months: this.state.contractTermMonths,
        interest_rate: this.state.interestRate,
        original_financing_contract_value: this.state.originalFinancingContractValue,
        payment_amount: this.state.paymentAmount,
        first_payment_date: this.state.firstPaymentDate,
        total_delinquent_payments_covered: this.state.totalDelinquentPaymentsCovered,
        total_extensions_covered: this.state.totalExtensionsCovered,
      });
    } else if (this.state.selectedOption === "manual") {
      this.props.updateAmortization({
        value: this.state.manualAmortizationValue,
        reason: this.state.manualAmortizationReason
      });
    }
  }

  handleRadioOptionClick(e) {
    this.setState({ selectedOption: e.target.value }, function () {
      if (this.state.selectedOption === "calculate") {
        this.setState({ submitButtonName: "Calculate" });
      } else if (this.state.selectedOption === "manual") {
        this.setState({ submitButtonName: "Update" });
      }
    });
  }

  renderLoanHistory() {
    const isDisabled = this.state.selectedOption !== "calculate";
    const dealDate = this.state.contractDealDate ? moment.utc(this.state.contractDealDate).format(dateFormat.displayDateFormat) : '';
    const firstPaymentDate = this.state.firstPaymentDate ? moment.utc(this.state.firstPaymentDate).format(dateFormat.displayDateFormat) : '';
    return (
      <div>
        <div>
          Calculate Amortization:
        </div>
        <div className="form-group row col-12">
          <div className="col-6">
            <label className="col-form-label col-form-label-sm">Deal date:</label>
          </div>
          <span className="input-group input-group-sm col-6">
            <DatePicker
              selected={ moment(dealDate, dateFormat.displayDateFormat) }
              id={ "amortization_deal_dateInputBox" }
              dateFormat={dateFormat.displayDateFormat}
              onChange={ this.handleDateChange.bind(null, "contractDealDate") }
              className="form-control form-control-sm date-field" disabled={isDisabled}/>
          </span>
        </div>
        <div className="form-group row col-12">
          <div className="col-6">
            <label className="col-form-label col-form-label-sm">Term (months):</label>
          </div>
          <InputBox type="Number"
            customClass="col-6"
            hasDefaultValue={ true }
            isDisabled={isDisabled}
            value={ this.state.contractTermMonths }
            onChange={ this.onChange.bind(null, "contractTermMonths") }
            onBlur={ this.onChange.bind(null, "contractTermMonths") }/>
        </div>
        <div className="form-group row col-12">
          <div className="col-6">
            <label className="col-form-label col-form-label-sm">Interest rate:</label>
          </div>
          <InputBox type="Percentage"
            customClass="col-6"
            hasDefaultValue={ true }
            isDisabled={isDisabled}
            value={ this.state.interestRate }
            onChange={ this.onChange.bind(null, "interestRate") }
            onBlur={ this.onChange.bind(null, "interestRate") }/>
        </div>
        <div className="form-group row col-12">
          <div className="col-6">
            <label className="col-form-label col-form-label-sm">Loan amount:</label>
          </div>
          <InputBox type="Currency"
            customClass="col-6"
            hasDefaultValue={ true }
            isDisabled={isDisabled}
            value={ this.state.originalFinancingContractValue }
            onChange={ this.onChange.bind(null, "originalFinancingContractValue") }
            onBlur={ this.onChange.bind(null, "originalFinancingContractValue") }/>
        </div>
        <div className="form-group row col-12">
          <div className="col-6">
            <label className="col-form-label col-form-label-sm">Payment amount:</label>
          </div>
          <InputBox type="Currency"
            customClass="col-6"
            hasDefaultValue={ true }
            isDisabled={isDisabled}
            value={ this.state.paymentAmount }
            onChange={ this.onChange.bind(null, "paymentAmount") }
            onBlur={ this.onChange.bind(null, "paymentAmount") }/>
        </div>
        <div className="form-group row col-12">
          <div className="col-6">
            <label className="col-form-label col-form-label-sm">First payment date:</label>
          </div>
          <span className="input-group input-group-sm col-6">
            <DatePicker
              selected={ moment(firstPaymentDate, dateFormat.displayDateFormat) }
              id={ "amortization_first_payment_dateInputBox" }
              dateFormat={dateFormat.displayDateFormat}
              onChange={ this.handleDateChange.bind(null, "firstPaymentDate") }
              className="form-control form-control-sm date-field" disabled={isDisabled}/>
          </span>
        </div>
        <div className="form-group row col-12">
          <div className="col-6">
            <label className="col-form-label col-form-label-sm">Total Delinquent Payments Covered</label>
          </div>
          <InputBox type="Currency"
            customClass="col-6"
            hasDefaultValue={ true }
            isDisabled={isDisabled}
            value={ this.state.totalDelinquentPaymentsCovered }
            onChange={ this.onChange.bind(null, "totalDelinquentPaymentsCovered") }
            onBlur={ this.onChange.bind(null, "totalDelinquentPaymentsCovered") }/>
        </div>
        <div className="form-group row col-12">
          <div className="col-6">
            <label className="col-form-label col-form-label-sm">Total Extensions Covered</label>
          </div>
          <InputBox type="Currency"
            customClass="col-6"
            hasDefaultValue={ true }
            isDisabled={isDisabled}
            value={ this.state.totalExtensionsCovered }
            onChange={ this.onChange.bind(null, "totalExtensionsCovered") }
            onBlur={ this.onChange.bind(null, "totalExtensionsCovered") }/>
        </div>
      </div>
    );
  }

  renderAmortizationTextBox() {
    const isDisabled = this.state.selectedOption !== "manual";
    return (
      <div>
        <div className="form-group row col-12">
          <div className="col-6">
            <label className="col-form-label col-form-label-sm">Use this amount:</label>
          </div>
          <InputBox type="Currency"
            customClass="col-6"
            hasDefaultValue={ true }
            isDisabled={isDisabled}
            value={ this.state.manualAmortizationValue }
            onChange={ this.onChange.bind(null, "manualAmortizationValue") }
            onBlur={ this.onChange.bind(null, "manualAmortizationValue") }/>
        </div>
        <div className="form-group row col-12">
          <div className="col-6">
            <label className="col-form-label col-form-label-sm">Reason:</label>
          </div>
          <textarea type="text"
            id="amortization_reason_textBox"
            className="form-control form-control-sm col-6"
            rows="3"
            value={ this.state.manualAmortizationReason }
            onChange={ this.onAmortizationReasonChange }
            disabled={ isDisabled }>
          </textarea>
        </div>
      </div>
    );
  }

  isSubmitButtonDisabled() {
    if (this.state.selectedOption === "calculate" &&
      (parseInt(this.state.contractTermMonths) <= 0 ||
      this.state.contractTermMonths === "" ||
      parseFloat(this.state.interestRate) <= 0 ||
      this.state.interestRate === "" ||
      parseFloat(this.state.originalFinancingContractValue) <= 0) ||
      this.state.originalFinancingContractValue === "") {
      return true;
    } else if (this.state.selectedOption === "manual" &&
      (this.state.manualAmortizationValue === 0 || this.state.manualAmortizationReason === "")) {
      return true;
    }
    return false;
  }

  render() {
    return (
      <section>
        <Modal visible={ this.props.displayModal }
          close={ this.props.closeModal }
          title="Amortization">
          <div className="row justify-content-center">
            <form className="col-10">
              <div className="row">
                <div className="form-check">
                  <label className="form-check-label">
                    <input className="form-check-input" type="radio" value="calculate"
                      checked={this.state.selectedOption === "calculate"} onChange={this.handleRadioOptionClick}/>
                    {this.renderLoanHistory()}
                  </label>
                </div>
              </div>
              <div className="row">
                <div className="form-check">
                  <label className="form-check-label">
                    <input className="form-check-input" type="radio" value="manual"
                      checked={this.state.selectedOption === "manual"} onChange={this.handleRadioOptionClick}/>
                    {this.renderAmortizationTextBox()}
                  </label>
                </div>
              </div>
              <div className="row text-right mt-2 mr-3">
                <button className="btn btn-secondary mr-2"
                  onClick={ this.props.closeModal }>
                  Back
                </button>
                <button className="btn btn-primary" onClick={ this.updateAmortization }
                  disabled={this.isSubmitButtonDisabled()}>
                  <i className="fa fa-check"/> {this.state.submitButtonName}
                </button>
              </div>
            </form>
          </div>
        </Modal>
      </section>
    );
  }
}