import React from 'react';
import { CONSTANTS } from "./../reusable/Constants/constants.js";
import PropTypes from 'prop-types';
import If from "./../reusable/If/If.jsx";

const style = {
  notesAvailable: { color: '#4A4A4A' },
  notesUnavailable: { color: '#DDDDDD' }
};

export default class ItemNote extends React.Component {

  static propTypes = {
    onClick: PropTypes.func,
    fieldNotes: PropTypes.any,
    handleMouseEnter: PropTypes.func,
    handleMouseLeave: PropTypes.func,
    fieldId: PropTypes.number,
    contractDetails: PropTypes.shape({
      contractNumber: PropTypes.string.isRequired,
      contractCode: PropTypes.string.isRequired,
      gapContractNotes: PropTypes.any,
      contractName: PropTypes.string
    }),
    rowType: PropTypes.string,
    rowId: PropTypes.string,
    hideAttachment: PropTypes.bool,
    hideNotes: PropTypes.bool,
    handleAttachmentClick: PropTypes.func,
    claimType: PropTypes.string,
  };

  constructor(props) {
    super(props);
    this.handleSelection = this.handleSelection.bind(this);
    this.getOffsetParent = this.getOffsetParent.bind(this);
    this.isItemNotesAvailable = this.isItemNotesAvailable.bind(this);
    this.getStyle = this.getStyle.bind(this);
    this.handleAttachement = this.handleAttachement.bind(this);
  }

  handleSelection(isClickEvent, callBack) {
    const itemNoteOffset = this.getOffsetParent(this.itemNote, "parentDiv");
    const params = {
      itemNoteOffset,
      rowType: this.props.rowType,
      isSelected: true
    };
    if (this.props.rowType === CONSTANTS.FIELD_TYPE.dynamic && this.props.contractDetails) {
      params['contractDetails'] = this.props.contractDetails;
    }
    if (this.props.rowType === CONSTANTS.FIELD_TYPE.static && this.props.fieldId) {
      params['fieldId'] = this.props.fieldId;
    }
    if (this.props.rowType === CONSTANTS.FIELD_TYPE.static && this.props.fieldNotes) {
      params['fieldNotes'] = this.props.fieldNotes;
    }
    if (this.isItemNotesAvailable() || isClickEvent) {
      callBack(params);
    }
  }

  getOffsetParent(el, id) {
    if (el.offsetParent.id === id) {
      return el.offsetTop;
    } else {
      return this.getOffsetParent(el.offsetParent, id);
    }
  }

  isItemNotesAvailable() {
    if (this.props.rowType === CONSTANTS.FIELD_TYPE.static && this.props.fieldNotes && this.props.fieldNotes.count > 0) {
      return true;
    } else if (this.props.rowType === CONSTANTS.FIELD_TYPE.dynamic &&
      this.props.contractDetails.gapContractNotes && this.props.contractDetails.gapContractNotes.length > 0) {
      return true;
    } else {
      return false;
    }
  }

  getStyle() {
    if (this.isItemNotesAvailable()) {
      return style.notesAvailable;
    } else {
      return style.notesUnavailable;
    }
  }

  handleAttachement() {
    const attachmentParam = {
      fieldId: this.props.fieldId,
      rowType: this.props.rowType,
      contractCode: this.props.contractDetails && this.props.contractDetails.contractCode,
      contractNumber: this.props.contractDetails && this.props.contractDetails.contractNumber,
    };
    if (this.props.rowType === CONSTANTS.FIELD_TYPE.static && this.props.claimType === CONSTANTS.CLAIM_TYPE.gapRecovery) {
      attachmentParam.title = CONSTANTS.RECOVERY_WORKSHEET_COMPONENT_MAP[this.props.rowId]['title'];
    } else if (this.props.rowType === CONSTANTS.FIELD_TYPE.static && this.props.claimType === CONSTANTS.CLAIM_TYPE.vta) {
      attachmentParam.title = CONSTANTS.VTA_WORKSHEET_COMPONENT_MAP[this.props.rowId]['title'];
    } else if(this.props.rowType === CONSTANTS.FIELD_TYPE.static && this.props.claimType !== CONSTANTS.CLAIM_TYPE.gapRecovery 
      && this.props.claimType !== CONSTANTS.CLAIM_TYPE.vta && this.props.claimType !== CONSTANTS.CLAIM_TYPE.lwt){
      attachmentParam.title = CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP[this.props.rowId]['title'];
    } else if(this.props.rowType === CONSTANTS.FIELD_TYPE.static && this.props.claimType === CONSTANTS.CLAIM_TYPE.lwt){
      attachmentParam.title = CONSTANTS.LWT_WORKSHEET_COMPONENT_MAP[this.props.rowId]['title'];
      attachmentParam.documentTypeId = CONSTANTS.LWT_WORKSHEET_COMPONENT_MAP[this.props.rowId]['documentType'];
    } else if (this.props.rowType === CONSTANTS.FIELD_TYPE.dynamic) {
      attachmentParam.title = this.props.contractDetails && this.props.contractDetails.contractName;
    }
    this.props.handleAttachmentClick(attachmentParam);
  }

  render() {
    return (
      <div className="d-inline-block">
        <If condition={ !this.props.hideNotes }>
          <i aria-hidden="true"
            ref={ (itemNote) => this.itemNote = itemNote }
            id={ `${this.props.rowId}_itemNote` }
            style={ this.getStyle() }
            className="fa fa-file-text-o cursor-pointer ml-3"
            onClick={ this.handleSelection.bind(this, true, this.props.onClick) }
            onMouseEnter={ this.handleSelection.bind(this, false, this.props.handleMouseEnter) }
            onMouseLeave={ this.props.handleMouseLeave }/>
        </If>
        <If condition={ !this.props.hideAttachment }>
          <i aria-hidden="true"
            className="fa fa-paperclip cursor-pointer text-muted ml-3"
            id={ `${this.props.rowId}_attachment` }
            onClick={ this.handleAttachement }/>
        </If>
      </div>
    );
  }
}