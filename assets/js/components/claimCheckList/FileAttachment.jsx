import React from 'react';
import AttachmentModal from "../reusable/AttachmentModal/AttachmentModal.jsx";
import { URLS as apiUrls } from "../reusable/Utilities/urls";
import { json as ajax } from "./../../ajax.js";
const FIELD_TYPE = require("../reusable/Constants/constants").CONSTANTS.FIELD_TYPE;
import { addRecordNote } from "../reusable/RecordNotes/addRecordNotes.js";
import PropTypes from 'prop-types';

export default class FileAttachment extends React.Component {

  static propTypes = {
    displayModal: PropTypes.bool.isRequired,
    closeModal: PropTypes.func.isRequired,
    maxFileSizeInMBs: PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.number
    ]),
    isMultipleAllowed: PropTypes.bool,
    claimId: PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.number
    ]),
    attachmentParams: PropTypes.shape({
      title: PropTypes.string,
      fieldId: PropTypes.oneOfType([
        PropTypes.string,
        PropTypes.number
      ]),
      rowType: PropTypes.string,
      contractCode: PropTypes.string,
      contractNumber: PropTypes.string
    })
  };

  constructor(props) {
    super(props);
    this.state = {
      isUploading: false
    };
    this.getParams = this.getParams.bind(this);
    this.handleAttachmentUpload = this.handleAttachmentUpload.bind(this);
    this.onAddNoteSuccess = this.onAddNoteSuccess.bind(this);
  }

  getParams() {
    if (this.props.attachmentParams.rowType === FIELD_TYPE.static) {
      return { "gap_claim_id": this.props.claimId, "field_id": this.props.attachmentParams.fieldId };
    } else if (this.props.attachmentParams.rowType === FIELD_TYPE.dynamic) {
      return {
        "gap_claim_id": this.props.claimId,
        "contract_number": this.props.attachmentParams.contractNumber,
        "contract_code": this.props.attachmentParams.contractCode
      };
    }
  }

  handleAttachmentUpload(fileData, fileName, fileExtension) {
    const params = this.getParams();
    params.file_content = fileData;
    params.file_name = fileName;
    params.file_type = fileExtension;
    this.setState({ isUploading: true }, function () {
      ajax(apiUrls.document, params, { method: 'POST' }, (data, status) => {
        if (status === 200) {
          const noteText = `The file ${fileName} was attached to the ${this.props.attachmentParams.title}.`;
          addRecordNote(this.props.claimId, noteText, this.onAddNoteSuccess);
        } else {
          this.setState({ isUploading: false }, function () {
            this.props.closeModal(false, true);
          });
        }
      });
    });
  }

  onAddNoteSuccess() {
    this.setState({ isUploading: false }, function () {
      this.props.closeModal(true, false);
    });
  }

  render() {
    return (
      <section className="container">
        <AttachmentModal displayModal={this.props.displayModal}
          handleUpload={this.handleAttachmentUpload}
          closeModal={this.props.closeModal}
          title={this.props.attachmentParams.title}
          isUploading={this.state.isUploading}/>
      </section>
    );
  }
}