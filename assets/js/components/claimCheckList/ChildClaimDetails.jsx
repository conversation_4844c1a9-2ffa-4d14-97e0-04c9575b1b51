import React from "react";
import StatusDropdown from "./StatusDropdown.jsx";
import PaymentInfo from "./PaymentInfo.jsx";
import InputBoxRow from "./InputBoxRow.jsx";
import { CONSTANTS } from "./../reusable/Constants/constants.js";
import PropTypes from 'prop-types';
import { URLS as apiUrls } from "./../reusable/Utilities/urls";
import { json as ajax } from "./../../ajax.js";
import Loader from "react-loader-advanced";
import Alert from "react-s-alert";
import CSCheckAttachmentModal from "./CSCheckAttachment.jsx";
const COMPONENT_MAP = require("./../reusable/Constants/constants.js").CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP;

export default class ChildClaim extends React.Component {

  static propTypes = {
    claimObject: PropTypes.object.isRequired,
    userHasRole: PropTypes.func.isRequired,
    status: PropTypes.string,
    statusMap: PropTypes.object.isRequired,
    user: PropTypes.shape({
      id: PropTypes.number.isRequired,
      first_name: PropTypes.string.isRequired,
      last_name: PropTypes.string.isRequired,
      email: PropTypes.string.isRequired,
      stores: PropTypes.arrayOf(PropTypes.string.isRequired),
      roles: PropTypes.shape({
        Map: PropTypes.object,
      }).isRequired
    }),
    getFieldNotes: PropTypes.func.isRequired,
    getFieldDocs: PropTypes.func.isRequired,
    selectedFieldNote: PropTypes.object.isRequired,
    handleItemNoteClick: PropTypes.func.isRequired,
    handleMouseEnter: PropTypes.func.isRequired,
    handleMouseLeave: PropTypes.func.isRequired,
    handleAttachmentClick: PropTypes.func.isRequired,
    deleteAttachment: PropTypes.func,
    rowType: PropTypes.string.isRequired,
    rowStyle: PropTypes.object.isRequired,
    attachments: PropTypes.object.isRequired,
    notes: PropTypes.object.isRequired,
    isFinanceUser: PropTypes.bool,
    childClaimId: PropTypes.number,
    updateTriggered: PropTypes.bool,
    onUpdate: PropTypes.func,
    onCSChildClaimCheckAttachmentClose: PropTypes.func,
    showCSCheckAttachmentModal: PropTypes.bool,
    index: PropTypes.number,
    getCaseReserve: PropTypes.func,
  };

  constructor(props) {
    super(props);
    this.state = {
      showLoader: false,
      gapChildClaims: {},
      initialStatus: '',
      isWorksheetUpdated: false,
      status: ''
    };
  }

  componentDidMount = () => {
    this.loadClaim();
  };

  UNSAFE_componentWillReceiveProps = (nextProps) => {
    if (nextProps.updateTriggered && this.state.isWorksheetUpdated) {
      this.updateChildClaim();
    }
    if (this.props.childClaimId === nextProps.childClaimId && this.props.status !== nextProps.status) {
      this.loadClaim();
    }
  };

  loadClaim = () => {
    this.setState({ showLoader: true }, () => {
      ajax(`${apiUrls.gapGetChildClaim.replace('__claimId__', this.props.claimObject.id).replace('__childClaimId__', this.props.childClaimId)}`, {}, {}, (data, status) => {
        if (status === 200) {
          this.setState({
            showLoader: false,
            initialStatus: data.child_claim.status,
            gapChildClaims: data.child_claim
          });
        } else {
          this.setState({ showLoader: false }, ()=> {
            Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  updateChildClaim = () => {
    this.setState({ showLoader: true }, () => {
      ajax(`${apiUrls.gapGetChildClaim.replace('__claimId__', this.props.claimObject.id).replace('__childClaimId__', this.props.childClaimId)}`, this.state.gapChildClaims, { method: 'PUT' }, (data, status) => {
        if (status === 200) {
          this.setState({
            showLoader: false
          }, ()=> {
            this.props.onUpdate(this.state.gapChildClaims.id, this.state.gapChildClaims.status);
            this.loadClaim();
          });
        } else {
          this.setState({ showLoader: false }, ()=> {
            Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  onCursorValueChange = (name, value) => {
    const claimObject = this.state.gapChildClaims;
    claimObject[name] = value;
    this.setState({ isWorksheetUpdated: true, gapChildClaims: claimObject });
  };

  onCursorChange = (name, e) => {
    const claimObject = this.state.gapChildClaims;
    claimObject[name] = e.target.value;
    this.setState({ isWorksheetUpdated: true, gapChildClaims: claimObject });
  };

  changeStatus = (statusType, e) => {
    const statusCode = CONSTANTS.STATUS_MAP[e.target.value];
    this.setState({
      status: e.target.value
    }, () => {
      this.onCursorValueChange(statusType, statusCode);
    });
  };

  /**
   * This function will render request amount currency input box till the child claim is authorized.
   * @param claimObject
   * */

  renderRequestAmount = (claimObject) => {
    const isDisabled = (this.state.initialStatus === CONSTANTS.STATUS_MAP.authorization ||
      this.state.initialStatus === CONSTANTS.STATUS_MAP.checkWritten ||
      this.state.initialStatus === CONSTANTS.STATUS_MAP.checkVoided ||
      this.state.initialStatus === CONSTANTS.STATUS_MAP.denied ||
      this.state.initialStatus === CONSTANTS.STATUS_MAP.closedNoResponse ||
      this.state.initialStatus === CONSTANTS.STATUS_MAP.noGap) || false;
    if (!(this.state.initialStatus === this.props.statusMap.checkWritten)) {
      return (
        <InputBoxRow labelContainerClass="col-2"
          inputBoxContainerClass="col-2"
          containerClass="justify-content-end"
          hasLabel={ true }
          hasInputBox={ true }
          isFinanceUser={ this.props.isFinanceUser }
          label="Amount:"
          inputBoxType="Currency"
          inputBoxAttribute="case_reserve"
          hasDefaultValue={ true }
          isDisabled={ isDisabled || this.props.isFinanceUser }
          claimObject={ claimObject }
          onInputBoxChange={ this.onCursorValueChange }
          onInputBoxBlur={ this.onCursorValueChange }
          rowId={ COMPONENT_MAP['childClaimAmount']['rowId'] }
          fieldId={ (this.props.index * 100) + COMPONENT_MAP['childClaimAmount']['id'] }
          fieldNotes={ this.props.getFieldNotes(this.props.notes, (this.props.index * 100) + COMPONENT_MAP['childClaimAmount']['id']) }
          fieldDocs={ this.props.getFieldDocs(this.props.attachments, (this.props.index * 100) + COMPONENT_MAP['childClaimAmount']['id']) }
          selectedFieldNote={ this.props.selectedFieldNote }
          handleItemNoteClick={ this.props.handleItemNoteClick }
          handleMouseEnter={ this.props.handleMouseEnter }
          handleMouseLeave={ this.props.handleMouseLeave }
          handleAttachmentClick={ this.props.handleAttachmentClick }
          deleteAttachment={ this.props.deleteAttachment }
          rowType={ this.props.rowType }
          rowStyle={ this.props.rowStyle }/>
      );
    }
  };

  onCSCheckAttachmentClose = (type) => {
    if (type === 'success') {
      this.updateChildClaim();
    }
    this.props.onCSChildClaimCheckAttachmentClose(type);
  };

  render() {
    const isDisabled = (this.state.initialStatus === this.props.statusMap.authorization ||
    this.state.initialStatus === this.props.statusMap.checkWritten ||
    this.state.initialStatus === this.props.statusMap.checkVoided);
    const spinnerMessage = <p className='text-center'><i className="fa fa-refresh fa-spin"/> Loading...</p>;
    return (
      <div>
        <Loader show={ this.state.showLoader } message={ spinnerMessage }>
          <InputBoxRow labelContainerClass="col-5"
            hasLabel={ true }
            isFinanceUser={ this.props.isFinanceUser }
            label="Child claim"
            rowId={ COMPONENT_MAP['childClaim']['rowId'] }
            fieldId={ (this.props.index * 100) + COMPONENT_MAP['childClaim']['id'] }
            fieldNotes={ this.props.getFieldNotes(this.props.notes, (this.props.index * 100) + COMPONENT_MAP['childClaim']['id']) }
            fieldDocs={ this.props.getFieldDocs(this.props.attachments, (this.props.index * 100) + COMPONENT_MAP['childClaim']['id']) }
            selectedFieldNote={ this.props.selectedFieldNote }
            handleItemNoteClick={ this.props.handleItemNoteClick }
            handleMouseEnter={ this.props.handleMouseEnter }
            handleMouseLeave={ this.props.handleMouseLeave }
            handleAttachmentClick={ this.props.handleAttachmentClick }
            deleteAttachment={ this.props.deleteAttachment }
            rowType={ this.props.rowType }
            rowStyle={ this.props.rowStyle }/>
          <StatusDropdown claimObject={ this.state.gapChildClaims }
            statusType="status"
            userHasRole={ this.props.userHasRole }
            isFinanceUser={ this.props.isFinanceUser }
            handleStatusChange={ this.changeStatus }
            status={ this.state.status }
            initialStatus={ this.state.initialStatus }
            user={ this.props.user }
            statusMap={ this.props.statusMap }
            onCursorChange={ this.onCursorChange }
            statusChangeDescription="status_change_description"
            onCursorValueChange={ this.onCursorValueChange }
            labelCustomClass='col-2'
            inputCustomClass='col-2'
            fieldType="child"
            isCsClaim={ (this.props.claimObject.is_cs_claim && !this.props.claimObject.not_paid_by_cs) }
            childIndex={ this.props.index }
            attachments={ this.props.attachments }
            notes={ this.props.notes }
            getFieldNotes={ this.props.getFieldNotes }
            getFieldDocs={ this.props.getFieldDocs }
            selectedFieldNote={ this.props.selectedFieldNote }
            handleItemNoteClick={ this.props.handleItemNoteClick }
            handleMouseEnter={ this.props.handleMouseEnter }
            handleMouseLeave={ this.props.handleMouseLeave }
            handleAttachmentClick={ this.props.handleAttachmentClick }
            deleteAttachment={ this.props.deleteAttachment }
            rowType={ this.props.rowType }
            rowStyle={ this.props.rowStyle }
            getCaseReserve={ this.props.getCaseReserve }/>
          { this.renderRequestAmount(this.state.gapChildClaims) }
          <PaymentInfo claimObject={ this.props.claimObject }
            claimId={ this.props.childClaimId }
            status={ this.state.initialStatus }
            statusMap={ this.props.statusMap }
            isFinanceUser={ this.props.isFinanceUser }
            fieldType="child"
            csCheckAmount={ this.state.gapChildClaims.cs_check_amount }
            childIndex={ this.props.index }
            attachments={ this.props.attachments }
            notes={ this.props.notes }
            getFieldNotes={ this.props.getFieldNotes }
            getFieldDocs={ this.props.getFieldDocs }
            selectedFieldNote={ this.props.selectedFieldNote }
            handleItemNoteClick={ this.props.handleItemNoteClick }
            handleMouseEnter={ this.props.handleMouseEnter }
            handleMouseLeave={ this.props.handleMouseLeave }
            handleAttachmentClick={ this.props.handleAttachmentClick }
            deleteAttachment={ this.props.deleteAttachment }
            rowType={ this.props.rowType }
            rowStyle={ this.props.rowStyle }/>
          <InputBoxRow labelContainerClass="col-2"
            inputBoxContainerClass="col-2"
            containerClass="justify-content-end"
            hasLabel={ true }
            hasTextAreaBox={ true }
            isFinanceUser={ this.props.isFinanceUser }
            label="Reason:"
            inputBoxAttribute="child_claim_reason"
            claimObject={ this.state.gapChildClaims }
            placeholder="Reason"
            textAreaRowSpan="4"
            isDisabled={ isDisabled || this.props.isFinanceUser }
            onInputBoxChange={ this.onCursorChange }
            rowId={ COMPONENT_MAP['childClaimReason']['rowId'] }
            fieldId={ (this.props.index * 100) + COMPONENT_MAP['childClaimReason']['id'] }
            fieldNotes={ this.props.getFieldNotes(this.props.notes, (this.props.index * 100) + COMPONENT_MAP['childClaimReason']['id']) }
            fieldDocs={ this.props.getFieldDocs(this.props.attachments, (this.props.index * 100) + COMPONENT_MAP['childClaimReason']['id']) }
            selectedFieldNote={ this.props.selectedFieldNote }
            handleItemNoteClick={ this.props.handleItemNoteClick }
            handleMouseEnter={ this.props.handleMouseEnter }
            handleMouseLeave={ this.props.handleMouseLeave }
            handleAttachmentClick={ this.props.handleAttachmentClick }
            deleteAttachment={ this.props.deleteAttachment }
            rowType={ this.props.rowType }
            rowStyle={ this.props.rowStyle }/>
        </Loader>
        <CSCheckAttachmentModal closeModal={ this.onCSCheckAttachmentClose }
          displayModal={ this.props.showCSCheckAttachmentModal && (this.state.initialStatus === CONSTANTS.STATUS_MAP.authorization) }
          claimId={ this.props.claimObject.id }
          statusType={ "status" }
          isChildClaim={ true }
          index={ this.props.index }
          checkAmountAttribute={ "cs_check_amount" }
          checkAmount={ this.state.gapChildClaims.cs_check_amount }
          onCursorValueChange={ this.onCursorValueChange }
          changeStatus={ (name, value) => {
            if(name === 'status') {
              this.onCursorValueChange(name, CONSTANTS.STATUS_MAP[value]);
            }
          } } />
      </div>
    );
  }
}