import React from 'react';
import Modal from "../../Modal.jsx";
import PropTypes from 'prop-types';

export default class UserSelectionModal extends React.Component {

  static propTypes = {
    userList: PropTypes.array.isRequired,
    displayModal: PropTypes.bool.isRequired,
    showUserListLoader: PropTypes.bool.isRequired,
    userHasRole: PropTypes.func.isRequired,
    handleOwnerChange: PropTypes.func.isRequired,
    handleModalSubmit: PropTypes.func.isRequired
  };

  constructor(props) {
    super(props);
    this.renderClaimManagerDropdown = this.renderClaimManagerDropdown.bind(this);
    this.renderClaimManagerList = this.renderClaimManagerList.bind(this);
  }

  renderClaimManagerDropdown() {
    if (this.props.userList.length > 0) {
      return this.props.userList.map(this.renderClaimManagerList);
    }
  }

  renderClaimManagerList(user) {
    if (this.props.userHasRole(user, 'gap_claims_manager')) {
      return (<option value={user.id} key={user.id}>{`${user.first_name} ${user.last_name}`}</option>);
    }
  }

  render() {
    return (
      <section>
        <Modal visible={ this.props.displayModal }>
          <div className="my-3">
            <p className={ this.props.showUserListLoader === true ? 'text-center' : 'd-none'}>
              <i className="fa fa-refresh fa-spin"/> Fetching GAP Claim Manager list
            </p>
            <div
              className={ this.props.showUserListLoader === true ? 'd-none' : 'form-group text-center clearfix' }>
              <label className="form-check-label col-form-label col-form-label-sm">
                Select Owner:&nbsp;
              </label>
              <select className="form-control ml-1 col-7 d-inline-block" onChange={ this.props.handleOwnerChange }>
                <option value=''>Select Owner</option>
                { this.renderClaimManagerDropdown() }
              </select>
              <div className="text-center mt-2">
                <button className="btn btn-primary" onClick={ this.props.handleModalSubmit }>
                  <i className="fa fa-check"/>&nbsp;Submit
                </button>
              </div>
            </div>
          </div>
        </Modal>
      </section>
    );
  }
}