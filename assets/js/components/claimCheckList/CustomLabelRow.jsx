import React from 'react';
import InputBox from '../reusable/InputBox/InputBox.jsx';
import LabelInputBox from '../reusable/LabelInputBox/LabelInputBox.jsx';
import DocumentLinkList from "../reusable/DocumentLinkList/DocumentLinkList.jsx";
import ItemNote from "./ItemNote.jsx";
import PropTypes from 'prop-types';

export default class CustomLabelRow extends React.Component {

  static propTypes = {
    disableBtn: PropTypes.bool.isRequired,
    disableLabel: PropTypes.bool.isRequired,
    claimObject: PropTypes.object.isRequired,
    onCursorToggle: PropTypes.func,
    managerCheckBoxAttribute: PropTypes.string,
    showManagerCheckbox: PropTypes.bool,
    isDisabled: PropTypes.bool,
    actionButton: PropTypes.oneOf(['ADD', 'REMOVE']),
    keyValue: PropTypes.number,
    onLabelChange: PropTypes.func.isRequired,
    onValueChange: PropTypes.func.isRequired,
    onValueBlur: PropTypes.func.isRequired,
    label: PropTypes.string,
    value: PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.number
    ]),
    addRemoveRow: PropTypes.func.isRequired,
    fieldNotes: PropTypes.any,
    selectedFieldNote: PropTypes.object,
    handleItemNoteClick: PropTypes.func,
    handleMouseEnter: PropTypes.func,
    handleMouseLeave: PropTypes.func,
    handleAttachmentClick: PropTypes.func.isRequired,
    deleteAttachment: PropTypes.func,
    fieldId: PropTypes.number,
    rowType: PropTypes.string.isRequired,
    rowStyle: PropTypes.object.isRequired,
    rowId: PropTypes.string,
    fieldDocs: PropTypes.array,
    isFinanceUser: PropTypes.bool
  };

  constructor(props) {
    super(props);
    this.state = {
      isItemNotesSelected: false
    };
  }

  UNSAFE_componentWillReceiveProps = (nextProps) => {
    if (nextProps.fieldId === nextProps.selectedFieldNote.fieldId) {
      this.setState({
        isItemNotesSelected: true
      });
    } else if (nextProps.fieldId !== nextProps.selectedFieldNote.fieldId) {
      this.setState({
        isItemNotesSelected: false
      });
    }
  };

  getContainerStyle = () => {
    if (this.state.isItemNotesSelected) {
      return this.props.rowStyle;
    }
  };

  addRemoveRow = (id, action) => {
    if (!this.props.disableBtn) {
      this.props.addRemoveRow(id, action);
    }
  };

  renderAddRemoveOption = () => {
    if (this.props.actionButton === 'ADD') {
      return (
        <span className={ this.props.disableBtn ? "text-muted" : "cursor-pointer text-primary" }
          id={ `${this.props.rowId}_addRow` }
          onClick={ this.addRemoveRow.bind(this, this.props.keyValue, 'ADD') }>
          <i className="fa fa-plus"></i>
        </span>
      );
    } else if (this.props.actionButton === 'REMOVE') {
      return (
        <span className={ this.props.disableBtn ? "text-muted" : "cursor-pointer text-primary" }
          id={ `${this.props.rowId}_removeRow` }
          onClick={ this.addRemoveRow.bind(this, this.props.keyValue, 'REMOVE') }>
          <i className="fa fa-minus"></i>
        </span>
      );
    }
  };

  renderManagerCheckbox = (attribute_name) => {
    if (this.props.showManagerCheckbox) {
      return (
        <input type="checkbox"
          id={ `${this.props.rowId}_managerCheckBox` }
          checked={ this.props.claimObject[attribute_name] }
          onChange={ this.props.onCursorToggle.bind(null, attribute_name) }/>
      );
    }
  };

  render() {
    return (
      <div
        className={ this.state.isItemNotesSelected ? "form-group row justify-content-end bg-faded" : "form-group row justify-content-end" }
        key={ this.props.keyValue }
        style={ this.getContainerStyle() }>
        <LabelInputBox label={ this.props.label }
          rowId={ this.props.rowId }
          onChange={ this.props.onLabelChange }
          customClass='col-2'
          displayColon={ true }
          disableLabel={ this.props.disableLabel }/>
        <div className="col-2 px-0 d-inline-block">
          <InputBox type='Currency'
            id={ `${this.props.rowId}_inputBox` }
            customClass='col-10 d-inline-flex'
            hasDefaultValue={ true }
            value={ this.props.value }
            onChange={ this.props.onValueChange }
            onBlur={ this.props.onValueBlur }
            isDisabled={ this.props.isDisabled }/>
          { this.renderAddRemoveOption() }
        </div>
        <div className="col-1 px-0">
          { this.renderManagerCheckbox(this.props.managerCheckBoxAttribute) }
          <ItemNote onClick={ this.props.handleItemNoteClick }
            handleMouseEnter={ this.props.handleMouseEnter }
            handleMouseLeave={ this.props.handleMouseLeave }
            fieldNotes={ this.props.fieldNotes }
            rowType={ this.props.rowType }
            rowId={ this.props.rowId }
            fieldId={ this.props.fieldId }
            handleAttachmentClick={ this.props.handleAttachmentClick }/>
        </div>
        <div className="col-6">
          <DocumentLinkList fieldDocs={ this.props.fieldDocs }
            deleteAttachment={ this.props.deleteAttachment }
            isFinanceUser={ this.props.isFinanceUser }/>
        </div>
      </div>
    );
  }
}