import React from "react";
import accounting from "accounting";
import PropTypes from "prop-types";
import { json as ajax } from "./../../ajax.js";
import { URLS as apiUrls } from "./../reusable/Utilities/urls";
import Alert from "react-s-alert";
import { CONSTANTS } from "./../reusable/Constants/constants.js";
import Loader from "react-loader-advanced";

export default class VoidCheckFinanceInfo extends React.Component {

  static propTypes = {
    claimId: PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.number
    ]),
    claimStatus: PropTypes.string,
    onSelect: PropTypes.func,
    selectedClaimId: PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.number
    ])
  };

  constructor(props) {
    super(props);
    this.state = {
      showLoader: false,
      paymentObject: {}
    };
    this.getFinancialInformation = this.getFinancialInformation.bind(this);
  }

  componentDidMount() {
    if (this.props.claimId) {
      this.getFinancialInformation();
    }
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (this.props.claimId !== nextProps.claimId) {
      this.getFinancialInformation();
    }
  }

  getFinancialInformation() {
    this.setState({ showLoader: true }, function () {
      ajax(`${apiUrls.gapClaimPayment.replace('__claimId__', this.props.claimId)}`, {}, {}, (data, status) => {
        if (status === 200) {
          this.setState({ showLoader: false, paymentObject: data.gap_claim_payment });
        } else {
          this.setState({
            showLoader: false
          }, () => {
            Alert.error("Click the browser's Refresh button to reload the payment data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  }

  render() {
    const spinnerMessage = <p className='text-center'><i className="fa fa-refresh fa-spin"/> Loading...</p>;
    let amount = parseFloat(this.state.paymentObject.amount) ? parseFloat(this.state.paymentObject.amount) : undefined;
    if (amount === undefined && this.state.paymentObject.amount && this.state.paymentObject.amount.Valid) {
      amount = this.state.paymentObject.amount.Decimal;
    }
    return (
      <div className="col-12">
        <Loader show={this.state.showLoader} message={spinnerMessage}>
          <div className="row col-12 justify-content-center">
            <div className="col-1">
              <input type="radio"
                checked={this.props.selectedClaimId === this.props.claimId}
                disabled={this.props.claimStatus === CONSTANTS.STATUS_MAP.checkVoided}
                onChange={this.props.onSelect}/>
            </div>
            <div className="col-11">
              <div className="col-10 justify-content-start form-group row">
                <label className="col-5">
                  Authorization #:
                </label>
                <div className="col-7">
                  <p className="col-form-label-sm mb-0">{ this.state.paymentObject.authorization_number }</p>
                </div>
              </div>
              <div className="col-10 justify-content-start form-group row">
                <label className="col-5">
                  Check #:
                </label>
                <div className="col-7">
                  <p className="col-form-label-sm mb-0">{ this.state.paymentObject.check_number }</p>
                </div>
              </div>
              <div className="col-10 justify-content-start form-group row">
                <label className="col-5">
                  Amount:
                </label>
                <div className="col-7">
                  <p
                    className="col-form-label-sm mb-0">{ accounting.formatMoney(amount, "$", 2) }</p>
                </div>
              </div>
              <div className="col-10 justify-content-start form-group row">
                <label className="col-5">
                  Status:
                </label>
                <div className="col-7">
                  <p
                    className="col-form-label-sm mb-0">{ CONSTANTS.READABLE_STATUS_MAP[this.props.claimStatus] }</p>
                </div>
              </div>
            </div>
          </div>
        </Loader>
      </div>
    );
  }
}