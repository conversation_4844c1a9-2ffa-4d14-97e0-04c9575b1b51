import React from 'react';
import ItemNote from "./ItemNote.jsx";
import InputBox from "../reusable/InputBox/InputBox.jsx";
import DocumentLinkList from "../reusable/DocumentLinkList/DocumentLinkList.jsx";
import ReactTooltip from 'react-tooltip';
import PropTypes from 'prop-types';
import ClickToCopy from './../reusable/ClickToCopy/ClickToCopy.jsx';

export default class InputBoxRow extends React.Component {

  /***
   * claimObject: This prop is claim object which holds attributes for all the elements of GAP Claim worksheet.
   * containerClass: This prop is custom class value for row container.
   * labelContainerClass: This prop is custom class value for row label div container.
   * inputBoxContainerClass: This prop is custom class value for row label div container.
   * labelClass: This prop is custom class value for row label node container.
   * hasLabel: This prop should be true if the row has label.
   * label: This prop is label to be displayed in row on left side of row.
   * hasCheckbox: This prop should be true if the row has checkbox.
   * checkBoxAttribute: This prop is checkbox attribute in claimObject which holds checkbox status.
   * onCursorToggle: This is function prop to handle toggle behaviour of checkbox.
   * hasInputLabel: This prop should be true if the row has Input box instead of label on left side of row.
   * inputLabelPlaceholder: This prop is placeholder value for Input box used for  on left hand side of row
   * inputLabelAttribute: This prop is input label attribute in claimObject.
   * inputLabelCustomClass: This prop is custom class value for row input label on left side of row.
   * inputLabelType: This prop is input label type which has one of below mentioned value.
   * [ 'Currency', 'Number', 'Text', 'Percentage', 'AlphaNumeric', 'Currency-Negative', 'Currency-Positive' ]
   * hasInputBox: This prop should be true if the row has Input box instead of label on left side of row.
   * inputBoxType: This prop is input box type which has one of below mentioned value.
   * [ 'Currency', 'Number', 'Text', 'Percentage', 'AlphaNumeric', 'Currency-Negative', 'Currency-Positive' ]
   * inputBoxAttribute: This prop is input box attribute in claimObject which holds input box value.
   * onInputBoxChange: This is function props to handle 'onChange' event of input box.
   * onInputBoxBlur: This is function props to handle 'onBlur' event of input box.
   * placeholder: This prop holds placeholder text for input box.
   * hasDefaultValue: This prop will be true if box has default value as '0' and false if it doesn't have any default value.
   * hasTextAreaBox: This prop should be true if the row has Text area on right side of row.
   * textAreaRowSpan: This prop is row span value for text area.
   * hasTextValue: This prop should be true if the row has uneditable text label on right side of row.
   * textValue: This prop is value of uneditable text label which will be rendered on right side of row.
   * hasNode: This prop should be true if the row has DOM node on right side of row.
   * renderNode: This is function prop to load DOM node on right side of row.
   * isDisabled: This prop will be true if text area should be disabled.
   * rowId: This prop is string value of row which will be used to provide IDs to user input fields for automation.
   * fieldNotes: This prop consist of field note(Item not for fixed fields)
   * selectedFieldNote: This prop consist of selected Item note(row) object.
   * handleItemNoteClick: This prop is function callback to handle onClick event on ItemNote component.
   * handleMouseEnter: This prop is function callback to handle MouseEnter event on ItemNote component in order to handle the hover behaviour.
   * handleMouseLeave: This prop is function callback to handle MouseLeave even on ItemNote component to hide Record Note component displayed on MouseEnter.
   * fieldId: This prop consist of numeric ID assign to individual row from GAP_WORKSHEET_COMPONENT_MAP in constant js.
   * rowStyle: This prop consist of style of for row style when ItemNote component is clicked or hovered.
   * rowType: This prop have string value of Note Type weather it is Fixed Note or Dynamic Note.
   * fieldDocs: This prop has list of documents attached to this row.
   *
   * */

  static propTypes = {
    containerClass: PropTypes.string,
    managerCheckBoxAttribute: PropTypes.string,
    showManagerCheckbox: PropTypes.bool,
    labelContainerClass: PropTypes.string,
    inputBoxContainerClass: PropTypes.string,
    labelClass: PropTypes.string,
    hasLabel: PropTypes.bool,
    label: PropTypes.string,
    hasCheckbox: PropTypes.bool,
    checkBoxAttribute: PropTypes.string,
    onCursorToggle: PropTypes.func,
    hasInputLabel: PropTypes.bool,
    inputLabelPlaceholder: PropTypes.string,
    inputLabelAttribute: PropTypes.string,
    inputLabelCustomClass: PropTypes.string,
    inputLabelType: PropTypes.string,
    hasInputBox: PropTypes.bool,
    inputBoxType: PropTypes.string,
    inputBoxAttribute: PropTypes.string,
    hasDefaultValue: PropTypes.bool,
    onInputBoxChange: PropTypes.func,
    onInputBoxBlur: PropTypes.func,
    placeholder: PropTypes.string,
    hasTextAreaBox: PropTypes.bool,
    textAreaRowSpan: PropTypes.string,
    isDisabled: PropTypes.bool,
    hasTextValue: PropTypes.bool,
    textValue: PropTypes.oneOfType([
      PropTypes.number,
      PropTypes.string
    ]),
    hasNode: PropTypes.bool,
    renderNode: PropTypes.func,
    claimObject: PropTypes.object,
    rowId: PropTypes.string,
    fieldNotes: PropTypes.any,
    selectedFieldNote: PropTypes.object,
    handleItemNoteClick: PropTypes.func,
    handleMouseEnter: PropTypes.func,
    handleMouseLeave: PropTypes.func,
    fieldId: PropTypes.number,
    rowStyle: PropTypes.object,
    rowType: PropTypes.string,
    hideAttachment: PropTypes.bool,
    hideNotes: PropTypes.bool,
    handleAttachmentClick: PropTypes.func,
    deleteAttachment: PropTypes.func,
    fieldDocs: PropTypes.array,
    onLabelClick: PropTypes.func,
    isFinanceUser: PropTypes.bool,
    hasClickToCopy: PropTypes.bool,
    claimType: PropTypes.string,
  };

  constructor(props) {
    super(props);
    this.state = {
      isItemNotesSelected: false
    };
  }

  UNSAFE_componentWillReceiveProps = (nextProps) => {
    if (nextProps.selectedFieldNote && (nextProps.fieldId === nextProps.selectedFieldNote.fieldId)) {
      this.setState({
        isItemNotesSelected: true
      });
    } else if (nextProps.selectedFieldNote && (nextProps.fieldId !== nextProps.selectedFieldNote.fieldId)) {
      this.setState({
        isItemNotesSelected: false
      });
    }
  };

  renderLabel = () => {
    if (this.props.hasLabel && this.props.hasCheckbox) {
      return this.getLabelWithCheckbox();
    } else if (this.props.hasLabel) {
      return this.getLabel();
    }
  };

  getContainerClass = () => {
    let className = "form-group row";
    if (this.props.containerClass) {
      className += ` ${this.props.containerClass}`;
    }
    if (this.state.isItemNotesSelected) {
      className += " bg-faded";
    }
    return className;
  };

  getContainerStyle = () => {
    if (this.state.isItemNotesSelected) {
      return this.props.rowStyle;
    }
  };

  getLabel = () => {
    return (
      <label
        className={ this.props.labelClass ? `col-form-label col-form-label-sm ${this.props.labelClass}` : "col-form-label col-form-label-sm" }>
        { this.props.label }
      </label>
    );
  };

  getLabelWithCheckbox = () => {
    if (this.props.checkBoxAttribute === "has_run_amortization_sheet") {
      let amortizationInfo = "";
      let labelIcon = <i className="fa fa-exclamation-triangle fa-lg text-warning" aria-hidden="true"/>;

      if (this.props.claimObject.interest_rate === "0") {
        amortizationInfo = "Interest rate needed";
      } else if (this.props.claimObject.original_financing_contract_value === "0") {
        amortizationInfo = "Contract financing value needed";
      } else if (parseInt(this.props.claimObject.contract_term_months) <= 0) {
        amortizationInfo = "Term months needed";
      } else if (this.props.claimObject["calculate_amortization_value"]) {
        amortizationInfo = "System calculated value";
        labelIcon = <i className="fa fa-check-square fa-lg text-success" aria-hidden="true"/>;
      } else {
        amortizationInfo = "Manually entered value";
        labelIcon = <i className="fa fa-check-square fa-lg text-primary" aria-hidden="true"/>;
      }
      let label = <label className="cursor-pointer text-primary"
        id="label-run-amortization"
        onClick={ this.props.onLabelClick }><strong>{ this.props.label }</strong></label>;
      if (this.props.isDisabled) {
        label = <label id="label-run-amortization"
          className="cursor-pointer text-primary"><strong>{ this.props.label }</strong></label>;
      }
      return (
        <label
          className={ this.props.labelClass ? `form-check-label col-form-label col-form-label-sm pl-0 ${this.props.labelClass}` : "form-check-label col-form-label col-form-label-sm pl-0" }>
          { label }
          <span className="ml-2" data-tip data-for='amortizationInfo' data-place="bottom">{labelIcon}</span>
          <ReactTooltip id='amortizationInfo' aria-haspopup='true'>
            <span className="text-center">{amortizationInfo}</span>
          </ReactTooltip>
        </label>
      );
    }
    return (
      <label
        className={ this.props.labelClass ? `form-check form-check-label col-form-label col-form-label-sm ${this.props.labelClass}` : "form-check form-check-label col-form-label col-form-label-sm" }>
        <input type="checkbox"
          className="form-check-input"
          id={ `${this.props.rowId}_checkBox` }
          checked={ this.props.claimObject[this.props.checkBoxAttribute]}
          onChange={ this.props.onCursorToggle.bind(null, this.props.checkBoxAttribute) }
          disabled={ this.props.isDisabled }/>
        <span>{ this.props.label }</span>
      </label>
    );
  };

  renderInputBox = () => {
    let isDisabled = this.props.isDisabled;
    if (this.props.inputBoxAttribute === "run_amortization_sheet_value") {
      isDisabled = true;
    }
    if (this.props.hasInputBox) {
      return (
        <InputBox type={ this.props.inputBoxType }
          id={ `${this.props.rowId}_inputBox` }
          customClass={ this.props.inputBoxContainerClass }
          hasDefaultValue={ this.props.hasDefaultValue }
          placeholder={ this.props.placeholder }
          isDisabled={ isDisabled }
          value={ this.props.claimObject[this.props.inputBoxAttribute] }
          onChange={ this.props.onInputBoxChange.bind(null, this.props.inputBoxAttribute) }
          onBlur={ this.props.onInputBoxBlur.bind(null, this.props.inputBoxAttribute) }/>
      );
    }
  };

  renderTextArea = () => {
    if (this.props.hasTextAreaBox) {
      return (
        <div className={ this.props.inputBoxContainerClass }>
          <textarea
            id={ `${this.props.rowId}_textBox` }
            className="form-control form-control-sm"
            placeholder={ this.props.placeholder }
            rows={ this.props.textAreaRowSpan }
            value={ this.props.claimObject[this.props.inputBoxAttribute] }
            onChange={ this.props.onInputBoxChange.bind(null, this.props.inputBoxAttribute) }
            disabled={ this.props.isDisabled }/>
        </div>
      );
    }
  };

  renderTextValue = () => {
    if (this.props.hasTextValue) {
      return (
        <div className={ this.props.inputBoxContainerClass }>
          <p className="col-form-label-sm mb-0">{ this.props.textValue }</p>
        </div>
      );
    }
  };

  renderClickToCopy = () => {
    if (this.props.hasClickToCopy) {
      return (
        <div className={ this.props.inputBoxContainerClass }>
          <ClickToCopy className="col-form-label-sm mb-0" text={ this.props.textValue } />
        </div>
      );
    }
  };

  renderInputLabel = () => {
    if (this.props.hasInputLabel) {
      return (
        <InputBox type={ this.props.inputLabelType }
          placeholder={ this.props.inputLabelPlaceholder }
          customClass={ this.props.inputLabelCustomClass }
          id={ `${this.props.rowId}_inputLabel` }
          value={ this.props.claimObject[this.props.inputLabelAttribute] }
          onChange={ this.props.onInputBoxChange.bind(null, this.props.inputLabelAttribute) }
          onBlur={ this.props.onInputBoxChange.bind(null, this.props.inputLabelAttribute) }
          isDisabled={ this.props.isDisabled }/>
      );
    }
  };

  renderNodeTemplate = () => {
    if (this.props.hasNode) {
      return (
        <div className={ this.props.inputBoxContainerClass }>
          { this.props.renderNode() }
        </div>
      );
    }
  };

  renderManagerCheckbox = (attribute_name) => {
    if (this.props.showManagerCheckbox) {
      return (
        <input type="checkbox"
          id={ `${this.props.rowId}_managerCheckBox` }
          checked={ this.props.claimObject[attribute_name] }
          onChange={ this.props.onCursorToggle.bind(null, attribute_name) }/>
      );
    }
  };

  render() {
    return (
      <div className={ this.getContainerClass() } style={ this.getContainerStyle() }>
        <div className={ this.props.labelContainerClass }>
          { this.renderLabel() }
          { this.renderInputLabel() }
        </div>
        { this.renderInputBox() }
        { this.renderTextArea() }
        { this.renderTextValue() }
        { this.renderNodeTemplate() }
        { this.renderClickToCopy() }
        <div className="col-1 px-0">
          { this.renderManagerCheckbox(this.props.managerCheckBoxAttribute) }
          <ItemNote onClick={ this.props.handleItemNoteClick }
            handleMouseEnter={ this.props.handleMouseEnter }
            handleMouseLeave={ this.props.handleMouseLeave }
            fieldNotes={ this.props.fieldNotes }
            fieldId={ this.props.fieldId }
            rowId={ this.props.rowId }
            hideAttachment={ this.props.hideAttachment }
            hideNotes={ this.props.hideNotes }
            rowType={ this.props.rowType }
            claimType={ this.props.claimType }
            handleAttachmentClick={ this.props.handleAttachmentClick }/>
        </div>
        <div className="col-6">
          <DocumentLinkList fieldDocs={ this.props.fieldDocs }
            deleteAttachment={ this.props.deleteAttachment }
            claimType={ this.props.claimType }
            isFinanceUser={ this.props.isFinanceUser }/>
        </div>
      </div>
    );
  }
}