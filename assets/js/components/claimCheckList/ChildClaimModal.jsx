import React from "react";
import Modal from "../../Modal.jsx";
import PropTypes from 'prop-types';
import { CONSTANTS } from "./../reusable/Constants/constants.js";

export default class ChildClaimModal extends React.Component {

  static propTypes = {
    displayChildClaim: PropTypes.bool.isRequired,
    closeChildModal: PropTypes.func.isRequired,
    reopenType: PropTypes.oneOf(['reopen', 'reopenChild', 'createChild']),
    reopenClaim: PropTypes.func.isRequired,
    openChildClaim: PropTypes.func.isRequired,
    statusMap: PropTypes.object.isRequired,
    userHasRole: PropTypes.func.isRequired,
    user: PropTypes.shape({
      id: PropTypes.number.isRequired,
      first_name: PropTypes.string.isRequired,
      last_name: PropTypes.string.isRequired,
      email: PropTypes.string.isRequired,
      stores: PropTypes.arrayOf(PropTypes.string.isRequired),
      roles: PropTypes.shape({
        Map: PropTypes.object,
      }).isRequired
    })
  };

  constructor(props) {
    super(props);
  }

  getUserSpecificMessage = (buttonText, claimStatus) => {
    if (this.props.userHasRole(this.props.user, "gap_claims_manager")) {
      return (
        <div>
          <div className="row justify-content-center">
            <div className="col-11 mt-2">
              <div>
                <p>This claim has been approved and is waiting for a check. As a GAP manager, you can verify the check
                  will not been written and reopen the claim. Upon approving the modified claim, a new Authorization
                  number will be generated.</p>
                <p>If Finance is processing the check , the Agent will need to open a Child Claim for corrections.</p>
                <p>Are you sure you want to recall the authorization number?</p>
              </div>
            </div>
          </div>
          <div className="row justify-content-center my-2">
            <button className="btn btn-secondary mr-4" onClick={ this.props.closeChildModal }>Cancel</button>
            <button type="submit" className="btn btn-primary" onClick={ this.props.reopenClaim.bind(this, claimStatus) }>
              {buttonText}
            </button>
          </div>
        </div>
      );
    } else if (this.props.userHasRole(this.props.user, "gap_claims") && !this.props.userHasRole(this.props.user, 'gap_claims_manager')) {
      return (
        <div>
          <div className="row justify-content-center">
            <div className="col-11 mt-2">
              <div>
                <p>This claim has been approved and is waiting for a check. If you need to modify this claim, check with
                  Finance to ensure they have not and will not issue a check. If Finance has not issued a check, your
                  manager can reopen this claim.</p>
                <p>If Finance is processing the check , you will need to wait until the check number is displayed in the
                  claim and then create a Child Claim.</p>
                <p>If you need to add Notes, click Close and add notes as needed.</p>
              </div>
            </div>
          </div>
          <div className="row justify-content-center my-2">
            <button className="btn btn-secondary" onClick={ this.props.closeChildModal }>Close</button>
          </div>
        </div>
      );
    }
  };

  /**
   * This function will render the Modal content based on status if 'WC' or 'C'.
   * */

  renderConfirmationText = () => {
    switch (this.props.reopenType) {
    case 'reopen':
      return (
        <div className="col-11">
          <h5>Reopening an Approved Claim</h5>
          { this.getUserSpecificMessage("Re-Open Original Claim", CONSTANTS.STATUS_MAP.waitingForAuthorization) }
        </div>
      );
    case 'reopenChild':
      return (
        <div className="col-11">
          <h5>Reopening an Approved Child Claim</h5>
          { this.getUserSpecificMessage("Re-Open Child Claim", CONSTANTS.STATUS_MAP.pending) }
        </div>
      );
    case 'createChild':
      return (
        <div className="col-11">
          <h5>Reopening a Closed Claim</h5>
          <div className="row justify-content-center">
            <div className="col-11 mt-2">
              <div>
                <p>This claim is closed and a check has been written. If you need to modify this claim, you can add a
                  Child claim, which enables you to add an additional amount and reason</p>
                <p>If you need to add Notes, click Cancel and add notes as needed.</p>
              </div>
            </div>
          </div>
          <div className="row justify-content-center my-2">
            <button className="btn btn-secondary mr-4" onClick={ this.props.closeChildModal }>Cancel</button>
            <button type="submit" className="btn btn-primary" onClick={ this.props.openChildClaim }>
              Create Child Claim
            </button>
          </div>
        </div>
      );
    }
  };

  render() {
    return (
      <section>
        <Modal visible={ this.props.displayChildClaim } close={ this.props.closeChildModal }
          size="large">
          <div className="row justify-content-center">
            { this.renderConfirmationText() }
          </div>
        </Modal>
      </section>
    );
  }
}
