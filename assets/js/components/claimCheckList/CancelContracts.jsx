import React from 'react';
import CustomLabel from "./CustomLabelRow.jsx";
import ContractListRow from "./ContractListRow.jsx";
const COMPONENT_MAP = require("./../reusable/Constants/constants.js").CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP;
import PropTypes from 'prop-types';

export default class CancelContracts extends React.Component {

  static propTypes = {
    claimObject: PropTypes.object.isRequired,
    showManagerCheckbox: PropTypes.bool,
    onCursorToggle: PropTypes.func.isRequired,
    onCursorValueChange: PropTypes.func.isRequired,
    addRemoveCustomRow: PropTypes.func.isRequired,
    isDisabled: PropTypes.bool.isRequired,
    customRowsFieldMap: PropTypes.array.isRequired,
    getFieldNotes: PropTypes.func.isRequired,
    getContractFieldNotes: PropTypes.func.isRequired,
    getFieldDocs: PropTypes.func.isRequired,
    selectedFieldNote: PropTypes.object.isRequired,
    handleItemNoteClick: PropTypes.func.isRequired,
    handleMouseEnter: PropTypes.func.isRequired,
    handleMouseLeave: PropTypes.func.isRequired,
    handleAttachmentClick: PropTypes.func.isRequired,
    deleteAttachment: PropTypes.func,
    rowType: PropTypes.string.isRequired,
    rowStyle: PropTypes.object.isRequired,
    attachments: PropTypes.object.isRequired,
    notes: PropTypes.object.isRequired,
    isFinanceUser: PropTypes.bool
  };

  constructor(props) {
    super(props);
    this.handleCheckBoxChange = this.handleCheckBoxChange.bind(this);
    this.handleInputBoxChange = this.handleInputBoxChange.bind(this);
    this.hasMultipleInstance = this.hasMultipleInstance.bind(this);
    this.renderContractList = this.renderContractList.bind(this);
    this.renderCustomInputBox = this.renderCustomInputBox.bind(this);
    this.renderCustomInputBoxRow = this.renderCustomInputBoxRow.bind(this);
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (!this.isDisabled(nextProps.claimObject) && !nextProps.claimObject.has_canceled_contracts) {
      this.props.onCursorToggle('has_canceled_contracts');
    } else if (this.isDisabled(nextProps.claimObject) && nextProps.claimObject.has_canceled_contracts) {
      this.props.onCursorToggle('has_canceled_contracts');
    }
  }

  isDisabled(claimObject) {
    if (claimObject.contracts.length > 0) {
      for (let index = 0; index < claimObject.contracts.length; index++) {
        if (claimObject.contracts[index]['contract_flag'] !== true) {
          return true;
        }
      }
    }
    return false;
  }

  handleCheckBoxChange(index, flag) {
    const contracts = this.props.claimObject.contracts.slice(0);
    contracts[index][flag] = !contracts[index][flag];
    this.props.onCursorValueChange('contracts', contracts);
  }

  handleInputBoxChange(index, value) {
    const contracts = this.props.claimObject.contracts.slice(0);
    contracts[index]['contract_value'] = value;
    this.props.onCursorValueChange('contracts', contracts);
  }

  hasMultipleInstance(label) {
    let count = 0;
    for (let index = 0; index < this.props.claimObject.contracts.length; index++) {
      if (this.props.claimObject.contracts[index]['contract_code'] === label) {
        count++;
      }
    }
    return (count > 1);
  }

  getContractFieldDocs(attachments, contractDetails) {
    if (attachments && attachments.contract_field_documents && attachments.contract_field_documents.length > 0) {
      for (let index = 0; index < attachments.contract_field_documents.length; index++) {
        if (attachments.contract_field_documents[index]['contract_number'] === contractDetails.contract_number &&
          attachments.contract_field_documents[index]['contract_code'] === contractDetails.contract_code) {
          return attachments.contract_field_documents[index]['documents'];
        }
      }
    }
  }

  renderContractList() {
    const contractRows = [];
    if (this.props.claimObject.contracts.length > 0) {
      for (let index = 0; index < this.props.claimObject.contracts.length; index++) {
        let contractDetails = {
          contractNumber: this.props.claimObject.contracts[index]['contract_number'],
          contractCode: this.props.claimObject.contracts[index]['contract_code'],
          contractName: this.props.claimObject.contracts[index]['contract_name']
        };
        contractRows.push(<ContractListRow labelCustomClass="col-2"
          inputBoxCustomClass="col-2"
          isFinanceUser={this.props.isFinanceUser}
          contractDetails={ this.props.claimObject.contracts[index] }
          onCheckBoxChange={ this.handleCheckBoxChange }
          showManagerCheckbox={ this.props.showManagerCheckbox }
          isDisabled={this.props.isDisabled }
          onInputBoxChange={ this.handleInputBoxChange }
          onInputBoxBlur={ this.handleInputBoxChange }
          key={ index }
          fieldNotes={this.props.getContractFieldNotes(this.props.notes, contractDetails)}
          fieldDocs={this.getContractFieldDocs(this.props.attachments, this.props.claimObject.contracts[index])}
          displayTooltip={ this.hasMultipleInstance(this.props.claimObject.contracts[index]['contract_code']) }
          index={ index }
          selectedFieldNote={ this.props.selectedFieldNote }
          handleItemNoteClick={ this.props.handleItemNoteClick }
          handleMouseEnter={ this.props.handleMouseEnter }
          handleMouseLeave={ this.props.handleMouseLeave }
          handleAttachmentClick={this.props.handleAttachmentClick}
          deleteAttachment={this.props.deleteAttachment}
          rowStyle={ this.props.rowStyle }/>);
      }
    }
    return contractRows;
  }

  renderCustomInputBox(claimObject, isDisabled) {
    const customRows = [];
    const customRowsFieldMap = this.props.customRowsFieldMap.slice(0);
    for (let index = 0; index < customRowsFieldMap.length; index++) {
      if (index === 0 &&
        (parseFloat(claimObject[customRowsFieldMap[index + 1].value]) !== 0 || customRowsFieldMap[index + 1].show === true) &&
        (parseFloat(claimObject[customRowsFieldMap[index + 2].value]) !== 0 || customRowsFieldMap[index + 2].show === true)) {
        customRows.push(this.renderCustomInputBoxRow(true, 'ADD', customRowsFieldMap[index], claimObject, isDisabled));
      } else if (index === 0 && !(
        (parseFloat(claimObject[customRowsFieldMap[index + 1].value]) !== 0 || customRowsFieldMap[index + 1].show === true) &&
        (parseFloat(claimObject[customRowsFieldMap[index + 2].value]) !== 0 || customRowsFieldMap[index + 2].show === true))) {
        customRows.push(this.renderCustomInputBoxRow(false, 'ADD', customRowsFieldMap[index], claimObject, isDisabled));
      } else if (index !== 0 && (parseFloat(claimObject[customRowsFieldMap[index].value]) !== 0 || customRowsFieldMap[index].show === true)) {
        customRows.push(this.renderCustomInputBoxRow(false, 'REMOVE', customRowsFieldMap[index], claimObject, isDisabled));
      }
    }
    return customRows;
  }

  renderCustomInputBoxRow(disableBtn, actionButton, customRow, claimObject, isDisabled) {
    return (
      <div key={ customRow.id }>
        <CustomLabel disableBtn={ ((disableBtn || isDisabled) ? true : false) }
          disableLabel={ isDisabled }
          keyValue={ customRow.id }
          actionButton={ actionButton }
          isFinanceUser={this.props.isFinanceUser}
          onLabelChange={ this.props.onCursorValueChange.bind(null, customRow.label) }
          onValueChange={ this.props.onCursorValueChange.bind(null, customRow.value) }
          onValueBlur={ this.props.onCursorValueChange.bind(null, customRow.value) }
          addRemoveRow={ this.props.addRemoveCustomRow }
          onCursorToggle={ this.props.onCursorToggle }
          claimObject={this.props.claimObject}
          managerCheckBoxAttribute={ customRow.managerCheckBoxField }
          showManagerCheckbox={ this.props.showManagerCheckbox }
          isDisabled={this.props.isDisabled}
          label={ claimObject[customRow.label] }
          value={ claimObject[customRow.value] }
          rowId={ COMPONENT_MAP[customRow.itemNoteAttribute]['rowId'] }
          fieldId={ COMPONENT_MAP[customRow.itemNoteAttribute]['id'] }
          fieldNotes={ this.props.getFieldNotes(this.props.notes, COMPONENT_MAP[customRow.itemNoteAttribute]['id']) }
          fieldDocs={ this.props.getFieldDocs(this.props.attachments, COMPONENT_MAP[customRow.itemNoteAttribute]['id']) }
          selectedFieldNote={ this.props.selectedFieldNote }
          handleItemNoteClick={ this.props.handleItemNoteClick }
          handleMouseEnter={ this.props.handleMouseEnter }
          handleMouseLeave={ this.props.handleMouseLeave }
          handleAttachmentClick={this.props.handleAttachmentClick}
          deleteAttachment={this.props.deleteAttachment}
          rowType={ this.props.rowType }
          rowStyle={ this.props.rowStyle }/>
      </div>
    );
  }

  render() {
    const isDisabled = (this.props.isDisabled || this.isDisabled(this.props.claimObject));
    return (
      <div>
        <div className="form-check">
          <label className="form-check-label col-form-label col-form-label-sm">
            <input type="checkbox"
              className="form-check-input"
              id="cancelContract_checkBox"
              checked={ this.props.claimObject.has_canceled_contracts }
              onChange={ this.props.onCursorToggle.bind(null, 'has_canceled_contracts') }
              disabled={ isDisabled }/>
            <span>Cancel contracts</span>
          </label>
        </div>
        { this.renderContractList() }
        { this.renderCustomInputBox(this.props.claimObject, this.props.isDisabled) }
      </div>
    );
  }
}
