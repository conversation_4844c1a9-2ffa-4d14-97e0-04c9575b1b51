import React from "react";
import moment from "moment";
import dateFormat from "./../reusable/Utilities/dateFormat.js";
import ReactTooltip from "react-tooltip";
import { CONSTANTS } from "./../reusable/Constants/constants.js";
import PropTypes from "prop-types";
import If from "./../reusable/If/If.jsx";
import accounting from "accounting";
import { getClasses } from "../reusable/Utilities/headerClasses";
export default class Header extends React.Component {

  static contextTypes = {
    router: PropTypes.object.isRequired
  };

  static propTypes = {
    claimObject: PropTypes.shape({
      id: PropTypes.oneOfType([
        PropTypes.number,
        PropTypes.string
      ]).isRequired,
      contract_number: PropTypes.string.isRequired,
      vin: PropTypes.string.isRequired,
      updated_by_user_name: PropTypes.string.isRequired,
      updated_at: PropTypes.string.isRequired,
      first_name: PropTypes.string.isRequired,
      last_name: PropTypes.string.isRequired,
      is_business: PropTypes.bool.isRequired,
      business_name: PropTypes.string,
      customer_id: PropTypes.number
    }),
    user: PropTypes.shape({
      id: PropTypes.number.isRequired,
      first_name: PropTypes.string.isRequired,
      last_name: PropTypes.string.isRequired,
      email: PropTypes.string.isRequired,
      stores: PropTypes.arrayOf(PropTypes.string.isRequired),
      roles: PropTypes.shape({
        Map: PropTypes.object,
      }).isRequired,
      banner_info: PropTypes.shape({
        header: PropTypes.string.isRequired,
        message: PropTypes.string.isRequired,
        enabled: PropTypes.bool.isRequired,
      }).isRequired,
    }),
    nextButtonClassName: PropTypes.string,
    nextButtonText: PropTypes.string.isRequired,
    nextButtonDisabled: PropTypes.bool,
    nextButtonOnClick: PropTypes.func,
    backButtonOnClick: PropTypes.func,
    pageTitle: PropTypes.string.isRequired,
    lastUpdatedBy: PropTypes.string,
    lastUpdatedAt: PropTypes.string,
    claimStatus: PropTypes.string,
    showHistoryButtonClick: PropTypes.func,
    renderAssignOwnerButton: PropTypes.bool,
    assignOwnerButtonClick: PropTypes.func,
    showVoidCheckButton: PropTypes.bool,
    handleOnClickVoidCheck: PropTypes.func,
    isFinanceUser: PropTypes.bool,
    hasAttachment: PropTypes.func,
    getCaseReserveAmount: PropTypes.func,
    enableRecoveryCase: PropTypes.bool,
  };

  constructor(props) {
    super(props);
    this.onContractNumberClick = this.onContractNumberClick.bind(this);
    this.renderLastUpdatedBy = this.renderLastUpdatedBy.bind(this);
    this.renderNextButton = this.renderNextButton.bind(this);
    this.renderAssignOwnerButton = this.renderAssignOwnerButton.bind(this);
    this.renderVoidCheckButton = this.renderVoidCheckButton.bind(this);
    this.redirectToEmail = this.redirectToEmail.bind(this);
    this.getCaseReserveLabel = this.getCaseReserveLabel.bind(this);
  }

  onContractNumberClick() {
    window.open(`/gap-contract/${this.props.claimObject.contract_number}`, this.props.claimObject.contract_number, "width=1000,height=600");
  }

  getCaseReserveLabel(claimObject) {
    if (this.props.claimStatus === CONSTANTS.STATUS_MAP.inquiry || !this.props.hasAttachment() || claimObject.is_cs_claim) {
      return 'Reserve';
    } else if (!claimObject.settlement_amount ||
      claimObject.settlement_amount === "0" ||
      !claimObject.run_amortization_sheet_value ||
      claimObject.run_amortization_sheet_value === "0") {
      return 'Average';
    } else if (claimObject.settlement_amount && claimObject.run_amortization_sheet_value && this.props.hasAttachment()) {
      return 'Reserve';
    }
  }

  renderLastUpdatedBy() {
    return (
      <span
        className="row flex-row-reverse pr-3">
          Last updated by: { `${this.props.claimObject.updated_by_user_name} - ${moment(new Date(this.props.claimObject.updated_at)).format(dateFormat.displayDateFormat)}` }
      </span>
    );
  }

  renderNextButton() {
    if (this.props.nextButtonText && !this.props.isFinanceUser) {
      return (
        <button className={ `btn btn-primary mr-2 cursor-pointer${this.props.nextButtonDisabled ? " disabled" : ""}` }
          onClick={ this.props.nextButtonOnClick }
          id="btn-next"
          disabled={ !!this.props.nextButtonDisabled }>
          <i className={ `fa ${this.props.nextButtonClassName}` }/>
          &nbsp;{this.props.nextButtonText}
        </button>
      );
    }
  }

  renderAssignOwnerButton() {
    if (this.props.renderAssignOwnerButton && !this.props.isFinanceUser) {
      return (
        <button type="button"
          className="btn btn-secondary mr-2 cursor-pointer"
          id="btn-assign-owner"
          onClick={ this.props.assignOwnerButtonClick }>
          <i className="fa fa-crosshairs"/>&nbsp;Assign
        </button>
      );
    }
  }

  renderVoidCheckButton() {
    if (this.props.showVoidCheckButton && !this.props.isFinanceUser) {
      return (
        <button type="button"
          className="btn btn-primary mr-2 cursor-pointer"
          id="btn-void-check"
          onClick={ this.props.handleOnClickVoidCheck }>
          <i className="fa fa-times"/>
          &nbsp;Void Check
        </button>
      );
    }
  }

  redirectToEmail() {
    if (this.props.claimObject && this.props.claimObject.id && this.props.claimObject.contract_number
      && this.props.claimObject.vin && (this.props.claimObject.first_name ||
        this.props.claimObject.last_name || this.props.claimObject.business_name)) {
      let customerName = this.props.claimObject.last_name.concat(',',this.props.claimObject.first_name);
      customerName = customerName.trim();
      if (this.props.claimObject.is_business) {
        if (customerName) {
          customerName = customerName+'/';
        }
        customerName = customerName.concat(this.props.claimObject.business_name);
      }
      const query = {
        id: this.props.claimObject.id,
        contractNumber: this.props.claimObject.contract_number,
        vin: this.props.claimObject.vin,
        customerName: customerName,
        customerID: this.props.claimObject.customer_id
      };
      const route = { pathname: "/gap-email", query };
      if (!this.context.router.isActive(route)) {
        this.context.router.push(route);
      }
    }
  }
  loadPDF = () => {
    window.open(`/api/gap-claims/${this.props.claimObject.id}/pdf`);
  };

  render() {
    const {
      claimObject,
      user : {
        banner_info: bannerInfo,
      },
    } = this.props;
    const classes = getClasses(bannerInfo);
    const pageSpacingStyle = { marginTop: '200px' };
    let customerName = claimObject.last_name.concat(",",claimObject.first_name);
    customerName = customerName.trim();
    if (claimObject.is_business) {
      if (customerName) {
        customerName+="/";
      }
      customerName = customerName.concat(claimObject.business_name);
    } 
    return (
      <section className="page-header" style={ pageSpacingStyle }>
        <div className={classes}>
          <div className="row align-items-center">
            <div className="col-5 text-truncate h2">
              <h2 className="d-inline-block">{ this.props.pageTitle }&nbsp;-&nbsp;</h2>
              {`${customerName}`}
            </div>
            <div className="col-3 text-center h3">
              <p className="d-inline-block h4">
                <a href="#!"
                  id="link-contract-number"
                  onClick={ this.onContractNumberClick }>{this.props.claimObject.contract_number}</a>
              </p>
              <div className="d-inline-block h4">&nbsp;&nbsp;&nbsp;
                <span data-tip data-for='claimStatus' data-place="bottom">
                  { this.props.claimStatus }
                </span>
                <ReactTooltip id='claimStatus' aria-haspopup='true'>
                  <span className="text-center">{ CONSTANTS.READABLE_STATUS_MAP[this.props.claimStatus] }</span>
                </ReactTooltip>
              </div>
            </div>
            <div className="col-4">
              { this.renderLastUpdatedBy() }
            </div>
          </div>
          <div className="row py-3">
            <div className="col-5">
              <button onClick={ this.props.backButtonOnClick }
                className="btn btn-secondary mr-2 cursor-pointer"
                id="btn-back">
                <i className="fa fa-arrow-left"/>
                &nbsp;Back
              </button>
              { this.renderNextButton() }
              { this.renderAssignOwnerButton() }
              { this.renderVoidCheckButton() }
            </div>
            <div className="col-3 col-form-label-sm text-center">
              <label className="h5">
                { `${this.getCaseReserveLabel(this.props.claimObject)}:` }&nbsp;&nbsp;
              </label>
              <p className="d-inline-block h5">
                { accounting.formatMoney(this.props.getCaseReserveAmount(), '$', 2) }
              </p>
            </div>
            <div className="col-4">
              <div className="row flex-row-reverse pr-3">
                <div className="btn-toolbar">
                  <button type="button" className="btn mr-2 btn-secondary" title="Print" 
                    onClick={ this.loadPDF.bind() }>
                    <i className='fa fa-print'/>
                  </button>
                  <If condition={ !this.props.isFinanceUser }>
                    <button type="button"
                      className="btn btn-secondary mr-2 cursor-pointer"
                      id="btn-letters"
                      onClick={ this.redirectToEmail }>
                      Letters
                    </button>
                  </If>
                  <button type="button"
                    className="btn btn-secondary mr-2 cursor-pointer"
                    id="btn-contract-notes"
                    onClick={ this.props.showHistoryButtonClick.bind(null, "contract") }>
                    Contract Notes
                  </button>
                  <button type="button"
                    className="btn btn-secondary mr-2 cursor-pointer"
                    id="btn-claim-history"
                    onClick={ this.props.showHistoryButtonClick.bind(null, "claim") }>
                    Claim History
                  </button>
                  { this.props.enableRecoveryCase &&
                    <button type="button"
                      className="btn btn-secondary mr-2 cursor-pointer"
                      id="btn-claim-history"
                      onClick={ this.props.showHistoryButtonClick.bind(null, "recovery") }>
                      Recovery Notes
                    </button>
                  }
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    );
  }
}
