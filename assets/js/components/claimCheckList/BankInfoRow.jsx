import React from 'react';
import ItemNote from "./ItemNote.jsx";
import DocumentLinkList from "./../reusable/DocumentLinkList/DocumentLinkList.jsx";
import PropTypes from 'prop-types';

export default class BankInfoRow extends React.Component {

  static propTypes = {
    showManagerCheckbox: PropTypes.bool,
    managerCheckBoxAttribute: PropTypes.string,
    claimObject: PropTypes.object.isRequired,
    onCursorToggle: PropTypes.func.isRequired,
    loadBankSelectionModal: PropTypes.func.isRequired,
    isDisabled: PropTypes.bool.isRequired,
    displayBankSelectionModal: PropTypes.bool.isRequired,
    fieldNotes: PropTypes.any,
    selectedFieldNote: PropTypes.object,
    handleItemNoteClick: PropTypes.func,
    handleMouseEnter: PropTypes.func,
    handleMouseLeave: PropTypes.func,
    handleAttachmentClick: PropTypes.func.isRequired,
    deleteAttachment: PropTypes.func,
    fieldId: PropTypes.number,
    rowType: PropTypes.string.isRequired,
    rowStyle: PropTypes.object.isRequired,
    rowId: PropTypes.string,
    fieldDocs: PropTypes.array,
    isFinanceUser: PropTypes.bool
  };

  constructor(props) {
    super(props);
    this.state = {
      isItemNotesSelected: false
    };
  }

  UNSAFE_componentWillReceiveProps = (nextProps) => {
    if (nextProps.fieldId === nextProps.selectedFieldNote.fieldId) {
      this.setState({
        isItemNotesSelected: true
      });
    } else if (nextProps.fieldId !== nextProps.selectedFieldNote.fieldId) {
      this.setState({
        isItemNotesSelected: false
      });
    }
  };

  getContainerStyle = () => {
    if (this.state.isItemNotesSelected) {
      return this.props.rowStyle;
    }
  };

  renderBankSelection = (claimObject, isDisabled) => {
    if (!claimObject.bank_account_name || this.props.displayBankSelectionModal) {
      return (
        <div className="row">
          <div className="col-10">
            <a href="#!"
              className="small"
              id={ `${this.props.rowId}_addBank` }
              onClick={ this.props.loadBankSelectionModal }>
              <i className="fa fa-plus" aria-hidden="true"></i>&nbsp;Add Bank Information
            </a>
          </div>
        </div>
      );
    } else {
      return (
        <div className="row">
          <div className="col-10">
            <a href="#!"
              id={ `${this.props.rowId}_updateBank` }
              className={ isDisabled && "text-muted" }
              onClick={ !isDisabled && this.props.loadBankSelectionModal }
              disabled={ isDisabled }>
              <strong>{ claimObject.bank_account_name }</strong>
            </a>
            <p className="small mb-0">{ claimObject.bank_address_street1 }</p>
            <p className="small mb-0">{ claimObject.bank_address_street2 }</p>
            <div className="small">
              <span>{ claimObject.bank_address_city } </span>
              <span>{ claimObject.bank_address_state } </span>
              <span>{ claimObject.bank_address_zip }</span>
            </div>
          </div>
        </div>
      );
    }
  };

  renderManagerCheckbox = (attribute_name) => {
    if (this.props.showManagerCheckbox) {
      return (
        <input type="checkbox"
          id={ `${this.props.rowId}_managerCheckBox` }
          checked={ this.props.claimObject[attribute_name] }
          onChange={ this.props.onCursorToggle.bind(null, attribute_name) }/>
      );
    }
  };

  render() {
    return (
      <div
        className={ this.state.isItemNotesSelected ? "form-group row justify-content-end bg-faded" : "form-group row justify-content-end" }
        style={ this.getContainerStyle() }>
        <div className="col-3">
          <label className="form-check form-check-label col-form-label col-form-label-sm">
            <input type="checkbox"
              id={ `${this.props.rowId}_checkBox` }
              className="form-check-input"
              checked={ this.props.claimObject.has_bank_information }
              onChange={ this.props.onCursorToggle.bind(null, 'has_bank_information') }
              disabled={ this.props.isDisabled }/>
            <span>Bank information</span>
          </label>
        </div>
        <div className="col-2 d-inline-block">
          { this.renderBankSelection(this.props.claimObject, this.props.isDisabled) }
        </div>
        <div className="col-1 px-0">
          { this.renderManagerCheckbox(this.props.managerCheckBoxAttribute) }
          <ItemNote onClick={ this.props.handleItemNoteClick }
            handleMouseEnter={ this.props.handleMouseEnter }
            handleMouseLeave={ this.props.handleMouseLeave }
            fieldNotes={ this.props.fieldNotes }
            rowType={ this.props.rowType }
            rowId={ this.props.rowId }
            fieldId={ this.props.fieldId }
            handleAttachmentClick={ this.props.handleAttachmentClick }/>
        </div>
        <div className="col-6">
          <DocumentLinkList fieldDocs={ this.props.fieldDocs }
            deleteAttachment={ this.props.deleteAttachment }
            isFinanceUser={ this.props.isFinanceUser }/>
        </div>
      </div>
    );
  }
}