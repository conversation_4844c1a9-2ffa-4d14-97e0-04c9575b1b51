import React from 'react';
import { json as ajax } from "./../../ajax.js";
import Modal from "../../Modal.jsx";
import Alert from "react-s-alert";
import Loader from "react-loader-advanced";
import { URLS as apiUrls } from "./../reusable/Utilities/urls";
import PropTypes from 'prop-types';

export default class BankSelectionModal extends React.Component {

  static propTypes = {
    searchQuery: PropTypes.string,
    bankDetails: PropTypes.object,
    selectBankDetails: PropTypes.func.isRequired,
    closeBankSelectionModal: PropTypes.func.isRequired,
    displayBankSelectionModal: PropTypes.bool.isRequired
  };

  constructor(props) {
    super(props);
    this.state = {
      searchQuery: props.searchQuery,
      bankDetails: {},
      showBankDetailsLoader: false,
      loadBankList: false
    };
    this.getBankList = this.getBankList.bind(this);
    this.onBankSearchQueryChange = this.onBankSearchQueryChange.bind(this);
    this.selectBankDetails = this.selectBankDetails.bind(this);
    this.renderBankDetails = this.renderBankDetails.bind(this);
    this.renderBankListUnit = this.renderBankListUnit.bind(this);
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    const { bankDetails } = nextProps;
    if(bankDetails && Object.keys(bankDetails).length !== 0){
      this.setState({
        searchQuery: nextProps.searchQuery,
        bankDetails: bankDetails,
        loadBankList: true
      });
    }
    else if(nextProps.searchQuery && this.props.searchQuery !== nextProps.searchQuery) {
      this.setState({
        searchQuery: nextProps.searchQuery,
      }, () => {
        this.fetchBankList(nextProps.searchQuery);
      });
    }
  }

  /**
   * This function will fetch bank details based on 'Bank name' or 'Bank zipcode'
   * In order to search bank details, User needs to enter atleast 3 characters in 'Bank name field' or full zipcode.
   *
   * @param event
   * */

  getBankList(event) {
    event.preventDefault();
    if ((this.state.searchQuery && this.state.searchQuery.length >= 3)) {
      this.fetchBankList(this.state.searchQuery);
    }
  }

  fetchBankList(name) {
    this.setState({ showBankDetailsLoader: true }, () => {
      ajax(`${apiUrls.gapIntacct}?name=${name.toString().toUpperCase() || ''}`, {}, {}, (data, status) => {
        if (status === 200) {
          this.setState({ showBankDetailsLoader: false, loadBankList: true, bankDetails: data });
        } else if (status === 404) {
          this.setState({ showBankDetailsLoader: false, loadBankList: true, bankDetails: {} });
        }
        else {
          this.setState({ showBankDetailsLoader: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  }

  onBankSearchQueryChange(state, event) {
    const stateChange = {};
    stateChange[state] = event.target.value;
    this.setState(stateChange);
  }

  selectBankDetails(data) {
    this.setState({
      searchQuery: '',
      loadBankList: false
    }, function () {
      this.props.selectBankDetails(data);
    });
  }

  renderBankDetails() {
    if (this.state.loadBankList && this.state.bankDetails && this.state.bankDetails.vendors && Array.isArray(this.state.bankDetails.vendors) && this.state.bankDetails.vendors.length > 0) {
      return (
        <div className="row justify-content-end">
          <div className="col-12">
            <p className="small text-muted"><em>Showing { this.state.bankDetails.count } items</em></p>
          </div>
          { this.state.bankDetails.vendors.map(this.renderBankListUnit) }
        </div>
      );
    } else if (this.state.loadBankList && this.state.bankDetails && (!this.state.bankDetails.count || this.state.bankDetails.count === 0)) {
      return (
        <div className="row justify-content-center">
          <div className="col-4">
            <p className="text-center">No Data Available.</p>
          </div>
        </div>
      );
    }
  }

  renderBankListUnit(data, index) {
    return (
      <div key={ index } className="col-12 cursor-pointer" onClick={ this.selectBankDetails.bind(this, data) }>
        <div className="dropdown-divider"></div>
        <p className="mb-0"><strong>{ `${data.name} (${data.vendor_id})`}</strong></p>
        <p className="small mb-0">{ data.address1 }</p>
        <p className="small mb-0">{ data.address2 }</p>
        <div className="small">
          <span>{data.city}</span>&nbsp;
          <span>{data.state}</span>&nbsp;
          <span>{data.zip}</span>
        </div>
        <p className="small mb-0">{data.country}</p>
      </div>
    );
  }

  render() {
    const spinnerMessage = <p className='text-center'><i className="fa fa-refresh fa-spin"/> Loading...</p>;
    return (
      <section>
        <Modal visible={ this.props.displayBankSelectionModal } close={ this.props.closeBankSelectionModal }>
          <Loader show={ this.state.showBankDetailsLoader } message={ spinnerMessage }>
            <div className="row justify-content-center">
              <div className="col-12 my-2">
                <form className="form-inline row justify-content-center">
                  <div className="input-group col-8">
                    <input type="text"
                      className="form-control"
                      value={ this.state.searchQuery }
                      onChange={ this.onBankSearchQueryChange.bind(this, 'searchQuery') }
                      placeholder="Search by name or vendor id"/>
                    <span className="input-group-btn">
                      <button type="submit"
                        className="btn btn-primary"
                        onClick={ this.getBankList }>
                        Go
                      </button>
                    </span>
                  </div>
                </form>
              </div>
              <div className="col-8 mb-4" style={ { maxHeight: '300px', overflow: 'auto' } }>
                { this.renderBankDetails() }
              </div>
            </div>
          </Loader>
        </Modal>
      </section>
    );
  }
}