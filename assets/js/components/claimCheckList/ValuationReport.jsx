import React from 'react';
import DateRow from "./DateRow.jsx";
import InputBoxRow from "./InputBoxRow.jsx";
import If from "./../reusable/If/If.jsx";
import PropTypes from 'prop-types';
import { CONSTANTS } from '../reusable/Constants/constants';
import accounting from 'accounting';
import { URLS as apiUrls } from '../reusable/Utilities/urls';
import Alert from 'react-s-alert';
import { json as ajax } from '../../ajax';
import Select from "react-select";

const menuContainerStyle = {
  zIndex: 999
};

export default class ValuationReport extends React.Component {

  static propTypes = {
    addInsuranceCompanyName: PropTypes.func.isRequired,
    claimObject: PropTypes.object.isRequired,
    showManagerCheckbox: PropTypes.bool,
    isDisabled: PropTypes.bool,
    onCursorToggle: PropTypes.func.isRequired,
    onCursorValueChange: PropTypes.func.isRequired,
    handleDateChange: PropTypes.func.isRequired,
    onCursorChange: PropTypes.func.isRequired,
    dateFormat: PropTypes.string.isRequired,
    getFieldNotes: PropTypes.func.isRequired,
    getFieldDocs: PropTypes.func.isRequired,
    selectedFieldNote: PropTypes.object.isRequired,
    handleItemNoteClick: PropTypes.func.isRequired,
    handleMouseEnter: PropTypes.func.isRequired,
    handleMouseLeave: PropTypes.func.isRequired,
    handleAttachmentClick: PropTypes.func.isRequired,
    deleteAttachment: PropTypes.func.isRequired,
    rowType: PropTypes.string.isRequired,
    rowStyle: PropTypes.object.isRequired,
    attachments: PropTypes.object.isRequired,
    notes: PropTypes.object.isRequired,
    isFinanceUser: PropTypes.bool
  };

  constructor(props) {
    super(props);
    this.state = {
      queryString: '',
      searching: false,
      typingTimeOut: 0,
      insuranceCompanies: []
    };
  }

  searchInsuranceCompany = () => {
    this.setState({ searching: true }, function () {
      ajax(`${apiUrls.insuranceCompanies}?q=${encodeURIComponent(this.state.queryString)}`, {}, {}, (data, status) => {
        if (status === 200) {
          this.setState({ insuranceCompanies: data.insurance_companies, searching: false });
        } else {
          this.setState({ searching: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  updateInsuranceCompany = queryString => {
    if (this.state.typingTimeOut) {
      clearTimeout(this.state.typingTimeOut);
    }
    this.setState({
      queryString: queryString,
      typingTimeOut: setTimeout(() => {
        if (this.state.queryString && this.state.queryString.length > 2) {
          this.searchInsuranceCompany();
        }
      }, 500)
    });
  };

  onInsuranceCompanySelection = insuranceCompany => {
    if (insuranceCompany) {
      this.props.onCursorValueChange('insurance_company', insuranceCompany.value);
    } else {
      this.props.onCursorValueChange('insurance_company', '');
    }
  };

  getDifference = () => {
    const claim = this.props.claimObject;
    return accounting.formatMoney(claim.nada - claim.valuation_report_adjustments, '$', 2);
  };

  getDelinquentPayments = () => {
    const claim = this.props.claimObject;
    return accounting.formatMoney(claim.number_of_delinquent_payments * claim.payment_amount, '$', 2);
  };

  getExtensions = () => {
    const claim = this.props.claimObject;
    return accounting.formatMoney(claim.number_of_extensions * claim.payment_amount, '$', 2);
  };

  getInsuranceCompanyOptions() {
    let insuranceCompaniesArray = [];
    if (this.state.insuranceCompanies && this.state.insuranceCompanies.length > 0) {
      insuranceCompaniesArray = this.state.insuranceCompanies.map((element) => {
        return { label: element.name, value: element.name };
      });
    }
    if (this.props.claimObject && this.props.claimObject.insurance_company) {
      const insuranceCompany = insuranceCompaniesArray.find((item) => (item.label === this.props.claimObject.insurance_company));
      if (!insuranceCompany) {
        insuranceCompaniesArray = [ {
          value: this.props.claimObject.insurance_company,
          label: this.props.claimObject.insurance_company
        }, ...insuranceCompaniesArray ];
      }
    }
    return insuranceCompaniesArray;
  }

  filterOptions = (options, filter, currentValues) => {
    // If there are not any options available yet, then
    // make the current user input available as an option.
    if (filter !== '' && options.length === 0) {
      return [{ value: -1, label: filter }];
    }
    return options;
  }

  renderInsuranceCompanyDropdown = () => {
    return (
      <div className="col-12 px-0 col-12 d-inline-flex align-items-center">
        <Select
          value={ this.props.claimObject.insurance_company || 0 }
          id="insurance-company-dropdown"
          disabled={ this.props.isDisabled }
          isLoading={ this.state.searching }
          options={ this.getInsuranceCompanyOptions() }
          onChange={ this.onInsuranceCompanySelection }
          onInputChange={ this.updateInsuranceCompany }
          filterOptions={ this.filterOptions }
          menuContainerStyle={ menuContainerStyle }
          className="col-10 px-0"
          tabSelectsValue={ false }
          matchProp='label'/>
        <button
          type="button"
          className={ `cursor-pointer col-2 px-2 btn btn-link border-0 ${this.props.isDisabled ? "text-muted": "text-primary"}` }
          onClick={ !this.props.isDisabled && this.props.addInsuranceCompanyName }>
          <i className="fa fa-plus" />
        </button>
      </div>
    );
  };

  renderTypeDropdown = (COMPONENT_MAP) => {
    return (
      <select className="form-control form-control-sm"
        id={ `${COMPONENT_MAP[ 'type' ][ 'rowId' ]}_dropdown` }
        value={ this.props.claimObject.valuation_report_type }
        onChange={ this.props.onCursorChange.bind(null, 'valuation_report_type') }
        disabled={ this.props.isDisabled }>
        <option value="">Select type</option>
        <option value="accident">Accident</option>
        <option value="hail">Hail</option>
        <option value="flood">Flood</option>
        <option value="theft">Theft</option>
        <option value="other">Other</option>
      </select>
    );
  };

  render() {
    const COMPONENT_MAP = CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP;
    const {
      isFinanceUser,
      isDisabled,
      onCursorValueChange,
      onCursorChange,
      onCursorToggle,
      claimObject,
      handleDateChange,
      dateFormat,
      showManagerCheckbox,
      getFieldNotes,
      getFieldDocs,
      notes,
      attachments,
      selectedFieldNote,
      handleItemNoteClick,
      handleMouseEnter,
      handleMouseLeave,
      handleAttachmentClick,
      deleteAttachment,
      rowType,
      rowStyle,
    } = this.props;
    return (
      <span className="valuation-report">
        <InputBoxRow labelContainerClass="col-3"
          inputBoxContainerClass="col-2"
          containerClass="justify-content-end"
          isFinanceUser={isFinanceUser }
          isDisabled={ isDisabled }
          onCursorToggle={ onCursorToggle }
          claimObject={ claimObject }
          showManagerCheckbox={ false }
          hasLabel={ true }
          hasCheckbox={ true }
          checkBoxAttribute="has_insurance_company"
          hasNode={ true }
          label="Insurance Company:"
          renderNode={ this.renderInsuranceCompanyDropdown }
          rowId={ COMPONENT_MAP[ 'insuranceCompany' ][ 'rowId' ] }
          fieldId={ COMPONENT_MAP[ 'insuranceCompany' ][ 'id' ] }
          fieldNotes={ getFieldNotes(notes, COMPONENT_MAP[ 'insuranceCompany' ][ 'id' ]) }
          fieldDocs={ getFieldDocs(attachments, COMPONENT_MAP[ 'insuranceCompany' ][ 'id' ]) }
          selectedFieldNote={ selectedFieldNote }
          handleItemNoteClick={ handleItemNoteClick }
          handleMouseEnter={ handleMouseEnter }
          handleMouseLeave={ handleMouseLeave }
          handleAttachmentClick={ handleAttachmentClick }
          deleteAttachment={ deleteAttachment }
          rowType={ rowType }
          rowStyle={ rowStyle }/>
        <InputBoxRow labelContainerClass="col-2"
          inputBoxContainerClass="col-2"
          containerClass="justify-content-end"
          isFinanceUser={ isFinanceUser }
          hasLabel={ true }
          hasCheckbox={ true }
          hasInputBox={ true }
          checkBoxAttribute="has_policy_number"
          label="Claim Number:"
          inputBoxType="Text"
          inputBoxAttribute="policy_number"
          showManagerCheckbox={ false }
          isDisabled={ isDisabled }
          hasDefaultValue={ true }
          claimObject={ claimObject }
          onCursorToggle={ onCursorToggle }
          onInputBoxChange={ onCursorValueChange }
          onInputBoxBlur={ onCursorValueChange }
          rowId={ COMPONENT_MAP[ 'policyNumber' ][ 'rowId' ] }
          fieldId={ COMPONENT_MAP[ 'policyNumber' ][ 'id' ] }
          fieldNotes={ getFieldNotes(notes, COMPONENT_MAP[ 'policyNumber' ][ 'id' ]) }
          fieldDocs={ getFieldDocs(attachments, COMPONENT_MAP[ 'policyNumber' ][ 'id' ]) }
          selectedFieldNote={ selectedFieldNote }
          handleItemNoteClick={ handleItemNoteClick }
          handleMouseEnter={ handleMouseEnter }
          handleMouseLeave={ handleMouseLeave }
          handleAttachmentClick={ handleAttachmentClick }
          deleteAttachment={ deleteAttachment }
          rowType={ CONSTANTS.FIELD_TYPE.static }
          rowStyle={ rowStyle }/>
        <InputBoxRow labelContainerClass="col-3"
          isFinanceUser={ isFinanceUser }
          inputBoxContainerClass="col-2"
          hasLabel={ true }
          hasCheckbox={ true }
          hasInputBox={ true }
          checkBoxAttribute="has_number_of_delinquent_payments"
          label="Number of  Delinquent Payments"
          inputBoxType="Number"
          inputBoxAttribute="number_of_delinquent_payments"
          managerCheckBoxAttribute="number_of_delinquent_payments_manager_flag"
          showManagerCheckbox={ showManagerCheckbox }
          isDisabled={ isDisabled }
          hasDefaultValue={ true }
          claimObject={ claimObject }
          onCursorToggle={ onCursorToggle }
          onInputBoxChange={ onCursorValueChange }
          onInputBoxBlur={ onCursorValueChange }
          rowId={ COMPONENT_MAP[ 'numberOfDelinquentPayments' ][ 'rowId' ] }
          fieldId={ COMPONENT_MAP[ 'numberOfDelinquentPayments' ][ 'id' ] }
          fieldNotes={ getFieldNotes(notes, COMPONENT_MAP[ 'numberOfDelinquentPayments' ][ 'id' ]) }
          fieldDocs={ getFieldDocs(attachments, COMPONENT_MAP[ 'numberOfDelinquentPayments' ][ 'id' ]) }
          selectedFieldNote={ selectedFieldNote }
          handleItemNoteClick={ handleItemNoteClick }
          handleMouseEnter={ handleMouseEnter }
          handleMouseLeave={ handleMouseLeave }
          handleAttachmentClick={ handleAttachmentClick }
          deleteAttachment={ deleteAttachment }
          rowType={ CONSTANTS.FIELD_TYPE.static }
          rowStyle={ rowStyle }/>
        <InputBoxRow labelContainerClass="col-3"
          inputBoxContainerClass="col-2"
          hasLabel={ true }
          hasCheckbox={ false }
          hasTextValue={ true }
          isFinanceUser={ isFinanceUser }
          showManagerCheckbox={ false }
          isDisabled={ isDisabled }
          label="Calculated Delinquent Payments"
          textValue={ this.getDelinquentPayments() }
          claimObject={ claimObject }
          onCursorToggle={ onCursorToggle }
          rowId={ COMPONENT_MAP[ 'calculatedDelinquentPayments' ][ 'rowId' ] }
          fieldId={ COMPONENT_MAP[ 'calculatedDelinquentPayments' ][ 'id' ] }
          fieldNotes={ getFieldNotes(notes, COMPONENT_MAP[ 'calculatedDelinquentPayments' ][ 'id' ]) }
          fieldDocs={ getFieldDocs(attachments, COMPONENT_MAP[ 'calculatedDelinquentPayments' ][ 'id' ]) }
          selectedFieldNote={ selectedFieldNote }
          handleItemNoteClick={ handleItemNoteClick }
          handleMouseEnter={ handleMouseEnter }
          handleMouseLeave={ handleMouseLeave }
          handleAttachmentClick={ handleAttachmentClick }
          deleteAttachment={ deleteAttachment }
          rowType={ rowType }
          rowStyle={ rowStyle }/>
        <InputBoxRow labelContainerClass="col-3"
          isFinanceUser={ isFinanceUser }
          inputBoxContainerClass="col-2"
          hasLabel={ true }
          hasCheckbox={ true }
          hasInputBox={ true }
          checkBoxAttribute="has_total_delinquent_payments_covered"
          label="Total Delinquent Payments Covered"
          inputBoxType="Currency"
          inputBoxAttribute="total_delinquent_payments_covered"
          managerCheckBoxAttribute="total_delinquent_payments_covered_manager_flag"
          showManagerCheckbox={ showManagerCheckbox }
          isDisabled={ isDisabled }
          hasDefaultValue={ true }
          claimObject={ claimObject }
          onCursorToggle={ onCursorToggle }
          onInputBoxChange={ onCursorValueChange }
          onInputBoxBlur={ onCursorValueChange }
          rowId={ COMPONENT_MAP[ 'totalDelinquentPaymentsCovered' ][ 'rowId' ] }
          fieldId={ COMPONENT_MAP[ 'totalDelinquentPaymentsCovered' ][ 'id' ] }
          fieldNotes={ getFieldNotes(notes, COMPONENT_MAP[ 'totalDelinquentPaymentsCovered' ][ 'id' ]) }
          fieldDocs={ getFieldDocs(attachments, COMPONENT_MAP[ 'totalDelinquentPaymentsCovered' ][ 'id' ]) }
          selectedFieldNote={ selectedFieldNote }
          handleItemNoteClick={ handleItemNoteClick }
          handleMouseEnter={ handleMouseEnter }
          handleMouseLeave={ handleMouseLeave }
          handleAttachmentClick={ handleAttachmentClick }
          deleteAttachment={ deleteAttachment }
          rowType={ CONSTANTS.FIELD_TYPE.static }
          rowStyle={ rowStyle }/>
        <InputBoxRow labelContainerClass="col-3"
          isFinanceUser={ isFinanceUser }
          inputBoxContainerClass="col-2"
          hasLabel={ true }
          hasCheckbox={ true }
          hasInputBox={ true }
          checkBoxAttribute="has_number_of_extensions"
          label=" Number of Extensions"
          inputBoxType="Number"
          inputBoxAttribute="number_of_extensions"
          managerCheckBoxAttribute="number_of_extensions_manager_flag"
          showManagerCheckbox={ showManagerCheckbox }
          isDisabled={ isDisabled }
          hasDefaultValue={ true }
          claimObject={ claimObject }
          onCursorToggle={ onCursorToggle }
          onInputBoxChange={ onCursorValueChange }
          onInputBoxBlur={ onCursorValueChange }
          rowId={ COMPONENT_MAP[ 'numberOfExtensions' ][ 'rowId' ] }
          fieldId={ COMPONENT_MAP[ 'numberOfExtensions' ][ 'id' ] }
          fieldNotes={ getFieldNotes(notes, COMPONENT_MAP[ 'numberOfExtensions' ][ 'id' ]) }
          fieldDocs={ getFieldDocs(attachments, COMPONENT_MAP[ 'numberOfExtensions' ][ 'id' ]) }
          selectedFieldNote={ selectedFieldNote }
          handleItemNoteClick={ handleItemNoteClick }
          handleMouseEnter={ handleMouseEnter }
          handleMouseLeave={ handleMouseLeave }
          handleAttachmentClick={ handleAttachmentClick }
          deleteAttachment={ deleteAttachment }
          rowType={ CONSTANTS.FIELD_TYPE.static }
          rowStyle={ rowStyle }/>
        <InputBoxRow labelContainerClass="col-3"
          inputBoxContainerClass="col-2"
          hasLabel={ true }
          hasCheckbox={ false }
          hasTextValue={ true }
          isFinanceUser={ isFinanceUser }
          showManagerCheckbox={ false }
          isDisabled={ isDisabled }
          label="Calculated Extensions"
          textValue={ this.getExtensions(claimObject) }
          claimObject={ claimObject }
          onCursorToggle={ onCursorToggle }
          rowId={ COMPONENT_MAP[ 'calculatedExtensions' ][ 'rowId' ] }
          fieldId={ COMPONENT_MAP[ 'calculatedExtensions' ][ 'id' ] }
          fieldNotes={ getFieldNotes(notes, COMPONENT_MAP[ 'calculatedExtensions' ][ 'id' ]) }
          fieldDocs={ getFieldDocs(attachments, COMPONENT_MAP[ 'calculatedExtensions' ][ 'id' ]) }
          selectedFieldNote={ selectedFieldNote }
          handleItemNoteClick={ handleItemNoteClick }
          handleMouseEnter={ handleMouseEnter }
          handleMouseLeave={ handleMouseLeave }
          handleAttachmentClick={ handleAttachmentClick }
          deleteAttachment={ deleteAttachment }
          rowType={ rowType }
          rowStyle={ rowStyle }/>
        <InputBoxRow labelContainerClass="col-3"
          isFinanceUser={ isFinanceUser }
          inputBoxContainerClass="col-2"
          hasLabel={ true }
          hasCheckbox={ true }
          hasInputBox={ true }
          checkBoxAttribute="has_total_extensions_covered"
          label="Total Extensions Covered"
          inputBoxType="Currency"
          inputBoxAttribute="total_extensions_covered"
          managerCheckBoxAttribute="total_extensions_covered_manager_flag"
          showManagerCheckbox={ showManagerCheckbox }
          isDisabled={ isDisabled }
          hasDefaultValue={ true }
          claimObject={ claimObject }
          onCursorToggle={ onCursorToggle }
          onInputBoxChange={ onCursorValueChange }
          onInputBoxBlur={ onCursorValueChange }
          rowId={ COMPONENT_MAP[ 'totalExtensionsCovered' ][ 'rowId' ] }
          fieldId={ COMPONENT_MAP[ 'totalExtensionsCovered' ][ 'id' ] }
          fieldNotes={ getFieldNotes(notes, COMPONENT_MAP[ 'totalExtensionsCovered' ][ 'id' ]) }
          fieldDocs={ getFieldDocs(attachments, COMPONENT_MAP[ 'totalExtensionsCovered' ][ 'id' ]) }
          selectedFieldNote={ selectedFieldNote }
          handleItemNoteClick={ handleItemNoteClick }
          handleMouseEnter={ handleMouseEnter }
          handleMouseLeave={ handleMouseLeave }
          handleAttachmentClick={ handleAttachmentClick }
          deleteAttachment={ deleteAttachment }
          rowType={ CONSTANTS.FIELD_TYPE.static }
          rowStyle={ rowStyle }/>
        <InputBoxRow labelContainerClass="col-3"
          isFinanceUser={ isFinanceUser }
          inputBoxContainerClass="col-2"
          hasLabel={ true }
          hasCheckbox={ true }
          hasInputBox={ true }
          checkBoxAttribute="has_negative_equity_amount"
          label="Negative equity amount:"
          inputBoxType="Currency"
          inputBoxAttribute="negative_equity_amount"
          managerCheckBoxAttribute="has_negative_equity_amount_manager_flag"
          showManagerCheckbox={ showManagerCheckbox }
          isDisabled={ isDisabled }
          hasDefaultValue={ true }
          claimObject={ claimObject }
          onCursorToggle={ onCursorToggle }
          onInputBoxChange={ onCursorValueChange }
          onInputBoxBlur={ onCursorValueChange }
          rowId={ COMPONENT_MAP[ 'negativeEquityAmount' ][ 'rowId' ] }
          fieldId={ COMPONENT_MAP[ 'negativeEquityAmount' ][ 'id' ] }
          fieldNotes={ getFieldNotes(notes, COMPONENT_MAP[ 'negativeEquityAmount' ][ 'id' ]) }
          fieldDocs={ getFieldDocs(attachments, COMPONENT_MAP[ 'negativeEquityAmount' ][ 'id' ]) }
          selectedFieldNote={ selectedFieldNote }
          handleItemNoteClick={ handleItemNoteClick }
          handleMouseEnter={ handleMouseEnter }
          handleMouseLeave={ handleMouseLeave }
          handleAttachmentClick={ handleAttachmentClick }
          deleteAttachment={ deleteAttachment }
          rowType={ CONSTANTS.FIELD_TYPE.static }
          rowStyle={ rowStyle }/>
        <InputBoxRow labelContainerClass="col-3"
          isFinanceUser={ isFinanceUser }
          inputBoxContainerClass="col-2"
          hasLabel={ true }
          hasCheckbox={ true }
          hasInputBox={ true }
          checkBoxAttribute="has_settlement_amount"
          label="Settlement amount:"
          inputBoxType="Currency"
          inputBoxAttribute="settlement_amount"
          managerCheckBoxAttribute="settlement_letter_manager_flag"
          showManagerCheckbox={ showManagerCheckbox }
          isDisabled={ isDisabled }
          hasDefaultValue={ true }
          claimObject={ claimObject }
          onCursorToggle={ onCursorToggle }
          onInputBoxChange={ onCursorValueChange }
          onInputBoxBlur={ onCursorValueChange }
          rowId={ COMPONENT_MAP[ 'settlementAmount' ][ 'rowId' ] }
          fieldId={ COMPONENT_MAP[ 'settlementAmount' ][ 'id' ] }
          fieldNotes={ getFieldNotes(notes, COMPONENT_MAP[ 'settlementAmount' ][ 'id' ]) }
          fieldDocs={ getFieldDocs(attachments, COMPONENT_MAP[ 'settlementAmount' ][ 'id' ]) }
          selectedFieldNote={ selectedFieldNote }
          handleItemNoteClick={ handleItemNoteClick }
          handleMouseEnter={ handleMouseEnter }
          handleMouseLeave={ handleMouseLeave }
          handleAttachmentClick={ handleAttachmentClick }
          deleteAttachment={ deleteAttachment }
          rowType={ CONSTANTS.FIELD_TYPE.static }
          rowStyle={ rowStyle }/>
        <InputBoxRow labelContainerClass="col-3"
          isFinanceUser={ isFinanceUser }
          inputBoxContainerClass="col-2"
          hasLabel={ true }
          hasCheckbox={ true }
          hasInputBox={ true }
          checkBoxAttribute="has_insurance_check_amount"
          label="Insurance check amount:"
          inputBoxType="Currency"
          inputBoxAttribute="insurance_check_amount"
          managerCheckBoxAttribute="insurance_payment_check_manager_flag"
          showManagerCheckbox={ showManagerCheckbox }
          isDisabled={ isDisabled }
          hasDefaultValue={ true }
          claimObject={ claimObject }
          onCursorToggle={ onCursorToggle }
          onInputBoxChange={ onCursorValueChange }
          onInputBoxBlur={ onCursorValueChange }
          rowId={ COMPONENT_MAP[ 'insuranceCheckAmount' ][ 'rowId' ] }
          fieldId={ COMPONENT_MAP[ 'insuranceCheckAmount' ][ 'id' ] }
          fieldNotes={ getFieldNotes(notes, COMPONENT_MAP[ 'insuranceCheckAmount' ][ 'id' ]) }
          fieldDocs={ getFieldDocs(attachments, COMPONENT_MAP[ 'insuranceCheckAmount' ][ 'id' ]) }
          selectedFieldNote={ selectedFieldNote }
          handleItemNoteClick={ handleItemNoteClick }
          handleMouseEnter={ handleMouseEnter }
          handleMouseLeave={ handleMouseLeave }
          handleAttachmentClick={ handleAttachmentClick }
          deleteAttachment={ deleteAttachment }
          rowType={ CONSTANTS.FIELD_TYPE.static }
          rowStyle={ rowStyle }/>
        <InputBoxRow labelContainerClass="col-3"
          inputBoxContainerClass="col-2"
          hasLabel={ true }
          hasCheckbox={ true }
          hasInputBox={ true }
          isFinanceUser={ isFinanceUser }
          checkBoxAttribute="is_valuation_report_available"
          label="Valuation report:"
          inputBoxType="Currency"
          inputBoxAttribute="valuation_report_adjustments"
          managerCheckBoxAttribute="valuation_report_manager_flag"
          showManagerCheckbox={ showManagerCheckbox }
          isDisabled={ isDisabled }
          hasDefaultValue={ true }
          claimObject={ claimObject }
          onCursorToggle={ onCursorToggle }
          onInputBoxChange={ onCursorValueChange }
          onInputBoxBlur={ onCursorValueChange }
          rowId={ COMPONENT_MAP[ 'valuationReport' ][ 'rowId' ] }
          fieldId={ COMPONENT_MAP[ 'valuationReport' ][ 'id' ] }
          fieldNotes={ getFieldNotes(notes, COMPONENT_MAP[ 'valuationReport' ][ 'id' ]) }
          fieldDocs={ getFieldDocs(attachments, COMPONENT_MAP[ 'valuationReport' ][ 'id' ]) }
          selectedFieldNote={ selectedFieldNote }
          handleItemNoteClick={ handleItemNoteClick }
          handleMouseEnter={ handleMouseEnter }
          handleMouseLeave={ handleMouseLeave }
          handleAttachmentClick={ handleAttachmentClick }
          deleteAttachment={ deleteAttachment }
          rowType={ rowType }
          rowStyle={ rowStyle }/>
        <InputBoxRow labelContainerClass="col-3"
          inputBoxContainerClass="col-2"
          hasLabel={ true }
          hasCheckbox={ true }
          hasInputBox={ true }
          isFinanceUser={ isFinanceUser }
          checkBoxAttribute="has_nada"
          label="NADA Value:"
          inputBoxType="Currency"
          inputBoxAttribute="nada"
          managerCheckBoxAttribute="nada_manager_flag"
          showManagerCheckbox={ showManagerCheckbox }
          isDisabled={ isDisabled }
          hasDefaultValue={ true }
          claimObject={ claimObject }
          onCursorToggle={ onCursorToggle }
          onInputBoxChange={ onCursorValueChange }
          onInputBoxBlur={ onCursorValueChange }
          rowId={ COMPONENT_MAP[ 'nada' ][ 'rowId' ] }
          fieldId={ COMPONENT_MAP[ 'nada' ][ 'id' ] }
          fieldNotes={ getFieldNotes(notes, COMPONENT_MAP[ 'nada' ][ 'id' ]) }
          fieldDocs={ getFieldDocs(attachments, COMPONENT_MAP[ 'nada' ][ 'id' ]) }
          selectedFieldNote={ selectedFieldNote }
          handleItemNoteClick={ handleItemNoteClick }
          handleMouseEnter={ handleMouseEnter }
          handleMouseLeave={ handleMouseLeave }
          handleAttachmentClick={ handleAttachmentClick }
          deleteAttachment={ deleteAttachment }
          rowType={ rowType }
          rowStyle={ rowStyle }/>
        <InputBoxRow labelContainerClass="col-2"
          inputBoxContainerClass="col-2"
          containerClass="justify-content-end"
          hasLabel={ true }
          hasCheckbox={ true }
          hasTextValue={ true }
          isFinanceUser={ isFinanceUser }
          checkBoxAttribute="has_valuation_nada_difference"
          showManagerCheckbox={ false }
          isDisabled={ isDisabled }
          label="Difference:"
          textValue={ this.getDifference() }
          claimObject={ claimObject }
          onCursorToggle={ onCursorToggle }
          rowId={ COMPONENT_MAP[ 'difference' ][ 'rowId' ] }
          fieldId={ COMPONENT_MAP[ 'difference' ][ 'id' ] }
          fieldNotes={ getFieldNotes(notes, COMPONENT_MAP[ 'difference' ][ 'id' ]) }
          fieldDocs={ getFieldDocs(attachments, COMPONENT_MAP[ 'difference' ][ 'id' ]) }
          selectedFieldNote={ selectedFieldNote }
          handleItemNoteClick={ handleItemNoteClick }
          handleMouseEnter={ handleMouseEnter }
          handleMouseLeave={ handleMouseLeave }
          handleAttachmentClick={ handleAttachmentClick }
          deleteAttachment={ deleteAttachment }
          rowType={ rowType }
          rowStyle={ rowStyle }/>
        <InputBoxRow labelContainerClass="col-2"
          inputBoxContainerClass="col-2"
          containerClass="justify-content-end"
          isFinanceUser={ isFinanceUser }
          hasLabel={ true }
          hasCheckbox={ true }
          hasInputBox={ true }
          checkBoxAttribute="is_valuation_report_matches_base_value"
          label="Matches base value:"
          inputBoxType="Currency"
          inputBoxAttribute="valuation_report_matches_base_value"
          managerCheckBoxAttribute="valuation_report_base_value_manager_flag"
          showManagerCheckbox={ showManagerCheckbox }
          isDisabled={ isDisabled }
          hasDefaultValue={ true }
          claimObject={ claimObject }
          onCursorToggle={ onCursorToggle }
          onInputBoxChange={ onCursorValueChange }
          onInputBoxBlur={ onCursorValueChange }
          rowId={ COMPONENT_MAP[ 'matchesBaseValue' ][ 'rowId' ] }
          fieldId={ COMPONENT_MAP[ 'matchesBaseValue' ][ 'id' ] }
          fieldNotes={ getFieldNotes(notes, COMPONENT_MAP[ 'matchesBaseValue' ][ 'id' ]) }
          fieldDocs={ getFieldDocs(attachments, COMPONENT_MAP[ 'matchesBaseValue' ][ 'id' ]) }
          selectedFieldNote={ selectedFieldNote }
          handleItemNoteClick={ handleItemNoteClick }
          handleMouseEnter={ handleMouseEnter }
          handleMouseLeave={ handleMouseLeave }
          handleAttachmentClick={ handleAttachmentClick }
          deleteAttachment={ deleteAttachment }
          rowType={ rowType }
          rowStyle={ rowStyle }/>
        <InputBoxRow labelContainerClass="col-2"
          inputBoxContainerClass="col-2"
          containerClass="justify-content-end"
          hasLabel={ true }
          hasCheckbox={ true }
          hasTextValue={ true }
          isFinanceUser={ isFinanceUser }
          checkBoxAttribute="valuation_report_vin_matches"
          managerCheckBoxAttribute="valuation_report_vin_matches_manager_flag"
          showManagerCheckbox={ showManagerCheckbox }
          isDisabled={ isDisabled }
          label="VIN matches:"
          inputBoxAttribute="vin"
          textValue={ claimObject.vin }
          claimObject={ claimObject }
          onCursorToggle={ onCursorToggle }
          rowId={ COMPONENT_MAP[ 'vinMatches' ][ 'rowId' ] }
          fieldId={ COMPONENT_MAP[ 'vinMatches' ][ 'id' ] }
          fieldNotes={ getFieldNotes(notes, COMPONENT_MAP[ 'vinMatches' ][ 'id' ]) }
          fieldDocs={ getFieldDocs(attachments, COMPONENT_MAP[ 'vinMatches' ][ 'id' ]) }
          selectedFieldNote={ selectedFieldNote }
          handleItemNoteClick={ handleItemNoteClick }
          handleMouseEnter={ handleMouseEnter }
          handleMouseLeave={ handleMouseLeave }
          handleAttachmentClick={ handleAttachmentClick }
          deleteAttachment={ deleteAttachment }
          rowType={ rowType }
          rowStyle={ rowStyle }/>
        <InputBoxRow labelContainerClass="col-2"
          inputBoxContainerClass="col-2"
          containerClass="justify-content-end"
          hasLabel={ true }
          hasCheckbox={ true }
          hasInputBox={ true }
          isFinanceUser={ isFinanceUser }
          checkBoxAttribute="has_valuation_report_prior_damage"
          label="Prior damage:"
          inputBoxType="Currency"
          inputBoxAttribute="valuation_report_prior_damage_value"
          managerCheckBoxAttribute="valuation_report_prior_damage_manager_flag"
          showManagerCheckbox={ showManagerCheckbox }
          isDisabled={ isDisabled }
          hasDefaultValue={ true }
          claimObject={ claimObject }
          onCursorToggle={ onCursorToggle }
          onInputBoxChange={ onCursorValueChange }
          onInputBoxBlur={ onCursorValueChange }
          rowId={ COMPONENT_MAP[ 'priorDamage' ][ 'rowId' ] }
          fieldId={ COMPONENT_MAP[ 'priorDamage' ][ 'id' ] }
          fieldNotes={ getFieldNotes(notes, COMPONENT_MAP[ 'priorDamage' ][ 'id' ]) }
          fieldDocs={ getFieldDocs(attachments, COMPONENT_MAP[ 'priorDamage' ][ 'id' ]) }
          selectedFieldNote={ selectedFieldNote }
          handleItemNoteClick={ handleItemNoteClick }
          handleMouseEnter={ handleMouseEnter }
          handleMouseLeave={ handleMouseLeave }
          handleAttachmentClick={ handleAttachmentClick }
          deleteAttachment={ deleteAttachment }
          rowType={ rowType }
          rowStyle={ rowStyle }/>
        <InputBoxRow labelContainerClass="col-2"
          inputBoxContainerClass="col-2"
          containerClass="justify-content-end"
          hasLabel={ true }
          hasCheckbox={ true }
          hasInputBox={ true }
          isFinanceUser={ isFinanceUser }
          checkBoxAttribute="has_valuation_report_misc_fee"
          label="Misc fee:"
          inputBoxType="Currency"
          inputBoxAttribute="valuation_report_misc_fee_value"
          managerCheckBoxAttribute="valuation_report_misc_fee_manager_flag"
          showManagerCheckbox={ showManagerCheckbox }
          isDisabled={ isDisabled }
          hasDefaultValue={ true }
          claimObject={ claimObject }
          onCursorToggle={ onCursorToggle }
          onInputBoxChange={ onCursorValueChange }
          onInputBoxBlur={ onCursorValueChange }
          rowId={ COMPONENT_MAP[ 'miscFee' ][ 'rowId' ] }
          fieldId={ COMPONENT_MAP[ 'miscFee' ][ 'id' ] }
          fieldNotes={ getFieldNotes(notes, COMPONENT_MAP[ 'miscFee' ][ 'id' ]) }
          fieldDocs={ getFieldDocs(attachments, COMPONENT_MAP[ 'miscFee' ][ 'id' ]) }
          selectedFieldNote={ selectedFieldNote }
          handleItemNoteClick={ handleItemNoteClick }
          handleMouseEnter={ handleMouseEnter }
          handleMouseLeave={ handleMouseLeave }
          handleAttachmentClick={ handleAttachmentClick }
          deleteAttachment={ deleteAttachment }
          rowType={ rowType }
          rowStyle={ rowStyle }/>
        <InputBoxRow labelContainerClass="col-2"
          inputBoxContainerClass="col-2"
          containerClass="justify-content-end"
          hasLabel={ true }
          hasCheckbox={ true }
          hasInputBox={ true }
          isFinanceUser={ isFinanceUser }
          checkBoxAttribute="has_mileage_deduction"
          label="Mileage Deduction:"
          inputBoxType="Currency"
          inputBoxAttribute="mileage_deduction"
          showManagerCheckbox={ showManagerCheckbox }
          managerCheckBoxAttribute="mileage_deduction_manager_flag"
          isDisabled={ isDisabled }
          hasDefaultValue={ true }
          claimObject={ claimObject }
          onCursorToggle={ onCursorToggle }
          onInputBoxChange={ onCursorValueChange }
          onInputBoxBlur={ onCursorValueChange }
          rowId={ COMPONENT_MAP[ 'mileageDeduction' ][ 'rowId' ] }
          fieldId={ COMPONENT_MAP[ 'mileageDeduction' ][ 'id' ] }
          fieldNotes={ getFieldNotes(notes, COMPONENT_MAP[ 'mileageDeduction' ][ 'id' ]) }
          fieldDocs={ getFieldDocs(attachments, COMPONENT_MAP[ 'mileageDeduction' ][ 'id' ]) }
          selectedFieldNote={ selectedFieldNote }
          handleItemNoteClick={ handleItemNoteClick }
          handleMouseEnter={ handleMouseEnter }
          handleMouseLeave={ handleMouseLeave }
          handleAttachmentClick={ handleAttachmentClick }
          deleteAttachment={ deleteAttachment }
          rowType={ rowType }
          rowStyle={ rowStyle }/>
        <InputBoxRow labelContainerClass="col-2"
          inputBoxContainerClass="col-2"
          containerClass="justify-content-end"
          hasLabel={ true }
          hasInputBox={ true }
          isFinanceUser={ isFinanceUser }
          label="Mileage:"
          inputBoxType="Number"
          inputBoxAttribute="valuation_report_mileage"
          managerCheckBoxAttribute="valuation_report_mileage_manager_flag"
          showManagerCheckbox={ showManagerCheckbox }
          isDisabled={ isDisabled }
          hasDefaultValue={ true }
          claimObject={ claimObject }
          onCursorToggle={ onCursorToggle }
          onInputBoxChange={ onCursorValueChange }
          onInputBoxBlur={ onCursorValueChange }
          rowId={ COMPONENT_MAP[ 'mileage' ][ 'rowId' ] }
          fieldId={ COMPONENT_MAP[ 'mileage' ][ 'id' ] }
          fieldNotes={ getFieldNotes(notes, COMPONENT_MAP[ 'mileage' ][ 'id' ]) }
          fieldDocs={ getFieldDocs(attachments, COMPONENT_MAP[ 'mileage' ][ 'id' ]) }
          selectedFieldNote={ selectedFieldNote }
          handleItemNoteClick={ handleItemNoteClick }
          handleMouseEnter={ handleMouseEnter }
          handleMouseLeave={ handleMouseLeave }
          handleAttachmentClick={ handleAttachmentClick }
          deleteAttachment={ deleteAttachment }
          rowType={ rowType }
          rowStyle={ rowStyle }/>
        <DateRow containerClass="justify-content-end"
          labelContainerClass="col-2"
          datePickerContainerClass="col-2"
          hasCheckbox={ false }
          isFinanceUser={ isFinanceUser }
          label="DOL:"
          dateAttribute="date_of_loss"
          onCursorToggle={ onCursorToggle }
          managerCheckBoxAttribute="valuation_report_dol_manager_flag"
          showManagerCheckbox={ showManagerCheckbox }
          isDisabled={ isDisabled }
          claimObject={ claimObject }
          handleDateChange={ handleDateChange }
          dateFormat={ dateFormat }
          rowId={ COMPONENT_MAP[ 'dol' ][ 'rowId' ] }
          fieldId={ COMPONENT_MAP[ 'dol' ][ 'id' ] }
          fieldNotes={ getFieldNotes(notes, COMPONENT_MAP[ 'dol' ][ 'id' ]) }
          fieldDocs={ getFieldDocs(attachments, COMPONENT_MAP[ 'dol' ][ 'id' ]) }
          selectedFieldNote={ selectedFieldNote }
          handleItemNoteClick={ handleItemNoteClick }
          handleMouseEnter={ handleMouseEnter }
          handleMouseLeave={ handleMouseLeave }
          handleAttachmentClick={ handleAttachmentClick }
          deleteAttachment={ deleteAttachment }
          rowType={ rowType }
          rowStyle={ rowStyle }/>
        <InputBoxRow labelContainerClass="col-2"
          inputBoxContainerClass="col-2"
          containerClass="justify-content-end"
          isFinanceUser={ isFinanceUser }
          isDisabled={ isDisabled }
          onCursorToggle={ onCursorToggle }
          claimObject={ claimObject }
          managerCheckBoxAttribute="valuation_report_type_manager_flag"
          showManagerCheckbox={ showManagerCheckbox }
          hasLabel={ true }
          hasNode={ true }
          label="Type:"
          renderNode={ this.renderTypeDropdown.bind(this, COMPONENT_MAP) }
          rowId={ COMPONENT_MAP[ 'type' ][ 'rowId' ] }
          fieldId={ COMPONENT_MAP[ 'type' ][ 'id' ] }
          fieldNotes={ getFieldNotes(notes, COMPONENT_MAP[ 'type' ][ 'id' ]) }
          fieldDocs={ getFieldDocs(attachments, COMPONENT_MAP[ 'type' ][ 'id' ]) }
          selectedFieldNote={ selectedFieldNote }
          handleItemNoteClick={ handleItemNoteClick }
          handleMouseEnter={ handleMouseEnter }
          handleMouseLeave={ handleMouseLeave }
          handleAttachmentClick={ handleAttachmentClick }
          deleteAttachment={ deleteAttachment }
          rowType={ rowType }
          rowStyle={ rowStyle }/>
        <InputBoxRow labelContainerClass="col-5"
          isFinanceUser={ isFinanceUser }
          hasLabel={ true }
          hasCheckbox={ true }
          checkBoxAttribute="has_estimate_with_photos"
          label="Estimate with photos:"
          showManagerCheckbox={ false }
          isDisabled={ isDisabled }
          claimObject={ claimObject }
          onCursorToggle={ onCursorToggle }
          rowId={ COMPONENT_MAP[ 'estimateWithPhotos' ][ 'rowId' ] }
          fieldId={ COMPONENT_MAP[ 'estimateWithPhotos' ][ 'id' ] }
          fieldNotes={ getFieldNotes(notes, COMPONENT_MAP[ 'estimateWithPhotos' ][ 'id' ]) }
          fieldDocs={ getFieldDocs(attachments, COMPONENT_MAP[ 'estimateWithPhotos' ][ 'id' ]) }
          selectedFieldNote={ selectedFieldNote }
          handleItemNoteClick={ handleItemNoteClick }
          handleMouseEnter={ handleMouseEnter }
          handleMouseLeave={ handleMouseLeave }
          handleAttachmentClick={ handleAttachmentClick }
          deleteAttachment={ deleteAttachment }
          rowType={ CONSTANTS.FIELD_TYPE.static }
          rowStyle={ rowStyle }/>
        <InputBoxRow labelContainerClass="col-3"
          isFinanceUser={ isFinanceUser }
          inputBoxContainerClass="col-2"
          hasLabel={ true }
          hasCheckbox={ true }
          hasInputBox={ true }
          checkBoxAttribute="has_msrp_value"
          label="MSRP:"
          inputBoxType="Currency"
          inputBoxAttribute="msrp_value"
          managerCheckBoxAttribute="msrp_value_manager_flag"
          showManagerCheckbox={ showManagerCheckbox }
          isDisabled={ isDisabled }
          hasDefaultValue={ true }
          claimObject={ claimObject }
          onCursorToggle={ onCursorToggle }
          onInputBoxChange={ onCursorValueChange }
          onInputBoxBlur={ onCursorValueChange }
          rowId={ COMPONENT_MAP[ 'msrp' ][ 'rowId' ] }
          fieldId={ COMPONENT_MAP[ 'msrp' ][ 'id' ] }
          fieldNotes={ getFieldNotes(notes, COMPONENT_MAP[ 'msrp' ][ 'id' ]) }
          fieldDocs={ getFieldDocs(attachments, COMPONENT_MAP[ 'msrp' ][ 'id' ]) }
          selectedFieldNote={ selectedFieldNote }
          handleItemNoteClick={ handleItemNoteClick }
          handleMouseEnter={ handleMouseEnter }
          handleMouseLeave={ handleMouseLeave }
          handleAttachmentClick={ handleAttachmentClick }
          deleteAttachment={ deleteAttachment }
          rowType={ CONSTANTS.FIELD_TYPE.static }
          rowStyle={ rowStyle }/>
        <InputBoxRow labelContainerClass="col-3"
          isFinanceUser={ isFinanceUser }
          inputBoxContainerClass="col-2"
          hasLabel={ true }
          hasCheckbox={ true }
          hasInputBox={ true }
          checkBoxAttribute="has_original_financing_contract"
          label="Original financing contract:"
          inputBoxType="Currency"
          inputBoxAttribute="original_financing_contract_value"
          managerCheckBoxAttribute="original_financing_manager_flag"
          showManagerCheckbox={ showManagerCheckbox }
          isDisabled={ isDisabled }
          hasDefaultValue={ true }
          claimObject={ claimObject }
          onCursorToggle={ onCursorToggle }
          onInputBoxChange={ onCursorValueChange }
          onInputBoxBlur={ onCursorValueChange }
          rowId={ COMPONENT_MAP[ 'originalFinancingContract' ][ 'rowId' ] }
          fieldId={ COMPONENT_MAP[ 'originalFinancingContract' ][ 'id' ] }
          fieldNotes={ getFieldNotes(notes, COMPONENT_MAP[ 'originalFinancingContract' ][ 'id' ]) }
          fieldDocs={ getFieldDocs(attachments, COMPONENT_MAP[ 'originalFinancingContract' ][ 'id' ]) }
          selectedFieldNote={ selectedFieldNote }
          handleItemNoteClick={ handleItemNoteClick }
          handleMouseEnter={ handleMouseEnter }
          handleMouseLeave={ handleMouseLeave }
          handleAttachmentClick={ handleAttachmentClick }
          deleteAttachment={ deleteAttachment }
          rowType={ CONSTANTS.FIELD_TYPE.static }
          rowStyle={ rowStyle }/>
        <If
          condition={ claimObject.over_150_percent && parseFloat(claimObject.over_150_percent) < 0 }>
          <InputBoxRow labelContainerClass="col-2"
            inputBoxContainerClass="col-2"
            isFinanceUser={ isFinanceUser }
            containerClass="justify-content-end"
            hasLabel={ true }
            hasTextValue={ true }
            label="Over 150%:"
            inputBoxAttribute="over_150_percent"
            managerCheckBoxAttribute="options_book_out_over_150_percent_manager_flag"
            onCursorToggle={ onCursorToggle }
            showManagerCheckbox={ showManagerCheckbox }
            textValue={ accounting.formatMoney(claimObject.over_150_percent, '$', 2) }
            claimObject={ claimObject }
            rowId={ COMPONENT_MAP[ 'optionsMatchBookOutOver150Percentage' ][ 'rowId' ] }
            fieldId={ COMPONENT_MAP[ 'optionsMatchBookOutOver150Percentage' ][ 'id' ] }
            fieldNotes={ getFieldNotes(notes, COMPONENT_MAP[ 'optionsMatchBookOutOver150Percentage' ][ 'id' ]) }
            fieldDocs={ getFieldDocs(attachments, COMPONENT_MAP[ 'optionsMatchBookOutOver150Percentage' ][ 'id' ]) }
            selectedFieldNote={ selectedFieldNote }
            handleItemNoteClick={ handleItemNoteClick }
            handleMouseEnter={ handleMouseEnter }
            handleMouseLeave={ handleMouseLeave }
            handleAttachmentClick={ handleAttachmentClick }
            deleteAttachment={ deleteAttachment }
            rowType={ CONSTANTS.FIELD_TYPE.static }
            rowStyle={ rowStyle }/>
        </If>
        <InputBoxRow labelContainerClass="col-4"
          containerClass="justify-content-end"
          isFinanceUser={ isFinanceUser }
          hasLabel={ true }
          hasCheckbox={ true }
          hasInputBox={ false }
          checkBoxAttribute="contract_number_matches"
          managerCheckBoxAttribute="contract_number_matches_manager_flag"
          showManagerCheckbox={ showManagerCheckbox }
          isDisabled={ isDisabled }
          label="Contract# matches"
          claimObject={ claimObject }
          onCursorToggle={ onCursorToggle }
          rowId={ COMPONENT_MAP[ 'contractNumberMatches' ][ 'rowId' ] }
          fieldId={ COMPONENT_MAP[ 'contractNumberMatches' ][ 'id' ] }
          fieldNotes={ getFieldNotes(notes, COMPONENT_MAP[ 'contractNumberMatches' ][ 'id' ]) }
          fieldDocs={ getFieldDocs(attachments, COMPONENT_MAP[ 'contractNumberMatches' ][ 'id' ]) }
          selectedFieldNote={ selectedFieldNote }
          handleItemNoteClick={ handleItemNoteClick }
          handleMouseEnter={ handleMouseEnter }
          handleMouseLeave={ handleMouseLeave }
          handleAttachmentClick={ handleAttachmentClick }
          deleteAttachment={ deleteAttachment }
          rowType={ CONSTANTS.FIELD_TYPE.static }
          rowStyle={ rowStyle }/>
        <InputBoxRow labelContainerClass="col-4"
          containerClass="justify-content-end"
          isFinanceUser={ isFinanceUser }
          hasLabel={ true }
          hasCheckbox={ true }
          hasInputBox={ false }
          checkBoxAttribute="bank_history_matches"
          managerCheckBoxAttribute="bank_history_matches_manager_flag"
          showManagerCheckbox={ showManagerCheckbox }
          isDisabled={ isDisabled }
          label="Bank history matches"
          claimObject={ claimObject }
          onCursorToggle={ onCursorToggle }
          rowId={ COMPONENT_MAP[ 'bankHistoryMatches' ][ 'rowId' ] }
          fieldId={ COMPONENT_MAP[ 'bankHistoryMatches' ][ 'id' ] }
          fieldNotes={ getFieldNotes(notes, COMPONENT_MAP[ 'bankHistoryMatches' ][ 'id' ]) }
          fieldDocs={ getFieldDocs(attachments, COMPONENT_MAP[ 'bankHistoryMatches' ][ 'id' ]) }
          selectedFieldNote={ selectedFieldNote }
          handleItemNoteClick={ handleItemNoteClick }
          handleMouseEnter={ handleMouseEnter }
          handleMouseLeave={ handleMouseLeave }
          handleAttachmentClick={ handleAttachmentClick }
          deleteAttachment={ deleteAttachment }
          rowType={ CONSTANTS.FIELD_TYPE.static }
          rowStyle={ rowStyle }/>
        <InputBoxRow labelContainerClass="col-5"
          isFinanceUser={ isFinanceUser }
          hasLabel={ true }
          hasCheckbox={ true }
          checkBoxAttribute="is_police_report_available"
          label="Police report:"
          managerCheckBoxAttribute="police_report_manager_flag"
          showManagerCheckbox={ showManagerCheckbox }
          isDisabled={ isDisabled }
          claimObject={ claimObject }
          onCursorToggle={ onCursorToggle }
          placeholder="Issues?"
          textAreaRowSpan="4"
          onInputBoxChange={ onCursorChange }
          rowId={ COMPONENT_MAP[ 'policeReport' ][ 'rowId' ] }
          fieldId={ COMPONENT_MAP[ 'policeReport' ][ 'id' ] }
          fieldNotes={ getFieldNotes(notes, COMPONENT_MAP[ 'policeReport' ][ 'id' ]) }
          fieldDocs={ getFieldDocs(attachments, COMPONENT_MAP[ 'policeReport' ][ 'id' ]) }
          selectedFieldNote={ selectedFieldNote }
          handleItemNoteClick={ handleItemNoteClick }
          handleMouseEnter={ handleMouseEnter }
          handleMouseLeave={ handleMouseLeave }
          handleAttachmentClick={ handleAttachmentClick }
          deleteAttachment={ deleteAttachment }
          rowType={ CONSTANTS.FIELD_TYPE.static }
          rowStyle={ rowStyle }/>
      </span>
    );
  }
}
