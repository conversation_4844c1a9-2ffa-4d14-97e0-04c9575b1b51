import React from "react";
import Loader from "react-loader-advanced";
import moment from "moment";
import InputBoxRow from "./InputBoxRow.jsx";
import dateFormat from "./../reusable/Utilities/dateFormat.js";
import { CONSTANTS } from "./../reusable/Constants/constants.js";
import accounting from "accounting";
import PropTypes from "prop-types";
import { URLS as apiUrls } from "./../reusable/Utilities/urls";
import { json as ajax } from "./../../ajax.js";
import Alert from "react-s-alert";
const COMPONENT_MAP = require("./../reusable/Constants/constants.js").CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP;

export default class PaymentInfo extends React.Component {

  static propTypes = {
    claimObject: PropTypes.object.isRequired,
    status: PropTypes.string.isRequired,
    statusMap: PropTypes.object.isRequired,
    fieldType: PropTypes.oneOf(['parent', 'child']),
    getFieldNotes: PropTypes.func.isRequired,
    getFieldDocs: PropTypes.func.isRequired,
    selectedFieldNote: PropTypes.object.isRequired,
    handleItemNoteClick: PropTypes.func.isRequired,
    handleMouseEnter: PropTypes.func.isRequired,
    handleMouseLeave: PropTypes.func.isRequired,
    handleAttachmentClick: PropTypes.func.isRequired,
    deleteAttachment: PropTypes.func,
    rowType: PropTypes.string.isRequired,
    rowStyle: PropTypes.object.isRequired,
    attachments: PropTypes.object.isRequired,
    notes: PropTypes.object.isRequired,
    isFinanceUser: PropTypes.bool,
    childIndex: PropTypes.number,
    claimId: PropTypes.number,
    csCheckAmount: PropTypes.number
  };

  constructor(props) {
    super(props);
    this.state = {
      showLoader: false,
      paymentObject: {}
    };
    this.renderAuthorizationNumber = this.renderAuthorizationNumber.bind(this);
    this.renderClaimPaymentDetails = this.renderClaimPaymentDetails.bind(this);
    this.renderCheckNumber = this.renderCheckNumber.bind(this);
    this.renderPaidClaimDetails = this.renderPaidClaimDetails.bind(this);
    this.getPaymentDetails = this.getPaymentDetails.bind(this);
  }

  componentDidMount() {
    if ((this.props.status === CONSTANTS.STATUS_MAP.authorization ||
      this.props.status === CONSTANTS.STATUS_MAP.checkWritten) && !(this.props.claimObject.is_cs_claim && !this.props.claimObject.not_paid_by_cs)) {
      this.getPaymentDetails();
    }
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (this.props.status !== nextProps.status && (nextProps.status === CONSTANTS.STATUS_MAP.authorization ||
      nextProps.status === CONSTANTS.STATUS_MAP.checkWritten) && !(nextProps.claimObject.is_cs_claim && !nextProps.claimObject.not_paid_by_cs)) {
      this.getPaymentDetails();
    }
  }

  getPaymentDetails() {
    this.setState({ showLoader: true }, function () {
      ajax(`${apiUrls.gapClaimPayment.replace('__claimId__', this.props.claimId)}`, {}, {}, (data, status) => {
        if (status === 200) {
          this.setState({ showLoader: false, paymentObject: data.gap_claim_payment });
        } else {
          this.setState({
            showLoader: false
          }, () => {
            Alert.error("Click the browser's Refresh button to reload the payment data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  }

  renderPaidClaimDetails(paymentObject) {
    if (this.props.status === this.props.statusMap.checkWritten) {
      let amount;
      let paidDate;
      if (this.props.claimObject.is_cs_claim && !this.props.claimObject.not_paid_by_cs) {
        if (this.props.fieldType === "child") {
          var checkDocument;
          amount = accounting.formatMoney(this.props.csCheckAmount, '$', 2);
          checkDocument = this.props.attachments.field_documents.find(document => document.field_id === ((this.props.childIndex * 100) + CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP.csChildCheck.id));
        } else {
          amount = accounting.formatMoney(this.props.claimObject.cs_check_amount, '$', 2);
          checkDocument = this.props.attachments.field_documents.find(document => document.field_id === CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP.csCheck.id);
        }
        if (checkDocument) {
          paidDate = checkDocument.documents[0].created_at;
        }
      } else {
        amount = paymentObject.amount ? accounting.formatMoney(paymentObject.amount.Decimal, '$', 2) : "";
        paidDate = paymentObject.paid_date;
      }
      return (
        <div>
          { this.renderCheckNumber(paymentObject) }
          <InputBoxRow labelContainerClass="col-2"
            inputBoxContainerClass="col-2"
            containerClass="justify-content-end"
            hasLabel={ true }
            hasTextValue={ true }
            isFinanceUser={ this.props.isFinanceUser }
            label="Amount:"
            textValue={ amount }
            isDisabled={ this.props.isFinanceUser }
            rowId={ COMPONENT_MAP[`${this.props.fieldType}PaidAmount`]['rowId'] }
            fieldId={ this.props.fieldType === 'parent' ? CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP[`${this.props.fieldType}PaidAmount`]['id'] : (this.props.childIndex * 100) + CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP[`${this.props.fieldType}PaidAmount`]['id'] }
            fieldNotes={ this.props.getFieldNotes(this.props.notes, this.props.fieldType === 'parent' ? CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP[`${this.props.fieldType}PaidAmount`]['id'] : (this.props.childIndex * 100) + CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP[`${this.props.fieldType}PaidAmount`]['id']) }
            fieldDocs={ this.props.getFieldDocs(this.props.attachments, this.props.fieldType === 'parent' ? CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP[`${this.props.fieldType}PaidAmount`]['id'] : (this.props.childIndex * 100) + CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP[`${this.props.fieldType}PaidAmount`]['id']) }
            selectedFieldNote={ this.props.selectedFieldNote }
            handleItemNoteClick={ this.props.handleItemNoteClick }
            handleMouseEnter={ this.props.handleMouseEnter }
            handleMouseLeave={ this.props.handleMouseLeave }
            handleAttachmentClick={ this.props.handleAttachmentClick }
            deleteAttachment={ this.props.deleteAttachment }
            rowType={ this.props.rowType }
            rowStyle={ this.props.rowStyle }/>
          <InputBoxRow labelContainerClass="col-2"
            inputBoxContainerClass="col-2"
            containerClass="justify-content-end"
            hasLabel={ true }
            hasTextValue={ true }
            isFinanceUser={ this.props.isFinanceUser }
            label="Paid date:"
            isDisabled={ this.props.isFinanceUser }
            textValue={ paidDate && moment.utc(paidDate).format(dateFormat.displayDateFormat) }
            rowId={ COMPONENT_MAP[`${this.props.fieldType}PaidDate`]['rowId'] }
            fieldId={ this.props.fieldType === 'parent' ? CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP[`${this.props.fieldType}PaidDate`]['id'] : (this.props.childIndex * 100) + CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP[`${this.props.fieldType}PaidDate`]['id'] }
            fieldNotes={ this.props.getFieldNotes(this.props.notes, this.props.fieldType === 'parent' ? CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP[`${this.props.fieldType}PaidDate`]['id'] : (this.props.childIndex * 100) + CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP[`${this.props.fieldType}PaidDate`]['id']) }
            fieldDocs={ this.props.getFieldDocs(this.props.attachments, this.props.fieldType === 'parent' ? CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP[`${this.props.fieldType}PaidDate`]['id'] : (this.props.childIndex * 100) + CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP[`${this.props.fieldType}PaidDate`]['id']) }
            selectedFieldNote={ this.props.selectedFieldNote }
            handleItemNoteClick={ this.props.handleItemNoteClick }
            handleMouseEnter={ this.props.handleMouseEnter }
            handleMouseLeave={ this.props.handleMouseLeave }
            handleAttachmentClick={ this.props.handleAttachmentClick }
            deleteAttachment={ this.props.deleteAttachment }
            rowType={ this.props.rowType }
            rowStyle={ this.props.rowStyle }/>
        </div>
      );
    }
  }

  renderCheckNumber(paymentObject) {
    if (!(this.props.claimObject.is_cs_claim && !this.props.claimObject.not_paid_by_cs)) {
      return (
        <InputBoxRow labelContainerClass="col-2"
          inputBoxContainerClass="col-2"
          containerClass="justify-content-end"
          hasLabel={ true }
          hasTextValue={ true }
          isFinanceUser={ this.props.isFinanceUser }
          label="Check #:"
          isDisabled={ this.props.isFinanceUser }
          textValue={ paymentObject.check_number }
          rowId={ COMPONENT_MAP[`${this.props.fieldType}CheckNumber`]['rowId'] }
          fieldId={ this.props.fieldType === 'parent' ? CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP[`${this.props.fieldType}CheckNumber`]['id'] : (this.props.childIndex * 100) + CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP[`${this.props.fieldType}CheckNumber`]['id'] }
          fieldNotes={ this.props.getFieldNotes(this.props.notes, this.props.fieldType === 'parent' ? CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP[`${this.props.fieldType}CheckNumber`]['id'] : (this.props.childIndex * 100) + CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP[`${this.props.fieldType}CheckNumber`]['id']) }
          fieldDocs={ this.props.getFieldDocs(this.props.attachments, this.props.fieldType === 'parent' ? CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP[`${this.props.fieldType}CheckNumber`]['id'] : (this.props.childIndex * 100) + CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP[`${this.props.fieldType}CheckNumber`]['id']) }
          selectedFieldNote={ this.props.selectedFieldNote }
          handleItemNoteClick={ this.props.handleItemNoteClick }
          handleMouseEnter={ this.props.handleMouseEnter }
          handleMouseLeave={ this.props.handleMouseLeave }
          handleAttachmentClick={ this.props.handleAttachmentClick }
          deleteAttachment={ this.props.deleteAttachment }
          rowType={ this.props.rowType }
          rowStyle={ this.props.rowStyle }/>
      );
    }
  }

  renderAuthorizationNumber(paymentObject) {
    if (!(this.props.claimObject.is_cs_claim && !this.props.claimObject.not_paid_by_cs)) {
      return (
        <InputBoxRow labelContainerClass="col-2"
          inputBoxContainerClass="col-2"
          containerClass="justify-content-end"
          hasLabel={ true }
          hasTextValue={ true }
          isFinanceUser={ this.props.isFinanceUser }
          label="Authorization #:"
          isDisabled={ this.props.isFinanceUser }
          textValue={ paymentObject.authorization_number }
          rowId={ COMPONENT_MAP[`${this.props.fieldType}AuthorizationNumber`]['rowId'] }
          fieldId={ this.props.fieldType === 'parent' ? CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP[`${this.props.fieldType}AuthorizationNumber`]['id'] : (this.props.childIndex * 100) + CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP[`${this.props.fieldType}AuthorizationNumber`]['id'] }
          fieldNotes={ this.props.getFieldNotes(this.props.notes, this.props.fieldType === 'parent' ? CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP[`${this.props.fieldType}AuthorizationNumber`]['id'] : (this.props.childIndex * 100) + CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP[`${this.props.fieldType}AuthorizationNumber`]['id']) }
          fieldDocs={ this.props.getFieldDocs(this.props.attachments, this.props.fieldType === 'parent' ? CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP[`${this.props.fieldType}AuthorizationNumber`]['id'] : (this.props.childIndex * 100) + CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP[`${this.props.fieldType}AuthorizationNumber`]['id']) }
          selectedFieldNote={ this.props.selectedFieldNote }
          handleItemNoteClick={ this.props.handleItemNoteClick }
          handleMouseEnter={ this.props.handleMouseEnter }
          handleMouseLeave={ this.props.handleMouseLeave }
          handleAttachmentClick={ this.props.handleAttachmentClick }
          deleteAttachment={ this.props.deleteAttachment }
          rowType={ this.props.rowType }
          rowStyle={ this.props.rowStyle }/>
      );
    }
  }

  renderClaimPaymentDetails(paymentObject) {
    const spinnerMessage = <p className='text-center'><i className="fa fa-refresh fa-spin"/> Loading...</p>;
    if (this.props.status === this.props.statusMap.checkWritten || this.props.status === this.props.statusMap.authorization) {
      return (
        <Loader show={ this.state.showLoader } message={ spinnerMessage }>
          { this.renderAuthorizationNumber(paymentObject) }
          { this.renderPaidClaimDetails(paymentObject) }
        </Loader>
      );
    }
  }

  render() {
    return (
      <div>
        { this.renderClaimPaymentDetails(this.state.paymentObject) }
      </div>
    );
  }
}