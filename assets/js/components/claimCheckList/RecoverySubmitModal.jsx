import React from 'react';
import Modal from '../../Modal.jsx';
import PropTypes from 'prop-types';
import If from "./../reusable/If/If.jsx";

export default class RecoverySubmitModal extends React.Component {

  static propTypes = {
    displayModal: PropTypes.bool.isRequired,
    onSubmit: PropTypes.func.isRequired,
    onDecline: PropTypes.func,
    onChange: PropTypes.func,
    hasRecovery: PropTypes.bool,
    recoveryReason: PropTypes.string
  };

  render() {
    const {displayModal, hasRecovery, recoveryReason, onChange, onDecline, onSubmit} = this.props;
    return (
      <Modal visible={displayModal}
        size="small">
        <div className="row">
          <div className="col-12 my-4">
            <If condition={hasRecovery}>
              <h3>Recovery Candidate</h3>
            </If>
            <If condition={!hasRecovery}>
              <h3>Not Recovery Candidate</h3>
            </If>
          </div>
          <div className="col-12">
            <label>Reason:</label>
            <textarea className="form-control"
              id="recoveryReason"
              rows={4}
              onChange={onChange}
              placeholder="Reason"
              value={recoveryReason}/>
          </div>
          <div className="col-12 text-center my-4 row justify-content-end">
            <button type="button"
              className="btn btn-secondary cursor-pointer mr-3"
              onClick={onDecline}>
              Cancel
            </button>
            <button type="button"
              className="btn  btn-primary cursor-pointer"
              disabled={!recoveryReason}
              onClick={onSubmit}>
              Submit
            </button>
          </div>
        </div>
      </Modal>
    );
  }
}