import React from 'react';
import Modal from "../../Modal.jsx";
import PropTypes from 'prop-types';

export default class AssignOwnerModal extends React.Component {

  constructor(props) {
    super(props);
    this.renderClaimUserList = this.renderClaimUserList.bind(this);
    this.onListItemClick = this.onListItemClick.bind(this);
    this.renderClaimUserListItem = this.renderClaimUserListItem.bind(this);
    this.renderLoader = this.renderLoader.bind(this);
    this.renderAssignButton = this.renderAssignButton.bind(this);
    this.renderModalContent = this.renderModalContent.bind(this);
    this.state = {
      selectedUser: ""
    };
  }

  static propTypes = {
    userList: PropTypes.array.isRequired,
    displayModal: PropTypes.bool.isRequired,
    showUserListLoader: PropTypes.bool.isRequired,
    userHasRole: PropTypes.func.isRequired,
    handleOwnerChange: PropTypes.func.isRequired,
    handleModalSubmit: PropTypes.func.isRequired,
    userRole: PropTypes.string.isRequired,
    closeModal: PropTypes.func.isRequired
  };

  renderClaimUserList() {
    if (this.props.userList.length > 0) {
      return this.props.userList.map(this.renderClaimUserListItem);
    }
  }

  onListItemClick(e) {
    if (e.target.value !== '') {
      this.setState({ selectedUser: e.target.value });
      this.props.handleOwnerChange(e);
    }
  }

  renderClaimUserListItem(user) {
    const listItemStyle = {
      "padding": 0,
      "marginBottom": 0,
      "border": 0,
      "borderTopRightRadius": 0,
      "borderTopLeftRadius": 0,
      "borderBottomRightRadius": 0,
      "borderBottomLeftRadius": 0
    };
    if (this.props.userHasRole(user, this.props.userRole)) {
      const listItemClass = this.state.selectedUser === user.id ? "list-group-item active" : "list-group-item";
      return (<li value={user.id} key={user.id} onClick={ this.onListItemClick } style={listItemStyle}
        className={ listItemClass }>
        {`${user.first_name} ${user.last_name}`}
      </li>);
    }
  }

  renderLoader() {
    return (
      <p className={ "text-center"}>
        <i className="fa fa-refresh fa-spin"/> Fetching GAP Claim user list
      </p>
    );
  }

  renderAssignButton() {
    return (
      <button className={"btn btn-primary mr-2"}
        onClick={ this.props.handleModalSubmit }
        disabled={this.state.selectedUser === "" ? true : false}>
        <i className="fa fa-check"></i>&nbsp;Assign
      </button>
    );
  }

  renderModalContent() {
    const listStyle = {
      "height": "150px",
      "overflow": "auto"
    };
    return (
      <div className="form-group">
        <div>
          <p><b> Claim Assignment </b></p>
        </div>
        <div>
          <label className="form-check-label col-form-label col-form-label-sm float-left m-0">
            Assign claim to:&nbsp;
          </label>
          <ul className="form-control ml-1 col-8 d-inline-block list-unstyled" style={listStyle}>
            { this.renderClaimUserList() }
          </ul>
        </div>
        <div className="text-right mt-2 mr-3">
          <button className="btn btn-secondary mr-2" onClick={ this.props.closeModal }>
            Cancel
          </button>
          {this.renderAssignButton()}
        </div>
      </div>
    );
  }

  render() {
    return (
      <section>
        <Modal visible={ this.props.displayModal } size="medium">
          <div>
            { this.props.showUserListLoader ? this.renderLoader() : this.renderModalContent() }
          </div>
        </Modal>
      </section>
    );
  }
}