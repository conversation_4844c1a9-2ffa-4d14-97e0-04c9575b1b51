import React from 'react';
import DateRow from "./DateRow.jsx";
import InputBoxRow from "./InputBoxRow.jsx";
import BankInfoRow from "./BankInfoRow.jsx";
const COMPONENT_MAP = require("./../reusable/Constants/constants.js").CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP;
import PropTypes from 'prop-types';

export default class BankInformation extends React.Component {

  constructor(props) {
    super(props);
  }

  static propTypes = {
    showManagerCheckbox: PropTypes.bool,
    claimObject: PropTypes.object.isRequired,
    onCursorToggle: PropTypes.func.isRequired,
    onCursorValueChange: PropTypes.func.isRequired,
    onCursorChange: PropTypes.func.isRequired,
    loadBankSelectionModal: PropTypes.func.isRequired,
    handleDateChange: PropTypes.func.isRequired,
    isDisabled: PropTypes.bool.isRequired,
    displayBankSelectionModal: PropTypes.bool.isRequired,
    dateFormat: PropTypes.string.isRequired,
    getFieldNotes: PropTypes.func.isRequired,
    getFieldDocs: PropTypes.func.isRequired,
    selectedFieldNote: PropTypes.object.isRequired,
    handleItemNoteClick: PropTypes.func.isRequired,
    handleMouseEnter: PropTypes.func.isRequired,
    handleMouseLeave: PropTypes.func.isRequired,
    handleAttachmentClick: PropTypes.func.isRequired,
    deleteAttachment: PropTypes.func,
    rowType: PropTypes.string.isRequired,
    rowStyle: PropTypes.object.isRequired,
    attachments: PropTypes.object.isRequired,
    notes: PropTypes.object.isRequired,
    isFinanceUser: PropTypes.bool
  };

  render() {
    return (
      <div>
        <InputBoxRow labelContainerClass="col-3"
          inputBoxContainerClass="col-2"
          hasLabel={ true }
          hasCheckbox={ true }
          hasInputBox={ true }
          isFinanceUser={this.props.isFinanceUser}
          checkBoxAttribute="has_insurance_policy_deductible"
          label="Insurance policy deductible:"
          inputBoxType="Currency-Positive"
          inputBoxAttribute="insurance_policy_deductible_value_addition"
          isDisabled={ this.props.isDisabled }
          managerCheckBoxAttribute="insurance_deductible_addition_manager_flag"
          showManagerCheckbox={ this.props.showManagerCheckbox }
          hasDefaultValue={ true }
          claimObject={ this.props.claimObject }
          onCursorToggle={ this.props.onCursorToggle }
          onInputBoxChange={ this.props.onCursorValueChange }
          onInputBoxBlur={ this.props.onCursorValueChange }
          rowId={ COMPONENT_MAP['insurancePolicyDeductiblePositive']['rowId'] }
          fieldId={ COMPONENT_MAP['insurancePolicyDeductiblePositive']['id'] }
          fieldNotes={ this.props.getFieldNotes(this.props.notes, COMPONENT_MAP['insurancePolicyDeductiblePositive']['id']) }
          fieldDocs={ this.props.getFieldDocs(this.props.attachments, COMPONENT_MAP['insurancePolicyDeductiblePositive']['id']) }
          selectedFieldNote={ this.props.selectedFieldNote }
          handleItemNoteClick={ this.props.handleItemNoteClick }
          handleMouseEnter={ this.props.handleMouseEnter }
          handleMouseLeave={ this.props.handleMouseLeave }
          handleAttachmentClick={this.props.handleAttachmentClick}
          deleteAttachment={this.props.deleteAttachment}
          rowType={ this.props.rowType }
          rowStyle={ this.props.rowStyle }/>
        <InputBoxRow labelContainerClass="col-3 form-check-label"
          inputBoxContainerClass="col-2"
          hasInputLabel={ true }
          hasInputBox={ true }
          isFinanceUser={this.props.isFinanceUser}
          inputLabelPlaceholder="Add reason"
          inputLabelAttribute="insurance_policy_deductible_reason"
          inputLabelCustomClass="col-12"
          inputLabelType="Text"
          inputBoxType="Currency-Negative"
          inputBoxAttribute="insurance_policy_deductible_value_subtraction"
          isDisabled={ this.props.isDisabled }
          managerCheckBoxAttribute="insurance_deductible_subtraction_manager_flag"
          showManagerCheckbox={ this.props.showManagerCheckbox }
          hasDefaultValue={ true }
          claimObject={ this.props.claimObject }
          onCursorToggle={ this.props.onCursorToggle }
          onInputBoxChange={ this.props.onCursorValueChange }
          onInputBoxBlur={ this.props.onCursorValueChange }
          rowId={ COMPONENT_MAP['insurancePolicyDeductibleNegative']['rowId'] }
          fieldId={ COMPONENT_MAP['insurancePolicyDeductibleNegative']['id'] }
          fieldNotes={ this.props.getFieldNotes(this.props.notes, COMPONENT_MAP['insurancePolicyDeductibleNegative']['id']) }
          fieldDocs={ this.props.getFieldDocs(this.props.attachments, COMPONENT_MAP['insurancePolicyDeductibleNegative']['id']) }
          selectedFieldNote={ this.props.selectedFieldNote }
          handleItemNoteClick={ this.props.handleItemNoteClick }
          handleMouseEnter={ this.props.handleMouseEnter }
          handleMouseLeave={ this.props.handleMouseLeave }
          handleAttachmentClick={this.props.handleAttachmentClick}
          deleteAttachment={this.props.deleteAttachment}
          rowType={ this.props.rowType }
          rowStyle={ this.props.rowStyle }/>
        <BankInfoRow claimObject={ this.props.claimObject }
          onCursorToggle={ this.props.onCursorToggle }
          isFinanceUser={this.props.isFinanceUser}
          managerCheckBoxAttribute="bank_information_manager_flag"
          showManagerCheckbox={ this.props.showManagerCheckbox }
          loadBankSelectionModal={ this.props.loadBankSelectionModal }
          isDisabled={ this.props.isDisabled }
          displayBankSelectionModal={ this.props.displayBankSelectionModal }
          fieldId={ COMPONENT_MAP['bankInformation']['id'] }
          rowId={ COMPONENT_MAP['bankInformation']['rowId'] }
          fieldNotes={ this.props.getFieldNotes(this.props.notes, COMPONENT_MAP['bankInformation']['id']) }
          fieldDocs={ this.props.getFieldDocs(this.props.attachments, COMPONENT_MAP['bankInformation']['id']) }
          selectedFieldNote={ this.props.selectedFieldNote }
          handleItemNoteClick={ this.props.handleItemNoteClick }
          handleMouseEnter={ this.props.handleMouseEnter }
          handleMouseLeave={ this.props.handleMouseLeave }
          handleAttachmentClick={this.props.handleAttachmentClick}
          deleteAttachment={this.props.deleteAttachment}
          rowType={ this.props.rowType }
          rowStyle={ this.props.rowStyle }/>
        <div className="form-group row justify-content-end">
          <div className="col-2">
            <label className="col-form-label col-form-label-sm">Vendor number:</label>
          </div>
          <div className="col-9">
            <p className="col-form-label-sm mb-0">{ this.props.claimObject.bank_vendor_id }</p>
          </div>
        </div>
        <InputBoxRow labelContainerClass="col-4"
          containerClass="justify-content-end"
          hasLabel={ true }
          hasCheckbox={ true }
          isFinanceUser={this.props.isFinanceUser}
          checkBoxAttribute="is_full_loan_history_available"
          isDisabled={ this.props.isDisabled }
          managerCheckBoxAttribute="full_loan_history_manager_flag"
          showManagerCheckbox={ this.props.showManagerCheckbox }
          label="Full loan history"
          claimObject={ this.props.claimObject }
          onCursorToggle={ this.props.onCursorToggle }
          fieldId={ COMPONENT_MAP['fullLoanHistory']['id'] }
          rowId={ COMPONENT_MAP['fullLoanHistory']['rowId'] }
          fieldNotes={ this.props.getFieldNotes(this.props.notes, COMPONENT_MAP['fullLoanHistory']['id']) }
          fieldDocs={ this.props.getFieldDocs(this.props.attachments, COMPONENT_MAP['fullLoanHistory']['id']) }
          selectedFieldNote={ this.props.selectedFieldNote }
          handleItemNoteClick={ this.props.handleItemNoteClick }
          handleMouseEnter={ this.props.handleMouseEnter }
          handleMouseLeave={ this.props.handleMouseLeave }
          handleAttachmentClick={this.props.handleAttachmentClick}
          deleteAttachment={this.props.deleteAttachment}
          rowType={ this.props.rowType }
          rowStyle={ this.props.rowStyle }/>
        <InputBoxRow labelContainerClass="col-2"
          inputBoxContainerClass="col-2"
          containerClass="justify-content-end"
          hasLabel={ true }
          hasInputBox={ true }
          isFinanceUser={this.props.isFinanceUser}
          label="Loan number:"
          inputBoxType="Text"
          inputBoxAttribute="bank_account_number"
          onCursorToggle={ this.props.onCursorToggle }
          managerCheckBoxAttribute="loan_number_manager_flag"
          showManagerCheckbox={ this.props.showManagerCheckbox }
          isDisabled={ this.props.isDisabled }
          claimObject={ this.props.claimObject }
          onInputBoxChange={ this.props.onCursorValueChange }
          onInputBoxBlur={ this.props.onCursorValueChange }
          fieldId={ COMPONENT_MAP['loanNumber']['id'] }
          rowId={ COMPONENT_MAP['loanNumber']['rowId'] }
          fieldNotes={ this.props.getFieldNotes(this.props.notes, COMPONENT_MAP['loanNumber']['id']) }
          fieldDocs={ this.props.getFieldDocs(this.props.attachments, COMPONENT_MAP['loanNumber']['id']) }
          selectedFieldNote={ this.props.selectedFieldNote }
          handleItemNoteClick={ this.props.handleItemNoteClick }
          handleMouseEnter={ this.props.handleMouseEnter }
          handleMouseLeave={ this.props.handleMouseLeave }
          handleAttachmentClick={this.props.handleAttachmentClick}
          deleteAttachment={this.props.deleteAttachment}
          rowType={ this.props.rowType }
          rowStyle={ this.props.rowStyle }/>
        <DateRow containerClass="justify-content-end"
          labelContainerClass="col-2"
          datePickerContainerClass="col-2"
          hasCheckbox={false}
          isFinanceUser={this.props.isFinanceUser}
          label="Deal date:"
          dateAttribute="contract_deal_date"
          isDisabled={ this.props.isDisabled }
          onCursorToggle={ this.props.onCursorToggle }
          managerCheckBoxAttribute="contract_deal_date_manager_flag"
          showManagerCheckbox={ this.props.showManagerCheckbox }
          claimObject={ this.props.claimObject }
          handleDateChange={ this.props.handleDateChange }
          dateFormat={ this.props.dateFormat }
          rowId={ COMPONENT_MAP['dealDate']['rowId'] }
          fieldId={ COMPONENT_MAP['dealDate']['id'] }
          fieldNotes={ this.props.getFieldNotes(this.props.notes, COMPONENT_MAP['dealDate']['id']) }
          fieldDocs={ this.props.getFieldDocs(this.props.attachments, COMPONENT_MAP['dealDate']['id']) }
          selectedFieldNote={ this.props.selectedFieldNote }
          handleItemNoteClick={ this.props.handleItemNoteClick }
          handleMouseEnter={ this.props.handleMouseEnter }
          handleMouseLeave={ this.props.handleMouseLeave }
          handleAttachmentClick={this.props.handleAttachmentClick}
          deleteAttachment={this.props.deleteAttachment}
          rowType={ this.props.rowType }
          rowStyle={ this.props.rowStyle }/>
        <InputBoxRow labelContainerClass="col-2"
          inputBoxContainerClass="col-2"
          containerClass="justify-content-end"
          hasLabel={ true }
          hasInputBox={ true }
          isFinanceUser={this.props.isFinanceUser}
          label="Term"
          inputBoxType="Number"
          inputBoxAttribute="contract_term_months"
          onCursorToggle={ this.props.onCursorToggle }
          managerCheckBoxAttribute="contract_term_months_manager_flag"
          showManagerCheckbox={ this.props.showManagerCheckbox }
          isDisabled={ this.props.isDisabled }
          hasDefaultValue={ true }
          claimObject={ this.props.claimObject }
          onInputBoxChange={ this.props.onCursorValueChange }
          onInputBoxBlur={ this.props.onCursorValueChange }
          fieldId={ COMPONENT_MAP['term']['id'] }
          rowId={ COMPONENT_MAP['term']['rowId'] }
          fieldNotes={ this.props.getFieldNotes(this.props.notes, COMPONENT_MAP['term']['id']) }
          fieldDocs={ this.props.getFieldDocs(this.props.attachments, COMPONENT_MAP['term']['id']) }
          selectedFieldNote={ this.props.selectedFieldNote }
          handleItemNoteClick={ this.props.handleItemNoteClick }
          handleMouseEnter={ this.props.handleMouseEnter }
          handleMouseLeave={ this.props.handleMouseLeave }
          handleAttachmentClick={this.props.handleAttachmentClick}
          deleteAttachment={this.props.deleteAttachment}
          rowType={ this.props.rowType }
          rowStyle={ this.props.rowStyle }/>
        <InputBoxRow labelContainerClass="col-2"
          inputBoxContainerClass="col-2"
          containerClass="justify-content-end"
          hasLabel={ true }
          hasInputBox={ true }
          isFinanceUser={this.props.isFinanceUser}
          label="Payment amount:"
          inputBoxType="Currency"
          inputBoxAttribute="payment_amount"
          onCursorToggle={ this.props.onCursorToggle }
          managerCheckBoxAttribute="payment_amount_manager_flag"
          showManagerCheckbox={ this.props.showManagerCheckbox }
          isDisabled={ this.props.isDisabled }
          hasDefaultValue={ true }
          claimObject={ this.props.claimObject }
          onInputBoxChange={ this.props.onCursorValueChange }
          onInputBoxBlur={ this.props.onCursorValueChange }
          fieldId={ COMPONENT_MAP['paymentAmount']['id'] }
          rowId={ COMPONENT_MAP['paymentAmount']['rowId'] }
          fieldNotes={ this.props.getFieldNotes(this.props.notes, COMPONENT_MAP['paymentAmount']['id']) }
          fieldDocs={ this.props.getFieldDocs(this.props.attachments, COMPONENT_MAP['paymentAmount']['id']) }
          selectedFieldNote={ this.props.selectedFieldNote }
          handleItemNoteClick={ this.props.handleItemNoteClick }
          handleMouseEnter={ this.props.handleMouseEnter }
          handleMouseLeave={ this.props.handleMouseLeave }
          handleAttachmentClick={this.props.handleAttachmentClick}
          deleteAttachment={this.props.deleteAttachment}
          rowType={ this.props.rowType }
          rowStyle={ this.props.rowStyle }/>
        <InputBoxRow labelContainerClass="col-2"
          inputBoxContainerClass="col-2"
          containerClass="justify-content-end"
          hasLabel={ true }
          hasInputBox={ true }
          isFinanceUser={this.props.isFinanceUser}
          label="Interest rate:"
          inputBoxType="Percentage"
          inputBoxAttribute="interest_rate"
          onCursorToggle={ this.props.onCursorToggle }
          managerCheckBoxAttribute="interest_rate_manager_flag"
          showManagerCheckbox={ this.props.showManagerCheckbox }
          isDisabled={ this.props.isDisabled }
          hasDefaultValue={ true }
          claimObject={ this.props.claimObject }
          onInputBoxChange={ this.props.onCursorValueChange }
          onInputBoxBlur={ this.props.onCursorValueChange }
          fieldId={ COMPONENT_MAP['interestRate']['id'] }
          rowId={ COMPONENT_MAP['interestRate']['rowId'] }
          fieldNotes={ this.props.getFieldNotes(this.props.notes, COMPONENT_MAP['interestRate']['id']) }
          fieldDocs={ this.props.getFieldDocs(this.props.attachments, COMPONENT_MAP['interestRate']['id']) }
          selectedFieldNote={ this.props.selectedFieldNote }
          handleItemNoteClick={ this.props.handleItemNoteClick }
          handleMouseEnter={ this.props.handleMouseEnter }
          handleMouseLeave={ this.props.handleMouseLeave }
          handleAttachmentClick={this.props.handleAttachmentClick}
          deleteAttachment={this.props.deleteAttachment}
          rowType={ this.props.rowType }
          rowStyle={ this.props.rowStyle }/>
        <DateRow containerClass="justify-content-end"
          labelContainerClass="col-2"
          datePickerContainerClass="col-2"
          hasCheckbox={false}
          isFinanceUser={this.props.isFinanceUser}
          label="First payment date:"
          dateAttribute="first_payment_date"
          isDisabled={ this.props.isDisabled }
          onCursorToggle={ this.props.onCursorToggle }
          managerCheckBoxAttribute="first_payment_manager_flag"
          showManagerCheckbox={ this.props.showManagerCheckbox }
          claimObject={ this.props.claimObject }
          handleDateChange={ this.props.handleDateChange }
          dateFormat={ this.props.dateFormat }
          rowId={ COMPONENT_MAP['firstPaymentDate']['rowId'] }
          fieldId={ COMPONENT_MAP['firstPaymentDate']['id'] }
          fieldNotes={ this.props.getFieldNotes(this.props.notes, COMPONENT_MAP['firstPaymentDate']['id']) }
          fieldDocs={ this.props.getFieldDocs(this.props.attachments, COMPONENT_MAP['firstPaymentDate']['id']) }
          selectedFieldNote={ this.props.selectedFieldNote }
          handleItemNoteClick={ this.props.handleItemNoteClick }
          handleMouseEnter={ this.props.handleMouseEnter }
          handleMouseLeave={ this.props.handleMouseLeave }
          handleAttachmentClick={this.props.handleAttachmentClick}
          deleteAttachment={this.props.deleteAttachment}
          rowType={ this.props.rowType }
          rowStyle={ this.props.rowStyle }/>
      </div>
    );
  }
}
