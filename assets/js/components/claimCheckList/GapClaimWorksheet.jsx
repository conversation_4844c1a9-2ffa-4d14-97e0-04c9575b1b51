import React from "react";
import Alert from "react-s-alert";
import Loader from "react-loader-advanced";
import moment from "moment";
import immstruct from "immstruct";
import Immutable from "immutable";
import { json as ajax } from "./../../ajax.js";
import { addRecordNote } from "../reusable/RecordNotes/addRecordNotes.js";
import { scrollTo } from "../reusable/Utilities/Scroll.js";
import { CONSTANTS } from "./../reusable/Constants/constants.js";
import dateFormat from "./../reusable/Utilities/dateFormat.js";
import accounting from "accounting";
import { URLS as apiUrls } from "./../reusable/Utilities/urls";
import PropTypes from "prop-types";
import { userHasRole } from "./../reusable/Utilities/userHasRole";
import Header from "./Header.jsx";
import InputBoxRow from "./InputBoxRow.jsx";
import StatusDropdown from "./StatusDropdown.jsx";
import ChildClaimModal from "./ChildClaimModal.jsx";
import ChildClaimDetails from "./ChildClaimDetails.jsx";
import PaymentInfo from "./PaymentInfo.jsx";
import ValuationReport from "./ValuationReport.jsx";
import CancelContracts from "./CancelContracts.jsx";
import BankInformation from "./BankInformation.jsx";
import BankSelectionModal from "./BankSelectionModal.jsx";
import AddInsuranceCompanyModal from "./AddInsuranceCompanyModal";
import UserSelectionModal from "./UserSelectionModal.jsx";
import AssignOwnerModal from "./AssignOwnerModal.jsx";
import ConfirmUpdateModal from "./ConfirmUpdateModal.jsx";
import ConfirmationModal from "./../reusable/ConfirmationModal/ConfirmationModal.jsx";
import RecordNotes from "./../reusable/RecordNotes/RecordNotes.jsx";
import AttachmentModal from "./FileAttachment.jsx";
import RunAmortizationModal from "./AmortizationModal.jsx";
import VoidCheck from "./VoidCheck.jsx";
import CSCheckAttachmentModal from "./CSCheckAttachment.jsx";
import DocumentLinkList from "./../reusable/DocumentLinkList/DocumentLinkList.jsx";
import RecoveryData from "./RecoveryData.jsx";

const style = {
  rowStyle: {
    border: "1px solid #ccc",
    borderRadius: "0.25rem 0 0 0.25rem",
    position: "relative",
    borderRight: "none",
    marginRight: "-16px",
    zIndex: "9"
  },
  notesStyle: {
    position: 'absolute',
    right: 0,
    top: 0,
    zIndex: 9
  }
};

const MSRP_OVER_LIMIT = 1.5;

export default class GapClaimWorksheet extends React.Component {

  IMMS_KEY = 'gap_claim';

  static contextTypes = {
    router: PropTypes.object.isRequired
  };

  static propTypes = {
    location: PropTypes.shape({
      query: PropTypes.shape({
        id: PropTypes.string.isRequired
      }).isRequired,
    }).isRequired,
    user: PropTypes.shape({
      id: PropTypes.number.isRequired,
      first_name: PropTypes.string.isRequired,
      last_name: PropTypes.string.isRequired,
      email: PropTypes.string.isRequired,
      stores: PropTypes.arrayOf(PropTypes.string.isRequired),
      roles: PropTypes.shape({
        Map: PropTypes.object,
      }).isRequired,
      banner_info: PropTypes.shape({
        header: PropTypes.string.isRequired,
        message: PropTypes.string.isRequired,
        enabled: PropTypes.bool.isRequired,
      }).isRequired,
    })
  };

  constructor(props) {
    super(props);
    this.gap_claim = immstruct(
      this.IMMS_KEY,
      {
        id: 0,
        vin: '',
        created_by_user_id: this.props.user.id || '',
        status_change_description: '',
        date_of_claim_received: moment(),
        date_of_last_out: moment(),
        date_of_last_in: moment(),
        contract_number: '',
        date_of_loss: moment(),
        waiting_for: '',
        last_message: '',
        first_name: '',
        last_name: '',
        is_business:false,
        business_name: '',
        state: '',
        case_reserve: '',
        avg_case_reserve: '',
        finance_manager: '',
        run_amortization_sheet_value: '',
        calculate_amortization_value: false,
        has_canceled_contracts: false,
        contracts: [],
        other_label1: '',
        other_value1: '',
        other_label2: '',
        other_value2: '',
        other_label3: '',
        other_value3: '',
        run_amortization_manager_flag: false,
        other_label1_manager_flag: false,
        other_label2_manager_flag: false,
        other_label3_manager_flag: false,
        insurance_payment_check_manager_flag: false,
        settlement_letter_manager_flag: false,
        has_negative_equity_amount_manager_flag: false,
        valuation_report_manager_flag: false,
        valuation_report_base_value_manager_flag: false,
        valuation_report_vin_matches_manager_flag: false,
        valuation_report_prior_damage_manager_flag: false,
        valuation_report_misc_fee_manager_flag: false,
        valuation_report_mileage_manager_flag: false,
        valuation_report_dol_manager_flag: false,
        valuation_report_type_manager_flag: false,
        options_book_out_over_150_percent_manager_flag: false,
        original_financing_manager_flag: false,
        msrp_value_manager_flag: false,
        contract_number_matches_manager_flag: false,
        bank_history_matches_manager_flag: false,
        police_report_manager_flag: false,
        insurance_deductible_addition_manager_flag: false,
        insurance_deductible_subtraction_manager_flag: false,
        loan_number_manager_flag: false,
        contract_deal_date_manager_flag: false,
        contract_term_months_manager_flag: false,
        payment_amount_manager_flag: false,
        interest_rate_manager_flag: false,
        first_payment_manager_flag: false,
        full_loan_history_manager_flag: false,
        bank_information_manager_flag: false,
        has_settlement_amount: false,
        settlement_amount: '',
        has_insurance_check_amount: false,
        insurance_check_amount: '',
        is_valuation_report_available: false,
        valuation_report_adjustments: '',
        is_valuation_report_matches_base_value: true,
        valuation_report_matches_base_value: '',
        valuation_report_vin_matches: false,
        has_valuation_report_prior_damage: false,
        valuation_report_prior_damage_value: '',
        has_valuation_report_misc_fee: false,
        valuation_report_misc_fee_value: '',
        valuation_report_mileage: '',
        valuation_report_type: '',
        over_150_percent: '',
        has_msrp_value: false,
        msrp_value: '',
        has_original_financing_contract: false,
        original_financing_contract_value: '',
        contract_number_matches: false,
        bank_history_matches: false,
        is_police_report_available: false,
        has_insurance_policy_deductible: false,
        insurance_policy_deductible_value_addition: '',
        insurance_policy_deductible_value_subtraction: '',
        insurance_policy_deductible_reason: '',
        has_bank_information: false,
        is_full_loan_history_available: false,
        payment_amount: '',
        interest_rate: '',
        contract_deal_date: moment(),
        contract_term_months: '',
        first_payment_date: moment(),
        has_new_bank_information: false,
        bank_account_name: '',
        bank_account_number: '',
        bank_address_street1: '',
        bank_address_street2: '',
        bank_address_city: '',
        bank_address_state: '',
        bank_address_zip: '',
        bank_vendor_id: '',
        owner_id: this.props.user.id || '',
        child_claim_id: '',
        updated_at: '',
        updated_by_user_id: '',
        updated_by_user_name: '',
        is_cs_claim: false,
        not_paid_by_cs: false,
        cs_check_amount: '',
        child_claims: [],
        insurance_company: '',
        has_insurance_company: false,
        policy_number: '',
        has_policy_number: false,
        mileage_deduction: '',
        has_mileage_deduction: false,
        nada: '',
        has_nada: false,
        has_valuation_nada_difference: false,
        mileage_deduction_manager_flag: false,
        has_estimate_with_photos: false,
        nada_manager_flag: false,
        recovery_check_amount: '',
        recovery_status: '',
        is_in_progress: false,
        number_of_delinquent_payments: '',
        total_delinquent_payments_covered: '',
        number_of_extensions: '',
        total_extensions_covered: '',
      }
    );
    this.gap_claim.on('swap', (newStructure, oldStructure, keyPath) => {
      this.setState({ gap_claim: this.gap_claim.cursor() });
    });
    this.state = {
      gap_claim: this.gap_claim.cursor(),
      userList: [],
      showLoader: false,
      status: '',
      initialStatus: '',
      updateRecordNotes: false,
      displayModal: false,
      displayUpdateConfirmationModal: false,
      displayBackConfirmationModal: false,
      displayNoGapConfirmationModal: false,
      displayAssignOwnerModal: false,
      displayAttachmentModal: false,
      showUserListLoader: false,
      showParentPaymentLoader: false,
      showChildPaymentLoader: false,
      displayBankSelectionModal: false,
      displayChildClaimModal: false,
      insuranceDisplayModal: false,
      customRowsFieldMap: [
        {
          id: 1,
          label: 'other_label1',
          value: 'other_value1',
          managerCheckBoxField: 'other_label1_manager_flag',
          show: true,
          itemNoteAttribute: 'otherInputOne'
        },
        {
          id: 2,
          label: 'other_label2',
          value: 'other_value2',
          managerCheckBoxField: 'other_label2_manager_flag',
          show: false,
          itemNoteAttribute: 'otherInputTwo'
        },
        {
          id: 3,
          label: 'other_label3',
          value: 'other_value3',
          managerCheckBoxField: 'other_label3_manager_flag',
          show: false,
          itemNoteAttribute: 'otherInputThree'
        }
      ],
      showNotes: false,
      notesURL: "",
      worksheetOffset: 0,
      selectedFieldNote: {
        itemNoteOffset: '',
        rowType: '',
        isSelected: false,
        contractDetails: {},
        fieldId: '',
        fieldNotes: {}
      },
      hideFieldNoteTextArea: true,
      fieldNoteHovered: false,
      fieldNoteClicked: false,
      noteTitle: "",
      noteType: "",
      enableRecoveryCase: false,
      noteEventType: "",
      userRole: "",
      newOwnerName: "",
      prevOwnerID: "",
      attachmentParams: {
        title: '',
        fieldId: '',
        rowType: '',
        contractCode: '',
        contractNumber: ''
      },
      isWorksheetUpdated: false,
      displayAmortizationModal: false,
      showVoidCheckConfirmationModal: false,
      showCSCheckAttachmentModal: false,
      showAuthorizeConfirmationModal: false,
      statusType: '',
      statusValue: '',
      attachments: {},
      notes: {},
      updateTriggered: false,
      submitting: false,
    };
  }

  componentDidMount = () => {
    document.title = 'TCA Portal - GAP Claims Worksheet';
    this.loadClaim();
    this.loadAttachments();
    this.loadNotes();
  };

  componentWillUnmount = () => {
    this.gap_claim.removeAllListeners();
    immstruct.remove(this.IMMS_KEY);
  };

  loadAttachments = (successCallback) => {
    this.setState({ showLoader: true }, () => {
      ajax(`${apiUrls.getDocument.replace('__documentId__', this.props.location.query.id)}`, {}, {}, (data, status) => {
        if (status === 200) {
          this.setState({
            attachments: data.docs,
            showLoader: false
          }, () => {
            if (successCallback) {
              successCallback();
            }
          });
        } else {
          this.setState({ showLoader: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  loadNotes = (successCallback) => {
    this.setState({ showLoader: true }, () => {
      ajax(`${apiUrls.getNotes.replace('__claimId__', this.props.location.query.id)}`, {}, {}, (data, status) => {
        if (status === 200) {
          this.setState({
            notes: data,
            showLoader: false
          }, () => {
            if (successCallback) {
              successCallback();
            }
          });
        } else {
          this.setState({ showLoader: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  getClaimUserList = () => {
    this.setState({ showUserListLoader: true }, () => {
      ajax(apiUrls.userList, {}, {}, (data, status) => {
        if (status === 200) {
          this.setState({ userList: data.users, showUserListLoader: false });
        } else {
          this.setState({ showUserListLoader: false });
          Alert.error("Click the browser's Refresh button to reload the user list. If the error continues, contact your system administrator.");
        }
      });
    });
  };

  loadClaim = () => {
    this.setState({ showLoader: true }, () => {
      ajax(`${apiUrls.gapclaims}/${this.props.location.query.id}`, {}, {}, (data, status) => {
        if (status === 200) {
          this.state.gap_claim.update(() => Immutable.fromJS(data.gap_claim));
          this.setState({
            initialStatus: data.gap_claim.status,
            showLoader: false,
            isWorksheetUpdated: false
          });
        } else {
          this.setState({ showLoader: false, isWorksheetUpdated: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  calculateCaseReserveAmount = (claimObject) => {
    let caseReserve = 0;
    if (claimObject.insurance_policy_deductible_value_addition) {
      caseReserve += parseFloat(claimObject.insurance_policy_deductible_value_addition);
    }
    if (claimObject.run_amortization_sheet_value > 0) {
      caseReserve += parseFloat(claimObject.run_amortization_sheet_value);
    }
    if (claimObject.contracts.length > 0) {
      for (let index = 0; index < claimObject.contracts.length; index++) {
        if (claimObject.contracts[index]['contract_value']) {
          caseReserve -= parseFloat(claimObject.contracts[index]['contract_value']);
        }
      }
    }
    if(claimObject.recovery_status === CONSTANTS.GAP_RECOVERY_STATUS_MAP.recovered.code) {
      caseReserve -= parseFloat(claimObject.recovery_check_amount);
    }
    if (claimObject.over_150_percent && claimObject.over_150_percent < 0) {
      caseReserve += parseFloat(claimObject.over_150_percent);
    }
    caseReserve = (caseReserve - claimObject.other_value1 - claimObject.other_value2 -
      claimObject.other_value3 - claimObject.settlement_amount - claimObject.valuation_report_prior_damage_value -
      claimObject.valuation_report_misc_fee_value - claimObject.insurance_policy_deductible_value_subtraction - claimObject.mileage_deduction).toFixed(2);
    return caseReserve;
  };

  getCaseReserveAmount = (claimObject) => {
    if (this.state.initialStatus === CONSTANTS.STATUS_MAP.inquiry || !this.hasAttachment() || (claimObject.is_cs_claim && !claimObject.not_paid_by_cs)) {
      return 0;
    } else if (!claimObject.settlement_amount ||
      claimObject.settlement_amount === "0" ||
      !claimObject.run_amortization_sheet_value ||
      claimObject.run_amortization_sheet_value === "0") {
      return claimObject.avg_case_reserve;
    } else if (claimObject.settlement_amount && claimObject.run_amortization_sheet_value && this.hasAttachment()) {
      return this.calculateCaseReserveAmount(claimObject);
    }
  };

  getClaimData = () => {
    let claimObject = this.state.gap_claim.toJS();
    claimObject['case_reserve'] = this.getCaseReserveAmount(claimObject);
    let numericValueMap = [
      'valuation_report_mileage',
      'owner_id',
      'created_by_user_id',
      'contract_term_months',
      'number_of_delinquent_payments',
      'number_of_extensions',
    ];
    for (let i = 0; i < numericValueMap.length; i++) {
      claimObject[numericValueMap[i]] = !isNaN(claimObject[numericValueMap[i]]) && claimObject[numericValueMap[i]] !== '' ?
        parseFloat(claimObject[numericValueMap[i]]) : 0;
    }
    if (claimObject.status === CONSTANTS.STATUS_MAP.inquiry && this.hasAttachment()) {
      claimObject.status = CONSTANTS.STATUS_MAP.pending;
    }
    return claimObject;
  };

  displayUpdateConfirmation = () => {
    this.setState({ displayUpdateConfirmationModal: true, displayAssignOwnerModal: false });
  };

  confirmUpdate = () => {
    this.setState({ displayUpdateConfirmationModal: false, displayAssignOwnerModal: false }, () => {
      this.updateClaimList();
    });
  };

  cancelUpdate = () => {
    this.setState({ displayUpdateConfirmationModal: false, displayAssignOwnerModal: false }, () => {
      this.state.gap_claim.cursor('owner_id').update(() => parseInt(this.state.prevOwnerID));
    });
  };

  onInsuranceModalClosed = (companyName) => {
    this.setState({
      insuranceDisplayModal: false,
      isWorksheetUpdated: companyName || this.state.isWorksheetUpdated
    }, () => {
      // update cursor after set state callback
      companyName && this.state.gap_claim.cursor('insurance_company').update(() => companyName);
    });
  };

  addInsuranceCompanyName = () => {
    this.setState({
      insuranceDisplayModal: true
    });
  };

  updateClaimList = () => {
    if (this.state.isWorksheetUpdated) {
      const claimObject = this.getClaimData();
      this.setState({ showLoader: true, submitting: true }, () => {
        ajax(`${apiUrls.gapclaims}/${this.props.location.query.id}`, claimObject, { method: 'PUT' }, (data, status) => {
          if (status === 200) {
            this.setState({ displayModal: false, showLoader: false, submitting:false }, () => {
              if (claimObject.status === CONSTANTS.STATUS_MAP.waitingForAuthorization && userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaims) && !userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaimsManager) && parseFloat(this.calculateCaseReserveAmount(claimObject)) <= 0) {
                this.addNote("This claim is a No GAP Claim.");
              }
              if (claimObject.status === CONSTANTS.STATUS_MAP.returnedForCorrection && userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaimsManager) && parseFloat(this.calculateCaseReserveAmount(claimObject)) <= 0) {
                this.addNote("Send Out No GAP Letter.");
              }
              this.reloadRecordNotesOnStatusChange(claimObject.status);
              Alert.success("Update successful.");
              if (this.state.displayBackConfirmationModal) {
                this.setState({ displayBackConfirmationModal: false }, () => {
                  this.context.router.goBack();
                });
              }
              this.loadClaim();
            });
          } else {
            this.setState({
              displayModal: false,
              showLoader: false,
              displayBackConfirmationModal: false,
              submitting: false
            }, () => {
              const errorMessage = data.message || "Click the Submit button again. If the error continues, contact your system administrator.";
              if (data.errors && data.errors.bank_information) {
                Alert.error("Invalid bank information, update bank details.");
              } else if (userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaims) && !userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaimsManager)) {
                Alert.error(errorMessage);
              } else if (userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaimsManager)) {
                Alert.error(errorMessage);
              }
            });
          }
        });
      });
    } else {
      this.setState({ updateTriggered: true });
    }
  };

  reloadRecordNotesOnStatusChange = (currentStatus) => {
    if (this.state.initialStatus !== currentStatus) {
      this.setState({ updateRecordNotes: true });
    }
  };

  addNote = (note) => {
    addRecordNote(this.props.location.query.id, note, this.onAddNoteSuccess);
  };

  onAddNoteSuccess = () => {
    this.setState({ updateRecordNotes: true });
  };

  onCursorToggle = (name, e) => {
    this.setState({ isWorksheetUpdated: true });
    this.state.gap_claim.cursor(name).update(oldValue => !oldValue);
  };

  onCursorValueChange = (name, value) => {
    const claimObject = this.state.gap_claim.toJS();
    this.setState({ isWorksheetUpdated: true });
    switch (name) {
    case 'original_financing_contract_value':
      if (claimObject['msrp_value'] && (parseFloat(value) > parseFloat(claimObject['msrp_value']))) {
        this.state.gap_claim.update(oldValue => oldValue.merge(Immutable.fromJS({
          original_financing_contract_value: value,
          over_150_percent: ((parseFloat(claimObject['msrp_value']) * MSRP_OVER_LIMIT) - parseFloat(value)).toFixed(2)
        })));
        return;
      } else if ((claimObject['msrp_value'] && (parseFloat(value) < parseFloat(claimObject['msrp_value']))) || !claimObject['msrp_value'] || !parseFloat(value)) {
        this.state.gap_claim.update(oldValue => oldValue.merge(Immutable.fromJS({
          original_financing_contract_value: value,
          over_150_percent: "0"
        })));
        return;
      }
      break;
    case 'msrp_value':
      if (claimObject['original_financing_contract_value'] && (parseFloat(value) < parseFloat(claimObject['original_financing_contract_value']))) {
        this.state.gap_claim.update(oldValue => oldValue.merge(Immutable.fromJS({
          msrp_value: value,
          over_150_percent: ((parseFloat(value) * MSRP_OVER_LIMIT) - parseFloat(claimObject['original_financing_contract_value'])).toFixed(2)
        })));
        return;
      } else if ((claimObject['original_financing_contract_value'] && (parseFloat(value) > parseFloat(claimObject['original_financing_contract_value']))) || !claimObject['original_financing_contract_value'] || !parseFloat(value)) {
        this.state.gap_claim.update(oldValue => oldValue.merge(Immutable.fromJS({
          msrp_value: value,
          over_150_percent: "0"
        })));
        return;
      }
      break;
    case 'total_delinquent_payments_covered':
      if (claimObject['payment_amount'] && claimObject['number_of_delinquent_payments']) {
        if (parseFloat(value).toFixed(2) !== ((parseFloat(claimObject['number_of_delinquent_payments']) * parseFloat(claimObject['payment_amount'])).toFixed(2))){
          Alert.warning("Delinquent payments don't match with calculated value");
        }
      }
      break;
    case 'total_extensions_covered':
      if (claimObject['payment_amount'] && claimObject['number_of_extensions']) {
        if (parseFloat(value).toFixed(2) !== ((parseFloat(claimObject['number_of_extensions']) * parseFloat(claimObject['payment_amount'])).toFixed(2))){
          Alert.warning("Extensions don't match with calculated value");
        }
      }
      break;
    }
    this.state.gap_claim.cursor(name).update(() => value);
  };

  onCursorChange = (name, e) => {
    this.setState({ isWorksheetUpdated: true });
    this.state.gap_claim.cursor(name).update(() => e.target.value);
  };

  handleDateChange = (name, date) => {
    this.setState({ isWorksheetUpdated: true });
    this.state.gap_claim.cursor(name).update(() => {
      if (date) {
        date = date.format(dateFormat.displayDateFormat);
        return moment.utc(date, dateFormat.displayDateFormat).format(dateFormat.backendDateFormat);
      } else {
        return moment.utc(new Date()).format(dateFormat.backendDateFormat);
      }
    });
  };

  handleStatusChange = (statusType, e) => {
    const claimObject = this.state.gap_claim.toJS();
    if (e.target.value) {
      if (!this.hasAttachment()) {
        Alert.error('A status change can not be completed until a document is attached to the claim.  Please attach a document and try again.');
      } else if (((e.target.value === 'waitingForAuthorization' || e.target.value === 'pendingDenial' || e.target.value === 'authorization' ) &&
          userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaims) && !userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaimsManager) && !this.hasRequiredDetails()) ||
        ((userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaimsManager) && !this.hasRequiredDetails())
          && e.target.value !== 'noGap'
          && e.target.value !== 'closedNoResponse'
          && e.target.value !== 'denied'
          && this.state.initialStatus !== CONSTANTS.STATUS_MAP.noGap
          && this.state.initialStatus !== CONSTANTS.STATUS_MAP.closedNoResponse)) {
        Alert.error('Check all the required checkboxes before submitting the claim.');
      } else if (claimObject.is_cs_claim && e.target.value === 'authorization' && !this.hasRequiredDetails()) {
        Alert.error('Check all the required checkboxes before submitting the claim.');
      } else if (!(claimObject.is_cs_claim && !claimObject.not_paid_by_cs) && e.target.value === 'authorization' && this.hasRequiredDetails()) {
        this.setState({
          showAuthorizeConfirmationModal: true,
          statusType: statusType,
          statusValue: e.target.value
        });
      } else {
        this.changeStatus(statusType, e.target.value);
      }
    }
  };

  changeStatus = (statusType, statusValue) => {
    const statusCode = CONSTANTS.STATUS_MAP[statusValue];
    this.setState({
      status: statusValue
    }, () => {
      this.onCursorValueChange(statusType, statusCode);
    });
  };

  handleOwnerChange = (e) => {
    if (e.target.value !== '') {
      const claimObject = this.state.gap_claim.toJS();
      this.setState({
        newOwnerName: e.target.textContent,
        prevOwnerID: claimObject.owner_id,
        isWorksheetUpdated: true
      });
      this.state.gap_claim.cursor('owner_id').update(() => parseInt(e.target.value));
    }
  };

  handleReopenClaim = () => {
    this.setState({ displayChildClaimModal: true });
  };

  closeChildClaimModal = () => {
    this.setState({ displayChildClaimModal: false });
  };

  /**
   * This function is added to reopen approved claim,
   * It will change Authorized claim's status to 'P - Pending' from 'WC - Waiting for check'.
   * It will also change the owner of claim to the claim creator.
   * */

  reopenClaim = (status) => {
    const claimObject = this.state.gap_claim.toJS();
    let id;
    if (!claimObject.child_claims) {
      id = this.props.location.query.id;
    } else {
      id = this.getReopenChildClaimId(claimObject);
    }
    // If claim is voided then we will override the id with current claims id
    if (claimObject.status === CONSTANTS.STATUS_MAP.checkVoided) {
      id = claimObject.id;
    }

    this.setState({ showLoader: true }, () => {
      ajax(`${apiUrls.gapclaims}/${id}`, {
        'owner_id': claimObject.created_by_user_id,
        'id': claimObject.id,
        'status': status
      }, { method: 'PUT' }, (data, status) => {
        if (status === 200) {
          this.reloadRecordNotesOnStatusChange(status);
          this.loadClaim();
        } else {
          this.setState({ showLoader: false });
          Alert.error("Click the Update button again. If the error continues, contact your system administrator.");
        }
      });
      this.closeChildClaimModal();
    });
  };

  openChildClaim = () => {
    this.setState({ showLoader: true }, () => {
      ajax(`${apiUrls.gapClaimsCreateChildClaim.replace("__claimId__", this.props.location.query.id)}`, {}, { method: 'POST' }, (data, status) => {
        if (status === 200) {
          this.addNote('Child claim opened.');
          this.loadClaim();
        } else {
          this.setState({ showLoader: false });
          if (data.message) {
            Alert.error(data.message);
          } else {
            Alert.error("Click the Reopen button again. If the error continues, contact your system administrator.");
          }
        }
      });
      this.closeChildClaimModal();
    });
  };

  handleUpdateSubmit = () => {
    const claimObject = this.state.gap_claim.toJS();
    if (userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaims) && !userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaimsManager) &&
      (claimObject.status === CONSTANTS.STATUS_MAP.waitingForAuthorization || claimObject.status === CONSTANTS.STATUS_MAP.pendingDenial)) {
      this.setState({ displayBackConfirmationModal: false, displayModal: true }, () => {
        this.getClaimUserList();
      });
    } else {
      this.updateClaimList();
    }
  };

  handleNoGapSubmit = () => {
    this.setState({ displayNoGapConfirmationModal: true });
  };

  onNoGapSubmitConfirm = () => {
    this.setState({ displayNoGapConfirmationModal: false }, () => {
      this.handleUpdateSubmit();
    });
  };

  handleBackButtonOnClick = () => {
    if (this.state.isWorksheetUpdated) {
      this.setState({ displayBackConfirmationModal: true });
    } else {
      this.context.router.goBack();
    }
  };

  redirectToPrevious = () => {
    this.setState({ displayBackConfirmationModal: false }, () => {
      this.context.router.goBack();
    });
  };

  hasRequiredDetails = () => {
    const claimObject = this.state.gap_claim.toJS();
    if (claimObject.run_amortization_sheet_value !== "0" &&
      claimObject.has_canceled_contracts &&
      claimObject.has_settlement_amount &&
      claimObject.has_insurance_check_amount &&
      claimObject.is_valuation_report_available &&
      claimObject.has_valuation_report_prior_damage &&
      claimObject.has_valuation_report_misc_fee &&
      claimObject.has_original_financing_contract &&
      claimObject.has_msrp_value &&
      claimObject.contract_number_matches &&
      claimObject.is_police_report_available &&
      claimObject.has_insurance_policy_deductible &&
      claimObject.is_full_loan_history_available &&
      claimObject.has_bank_information &&
      claimObject.has_number_of_delinquent_payments &&
      claimObject.has_total_delinquent_payments_covered &&
      claimObject.has_number_of_extensions &&
      claimObject.has_total_extensions_covered) {
      return true;
    } else if (!(claimObject.run_amortization_sheet_value !== "0" &&
        claimObject.has_canceled_contracts &&
        claimObject.has_settlement_amount &&
        claimObject.has_insurance_check_amount &&
        claimObject.is_valuation_report_available &&
        claimObject.has_valuation_report_prior_damage &&
        claimObject.has_valuation_report_misc_fee &&
        claimObject.has_original_financing_contract &&
        claimObject.has_msrp_value &&
        claimObject.contract_number_matches &&
        claimObject.is_police_report_available &&
        claimObject.has_insurance_policy_deductible &&
        claimObject.is_full_loan_history_available &&
        claimObject.has_bank_information &&
        claimObject.has_number_of_delinquent_payments &&
        claimObject.has_total_delinquent_payments_covered &&
        claimObject.has_number_of_extensions &&
        claimObject.has_total_extensions_covered)) {
      return false;
    }
  };

  hasAttachment = () => {
    if (this.state.attachments && this.state.attachments.field_documents && this.state.attachments.field_documents.length) {
      return true;
    }
    if (this.state.attachments && this.state.attachments.contract_field_documents && this.state.attachments.contract_field_documents.length) {
      return true;
    }
  };

  onAssignOwnerButtonClick = () => {
    this.setState({ displayAssignOwnerModal: true, userRole: CONSTANTS.USER_ROLES.gapClaims }, () => {
      this.getClaimUserList();
    });
  };

  canReopenChildClaims = (claimObject) => {
    if (claimObject.child_claims && claimObject.child_claims.length > 0) {
      for (let index = 0; index < claimObject.child_claims.length; index++) {
        if (claimObject.child_claims[index]['status'] !== CONSTANTS.STATUS_MAP.authorization &&
          claimObject.child_claims[index]['status'] !== CONSTANTS.STATUS_MAP.checkWritten &&
          claimObject.child_claims[index]['status'] !== CONSTANTS.STATUS_MAP.checkVoided) {
          return false;
        }
      }
    }
    return true;
  };

  getClaimReopenType = (claimObject) => {
    if (this.state.initialStatus === CONSTANTS.STATUS_MAP.authorization ||
        this.state.initialStatus === CONSTANTS.STATUS_MAP.checkVoided) {
      return 'reopen';
    }
    if (claimObject.child_claims && claimObject.child_claims.length > 0) {
      for (let index = 0; index < claimObject.child_claims.length; index++) {
        if (claimObject.child_claims[index]['status'] === CONSTANTS.STATUS_MAP.authorization || claimObject.child_claims[index]['status'] === CONSTANTS.STATUS_MAP.checkVoided) {
          return 'reopenChild';
        }
      }
    }
    return 'createChild';
  };

  getReopenChildClaimId = (claimObject) => {
    if (claimObject.child_claims && claimObject.child_claims.length > 0) {
      for (let index = 0; index < claimObject.child_claims.length; index++) {
        if (claimObject.child_claims[index]['status'] === CONSTANTS.STATUS_MAP.authorization || claimObject.child_claims[index]['status'] === CONSTANTS.STATUS_MAP.checkVoided) {
          return claimObject.child_claims[index]['id'];
        }
      }
    }
  };

  onChildClaimUpdate = (childClaimId, status) => {
    const childClaimIndex = this.state.gap_claim.cursor(['child_claims']).deref().findIndex(c => c.toJS().id === childClaimId);
    if (childClaimIndex !== -1) {
      this.state.gap_claim.cursor(['child_claims', childClaimIndex, 'status']).update(() => status);
    }
    this.setState({ updateTriggered: false });
  };

  renderClaimDetails = (claimObject, isDisabled) => {
    let nextButtonText = '';
    let nextButtonDisable = false;
    let nextButtonClassName = '';
    let nextButtonOnClick;
    if (((this.state.initialStatus === CONSTANTS.STATUS_MAP.authorization ||
        this.state.initialStatus === CONSTANTS.STATUS_MAP.checkWritten ||
        this.state.initialStatus === CONSTANTS.STATUS_MAP.checkVoided) && !claimObject.child_claims) ||
      (claimObject.child_claims && this.canReopenChildClaims(claimObject) &&
      (this.state.initialStatus === CONSTANTS.STATUS_MAP.authorization ||
        this.state.initialStatus === CONSTANTS.STATUS_MAP.checkWritten ||
        this.state.initialStatus === CONSTANTS.STATUS_MAP.checkVoided))) {
      nextButtonText = 'Reopen';
      nextButtonDisable = false;
      nextButtonClassName = "";
      nextButtonOnClick = this.handleReopenClaim;
    } else if ((userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaims) && !userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaimsManager) &&
        claimObject.child_claims === false && isDisabled === true)) {
      nextButtonText = 'Update';
      nextButtonDisable = true;
      nextButtonClassName = "fa-check";
      nextButtonOnClick = this.handleUpdateSubmit;
    } else if (userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaimsManager) &&
      ((claimObject.child_claims === false &&
        (this.state.initialStatus === CONSTANTS.STATUS_MAP.authorization ||
          this.state.initialStatus === CONSTANTS.STATUS_MAP.checkWritten ||
          this.state.initialStatus === CONSTANTS.STATUS_MAP.checkVoided ||
          this.state.initialStatus === CONSTANTS.STATUS_MAP.denied)))) {
      nextButtonText = 'Update';
      nextButtonDisable = true;
      nextButtonClassName = "fa-check";
      nextButtonOnClick = this.handleUpdateSubmit;
    } else if ((userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaims) && !userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaimsManager) &&
        ((claimObject.child_claims === false &&
          (this.state.initialStatus === CONSTANTS.STATUS_MAP.waitingForAuthorization ||
            this.state.initialStatus === CONSTANTS.STATUS_MAP.pendingDenial ||
            this.state.initialStatus === CONSTANTS.STATUS_MAP.denied ||
            this.state.initialStatus === CONSTANTS.STATUS_MAP.closedNoResponse ||
            this.state.initialStatus === CONSTANTS.STATUS_MAP.noGap ||
            this.state.initialStatus === CONSTANTS.STATUS_MAP.authorization ||
            this.state.initialStatus === CONSTANTS.STATUS_MAP.checkVoided ||
            this.state.initialStatus === CONSTANTS.STATUS_MAP.checkWritten))))) {
      nextButtonText = 'Update';
      nextButtonDisable = true;
      nextButtonClassName = "fa-check";
      nextButtonOnClick = this.handleUpdateSubmit;
    } else if ((userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaimsManager)) &&
      (claimObject.status === CONSTANTS.STATUS_MAP.authorization)) {
      nextButtonText = 'Submit';
      nextButtonDisable = false;
      nextButtonClassName = "";
      if (this.calculateCaseReserveAmount(claimObject) <= 0) {
        nextButtonOnClick = this.handleNoGapSubmit;
      } else {
        nextButtonOnClick = this.handleUpdateSubmit;
      }
    } else if ((userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaimsManager))) {
      nextButtonText = 'Update';
      nextButtonDisable = false;
      nextButtonClassName = "fa-check";
      nextButtonOnClick = this.handleUpdateSubmit;
    } else {
      nextButtonText = 'Update';
      nextButtonDisable = false;
      nextButtonClassName = "fa-check";
      nextButtonOnClick = this.handleUpdateSubmit;
    }
    let renderAssignOwnerButton = false;
    if (userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaims) &&
      (this.state.initialStatus === CONSTANTS.STATUS_MAP.inquiry ||
        this.state.initialStatus === CONSTANTS.STATUS_MAP.pending ||
        this.state.initialStatus === CONSTANTS.STATUS_MAP.returnedForCorrection ||
        this.state.initialStatus === CONSTANTS.STATUS_MAP.readyToProcess)) {
      renderAssignOwnerButton = true;
    }
    return (
      <Header
        pageTitle="GAP Claim"
        isFinanceUser={ this.isFinanceUser() }
        claimObject={ claimObject }
        nextButtonClassName={ nextButtonClassName }
        nextButtonText={ nextButtonText }
        nextButtonOnClick={ nextButtonOnClick }
        backButtonOnClick={ this.handleBackButtonOnClick }
        nextButtonDisabled={ nextButtonDisable || this.state.submitting}
        showHistoryButtonClick={ this.onShowHistoryButtonClick }
        showVoidCheckButton={ userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaimsManager) && this.state.initialStatus === CONSTANTS.STATUS_MAP.checkWritten && !(claimObject.is_cs_claim && !claimObject.not_paid_by_cs) }
        handleOnClickVoidCheck={ () => {
          if (claimObject.status === CONSTANTS.STATUS_MAP.checkWritten || claimObject.status === CONSTANTS.STATUS_MAP.checkVoided) {
            this.setState({ showVoidCheckConfirmationModal: true });
          }
        } }
        renderAssignOwnerButton={ renderAssignOwnerButton }
        assignOwnerButtonClick={ this.onAssignOwnerButtonClick }
        hasAttachment={ this.hasAttachment }
        getCaseReserveAmount={ this.getCaseReserveAmount.bind(this, claimObject) }
        enableRecoveryCase = { this.state.enableRecoveryCase }
        claimStatus={ this.state.initialStatus }
        user={this.props.user}/>
    );
  };

  selectBankDetails = (data) => {
    this.state.gap_claim.update(oldValue => oldValue.merge(Immutable.fromJS({
      bank_account_name: data.name,
      bank_address_street1: data.address1,
      bank_address_street2: data.address2,
      bank_address_zip: data.zip,
      bank_address_city: data.city,
      bank_address_state: data.state,
      bank_vendor_id: data.vendor_id
    })));
    this.setState({ isWorksheetUpdated: true, displayBankSelectionModal: false });
  };

  addRemoveCustomRow = (id, action) => {
    const claimObject = this.state.gap_claim.toJS();
    const customRowsFieldMap = this.state.customRowsFieldMap.slice(0);
    let index;
    if (action === 'ADD') {
      /** The index starts from 1 because first custom input field will always be there */
      for (index = 1; index < customRowsFieldMap.length; index++) {
        if (parseFloat(claimObject[customRowsFieldMap[index].value]) === 0) {
          customRowsFieldMap[index].show = true;
          const customRow = customRowsFieldMap.splice(index, 1);
          customRowsFieldMap.push(customRow[0]);
          this.setState({ customRowsFieldMap });
          break;
        }
      }
    } else if (action === 'REMOVE') {
      for (index = 1; index < customRowsFieldMap.length; index++) {
        if (customRowsFieldMap[index].id === id) {
          customRowsFieldMap[index].show = false;
          claimObject [`other_label${id}`] = 'Other';
          claimObject[`other_value${id}`] = 0;
          this.setState({
            customRowsFieldMap
          }, () => {
            this.state.gap_claim.update(() => Immutable.fromJS(claimObject));
          });
        }
      }
    }
  };

  // enable recovery case when recovery status is not empty
  enableRecoveryCase = () => {
    this.setState({
      enableRecoveryCase: true
    });
  };

  renderChildClaim = (claimObject, isFinanceUser) => {
    if (claimObject.child_claims && claimObject.child_claims.length > 0) {
      return claimObject.child_claims.map((element, index) => {
        return (
          <ChildClaimDetails claimObject={ claimObject }
            key={ element.id }
            index={ index }
            childClaimId={ element.id }
            status={ element.status }
            updateTriggered={ this.state.updateTriggered }
            onUpdate={ this.onChildClaimUpdate }
            isFinanceUser={ isFinanceUser }
            userHasRole={ userHasRole }
            user={ this.props.user }
            statusMap={ CONSTANTS.STATUS_MAP }
            showPaymentLoader={ this.state.showChildPaymentLoader }
            attachments={ this.state.attachments }
            notes={ this.state.notes }
            getFieldNotes={ this.getFieldNotes }
            getFieldDocs={ this.getFieldDocs }
            selectedFieldNote={ this.state.selectedFieldNote }
            handleItemNoteClick={ this.handleFieldNoteClick }
            handleMouseEnter={ this.handleMouseEnter }
            handleMouseLeave={ this.handleMouseLeave }
            handleAttachmentClick={ this.handleAttachmentClick }
            deleteAttachment={ this.deleteAttachment }
            onCSChildClaimCheckAttachmentClose={ this.onCSChildClaimCheckAttachmentClose }
            showCSCheckAttachmentModal={ this.state.showCSCheckAttachmentModal }
            rowType={ CONSTANTS.FIELD_TYPE.static }
            rowStyle={ style.rowStyle }
            getCaseReserve={this.getCaseReserveAmount}/>
        );
      });
    }
  };

  /**
   * This function will change showNotes state which will execute load notes function
   * @param notesType - Type of notes "contract" or "claim"
   * @param e
   */

  onShowHistoryButtonClick = (notesType, e) => {
    let notesURL;
    let noteTitle = "";
    let noteType = "";
    let hideFieldNoteTextArea;
    const { gap_claim } = this.state;
    const gc = gap_claim.toJS();
    if (notesType === "contract") {
      notesURL = `${apiUrls.contract}/${gc.contract_number}/notes`;
      noteTitle = "Contract Notes";
      hideFieldNoteTextArea = true;
      noteType = "contract";
    } else if (notesType === "claim") {
      notesURL = apiUrls.gapClaimNotes;
      noteTitle = "Claim History";
      hideFieldNoteTextArea = false;
    } else if (notesType === "recovery") {
      notesURL = apiUrls.gapClaimRecoveryCaseNotes;
      noteTitle = "Recovery Notes";
      hideFieldNoteTextArea = true;
      noteType = 'RECOVERY_CASE';
    }
    this.setState({
      showNotes: true,
      notesURL,
      updateRecordNotes: true,
      noteTitle,
      noteType,
      selectedFieldNote: {
        itemNoteOffset: '',
        rowType: '',
        isSelected: false,
        contractDetails: {},
        fieldId: '',
        fieldNotes: {}
      },
      hideFieldNoteTextArea,
      fieldNoteHovered: false,
      fieldNoteClicked: false
    }, () => {
      scrollTo(0, 0);
    });
  };

  showRecordNotes = (isFinanceUser) => {
    const gc = this.state.gap_claim.toJS();
    //TODO: check for gap contract notes
    const location = { "id": this.props.location.query.id };
    if (this.state.showNotes && !this.state.selectedFieldNote.isSelected) {
      if (this.state.notesURL === apiUrls.gapContractNotes) {
        location["id"] = gc.contract_number;
      }
      return (
        <RecordNotes location={ location }
          updateRecordNotes={ this.state.updateRecordNotes }
          isFinanceUser={ isFinanceUser }
          onRecordNotesUpdate={ () => {
            this.setState({ updateRecordNotes: false });
          } }
          type={ this.state.noteType }
          apiURL={ this.state.notesURL }
          hideAddNoteTextArea={ (this.state.hideFieldNoteTextArea || isFinanceUser) && !userHasRole(this.props.user, CONSTANTS.USER_ROLES.viewOnlyClaims) }
          hasTitle={ true }
          hasCloseButton={ true }
          title={ this.state.noteTitle }
          containerClass="col-6"
          notesStyle={ style.notesStyle }
          closeButtonCallback={ this.closeRecordNote }/>
      );
    } else if (this.state.showNotes && this.state.selectedFieldNote.isSelected &&
      this.state.selectedFieldNote.rowType === CONSTANTS.FIELD_TYPE.static) {
      return (
        <RecordNotes updateRecordNotes={ this.state.updateRecordNotes }
          location={ location }
          onRecordNotesUpdate={ this.loadNotes }
          isFinanceUser={ isFinanceUser }
          apiURL={ apiUrls.fieldNotes }
          hideAddNoteTextArea={ (this.state.hideFieldNoteTextArea || isFinanceUser) && !userHasRole(this.props.user, CONSTANTS.USER_ROLES.viewOnlyClaims) }
          type={ this.state.selectedFieldNote.rowType }
          excludePagination={ true }
          recordNotes={ this.getFieldNotes(this.state.notes, this.state.selectedFieldNote.fieldId) }
          selectedFieldNote={ this.state.selectedFieldNote }
          hasCloseButton={ !this.state.fieldNoteHovered && true }
          containerClass="col-6"
          notesStyle={ style.notesStyle }
          closeButtonCallback={ this.closeRecordNote }
          worksheetOffset={ this.state.worksheetOffset }
          eventType={ this.state.noteEventType }/>
      );
    } else if (this.state.showNotes && this.state.selectedFieldNote.isSelected &&
      this.state.selectedFieldNote.rowType === CONSTANTS.FIELD_TYPE.dynamic) {
      return (
        <RecordNotes updateRecordNotes={ this.state.updateRecordNotes }
          location={ location }
          isFinanceUser={ isFinanceUser }
          onRecordNotesUpdate={ this.loadNotes }
          apiURL={ apiUrls.dynamicFieldNotes }
          hideAddNoteTextArea={ (this.state.hideFieldNoteTextArea || isFinanceUser) && !userHasRole(this.props.user, CONSTANTS.USER_ROLES.viewOnlyClaims) }
          type={ this.state.selectedFieldNote.rowType }
          excludePagination={ true }
          recordNotes={ this.getContractFieldNotes(this.state.notes, this.state.selectedFieldNote.contractDetails) }
          selectedFieldNote={ this.state.selectedFieldNote }
          hasCloseButton={ !this.state.fieldNoteHovered && true }
          containerClass="col-6"
          notesStyle={ style.notesStyle }
          closeButtonCallback={ this.closeRecordNote }
          worksheetOffset={ this.state.worksheetOffset }
          eventType={ this.state.noteEventType }/>
      );
    }
  };

  getFieldNotes = (claimObject, id) => {
    if (claimObject.field_notes && claimObject.field_notes.length > 0) {
      for (let index = 0; index < claimObject.field_notes.length; index++) {
        if (claimObject.field_notes[index]['field_id'] === id) {
          return claimObject.field_notes[index];
        }
      }
    }
  };

  getContractFieldNotes = (claimObject, contractDetails) => {
    if (claimObject.contract_notes && claimObject.contract_notes.length > 0) {
      for (let index = 0; index < claimObject.contract_notes.length; index++) {
        if (claimObject.contract_notes[index]['contract_number'] === contractDetails.contractNumber &&
          claimObject.contract_notes[index]['contract_code'] === contractDetails.contractCode) {
          return claimObject.contract_notes[index]['contract_field_notes'];
        }
      }
    }
  };

  getFieldDocs = (claimObject, id) => {
    if (claimObject.field_documents && claimObject.field_documents.length > 0) {
      for (let index = 0; index < claimObject.field_documents.length; index++) {
        if (claimObject.field_documents[index]['field_id'] === id && claimObject.field_documents[index]['documents'].length > 0) {
          return claimObject.field_documents[index]['documents'];
        }
      }
    }
  };

  handleFieldNoteClick = (params) => {
    this.setState({
      selectedFieldNote: params,
      showNotes: true,
      hideFieldNoteTextArea: false,
      fieldNoteHovered: false,
      fieldNoteClicked: true,
      updateRecordNotes: false,
      worksheetOffset: this.worksheet.offsetHeight,
      noteEventType: "click"
    }, () => {
      scrollTo(0, this.state.selectedFieldNote.itemNoteOffset);
    });
  };

  handleMouseEnter = (params) => {
    if (!this.state.fieldNoteClicked) {
      this.setState({
        selectedFieldNote: params,
        showNotes: true,
        hideFieldNoteTextArea: true,
        fieldNoteHovered: true,
        updateRecordNotes: false,
        worksheetOffset: this.worksheet.offsetHeight,
        noteEventType: "hover"
      });
    }
  };

  handleMouseLeave = () => {
    if (!this.state.fieldNoteClicked && this.state.fieldNoteHovered) {
      this.setState({
        selectedFieldNote: {
          itemNoteOffset: '',
          rowType: '',
          isSelected: false,
          contractDetails: {},
          fieldId: '',
          fieldNotes: {}
        },
        worksheetOffset: 0,
        showNotes: false,
        fieldNoteHovered: false,
        updateRecordNotes: false,
        noteEventType: ""
      });
    }
  };

  closeRecordNote = () => {
    this.setState({
      selectedFieldNote: {
        itemNoteOffset: '',
        rowType: '',
        isSelected: false,
        contractDetails: {},
        fieldId: '',
        fieldNotes: {}
      },
      worksheetOffset: 0,
      showNotes: false,
      hideFieldNoteTextArea: true,
      fieldNoteHovered: false,
      fieldNoteClicked: false,
      updateRecordNotes: false,
      noteEventType: ""
    });
  };

  isFinanceUser = () => {
    return (userHasRole(this.props.user, CONSTANTS.USER_ROLES.accounting) || userHasRole(this.props.user, CONSTANTS.USER_ROLES.viewOnlyClaims))  &&
      !userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaimsManager)
          && !userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaims);
  };

  isFormDisabled = () => {
    if ((userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaims) && !userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaimsManager) &&
        (this.state.initialStatus === CONSTANTS.STATUS_MAP.waitingForAuthorization || this.state.initialStatus === CONSTANTS.STATUS_MAP.pendingDenial)) ||
         this.isFinanceUser()
    ) {
      return true;
    }
    return (this.state.initialStatus === CONSTANTS.STATUS_MAP.authorization ||
      this.state.initialStatus === CONSTANTS.STATUS_MAP.checkWritten ||
      this.state.initialStatus === CONSTANTS.STATUS_MAP.checkVoided ||
      this.state.initialStatus === CONSTANTS.STATUS_MAP.denied ||
      this.state.initialStatus === CONSTANTS.STATUS_MAP.closedNoResponse ||
      this.state.initialStatus === CONSTANTS.STATUS_MAP.noGap) || false;
  };

  showManagerCheckbox = () => {
    return (!!(userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaimsManager) &&
      (this.state.initialStatus === CONSTANTS.STATUS_MAP.waitingForAuthorization ||
        this.state.initialStatus === CONSTANTS.STATUS_MAP.pendingDenial)));
  };

  handleAttachmentClick = (attachmentParams) => {
    if (!this.isFinanceUser()) {
      this.setState({
        displayAttachmentModal: true,
        attachmentParams
      });
    }
  };

  closeAttachmentModal = (claimObject, isSuccess, isFailure) => {
    this.setState({
      displayAttachmentModal: false,
      attachmentParams: {
        title: '',
        fieldId: '',
        rowType: '',
        contractCode: '',
        contractNumber: ''
      }
    }, () => {
      if (isSuccess) {
        this.loadAttachments(this.onAttachmentReload.bind(this, 'add', claimObject));
      }
      if (isFailure) {
        Alert.error("Click the browser's Refresh button to reload. If the error continues, contact your system administrator.");
      }
    });
  };

  onAttachmentReload = (type, claimObject) => {
    this.setState({ showLoader: false }, () => {
      if (type === 'add') {
        if (claimObject && claimObject.status === CONSTANTS.STATUS_MAP.inquiry) {
          this.setState({ isWorksheetUpdated: true }, () => {
            this.handleUpdateSubmit();
          });
        }
        Alert.success("Document uploaded successfully.");
      } else {
        Alert.success("Document deleted successfully.");
      }
    });
  };

  deleteAttachment = (id) => {
    this.setState({ showLoader: true }, () => {
      ajax(`${apiUrls.document}/${id}`, {}, { method: 'DELETE' }, (data, status) => {
        if (status === 200) {
          this.setState({ showLoader: false }, () => {
            this.loadAttachments(this.onAttachmentReload.bind(this, 'delete'));
          });
        } else {
          this.setState({ showLoader: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  calculateAmortization = (loanHistory) => {
    this.state.gap_claim.update(oldValue => oldValue.merge(Immutable.fromJS({
      interest_rate: loanHistory.interest_rate,
      contract_deal_date: loanHistory.contract_deal_date,
      contract_term_months: loanHistory.contract_term_months,
      original_financing_contract_value: loanHistory.original_financing_contract_value,
      payment_amount: loanHistory.payment_amount,
      first_payment_date: loanHistory.first_payment_date,
      total_delinquent_payments_covered: loanHistory.total_delinquent_payments_covered,
      total_extensions_covered: loanHistory.total_extensions_covered,
      calculate_amortization_value: true
    })));
    this.setState({ displayAmortizationModal: false, isWorksheetUpdated: true }, () => {
      this.handleUpdateSubmit();
    });
  };

  updateAmortization = (newAmortization) => {
    this.state.gap_claim.update(oldValue => oldValue.merge(Immutable.fromJS({
      run_amortization_sheet_value: newAmortization.value,
      calculate_amortization_value: false
    })));
    this.setState({ displayAmortizationModal: false, isWorksheetUpdated: true }, () => {
      this.handleUpdateSubmit();
      const data = {
        "gap_claim_id": parseInt(this.props.location.query.id),
        "field_id": CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP['runAmortizationSheet']['id'],
        "notes_text": newAmortization.reason
      };
      ajax(apiUrls.fieldNotes, data, { method: 'POST' }, (data, status) => {
        if (status !== 200) {
          Alert.error("Failed to record note for manual amortization. If the error continues, contact your system administrator.");
        }
      });
    });
  };

  renderCSCheckAttachment = (claimObject, isFinanceUser) => {
    let isCheckAttachmentDisabled = true;
    if (claimObject.child_claims && this.hasAuthorizedChild(claimObject)) {
      isCheckAttachmentDisabled = false;
    } else if (this.state.initialStatus === CONSTANTS.STATUS_MAP.authorization) {
      isCheckAttachmentDisabled = false;
    }
    if (claimObject.is_cs_claim && !claimObject.not_paid_by_cs && (this.state.initialStatus === CONSTANTS.STATUS_MAP.authorization ||
        this.state.initialStatus === CONSTANTS.STATUS_MAP.checkWritten ||
        this.state.initialStatus === CONSTANTS.STATUS_MAP.checkVoided)) {
      return (
        <div className="col-4 ml-5">
          <button className="btn btn-primary mr-2 cursor-pointer" onClick={ (e) => {
            e.preventDefault();
            this.setState({ showCSCheckAttachmentModal: true });
          } } disabled={ isCheckAttachmentDisabled }>
            <i className="fa fa-paperclip"/>&nbsp;Attach Check
          </button>
          <div>
            <DocumentLinkList
              fieldDocs={ this.getFieldDocs(this.state.attachments, CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP['csCheck']['id']) }
              deleteAttachment={ this.deleteAttachment }
              isFinanceUser={ isFinanceUser }/>
            {this.renderCSCheckChildClaimsAttachments(claimObject, isFinanceUser)}
          </div>
        </div>
      );
    }
  };

  renderCSCheckChildClaimsAttachments = (claimObject, isFinanceUser) => {
    let documents = [];
    if (claimObject.child_claims && claimObject.child_claims.length > 0) {
      for (let index = 0; index < claimObject.child_claims.length; index++) {
        documents.push(
          <DocumentLinkList
            key={ index }
            fieldDocs={ this.getFieldDocs(this.state.attachments, (index * 100) + CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP['csChildCheck']['id']) }
            deleteAttachment={ this.deleteAttachment }
            isFinanceUser={ isFinanceUser }/>
        );
      }
    }
    return documents;
  };

  hasAuthorizedChild = (claimObject) => {
    if (claimObject.child_claims && claimObject.child_claims.length > 0) {
      for (let index = 0; index < claimObject.child_claims.length; index++) {
        if (claimObject.child_claims[index]['status'] === CONSTANTS.STATUS_MAP.authorization) {
          return true;
        }
      }
    }
    return false;
  };

  onCSCheckAttachmentClose = (type) => {
    this.setState({ showCSCheckAttachmentModal: false }, () => {
      if (type === 'success') {
        this.setState({ isWorksheetUpdated: true }, () => {
          this.handleUpdateSubmit();
        });
        this.loadAttachments();
      }
      if (type === 'error') {
        Alert.error("Click the browser's Refresh button to reload. If the error continues, contact your system administrator.");
      }
    });
  };

  onCSChildClaimCheckAttachmentClose = (type) => {
    this.setState({ showCSCheckAttachmentModal: false }, () => {
      if (type === 'success') {
        this.loadAttachments();
      }
      if (type === 'error') {
        Alert.error("Click the browser's Refresh button to reload. If the error continues, contact your system administrator.");
      }
    });
  };

  renderPaidByCSCICheckbox = (gc, isFinanceUser) => {
    if (!gc.is_cs_claim) {
      return;
    }
    const isDisabled = (this.state.initialStatus === CONSTANTS.STATUS_MAP.authorization ||
      this.state.initialStatus === CONSTANTS.STATUS_MAP.checkWritten ||
      this.state.initialStatus === CONSTANTS.STATUS_MAP.checkVoided ||
      this.state.initialStatus === CONSTANTS.STATUS_MAP.denied ||
      this.state.initialStatus === CONSTANTS.STATUS_MAP.closedNoResponse ||
      this.state.initialStatus === CONSTANTS.STATUS_MAP.waitingForAuthorization);
    return (<InputBoxRow labelContainerClass="col-5"
      containerClass="justify-content-end"
      hasLabel={ true }
      hasCheckbox={ true }
      isFinanceUser={ isFinanceUser }
      checkBoxAttribute="not_paid_by_cs"
      showManagerCheckbox={ false }
      isDisabled={ isDisabled }
      label="CSCI not paying"
      claimObject={ gc }
      onCursorToggle={ this.onCursorToggle }
      rowId={ CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP['isPaidByCS']['rowId'] }
      fieldId={ CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP['isPaidByCS']['id'] }
      fieldNotes={ this.getFieldNotes(this.state.notes, CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP['isPaidByCS']['id']) }
      fieldDocs={ this.getFieldDocs(this.state.attachments, CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP['isPaidByCS']['id']) }
      selectedFieldNote={ this.state.selectedFieldNote }
      handleItemNoteClick={ this.handleFieldNoteClick }
      handleMouseEnter={ this.handleMouseEnter }
      handleMouseLeave={ this.handleMouseLeave }
      handleAttachmentClick={ this.handleAttachmentClick }
      deleteAttachment={ this.deleteAttachment }
      rowType={ CONSTANTS.FIELD_TYPE.static }
      rowStyle={ style.rowStyle }/>);
  };

  render() {
    const gc = this.state.gap_claim.toJS();
    const isDisabled = this.isFormDisabled();
    const spinnerMessage = <p className='text-center'><i className="fa fa-refresh fa-spin"/> Loading...</p>;
    const showManagerCheckbox = this.showManagerCheckbox();
    const isFinanceUser = this.isFinanceUser();
    return (
      <Loader show={ this.state.showLoader } message={ spinnerMessage }>
        <section className="claim-worksheet">
          {this.renderClaimDetails(gc, isDisabled)}
          <section className="mt-3 pt-3">
            <section className="row mt-1">
              <section className="col-12" id="parentDiv" ref={ (worksheet) => this.worksheet = worksheet }>
                <form>
                  <fieldset>
                    <label className="form-check form-check-label col-form-label col-form-label-sm">
                      <input type="checkbox"
                        className="form-check-input"
                        id="inProgress_checkBox"
                        checked={ gc.is_in_progress }
                        onChange={ this.onCursorToggle.bind(null, 'is_in_progress') }
                        disabled={ isDisabled }/>
                      <span>In Progress</span>
                    </label>
                    <InputBoxRow labelContainerClass="col-3"
                      inputBoxContainerClass="col-2"
                      isFinanceUser={ isFinanceUser }
                      hasLabel={ true }
                      onLabelClick={ () => {
                        this.setState({ displayAmortizationModal: true });
                      } }
                      hasCheckbox={ true }
                      hasInputBox={ true }
                      checkBoxAttribute="has_run_amortization_sheet"
                      label="Run amortization "
                      inputBoxType="Currency"
                      inputBoxAttribute="run_amortization_sheet_value"
                      managerCheckBoxAttribute="run_amortization_manager_flag"
                      showManagerCheckbox={ showManagerCheckbox }
                      isDisabled={ isDisabled }
                      hasDefaultValue={ true }
                      claimObject={ gc }
                      onCursorToggle={ this.onCursorToggle }
                      onInputBoxChange={ this.onCursorValueChange }
                      onInputBoxBlur={ this.onCursorValueChange }
                      rowId={ CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP['runAmortizationSheet']['rowId'] }
                      fieldId={ CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP['runAmortizationSheet']['id'] }
                      fieldNotes={ this.getFieldNotes(this.state.notes, CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP['runAmortizationSheet']['id']) }
                      fieldDocs={ this.getFieldDocs(this.state.attachments, CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP['runAmortizationSheet']['id']) }
                      selectedFieldNote={ this.state.selectedFieldNote }
                      handleItemNoteClick={ this.handleFieldNoteClick }
                      handleMouseEnter={ this.handleMouseEnter }
                      handleMouseLeave={ this.handleMouseLeave }
                      handleAttachmentClick={ this.handleAttachmentClick }
                      deleteAttachment={ this.deleteAttachment }
                      rowType={ CONSTANTS.FIELD_TYPE.static }
                      rowStyle={ style.rowStyle }/>
                    <CancelContracts claimObject={ gc }
                      isFinanceUser={ isFinanceUser }
                      onCursorToggle={ this.onCursorToggle }
                      showManagerCheckbox={ showManagerCheckbox }
                      onCursorValueChange={ this.onCursorValueChange }
                      onCursorChange={ this.onCursorChange }
                      addRemoveCustomRow={ this.addRemoveCustomRow }
                      isDisabled={ isDisabled }
                      customRowsFieldMap={ this.state.customRowsFieldMap }
                      getFieldNotes={ this.getFieldNotes }
                      getFieldDocs={ this.getFieldDocs }
                      getContractFieldNotes={ this.getContractFieldNotes }
                      attachments={ this.state.attachments }
                      notes={ this.state.notes }
                      selectedFieldNote={ this.state.selectedFieldNote }
                      handleItemNoteClick={ this.handleFieldNoteClick }
                      handleMouseEnter={ this.handleMouseEnter }
                      handleMouseLeave={ this.handleMouseLeave }
                      handleAttachmentClick={ this.handleAttachmentClick }
                      deleteAttachment={ this.deleteAttachment }
                      rowType={ CONSTANTS.FIELD_TYPE.static }
                      rowStyle={ style.rowStyle }/>
                    <ValuationReport claimObject={ gc }
                      addInsuranceCompanyName={ this.addInsuranceCompanyName }
                      isFinanceUser={ isFinanceUser }
                      showManagerCheckbox={ showManagerCheckbox }
                      isDisabled={ isDisabled }
                      onCursorToggle={ this.onCursorToggle }
                      onCursorValueChange={ this.onCursorValueChange }
                      handleDateChange={ this.handleDateChange }
                      onCursorChange={ this.onCursorChange }
                      dateFormat={ dateFormat.displayDateFormat }
                      getFieldNotes={ this.getFieldNotes }
                      getFieldDocs={ this.getFieldDocs }
                      attachments={ this.state.attachments }
                      notes={ this.state.notes }
                      selectedFieldNote={ this.state.selectedFieldNote }
                      handleItemNoteClick={ this.handleFieldNoteClick }
                      handleMouseEnter={ this.handleMouseEnter }
                      handleMouseLeave={ this.handleMouseLeave }
                      handleAttachmentClick={ this.handleAttachmentClick }
                      deleteAttachment={ this.deleteAttachment }
                      rowType={ CONSTANTS.FIELD_TYPE.static }
                      rowStyle={ style.rowStyle }/>
                    <BankInformation claimObject={ gc }
                      isFinanceUser={ isFinanceUser }
                      showManagerCheckbox={ showManagerCheckbox }
                      onCursorToggle={ this.onCursorToggle }
                      onCursorValueChange={ this.onCursorValueChange }
                      onCursorChange={ this.onCursorChange }
                      loadBankSelectionModal={ () => {
                        this.setState({ displayBankSelectionModal: true });
                      } }
                      handleDateChange={ this.handleDateChange }
                      isDisabled={ isDisabled }
                      displayBankSelectionModal={ this.state.displayBankSelectionModal }
                      dateFormat={ dateFormat.displayDateFormat }
                      attachments={ this.state.attachments }
                      notes={ this.state.notes }
                      getFieldNotes={ this.getFieldNotes }
                      getFieldDocs={ this.getFieldDocs }
                      selectedFieldNote={ this.state.selectedFieldNote }
                      handleItemNoteClick={ this.handleFieldNoteClick }
                      handleMouseEnter={ this.handleMouseEnter }
                      handleMouseLeave={ this.handleMouseLeave }
                      handleAttachmentClick={ this.handleAttachmentClick }
                      deleteAttachment={ this.deleteAttachment }
                      rowType={ CONSTANTS.FIELD_TYPE.static }
                      rowStyle={ style.rowStyle }/>
                  </fieldset>
                  { this.renderPaidByCSCICheckbox(gc, isFinanceUser) }
                  <StatusDropdown claimObject={ gc }
                    statusType="status"
                    isFinanceUser={ isFinanceUser }
                    userHasRole={ userHasRole }
                    handleStatusChange={ this.handleStatusChange }
                    status={ this.state.status }
                    initialStatus={ this.state.initialStatus }
                    user={ this.props.user }
                    statusMap={ CONSTANTS.STATUS_MAP }
                    onCursorChange={ this.onCursorChange }
                    onCursorValueChange={ this.onCursorValueChange }
                    labelCustomClass='col-3'
                    inputCustomClass='col-2'
                    statusChangeDescription="status_change_description"
                    fieldType="parent"
                    isCsClaim={ (gc.is_cs_claim && !gc.not_paid_by_cs) }
                    attachments={ this.state.attachments }
                    notes={ this.state.notes }
                    getFieldNotes={ this.getFieldNotes }
                    getFieldDocs={ this.getFieldDocs }
                    selectedFieldNote={ this.state.selectedFieldNote }
                    handleItemNoteClick={ this.handleFieldNoteClick }
                    handleMouseEnter={ this.handleMouseEnter }
                    handleMouseLeave={ this.handleMouseLeave }
                    handleAttachmentClick={ this.handleAttachmentClick }
                    deleteAttachment={ this.deleteAttachment }
                    rowType={ CONSTANTS.FIELD_TYPE.static }
                    getCaseReserve={this.getCaseReserveAmount}
                    rowStyle={ style.rowStyle }/>
                  <RecoveryData claimObject={ gc }
                    isFinanceUser={ isFinanceUser }
                    getFieldNotes={ this.getFieldNotes }
                    getFieldDocs={ this.getFieldDocs }
                    loadNotes={ this.loadNotes }
                    attachments={ this.state.attachments }
                    notes={ this.state.notes }
                    selectedFieldNote={ this.state.selectedFieldNote }
                    handleItemNoteClick={ this.handleFieldNoteClick }
                    handleMouseEnter={ this.handleMouseEnter }
                    handleMouseLeave={ this.handleMouseLeave }
                    handleAttachmentClick={ this.handleAttachmentClick }
                    deleteAttachment={ this.deleteAttachment }
                    rowType={ CONSTANTS.FIELD_TYPE.static }
                    rowStyle={ style.rowStyle }
                    enableRecoveryCase = { this.enableRecoveryCase }
                  />
                  <PaymentInfo showLoader={ this.state.showParentPaymentLoader }
                    isFinanceUser={ isFinanceUser }
                    claimId={ gc.id }
                    status={ this.state.initialStatus }
                    statusMap={ CONSTANTS.STATUS_MAP }
                    claimObject={ gc }
                    fieldType="parent"
                    attachments={ this.state.attachments }
                    notes={ this.state.notes }
                    getFieldNotes={ this.getFieldNotes }
                    getFieldDocs={ this.getFieldDocs }
                    selectedFieldNote={ this.state.selectedFieldNote }
                    handleItemNoteClick={ this.handleFieldNoteClick }
                    handleMouseEnter={ this.handleMouseEnter }
                    handleMouseLeave={ this.handleMouseLeave }
                    handleAttachmentClick={ this.handleAttachmentClick }
                    deleteAttachment={ this.deleteAttachment }
                    rowType={ CONSTANTS.FIELD_TYPE.static }
                    rowStyle={ style.rowStyle }/>
                  {this.renderChildClaim(gc, isFinanceUser)}
                  {this.renderCSCheckAttachment(gc, isFinanceUser)}
                  <VoidCheck
                    showVoidCheckConfirmationModal={ this.state.showVoidCheckConfirmationModal }
                    claimObject={ gc }
                    onVoidCheckSuccess={ this.loadClaim }
                    initiateLoader={ () => {
                      this.setState({ showLoader: true });
                    } }
                    onVoidCheckError={ () => {
                      this.setState({ showLoader: false });
                    } }
                    onDeclineVoid={ () => {
                      this.setState({ showVoidCheckConfirmationModal: false });
                    } }/>
                </form>
                {this.showRecordNotes(isFinanceUser)}
              </section>
            </section>
          </section>
          <UserSelectionModal userList={ this.state.userList }
            displayModal={ this.state.displayModal }
            showUserListLoader={ this.state.showUserListLoader }
            userHasRole={ userHasRole }
            handleOwnerChange={ this.handleOwnerChange }
            handleModalSubmit={ this.updateClaimList }/>
          <AssignOwnerModal userList={ this.state.userList }
            displayModal={ this.state.displayAssignOwnerModal }
            showUserListLoader={ this.state.showUserListLoader }
            userRole={ this.state.userRole }
            userHasRole={ userHasRole }
            handleOwnerChange={ this.handleOwnerChange }
            handleModalSubmit={ this.displayUpdateConfirmation }
            closeModal={ this.cancelUpdate }/>
          <ConfirmUpdateModal displayModal={ this.state.displayUpdateConfirmationModal }
            customTextMessage={ `You are assigning ownership of this claim to ${this.state.newOwnerName}` }
            handleYesClick={ this.confirmUpdate }
            handleCloseClick={ this.cancelUpdate }/>
          <ConfirmationModal confirmButtonText="Yes"
            declineButtonText="No"
            displayConfirmationModal={ this.state.displayBackConfirmationModal }
            displayMessage="You have unsaved work, do you want to save it before continuing?"
            onConfirm={ this.handleUpdateSubmit }
            onDecline={ this.redirectToPrevious }/>
          <ConfirmationModal confirmButtonText="OK"
            declineButtonText="Cancel"
            displayConfirmationModal={ this.state.displayNoGapConfirmationModal }
            displayMessage="This claim is a No GAP claim and can not be processed for payment"
            onConfirm={ this.onNoGapSubmitConfirm }
            onDecline={ () => {
              this.setState({ displayNoGapConfirmationModal: false });
            } }/>
          <ConfirmationModal confirmButtonText="Save/Submit"
            declineButtonText="Cancel"
            displayConfirmationModal={ this.state.showAuthorizeConfirmationModal }
            displayMessage={ `You have unsaved changes that would change the case reserve to ${accounting.formatMoney(this.calculateCaseReserveAmount(gc), '$', 2)}. Do you want to save and continue to authorize. If not, please press cancel to return to the checklist` }
            onConfirm={ () => {
              this.setState({ showAuthorizeConfirmationModal: false }, () => {
                this.changeStatus(this.state.statusType, this.state.statusValue);
              });
            } }
            onDecline={ () => {
              this.setState({ showAuthorizeConfirmationModal: false });
            } }/>
          <AddInsuranceCompanyModal displayModal={ this.state.insuranceDisplayModal }
            onCloseModal={ this.onInsuranceModalClosed }/>
          <BankSelectionModal selectBankDetails={ this.selectBankDetails }
            closeBankSelectionModal={ () => {
              this.setState({ displayBankSelectionModal: false });
            } }
            displayBankSelectionModal={ this.state.displayBankSelectionModal }/>
          <ChildClaimModal displayChildClaim={ this.state.displayChildClaimModal }
            closeChildModal={ this.closeChildClaimModal }
            claimStatus={ this.state.initialStatus }
            statusMap={ CONSTANTS.STATUS_MAP }
            reopenType={ this.getClaimReopenType(gc) }
            reopenClaim={ this.reopenClaim }
            openChildClaim={ this.openChildClaim }
            userHasRole={ userHasRole }
            user={ this.props.user }/>
          <AttachmentModal displayModal={ this.state.displayAttachmentModal }
            closeModal={ this.closeAttachmentModal.bind(this, gc) }
            claimId={ gc.id }
            attachmentParams={ this.state.attachmentParams }/>
          <RunAmortizationModal displayModal={ this.state.displayAmortizationModal }
            closeModal={ () => {
              this.setState({ displayAmortizationModal: false });
            } }
            calculateAmortization={ this.calculateAmortization }
            updateAmortization={ this.updateAmortization }
            loanHistory={ gc }/>
          <CSCheckAttachmentModal closeModal={ this.onCSCheckAttachmentClose }
            displayModal={ this.state.showCSCheckAttachmentModal && (this.state.initialStatus === CONSTANTS.STATUS_MAP.authorization) }
            claimId={ gc.id }
            statusType={ "status" }
            isChildClaim={ false }
            checkAmountAttribute={ "cs_check_amount" }
            checkAmount={ gc.cs_check_amount }
            onCursorValueChange={ this.onCursorValueChange }
            changeStatus={ this.changeStatus }/>
          <div className="clearfix" />
        </section>
      </Loader>
    );
  }
}
