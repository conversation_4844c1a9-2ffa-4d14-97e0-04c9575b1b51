import React from "react";
import Alert from "react-s-alert";
import { URLS as apiUrls } from "../reusable/Utilities/urls";
import { json as ajax } from "./../../ajax.js";
import Loader from "react-loader-advanced";
import { addRecordNote } from "../reusable/RecordNotes/addRecordNotes.js";
import Modal from "../../Modal.jsx";
import InputBox from "../reusable/InputBox/InputBox.jsx";
import UploadFile from "../reusable/UploadFile/UploadFile.jsx";
import { CONSTANTS } from "./../reusable/Constants/constants.js";
import PropTypes from "prop-types";

export default class CSCheckAttachment extends React.Component {

  static propTypes = {
    displayModal: PropTypes.bool.isRequired,
    closeModal: PropTypes.func.isRequired,
    maxFileSizeInMBs: PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.number
    ]),
    isMultipleAllowed: PropTypes.bool,
    claimId: PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.number
    ]),
    onCursorValueChange: PropTypes.func,
    changeStatus: PropTypes.func,
    statusType: PropTypes.string.isRequired,
    checkAmount: PropTypes.string,
    checkAmountAttribute: PropTypes.string,
    isChildClaim: PropTypes.bool,
    index: PropTypes.number
  };

  constructor(props) {
    super(props);
    this.state = {
      isUploading: false,
      title: this.props.isChildClaim ? CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP['csChildCheck']['title'] : CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP['csCheck']['title'],
      attachmentParams: {}
    };
    this.handleAttachmentUpload = this.handleAttachmentUpload.bind(this);
    this.handleAttachCheck = this.handleAttachCheck.bind(this);
    this.onAddNoteSuccess = this.onAddNoteSuccess.bind(this);
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (nextProps.statusType !== this.props.statusType) {
      this.setState({
        title: nextProps.isChildClaim ? CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP['csChildCheck']['title'] : CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP['csCheck']['title'],
      });
    }
  }

  handleAttachmentUpload(fileData, fileName, fileExtension) {
    const params = {};
    if (this.props.isChildClaim) {
      params.field_id = (this.props.index * 100) + CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP['csChildCheck']['id'];
    } else {
      params.field_id = CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP['csCheck']['id'];
    }
    params.gap_claim_id = this.props.claimId;
    params.file_content = fileData;
    params.file_name = fileName;
    params.file_type = fileExtension;
    this.setState({
      attachmentParams: params
    });
  }

  handleAttachCheck(e) {
    e.preventDefault();
    if (this.state.attachmentParams.file_name) {
      this.setState({ isUploading: true }, function () {
        ajax(apiUrls.document, this.state.attachmentParams, { method: 'POST' }, (data, status) => {
          if (status === 200) {
            const noteText = `The file ${this.state.attachmentParams.file_name} was attached to the ${this.state.title}.`;
            this.props.changeStatus(this.props.statusType, "checkWritten");
            addRecordNote(this.props.claimId, noteText, this.onAddNoteSuccess);
          } else {
            this.setState({ isUploading: false }, function () {
              this.props.closeModal("error");
            });
          }
        });
      }.bind(this));
    } else {
      Alert.error("Add check attachment");
    }
  }

  onAddNoteSuccess() {
    this.setState({ isUploading: false }, function () {
      this.props.closeModal("success");
    });
  }

  render() {
    const spinnerMessage = <p className='text-center'><i className="fa fa-refresh fa-spin"/> Uploading...</p>;
    return (
      <Modal visible={this.props.displayModal}
        close={this.props.closeModal}
        size="large">
        <Loader show={this.state.isUploading} message={spinnerMessage}>
          <div>
            <p className="h5">{this.state.title}</p>
          </div>
          <div className="row">
            <label className="col-form-label col-form-label-sm col-3"> Check Amount: </label>
            <InputBox type="Currency"
              id="CSCheckAmount_inputBox"
              customClass="col-2"
              hasDefaultValue={ true }
              isDisabled={ false }
              value={ this.props.checkAmount }
              onChange={ this.props.onCursorValueChange.bind(null, this.props.checkAmountAttribute) }
              onBlur={ this.props.onCursorValueChange.bind(null, this.props.checkAmountAttribute) }/>
          </div>
          <UploadFile maxFileSizeInMBs={this.props.maxFileSizeInMBs}
            handleUpload={this.handleAttachmentUpload}
            isMultipleAllowed={this.props.isMultipleAllowed}
            closeModal={this.props.closeModal}/>
          <div className="row flex-row-reverse">
            <button className="btn btn-primary mr-3 cursor-pointer" onClick={this.handleAttachCheck}>Attach Check
            </button>
            <button className="btn btn-secondary mr-3 cursor-pointer" onClick={this.props.closeModal}>Cancel</button>
          </div>
        </Loader>
      </Modal>
    );
  }
}