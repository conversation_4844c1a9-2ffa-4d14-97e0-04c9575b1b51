var has = function(v, k) {
  if (!v || !v.Map) {
    return false;
  }
  return v.Map[k] && v.Map[k].Valid;
};

module.exports = {
  has: has,

  hasAny: function(v, keys) {
    if (!keys || keys.length === 0) {
      return true;
    }
    if (!v || !v.Map) {
      return false;
    }
    for (var i = 0; i < keys.length; i++) {
      if (has(v, keys[i])) {
        return true;
      }
    }
    return false;
  },

  toArray: function(hstore) {
    if (!hstore || !hstore.Map) {
      return [];
    }
    return Object.keys(hstore.Map).filter(function(v) {
      return hstore.Map[v].Valid;
    });
  }
};
