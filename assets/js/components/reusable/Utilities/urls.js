export const URLS = {
  'gapIntacct': '/api/vendors',
  'gapclaims': '/api/gapclaims',
  'gapClaimsCreateChildClaim': '/api/gapclaims/__claimId__/child',
  'gapGetChildClaim': '/api/gapclaims/__claimId__/child/__childClaimId__',
  'gapClaimsFinance': '/api/gapclaims/finance',
  'gapclaimsVoid': '/api/gapclaims/__claimId__/void',
  'gapClaimsCountStatus': '/api/gapclaims/count/status',
  'gapClaimsCountAgents': '/api/gapclaims/count/agent',
  'gapClaimsCountAge': '/api/gapclaims/count/age',
  'gapClaimsBatchAuthorize': '/api/gapclaims/authorize',
  'gapRecovery': '/api/gapclaims/recovery',
  'gapRecoveryDetails': '/api/gapclaims/__claimId__/recovery-details',
  'gapClaimPayment': '/api/gapclaims/__claimId__/payments',
  'gapClaimNotes': '/api/gapclaims/record-notes',
  'gapClaimRecovery': '/api/gapclaims/__claimId__/recovery',
  'gapClaimRecoveryCaseNotes': '/api/gapclaims/__claimId__/recovery-details/comments',
  'vtaclaims': '/api/vta-claims',
  'vtaClaimsCountStatus': '/api/vta-claims/count/status',
  'vtaClaimsCountAgents': '/api/vta-claims/count/agent',
  'vtaClaimsCountAge': '/api/vta-claims/count/age',
  'vtaClaimRecordNotes': '/api/vta-claims/record-notes',
  'vtaClaimContractNotes': '/api/vta-contract/notes',
  'vtaClaimDocuments': '/api/vta-claims/documents',
  'vtaClaimGetDocuments': '/api/vta-claims/__documentId__/documents',
  'vtaClaimFieldNotes': '/api/vta-claims/field-notes',
  'vtaClaimGetFieldNotes': '/api/vta-claims/__claimId__/field-notes',
  'vtaPayments': '/api/vta-claims/__claimId__/payments',
  'vtaClaimUpdateFromGap': '/api/vta-claims/__claimId__/gap',
  'vtaClaimVoid': '/api/vta-claims/__claimId__/void',
  'checkVTAContracts': '/api/vta-claims/contract',
  'checkLWTContracts': '/api/lwt-claims/contract',
  'insuranceCompanies': '/api/insurance-companies',
  'gapContractNotes': '/api/gapcontract/notes',
  'fieldNotes': '/api/gapclaims/field-notes',
  'getNotes': '/api/gapclaims/__claimId__/field-notes',
  'dynamicFieldNotes': '/api/gapclaims/contract-field-notes',
  'contract': '/api/contract',
  'contracts': '/api/contracts',
  'contractAttachment': '/api/contract/__contractId__/attachments/__attachmentId__',
  'gapContract': '/api/gapcontract',
  'checkContracts': '/api/gapclaims/contract',
  'templates': '/api/email-templates',
  'templatesReorder': '/api/email-templates-reorder',
  'customers': '/api/customers',
  'emailTemplate': '/api/gapclaims/__claimId__/email-body/__templateId__',
  'letter': '/api/gapclaims/__claimId__/letter',
  'letters': '/api/gapclaims/__claimId__/letters',
  'document': '/api/gapclaims/documents',
  'getDocument': '/api/gapclaims/__documentId__/documents',
  'facility': '/api/facilities',
  'facilityVendorUpdate': '/api/facilities/vendor',
  'facilityList': '/api/facilities',
  'roLookup': '/api/dms/ro',
  'automotiveClaims': '/api/automotive-claims',
  'automotiveClaimsNotes': '/api/automotive-claims/record-notes',
  'automotiveContractHistory': '/api/automotive-claims/claim-history',
  'automotiveClaimDocument': '/api/automotive-claims/documents',
  'autoClaimDocumentIndex': '/api/automotive-claims/__claimId__/documents',
  'autoClaimCCDocumentIndex': '/api/automotive-claims/__claimId__/ccdocuments',
  'autoClaimInspectionDocumentIndex': '/api/automotive-claims/__claimId__/inspection/documents',
  'automotiveComplaint': '/api/automotive-claims/__claimId__/complaints',
  'automotiveClaimReOpen': '/api/automotive-claims/__claimId__/reopen',
  'automotiveComplaintDelete': '/api/automotive-claims/complaints/__complaintId__',
  'automotiveParts': '/api/automotive-claims/__claimId__/complaints/__complaintId__/parts',
  'automotiveLabors': '/api/automotive-claims/__claimId__/complaints/__complaintId__/labors',
  'automotiveTowings': '/api/automotive-claims/__claimId__/complaints/__complaintId__/towings',
  'automotiveRentals': '/api/automotive-claims/__claimId__/complaints/__complaintId__/rentals',
  'automotiveSublets': '/api/automotive-claims/__claimId__/complaints/__complaintId__/sublets',
  'automotiveMiscs': '/api/automotive-claims/__claimId__/complaints/__complaintId__/miscs',
  'automotiveClaimsCountStatus': '/api/automotive-claims/count/status',
  'automotiveClaimsCountAgents': '/api/automotive-claims/count/agent',
  'automotiveClaimsCountAge': '/api/automotive-claims/count/age',
  'automotiveClaimsReassign': '/api/automotive-claims/reassign',
  'automotiveClaimsReassignManager': '/api/automotive-claims/reassign/manager',
  'automotiveClaimsReassignSelf': '/api/automotive-claims/reassign/self',
  'automotiveClaimsReassignAgent': '/api/automotive-claims/reassign/agent',
  'automotiveClaimsReassignAction': '/api/automotive-claims/reassign/action',
  'automotivePayee': '/api/automotive-claims/__claimId__/payee',
  'repairCodes': '/api/repair-codes/__code__',
  'zones': '/api/zones',
  'zoneUpdate': '/api/zones/__zoneId__',
  "zoneFacilities": '/api/zones/__zoneId__/facilities',
  'unassignedAgents': '/api/automotive-claims/unassigned-agents',
  'holdZone': '/api/zones/__zoneId__/hold',
  'unHoldZone': '/api/zones/__zoneId__/unhold',
  'gapBatchDetails': '/api/gapclaims/batch/__batchId__',
  'gapBatchHistory': '/api/gapclaims/batches',
  'userList': '/api/users',
  'usersPreApprovedLimits': '/api/users/pre-approved-limits',
  'gapClaimOwners': '/api/users/claim-owners/gap',
  'autoClaimOwners': '/api/users/claim-owners/auto',
  'vtaClaimOwners': '/api/users/claim-owners/vta',
  'lwtClaimOwners': '/api/users/claim-owners/lwt',
  'automotiveBatchHistory': '/api/automotive-claims/batches',
  'automotiveBatchDetails': '/api/automotive-claims/batch/__batchId__',
  'automotivePayments': '/api/automotive-claims/__claimId__/payments',
  'automotiveApprovedClaims': '/api/automotive-claims/approved-claims',
  'automotiveApprovedClaimsSupportingData': '/api/automotive-claims/approved-claims/supporting-data',
  'automotiveClaimsBatchAuthorize': '/api/automotive-claims/authorize',
  'automotiveReconciliation': '/api/automotive-claims/reconciliations',
  'automotiveReconciliationHistory': '/api/automotive-claims/reconciliations/history',
  'getROCustomer': "api/dms/ro/customer",
  "creditCardInfo": "api/credit-card",
  "creditRecordNotes": "api/credit-card/record-notes",
  "downloadAutoCCInvoice": "download/automotive-claims/__claimId__/invoice/credit-card",
  "automotiveReverse": "api/automotive-claims/reverse",
  "automotiveAdjust": "api/automotive-claims/adjust",
  'coverage': '/api/contracts/coverage/__code__',
  'lwtClaims': '/api/lwt-claims',
  'lwtClaimRecordNotes': '/api/lwt-claims/record-notes',
  'lwtClaimGetFieldNotes': '/api/lwt-claims/__claimId__/field-notes',
  'lwtClaimFieldNotes': '/api/lwt-claims/field-notes',
  'lwtClaimDocuments': '/api/lwt-claims/documents',
  'lwtClaimsDocumentTypes': '/api/lwt-claims/document-types',
  'lwtClaimGetDocuments': '/api/lwt-claims/__documentId__/documents',
  'lwtLineItem': '/api/lwt-claims/__claimId__/line-items',
  'lwtLineItemUpdate': '/api/lwt-claims/__lineItemId__/line-items',
  'lwtLineItemDelete': '/api/lwt-claims/line-items/__lineItemId__',
  'lwtClaimPayment': '/api/lwt-claims/__claimId__/payments',
  'lwtClaimVoid': '/api/lwt-claims/__claimId__/void',
  "lwtAdjust": "api/lwt-claims/adjust",
  'lwtLetter': '/api/lwt-claims/__claimId__/letter',
  'lwtLetters': '/api/lwt-claims/__claimId__/letters',
  'lwtEmailTemplate': '/api/lwt-claims/__claimId__/email-body/__templateId__',
};