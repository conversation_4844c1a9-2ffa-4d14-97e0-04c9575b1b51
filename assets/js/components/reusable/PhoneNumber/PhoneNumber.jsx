import React from 'react';
import Alert from 'react-s-alert';
import PropTypes from 'prop-types';

export default class PhoneNumberBox extends React.Component {

  static propTypes = {
    id: PropTypes.string,
    value: PropTypes.string,
    placeholder: PropTypes.string,
    onChange: PropTypes.func,
    customClassName: PropTypes.string
  };

  constructor(props) {
    super(props);
    this.state = {
      value: this.props.value || ''
    };
    this.handleBlur = this.handleBlur.bind(this);
    this.handleChange = this.handleChange.bind(this);
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    this.setState({ value: nextProps.value });
  }

  /**
   * Regex test to validate phone number
   * @param value
   * @returns {boolean}
   */

  validatePhoneNumber(value) {
    // Phone number having 10 to 11 digits are considered valid phone numbers
    // TODO : Find a better way or API to validate phone numbers
    const re = /^\d{10,11}$/;
    return re.test(value);
  }

  handleBlur(e) {
    const phoneNumber = e.target.value;
    if (this.validatePhoneNumber(phoneNumber)) {
      this.setState({ value: phoneNumber }, function () {
        this.props.onChange(this.state.value);
      });
    } else {
      Alert.error('Enter a valid phone number');
    }
  }

  handleChange(e) {
    const nonNumericRegex = /[^0-9]+/g;
    let phoneNumber = e.target.value.replace(nonNumericRegex, "").replace(/^(-.*?)-/g, "");
    if (phoneNumber.length > 11) {
      phoneNumber = phoneNumber.substring(0, 11);
    }
    this.setState({ value: phoneNumber });
  }

  render() {
    return (
      <div>
        <input type="text"
          id={this.props.id || ""}
          className={ `form-control ${this.props.customClassName}`}
          value={this.state.value}
          pattern="[0-9]{10,11}"
          title="Enter a valid phone number"
          placeholder={this.props.placeholder}
          onBlur={this.handleBlur}
          onChange={this.handleChange}/>
      </div>
    );
  }
}