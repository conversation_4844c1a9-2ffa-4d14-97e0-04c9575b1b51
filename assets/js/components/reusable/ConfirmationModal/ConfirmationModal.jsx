import React from 'react';
import Modal from '../../../Modal.jsx';
import PropTypes from 'prop-types';

export default class ConfirmationModal extends React.Component {

  static propTypes = {
    displayConfirmationModal: PropTypes.bool.isRequired,
    displayMessage: PropTypes.string.isRequired,
    onConfirm: PropTypes.func.isRequired,
    onDecline: PropTypes.func,
    confirmButtonText: PropTypes.string.isRequired,
    declineButtonText: PropTypes.string,
    type: PropTypes.string,
    renderTemplate: PropTypes.func,
    disableConfirmButton: PropTypes.bool
  };

  static defaultProps = {
    disableConfirmButton: false
  };

  constructor(props) {
    super(props);
    this.renderDeclineButton = this.renderDeclineButton.bind(this);
    this.renderNode = this.renderNode.bind(this);
  }

  renderDeclineButton() {
    if (this.props.declineButtonText) {
      return (
        <button type="button"
          className="btn btn-secondary cursor-pointer"
          onClick={this.props.onDecline}>
          {this.props.declineButtonText}
        </button>
      );
    }
  }

  renderNode() {
    if (this.props.type === 'node') {
      return this.props.renderTemplate();
    }
  }

  render() {
    return (
      <Modal visible={this.props.displayConfirmationModal}
        size="small" close={this.props.onDecline}>
        <div className="row">
          <div className="col-12 my-4">
            <p className="text-center">
              {this.props.displayMessage}
            </p>
          </div>
          {this.renderNode()}
          <div className="col-12 text-center my-4">
            <button type="button"
              className="btn btn-primary cursor-pointer mr-3"
              onClick={this.props.onConfirm}
              disabled={this.props.disableConfirmButton}>
              {this.props.confirmButtonText}
            </button>
            {this.renderDeclineButton()}
          </div>
        </div>
      </Modal>
    );
  }
}