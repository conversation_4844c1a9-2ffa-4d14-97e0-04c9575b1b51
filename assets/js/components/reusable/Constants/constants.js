export const CONSTANTS = {

  /**
     *
     * Warnings:
     * 1. DO NOT CHANGE the GAP_WORKSHEET_COMPONENT_MAP and RECOVERY_WORKSHEET_COMPONENT_MAP map, It will break Item notes and document attachment flow.
     *
     * 2. Field IDs in multiple of 100 for Fields 38 to 47 and 51 are reserved for dynamically added child claims.
     * Please do not use it.
     *
     * 3. For GAP, add new field IDs till 100 only.
     *
     * 4. We have field IDs of Recovery worksheet from 1001 onwards.
     *
     * */

  MAX_BATCH_CLAIM_COUNT: 100,

  GAP_WORKSHEET_COMPONENT_MAP: {
    'bookOutRequested': {
      'id': 1,
      'title': 'Book out - requested',
      'rowId': 'bookOutRequested'
    },
    'runAmortizationSheet': {
      'id': 2,
      'title': 'Run amortization sheet',
      'rowId': 'runAmortizationSheet'
    },
    'cancelContract': {
      'id': 3,
      'title': 'Cancel contracts',
      'rowId': 'cancelContract'
    },
    'otherInputOne': {
      'id': 4,
      'title': 'Other',
      'rowId': 'otherInputOne'
    },
    'otherInputTwo': {
      'id': 5,
      'title': 'Other',
      'rowId': 'otherInputTwo'
    },
    'otherInputThree': {
      'id': 6,
      'title': 'Other',
      'rowId': 'otherInputThree'
    },
    'settlementAmount': {
      'id': 7,
      'title': 'Settlement amount',
      'rowId': 'settlementAmount'
    },
    'insuranceCheckAmount': {
      'id': 8,
      'title': 'Insurance check amount',
      'rowId': 'insuranceCheckAmount'
    },
    'valuationReport': {
      'id': 9,
      'title': 'Valuation report',
      'rowId': 'valuationReport'
    },
    'matchesBaseValue': {
      'id': 10,
      'title': 'Matches base value',
      'rowId': 'matchesBaseValue'
    },
    'vinMatches': {
      'id': 11,
      'title': 'VIN matches',
      'rowId': 'vinMatches'
    },
    'priorDamage': {
      'id': 12,
      'title': 'Prior damage',
      'rowId': 'priorDamage'
    },
    'miscFee': {
      'id': 13,
      'title': 'Misc fee',
      'rowId': 'miscFee'
    },
    'mileage': {
      'id': 14,
      'title': 'Mileage',
      'rowId': 'mileage'
    },
    'dol': {
      'id': 15,
      'title': 'DOL',
      'rowId': 'dol'
    },
    'type': {
      'id': 16,
      'title': 'Type',
      'rowId': 'type'
    },
    'optionsMatchBookOut': {
      'id': 17,
      'title': 'Options match book out',
      'rowId': 'optionsMatchBookOut'
    },
    'optionsMatchBookOutOver150Percentage': {
      'id': 18,
      'title': 'Over 150%',
      'rowId': 'optionsMatchBookOutOver150Percentage'
    },
    'originalFinancingContract': {
      'id': 19,
      'title': 'Original financing contract',
      'rowId': 'originalFinancingContract'
    },
    'contractNumberMatches': {
      'id': 20,
      'title': 'Contract# matches',
      'rowId': 'contractNumberMatches'
    },
    'bankHistoryMatches': {
      'id': 21,
      'title': 'Bank history matches',
      'rowId': 'bankHistoryMatches'
    },
    'policeReport': {
      'id': 22,
      'title': 'Police report',
      'rowId': 'policeReport'
    },
    'insurancePolicyDeductiblePositive': {
      'id': 23,
      'title': 'Insurance policy deductible',
      'rowId': 'insurancePolicyDeductiblePositive'
    },
    'insurancePolicyDeductibleNegative': {
      'id': 24,
      'title': 'Insurance policy deductible',
      'rowId': 'insurancePolicyDeductibleNegative'
    },
    'bankInformation': {
      'id': 25,
      'title': 'Bank information',
      'rowId': 'bankInformation'
    },
    'fullLoanHistory': {
      'id': 26,
      'title': 'Full loan history',
      'rowId': 'fullLoanHistory'
    },
    'loanNumber': {
      'id': 27,
      'title': 'Loan number',
      'rowId': 'loanNumber'
    },
    'paymentAmount': {
      'id': 28,
      'title': 'Payment amount',
      'rowId': 'paymentAmount'
    },
    'interestRate': {
      'id': 29,
      'title': 'Interest rate',
      'rowId': 'interestRate'
    },
    'firstPaymentDate': {
      'id': 30,
      'title': 'First payment date',
      'rowId': 'firstPaymentDate'
    },
    'parentStatus': {
      'id': 31,
      'title': 'Status',
      'rowId': 'parentStatus'
    },
    'parentReturnedNote': {
      'id': 32,
      'title': 'Note',
      'rowId': 'parentReturnedNote'
    },
    'parentDeniedReason': {
      'id': 33,
      'title': 'Reason',
      'rowId': 'parentDeniedReason'
    },
    'parentAuthorizationNumber': {
      'id': 34,
      'title': 'Authorization #',
      'rowId': 'parentAuthorizationNumber'
    },
    'parentCheckNumber': {
      'id': 35,
      'title': 'Check #',
      'rowId': 'parentCheckNumber'
    },
    'parentPaidAmount': {
      'id': 36,
      'title': 'Amount',
      'rowId': 'parentPaidAmount'
    },
    'parentPaidDate': {
      'id': 37,
      'title': 'Paid date',
      'rowId': 'parentPaidDate'
    },
    'childClaim': {
      'id': 38,
      'title': 'Child claim',
      'rowId': 'childClaim'
    },
    'childClaimReason': {
      'id': 39,
      'title': 'Child claim reason',
      'rowId': 'childClaimReason'
    },
    'childClaimAmount': {
      'id': 40,
      'title': 'Child claim amount',
      'rowId': 'childClaimAmount'
    },
    'childStatus': {
      'id': 41,
      'title': 'Child claim status',
      'rowId': 'childStatus'
    },
    'childReturnedNote': {
      'id': 42,
      'title': 'Child claim note',
      'rowId': 'childReturnedNote'
    },
    'childDeniedReason': {
      'id': 43,
      'title': 'Child claim denied reason',
      'rowId': 'childDeniedReason'
    },
    'childAuthorizationNumber': {
      'id': 44,
      'title': 'Child claim authorization #',
      'rowId': 'childAuthorizationNumber'
    },
    'childCheckNumber': {
      'id': 45,
      'title': 'Child claim check #',
      'rowId': 'childCheckNumber'
    },
    'childPaidAmount': {
      'id': 46,
      'title': 'Child claim paid amount',
      'rowId': 'childPaidAmount'
    },
    'childPaidDate': {
      'id': 47,
      'title': 'Child claim Paid date',
      'rowId': 'childPaidDate'
    },
    'dealDate': {
      'id': 48,
      'title': 'Deal date',
      'rowId': 'dealDate'
    },
    'term': {
      'id': 49,
      'title': 'Term',
      'rowId': 'term'
    },
    'csCheck': {
      'id': 50,
      'title': 'CS Check',
      'rowId': 'csCheck'
    },
    'csChildCheck': {
      'id': 51,
      'title': 'CS Child Check',
      'rowId': 'csChildCheck'
    },
    'msrp': {
      'id': 52,
      'title': 'MSRP',
      'rowId': 'msrp'
    },
    'insuranceCompany': {
      'id': 53,
      'title': 'Insurance Company',
      'rowId': 'insuranceCompany'
    },
    'policyNumber': {
      'id': 54,
      'title': 'Policy Number',
      'rowId': 'policyNumber'
    },
    'mileageDeduction': {
      'id': 55,
      'title': 'Mileage Deduction',
      'rowId': 'mileageDeduction'
    },
    'nada': {
      'id': 56,
      'title': 'Nada',
      'rowId': 'nada'
    },
    'difference': {
      'id': 57,
      'title': 'Difference',
      'rowId': 'difference'
    },
    'recoveryReview': {
      'id': 58,
      'title': 'Recovery Review',
      'rowId': 'recoveryReview'
    },
    'recoveryAdded': {
      'id': 59,
      'title': 'Added',
      'rowId': 'recoveryAdded'
    },
    'recoveryRemoved': {
      'id': 60,
      'title': 'Removed',
      'rowId': 'recoveryRemoved'
    },
    'recoveryStatus': {
      'id': 61,
      'title': 'Recovery Status',
      'rowId': 'recoveryStatus'
    },
    'estimateWithPhotos': {
      'id': 62,
      'title': 'Estimate with photos',
      'rowId': 'estimateWithPhotos'
    },
    'isPaidByCS': {
      'id': 63,
      'title': 'Is paid by CS',
      'rowId': 'isPaidByCS'
    },
    'negativeEquityAmount': {
      'id': 64,
      'title': 'Negative Equity Amount',
      'rowId': 'negativeEquityAmount'
    },
    'numberOfDelinquentPayments': {
      'id': 65,
      'title': 'Number of Delinquent Payments',
      'rowId': 'numberOfDelinquentPayments'
    },
    'totalDelinquentPaymentsCovered': {
      'id': 66,
      'title': 'Total Delinquent Payments Covered',
      'rowId': 'totalDelinquentPaymentsCovered'
    },
    'numberOfExtensions': {
      'id': 67,
      'title': 'Number of Extensions',
      'rowId': 'numberOfExtensions'
    },
    'totalExtensionsCovered': {
      'id': 68,
      'title': 'Total Extensions Covered',
      'rowId': 'totalExtensionsCovered'
    },
    'calculatedDelinquentPayments': {
      'id': 69,
      'title': 'Calculated Delinquent Payments',
      'rowId': 'calculatedDelinquentPayments'
    },
    'calculatedExtensions': {
      'id': 70,
      'title': 'Calculated Extensions',
      'rowId': 'calculatedExtensions'
    },
  },
  RECOVERY_WORKSHEET_COMPONENT_MAP: {
    'alliedClaimNumber': {
      'id': 1001,
      'title': 'Allied Claim Number',
      'rowId': 'alliedClaimNumber'
    },
    'insuranceCompany': {
      'id': 1002,
      'title': 'Insurance Company',
      'rowId': 'insuranceCompany'
    },
    'insuranceClaimNumber': {
      'id': 1003,
      'title': 'Insurance Claim Number',
      'rowId': 'insuranceClaimNumber'
    },
    // TODO : caseAttachments : the same field is used on GAP worksheet and Recovery worksheet
    // hence reusing the recoveryReview = 58, as defined in line 304
    // still blocking 1004 for now, if we plan to keep different notes for the field on two worksheet, we can use it
    // 'caseAttachments': {
    //   'id': 1004,
    //   'title': 'Case Attachments',
    //   'rowId': 'caseAttachments'
    // },
    'contractBalance': {
      'id': 1005,
      'title': 'Contract Balance',
      'rowId': 'contractBalance'
    },
    'claimType': {
      'id': 1006,
      'title': 'Claim Type',
      'rowId': 'claimType'
    },
    'caseComments': {
      'id': 1007,
      'title': 'Case Comments',
      'rowId': 'caseComments'
    },
    'status': {
      'id': 1008,
      'title': 'Status',
      'rowId': 'status'
    },
    'checkAmount': {
      'id': 1009,
      'title': 'Check Amount',
      'rowId': 'checkAmount'
    },
  },
  VTA_WORKSHEET_COMPONENT_MAP: {
    'policeReport': {
      'id': 1,
      'title': 'Police Report',
      'rowId': 'policeReport'
    },
    'settlementCheck': {
      'id': 2,
      'title': 'Settlement Check',
      'rowId': 'settlementCheck'
    },
    'originalFinancing': {
      'id': 3,
      'title': 'Original Financing',
      'rowId': 'originalFinancing'
    },
    'vtaContract': {
      'id': 4,
      'title': 'VTA Contract',
      'rowId': 'vtaContract'
    },
    'insuranceNotRecovered': {
      'id': 5,
      'title': 'Insurance Not Recovered',
      'rowId': 'insuranceNotRecovered'
    },
    'status': {
      'id': 6,
      'title': 'Status',
      'rowId': 'status'
    },
    'deniedReason': {
      'id': 7,
      'title': 'Reason',
      'rowId': 'deniedReason'
    },
    'authorizationNumber': {
      'id': 8,
      'title': 'Authorization #',
      'rowId': 'authorizationNumber'
    },
    'checkNumber': {
      'id': 9,
      'title': 'Check #',
      'rowId': 'checkNumber'
    },
    'paidAmount': {
      'id': 10,
      'title': 'Amount',
      'rowId': 'paidAmount'
    },
    'paidDate': {
      'id': 11,
      'title': 'Paid date',
      'rowId': 'paidDate'
    },
    'vendorId': {
      'id': 12,
      'title': 'Vendor ID',
      'rowId': 'vendorId'
    },
    'caseReserve': {
      'id': 13,
      'title': 'Case Reserve',
      'rowId': 'caseReserve'
    },
  },
  LWT_WORKSHEET_COMPONENT_MAP: {
    'financeContract': {
      'id': 1,
      'title': 'Finance Contract',
      'rowId': 'financeContract',
      'documentType': 1,
    },
    'completedClaimForm': {
      'id': 2,
      'title': 'Completed Claim Form',
      'rowId': 'completedClaimForm',
      'documentType': 2,
    },
    'vehicleConditionReport': {
      'id': 3,
      'title': 'Vehicle Condition Report',
      'rowId': 'vehicleConditionReport',
      'documentType': 3,
    },
    'images': {
      'id': 4,
      'title': 'Images',
      'rowId': 'images',
      'documentType': -1,
    },
    'vinPlateImages': {
      'id': 5,
      'title': 'VIN Plate / Door Sticker Images',
      'rowId': 'vinPlateImages',
      'documentType': 4,
    },
    'wearAndTearDamageImages': {
      'id': 6,
      'title': 'Wear and Tear Damage Images',
      'rowId': 'wearAndTearDamageImages',
      'documentType': 5,
    },
    'finalInvoiceFromLessor': {
      'id': 7,
      'title': 'Final Invoice from Lessor',
      'rowId': 'finalInvoiceFromLessor',
      'documentType': 6,
    },
    'vendorId': {
      'id': 8,
      'title': 'Vendor ID',
      'rowId': 'vendorId',
      'documentType': -1,
    },
    'vendorName': {
      'id': 9,
      'title': 'Vendor Name',
      'rowId': 'vendorName',
      'documentType': -1,
    },
  },
  LWT_CLAIM_LINE_ITEM_STATUS_MAP: {
    'open': 'Open',
    'approved': 'Approved',
    'denied': 'Denied',
  },
  STATUS_MAP: {
    'inquiry': 'I',
    'pending': 'P',
    'readyToProcess': 'RP',
    'waitingForAuthorization': 'WA',
    'pendingDenial': 'PD',
    'authorization': 'WC',
    'returnedForCorrection': 'RC',
    'checkWritten': 'C',
    'checkVoided': 'CV',
    'denied': 'D',
    'closedNoResponse': 'C-NR',
    'pendingReopened': 'RO-RP',
    'noGap': 'NG',
    'noVta': 'NV',
    'extend2Weeks': 'P' /** This is added to handle status dropdown option 'Extend 2 weeks' with status as 'P' **/
  },
  READABLE_STATUS_MAP: {
    'I': 'Inquiry',
    'P': 'Pending',
    'RP': 'Ready to process',
    'WA': 'Waiting for authorization',
    'PD': 'Pending denial',
    'WC': 'Authorization',
    'RC': 'Returned',
    'C': 'Check Written',
    'CV': 'Check Voided',
    'D': 'Denied',
    'C-NR': 'Closed No-Response',
    'RO-RP': 'Pending Reopened',
    'NG': 'No GAP'
  },
  GAP_RECOVERY_STATUS_MAP: {
    'recoveryInquiry': { name: 'Recovery Inquiry', code: 'RI' },
    'possibleRecovery': { name: 'Possible Recovery', code: 'PR' },
    'waitingRecovery': { name: 'Waiting Recovery', code: 'WR' },
    'inRecovery': { name: 'In Recovery', code: 'IR' },
    'recovered': { name: 'Recovered', code: 'R' },
    'noRecovery': { name: 'No Recovery', code: 'NR' },
  },
  GAP_RECOVERY_READABLE_STATUS_MAP: {
    'RI': 'Recovery Inquiry',
    'IR': 'In Recovery',
    'PR': 'Possible Recovery',
    'WR': 'Waiting Recovery',
    'R': 'Recovered',
    'NR': 'No Recovery'
  },
  FIELD_TYPE: {
    'static': 'static',
    'dynamic': 'dynamic',
    'contract': 'contract'
  },
  CONTRACT_STATUS_MAP: {
    'Active': 'A',
    'Pending': 'P',
    'Canceled': 'C',
    'Expired': 'X'
  },
  //TODO: rename when disconnect from SB
  CONTRACT_STATUS_NEW_MAP: {
    'Active': 'Active',
    'Pending': 'Pending',
    'Canceled': 'Cancelled',
    'Expired': 'Expired',
    'Generated': 'Generated',
    'PendingCancel': 'PendingCancel',
    'Remitted': 'Remitted',
    'Voided': 'Voided',
  },
  PRODUCT_CODE_NEW_MAP: {
    'service': 'VSC',
    'maintenance': 'MNT',
    'gap': 'GAP',
    'appearanceProtection': 'AP',
    'paintlessDentRepair' : 'PDR',
    'leaseWearTear': 'LWT',
    'keyReplacement': 'KEY',
    'theftRegistration': 'VTA',
    'drivePur': 'DP',
    'tireWheel': 'TW',
    'century': 'CP',
    'toyotaTireWheel': 'TT',
    'toyotaExcessiveWearUse': 'WU',
    'toyotaGap': 'TG',
    'key': 'KE',
    'nsdTheft': 'NT'
  },
  LETTER_METHOD: [
    { 'type': 'Email' },
    { 'type': 'Print' },
    { 'type': 'Both' },
  ],
  USER_ROLES: {
    'gapClaims': 'gap_claims',
    'gapClaimsManager': 'gap_claims_manager',
    'autoClaims': 'auto_claims',
    'autoClaimsManager': 'auto_claims_manager',
    'accounting': 'accounting',
    'recoveryTeam': 'recovery_team',
    'productManager': 'product_manager',
    'accountingClaimHandler': 'accounting_claim_handler',
    'viewOnlyClaims': 'view_only_claims',
    'accountingClaimAdmin': 'accounting_claim_admin',
  },
  CLAIM_TYPE: {
    'vta': 'VTA',
    'gapRecovery': 'GAP_RECOVERY',
    'gap': 'GAP',
    'automotive': 'AUTOMOTIVE',
    'lwt': 'LWT',
  },
  AUTO_CLAIM_REASSIGNMENT_STATUS_MAP: {
    'new': 'New',
    'pending': 'Pending',
    'rejected': 'Rejected',
    'accepted': 'Accepted'
  },
  AUTO_CLAIM_COVERAGE_FLAGS: {
    'DisappearingDeductible': 'disappearing_deductible',
    'HighTech': 'high_tech',
    'SealsAndGasket': 'seals_and_gasket',
    'RentalUpgrade': 'rental_upgrade',
    'CommercialUse': 'commercial_use',
    'StandardPowertrainPlusOption': 'standard_powertrain_plus_option',
    'SmartTechOption': 'smart_tech_option',
    'CanadianVehicle': 'canadian_vehicle',
    'KeyCount': 'key_count',
    'Paint': 'paint',
    'Fabric': 'fabric',
    'LeatherOrVinyl': 'leather_or_vinyl',
    'DentAndDing': 'dent_and_ding'
  },
  AUTO_CLAIM_COVERAGE_FLAG_DISPLAY_NAME: {
    'Paint': 'Paint',
    'Fabric': 'Fabric',
    'LeatherOrVinyl': 'Leather/Vinyl',
    'DentAndDing': 'Dent and Ding'
  },
  AUTO_CLAIM_COMPLAINTS: {
    'Interior': 'Interior',
    'Exterior': 'Exterior',
    'DentAndDing': 'Dent and Ding'
  },
  AUTO_CLAIM_STATUS_MAP: {
    'preAuthorization': 'PreAuthorization',
    'open': 'Open',
    'returned': 'Returned',
    'payable': 'Payable',
    'ccPaid': 'CCPaid',
    'approved': 'Approved',
    'denied': 'Denied',
    'deactivated': 'Deactivated',
    'waitingForCheck': 'WaitingForCheck',
    'checkWritten': 'CheckWritten',
    'reversed': 'Reversed',
    'waitingForReversed': 'WaitingForReversed',
    'adjusted': 'Adjusted',
    'waitingOnVendor': 'WaitingOnVendor',
    'needRentalBill': 'NeedRentalBill',
    'needSubletBill': 'NeedSubletBill',
    'needSMToCall': 'NeedSMToCall',
    'needClosedAccountingRO': 'NeedClosedAccountingRO',
    'needProofOfDeductibleReimbursement': 'NeedProofOfDeductibleReimbursement',
    'invoiceSent': 'InvoiceSent',
    'authorizedCCClaim': 'AuthorizedCCClaim',
    'chargebackCollected': 'ChargebackCollected',
    'chargeback': 'Chargeback',
    'dealerChargedBack': 'DealerChargedBack',
  },
  AUTO_CLAIM_STATUS_DISPLAY_MAP: {
    'preAuthorization': 'PreAuthorization',
    'open': 'Open',
    'returned': 'Returned',
    'payable': 'Payable',
    'ccPaid': 'Paid By Credit Card',
    'approved': 'Approved',
    'denied': 'Denied',
    'deactivated': 'Deactivated',
    'waitingForCheck': 'Waiting For Check',
    'checkWritten': 'Check Written',
    'reversed': 'Reversed',
    'waitingForReversed': 'Waiting For Reversed',
    'adjusted': 'Adjusted',
    'waitingOnVendor': 'Waiting On Vendor #',
    'needRentalBill': 'Need Rental Bill',
    'needSubletBill': 'Need Sublet Bill',
    'needSMToCall': 'Need SM To Call',
    'needClosedAccountingRO': 'Need Closed Accounting RO',
    'needProofOfDeductibleReimbursement': 'Need Proof of Deductible Reimbursement',
    'invoiceSent': 'Invoice Sent',
    'authorizedCCClaim': 'Authorized CC Claim',
    'dealerChargedBack': 'Dealer Charged Back',
  },
  CHARGEBACK_CLAIM_STATUS_DISPLAY_MAP: {
    'open': 'Open',
    'approved': 'Approved',
    'waitingForChargeback': 'Waiting For Chargeback',
    'chargebackCollected': 'Chargeback Collected',
    'chargeback': 'Chargeback',
    'deactivated': 'Deactivated',
  },
  CHARGEBACK_CLAIM_STATUS_MAP: {
    'open': 'Open',
    'approved': 'Approved',
    'waitingForChargeback': 'WaitingForChargeback',
    'chargebackCollected': 'ChargebackCollected',
    'chargeback': 'Chargeback',
    'deactivated': 'Deactivated',
  },
  LWT_CLAIM_STATUS_DISPLAY_MAP: {
    'Inquiry': 'Inquiry',
    'ClaimInProcess': 'Claim In Process',
    'PendingApproval': 'Pending Approval',
    'PendingDenial': 'Pending Denial',
    'Approved': 'Approved',
    'CheckWritten': 'CheckWritten',
    'NoResponse': 'No Response',
    'ReturnedClaim': 'Returned Claim',
    'WaitingForCheck': 'Waiting For Check',
    'Denied': 'Denied',
    'Deactivated': 'Deactivated',
  },
  LWT_CLAIM_STATUS_MAP: {
    'inquiry': 'Inquiry',
    'claimInProcess': 'ClaimInProcess',
    'pendingApproval': 'PendingApproval',
    'pendingDenial': 'PendingDenial',
    'approved': 'Approved',
    'checkWritten': 'CheckWritten',
    'noResponse': 'NoResponse',
    'returnedClaim': 'ReturnedClaim',
    'waitingForCheck': 'WaitingForCheck',
    'denied': 'Denied',
    'deactivated': 'Deactivated',
  },
  PRODUCT_CODE_NAME_MAP: {
    'VSC': 'Service',
    'MNT': 'Maintenance',
    'GAP': 'Gap',
    'AP' : 'Appearance Protection',
    'PDR': 'Paintless Dent Repair',
    'VTA': 'Theft Registration',
    'CP': 'Century',
    'KEY': 'key Replacement',
    'TW': 'Tire Wheel',
    'LWT': 'Lease Wear and Tear',
    'DP': 'DrivePUR',
    'TT': 'Toyota Tire Wheel',
    'WU': 'Toyota Excessive Wear Use',
    'TG': 'Toyota Gap',
    'KE': 'key',
    'NT': 'NSD Theft'
  },
  PAYMENT_TYPE_CODES: {
    'CREDIT_CARD': 'CC',
    'CUSTOMER': 'CR',
    'STORE': 'RF'
  },
  PAYMENT_TYPE_NAMES: {
    'DEFAULT': '--Select Pay Type--',
    'CREDIT_CARD': 'Credit Card',
    'CUSTOMER': 'Customer',
    'STORE': 'Store'
  },
  PAYMENT_TYPE_NAME_MAP: {
    'CC': 'Credit Card',
    'CR': 'Customer',
    'RF': 'Store'
  },
  PART_PAID_SHORT: [
    'Part over MSRP',
    'Part over $250.00 max mark up',
    'Non OEM part marked up over 60%',
    'Part or fluid not covered'
  ],
  LABOR_PAID_SHORT: [
    'Labor time exceeds Alldata time or not supported',
    'Labor Diagnosis not allowed, excessive or not supported'
  ],
  COUNTRY_MAP: {
    'USA': 'USA',
    'Canada': 'Canada'
  },
  AUTO_CLAIM_BATCH_FIELD: {
    "CONTRACT": "contract",
    "RO": "ro",
    "AMOUNT": "estimate",
    "AGENT": "agent",
    "APPROVER": "approver"
  },
  AUTO_CLAIM_DENIED_REASON: {
    "over60days": "Over 60 days"
  },
  PAYMENT_TYPE_MAP: {
    'CASH': 'Cash',
    'SPP': 'SPP',
  },
  AUTO_CLAIM_COMPLAINTS_DENIED_REASON: {
    "expiredByTime": "Expired - by time",
    "expiredByMileage": "Expired - by mileage",
    "failedInspectionComponent": "Failed Inspection Component",
    "notCovered": "Not Covered",
    "notPreAuthorized": "Not Pre-Authorized",
    "stillInFactoryWarranty": "Still in Factory Warranty",
    "underWritingPeriod30": "Underwriting Period - 30 days",
    "underWritingPeriod60": "Underwriting Period - 60 days",
    "underWritingPeriod90": "Underwriting Period - 90 days",
    "other": "Other",
  },
};
