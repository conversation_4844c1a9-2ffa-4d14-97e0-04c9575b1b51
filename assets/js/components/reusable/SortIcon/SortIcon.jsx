import React from 'react';
import PropTypes from 'prop-types';
import If from "../If/If.jsx";

const SortIcon = ({
  sortBy,
  sortOrder,
  fieldName
}) => {
  return (
    <span>
      <If condition={ sortBy === fieldName &&  sortOrder === 'asc' }>
        <i className="fa fa-caret-up"/>
      </If>
      <If condition={ sortBy === fieldName &&  sortOrder === 'desc' }>
        <i className="fa fa-caret-down"/>
      </If>
      <If condition={ sortBy !== fieldName }>
        <i className="fa fa-sort"/>
      </If>
    </span>
  );
};

SortIcon.propTypes = {
  sortBy: PropTypes.string.isRequired,
  sortOrder: PropTypes.string.isRequired,
  fieldName: PropTypes.string.isRequired,
};

export default SortIcon;