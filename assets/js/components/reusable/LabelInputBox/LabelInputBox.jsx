import React from 'react';
import InputBox from '../../reusable/InputBox/InputBox.jsx';
import PropTypes from 'prop-types';

export default class LabelInputBox extends React.Component {

  static propTypes = {
    label: PropTypes.string.isRequired,
    disableLabel: PropTypes.bool.isRequired,
    onChange: PropTypes.func,
    customClass: PropTypes.string,
    displayColon: PropTypes.bool,
    style: PropTypes.object,
    rowId: PropTypes.string
  };

  constructor(props) {
    super(props);
    this.state = {
      label: this.props.label || '',
      showLabel: true
    };
    this.showInputBox = this.showInputBox.bind(this);
    this.onKeyUp = this.onKeyUp.bind(this);
    this.onBlur = this.onBlur.bind(this);
    this.renderLabel = this.renderLabel.bind(this);
    this.renderInputBox = this.renderInputBox.bind(this);
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (nextProps.label !== this.state.label) {
      this.setState({ label: nextProps.label });
    }
  }

  showInputBox() {
    if (!this.props.disableLabel) {
      this.setState({ showLabel: false });
    }
  }

  onKeyUp(e) {
    if (e.keyCode === 13) {
      this.setState({ showLabel: true });
    }
  }

  onBlur() {
    this.setState({ showLabel: true });
  }

  renderLabel() {
    if (this.state.showLabel) {
      return (
        <label className="col-form-label col-form-label-sm text-primary">
          <a href="#!"
            id={ `${this.props.rowId}_clickableLabel` }
            onClick={ this.showInputBox }
            disabled={ this.props.disableLabel }>{ this.state.label }</a>
          <span className={ !this.props.displayColon && 'd-none' }>:</span>
        </label>
      );
    }
  }

  renderInputBox() {
    if (!this.state.showLabel) {
      return (
        <InputBox type='Text'
          id={ `${this.props.rowId}_inputLabel` }
          value={ this.state.label }
          onChange={ this.props.onChange }
          onBlur={ this.onBlur }
          onKeyUp={ this.onKeyUp }
          isFocused={ true }/>
      );
    }
  }

  render() {
    return (
      <span className={ this.props.customClass } style={ this.props.style }>
        { this.renderLabel() }
        { this.renderInputBox() }
      </span>
    );
  }
}