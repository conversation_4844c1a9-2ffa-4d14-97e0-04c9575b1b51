var React = require('react');
var PropTypes = require('prop-types');

class ErrorSummary extends React.Component {
  static propTypes = {
    errors: PropTypes.object
  };

  render() {
    if (!this.props.errors || Object.keys(this.props.errors).length < 1) {
      return null;
    }
    return (
      <div className="alert alert-danger">
        <p><strong>The following errors prevented this form from being successfully submitted:</strong></p>
        <ul>
          {Object.keys(this.props.errors).map(function(err<PERSON><PERSON>, i){
            return <li key={i}>{this.props.errors[errKey]}</li>;
          }.bind(this))}
        </ul>
      </div>
    );
  }
}

module.exports = ErrorSummary;
