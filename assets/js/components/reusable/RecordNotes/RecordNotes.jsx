import React from 'react';
import Loader from 'react-loader-advanced';
import <PERSON><PERSON> from 'react-s-alert';
import { json as ajax } from './../../../ajax.js';
import moment from 'moment';
import dateFormat from "./../Utilities/dateFormat.js";
import PropTypes from 'prop-types';
import { URLS as apiUrls } from "../Utilities/urls";
import If from "../If/If.jsx";
import { CONSTANTS } from "../Constants/constants";
const FIELD_TYPE = require("./../Constants/constants.js").CONSTANTS.FIELD_TYPE;

export default class RecordNotes extends React.Component {

  static propTypes = {
    location: PropTypes.shape({
      id: PropTypes.oneOfType([
        PropTypes.string,
        PropTypes.number
      ]),
      contract_number: PropTypes.string,
      productCode: PropTypes.string
    }),
    updateRecordNotes: PropTypes.bool,
    onRecordNotesUpdate: PropTypes.func,
    apiURL: PropTypes.string,
    hideAddNoteTextArea: PropTypes.bool,
    hideNotes: PropTypes.bool,
    excludePagination: PropTypes.bool,
    recordNotes: PropTypes.oneOfType([
      PropTypes.object,
      PropTypes.array
    ]),
    hasTitle: PropTypes.bool,
    title: PropTypes.string,
    hasCloseButton: PropTypes.bool,
    closeButtonCallback: PropTypes.func,
    type: PropTypes.string,
    selectedFieldNote: PropTypes.object,
    worksheetOffset: PropTypes.number,
    eventType: PropTypes.string,
    containerClass: PropTypes.string,
    notesStyle: PropTypes.object,
    addedNote: PropTypes.func,
    newNoteText: PropTypes.string,
    createdByUserID: PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.number
    ]),
    includeLocation: PropTypes.bool
  };

  static defaultProps = {
    includeLocation: true
  };

  constructor(props) {
    super(props);
    let recordNotes = [];
    if (this.props.type === FIELD_TYPE.static && this.props.recordNotes) {
      recordNotes = this.props.recordNotes['notes'];
    } else if (this.props.type === FIELD_TYPE.dynamic &&
      this.props.recordNotes) {
      recordNotes = this.props.recordNotes;
    }
    this.state = {
      record_notes: recordNotes,
      newNoteText: this.props.newNoteText ? this.props.newNoteText : '',
      showLoader: false,
      pageNumber: 1,
      totalNotes: 0,
      pageSize: 20,
      notesOffset: this.props.selectedFieldNote && this.props.selectedFieldNote.itemNoteOffset ? this.props.selectedFieldNote.itemNoteOffset : ''
    };
    this.updateNotePosition = this.updateNotePosition.bind(this);
    this.loadNotes = this.loadNotes.bind(this);
    this.handleNoteOnChange = this.handleNoteOnChange.bind(this);
    this.saveNote = this.saveNote.bind(this);
    this.addNote = this.addNote.bind(this);
    this.handleShowMore = this.handleShowMore.bind(this);
    this.handleShowPrevious = this.handleShowPrevious.bind(this);
    this.renderRecordNotesModule = this.renderRecordNotesModule.bind(this);
    this.renderRecordNotes = this.renderRecordNotes.bind(this);
    this.renderRecordNote = this.renderRecordNote.bind(this);
    this.getUserName = this.getUserName.bind(this);
    this.renderShowMoreOption = this.renderShowMoreOption.bind(this);
    this.renderShowPreviousOption = this.renderShowPreviousOption.bind(this);
    this.renderAddRecordNotesModule = this.renderAddRecordNotesModule.bind(this);
    this.getStyle = this.getStyle.bind(this);
    this.getTitle = this.getTitle.bind(this);
    this.getCloseButton = this.getCloseButton.bind(this);
  }

  componentDidMount() {
    if (this.props.type !== FIELD_TYPE.static && this.props.type !== FIELD_TYPE.dynamic) {
      this.loadNotes(this.props, false);
    }
    this.updateNotePosition();
  }

  componentDidUpdate() {
    /** The reason of calling updateNotePosition function here is the height of dynamic record note component
     * will be available only after rendering that component, This function does include conditional setState. **/
    this.updateNotePosition();
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (nextProps.updateRecordNotes === true && nextProps.type !== FIELD_TYPE.static &&
      nextProps.type !== FIELD_TYPE.dynamic) {
      this.loadNotes(nextProps, false, nextProps.updateRecordNotes);
    }
    if (nextProps.type === FIELD_TYPE.static && nextProps.recordNotes) {
      this.setState({ record_notes: nextProps.recordNotes['notes'] });
    } else if (nextProps.type === FIELD_TYPE.dynamic &&
      nextProps.recordNotes) {
      this.setState({ record_notes: nextProps.recordNotes });
    } else if ((nextProps.type === FIELD_TYPE.static && !nextProps.recordNotes) ||
      (nextProps.type === FIELD_TYPE.dynamic && !nextProps.recordNotes)) {
      this.setState({ record_notes: [] });
    }
    if (nextProps.type === FIELD_TYPE.static || nextProps.type === FIELD_TYPE.dynamic) {
      this.setState({ notesOffset: nextProps.selectedFieldNote.itemNoteOffset });
    }
  }

  /**
   * This function is added to update position of record note component on hover event.
   * */

  updateNotePosition() {
    if (this.props.eventType === 'hover' && (this.props.worksheetOffset - this.state.notesOffset) < (this.recordNote.clientHeight - 50 )) {
      /** The conditional setState is added here to dynamically place record note card **/
      this.setState({ notesOffset: this.state.notesOffset - (this.recordNote.clientHeight - 50) });
    }
  }

  /**
   * This function will fetch record notes based on {Claim ID}
   * @param props
   * current props
   * @param toPaginate:
   * Accepts boolean value true or false, If user have clicked on showMore it will be true otherwise it will be false.
   * @param updateRecordNotes:
   * This parameter is used to set value of updateRecordNotes state in parent component once the Record Notes component has been updated with latest notes list.
   * */

  loadNotes(props, toPaginate, updateRecordNotes) {
    let url = `${props.apiURL}?page=${this.state.pageNumber}`;
    if (props.includeLocation) {
      if (props.type === CONSTANTS.FIELD_TYPE.contract) {
        url = props.apiURL;
      } else {
        url = `${props.apiURL}/${props.location.id}?page=${this.state.pageNumber}`;
      }
    }
    if (updateRecordNotes === true) {
      props.onRecordNotesUpdate();
    }
    if (props.type === 'manual') {
      url += "&is_manual=true";
    }
    if(props.type === 'RECOVERY_CASE') {
      url = props.apiURL.replace('__claimId__', props.location.id);
    }
    this.setState({ showLoader: true, record_notes: [], totalNotes: 0 }, () =>  {
      ajax(url, {}, {}, (data, status) => {
        if (status === 200) {
          if (toPaginate) {
            const recordNotes = this.state.record_notes.slice(0);
            recordNotes.push(...data.record_notes);
            this.setState({ record_notes: recordNotes, showLoader: false, totalNotes: data.count });
          } else {
            if(data.notes){
              this.setState({record_notes: data.notes, showLoader: false, totalNotes: data.notes.length});
            } else {
              this.setState({record_notes: data.record_notes, showLoader: false, totalNotes: data.count});
            }
          }
        } else if (status === 400) {
          this.setState({ showLoader: false });
        } else {
          this.setState({ showLoader: false }, () => {
            Alert.error("Click the browser's Refresh button to reload record notes. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  }

  handleNoteOnChange(event) {
    this.setState({ newNoteText: event.target.value }, function () {
      if (this.props.addedNote) {
        this.props.addedNote(this.state.newNoteText);
      }
    });
  }

  saveNote() {
    let data;
    let url = this.props.apiURL;
    if (this.props.apiURL === "/api/gapcontract/notes") {
      data = { "id": this.props.location.id, "notes_text": this.state.newNoteText };
    } else if(this.props.apiURL === `${apiUrls.contract}/${this.props.location.contract_number}/notes`) {
      data = { "notes_text": this.state.newNoteText };
    } else if (this.props.type === FIELD_TYPE.static && this.props.apiURL === apiUrls.fieldNotes) {
      data = {
        "gap_claim_id": parseInt(this.props.location.id),
        "field_id": this.props.selectedFieldNote.fieldId,
        "notes_text": this.state.newNoteText
      };
    } else if (this.props.type === FIELD_TYPE.dynamic  && this.props.apiURL === apiUrls.dynamicFieldNotes) {
      data = {
        "gap_claim_id": parseInt(this.props.location.id),
        "contract_number": this.props.selectedFieldNote.contractDetails.contractNumber,
        "contract_code": this.props.selectedFieldNote.contractDetails.contractCode,
        "notes_text": this.state.newNoteText
      };
    } else if (this.props.type === FIELD_TYPE.static && this.props.apiURL === apiUrls.vtaClaimFieldNotes) {
      data = {
        "vta_claim_id": parseInt(this.props.location.id),
        "field_id": this.props.selectedFieldNote.fieldId,
        "notes_text": this.state.newNoteText
      };
    } else if (this.props.type === FIELD_TYPE.static && this.props.apiURL === apiUrls.lwtClaimFieldNotes) {
      data = {
        "lwt_claim_id": parseInt(this.props.location.id),
        "field_id": this.props.selectedFieldNote.fieldId,
        "notes_text": this.state.newNoteText
      };
    } else if (this.props.type === 'manual') {
      if (this.props.apiURL === apiUrls.lwtClaimRecordNotes) {
        data = {
          "id": parseInt(this.props.location.id),
          "is_manual": true,
          "created_by_user_id": parseInt(this.props.createdByUserID),
          "notes_text": this.state.newNoteText 
        };
      } else { // automotive manual notes
        data = {
          "automotive_claim_id": parseInt(this.props.location.id),
          "is_manual": true,
          "created_by_user_id": parseInt(this.props.createdByUserID),
          "notes_text": this.state.newNoteText
        };
      }
    } else {
      data = { "id": parseInt(this.props.location.id), "notes_text": this.state.newNoteText };
    }
    if(this.props.type === 'RECOVERY_CASE') {
      url = this.props.apiURL.replace('__claimId__', this.props.location.id);
    }
    ajax(url, data, { method: 'POST' }, (data, status) => {
      if (status === 200) {
        this.setState({ newNoteText: '', pageNumber: 1 }, function () {
          if (this.props.type === FIELD_TYPE.static || this.props.type === FIELD_TYPE.dynamic) {
            this.props.onRecordNotesUpdate();
          } else if (this.props.type === FIELD_TYPE.contract) {
            this.props.onRecordNotesUpdate();
            this.loadNotes(this.props, false);
          } else {
            this.loadNotes(this.props, false);
          }
        });
      } else {
        this.setState({ newNoteText: '' }, () => {
          Alert.error("Click the Record note button again. If the error continues, contact your system administrator.");
        });
      }
    });
  }

  addNote(event) {
    event.preventDefault();
    this.saveNote();
  }

  handleShowMore() {
    this.setState({ pageNumber: this.state.pageNumber + 1 }, function () {
      this.loadNotes(this.props, true);
    });
  }

  handleShowPrevious() {
    this.setState({ pageNumber: this.state.pageNumber - 1 }, function () {
      this.loadNotes(this.props, true);
    });
  }

  renderRecordNotesModule() {
    /**
     * This style has been added to create fixed height card with vertical scroll.
     * */
    const notesCard = {
      maxHeight: '300px',
      overflowY: 'auto'
    };
    if (this.state.record_notes && this.state.record_notes.length > 0) {
      return (
        <div className="card card-body mb-0" style={ notesCard }>
          { this.renderShowPreviousOption() }
          { this.renderRecordNotes() }         
          { this.renderShowMoreOption() }
        </div>
      );
    } 
    return (
      <section>
        <p className="text-center">No notes available.</p>
      </section>
    );
  }

  renderRecordNotes() {
    return this.state.record_notes.map(this.renderRecordNote);
  }

  renderRecordNote(note, index) {
    return (
      <div key={ index } className="clearfix my-1">
        <span className="small">
          { note.notes_text }
        </span>
        <span className="small float-right font-italic">
          { this.getUserName(note) }
        </span>
      </div>
    );
  }

  getUserName(note) {
    if (this.props.type === 'manual' || this.props.type === CONSTANTS.FIELD_TYPE.contract) {
      return (`${note.user_name}-${moment(new Date(note.created_at)).format(dateFormat.displayDateFormat)}`);
    } else {
      return (`${note.first_name} ${note.last_name}-${moment(new Date(note.created_at)).format(dateFormat.displayDateFormat)}`);
    }
  }

  renderShowMoreOption() {
    if (!this.props.excludePagination && this.state.totalNotes > this.state.pageSize && this.state.totalNotes > this.state.pageSize * this.state.pageNumber) {
      return (
        <div className="my-1">
          <div className="text-center">
            <a href="#!"
              onClick={ this.handleShowMore }>
              Show More
            </a>
          </div>
        </div>
      );
    }
  }

  renderShowPreviousOption() {
    if (!this.props.excludePagination && this.state.totalNotes > this.state.pageSize && this.state.pageNumber > 1) {
      return (
        <div className="my-1">
          <div className="text-center">
            <a href="#!"
              onClick={ this.handleShowPrevious }>
              Show Previous
            </a>
          </div>
        </div>
      );
    }
  }

  /**
   * This function will render "Notes text area" and "Record note" button.
   * @returns {XML}
   */

  renderAddRecordNotesModule() {
    if (!this.props.hideAddNoteTextArea) {
      return (
        <div>
          <textarea type="text"
            className="form-control"
            rows="5"
            value={ this.state.newNoteText }
            onChange={ this.handleNoteOnChange } />
          <button className="btn btn-secondary float-right mt-3"
            id="addNote"
            disabled={ !this.state.newNoteText }
            onClick={ this.addNote }>
            Record Note
          </button>
          <div className="clearfix" />
          <hr />
        </div>
      );
    }
  }

  getStyle() {
    const style = {};
    if (this.props.type === FIELD_TYPE.static || this.props.type === FIELD_TYPE.dynamic) {
      style ['top'] = this.state.notesOffset ? `${this.state.notesOffset - 10}px` : '-10px';
    }
    return style;
  }

  getTitle() {
    if (this.props.hasTitle) {
      return (
        <div className="row justify-content-end">
          <div className="col-12 pb-1">
            <p className="h6">{ this.props.title }</p>
          </div>
        </div>
      );
    }
  }

  getCloseButton() {
    const closeIcon = {
      marginRight: "-20px",
      marginTop: "-20px"
    };
    /**
     * This style has been added to bring close button on Top right corner in Record note card
     * */
    if (this.props.hasCloseButton) {
      return (
        <div className="row justify-content-end">
          <div className="col-1" style={ closeIcon }>
            <span className="fa fa-times cursor-pointer" id="icon-close-note" onClick={ this.props.closeButtonCallback }>
            </span>
          </div>
        </div>
      );
    }
  }

  render() {
    const spinnerMessage = <p className='text-center'><i className="fa fa-refresh fa-spin"/> Loading Notes</p>;
    return (
      <section style={ this.props.notesStyle } className={ this.props.containerClass }>
        <Loader show={ this.state.showLoader } message={ spinnerMessage }>
          <section className="row">
            <div className="card card-body bg-faded col-12" ref={ recordNote => this.recordNote = recordNote } style={ this.getStyle() }>
              { this.getCloseButton() }
              { this.getTitle() }
              { this.renderAddRecordNotesModule() }
              <If condition={ !this.props.hideNotes }>
                { this.renderRecordNotesModule() }
              </If>
            </div>
          </section>
        </Loader>
      </section>
    );
  }
}