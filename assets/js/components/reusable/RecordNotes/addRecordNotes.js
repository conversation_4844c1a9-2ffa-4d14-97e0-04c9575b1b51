import { json as ajax } from "../../../ajax.js";
import { URLS as apiUrls } from "../Utilities/urls";

export const addRecordNote = (id, note, successCallback, errorCallback, url) => {
  let notesUrl = apiUrls.gapClaimNotes;
  if(url) {
    notesUrl = url;
  }
  let data = { "id": parseInt(id), "notes_text": note };
  ajax(notesUrl, data, { method: 'POST' }, (data, status) => {
    if (status === 200) {
      successCallback && successCallback(data);
    } else {
      errorCallback && errorCallback();
    }
  });
};
