import React from 'react';
import Alert from 'react-s-alert';
import PropTypes from 'prop-types';

export default class EmailBox extends React.Component {

  static propTypes = {
    value: PropTypes.string,
    placeholder: PropTypes.string,
    onChange: PropTypes.func,
    customClassName: PropTypes.string
  };

  constructor(props) {
    super(props);
    this.state = {
      value: this.props.value || ''
    };
    this.validateEmail = this.validateEmail.bind(this);
    this.handleBlur = this.handleBlur.bind(this);
    this.handleChange = this.handleChange.bind(this);
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    this.setState({ value: nextProps.value });
  }

  getEmailRegex() {
    return "[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$";
  }

  /**
   * Regex test to validate email id
   * @param value
   * @returns {boolean}
   */

  validateEmail(value) {
    const re = new RegExp(this.getEmailRegex());
    return re.test(value);
  }

  /**
   * Added to update value of email field
   *
   * @event onBlur
   * @param {event} e
   *  Event object to get the value of input box
   * */

  handleBlur(e) {
    const email = e.target.value;
    if (this.validateEmail(email)) {
      this.setState({ value: email }, function () {
        this.props.onChange(this.state.value);
      });
    } else {
      Alert.error('Enter a valid email');
    }
  }

  /**
   * Addded to update email state on change event
   * @event onChange
   * @param {event} e
   */

  handleChange(e) {
    this.setState({ value: e.target.value });
  }

  render() {
    return (
      <div>
        <input type="text"
          className={ `form-control ${this.props.customClassName}`}
          value={this.state.value}
          pattern={this.getEmailRegex()}
          title="Enter a valid email ID"
          placeholder={this.props.placeholder}
          onBlur={this.handleBlur}
          onChange={this.handleChange}/>
      </div>
    );
  }
}