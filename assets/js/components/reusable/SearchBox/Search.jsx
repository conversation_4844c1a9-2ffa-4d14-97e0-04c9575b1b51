import React from "react";
import PropTypes from "prop-types";

export default class Search extends React.Component {

  static propTypes = {
    onSearch: PropTypes.func.isRequired,
    placeholder: PropTypes.string,
    value: PropTypes.string
  };

  constructor(props) {
    super(props);
    this.state = {
      value: this.props.value || ''
    };
    this.handleSearchClick = this.handleSearchClick.bind(this);
    this.handleChange = this.handleChange.bind(this);
  }

  UNSAFE_componentWillReceiveProps(nextProps){
    this.setState({
      value: this.state.value || nextProps.value
    });
  }

  handleSearchClick(e) {
    e.preventDefault();
    this.props.onSearch(this.state.value);
  }

  handleChange(e) {
    this.setState({ value: e.target.value });
  }

  render() {
    return (
      <section className="search-component">
        <form onSubmit={this.handleSearchClick}>
          <div className="input-group">
            <input type="text"
              placeholder={this.props.placeholder}
              value={this.state.value}
              id="search"
              className="form-control"
              onChange={this.handleChange}/>
            <span className="input-group-btn">
              <button type="submit"
                className="btn btn-primary"
                id="btn-search">
                <i className="fa fa-search pr-1"/>
                  Search
              </button>
            </span>
          </div>
        </form>
      </section>
    );
  }
}