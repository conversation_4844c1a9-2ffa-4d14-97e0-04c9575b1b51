import React from 'react';
import PropTypes from 'prop-types';
import Modal from "../../../Modal.jsx";
import Select from "react-select";
import Alert from "react-s-alert";
import { jsonPromise as ajax, raw as ajaxRaw } from '../../../ajax';
import {URLS as apiUrls} from "../Utilities/urls";


export default class CoverageModal extends React.Component {
  static propTypes = {
    visible: PropTypes.bool,
    onCloseModal: PropTypes.func,
    contractList: PropTypes.array,
    displayId: PropTypes.number,
    contractClaims: PropTypes.array,
  };
  constructor(props) {
    super(props);
    this.state = {
      currentMileage: 0,
      expiredContract: true,
      isVerifiedContract: false,
      coverageDetails: [],
      contractFormID: -1,
      selectedComponents: [],
      expandedGroup: '',
      groupedComponents: [],
      isValidMileage: false,
      loading: false,
    };
  }

  componentDidMount() {
    this.setState({loading: true}, () => {
      const contracts = this.props.contractList.filter((contract) => contract.product_code === 'VSC');
      let contract = contracts[0];
      if (contracts.length > 1) {
        contract = contracts.find(obj => obj.id === this.props.displayId);
      }

      let code = contract  && contract.code;
      if (code === undefined) {
        return;
      }
      const url = apiUrls.coverage.replace('__code__', code);
      ajax(url, {}, {})
        .then(results => {
          if (results.status !== 200) {
            Alert.error('Error while fetching the contract components');
          } else {
            if (results.data.data.contractFormID === 0) {
              this.setState({ contractFormID: results.data.data.contractFormID, loading: false,});
              return;
            }
            if (results.data.data.coverageDetails !== null) {
              this.setState({
                coverageDetails: results.data.data.coverageDetails,
                contractFormID: results.data.data.contractFormID,
                groupedComponents: results.data.data.coverageDetails.reduce((cumulative, item) => {
                  if (cumulative[item.group_name]) {
                    cumulative[item.group_name].push(item);
                  } else {
                    cumulative[item.group_name] = [item];
                  }
                  return cumulative;
                }, {}),
                loading: false,
              });
            } else {
              this.setState({
                contractFormID: results.data.data.contractFormID,
                loading: false,
              });
            }
          }
        })
        .catch(() => {
          this.setState({loading: false});
          Alert.error('Error while fetching the contract components');
        });
    });
  }

  getComponentsList = () => {
    return this.state.coverageDetails.map(option => {
      return {
        label: `${this.getComponentStatus(option)} - ${option.group_name} - ${option.description}`,
        value: option.product_type_id,
        covered: option.covered,
        code: option.code,
        group_name: option.group_name,
        description: option.description,
        product_type_id: option.product_type_id,
        product_name: option.product_name,
        option_code: option.option_code,
        updated_option_code: option.updated_option_code,
      };
    });
  };

  getComponentIcon = component => {
    if (component.covered === 'Y')
      return <i className='fa fa-check-square-o' style={{color:"green"}}/>;
    if (component.covered === 'N')
      return <i className='fa fa-times' style={{color:"red"}}/>;
    if (component.covered === 'O') {
      if (this.getCoveredStatusForOption(component)) {
        return <i className='fa fa-check-square-o' style={{color:"green"}}/>;
      } else {
        return <i className='fa fa-times' style={{color:"red"}}/>;
      }
    }
    return (
      <span
        data-toggle="tooltip"
        data-placement="top"
        title="Need authorization, call 1-801-563-4185"
        style={{color:"#B8860B"}}
      >
        Call TCA
      </span>
    );
  };

  getComponentStatus = option => {
    if (option.covered === 'Y')
      return "Yes";
    if (option.covered === 'N')
      return "No";
    if (option.covered === 'O') {
      if (this.getCoveredStatusForOption(option)) {
        return "Yes";
      } else {
        return "No";
      }
    }
    return "Call TCA";
  };

  getCoveredStatusForOption = component => {
    let contractOptions = this.props.contractList.filter((contract) => contract.product_code === 'VSC' && contract.id === this.props.displayId)[0].options;
    if (contractOptions.find((opt) => opt.code === component.option_code || opt.code === component.updated_option_code)) {
      return true;
    } else {
      return false;
    }
  }

  updateSelectedComponents = component => {
    let currentComponents = this.state.selectedComponents;
    let currentOptions = this.state.coverageDetails;
    if (currentComponents.find(option => option.code === component.code)) {
      this.setState({
        coverageDetails: currentOptions.concat(component),
        selectedComponents: currentComponents.filter(option => option.code !== component.code),
      });
      return;
    }
    this.setState({
      coverageDetails: currentOptions.filter(option => option.code !== component.code),
      selectedComponents: currentComponents.concat(component),
    });
  };

  validateMileage = expirationMileage => {
    const { currentMileage } = this.state;
    this.setState({
      isVerifiedContract: true,
      expiredContract: currentMileage > expirationMileage,
      selectedComponents: [],
      isValidMileage: currentMileage < expirationMileage,
    });
  };

  getContractClaims = () => {
    const {
      contractClaims,
    } = this.props;
    if (contractClaims) {
      return (contractClaims && contractClaims.length) || 0;
    }
    return null;
  };

  generateCoveragePdf = (data) => {
    this.setState({ loading: true });
    ajaxRaw(`/api/contracts/coverage/pdf`, 
      {data: JSON.stringify(data), method: `POST`, responseType: 'blob'}, 
      (data) => {
        if (data.status === 200) {
          this.setState({ loading: false });
          const blob = new Blob([data.response], { type: 'application/pdf' });
          const url = URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          link.download = 'Contract_Coverage.pdf';
          link.click();
          URL.revokeObjectURL(url);
        } else {
          this.setState({ loading: false });
          Alert.error("Error generating contract coverage pdf");
        }
      });
    
    return null;
  };

  render() {
    const { visible, onCloseModal, contractList } = this.props;
    const { contractFormID, coverageDetails, selectedComponents, currentMileage, expiredContract, isVerifiedContract, isValidMileage } = this.state;
    let contracts = contractList.filter((contract) => contract.product_code === 'VSC' && contract.id === this.props.displayId);
    const contract = contracts[0];
    if (contract === undefined) {
      return null;
    }
    const data = {
      selectedItems: this.state.selectedComponents,
      contract: contract,
      currentMileage,
    };

    return (
      <Modal visible={visible} close={onCloseModal} minHeight={coverageDetails && coverageDetails.length > 0 ? 610 : 100} size="large">
        {
          this.state.loading && (
            <div>
              <h2><i className='fa fa-refresh fa-spin'></i> Loading ...</h2>
            </div>
          ) || (
            <div>
              <h4 className="border-bottom border-light">Coverage Lookup</h4>
              <div className="container">
                <div className="row py-3 d-flex justify-content-between">
                  <div>
                    <h4>{contract && contract.customer_name}</h4>
                  </div>
                  <div>
                    <div>
                      <h5>{contract.vin}</h5>
                      <h5>{contract.vehicle_make} {contract.vehicle_model} {contract.vehicle_year}</h5>
                    </div>
                  </div>
                  <div>
                    <div>
                      <h5>Basic warranty miles: {contract.warranty.basic_warranty_miles}</h5>
                      <h5>Basic warranty months: {contract.warranty.basic_warranty_months}</h5>
                      <h5>Drivetrain warranty miles: {contract.warranty.drivetrain_warranty_miles}</h5>
                      <h5>Drivetrain warranty months: {contract.warranty.drivetrain_warranty_months}</h5>
                    </div>
                  </div>
                </div>
                {
                  selectedComponents && selectedComponents.length > 0 && (
                    <div>
                      <div className="py-2">
                        <button className="btn btn-primary"
                          target='_blank'
                          rel="noopener noreferrer"
                          onClick={() => this.generateCoveragePdf(data)}
                        >
                          <i className="fa fa-print"/> Print List
                        </button>
                      </div>
                      <table className="table table-striped table-hover">
                        <thead>
                          <tr>
                            <th>&nbsp;</th>
                            <th>Covered</th>
                            <th>Code</th>
                            <th>Product Type</th>
                            <th>Group</th>
                            <th>Description</th>
                          </tr>
                        </thead>
                        <tbody>
                          {
                            selectedComponents.map(
                              (component) => {
                                return (
                                  <tr key={`${component.code}`}>
                                    <td>
                                      <span
                                        style={{ cursor: "pointer" }}
                                        data-toggle="tooltip"
                                        data-placement="top"
                                        onClick={ () => this.updateSelectedComponents(component) }
                                        title="Remove component">
                                        <i className='fa fa-minus-square'/>
                                      </span>
                                    </td>
                                    <td>{this.getComponentIcon(component)}</td>
                                    <td>{component.code}</td>
                                    <td>{component.product_name}</td>
                                    <td>{component.group_name}</td>
                                    <td>{component.description.replace('(requires authorization CALL)', '')}</td>
                                  </tr>
                                );
                              }
                            )
                          }
                        </tbody>
                      </table>
                    </div>
                  )
                }
                {
                  (contractFormID === 0 &&
                    <div className="text-center">
                      <h5 style={{color:"#dc3545"}}>The contract is not associated to any contract form!</h5>
                    </div>) ||
                  (coverageDetails.length === 0 && contractFormID > 0 && <div className="text-center">
                    <h5 style={{color: "#dc3545"}}>Component List not found</h5>
                  </div>) || (
                    <div>
                      <div className='row mb-4 ml-1 text-primary' style={{ fontSize: 'large' }}>
                        <div>All tech found items and all lines added must be called in for pre-authorization.</div>
                      </div>
                      <div className='row ml-1 font-weight-bold' style={{fontSize: 'large'}}>
                        <div><span>Claim Count: </span> <span className={this.getContractClaims() > 3 ? "text-danger" : "text-dark"}>{this.getContractClaims()} </span></div>
                      </div>
                      <div className='row mb-4 ml-1 text-primary' style={{ fontSize: 'large' }}>
                        <div>If claim count exceeds 4, pre-authorization is required.</div>
                      </div>
                      <div className='row  mb-2'>
                        <div className='col-sm-4'>
                          <input
                            type='number'
                            min={0}
                            className='form-control'
                            required={true}
                            value={ currentMileage === 0 ? '' : currentMileage }
                            placeholder="Current mileage"
                            onChange={({ target: { value } }) => this.setState({ currentMileage: value })}
                          />
                        </div>
                        <div className='col-sm-2'>
                          <button
                            type='button'
                            className='btn btn-primary'
                            disabled={ currentMileage <= 0 }
                            onClick={ () => this.validateMileage(contract.expiration_mileage) }
                          >Submit</button>
                        </div>
                        {
                          isVerifiedContract && expiredContract && (
                            <div className='col-sm-3'>
                              <h6 style={{color:"#dc3545"}}>Contract is expired by mileage!</h6>
                            </div>
                          )
                        }
                      </div>
                      <div className="card">
                        <div className="card-header d-flex flex-column">
                          <h4>Service</h4>
                          <div className="d-flex justify-content-between">
                            <div className="d-flex flex-column">
                              <h5>Contract #</h5>
                              <h6>{contract.original_code}</h6>
                            </div>
                            <div className="d-flex flex-column">
                              <h5>Product/Type</h5>
                              <h6>{contract.product_name}</h6>
                            </div>
                            <div className="d-flex flex-column">
                              <h5>Plan/Term</h5>
                              <h6>{contract.plan_name}</h6>
                            </div>
                            <div className="d-flex flex-column">
                              <h5>Options Selected</h5>
                              {
                                contract.options && contract.options.map(val => <h6 key={val.id}>{val.name}</h6>)
                              }
                            </div>
                          </div>
                          <div>
                            <h6 style={{color:"#dc3545"}}>
                              *These specific components represent the causal parts for a mechanical failure.<br />
                              The terms and conditions per the contact still apply and coverage is NOT guaranteed.<br />
                              *If a specific component is not listed, please contact TCA for verification.
                            </h6>
                          </div>
                        </div>
                        <div className="col-5 py-2">
                          <Select
                            id="coverage-component-input-box"
                            placeholder="Search Component..."
                            options={ this.getComponentsList() }
                            onChange={ component => this.updateSelectedComponents(component) }
                            menuContainerStyle={ { zIndex: 10 } }
                            tabSelectsValue={ false }
                            disabled = { expiredContract }
                            matchProp='label'/>
                        </div>
                      </div>
                      <div className="accordion">
                        {
                          isVerifiedContract && isValidMileage && Object.keys(this.state.groupedComponents).map((item) => {
                            const isExpandedGroup = item === this.state.expandedGroup;
                            return(
                              <div className="card" key={item}>
                                <div style={{ cursor: "pointer" }}
                                  className="card-header"
                                  onClick={() => this.setState((prevState) => ({ expandedGroup: prevState.expandedGroup !== item && item || '' }))}
                                >
                                  <h6 className="mb-0"><i className={`fa fa-caret-${isExpandedGroup && 'down' || 'right'}`}></i>&nbsp;&nbsp;&nbsp;
                                    {item}
                                  </h6>
                                </div>
                                <div
                                  className={`collapse ${isExpandedGroup && 'show' || ''}`}
                                  aria-labelledby="headingOne"
                                  data-parent="#accordionExample"
                                >
                                  <div className="card-body">
                                    <table className="table table-striped table-hover">
                                      <thead>
                                        <tr>
                                          <th>&nbsp;</th>
                                          <th>Covered</th>
                                          <th>Code</th>
                                          <th>Product Type</th>
                                          <th>Group</th>
                                          <th>Description</th>
                                        </tr>
                                      </thead>
                                      <tbody>
                                        {
                                          this.state.groupedComponents[item].map(
                                            (component) => {
                                              return (
                                                <tr key={`${component.code}`}>
                                                  <td>
                                                    {!selectedComponents.find(comp => component.code === comp.code) && <span
                                                      style={{ cursor: "pointer" }}
                                                      data-toggle="tooltip"
                                                      data-placement="top"
                                                      onClick={ () => this.updateSelectedComponents(component) }
                                                      title="Remove component">
                                                      <i className='fa fa-plus-square'/>
                                                    </span>}
                                                  </td>
                                                  <td>{this.getComponentIcon(component)}</td>
                                                  <td>{component.code}</td>
                                                  <td>{component.product_name}</td>
                                                  <td>{component.group_name}</td>
                                                  <td>{component.description.replace('(requires authorization CALL)', '')}</td>
                                                </tr>
                                              );
                                            }
                                          )
                                        }
                                      </tbody>
                                    </table>
                                  </div>
                                </div>
                              </div>
                            );
                          })
                        }
                      </div>
                    </div>
                  )
                }
              </div>
            </div>
          )
        }

      </Modal>
    );
  }
}