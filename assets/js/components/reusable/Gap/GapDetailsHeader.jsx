import React from 'react';
import PropTypes from 'prop-types';
import { userHasRole } from "../Utilities/userHasRole";
import {CONSTANTS} from "../Constants/constants";
import CoverageModal from "./CoverageModal";

export default class GapDetailsHeader extends React.Component {

  static contextTypes = {
    router: PropTypes.object.isRequired
  };

  static propTypes = {
    contractNumber: PropTypes.string,
    vehicleNumber: PropTypes.string.isRequired,
    customerName: PropTypes.string.isRequired,
    nextButtonClassName: PropTypes.string,
    nextButtonText: PropTypes.string,
    nextButtonDisabled: PropTypes.bool,
    nextButtonOnClick: PropTypes.func,
    backButtonOnClick: PropTypes.func.isRequired,
    contractData: PropTypes.array,
    user: PropTypes.shape({
      id: PropTypes.number.isRequired,
      first_name: PropTypes.string.isRequired,
      last_name: PropTypes.string.isRequired,
      email: PropTypes.string.isRequired,
      stores: PropTypes.arrayOf(PropTypes.string.isRequired),
      roles: PropTypes.shape({
        Map: PropTypes.object,
      }).isRequired
    }),
    displayId: PropTypes.number,
    contractClaims: PropTypes.array,
  };

  constructor(props) {
    super(props);
    this.renderContractID = this.renderContractID.bind(this);
    this.renderVehicleNumber = this.renderVehicleNumber.bind(this);
    this.renderCustomerName = this.renderCustomerName.bind(this);
    this.renderNextButton = this.renderNextButton.bind(this);
    this.renderButtons = this.renderButtons.bind(this);
    this.renderCoverageButton = this.renderCoverageButton.bind(this);
    this.state = {
      showCoverageModal: false
    };
  }

  renderContractID() {
    if (this.props.contractNumber) {
      return (
        <div className="col-3">
          <p className="text-muted mb-0">Contract #</p>
          <p className="text-primary small">{ this.props.contractNumber }</p>
        </div>);
    }
  }

  renderVehicleNumber() {
    if (this.props.vehicleNumber) {
      return (
        <div className="col-3">
          <p className="text-muted mb-0">Vehicle #</p>
          <p className="text-primary small">{ this.props.vehicleNumber }</p>
        </div>
      );
    }
  }

  renderCustomerName() {
    if (this.props.customerName) {
      return (
        <div className="col-2 pr-0">
          <p className="text-muted mb-0">Customer</p>
          <p className="text-primary small">{ this.props.customerName }</p>
        </div>
      );
    }
  }

  handleCoverage = () => {
    this.setState({
      showCoverageModal: true
    });
  }

  closeCoverage = () => {
    this.setState({
      showCoverageModal: false
    });
  };

  renderCoverageButton = () => {
    if (userHasRole(this.props.user, CONSTANTS.USER_ROLES.autoClaims) || 
      userHasRole(this.props.user, CONSTANTS.USER_ROLES.autoClaimsManager) ||
      userHasRole(this.props.user, CONSTANTS.USER_ROLES.viewOnlyClaims)
    ) {
      const contract = this.props.contractData.find((contract) => contract.product_code === 'VSC' && contract.id === this.props.displayId);
      if (contract === undefined) {
        return;
      }
      return (
        <button className="btn btn-primary mr-2 cursor-pointer"
          onClick={this.handleCoverage}
          disabled={false}>
          <i className="fa fa-search"></i>
          &nbsp;Coverage
        </button>
      );
    }
  }

  renderBackButton = () => {
    if(this.props.vehicleNumber && this.props.customerName){
      return (
        <button onClick={ this.props.backButtonOnClick } className="btn btn-secondary mr-2 cursor-pointer">
          <i className="fa fa-arrow-left"></i>
              &nbsp;Back
        </button>
      );
    }
  };

  renderNextButton() {
    if (this.props.nextButtonText) {
      return (
        <button className={ `btn btn-primary cursor-pointer${this.props.nextButtonDisabled ? ' disabled' : ''}` }
          onClick={ this.props.nextButtonOnClick }
          disabled={ !!this.props.nextButtonDisabled }>
          <i className={ `fa ${this.props.nextButtonClassName}` }/>
          &nbsp;{this.props.nextButtonText}
        </button>
      );
    }
  }

  renderButtons() {
    const colSize = `col-${this.props.contractNumber ? 4 : 7}`;
    return (
      <div className={ colSize }>
        <div className="row justify-content-end pr-3">
          {this.renderCoverageButton()}
          {this.renderBackButton()}
          {this.renderNextButton()}
        </div>
      </div>
    );
  }

  render() {
    const {
      showCoverageModal
    } = this.state;
    return (
      <div className="row pt-1">
        {this.renderContractID()}
        {this.renderVehicleNumber()}
        {this.renderCustomerName()}
        {this.renderButtons()}
        {
          (this.props.contractData && this.props.contractData.length > 0) &&
          <CoverageModal
            visible={showCoverageModal}
            onCloseModal={this.closeCoverage}
            contractList={this.props.contractData}
            displayId={this.props.displayId}
            contractClaims={this.props.contractClaims}
          />
        }
      </div>
    );
  }
}

GapDetailsHeader.defaultProps = {
  nextButtonDisabled: false
};
