import React from "react";
import PropTypes from "prop-types";

export default class Select<PERSON>ox extends React.Component {

  static propTypes = {
    value: PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.number
    ]),
    onChange: PropTypes.func,
    customClassName: PropTypes.string,
    optionsList: PropTypes.array,
    disabled: PropTypes.bool,
    id: PropTypes.string
  };

  constructor(props) {
    super(props);
    this.handleChange = this.handleChange.bind(this);
    this.renderOptions = this.renderOptions.bind(this);
  }

  handleChange(e) {
    e.preventDefault();
    this.props.onChange(e.target.value, e);
  }

  renderOptions() {
    return this.props.optionsList && this.props.optionsList.map(this.getOption);
  }

  getOption(state, index) {
    return (
      <option key={index} value={state.value}
        disabled={state.isDisabled ? state.isDisabled : false}>
        {state.name}
      </option>
    );
  }

  render() {
    return (
      <div>
        <select className={ `form-control ${this.props.customClassName}`}
          id={this.props.id}
          onChange={this.handleChange}
          onClick={(e)=> {
            e.preventDefault();
            if (e.stopPropagation) {
              e.stopPropagation();
            }
          }}
          value={this.props.value}
          disabled={ this.props.disabled }>
          {this.renderOptions()}
        </select>
      </div>
    );
  }
}
