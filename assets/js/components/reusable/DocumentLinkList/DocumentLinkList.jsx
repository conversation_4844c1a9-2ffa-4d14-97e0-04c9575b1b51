import React from 'react';
import DocumentLink from "./DocumentLink.jsx";
import PropTypes from 'prop-types';

export default class DocumentLinkList extends React.Component {

  static propTypes = {
    fieldDocs: PropTypes.array,
    deleteAttachment: PropTypes.func,
    claimType: PropTypes.string,
    isFinanceUser: PropTypes.bool,
    isContractAttachment: PropTypes.bool,
    contractId: PropTypes.number
  };

  renderColumnNames = () => {
    if (this.props.isContractAttachment === true) {
      return (
        <div className="row">
          <div className="col-6"><strong>Name</strong></div>
          <div className="col-2"><strong>Created At</strong></div>
          <div className="col-4"><strong>Description</strong></div>
        </div>);
    }
  }
  renderDocs = () => {

    if (this.props.fieldDocs && Array.isArray(this.props.fieldDocs) && this.props.fieldDocs.length > 0) {
      return(
        <div>
          {this.renderColumnNames()}
          <div className="row">
            <div className="col-12">
              {this.props.fieldDocs.map((doc, index) => <DocumentLink
                key={ index }
                index={ index }
                document={ doc }
                deleteAttachment={ this.props.deleteAttachment }
                isFinanceUser={ this.props.isFinanceUser }
                claimType={ this.props.claimType }
                isContractAttachment={ this.props.isContractAttachment }
                contractId={ this.props.contractId }
              />)}
            </div>
          </div>
        </div>);
    }
  };

  render() {
    return (
      <div>
        {this.renderDocs()}
      </div>
    );
  }
}