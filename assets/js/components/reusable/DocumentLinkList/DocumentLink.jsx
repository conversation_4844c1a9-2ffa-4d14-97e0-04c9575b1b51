import React from 'react';
import { URLS as url } from '../Utilities/urls';
import PropTypes from 'prop-types';
import { CONSTANTS } from "../Constants/constants.js";
import ConfirmationModal from "../ConfirmationModal/ConfirmationModal.jsx";
import If from '../If/If.jsx';
import moment from "moment";
import dateFormat from "./../Utilities/dateFormat.js";

export default class DocumentLink extends React.Component {

  static propTypes = {
    index: PropTypes.number,
    document: PropTypes.object,
    deleteAttachment: PropTypes.func,
    claimType: PropTypes.string,
    isFinanceUser: PropTypes.bool,
    isContractAttachment: PropTypes.bool,
    contractId: PropTypes.number,
  };

  constructor(props) {
    super(props);
    this.state = {
      toBeDeleted: 0,
      displayModal: false
    };
  }

  getFileIconClass = (document) => {
    const extension = document.file_name.slice(document.file_name.lastIndexOf("."), document.file_name.length).toLowerCase();
    switch (extension) {
    case '.png':
    case '.jpeg':
    case '.jpg':
      return 'fa-picture-o';
    case '.txt':
      return 'fa-file-text-o';
    case '.doc':
    case '.docx':
      return 'fa-file-word-o text-primary';
    case '.pdf':
      return 'fa-file-pdf-o text-danger';
    }
  };

  renderFileIcon = (document) => {
    return (
      <i className={ `mr-1 fa ${this.getFileIconClass(document)}` }/>
    );
  };

  getFileName = (document, index) => {
    return document.file_name.slice(document.file_name.lastIndexOf("/") + 1, document.file_name.lastIndexOf("."));
  };

  handleDeleteAttachment = (id) => {
    this.setState({ displayModal: true, toBeDeleted: id });
  };

  deleteAttachment = () => {
    const toBeDeleted = this.state.toBeDeleted;
    this.setState({ displayModal: false, toBeDeleted: 0 }, () => {
      this.props.deleteAttachment(toBeDeleted);
    });
  };

  closeModal = () => {
    this.setState({ displayModal: false, toBeDeleted: 0 });
  };

  getDownloadURL = () => {
    if (this.props.isContractAttachment === true) {
      let attachmentURL = url.contractAttachment;
      attachmentURL = attachmentURL.replace("__contractId__", this.props.contractId);
      attachmentURL = attachmentURL.replace("__attachmentId__", this.props.document.id);
      return attachmentURL;
    }
    switch (this.props.claimType) {
    case CONSTANTS.CLAIM_TYPE.automotive:
      return `${url.automotiveClaimDocument}/${this.props.document.id}`;
    case CONSTANTS.CLAIM_TYPE.vta:
      return `${url.vtaClaimDocuments}/${this.props.document.id}`;
    case CONSTANTS.CLAIM_TYPE.lwt:
      return `${url.lwtClaimDocuments}/${this.props.document.id}`;
    default:
      return `${url.document}/${this.props.document.id}`;
    }
  };

  renderDeleteIcon = () => {
    if (this.props.deleteAttachment) {
      if (this.props.claimType === CONSTANTS.CLAIM_TYPE.automotive) {
        return (
          <span className="cursor-pointer delete-document">
            <i className="fa fa-times" onClick={ this.props.deleteAttachment.bind(this, this.props.document.id) }/>
          </span>);
      } else {
        return (
          <If condition={ !this.props.isFinanceUser }>
            <span className="cursor-pointer delete-document">
              <span className="fa fa-times" onClick={ this.handleDeleteAttachment.bind(this, this.props.document.id) }></span>
            </span>
          </If>
        );
      }
    }
  };
  renderContractAttachment() {
    return (<div className="row">
      <div className="col-6">
        <a key={ this.props.index }
          target="_blank"
          rel="noopener noreferrer"
          className="d-inline-block col-form-label-sm text-muted mr-2"
          href={ this.getDownloadURL() }>
          {this.renderFileIcon(this.props.document)}
          {this.getFileName(this.props.document, this.props.index)}
        </a>
      </div>
      <div className="col-2">
        {this.props.document.created_at && moment.utc(this.props.document.created_at).format(dateFormat.displayDateFormat)}
      </div>
      <div className="col-4">
        {this.props.document.description}
      </div>
    </div>
    );
  }

  renderClaimAttachment() {
    return (
      <div className="d-inline-flex document-link">
        <a key={ this.props.index }
          target="_blank"
          rel="noopener noreferrer"
          className="d-inline-block col-form-label-sm text-muted mr-2"
          href={ this.getDownloadURL() }>
          {this.renderFileIcon(this.props.document)}
          {this.getFileName(this.props.document, this.props.index)}
        </a>
        {this.renderDeleteIcon()}
        <ConfirmationModal confirmButtonText="Yes"
          declineButtonText="No"
          displayConfirmationModal={ this.state.displayModal }
          displayMessage="You have selected to delete this attachment, do you want to continue?"
          onConfirm={ this.deleteAttachment }
          onDecline={ this.closeModal }/>
      </div>
    );
  }

  render() {
    return (
      <div>
        {this.props.isContractAttachment ? this.renderContractAttachment() : this.renderClaimAttachment()}
      </div>
    );
  }
}
