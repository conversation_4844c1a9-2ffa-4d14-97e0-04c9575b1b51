import React from 'react';
import Modal from "./../../../Modal.jsx";
import Loader from "react-loader-advanced";
import UploadFile from "./../UploadFile/UploadFile.jsx";
import PropTypes from 'prop-types';

export default class AttachmentModal extends React.Component {

  static propTypes = {
    displayModal: PropTypes.bool.isRequired,
    closeModal: PropTypes.func.isRequired,
    title: PropTypes.string,
    isUploading: PropTypes.bool,
    maxFileSizeInMBs: PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.number
    ]),
    handleUpload: PropTypes.func.isRequired,
    isMultipleAllowed: PropTypes.bool
  };

  constructor(props) {
    super(props);
    this.state = {
      dragEnter: false
    };
    this.renderTitle = this.renderTitle.bind(this);
  }

  renderTitle() {
    return (
      <div>
        <p className="h5">{this.props.title}</p>
      </div>
    );
  }

  render() {
    const spinnerMessage = <p className='text-center'><i className="fa fa-refresh fa-spin"/> Uploading...</p>;
    return (
      <Modal visible={this.props.displayModal}
        close={this.props.closeModal}
        size="large">
        <Loader show={this.props.isUploading} message={spinnerMessage}>
          {this.renderTitle()}
          <UploadFile maxFileSizeInMBs={this.props.maxFileSizeInMBs}
            handleUpload={this.props.handleUpload}
            isMultipleAllowed={this.props.isMultipleAllowed}
            closeModal={this.props.closeModal}/>
        </Loader>
      </Modal>
    );
  }
}

AttachmentModal.defaultProps = {
  isUploading: false
};