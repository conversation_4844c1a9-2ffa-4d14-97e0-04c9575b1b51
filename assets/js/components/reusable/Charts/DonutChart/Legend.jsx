import React from 'react';
import PropTypes from 'prop-types';

export default class Legend extends React.Component {

  static propTypes = {
    style: PropTypes.object.isRequired,
    fill: PropTypes.string.isRequired,
    data: PropTypes.object.isRequired,
    translate: PropTypes.string.isRequired,
    legendColumnXPositions: PropTypes.array.isRequired,
    rectWidth: PropTypes.number,
    rectHeight: PropTypes.number,
    onLegendClick: PropTypes.func,
    selectedBar: PropTypes.string
  };

  constructor(props) {
    super(props);
  }

  render() {
    let style = {};
    if (this.props.selectedBar === this.props.data.name || this.props.selectedBar === this.props.data.id.toString()) {
      style = {
        'stroke': '#090808'
      };
    }
    return (
      <g className="legend cursor-pointer" style={style} transform={this.props.translate}
        onClick={this.props.onLegendClick.bind(null, this.props.data.name, this.props.data.id)}>
        <rect width={this.props.rectWidth} height={this.props.rectHeight} rx="3" ry="3" y="-8.5"
          fill={this.props.fill}></rect>
        <text x={this.props.legendColumnXPositions[0]} style={this.props.style}><a>{this.props.data.name}</a></text>
        <text x={this.props.legendColumnXPositions[1]} style={this.props.style}>{this.props.data.count}</text>
      </g>
    );
  }
}