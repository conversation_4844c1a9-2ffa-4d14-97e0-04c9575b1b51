import React from 'react';
import * as d3 from 'd3';
import PropTypes from 'prop-types';

export default class Sector extends React.Component {

  static propTypes = {
    style: PropTypes.object.isRequired,
    fill: PropTypes.string.isRequired,
    innerRadius: PropTypes.number.isRequired,
    outerRadius: PropTypes.number.isRequired,
    data: PropTypes.object.isRequired
  };

  constructor(props) {
    super(props);
  }

  render() {
    const arc = d3.arc().outerRadius(this.props.outerRadius).innerRadius(this.props.innerRadius);
    return (
      <g className="arc">
        <path d={arc(this.props.data)} style={this.props.style} fill={this.props.fill}></path>
      </g>
    );
  }
}