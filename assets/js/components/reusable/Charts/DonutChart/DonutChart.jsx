import React from 'react';
import * as d3 from 'd3';
import Legend from './Legend.jsx';
import Sector from './Sector.jsx';
import PropTypes from 'prop-types';

export default class DonutChart extends React.Component {

  static propTypes = {
    data: PropTypes.array.isRequired,
    onLegendClick: PropTypes.func,
    config: PropTypes.shape({
      width: PropTypes.number.isRequired,
      height: PropTypes.number.isRequired,
      innerRadius: PropTypes.number.isRequired,
      outerRadius: PropTypes.number.isRequired,
      sectorStrokeWidth: PropTypes.string,
      centerTextTitle: PropTypes.string.isRequired,
      centerTextCount: PropTypes.string.isRequired,
      legendBulletSize: PropTypes.number.isRequired,
      legendStyle: PropTypes.object.isRequired,
      legendHeaderStyle: PropTypes.object.isRequired,
      legendColumnHeaders: PropTypes.array.isRequired,
      legendColumnXPositions: PropTypes.array.isRequired,
      barColors: PropTypes.array,
      selectedBar: PropTypes.string
    })
  };

  constructor(props) {
    super(props);
    this.calculateSVGHeight = this.calculateSVGHeight.bind(this);
    this.renderDataSeries = this.renderDataSeries.bind(this);
    this.legends = this.legends.bind(this);
    this.bars = this.bars.bind(this);
  }

  // If legends size grows more than 7 then it will not fit into current props height
  // Increasing height by 20 for each extra legend row
  // If there are 8 legends row :
  // props.height = 200
  // Y (legends Y axis position) for 8th legends row would be 100 (Calculated using offset)
  // height = (100*2) + 20 = 220

  calculateSVGHeight() {
    // The default minimum height
    let height = this.props.config.height;
    // The number of items in the data collection
    const count = this.props.data.length;

    if (count > 10) {
      // Calculate the new height based on the number of items
      // in the data collection minus 1 from the collection per
      // 10 beyond the first 10.
      height = 20 * (count - (Math.floor(count / 10) - 1));
    }

    return height;
  }

  renderDataSeries() {
    let color;
    if (this.props.config.barColors) {
      color = d3.scaleOrdinal().range(this.props.config.barColors);
    } else {
      const length = this.props.data.length;
      color = d3.scaleLinear().domain([1, length])
        .interpolate(d3.interpolateLab)
        .range([d3.rgb('#2E4053'), d3.rgb('#EBEDEF')]);
    }
    const pie = d3.pie().value(d => d.count).sort(null);
    const bars = pie(this.props.data).map(this.bars.bind(null, color));
    const legends = pie(this.props.data).map(this.legends.bind(null, color));
    return (
      <g transform="translate(50, 70)">
        {bars}
        {legends}
        <g className="title"
          transform={`translate(${this.props.config.outerRadius + 10},-${this.props.config.outerRadius + 10})`}>
          <text x={this.props.config.legendColumnXPositions[0]+5} y="15"
            style={this.props.config.legendHeaderStyle}>{this.props.config.legendColumnHeaders[0]}</text>
          <text x={this.props.config.legendColumnXPositions[1]} y="15"
            style={this.props.config.legendHeaderStyle}>{this.props.config.legendColumnHeaders[1]}</text>
        </g>
        <text textAnchor="middle" fontSize="1em" y={1} fill="#5b5858">{this.props.config.centerTextCount}</text>
        <text textAnchor="middle" fontSize="0.75em" y={20} fill="#716E6E">{this.props.config.centerTextTitle}</text>
      </g>
    );
  }

  legends(color, point, i) {
    const offset = this.props.config.outerRadius - 16;
    const x = this.props.config.outerRadius + 30;
    const y = (i * offset) / 2 - offset + 10;
    const translate = `translate(${x},${y})`;
    return (
      <Legend data={point.data}
        key={i}
        style={this.props.config.legendStyle}
        fill={color(i)}
        translate={translate}
        rectWidth={this.props.config.legendBulletSize}
        rectHeight={this.props.config.legendBulletSize}
        onLegendClick={this.props.onLegendClick}
        selectedBar={this.props.config.selectedBar}
        legendColumnXPositions={this.props.config.legendColumnXPositions}
      />
    );
  }

  bars(color, point, i) {
    const style = {
      strokeWidth: this.props.config.sectorStrokeWidth
    };
    return (
      <Sector data={point}
        key={i}
        style={style}
        fill={color(i)}
        innerRadius={this.props.config.innerRadius}
        outerRadius={this.props.config.outerRadius}/>
    );
  }

  render() {
    const noData = <text x={this.props.config.width / 2} y={this.props.config.height / 2} fontFamily="sans-serif"
      fontSize="12px" fill="red">No Data found!</text>;
    const height = this.calculateSVGHeight();
    return (
      <svg width={this.props.config.width} height={height}>
        { this.props.data && this.props.data.length !== 0 ? this.renderDataSeries() : noData }
      </svg>);
  }
}