import React from 'react';
import PropTypes from 'prop-types';

export default class ClickToCopy extends React.Component {
  copyText = (event) => {
    event.preventDefault();
    if (event.clipboardData) {
      event.clipboardData.setData("text/plain", this.textField.textContent);
    }
  };

  render() {
    const {text, className} = this.props;
    return (
      <span
        id="text"
        className={`cursor-pointer ${className}`}
        ref={(textField) => this.textField = textField}
        onCopy={this.copyText}
        onClick={() => document.execCommand("copy")}>
        {text}
      </span>
    );
  }
}

ClickToCopy.propTypes = {
  text: PropTypes.string,
  className: PropTypes.string,
};