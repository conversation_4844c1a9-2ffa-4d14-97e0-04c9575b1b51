import React from 'react';
import PropTypes from 'prop-types';
import { CONSTANTS } from "../Constants/constants";
import SelectBox from "../SelectBox/SelectBox";

const countryList = Object.keys(CONSTANTS.COUNTRY_MAP).map((key) => {
  return {
    "name": CONSTANTS.COUNTRY_MAP[key],
    "label": CONSTANTS.COUNTRY_MAP[key]
  };
});

export default class CountryList extends React.Component {

  static propTypes = {
    value: PropTypes.string,
    onChange: PropTypes.func,
    customClassName: PropTypes.string,
    isDisabled: PropTypes.bool,
    id: PropTypes.string
  };

  static defaultProps = {
    isDisabled: false
  };

  render() {
    return (
      <SelectBox id={ this.props.id }
        customClassName={ this.props.customClassName }
        disabled={ this.props.isDisabled }
        value={ this.props.value }
        onChange={ this.props.onChange }
        optionsList={ countryList }/>
    );
  }
}