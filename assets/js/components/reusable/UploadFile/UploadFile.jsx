import React from 'react';
import Alert from "react-s-alert";
import PropTypes from 'prop-types';

const style = {
  dropBox: {
    border: '2px dashed #ccc'
  },
  dropBoxActive: {
    border: '2px solid #ccc'
  },
  inputFile: {
    width: '0.1px',
    height: '0.1px',
    opacity: 0,
    overflow: 'hidden',
    position: 'absolute',
    zIndex: '-1'
  }
};

export default class UploadFile extends React.Component {

  static propTypes = {
    maxFileSizeInMBs: PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.number
    ]),
    handleUpload: PropTypes.func.isRequired,
    isMultipleAllowed: PropTypes.bool,
    closeModal: PropTypes.func,
  };

  constructor(props) {
    super(props);
    this.state = {
      dragEnter: false
    };
    this.handleDrop = this.handleDrop.bind(this);
    this.handleAttach = this.handleAttach.bind(this);
    this.handleUpload = this.handleUpload.bind(this);
    this.onReaderLoad = this.onReaderLoad.bind(this);
    this.handleDragToggle = this.handleDragToggle.bind(this);
    this.getDropBoxStyle = this.getDropBoxStyle.bind(this);
  }

  handleDrop(event) {
    event.preventDefault();
    event.stopPropagation();
    this.handleUpload(event.dataTransfer.files);
  }

  handleAttach() {
    this.handleUpload(this.attachment.files);
  }

  handleUpload(file) {
    const files = file;
    let fileName;
    let fileExtension;
    if (!this.props.isMultipleAllowed && files.length > 1) {
      Alert.error('More than one file is not allowed');
      return;
    }
    fileName = files[0].name.slice(0, files[0].name.lastIndexOf('.'));
    fileExtension = files[0].name.slice(files[0].name.lastIndexOf('.'), files[0].name.length).toLowerCase();
    if (!(fileExtension === '.jpeg' ||
      fileExtension === '.jpg' ||
      fileExtension === '.png' ||
      fileExtension === '.pdf' ||
      fileExtension === '.txt' ||
      fileExtension === '.doc' ||
      fileExtension === '.docx')) {
      this.props.closeModal();
      Alert.error('Invalid file format.');
      return;
    }

    const reader = new FileReader();
    reader.fileName = fileName;
    reader.fileExtension = fileExtension;
    reader.onload = this.onReaderLoad;
    if (files.length === 1) {
      reader.readAsBinaryString(files[0]);
    }
    this.setState({ dragEnter: false });
  }

  onReaderLoad(readerEvent) {
    if (readerEvent.total > 1024 * 1024 * this.props.maxFileSizeInMBs) {
      Alert.error('File size too large.');
    } else {
      this.props.handleUpload(btoa(readerEvent.target.result), readerEvent.target.fileName, readerEvent.target.fileExtension);
    }
  }

  handleDragToggle(event) {
    event.preventDefault();
    event.stopPropagation();
    this.setState({ dragEnter: !this.state.dragEnter });
  }

  handleDrag(event) {
    event.preventDefault();
    event.stopPropagation();
  }

  getDropBoxStyle() {
    if (!this.state.dragEnter) {
      return style.dropBox;
    } else {
      return style.dropBoxActive;
    }
  }

  render() {
    return (
      <div className="text-center p-4 my-4"
        onDragEnter={ this.handleDragToggle }
        onDragLeave={ this.handleDragToggle }
        onDragOver={ this.handleDrag }
        style={ this.getDropBoxStyle() }
        onDrop={ this.handleDrop }>
        <div className='form-group'>
          Drag file here to attach or <label htmlFor="attachmentLink" className="cursor-pointer text-primary">Choose
          File</label>
          <input type='file'
            id="attachmentLink"
            className='btn btn-link'
            ref={(attachment) => this.attachment = attachment}
            accept="image/*, .doc, .docx, .pdf, .txt" style={ style.inputFile } onChange={ this.handleAttach }/>
          <small className="form-text text-muted">
            .pdf, .doc, .docx, .txt, .jpeg, .jpg, .png Files Only, Max Size 10MB
          </small>
        </div>
      </div>
    );
  }
}

UploadFile.defaultProps = {
  maxFileSizeInMBs: 20,
  isMultipleAllowed: false
};