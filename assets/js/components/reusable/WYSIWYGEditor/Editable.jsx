import React from 'react';
import PropTypes from 'prop-types';

export default class Editable extends React.Component {

  static propTypes = {
    onChange: PropTypes.func.isRequired,
    content: PropTypes.node,
    editorStyle: PropTypes.object
  };

  constructor(props) {
    super(props);
    this.state = {
      html: this.props.content
    };
    this.emitChange = this.emitChange.bind(this);
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    this.setState({
      html: nextProps.content
    });
  }

  shouldComponentUpdate(nextProps) {
    return nextProps.content !== this.state.html;
  }

  emitChange() {
    const newHtml = this.editor.innerHTML;
    this.setState({ html: newHtml }, () => {
      this.props.onChange({
        target: {
          value: newHtml
        }
      });
    });
  }

  render() {
    return (
      <div
        ref={(editor) => this.editor = editor}
        style={this.props.editorStyle}
        className='form-control'
        contentEditable="true"
        dangerouslySetInnerHTML={{ __html: this.state.html }}
        onInput={this.emitChange}/>
    );
  }
}