import React from 'react';
import Editable from "./Editable.jsx";
import PreviewModal from "./PreviewModal.jsx";
import PropTypes from 'prop-types';

export default class Editor extends React.Component {

  static propTypes = {
    content: PropTypes.string.isRequired,
    onChange: PropTypes.func.isRequired,
    toolbarStyle: PropTypes.object,
    toolbarButtonSpacing: PropTypes.object,
    editorStyle: PropTypes.object,
    title: PropTypes.string
  };

  constructor(props) {
    super(props);
    this.state = {
      displayPreview: false
    };
    this.addLink = this.addLink.bind(this);
    this.print = this.print.bind(this);
  }

  addLink() {
    const linkURL = prompt('Enter a URL:', 'http://');
    if (linkURL !== null) {
      const selection = document.getSelection().toString();
      const linkText = selection === '' ? prompt('Enter the link text:', linkURL) : selection;
      if (linkText !== null)
        this.execCommand('insertHTML', `<a href="${linkURL}" target="_blank">${linkText}</a>`);
    }
  }

  execCommand(command, arg, ev) {
    if (ev && ev.preventDefault) {
      ev.preventDefault();
    }
    document.execCommand(command, false, arg);
  }

  print() {
    const WinPrint = window.open('', '', 'left=0,top=0,toolbar=0,scrollbars=0,status=0');
    WinPrint.document.write(this.props.content);
    WinPrint.document.close();
    WinPrint.focus();
    WinPrint.print();
    WinPrint.close();
  }

  render() {
    return (
      <div>
        <div style={this.props.toolbarStyle}>
          <div className="btn-group btn-group-sm btn-secondary" role="group"
            style={this.props.toolbarButtonSpacing}>
            <button type="button" className="btn btn-secondary" title="Paragraph"
              onClick={this.execCommand.bind(this, 'formatBlock', 'P')}>
              <i className='fa fa-paragraph'/>
            </button>
            <button type="button" className="btn btn-secondary" title="H1 Title"
              onClick={this.execCommand.bind(this, 'formatBlock', 'H1')}>
              <i className='fa fa-header'/> 1
            </button>
            <button type="button" className="btn btn-secondary" title="H2 Title"
              onClick={this.execCommand.bind(this, 'formatBlock', 'H2')}>
              <i className='fa fa-header'/> 2
            </button>
            <button type="button" className="btn btn-secondary" title="H3 Title"
              onClick={this.execCommand.bind(this, 'formatBlock', 'H3')}>
              <i className='fa fa-header'/> 3
            </button>
            <button type="button" className="btn btn-secondary" title="H4 Title"
              onClick={this.execCommand.bind(this, 'formatBlock', 'H4')}>
              <i className='fa fa-header'/> 4
            </button>
            <button type="button" className="btn btn-secondary" title="H5 Title"
              onClick={this.execCommand.bind(this, 'formatBlock', 'H5')}>
              <i className='fa fa-header'/> 5
            </button>
            <button type="button" className="btn btn-secondary" title="H6 Title"
              onClick={this.execCommand.bind(this, 'formatBlock', 'H6')}>
              <i className='fa fa-header'/> 6
            </button>
          </div>
          <div className="btn-group btn-group-sm btn-secondary" role="group"
            style={this.props.toolbarButtonSpacing}>
            <button type="button" className="btn btn-secondary" title="Bold"
              onClick={this.execCommand.bind(this, 'bold')}>
              <i className="fa fa-bold"/>
            </button>
            <button type="button" className="btn btn-secondary" title="Italic"
              onClick={this.execCommand.bind(this, 'italic')}>
              <i className="fa fa-italic"/>
            </button>
            <button type="button" className="btn btn-secondary" title="Underline"
              onClick={this.execCommand.bind(this, 'underline')}>
              <i className="fa fa-underline"/>
            </button>
            <button type="button" className="btn btn-secondary" title="Strike Through"
              onClick={this.execCommand.bind(this, 'strikeThrough')}>
              <i className="fa fa-strikethrough"/>
            </button>
          </div>
          <div className="btn-group btn-group-sm" role="group" style={this.props.toolbarButtonSpacing}>
            <button type="button" className="btn btn-secondary" title="Ordered List"
              onClick={this.execCommand.bind(this, 'insertOrderedList')}>
              <i className="fa fa-list-ol"/>
            </button>
            <button type="button" className="btn btn-secondary" title="Unordered List"
              onClick={this.execCommand.bind(this, 'insertUnorderedList')}>
              <i className="fa fa-list-ul"/>
            </button>
          </div>
          <div className="btn-group btn-group-sm" role="group" style={this.props.toolbarButtonSpacing}>
            <button type="button" className="btn btn-secondary" title="Justify Left"
              onClick={this.execCommand.bind(this, 'justifyLeft')}>
              <i className='fa fa-align-left'/>
            </button>
            <button type="button" className="btn btn-secondary" title="Justify Right"
              onClick={this.execCommand.bind(this, 'justifyRight')}>
              <i className='fa fa-align-right'/>
            </button>
            <button type="button" className="btn btn-secondary" title="Justify Center"
              onClick={this.execCommand.bind(this, 'justifyCenter')}>
              <i className='fa fa-align-center'/>
            </button>
            <button type="button" className="btn btn-secondary" title="Justify Full"
              onClick={this.execCommand.bind(this, 'justifyFull')}>
              <i className='fa fa-align-justify'/>
            </button>
          </div>
          <button
            type="button"
            title="Add Link"
            className="btn btn-secondary btn-sm"
            style={this.props.toolbarButtonSpacing}
            onClick={this.addLink}>
            <i className="fa fa-link"/>
          </button>
          <button
            type="button"
            title="Remove Format"
            className="btn btn-secondary btn-sm"
            style={this.props.toolbarButtonSpacing}
            onClick={this.execCommand.bind(this, 'removeFormat')}>
            <i className="fa fa-eraser"/>
          </button>
          <div className="btn-group btn-group-sm" role="group" style={this.props.toolbarButtonSpacing}>
            <button type="button" className="btn btn-secondary" title="Preview"
              onClick={()=>{this.setState({ displayPreview: true });}}>
              <i className='fa fa-file-text'/>
            </button>
            <button type="button" className="btn btn-secondary" title="Print"
              onClick={this.print}>
              <i className='fa fa-print'/>
            </button>
          </div>
        </div>
        <Editable onChange={this.props.onChange}
          content={this.props.content}
          editorStyle={this.props.editorStyle}/>
        <PreviewModal content={this.props.content}
          displayPreview={this.state.displayPreview}
          title={this.props.title}
          closePreview={()=>{this.setState({ displayPreview: false });}}/>
      </div>

    );
  }
}

Editor.defaultProps = {
  toolbarStyle: { marginBottom: 5 },
  toolbarButtonSpacing: { marginRight: 5 },
  editorStyle: { minHeight: 300, maxHeight: 350, overflowY: 'scroll' }
};