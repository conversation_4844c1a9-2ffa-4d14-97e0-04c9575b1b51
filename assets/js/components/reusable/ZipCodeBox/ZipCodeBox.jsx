import React from 'react';
import PropTypes from 'prop-types';

export default class ZipCode extends React.Component {

  static propTypes = {
    value: PropTypes.string,
    placeholder: PropTypes.string,
    onChange: PropTypes.func,
    customClassName: PropTypes.string,
    isDisabled: PropTypes.bool
  };

  static defaultProps = {
    isDisabled: false
  };

  constructor(props) {
    super(props);
    this.state = {
      value: this.props.value || ''
    };
    this.handleChange = this.handleChange.bind(this);
  }

  componentDidMount(){
    this.setState({
      value: this.props.value || ''
    });
  }

  UNSAFE_componentWillReceiveProps(nextProps){
    this.setState({
      value: nextProps.value || ''
    });
  }

  handleChange(e) {
    let zipCode = e.target.value;
    const nonNumericRegex = /[^0-9-]+/g;
    zipCode = zipCode.replace(nonNumericRegex, "").replace(/^(-.*?)-/g, "");
    if (zipCode.length > 10) {
      this.setState({ value: zipCode.substring(0, 10) }, function () {
        this.props.onChange(this.state.value);
      });
    } else {
      this.setState({ value: zipCode }, function () {
        this.props.onChange(this.state.value);
      });
    }
  }

  render() {
    return (
      <div>
        <input type="text"
          className={ `form-control ${this.props.customClassName}` }
          value={ this.state.value }
          disabled={ this.props.isDisabled }
          pattern="((^\d{5}$)|(^\d{5}-\d{4}$))"
          title="Enter a valid zip code (5 digits)"
          placeholder={ this.props.placeholder }
          onChange={ this.handleChange }/>
      </div>
    );
  }
}