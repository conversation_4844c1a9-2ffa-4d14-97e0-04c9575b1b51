import React, { useState } from "react";
import Alert from "react-s-alert";
import PropTypes from "prop-types";
import { json as ajax } from "./../../ajax.js";
import { URLS as apiUrls } from "./../reusable/Utilities/urls";
import Modal from "../../Modal.jsx";
import InputBox from "../reusable/InputBox/InputBox.jsx";

const ClaimAdjustment = (props) => { 
  const {
    claimId,
    disabled,
    loadClaim,
  } = props;

  const [amount, setAmoount] = useState(0);
  const [reason, setReason] = useState('');
  const [adjustmentModal, setAdjustmentModal] = useState(false);

  const submitClaimAdjustment = (e) => {
    if (e) {
      e.preventDefault();
    }

    if(!reason){
      Alert.warning(`Claim adjustment reason can't be empty.`);
      return;
    }

    const data = {
      id: claimId,
      amount: amount,
      reason: reason,
    };

    setAdjustmentModal(false);
    ajax(apiUrls.lwtAdjust, data, { method: 'POST' }, (res, status) => {
      if (status === 200) {
        Alert.success("Claim adjustment successful");
        loadClaim();
      } else {
        if (res.message) {
          Alert.error(res.message);    
        } else {
          Alert.error("Click the browser's Refresh button to reload the user list. If the error continues, contact your system administrator.");
        }
      }
    });
  };

  const showClaimAdjustmentModal = (e, value) => { 
    if (e) {
      e.preventDefault();
    }
    setAdjustmentModal(value);
  };

  const changeAdjustmentAmount = (value) => setAmoount(value);

  const changeAdjustmentReason = (e) => setReason(e.target.value);

  const renderClaimAdjustmentModal = () => {
    return (
      <Modal visible={adjustmentModal}
        size="medium"
        title="Claim Adjustment"
        close={(event) => showClaimAdjustmentModal(event, false)}>
        <div className="row justify-content-md-center">
          <div className="col-8">
            <div className="row my-2">
              <label className="col-4">
              Amount:
              </label>
              <div className="col-8">
                <InputBox type="Currency"
                  id="adjustment-amount-inputBox"
                  customClass=""
                  hasDefaultValue={ true }
                  value={ amount }
                  onBlur={ changeAdjustmentAmount }
                />
              </div>
            </div>
            <div className="row my-2">
              <label className="col-2">
              Reason:
              </label>
            </div>
            <div className="row my-2">
              <div className="col-12">
                <textarea type="text"
                  id="negative-text-area"
                  className="form-control"
                  rows="5"
                  value={ reason }
                  onChange={ changeAdjustmentReason } />
              </div>
            </div>
            <div className="row my-4 justify-content-center">
              <button className="btn btn-secondary mr-3"
                onClick={ (event) => showClaimAdjustmentModal(event, false) }>
              Cancel
              </button>
              <button className="btn btn-primary"
                onClick={(event) => submitClaimAdjustment(event)}>
              Submit
              </button>
            </div>
          </div>
        </div>
      </Modal>
    );
  };

  return (
    <div className="form-group row">
      <div className="col-12">
        <button className="btn btn-secondary cursor-pointer"
          style={{marginBottom: 20}}
          id="btn-negative-claim"
          disabled={disabled}
          onClick={(event) => showClaimAdjustmentModal(event, true)}>
            Claim Adjustment
        </button>
      </div>
      {renderClaimAdjustmentModal()}
    </div>
  );
};

ClaimAdjustment.propTypes = {
  claimId: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.number,
  ]),
  disabled: PropTypes.bool.isRequired,
  loadClaim: PropTypes.func.isRequired,
};

export default ClaimAdjustment;