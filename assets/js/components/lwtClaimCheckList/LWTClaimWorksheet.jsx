import React from "react";
import SelectBox from "../reusable/SelectBox/SelectBox.jsx";
import ReactTooltip from "react-tooltip";
import Alert from "react-s-alert";
import Loader from "react-loader-advanced";
import PropTypes from "prop-types";
import { json as ajax } from "./../../ajax.js";
import { scrollTo } from "../reusable/Utilities/Scroll.js";
import { CONSTANTS } from "./../reusable/Constants/constants.js";
import { URLS as apiUrls } from "./../reusable/Utilities/urls";
import Header from "./Header.jsx";
import RecordNotes from "./../reusable/RecordNotes/RecordNotes.jsx";
import AttachmentModal from "./FileAttachment.jsx";
import LWTChecklist from "./LWTChecklist.jsx";
import LineItemsInformation from "./LineItemsInformation.jsx";
import { userHasRole } from '../reusable/Utilities/userHasRole';
import ConfirmationModal from "./../reusable/ConfirmationModal/ConfirmationModal.jsx";
import UserSelectionModal from "./UserSelectionModal.jsx";
import VendorSelectionModal from "./../claimCheckList/BankSelectionModal.jsx";
import ConfirmUpdateModal from "./../claimCheckList/ConfirmUpdateModal.jsx";
import PaymentInfo from "./PaymentInfo.jsx";
import VoidCheck from "./VoidCheck.jsx";
import InputBox from "../reusable/InputBox/InputBox.jsx";
import dateFormat from "./../reusable/Utilities/dateFormat.js";
import moment from "moment";
import DatePicker from "react-datepicker";
import If from "../reusable/If/If";
import ClaimAdjustment from "./ClaimAdjustment.jsx";
import { formatCurrency } from "../reusable/Utilities/format";
import Modal from "../../Modal.jsx";
import hstore from "../reusable/Utilities/hstore.js";

const style = {
  rowStyle: {
    border: "1px solid #ccc",
    borderRadius: "0.25rem 0 0 0.25rem",
    position: "relative",
    borderRight: "none",
    marginRight: "-16px",
    zIndex: "9"
  },
  notesStyle: {
    position: 'absolute',
    right: 0,
    top: 0,
    zIndex: 9
  }
};


const optionList = {
  claimStatusManager: [
    { name: CONSTANTS.LWT_CLAIM_STATUS_DISPLAY_MAP.Inquiry, value: CONSTANTS.LWT_CLAIM_STATUS_MAP.inquiry, isDisabled: false },
    { name: CONSTANTS.LWT_CLAIM_STATUS_DISPLAY_MAP.ClaimInProcess, value: CONSTANTS.LWT_CLAIM_STATUS_MAP.claimInProcess, isDisabled: false },
    { name: CONSTANTS.LWT_CLAIM_STATUS_DISPLAY_MAP.PendingApproval, value: CONSTANTS.LWT_CLAIM_STATUS_MAP.pendingApproval, isDisabled: false },
    { name: CONSTANTS.LWT_CLAIM_STATUS_DISPLAY_MAP.PendingDenial, value: CONSTANTS.LWT_CLAIM_STATUS_MAP.pendingDenial, isDisabled: false },
    { name: CONSTANTS.LWT_CLAIM_STATUS_DISPLAY_MAP.Approved, value: CONSTANTS.LWT_CLAIM_STATUS_MAP.approved, isDisabled: false },
    { name: CONSTANTS.LWT_CLAIM_STATUS_DISPLAY_MAP.CheckWritten, value: CONSTANTS.LWT_CLAIM_STATUS_MAP.checkWritten, isDisabled: true },
    { name: CONSTANTS.LWT_CLAIM_STATUS_DISPLAY_MAP.NoResponse, value: CONSTANTS.LWT_CLAIM_STATUS_MAP.noResponse, isDisabled: false },
    { name: CONSTANTS.LWT_CLAIM_STATUS_DISPLAY_MAP.ReturnedClaim, value: CONSTANTS.LWT_CLAIM_STATUS_MAP.returnedClaim, isDisabled: false },
    { name: CONSTANTS.LWT_CLAIM_STATUS_DISPLAY_MAP.WaitingForCheck, value: CONSTANTS.LWT_CLAIM_STATUS_MAP.waitingForCheck, isDisabled: true },
    { name: CONSTANTS.LWT_CLAIM_STATUS_DISPLAY_MAP.Denied, value: CONSTANTS.LWT_CLAIM_STATUS_MAP.denied, isDisabled: false },
    { name: CONSTANTS.LWT_CLAIM_STATUS_DISPLAY_MAP.Deactivated, value: CONSTANTS.LWT_CLAIM_STATUS_MAP.deactivated, isDisabled: false }
  ],
  claimStatusAgent: [
    { name: CONSTANTS.LWT_CLAIM_STATUS_DISPLAY_MAP.Inquiry, value: CONSTANTS.LWT_CLAIM_STATUS_MAP.inquiry, isDisabled: false },
    { name: CONSTANTS.LWT_CLAIM_STATUS_DISPLAY_MAP.ClaimInProcess, value: CONSTANTS.LWT_CLAIM_STATUS_MAP.claimInProcess, isDisabled: false },
    { name: CONSTANTS.LWT_CLAIM_STATUS_DISPLAY_MAP.PendingApproval, value: CONSTANTS.LWT_CLAIM_STATUS_MAP.pendingApproval, isDisabled: false },
    { name: CONSTANTS.LWT_CLAIM_STATUS_DISPLAY_MAP.PendingDenial, value: CONSTANTS.LWT_CLAIM_STATUS_MAP.pendingDenial, isDisabled: false },
    { name: CONSTANTS.LWT_CLAIM_STATUS_DISPLAY_MAP.CheckWritten, value: CONSTANTS.LWT_CLAIM_STATUS_MAP.checkWritten, isDisabled: true },
    { name: CONSTANTS.LWT_CLAIM_STATUS_DISPLAY_MAP.NoResponse, value: CONSTANTS.LWT_CLAIM_STATUS_MAP.noResponse, isDisabled: false },
    { name: CONSTANTS.LWT_CLAIM_STATUS_DISPLAY_MAP.ReturnedClaim, value: CONSTANTS.LWT_CLAIM_STATUS_MAP.returnedClaim, isDisabled: true },
    { name: CONSTANTS.LWT_CLAIM_STATUS_DISPLAY_MAP.WaitingForCheck, value: CONSTANTS.LWT_CLAIM_STATUS_MAP.waitingForCheck, isDisabled: true },
    { name: CONSTANTS.LWT_CLAIM_STATUS_DISPLAY_MAP.Denied, value: CONSTANTS.LWT_CLAIM_STATUS_MAP.denied, isDisabled: true },
    { name: CONSTANTS.LWT_CLAIM_STATUS_DISPLAY_MAP.Deactivated, value: CONSTANTS.LWT_CLAIM_STATUS_MAP.deactivated, isDisabled: false }
  ]
};

export default class LWTClaimChecklist extends React.Component {

  constructor(props) {
    super(props);
    this.state = {
      claimObject: {},
      documentList: [],
      showApprovedConfirmation: false,
      buttonSubmit: false, 
      displayAssignOwnerModal: false,
      userList: [],
      showLoader: false,
      notes: {},
      attachments: {},
      initialStatus: '',
      attachmentParams: {
        title: '',
        fieldId: '',
        rowType: '',
        contractCode: '',
        contractNumber: ''
      },
      displayUpdateConfirmationModal: false,
      oldeOwnerId: '',
      vendorSearchValue: '',
      showManagerOwner: false,
      showReopenConfiramtionModal: false,

      displayAttachmentModal: false,
      worksheetOffset: 0,
      showNotes: false,
      notesURL: '',
      updateRecordNotes: false,
      noteTitle: '',
      selectedFieldNote: {
        itemNoteOffset: '',
        rowType: '',
        isSelected: false,
        contractDetails: {},
        fieldId: '',
        fieldNotes: {}
      },
      hideFieldNoteTextArea: true,
      fieldNoteHovered: false,
      fieldNoteClicked: false,
      noteEventType: '',
      isWorksheetUpdated: false,
      status: '',
      showAuthorizeConfirmationModal: false,
      displayUserSelectionModal: false,
      showUserListLoader: false,
      displayBackConfirmationModal: false,
      displayVendorSelectionModal: false,
      showVoidCheckConfirmationModal: false,
      displayClaimAdjustmentModal: false,
      adjustmentReason: '',
      adjustmentAmount: 0,
    };
  }

  componentDidMount = () => {
    document.title = 'TCA Portal - LWT Claims Worksheet';
    this.loadSupportingData();

    // Set up unsaved changes tracking
    if (this.props.setUnsavedChanges) {
      this.props.setUnsavedChanges(false, this.handleSaveAndNavigateHome);
    }
  };

  componentWillUnmount = () => {
    // Clear unsaved changes tracking
    if (this.props.setUnsavedChanges) {
      this.props.setUnsavedChanges(false);
    }
  };

  loadNotes = (successCallback) => {
    this.setState({ showLoader: true }, () => {
      ajax(`${apiUrls.lwtClaimGetFieldNotes.replace('__claimId__', this.props.location.query.id)}`, {}, {}, (data, status) => {
        if (status === 200) {
          this.setState({
            notes: data,
            showLoader: false
          }, () => {
            if (successCallback) {
              successCallback();
            }
          });
        } else {
          this.setState({ showLoader: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  onShowHistoryButtonClick = (notesType) => {
    let notesURL;
    let noteTitle = "";
    let hideFieldNoteTextArea = false;
    if (notesType === "contractNotes") {
      const {
        claimObject
      } = this.state;
      notesURL = `${apiUrls.contract}/${claimObject.contract_number}/notes`;
      noteTitle = "Contract Notes";
      hideFieldNoteTextArea = true;
    } else if (notesType === "claimNotes") {
      notesURL = apiUrls.lwtClaimRecordNotes;
      noteTitle = "Claim Notes";
      hideFieldNoteTextArea = false;
    } else if (notesType ==="automated") {
      notesURL = apiUrls.lwtClaimRecordNotes;
      noteTitle = "Claim Events";
      hideFieldNoteTextArea = true;
    }
    if (this.isViewOnlyRole() && !userHasRole(this.props.user, CONSTANTS.USER_ROLES.viewOnlyClaims)) {
      hideFieldNoteTextArea = true;
    }
    this.setState({
      showNotes: true,
      notesURL,
      updateRecordNotes: true,
      noteTitle,
      selectedFieldNote: {
        itemNoteOffset: '',
        rowType: '',
        isSelected: false,
        contractDetails: {},
        fieldId: '',
        fieldNotes: {}
      },
      hideFieldNoteTextArea: hideFieldNoteTextArea,
      fieldNoteHovered: false,
      fieldNoteClicked: false
    }, () => {
      scrollTo(0, 0);
    });
  };

  showRecordNotes = () => {
    const { 
      claimObject
    } = this.state;
    
    const location = { "id": this.props.location.query.id };
    if (this.state.showNotes && !this.state.selectedFieldNote.isSelected) {
      if (this.state.notesURL === `${apiUrls.contract}/${claimObject.contract_number}/notes`) {
        location["id"] = "";
        location["contract_number"] = claimObject.contract_number;
        location["productCode"] = CONSTANTS.PRODUCT_CODE_NEW_MAP.leaseWearTear;
      }
      return (
        <RecordNotes location={ location }
          updateRecordNotes={ this.state.updateRecordNotes }
          onRecordNotesUpdate={ () => {
            this.setState({ updateRecordNotes: false });
          } }
          type={ this.state.notesURL === `${apiUrls.contract}/${claimObject.contract_number}/notes` ? CONSTANTS.FIELD_TYPE.contract : (this.state.noteTitle === `Claim Notes` ? `manual`: null) }
          apiURL={ this.state.notesURL }
          hideAddNoteTextArea={ this.state.hideFieldNoteTextArea }
          hasTitle={ true }
          hasCloseButton={ true }
          title={ this.state.noteTitle }
          containerClass="col-6"
          notesStyle={ style.notesStyle }
          createdByUserID={ this.props.user.id }
          closeButtonCallback={ this.closeRecordNote }/>
      );
    } else if (CONSTANTS.FIELD_TYPE.static === this.state.selectedFieldNote.rowType && this.state.selectedFieldNote.isSelected && this.state.showNotes) {
      return (
        <RecordNotes updateRecordNotes={ this.state.updateRecordNotes }
          location={ location }
          onRecordNotesUpdate={ this.loadNotes }
          apiURL={ apiUrls.lwtClaimFieldNotes }
          hideAddNoteTextArea={ this.state.hideFieldNoteTextArea }
          type={ this.state.selectedFieldNote.rowType }
          excludePagination={ true }
          recordNotes={ this.getFieldNotes(this.state.notes, this.state.selectedFieldNote.fieldId) }
          selectedFieldNote={ this.state.selectedFieldNote }
          hasCloseButton={ !this.state.fieldNoteHovered && true }
          containerClass="col-6"
          notesStyle={ style.notesStyle }
          closeButtonCallback={ this.closeRecordNote }
          worksheetOffset={ this.state.worksheetOffset }
          eventType={ this.state.noteEventType }/>
      );
    }
  };

  closeRecordNote = () => {
    this.setState({
      selectedFieldNote: {
        itemNoteOffset: '',
        rowType: '',
        isSelected: false,
        contractDetails: {},
        fieldId: '',
        fieldNotes: {}
      },
      worksheetOffset: 0,
      showNotes: false,
      hideFieldNoteTextArea: true,
      fieldNoteHovered: false,
      fieldNoteClicked: false,
      updateRecordNotes: false,
      noteEventType: ""
    });
  };

  handleMouseEnter = (params) => {
    if (!this.state.fieldNoteClicked) {
      this.setState({
        selectedFieldNote: params,
        showNotes: true,
        hideFieldNoteTextArea: true,
        fieldNoteHovered: true,
        updateRecordNotes: false,
        worksheetOffset: this.worksheet.offsetHeight,
        noteEventType: "hover"
      });
    }
  };

  handleMouseLeave = () => {
    if (!this.state.fieldNoteClicked && this.state.fieldNoteHovered) {
      this.setState({
        selectedFieldNote: {
          itemNoteOffset: '',
          rowType: '',
          isSelected: false,
          contractDetails: {},
          fieldId: '',
          fieldNotes: {}
        },
        worksheetOffset: 0,
        showNotes: false,
        fieldNoteHovered: false,
        updateRecordNotes: false,
        noteEventType: ""
      });
    }
  };

  getClaimUserList = () => {
    this.setState({ showUserListLoader: true }, () => {
      ajax(apiUrls.userList, {}, {}, (data, status) => {
        if (status === 200) {
          this.setState({ userList: data.users.filter(user => 
            userHasRole(user, CONSTANTS.USER_ROLES.gapClaims) ||
            userHasRole(user, CONSTANTS.USER_ROLES.gapClaimsManager) ||
            userHasRole(user, CONSTANTS.USER_ROLES.accounting))
          , showUserListLoader: false });
        } else {
          this.setState({ showUserListLoader: false });
          Alert.error("Click the browser's Refresh button to reload the user list. If the error continues, contact your system administrator.");
        }
      });
    });
  };

  redirectToPrevious = () => {
    this.setState({ displayBackConfirmationModal: false }, () => {
      this.context.router.goBack();
    });
  };
  
  handleBackButtonOnClick = () => {
    if (this.state.isWorksheetUpdated) {
      this.setState({ displayBackConfirmationModal: true });
    } else {
      this.context.router.goBack();
    }
  };

  handleSaveAndNavigateHome = () => {
    this.setState({ isWorksheetUpdated: true }, () => {
      this.handleUpdateSubmit();
      // After successful save, navigate to home
      this.context.router.push('/');
    });
  };

  setWorksheetUpdated = (isUpdated) => {
    this.setState({ isWorksheetUpdated: isUpdated }, () => {
      if (this.props.setUnsavedChanges) {
        this.props.setUnsavedChanges(isUpdated, this.handleSaveAndNavigateHome);
      }
    });
  };

  handleReOpen = () => {
    const {
      initialStatus,
    } = this.state;

    // If we have any of one approved or waiting for check that means payment has been sent to the intacct we need to handle this 
    // differently than other reopen
    if (initialStatus === CONSTANTS.AUTO_CLAIM_STATUS_MAP.waitingForCheck || initialStatus === CONSTANTS.AUTO_CLAIM_STATUS_MAP.approved) {
      this.setState({
        showReopenConfiramtionModal: true,
      });
    } else {
      this.handleReOpenClaim();
    }    
  };

  handleReOpenClaim = (successCallback) => {
    this.setState({ showLoader: true,  displayUserSelectionModal: false, displayBackConfirmationModal : false, isWorksheetUpdated : false }, () => {
      ajax(`${apiUrls.lwtClaims}/${this.props.location.query.id}/reopen`, {}, { method: 'PUT' }, (data, status) => {
        if (status === 200) {
          Alert.success("Update successful.");
          if (successCallback) {
            successCallback();
          }
          this.setState({ displayModal: false, showLoader: false }, () => { this.loadClaim(); });
        } else {
          this.setState({
            showLoader: false,
          }, () => {
            Alert.error("Click the Re-Open button again. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  }

  closeReopenConfirmationModal = () => this.setState({ showReopenConfiramtionModal: false });

  renderReOpenConfirmation = () => {
    const {
      showReopenConfiramtionModal,
    } = this.state;
    return (
      <Modal visible={showReopenConfiramtionModal} close={this.closeReopenConfirmationModal}>
        <div className="col-11 text-center">
          <h5>Reopening an Approved Claim</h5>
        </div>
        <div>
          <div className="row justify-content-center">
            <div className="col-11 mt-2">
              <div>
                <p>This claim has been approved and is waiting for a check. As a manager, you can verify with accounting that the check will 
                not be written and reopen the claim. Upon approving the modified claim, a new bill number will be generated.</p>
                <p>If accounting has processed the original check,  you will need to add an adjustment to this claim.</p>
                <p>
                Are you sure you want to reopen this claim?
                </p>
              </div>
            </div>
          </div>
        </div>
        <p className='text-right'>
          <button type='button' className='btn btn-secondary' onClick={this.closeReopenConfirmationModal}>
            <i className='fa fa-ban' /> {`Cancel`}
          </button>
          {' '}
          <button type='button' className='btn btn-primary' onClick={() => this.handleReOpenClaim(this.closeReopenConfirmationModal)}>
            <i className='fa fa-check' /> {`Re-Open Claim`}
          </button>
        </p>
      </Modal>
    );
  }
 
  handleOwnerChange = (e) => {
    const {
      claimObject,
    } = this.state;
    const oldeOwnerId =  claimObject.owner_id;
    if (e.target.value) {
      this.setState({
        newOwnerName: e.target.options[e.target.selectedIndex].text,
        claimObject: {...claimObject, owner_id: parseInt(e.target.value, 10)},
        displayUpdateConfirmationModal: true,
        isWorksheetUpdated: true,
        oldeOwnerId,
      });
    }
  };

  isFormDisabled = () => {
    if (this.isViewOnlyRole()){
      return true;
    }
    const {
      claimObject: {
        owner_id: currentOwnerID,
      },
      initialStatus,
    } = this.state;

    if (initialStatus === CONSTANTS.LWT_CLAIM_STATUS_MAP.inquiry ||
      initialStatus === CONSTANTS.LWT_CLAIM_STATUS_MAP.claimInProcess ||
      initialStatus === CONSTANTS.LWT_CLAIM_STATUS_MAP.returnedClaim) {
      return false;
    }

    if ((userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaims) && !userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaimsManager) &&
      (initialStatus === CONSTANTS.LWT_CLAIM_STATUS_MAP.pendingApproval || initialStatus === CONSTANTS.LWT_CLAIM_STATUS_MAP.pendingDenial))
      || (parseInt(this.props.user.id) !== parseInt(currentOwnerID))) {
      return true;
    }
    return (this.state.initialStatus === CONSTANTS.LWT_CLAIM_STATUS_MAP.approved ||
      this.state.initialStatus === CONSTANTS.LWT_CLAIM_STATUS_MAP.checkWritten ||
      this.state.initialStatus === CONSTANTS.LWT_CLAIM_STATUS_MAP.denied ||
      this.state.initialStatus === CONSTANTS.LWT_CLAIM_STATUS_MAP.deactivated ||
      this.state.initialStatus === CONSTANTS.LWT_CLAIM_STATUS_MAP.noResponse || 
      this.state.initialStatus === CONSTANTS.LWT_CLAIM_STATUS_MAP.waitingForCheck) || false;
  };

  getStatusOptions = () => {
    const {
      user,
      user :{
        lwt_approval_limit: approvalLimit,
      }
    } = this.props;

    const {
      claimObject: {
        approved_amount: approvedAmount,
      },
    } = this.state;

    let statusOptionList = [];
    if (userHasRole(user, CONSTANTS.USER_ROLES.gapClaims) && !userHasRole(user, CONSTANTS.USER_ROLES.gapClaimsManager)) {
      statusOptionList = optionList.claimStatusAgent;

      // If amount is less than or equal to approval limit then enable approval status for agent
      const approvedIndex = statusOptionList.findIndex(obj  => obj.name === CONSTANTS.LWT_CLAIM_STATUS_DISPLAY_MAP.Approved);
      if (parseFloat(approvalLimit) >= parseFloat(approvedAmount)) {
        if (approvedIndex === -1) {
          const approvedStatus = { name: CONSTANTS.LWT_CLAIM_STATUS_DISPLAY_MAP.Approved, value: CONSTANTS.LWT_CLAIM_STATUS_MAP.approved, isDisabled: false };
          statusOptionList.splice(4, 0, approvedStatus);
        }
      } else if (approvedIndex !== -1){
        statusOptionList.splice(approvedIndex, 1);
      }
    } else if (userHasRole(user, CONSTANTS.USER_ROLES.gapClaimsManager)) {
      statusOptionList = optionList.claimStatusManager;
    }
    return statusOptionList;
  };

  loadClaim = () => {
    this.setState({ showLoader: true }, () => {
      ajax(`${apiUrls.lwtClaims}/${this.props.location.query.id}`, {}, {}, (data, status) => {
        if (status === 200) {
          this.setState({ showLoader: false, initialStatus: data.lwt_claim.status, claimObject: data.lwt_claim });
        } else {
          this.setState({ showLoader: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  hasLineItemsCorrect = () => {
    const {
      claimObject : {
        line_items: lineItems,
      },
    } = this.state;

    if (!lineItems || (lineItems && lineItems.length === 0)) {
      Alert.warning('At least one line item is required.');
      return false;
    }

    const biggerApproved = lineItems && lineItems.find(item => (item.status === CONSTANTS.LWT_CLAIM_LINE_ITEM_STATUS_MAP.approved && (parseFloat(item.approved_amount) > parseFloat(item.requested_amount))));
    if(biggerApproved) {
      Alert.warning('Approved amount is greater than requested amount, please correct it.');
      return false;
    }

    const missingApproved = lineItems && lineItems.find(item => item.status !== CONSTANTS.LWT_CLAIM_LINE_ITEM_STATUS_MAP.approved && item.status !== CONSTANTS.LWT_CLAIM_LINE_ITEM_STATUS_MAP.denied);
    if(missingApproved) {
      Alert.warning(`Make sure all line items are approved or denied status`);
      return false;
    }

    return true;
  };

  handleStatusChange = (statusType, e) => {
    const {
      initialStatus,
      claimObject,
      claimObject: {
        vendor_id: vendorID,
        invoice_number: invoiceNumber,
        loan_number: loanNumber,
        vehicle_return_date: vehicleReturnDate,
      }
    } = this.state;

    const {
      user,
    } = this.props;

    const status = e.target.value;
    
    if (initialStatus !== CONSTANTS.LWT_CLAIM_STATUS_MAP.inquiry && status === CONSTANTS.LWT_CLAIM_STATUS_MAP.inquiry) {
      Alert.warning('Status can not be reverted to Inquiry.');
      return;
    }

    if ((status === CONSTANTS.LWT_CLAIM_STATUS_MAP.pendingApproval || status === CONSTANTS.LWT_CLAIM_STATUS_MAP.approved) && !this.hasRequiredDetails()) {
      Alert.warning('Check all the required checkboxes before submitting the claim.');
      return;
    }

    if ((status === CONSTANTS.LWT_CLAIM_STATUS_MAP.pendingApproval || status === CONSTANTS.LWT_CLAIM_STATUS_MAP.approved 
        || status === CONSTANTS.LWT_CLAIM_STATUS_MAP.returnedClaim) 
        && !this.hasLineItemsCorrect()) {
      return;
    }

    if ((status === CONSTANTS.LWT_CLAIM_STATUS_MAP.pendingApproval || status === CONSTANTS.LWT_CLAIM_STATUS_MAP.approved) && (vendorID==='' || vendorID === undefined)) {
      Alert.warning('Vendor is required.');
      return;
    }

    if ((status === CONSTANTS.LWT_CLAIM_STATUS_MAP.pendingApproval || status === CONSTANTS.LWT_CLAIM_STATUS_MAP.approved) && (vehicleReturnDate === '' || vehicleReturnDate === undefined)) {
      Alert.warning('Vehicle Return Date is required.');
      return;
    }

    if ((status === CONSTANTS.LWT_CLAIM_STATUS_MAP.pendingApproval || status === CONSTANTS.LWT_CLAIM_STATUS_MAP.approved) && (loanNumber==='' || loanNumber === undefined)) {
      Alert.warning('Loan number is required.');
      return;
    }

    if ((status === CONSTANTS.LWT_CLAIM_STATUS_MAP.pendingApproval || status === CONSTANTS.LWT_CLAIM_STATUS_MAP.approved) && (invoiceNumber==='' || invoiceNumber === undefined)) {
      Alert.warning('Invoice number is required.');
      return;
    }

    // If user is agent and not a manager then show manager selection pop up
    if (userHasRole(user, CONSTANTS.USER_ROLES.gapClaims) && !userHasRole(user, CONSTANTS.USER_ROLES.gapClaimsManager) && status === CONSTANTS.LWT_CLAIM_STATUS_MAP.pendingApproval) {
      this.setState({claimObject: {...claimObject, status: status}, showManagerOwner: true }, () => {
        this.getClaimUserList();
      });
    }

    if (status === CONSTANTS.LWT_CLAIM_STATUS_MAP.approved &&  claimObject.status !== CONSTANTS.LWT_CLAIM_STATUS_MAP.approved) {
      this.setState({ showApprovedConfirmation: true, buttonSubmit: false, isWorksheetUpdated: true});
      return;
    }

    this.setState({ claimObject: {...claimObject, status: status}, buttonSubmit: false, isWorksheetUpdated: true });    
  };

  handleApprove = () => {
    const {
      claimObject,
    } = this.state;
    this.setState({ claimObject: {...claimObject, status: CONSTANTS.LWT_CLAIM_STATUS_MAP.approved}, buttonSubmit: true, isWorksheetUpdated: true });
  }

  hasRequiredDetails = () => {
    const { claimObject } = this.state;
    return (
      claimObject.finance_contract &&
      claimObject.completed_claim_form &&
      claimObject.vehicle_condition_report &&
      claimObject.vin_plate_images &&
      claimObject.wear_and_tear_damage_images &&
      claimObject.final_invoice_from_lessor
    );
  };

  loadSupportingData = () => {
    this.loadClaim();
    this.loadAttachments();
    this.loadNotes();
    this.loadDocuments();
  }

  loadDocuments = () => {
    this.setState({ showLoader: true }, () => {
      ajax(`${apiUrls.lwtClaimsDocumentTypes}`, {}, {}, (data, status) => {
        if (status === 200) {
          this.setState({
            documentList: data.lwt_documents,
            showLoader: false
          });
        } else {
          this.setState({ showLoader: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  }

  createNewLineItem = () => {
    const {
      claimObject,
    } = this.state;
    const url = apiUrls.lwtLineItem.replace('__claimId__', claimObject.id);
    this.setState({ showLoader: true }, () => {
      ajax(url, {}, { method: 'POST' }, (data, status) => {
        if (status === 200) {
          this.getLineItem(data.line_item_id);
        } else {
          this.setState({ showLoader: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  getLineItem(id) {
    const {
      claimObject,
    } = this.state;

    const url = apiUrls.lwtLineItem.replace('__claimId__', claimObject.id);
    ajax(`${url}/${id}`, {}, { method: 'GET' }, (data, status) => {
      if (status === 200) {
        this.pushLineItem(data.line_item);
      } else {
        this.setState({ showLoader: false }, () => {
          Alert.error("Click the browser's Refresh button to reload the data. If the error continues, contact your system administrator.");
        });
      }
    });
  }

  handleLineItemUpdate = (lineItemId, field, value) => {
    const {
      claimObject,
    } = this.state;
    
    let lineItemsClone = claimObject.line_items.map(obj => Object.assign({},obj));
    let lineItem = lineItemsClone.find(item => item.id === lineItemId);
    if (lineItem) {
      if (lineItem.status === CONSTANTS.LWT_CLAIM_LINE_ITEM_STATUS_MAP.approved && field === "approved_amount") {
        if (parseFloat(value) > parseFloat(lineItem.requested_amount)) {
          Alert.warning(`Approved amount is greater than requested amount, please correct it.`);
        }
      }

      if (lineItem.status === CONSTANTS.LWT_CLAIM_LINE_ITEM_STATUS_MAP.approved && field === "requested_amount") {
        if (parseFloat(value) > parseFloat(lineItem.requested_amount)) {
          Alert.warning(`Requested amount is greater than approved amount.`);
        }
      }

      let newLineItems = lineItemsClone.filter(item => item.id !== lineItemId);
      lineItem[field] = value;
      newLineItems = [...newLineItems, lineItem];
      newLineItems.sort((a, b) => a.id - b.id);

      if (field === "is_images_received" && !value && lineItem.status === CONSTANTS.LWT_CLAIM_LINE_ITEM_STATUS_MAP.approved) {
        Alert.warning(`Item already approved, move it to other status before uncheking images recieved.`);
        return;
      }

      if (field === "status") {
        if (value === CONSTANTS.LWT_CLAIM_LINE_ITEM_STATUS_MAP.approved) {
          if (parseFloat(lineItem.approved_amount) > parseFloat(lineItem.requested_amount)) {
            Alert.warning(`Approved amount is greater than requested amount, please correct it.`);
            return;
          }
  
          if (!lineItem.is_images_received) {
            Alert.warning(`Please check images received box.`);
            return;
          }
  
          if (!lineItem.description) {
            Alert.warning(`Please provide description for line item.`);
            return;
          }
        }
        const newRequestedTotal = newLineItems.map(item => (item.status === CONSTANTS.LWT_CLAIM_LINE_ITEM_STATUS_MAP.approved && parseFloat(item.requested_amount)) || 0).reduce((prev, next) => prev + next);
        const newApprovedTotal = newLineItems.map(item => (item.status === CONSTANTS.LWT_CLAIM_LINE_ITEM_STATUS_MAP.approved && parseFloat(item.approved_amount)) || 0).reduce((prev, next) => prev + next);
        if (newRequestedTotal > newApprovedTotal) {
          Alert.warning(`Requested amount is greater than approved amount.`);
        }

        if (newRequestedTotal < newApprovedTotal) {
          Alert.warning(`Approved amount is greater than requested amount.`);
        }

        if (parseFloat(newRequestedTotal) > 5000) {
          Alert.warning('Claim requested amount is more than $5000.');
        }
        this.setState({ claimObject: {...claimObject, line_items: newLineItems, requested_amount:newRequestedTotal, approved_amount: newApprovedTotal }, isWorksheetUpdated:true });
      } else {
        if (lineItem.status === CONSTANTS.LWT_CLAIM_LINE_ITEM_STATUS_MAP.approved && field === "approved_amount") {
          const requestedAmout = newLineItems.map(item => (item.status === CONSTANTS.LWT_CLAIM_LINE_ITEM_STATUS_MAP.approved && parseFloat(item.requested_amount)) || 0).reduce((prev, next) => prev + next);
          const approvedAmount = newLineItems.map(item => (item.status === CONSTANTS.LWT_CLAIM_LINE_ITEM_STATUS_MAP.approved && parseFloat(item.approved_amount)) || 0).reduce((prev, next) => prev + next);
          if (requestedAmout > approvedAmount) {
            Alert.warning(`Requested amount is greater than approved amount.`);
          }
  
          if (requestedAmout < approvedAmount) {
            Alert.warning(`Approved amount is greater than requested amount.`);
          }

          if (parseFloat(requestedAmout) > 5000) {
            Alert.warning('Claim requested amount is more than $5000.');
          }
      
          this.setState({ claimObject: {...claimObject, requested_amount:requestedAmout, approved_amount: approvedAmount, line_items: newLineItems }, isWorksheetUpdated:true });
        } else {
          this.setState({ claimObject: {...claimObject, line_items: newLineItems}, isWorksheetUpdated:true });
        }
      }

      if (field === 'manager_approval') {
        this.updateLineItem(lineItem);
      }
    }
  };

  updateLineItem = (lineItem) => {
    const url = apiUrls.lwtLineItemUpdate.replace('__lineItemId__', lineItem.id);
    this.setState({ showLoader: true }, () => {
      ajax(url, lineItem , { method: 'PUT' }, (data, status) => {
        if (status === 200) {
          if (lineItem.manager_approval) {
            Alert.success('Successfully approved line item.');
          } else {
            Alert.success('Successfully un-approved line item.');
          }
          this.setState({ showLoader: false });
        } else {
          if (data.message) {
            Alert.error(data.message);
          } else {
            Alert.error("Click the browser's Refresh button to reload the data. If the error continues, contact your system administrator.");
          }
          this.setState({ showLoader: false });
        }
      });
    });
  }

  handleLineItemDelete = (lineItemId) => {
    const url = apiUrls.lwtLineItemDelete.replace('__lineItemId__', lineItemId);
    this.setState({ showLoader: true }, () => {
      ajax(url, {}, { method: 'DELETE' }, (data, status) => {
        if (status === 200) {
          this.popLineItem(data.line_item_id);
        } else {
          this.setState({ showLoader: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  popLineItem = (line_item_id) => {
    const {
      claimObject,
      claimObject : {
        line_items: lineItems,
      }
    } = this.state;
    let newLineItems = [];
    if (lineItems && lineItems.length > 0) {
      newLineItems = lineItems.filter(item => item.id !== line_item_id);
    }    

    if (newLineItems && newLineItems.length > 0) {
      const requestedAmout = newLineItems.map(item => (item.status === CONSTANTS.LWT_CLAIM_LINE_ITEM_STATUS_MAP.approved 
        && parseFloat(item.requested_amount)) || 0).reduce((prev, next) => prev + next);
      const approvedAmount = newLineItems.map(item => (item.status === CONSTANTS.LWT_CLAIM_LINE_ITEM_STATUS_MAP.approved 
        && parseFloat(item.approved_amount)) || 0).reduce((prev, next) => prev + next);
     
      this.setState({ claimObject:{...claimObject, line_items: newLineItems, requested_amount: requestedAmout, approved_amount: approvedAmount }, isWorksheetUpdated:true, showLoader: false});
    } else {
      this.setState({ claimObject:{...claimObject, line_items: newLineItems }, isWorksheetUpdated:true, showLoader: false});
    }
    
  };

  pushLineItem = (lineItem) => {
    const {
      claimObject,
      claimObject : {
        line_items: lineItems,
        status,
      }
    } = this.state;
    let newLineItems = [];
    if (lineItems && lineItems.length > 0) {
      newLineItems = [...lineItems];
    }
    newLineItems = [...newLineItems, lineItem];   
    
    // If first line item and status still inquiry update status to Claim in progress
    if (newLineItems.length === 1 && status === CONSTANTS.LWT_CLAIM_STATUS_MAP.inquiry) {
      this.setState({ 
        claimObject: { 
          ...claimObject,
          line_items: newLineItems,
          status: CONSTANTS.LWT_CLAIM_STATUS_MAP.claimInProcess
        },
        isWorksheetUpdated:true,
        showLoader: false,
      }, () => this.updateClaim(() => { Alert.success('Status changed to Claim In Progress'); }));  
    } else {
      this.setState({ claimObject:{...claimObject, line_items: newLineItems}, isWorksheetUpdated:true, showLoader: false });
    }
  };

  handleInvoiceChange = (e) => {
    const {
      claimObject,
    } = this.state;
    this.setState({ claimObject : { ...claimObject, invoice_number: e}, isWorksheetUpdated:true });
  };

  handleLoanNumberChange = (e) => {
    const {
      claimObject,
    } = this.state;
    this.setState({ claimObject : { ...claimObject, loan_number: e}, isWorksheetUpdated:true });
  };

  renderVendorSearchBox = () => {
    const  { 
      claimObject,
      vendorSearchValue,
    } = this.state;

    const COMPONENT_MAP = CONSTANTS.LWT_WORKSHEET_COMPONENT_MAP;
    const isDisabled = this.isFormDisabled();
    if (claimObject.vendor_id) {
      if (isDisabled) {
        return (
          <div className="col-12 px-0 col-12 d-inline-flex align-items-center">
            <strong>{ claimObject.vendor_name }</strong>
          </div>
        );
      }
      return (
        <div className="col-12 px-0 col-12 d-inline-flex align-items-center">
          <a href=''
            id={ `updateBank` }
            onClick={ this.searchVendor }>
            <strong>{ claimObject.vendor_name }</strong>
          </a>
        </div>
      );
    }
    return (
      <div className="col-12 px-0 col-12 d-inline-flex align-items-center">
        <InputBox type="Text"
          id={ `${COMPONENT_MAP[ 'vendorName' ][ 'rowId' ]}_inputBox` }
          customClass="col-10 px-0"
          hasDefaultValue={ true }
          isDisabled={ isDisabled }
          value={ vendorSearchValue || claimObject.vendor_id}
          onChange={ this.onVendorChange }
          onKeyUp={ this.onEnterKeyPress }
          onBlur={ this.onVendorChange }
        />
        <button
          disabled={ isDisabled }
          type="button"
          className={ `cursor-pointer col-2 px-0 btn ${!isDisabled ? "text-primary" : ""} ` }
          onClick={ this.searchVendor }>
          <i className="fa fa-search"/>
        </button>
      </div>
    );
  };

  onEnterKeyPress = (e) => {
    if (e.keyCode === 13 || e.which === 13) {
      this.searchVendor();
    }
  }

  onVendorChange = (searchString) => {
    this.setState({ vendorSearchValue: searchString, isWorksheetUpdated:true });
  };

  searchVendor = (e) => {
    if (e) {
      e.preventDefault();
    }
    const  { 
      claimObject,
    } = this.state;

    this.setState({ displayVendorSelectionModal: true, vendorSearchValue: claimObject.vendor_id });
  };

  onVendorSelect = (data) => {
    const  { 
      claimObject,
    } = this.state;
    this.setState({ 
      displayVendorSelectionModal: false,
      claimObject: {...claimObject, vendor_id: data.vendor_id, vendor_name: data.name,
        vendor_address: data.address1, vendor_postal_code: data.zip, vendor_city: data.city, 
        vendor_state: data.state },
      isWorksheetUpdated:true,
    });
  };

  handleReturnDateChange = (date) => {
    const {
      claimObject,
      claimObject: {
        date_of_claim_received: claimOpenDate,
      }
    } = this.state;

    let val = moment.utc(new Date()).format(dateFormat.backendDateFormat);
    
    if (date) {
      date = date.format(dateFormat.displayDateFormat);
      val = moment.utc(date, dateFormat.displayDateFormat).format(dateFormat.backendDateFormat);
      const openDate = moment.utc(claimOpenDate);
      const returnDate = moment.utc(date);

      if (openDate.diff(returnDate, 'days') > 90) {
        Alert.warning('Vehicle return date is more than 90 days from claim opened date.');
      }
    }
    
    this.setState({ claimObject: {...claimObject, vehicle_return_date: val}, isWorksheetUpdated:true });
  };

  handleIsInProgressChange = (e) => {
    const {
      claimObject,
    } = this.state;
    this.setState({ claimObject: { ...claimObject, in_progress: e.target.checked }, isWorksheetUpdated:true });
  }

  handleFieldNoteClick = (params) => {
    this.setState({
      selectedFieldNote: params,
      showNotes: true,
      hideFieldNoteTextArea: false,
      fieldNoteHovered: false,
      fieldNoteClicked: true,
      updateRecordNotes: false,
      worksheetOffset: this.worksheet.offsetHeight,
      noteEventType: "click"
    }, () => {
      scrollTo(0, this.state.selectedFieldNote.itemNoteOffset);
    });
  };

  getFieldNotes = (notes, id) => {
    if (notes.field_notes && notes.field_notes.length > 0) {
      for (let index = 0; index < notes.field_notes.length; index++) {
        if (notes.field_notes[index]['field_id'] === id) {
          return notes.field_notes[index];
        }
      }
    }
  };

  getFieldDocs = (docs, id) => {
    if (docs.lwt_documents && docs.lwt_documents.length > 0) {
      for (let index = 0; index < docs.lwt_documents.length; index++) {
        if (docs.lwt_documents[index]['id'] === id && docs.lwt_documents[index]['documents'] && docs.lwt_documents[index]['documents'].length > 0) {
          return docs.lwt_documents[index]['documents'];
        }
      }
    }
  };

  handleDocumentCheckboxSelection = (name) => {
    const {
      claimObject,
      attachments: {
        lwt_documents: lwtDocuments,
      }
    } = this.state;
    const isSelected = claimObject[name];
    if (!isSelected) {
      const doc = lwtDocuments.find(obj => obj.key === name);
      if (doc && doc.required && doc.documents && doc.documents.length > 0) {
        this.setState({ claimObject: { ...claimObject, [name]: !isSelected } });    
      } else {
        Alert.warning('This is required document you need to upload the document before marking it as checked.');
      }
    } else {
      this.setState({ claimObject: { ...claimObject, [name]: !isSelected } });
    }
  };

  handleAttachmentClick = (attachmentParams) => {
    this.setState({
      displayAttachmentModal: true,
      attachmentParams
    });
  };

  hasAttachment = () => {
    return (this.state.attachments && this.state.attachments.lwt_documents && this.state.attachments.lwt_documents.length < 6);
  };

  deleteAttachment = (id) => {
    this.setState({ showLoader: true }, () => {
      ajax(`${apiUrls.lwtClaimDocuments}/${id}`, {}, { method: 'DELETE' }, (data, status) => {
        if (status === 200) {
          this.setState({ showLoader: false }, () => {
            this.loadAttachments(() => { this.onAttachmentReload('delete', data.update_document_flag); });
          });
        } else {
          this.setState({ showLoader: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  closeAttachmentModal = (isSuccess, isFailure) => {
    const {
      claimObject,
    } = this.state;

    this.setState({
      displayAttachmentModal: false,
      attachmentParams: {
        title: '',
        docmentTypeId: '',
        rowType: '',
        contractCode: '',
        contractNumber: ''
      }
    }, () => {
      if (isSuccess) {
        this.loadAttachments(() => {this.onAttachmentReload('add', claimObject);});
      }
      if (isFailure) {
        Alert.error("Click the browser's Refresh button to reload. If the error continues, contact your system administrator.");
      }
    });
  };

  loadAttachments = (successCallback) => {
    this.setState({ showLoader: true }, () => {
      ajax(`${apiUrls.lwtClaimGetDocuments.replace('__documentId__', this.props.location.query.id)}`, {}, {}, (data, status) => {
        if (status === 200) {
          this.setState({
            attachments: data.docs,
            showLoader: false
          }, () => {
            if (successCallback) {
              successCallback();
            }
          });
        } else {
          this.setState({ showLoader: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  onAttachmentReload = (type, data) => {
    const {
      attachments,
      claimObject,
      claimObject: {
        status,
      },
    } = this.state;
    this.setState({ showLoader: false }, () => {
      if (type === 'add') {
        let docs = [].concat.apply([],attachments.lwt_documents.map(obj => obj.documents));
        docs = docs.filter(obj => obj !== null);
        // If first document and status still inquiry update status to Claim in progress
        if(docs.length === 1 && status === CONSTANTS.LWT_CLAIM_STATUS_MAP.inquiry) {
          this.setState({
            isWorksheetUpdated: true,
            claimObject: {
              ...claimObject,
              documents: docs,
              status: CONSTANTS.LWT_CLAIM_STATUS_MAP.claimInProcess
            }
          }, () => this.updateClaim(() => { Alert.success('Status changed to Claim In Progress'); }) );
        } else {
          this.setState({ isWorksheetUpdated: true, claimObject: {...claimObject, documents: docs } });
        }
        Alert.success("Document uploaded successfully.");
      } else if (type === 'delete') {
        this.handleDeletedDocument(data);
      } else {
        Alert.success("Document deleted successfully.");
      }
    });
  };

  handleDeletedDocument = (update_document_flag) => {
    if (update_document_flag) {
      this.loadClaim();
    }
  };

  handleUpdateSubmit = () => {
    const {
      claimObject,
    } = this.state;
    if (this.state.initialStatus !== CONSTANTS.STATUS_MAP.waitingForAuthorization && claimObject.status === CONSTANTS.STATUS_MAP.waitingForAuthorization) {
      this.setState({ displayUserSelectionModal : true, displayBackConfirmationModal : false }, () => {
        this.getClaimUserList();
      });
    } else {
      this.updateClaim();
    }
  };

  updateClaim = (successCallback) => {
    const {
      claimObject,
      initialStatus,
    } = this.state;
    if (claimObject.status === CONSTANTS.STATUS_MAP.inquiry && this.hasAttachment()) {
      claimObject.status = CONSTANTS.LWT_CLAIM_STATUS_MAP.claimInProcess;
    }

    let data = {...claimObject, lender_id: claimObject.vendor_id};
    this.setState({ showLoader: true,  displayUserSelectionModal: false, displayBackConfirmationModal : false, isWorksheetUpdated : false }, () => {
      ajax(`${apiUrls.lwtClaims}/${this.props.location.query.id}`, data, { method: 'PUT' }, (data, status) => {
        if (status === 200) {
          if (successCallback) {
            successCallback();
          } else {
            Alert.success("Update successful.");
          }
          this.setState({ displayModal: false, showLoader: false }, () => {this.loadClaim();});
        } else {
          this.setState({
            showLoader: false,
            claimObject: {...claimObject, status: initialStatus },
          }, () => {
            if (data.errors) {
              Object.values(data.errors).forEach(err => Alert.warning(err));
              return;
            }
            Alert.error("Click the Update button again. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  handleAssignButtonClick = () => {
    this.setState({ displayAssignOwnerModal: true }, () => {
      this.getClaimUserList();
    });
  };

  cancelUpdate = () => {
    const {
      claimObject,
      claimObject: {
        status,
      },
      oldeOwnerId,
      initialStatus,
      showManagerOwner,
    } = this.state;
    if (oldeOwnerId) {
      // If user cancels manager assignment revert back the status
      if (showManagerOwner && status === CONSTANTS.LWT_CLAIM_STATUS_MAP.pendingApproval) {
        this.setState({ claimObject:{...claimObject, owner_id: oldeOwnerId, status: initialStatus }, displayUpdateConfirmationModal: false, displayAssignOwnerModal: false, showManagerOwner: false });
      } else {
        this.setState({ claimObject:{...claimObject, owner_id: oldeOwnerId }, displayUpdateConfirmationModal: false, displayAssignOwnerModal: false, showManagerOwner: false });
      }
    } else {
      if (showManagerOwner && status === CONSTANTS.LWT_CLAIM_STATUS_MAP.pendingApproval) {
        this.setState({ claimObject:{...claimObject, status: initialStatus }, displayUpdateConfirmationModal: false, displayAssignOwnerModal: false, showManagerOwner: false });
      } else {
        this.setState({ displayUpdateConfirmationModal: false, displayAssignOwnerModal: false, showManagerOwner: false });
      }
    }
  };

  confirmUpdate = () => {
    this.setState({ displayUpdateConfirmationModal: false, displayAssignOwnerModal: false, showManagerOwner: false }, () => {
      this.updateClaim(() => Alert.success('User assigned sucessfully.'));
    });
  };

  isViewOnlyRole = () => {
    let r = CONSTANTS.USER_ROLES;
    let u = this.props.user;
    const hasReadRole = hstore.hasAny(u.roles, [r.accounting, r.viewOnlyClaims]);
    const hasWriteRole = hstore.hasAny(u.roles, [r.gapClaims,r.gapClaimsManager]);
    return hasReadRole && !hasWriteRole;
  };

  handleRequestedChange = (value) => {
    const {
      claimObject,
    } = this.state;
    this.setState({
      claimObject: {
        ...claimObject,
        requested_amount: value,
      },
    });

    if (parseFloat(value) > 5000) {
      Alert.warning('Claim requested amount is more than $5000.');
    }

    if (parseFloat(value) > parseFloat(claimObject.approved_amount)) {
      Alert.warning('Requested amount is greater than approved amount.');
    } else if (parseFloat(value) < parseFloat(claimObject.approved_amount)) {
      Alert.warning('Approved amount is greater than requested amount.');
    }
  };

  handleAssignToMe = () => {
    const {
      claimObject,
    } = this.state;

    const {
      user,
    } = this.props;
    
    this.setState({
      claimObject: {...claimObject, owner_id: parseInt(user.id, 10)},
    }, () => this.updateClaim(() => Alert.success('User assigned sucessfully.')));
  }

  renderClaimDetails = (claimObject, isDisabled, showReOpenButton) => {
    const {
      buttonSubmit,
      initialStatus, 
    } = this.state;

    let renderAssignOwnerButton = false;
    if ((userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaims) &&
      (initialStatus === CONSTANTS.LWT_CLAIM_STATUS_MAP.inquiry ||
       initialStatus === CONSTANTS.LWT_CLAIM_STATUS_MAP.returnedClaim ||
       initialStatus === CONSTANTS.LWT_CLAIM_STATUS_MAP.claimInProcess)) || 
       (
         userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaimsManager) 
       )
    ) {
      renderAssignOwnerButton = true;
    }

    let renderAssignMe = !this.isViewOnlyRole() && (this.props.user.id !== claimObject.owner_id && ((
      claimObject.status === CONSTANTS.LWT_CLAIM_STATUS_MAP.inquiry ||
      claimObject.status === CONSTANTS.LWT_CLAIM_STATUS_MAP.returnedClaim ||
      claimObject.status === CONSTANTS.LWT_CLAIM_STATUS_MAP.claimInProcess
    ) ||
      (
        userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaimsManager) 
      )));

    return (
      <Header
        claimObject={ claimObject }
        user={this.props.user}
        nextButtonOnClick={ showReOpenButton ? this.handleReOpen : this.handleUpdateSubmit }
        backButtonOnClick={ this.handleBackButtonOnClick }
        showHistoryButtonClick={ this.onShowHistoryButtonClick }
        nextButtonDisabled={ isDisabled && !showReOpenButton }
        nextButtonText={ showReOpenButton ? 'Re-Open' : buttonSubmit ? 'Submit' : 'Update' }
        nextButtonId={ showReOpenButton ? 'btn-next' : 'btn-reopen' }
        claimStatus={ initialStatus }
        assignButtonOnClick={this.handleAssignButtonClick}
        renderAssignOwnerButton={ renderAssignOwnerButton }
        isFinanceUser={this.isViewOnlyRole()}
        showSyncGap={ this.state.showSyncGap && !showReOpenButton }
        renderAssignMe={renderAssignMe}
        handleAssignToMe={this.handleAssignToMe} 
        showVoidCheckButton={ userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaimsManager) && this.state.initialStatus === CONSTANTS.LWT_CLAIM_STATUS_MAP.checkWritten && !claimObject.adjustment_exists }
        handleOnClickVoidCheck={ () => {
          if (claimObject.status === CONSTANTS.LWT_CLAIM_STATUS_MAP.checkWritten) {
            this.setState({ showVoidCheckConfirmationModal: true });
          }
        } }/>
    );
  };

  shouldShowReopen = () => {
    if (this.isViewOnlyRole()) {
      return false;
    }
    const {
      initialStatus,
    } = this.state;
    const {
      user,
    } = this.props;
    
    return (((initialStatus === CONSTANTS.LWT_CLAIM_STATUS_MAP.denied 
      || initialStatus === CONSTANTS.LWT_CLAIM_STATUS_MAP.deactivated
      || initialStatus === CONSTANTS.LWT_CLAIM_STATUS_MAP.noResponse
      || initialStatus === CONSTANTS.LWT_CLAIM_STATUS_MAP.waitingForCheck) &&
        userHasRole(user, CONSTANTS.USER_ROLES.gapClaimsManager)) ||
    ((initialStatus === CONSTANTS.LWT_CLAIM_STATUS_MAP.noResponse) && 
    userHasRole(user, CONSTANTS.USER_ROLES.gapClaims)));
  }

  render() {
    const { 
      claimObject,
      claimObject :{
        id: claimId,
        customer,
        line_items: lineItems,
        in_progress: inProgress,
        owner_id: currentOwnerID,
        status,
        vehicle_return_date: vehicleReturnDate,
        invoice_number: invoiceNumber,
        loan_number: loanNumber,
      },
      initialStatus,
      showApprovedConfirmation,
      displayAssignOwnerModal,
      showUserListLoader,
      displayUpdateConfirmationModal,
      newOwnerName,
      showLoader,
      vendorSearchValue,
      showManagerOwner,
    } = this.state;

    const {
      user,
    } = this.props;

    const spinnerMessage = <p className='text-center'><i className="fa fa-refresh fa-spin"/> Loading...</p>;
    const longTextStyle = {
      "maxWidth": "130px",
      "overflow": "hidden",
      "textOverflow": "ellipsis",
      "whiteSpace": "nowrap"
    };

    const isDisabled = this.isFormDisabled();
    const vrd = vehicleReturnDate ? moment.utc(vehicleReturnDate).format(dateFormat.displayDateFormat) : null;
    const isNextDisabled = this.isViewOnlyRole() || ((isDisabled && (userHasRole(user, CONSTANTS.USER_ROLES.gapClaimsManager)))
      || (initialStatus === CONSTANTS.LWT_CLAIM_STATUS_MAP.noResponse)
      || (!userHasRole(user, CONSTANTS.USER_ROLES.gapClaimsManager) &&
      (initialStatus === CONSTANTS.LWT_CLAIM_STATUS_MAP.pendingApproval ||
        initialStatus === CONSTANTS.LWT_CLAIM_STATUS_MAP.pendingDenial)));
    const showReOpenButton = this.shouldShowReopen();
    return (
      <Loader show={ showLoader } message={ spinnerMessage }>
        <section className="claim-worksheet">
          {this.renderClaimDetails(claimObject, isNextDisabled, showReOpenButton)}
          {this.renderReOpenConfirmation()}
          <section className="mt-3 pt-3">
            <div className="row mt-1">
              <section className="col">
                <div className="form-group row">
                  <div className="col-6">
                    <label className="col-form-label col-form-label-sm">
                      Invoice Number
                    </label>
                  </div>
                  <InputBox type="Text"
                    id="invoice_inputBox"
                    isDisabled={ isDisabled }
                    customClass="col-6"
                    value={ invoiceNumber}
                    onChange={ this.handleInvoiceChange}
                  />
                </div>
                <div className="form-group row">
                  <div className="col-6">
                    <label className="col-form-label col-form-label-sm">
                      Vehicle Returned Date
                    </label>
                  </div>
                  <span className="input-group input-group-sm col-6">
                    <DatePicker
                      disabled={ isDisabled }
                      selected={ vrd && moment(vrd, dateFormat.displayDateFormat)}
                      id="vehicle_returned_date_dateInputBox"
                      dateFormat={ dateFormat.displayDateFormat }
                      onChange={ this.handleReturnDateChange }
                      maxDate={moment()}
                      className="form-control form-control-sm date-field"/>
                  </span>
                </div>
                <div className="form-group row">
                  <div className="col-6">
                    <label className="col-form-label col-form-label-sm">
                      Loan Number
                    </label>
                  </div>
                  <InputBox type="Text"
                    id="loan_number_inputBox"
                    isDisabled={ isDisabled }
                    customClass="col-6"
                    value={ loanNumber }
                    onChange={ this.handleLoanNumberChange}
                  />
                </div>
              </section>
              <section className="col">
                <p className="mb-0 col-form-label-sm">
                  <strong>Lender</strong>
                </p>
                <div className="col-form-label-sm">
                  {this.renderVendorSearchBox()}
                </div>  
                <p className="mb-0 col-form-label-sm">
                  {claimObject.vendor_address || ''}
                </p>
                <p className="mb-0 col-form-label-sm">
                  {`${claimObject.vendor_city || ''} ${claimObject.vendor_state || ''} ${claimObject.vendor_postal_code || ''}`}
                </p>
                <p className="col-form-label-sm">
                  {claimObject.vendor_id || ''}
                </p>      
       
              </section>
              <section className="col">
                <div className="d-inline-flex">
                  <a href={ `/gap-contract/${claimObject.contract_number}?active_tab=customer` }
                    id="link-customer-name"
                    data-tip
                    data-for="customer_name"
                    style={ longTextStyle }
                    target="_blank"
                    rel="noopener noreferrer">
                    {customer && `${customer.first_name + ' ' + customer.last_name}` }
                    <ReactTooltip id={ `customer_name` } aria-haspopup='true'>
                      <p className="text-center">{customer && `${customer.first_name + ' ' + customer.last_name}`}</p>
                    </ReactTooltip>
                  </a>
                </div>
                <p className="mb-0 col-form-label-sm">
                  {customer && customer.street_address}
                </p>
                <p className="mb-0 col-form-label-sm">
                  {`${customer && customer.city}, ${customer && customer.state} ${customer && customer.postal_code}`}
                </p>
                <p className="col-form-label-sm">
                  {customer && customer.phone_number}
                </p>      
                <a href={ `/gap-contract/${claimObject.contract_number}?active_tab=vehicle` }
                  id="link-vin"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="col-form-label-sm">
                  {claimObject.vin}
                </a>
                <p className="col-form-label-sm mb-2">
                  {`${claimObject.year} ${claimObject.make} ${claimObject.model}`}
                </p>
              </section>
              <section className="col">
                <p className="mb-0 col-form-label-sm">
                  {claimObject.contract_lender}
                </p>
                <p className="mb-0 col-form-label-sm">
                  {claimObject.contract_lender_address}
                </p>
                <p className="mb-0 col-form-label-sm">
                  {`${claimObject.contract_lender_city}, ${claimObject.contract_lender_state} ${claimObject.contract_lender_postal_code}`}
                </p>
              </section>
            </div>

            <section className="row mt-1">
              <section className="col-12" id="parentDiv" ref={ (worksheet) => this.worksheet = worksheet }>
                <form>
                  <fieldset>
                    <label className="form-check form-check-label col-form-label col-form-label-sm">
                      <input type="checkbox"
                        className="form-check-input"
                        id="inProgressCheckBox"
                        checked={ inProgress }
                        onChange={ this.handleIsInProgressChange }
                        disabled={ isDisabled }/>
                      <span>In Progress</span>
                    </label>
                    <LWTChecklist
                      claimObject={ claimObject }
                      onVendorChange={ this.onVendorChange }
                      searchVendor={ this.searchVendor }
                      onCursorToggle={ this.handleDocumentCheckboxSelection }
                      getFieldNotes={ this.getFieldNotes }
                      getFieldDocs={ this.getFieldDocs }
                      attachments={ this.state.attachments }
                      notes={ this.state.notes }
                      selectedFieldNote={ this.state.selectedFieldNote }
                      handleItemNoteClick={ !this.isViewOnlyRole() && this.handleFieldNoteClick }
                      handleMouseEnter={ this.handleMouseEnter }
                      handleMouseLeave={ this.handleMouseLeave }
                      handleAttachmentClick={ !this.isViewOnlyRole() && this.handleAttachmentClick }
                      deleteAttachment={ !this.isViewOnlyRole() && this.deleteAttachment }
                      rowStyle={ style.rowStyle }
                      isDisabled={ isDisabled }
                    />

                    <LineItemsInformation 
                      lineItems={ lineItems || []}
                      createNewLineItem={ this.createNewLineItem }
                      handleLineItemUpdate={ this.handleLineItemUpdate }
                      handleLineItemDelete={ this.handleLineItemDelete }
                      displayManagerApproval={userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaimsManager) || false}
                      isDisabled={ isDisabled }/>

                    <section className="row mt-1">
                      <section className="col-8">
                        <div className="col-6">
                          <div className="form-group row">
                            <div className="col-6">
                            Status
                            </div>
                            <div className="col-6">
                              <SelectBox
                                id="status-dropdown"
                                value={ status }
                                disabled={ isDisabled }
                                onChange={ this.handleStatusChange }
                                customClassName="form-control-sm"
                                optionsList={ this.getStatusOptions() }/>
                            </div>
                          </div>
                        </div>
                        <PaymentInfo initialStatus={ initialStatus } claimObject={ claimObject } />
                        <div className="col-6">
                          <If condition={
                            (status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.checkWritten &&
                              userHasRole(user, CONSTANTS.USER_ROLES.gapClaimsManager))
                          }>
                            <ClaimAdjustment 
                              claimId={claimId} 
                              disabled={user.id !== currentOwnerID || (status !== CONSTANTS.AUTO_CLAIM_STATUS_MAP.checkWritten)} 
                              loadClaim={this.loadClaim}
                            />
                          </If>
                        </div>
                      </section>
                      <section className="col-4">
                        <div className="form-group row">
                          <div className="col-6">
                            <strong>Claim Summary</strong>
                          </div>
                        </div>
                        <div className="form-group row">
                          <div className="col">
                            Requested Total
                          </div>
                          <div className="col-6">
                            <InputBox type="Currency"
                              id={ `requested-total-inputBox` }
                              customClass=""
                              hasDefaultValue={ true }
                              isDisabled={ isDisabled }
                              value={ formatCurrency(claimObject.requested_amount || '0') }
                              onChange={this.handleRequestedChange}
                            />
                          </div>
                        </div>
                        <div className="form-group row" style={{marginBottom: 30}}>
                          <div className="col">
                            Approved Total
                          </div>
                          <div className="col-6">
                            { formatCurrency(claimObject.approved_amount || '0') }
                          </div>
                        </div>
                      </section>
                    </section>
                  </fieldset>
                  <VoidCheck
                    showVoidCheckConfirmationModal={ this.state.showVoidCheckConfirmationModal }
                    claimObject={ claimObject }
                    onVoidCheckSuccess={ () => { this.setState({showVoidCheckConfirmationModal: false}, () => this.loadClaim()); }}
                    initiateLoader={ () => {
                      this.setState({ showLoader: true });
                    } }
                    onVoidCheckError={ () => {
                      this.setState({ showLoader: false, showVoidCheckConfirmationModal: false });
                    } }
                    onDeclineVoid={ () => {
                      this.setState({ showVoidCheckConfirmationModal: false });
                    } }/>
                </form>
                {this.showRecordNotes()}
              </section>
            </section>
          </section>

          <AttachmentModal
            displayModal={ this.state.displayAttachmentModal }
            closeModal={ this.closeAttachmentModal }
            claimId={ claimObject.id }
            attachmentParams={ this.state.attachmentParams }/>

          <ConfirmationModal confirmButtonText="Save/Submit"
            declineButtonText="Cancel"
            displayConfirmationModal={ this.state.showAuthorizeConfirmationModal }
            displayMessage={ `Do you want to save and continue to authorize. If not, please press cancel to return to the checklist` }
            onConfirm={ () => {
              this.setState({ showAuthorizeConfirmationModal: false }, () => {
                this.changeStatus(this.state.statusType, this.state.statusValue);
              });
            } }
            onDecline={ () => {
              this.setState({ showAuthorizeConfirmationModal: false });
            } }/>

          <UserSelectionModal userList={ this.state.userList }
            displayModal={ displayAssignOwnerModal || showManagerOwner}
            showUserListLoader={ showUserListLoader }
            userType={showManagerOwner ? CONSTANTS.USER_ROLES.gapClaimsManager : CONSTANTS.USER_ROLES.gapClaims}
            userHasRole={ userHasRole }
            handleOwnerChange={ this.handleOwnerChange }
            closeModal={ this.cancelUpdate }/>

          <ConfirmUpdateModal displayModal={ displayUpdateConfirmationModal }
            customTextMessage={ `You are assigning ownership of this claim to ${newOwnerName}` }
            handleYesClick={ this.confirmUpdate }
            handleCloseClick={ this.cancelUpdate }/>

          <div className="clearfix"/>

          <ConfirmationModal confirmButtonText="Yes"
            declineButtonText="No"
            displayConfirmationModal={ this.state.displayBackConfirmationModal }
            displayMessage="You have unsaved work, do you want to save it before continuing?"
            onConfirm={ this.handleUpdateSubmit }
            onDecline={ this.redirectToPrevious }/>

          <ConfirmationModal confirmButtonText="Save/Submit"
            declineButtonText="Cancel"
            displayConfirmationModal={ showApprovedConfirmation }
            displayMessage={ `This changes will be posted to the Intacct for the payment. Do you want to save and continue to approve. If not, please press cancel to return to the checklist.` }
            onConfirm={ () => {
              this.setState({ showApprovedConfirmation: false }, () => {
                this.handleApprove();
              });
            } }
            onDecline={ () => {
              this.setState({ showApprovedConfirmation: false });
            } }/>

          {this.state.displayVendorSelectionModal && (<VendorSelectionModal
            searchQuery={ vendorSearchValue || claimObject.vendor_id  }
            selectBankDetails={ this.onVendorSelect }
            closeBankSelectionModal={ () => {
              this.setState({ displayVendorSelectionModal: false });
            } }
            displayBankSelectionModal={ this.state.displayVendorSelectionModal }/>)}
        </section>
      </Loader>
    );
  }
}

LWTClaimChecklist.propTypes = {
  location: PropTypes.shape({
    query: PropTypes.shape({
      id: PropTypes.string.isRequired,
      contract_number: PropTypes.string,
    }).isRequired,
  }).isRequired,
  user: PropTypes.shape({
    id: PropTypes.number.isRequired,
    first_name: PropTypes.string.isRequired,
    last_name: PropTypes.string.isRequired,
    email: PropTypes.string.isRequired,
    stores: PropTypes.arrayOf(PropTypes.string.isRequired),
    lwt_approval_limit: PropTypes.string.isRequired,
    roles: PropTypes.shape({
      Map: PropTypes.object,
    }).isRequired,
    banner_info: PropTypes.shape({
      header: PropTypes.string.isRequired,
      message: PropTypes.string.isRequired,
      enabled: PropTypes.bool.isRequired,
    }).isRequired,
  })
};

LWTClaimChecklist.contextTypes = {
  router: PropTypes.object.isRequired
};
