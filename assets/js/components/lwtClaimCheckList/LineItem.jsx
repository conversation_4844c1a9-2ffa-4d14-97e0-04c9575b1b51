import React, { useState } from "react";
import If from "../reusable/If/If.jsx";
import InputBox from "../reusable/InputBox/InputBox.jsx";
import SelectBox from "../reusable/SelectBox/SelectBox.jsx";
import ConfirmationModal from "../reusable/ConfirmationModal/ConfirmationModal.jsx";
import moment from "moment";
import dateFormat from "../reusable/Utilities/dateFormat.js";
import PropTypes from "prop-types";
import { CONSTANTS } from "../reusable/Constants/constants.js";
import { formatCurrency } from "../reusable/Utilities/format";

const optionList = {
  lineItemStatus: [
    { name: '--Select status--', value: "" },
    { name: 'Open', value: "Open" },
    { name: 'Approved', value: "Approved" },
    { name: 'Denied', value: "Denied" },
  ],
  // TODO: This list should be populated from the database
  deniedReason: [
    {name: '--Select Reason--', value:''},
    {name: 'Excluded Item', value:1},
    {name: 'Commercial Use', value:2},
    {name: 'Over 90 Days', value:3},
    {name: 'Terminating Event', value:4},
  ],
};

const LineItem = (props) => {
  const [isExpanded, setIsExpanded] = useState( true);
  const [displayConfirmation, setDisplayConfirmation] = useState(false);
  const isLineItemDisabled = props.lineItem.status === 'Approved';

  const handleUpdate = (value) => {
    const {
      handleLineItemUpdate,
      lineItem,
    } = props;

    handleLineItemUpdate(lineItem.id, 'denial_reason_id', value);
  };

  const getDeniedReasonDropdown = () => {
    const {
      index,
      isDisabled,
      lineItem,
      handleLineItemUpdate,
    } = props;

    return (
      <div>
        <div className="form-group row">
          <div className="col-2">
            <label className="col-form-label">
                Denied Reason
            </label>
          </div>
          <SelectBox
            id={ `status-${index}-dropdown` }
            disabled={ isDisabled || isLineItemDisabled}
            customClassName="form-control-sm"
            value={ lineItem.denial_reason_id || '' }
            onChange={ handleUpdate }
            optionsList={ optionList.deniedReason }/>
        </div>
        <div className="form-group row">
          <div className="col-2">
            <label className="col-form-label">
                Denial Note
            </label>
          </div>
          <InputBox
            type="Text"
            id={ `denial-note-${index}-inputBox` }
            isDisabled={ isDisabled || isLineItemDisabled}
            customClass="col-10"
            value={ lineItem.denial_note }
            onChange={ (e) => handleLineItemUpdate(lineItem.id, 'denial_note', e) }/>
        </div>
      </div>
    );
  };

  const renderLineItemDetails = () => {
    const {
      index,
      isDisabled,
      lineItem,
      handleLineItemUpdate,
      displayManagerApproval,
    } = props;

    return (
      <div>
        <div className="form-group row">
          <label
            className="form-check form-check-label col-form-label col no-gutters">
            <input type="checkbox"
              className="form-check-input"
              id={ `imagesReceived-${index}-checkBox` }
              disabled={ isDisabled || isLineItemDisabled}
              checked={ lineItem.is_images_received }
              onChange={ (e) => handleLineItemUpdate( lineItem.id, 'is_images_received', !lineItem.is_images_received) }/>
            <span>Images Received</span>
          </label>
          {
            displayManagerApproval &&
            (<label
              className="form-check form-check-label col-form-label col no-gutters">
              <input type="checkbox"
                className="form-check-input"
                id={ `manager-approval-${index}-checkBox` }
                disabled={ isDisabled }
                checked={ lineItem.manager_approval }
                onChange={ (e) => handleLineItemUpdate( lineItem.id, 'manager_approval', e.target.checked) }/>
              <span>Manager Approval</span>
            </label>)
          }
        </div>
        <div className="form-group row">
          <div className="col-2">
            <label
              className="col-form-label">
                Description
            </label>
          </div>
          <InputBox
            type="Text"
            id={ `description-${index}-inputBox` }
            isDisabled={ isDisabled || isLineItemDisabled}
            customClass="col-10"
            value={ lineItem.description || '' }
            onChange={ (e) => handleLineItemUpdate(lineItem.id, 'description', e) }/>
        </div>
        <div className="form-group row">
          <div className="col-2">
            <label
              className="col-form-label">
              Note
            </label>
          </div>
          <InputBox type="Text"
            id={ `note-${index}-inputBox` }
            isDisabled={ isDisabled || isLineItemDisabled}
            customClass="col-10"
            value={ lineItem.note || '' }
            onChange={ (e) => handleLineItemUpdate(lineItem.id, 'note', e) }/>
        </div>
        <div className="form-group row">
          <div className="col-2">
            <label
              className="col-form-label">
              Requested Amount
            </label>
          </div>
          <InputBox type="Currency"
            id={ `requestedAmount-${index}-inputBox` }
            isDisabled={ isDisabled || isLineItemDisabled}
            hasDefaultValue={ true }
            customClass="col-10"
            value={ formatCurrency(lineItem.requested_amount || '0') }
            onBlur={ (e) => handleLineItemUpdate(lineItem.id, 'requested_amount', e) }/>
        </div>
        <div className="form-group row">
          <div className="col-2">
            <label
              className="col-form-label">
              Approved Amount
            </label>
          </div>
          <InputBox type="Currency"
            id={ `approvedAmount-${index}-inputBox` }
            isDisabled={ isDisabled || isLineItemDisabled}
            hasDefaultValue={ true }
            customClass="col-10"
            value={ formatCurrency(lineItem.approved_amount || '0') }
            onBlur={ (e) => handleLineItemUpdate(lineItem.id, 'approved_amount', e) }/>
        </div>
        <If condition={ parseFloat(lineItem.approved_amount) > parseFloat(lineItem.requested_amount) && lineItem.status !== 'Approved'}>
          <tr className="row col-12">
            <td className="col border-top-0 text-right">
              <span className="text-danger">Approved amount is greater than requested amount.</span>
            </td>
          </tr>
        </If>
        <If condition={ parseFloat(lineItem.requested_amount) > parseFloat(lineItem.approved_amount) && lineItem.status !== 'Approved'}>
          <tr className="row col-12">
            <td className="col border-top-0 text-right">
              <span className="text-danger">Requested amount is greater than approved amount.</span>
            </td>
          </tr>
        </If>
      </div>
    );
  };

  return (
    <div className="col mt-3">
      <div className="card">
        <div className="card-header d-flex cursor-pointer"
          id={ `lineItem-${props.index}` }
          onClick={ () => {
            setIsExpanded(!isExpanded);
          } }>
          <div className="col-8">
            <span className="align-middle">
              {`Line Item# ${parseInt(props.index) + 1}`}
            </span>
          </div>
          <div className="col-4 ml-auto row justify-content-end">
            <div className="d-inline-block form-group mb-0">
              {props.lineItem.status && <span>{`${props.lineItem.status} - `}</span>}
            </div>
            <div className="d-inline-block ml-2">
              <span className="align-middle">
                {moment.utc(props.lineItem.created_at, dateFormat.backendDateFormat).format(dateFormat.displayDateFormat)}
                <If condition={ !props.isDisabled }>
                  <i className="fa fa-window-close-o ml-2 cursor-pointer" title="Delete" onClick={ () => {
                    setDisplayConfirmation(true);
                  } }/>
                </If>
              </span>
            </div>
          </div>
        </div>
        <If condition={ isExpanded }>
          <div className="card-body">
            {renderLineItemDetails()}
            <div className="row justify-content-end">
              <div className="col-1">
                Status
              </div>
              <div className="col-2">
                <SelectBox
                  id={ `status-${props.index}-dropdown` }
                  disabled={ props.isDisabled }
                  customClassName="form-control-sm"
                  value={ props.lineItem.status }
                  onChange={ (e) => props.handleLineItemUpdate(props.lineItem.id, 'status', e) }
                  optionsList={ optionList.lineItemStatus }/>
              </div>
            </div>
            <br/>
            <If condition={props.lineItem.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.denied}>
              {getDeniedReasonDropdown()}
            </If>
          </div>
        </If>
        <ConfirmationModal
          displayConfirmationModal={ displayConfirmation }
          displayMessage="Are you sure you want to delete this lineItem?"
          onConfirm={ () => {
            setDisplayConfirmation(false);
            props.handleLineItemDelete(props.lineItem.id);
          } }
          onDecline={ () => {
            setDisplayConfirmation(false);
          } }
          type="warning"
          confirmButtonText="Yes"
          declineButtonText="Cancel"/>
      </div>
    </div>
  );
};

LineItem.propTypes = {
  lineItem: PropTypes.object,
  index: PropTypes.number.isRequired,
  handleLineItemUpdate: PropTypes.func.isRequired,
  handleLineItemDelete: PropTypes.func.isRequired,
  isDisabled: PropTypes.bool,
  displayManagerApproval: PropTypes.bool.isRequired,
};

export default LineItem;