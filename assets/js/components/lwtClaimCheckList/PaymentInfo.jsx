import React, { useEffect, useState } from "react";
import PropTypes from "prop-types";
import moment from "moment";
import dateFormat from "./../reusable/Utilities/dateFormat.js";
import accounting from "accounting";
import { CONSTANTS } from "../reusable/Constants/constants.js";
import { URLS as apiUrls } from "./../reusable/Utilities/urls";
import { json as ajax } from "./../../ajax.js";
import Alert from "react-s-alert";
import Loader from "react-loader-advanced";

const PaymentInfo = (props) => { 
  const {
    initialStatus,
    claimObject,
  } = props;

  const spinnerMessage = <p className='text-center'><i className="fa fa-refresh fa-spin"/> Loading...</p>;
  const [showLoader, setShowLoader] = useState(false);
  const [paymentObject, setPaymentObject] = useState([]);

  useEffect(() => {
    
    if ((props.initialStatus === CONSTANTS.LWT_CLAIM_STATUS_MAP.waitingForCheck ||
      props.initialStatus === CONSTANTS.LWT_CLAIM_STATUS_MAP.checkWritten) && props.claimObject && props.claimObject.id) {
      getPaymentDetails();
    }

  }, [props.initialStatus]);

  const getPaymentDetails = () => {
    const url = `${apiUrls.lwtClaimPayment.replace('__claimId__', claimObject.id)}`;
    
    setShowLoader(true);
    ajax(url, {}, {}, (data, status) => {
      if (status === 200) {
        
        setShowLoader(false);
        setPaymentObject(data.claim_payment);
      } else {
        setShowLoader(false);
        Alert.error("Click the browser's Refresh button to reload the payment data. If the error continues, contact your system administrator.");
      }
    });
  };

  const renderPayment = (payment) => {
    if (initialStatus === CONSTANTS.LWT_CLAIM_STATUS_MAP.waitingForCheck ||
      initialStatus === CONSTANTS.LWT_CLAIM_STATUS_MAP.checkWritten) {
      return (
        <div key={payment.id}>
          <div className="form-group row">
            <div className="col-6">
              Bill Memo:
            </div>
            <div className="col-6">
              {payment && payment.bill_memo}
            </div>
          </div>
          <div className="form-group row">
            <div className="col-6">
              Bill Number:
            </div>
            <div className="col-6">
              {payment && payment.bill_number}
            </div>
          </div>
          {
            payment.is_adjustment &&
            (
              <div className="form-group row">
                <div className="col-6">
                  Reason:
                </div>
                <div className="col-6">
                  {payment && payment.reason}
                </div>
              </div>
            )
          }
          {(initialStatus === CONSTANTS.LWT_CLAIM_STATUS_MAP.checkWritten || payment.paid_date) && renderPaymentInfo(payment)}
        </div>
      );
    }
  };

  const renderPaymentInfo = (payment) => {
    return (
      <div key={payment.id}> 
        <div className="form-group row">
          <div className="col-6">
              Check #:
          </div>
          <div className="col-6">
            {payment && payment.check_number}
          </div>
        </div>
        <div className="form-group row">
          <div className="col-6">
              Amount:
          </div>
          <div className="col-6">
            {payment && accounting.formatMoney(payment.amount.Decimal, '$', 2)}
          </div>
        </div>
        <div className="form-group row">
          <div className="col-6">
              Paid Date:
          </div>
          <div className="col-6">
            {payment && moment(payment.paid_date).format(dateFormat.displayDateFormat)}
          </div>
        </div>
      </div>
    );
  };

  return (
    <div  className="col-8 pl-0"> 
      <Loader show={ showLoader } message={ spinnerMessage }>
        {paymentObject && paymentObject.map(renderPayment)}
      </Loader>
    </div>
  );
};

PaymentInfo.propTypes = {
  claimObject: PropTypes.object.isRequired,
  initialStatus: PropTypes.string.isRequired
};

export default PaymentInfo;