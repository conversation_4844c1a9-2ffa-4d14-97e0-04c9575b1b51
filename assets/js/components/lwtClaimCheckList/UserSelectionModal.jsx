import React from 'react';
import Modal from "../../Modal.jsx";
import PropTypes from 'prop-types';
import { CONSTANTS } from "./../reusable/Constants/constants.js";

const UserSelectionModal = (props) => { 
  const {
    displayModal,
    showUserListLoader,
    handleOwnerChange,
    userList,
    closeModal,
    userType,
    userHasRole,
  } = props;

  const renderClaimOwnerDropdown = () => {
    let users = userList;
    if (users.length > 0) {
      if (userType === CONSTANTS.USER_ROLES.gapClaimsManager) {
        users = users.filter(obj => userHasRole(obj, CONSTANTS.USER_ROLES.gapClaimsManager));
      }
      return users.map(renderUserList);
    }
  };
    
  const renderUserList = (user) =>  {
    return (<option value={user.id} key={user.id}>{`${user.first_name} ${user.last_name}`}</option>);
  };

  return (
    <section>
      <Modal visible={ displayModal } close={closeModal}>
        <div className="my-3">
          <p className={ showUserListLoader === true ? 'text-center' : 'd-none'}>
            <i className="fa fa-refresh fa-spin"/> Fetching LWT Claim Owner list
          </p>
          <div
            className={ showUserListLoader === true ? 'd-none' : 'form-group text-center clearfix' }>
            <label className="form-check-label col-form-label col-form-label-sm">
                    Select Owner:&nbsp;
            </label>
            <select className="form-control ml-1 col-7 d-inline-block" onChange={ handleOwnerChange }>
              <option value=''>Select Owner</option>
              { renderClaimOwnerDropdown() }
            </select>
          </div>
        </div>
      </Modal>
    </section>
  );
};

UserSelectionModal.propTypes = {
  userList: PropTypes.array.isRequired,
  displayModal: PropTypes.bool.isRequired,
  showUserListLoader: PropTypes.bool.isRequired,
  handleOwnerChange: PropTypes.func.isRequired,
  closeModal: PropTypes.func,
  userType: PropTypes.string,
  userHasRole: PropTypes.func,
};

export default UserSelectionModal;