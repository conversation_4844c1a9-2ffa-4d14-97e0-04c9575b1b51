import React, { useEffect, useState } from "react";
import accounting from "accounting";
import PropTypes from "prop-types";
import { json as ajax } from "./../../ajax.js";
import { URLS as apiUrls } from "./../reusable/Utilities/urls";
import Alert from "react-s-alert";
import { CONSTANTS } from "./../reusable/Constants/constants.js";
import Loader from "react-loader-advanced";

const VoidCheckFinanceInfo = (props) => {
  const [showLoader, setShowLoader] = useState(false);
  const [paymentObject, setPaymentObject] = useState([]);
  
  useEffect(() => {

    getFinancialInformation();

  },[props.claimId]);

  const getFinancialInformation = () => {
    setShowLoader(true);
    ajax(`${apiUrls.lwtClaimPayment.replace('__claimId__', props.claimId)}`, {}, {}, (data, status) => {
      setShowLoader(false);
      if (status === 200) {
        setPaymentObject(data.claim_payment);
      } else {
        if (data.message) {
          Alert.error(data.message);
        } else {
          Alert.error("Click the browser's Refresh button to reload the payment data. If the error continues, contact your system administrator.");
        }
      }
    });
  };

  const onPaymentSelect = (checkNumber, e) => {
    const selected = paymentObject.find(obj => obj.check_number === checkNumber);
    const remaining = paymentObject.filter(obj => obj.check_number !== checkNumber);
    if (selected.isSelected) {
      selected.isSelected = !selected.isSelected;
    } else {
      selected.isSelected = true;
    }
    const newPayments = [...remaining, selected].sort((a, b) => a.id - b.id);
    setPaymentObject(newPayments);
  };

  const renderPaymentOptions = () => {
    return paymentObject.map((payment, index) => {
      return (
        <div className="row col-12 justify-content-center" key={index}>
          <div className="col-1">
            <input type="radio"
              id={index}
              checked={payment.isSelected}
              disabled={props.claimStatus !== CONSTANTS.LWT_CLAIM_STATUS_MAP.checkWritten}
              onClick={(e) => onPaymentSelect(payment.check_number, e)}
            />
          </div>
          <div className="col-11">
            <div className="col-10 justify-content-start form-group row">
              <label className="col-5">
                Check #:
              </label>
              <div className="col-7">
                <p className="col-form-label-sm mb-0">{ payment && payment.check_number }</p>
              </div>
            </div>
            <div className="col-10 justify-content-start form-group row">
              <label className="col-5">
                Amount:
              </label>
              <div className="col-7">
                <p
                  className="col-form-label-sm mb-0">{ payment && accounting.formatMoney(payment.amount.Decimal, "$", 2) }</p>
              </div>
            </div>
            <div className="col-10 justify-content-start form-group row">
              <label className="col-5">
                Status:
              </label>
              <div className="col-7">
                <p
                  className="col-form-label-sm mb-0">{ CONSTANTS.LWT_CLAIM_STATUS_DISPLAY_MAP[props.claimStatus] }</p>
              </div>
            </div>
          </div>
        </div>);
    });
  };

  const spinnerMessage = <p className='text-center'><i className="fa fa-refresh fa-spin"/> Loading...</p>;
  return (
    <div className="col-12">
      <Loader show={showLoader} message={spinnerMessage}>
        {renderPaymentOptions()}
      </Loader>
    </div>
  );
};

VoidCheckFinanceInfo.propTypes = {
  claimId: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.number
  ]),
  claimStatus: PropTypes.string,
};

export default VoidCheckFinanceInfo;