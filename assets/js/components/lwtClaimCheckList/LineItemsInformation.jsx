import React from "react";
import PropTypes from "prop-types";
import LineItem from "./LineItem.jsx";

const LineItemsInformation = (props) => {
  const iconStyle = { color:"grey" };

  const renderLineItems = () => {
    const {
      lineItems,
    } = props;
    if (lineItems && lineItems.length > 0) {
      return lineItems.map(getLineItems);
    }
  };

  const getLineItems = (lineItem, index) => {
    const {
      isDisabled,
      handleLineItemUpdate,
      handleLineItemDelete,
      displayManagerApproval,
    } = props;
    return (
      <LineItem lineItem={ lineItem }
        index={ index }
        isDisabled={ isDisabled }
        key={ lineItem.id }
        handleLineItemUpdate={ handleLineItemUpdate }
        displayManagerApproval = {displayManagerApproval}
        handleLineItemDelete={ handleLineItemDelete }/>
    );
  };

  const onNewLineItem = () => {
    if (!props.isDisabled) {
      props.createNewLineItem();
    }
  };

  return (
    <section>
      {renderLineItems()}
      <div className="col mt-3"
        style={ (props.isDisabled) ? iconStyle : {} }
        id="add-line-item"
        onClick={ onNewLineItem }>
        <div className="card">
          <div className={ `card-header d-flex ${props.isDisabled ? "" : "cursor-pointer"}` }>
            <span>
              <i className="fa fa-plus-circle mr-2"/>
                  Add New Line Item
            </span>
          </div>
        </div>
      </div>
    </section>
  );
};

LineItemsInformation.propTypes = {
  lineItems: PropTypes.array.isRequired,
  isDisabled: PropTypes.bool.isRequired,
  handleLineItemUpdate: PropTypes.func.isRequired,
  handleLineItemDelete: PropTypes.func.isRequired,
  createNewLineItem: PropTypes.func.isRequired,
  displayManagerApproval: PropTypes.bool.isRequired,
};

export default LineItemsInformation;