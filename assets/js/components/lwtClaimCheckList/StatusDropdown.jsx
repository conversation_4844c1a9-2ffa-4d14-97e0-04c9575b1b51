import React from 'react';
import InputBoxRow from "../claimCheckList/InputBoxRow.jsx";
import { CONSTANTS } from "./../reusable/Constants/constants.js";
import PropTypes from 'prop-types';

export default class StatusDropdown extends React.Component {

  static propTypes = {
    claimObject: PropTypes.object.isRequired,
    userHasRole: PropTypes.func.isRequired,
    handleStatusChange: PropTypes.func.isRequired,
    onCursorChange: PropTypes.func.isRequired,
    status: PropTypes.string,
    initialStatus: PropTypes.string,
    statusMap: PropTypes.object.isRequired,
    labelCustomClass: PropTypes.string.isRequired,
    inputCustomClass: PropTypes.string.isRequired,
    user: PropTypes.shape({
      id: PropTypes.number.isRequired,
      first_name: PropTypes.string.isRequired,
      last_name: PropTypes.string.isRequired,
      email: PropTypes.string.isRequired,
      stores: PropTypes.arrayOf(PropTypes.string.isRequired),
      roles: PropTypes.shape({
        Map: PropTypes.object,
      }).isRequired
    }),
    getFieldNotes: PropTypes.func.isRequired,
    getFieldDocs: PropTypes.func.isRequired,
    selectedFieldNote: PropTypes.object.isRequired,
    handleItemNoteClick: PropTypes.func.isRequired,
    handleMouseEnter: PropTypes.func.isRequired,
    handleMouseLeave: PropTypes.func.isRequired,
    handleAttachmentClick: PropTypes.func.isRequired,
    deleteAttachment: PropTypes.func,
    rowType: PropTypes.string.isRequired,
    rowStyle: PropTypes.object.isRequired,
    attachments: PropTypes.object.isRequired,
    notes: PropTypes.object.isRequired
  };

  /**
   * This function will render the status based on logged in user role which can be;
   * 'gap_claims' or 'gap_claims_manager'
   * @param claimObject
   * vta claim object
   *
   * - statusType
   * This can have one of the two values 'status'(Parent claim status) or 'child_status'(Child claim status).
   * */

  renderStatusDropdown = (claimObject) => {
    if (this.props.userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaims) && !this.props.userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaimsManager)) {
      return this.returnProcessorStatusDropdown(claimObject);
    } else if (this.props.userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaimsManager)) {
      return this.returnManagerStatusDropdown(claimObject);
    }
  };

  returnProcessorStatusDropdown = (claimObject) => {
    let statusValue = '';
    if ((claimObject.status === this.props.statusMap.pending ||
      claimObject.status === this.props.statusMap.pendingReopened ||
      claimObject.status === this.props.statusMap.returnedForCorrection) &&
      (this.props.status === 'pending' || this.props.status === '')) {
      statusValue = 'pending';
    } else if (claimObject.status === this.props.statusMap.waitingForAuthorization) {
      statusValue = 'waitingForAuthorization';
    } else if (claimObject.status === this.props.statusMap.readyToProcess) {
      statusValue = 'readyToProcess';
    } else if (claimObject.status === this.props.statusMap.pendingDenial) {
      statusValue = 'pendingDenial';
    } else if (claimObject.status === this.props.statusMap.pending && this.props.status === 'extend2Weeks') {
      statusValue = 'extend2Weeks';
    } else if(claimObject.status === this.props.statusMap.closedNoResponse){
      statusValue = 'closedNoResponse';
    }
    return (
      <InputBoxRow labelContainerClass={ this.props.labelCustomClass }
        inputBoxContainerClass={ this.props.inputCustomClass }
        containerClass="justify-content-end"
        hasLabel={ true }
        hasNode={ true }
        label="Status:"
        renderNode={ this.getProcessorDropdown.bind(this, claimObject, statusValue) }
        rowId={ CONSTANTS.VTA_WORKSHEET_COMPONENT_MAP['status']['rowId'] }
        fieldId={ CONSTANTS.VTA_WORKSHEET_COMPONENT_MAP['status']['id'] }
        fieldNotes={ this.props.getFieldNotes(this.props.notes, CONSTANTS.VTA_WORKSHEET_COMPONENT_MAP['status']['id']) }
        fieldDocs={ this.props.getFieldDocs(this.props.attachments, CONSTANTS.VTA_WORKSHEET_COMPONENT_MAP['status']['id']) }
        selectedFieldNote={ this.props.selectedFieldNote }
        handleItemNoteClick={ this.props.handleItemNoteClick }
        handleMouseEnter={ this.props.handleMouseEnter }
        handleMouseLeave={ this.props.handleMouseLeave }
        handleAttachmentClick={ this.props.handleAttachmentClick }
        deleteAttachment={ this.props.deleteAttachment }
        claimType={ CONSTANTS.CLAIM_TYPE.vta }
        rowType={ this.props.rowType }
        rowStyle={ this.props.rowStyle }/>
    );
  };

  getProcessorDropdown = (claimObject, statusValue) => {
    if(this.props.initialStatus === this.props.statusMap.denied){
      return (
        <select className="form-control form-control-sm"
          id={`${CONSTANTS.VTA_WORKSHEET_COMPONENT_MAP['status']['rowId']}_dropdown`}
          value={statusValue}
          disabled={true}>
          <option value='denied'>Denied</option>
        </select>
      );
    }
    if(this.props.initialStatus === this.props.statusMap.closedNoResponse){
      return (
        <select className="form-control form-control-sm"
          id={`${CONSTANTS.VTA_WORKSHEET_COMPONENT_MAP['status']['rowId']}_dropdown`}
          value={statusValue}
          onChange={this.props.handleStatusChange.bind(null, 'status')}
          disabled={true}>
          <option value='closedNoResponse'>Closed No-Response</option>
        </select>
      );
    } else {
      return (
        <select className="form-control form-control-sm"
          id={`${CONSTANTS.VTA_WORKSHEET_COMPONENT_MAP['status']['rowId']}_dropdown`}
          value={statusValue}
          onChange={this.props.handleStatusChange.bind(null, 'status')}
          disabled={(claimObject.status === this.props.statusMap.authorization ||
                          claimObject.status === this.props.statusMap.checkWritten ||
                          claimObject.status === this.props.statusMap.checkVoided)}>
          <option value=''>Select status</option>
          <option value='pending'>Pending</option>
          <option value='readyToProcess'>Ready to process</option>
          <option value='pendingDenial'>Pending denial</option>
          <option value='waitingForAuthorization'>Waiting for authorization</option>
          <option value='extend2Weeks'>Extend 2 weeks</option>
          <option value='closedNoResponse'>Closed No-Response</option>
        </select>
      );
    }
  };

  returnManagerStatusDropdown = (claimObject) => {
    let statusValue = '';
    if (claimObject.status === this.props.statusMap.waitingForAuthorization) {
      statusValue = 'waitingForAuthorization';
    } else if (claimObject.status === this.props.statusMap.pendingDenial) {
      statusValue = 'pendingDenial';
    } else if (claimObject.status === this.props.statusMap.authorization) {
      statusValue = 'authorization';
    } else if (claimObject.status === this.props.statusMap.returnedForCorrection || claimObject.status === this.props.statusMap.pendingReopened) {
      statusValue = 'returnedForCorrection';
    } else if (claimObject.status === this.props.statusMap.denied) {
      statusValue = 'denied';
    } else if (claimObject.status === this.props.statusMap.checkWritten) {
      statusValue = 'checkWritten';
    } else if (claimObject.status === this.props.statusMap.checkVoided) {
      statusValue = 'checkVoided';
    } else if(claimObject.status === this.props.statusMap.closedNoResponse){
      statusValue = 'closedNoResponse';
    }
    return (
      <InputBoxRow labelContainerClass={ this.props.labelCustomClass }
        inputBoxContainerClass={ this.props.inputCustomClass }
        containerClass="justify-content-end"
        hasLabel={ true }
        hasNode={ true }
        label="Status:"
        renderNode={ this.getManagerStatusDropdown.bind(this, claimObject, statusValue) }
        rowId={ CONSTANTS.VTA_WORKSHEET_COMPONENT_MAP['status']['rowId'] }
        fieldId={ CONSTANTS.VTA_WORKSHEET_COMPONENT_MAP['status']['id'] }
        fieldNotes={ this.props.getFieldNotes(this.props.notes, CONSTANTS.VTA_WORKSHEET_COMPONENT_MAP['status']['id']) }
        fieldDocs={ this.props.getFieldDocs(this.props.attachments, CONSTANTS.VTA_WORKSHEET_COMPONENT_MAP['status']['id']) }
        selectedFieldNote={ this.props.selectedFieldNote }
        handleItemNoteClick={ this.props.handleItemNoteClick }
        handleMouseEnter={ this.props.handleMouseEnter }
        handleMouseLeave={ this.props.handleMouseLeave }
        handleAttachmentClick={ this.props.handleAttachmentClick }
        deleteAttachment={ this.props.deleteAttachment }
        claimType={ CONSTANTS.CLAIM_TYPE.vta }
        rowType={ this.props.rowType }
        rowStyle={ this.props.rowStyle }/>
    );
  };

  getManagerStatusDropdown = (claimObject, statusValue) => {
    if(this.props.initialStatus === this.props.statusMap.denied){
      return (
        <select className="form-control form-control-sm"
          id={`${CONSTANTS.VTA_WORKSHEET_COMPONENT_MAP['status']['rowId']}_dropdown`}
          value={statusValue}
          disabled={true}>
          <option value='denied'>Denied</option>
        </select>
      );
    }
    if(this.props.initialStatus === this.props.statusMap.closedNoResponse){
      return (
        <select className="form-control form-control-sm"
          id={`${CONSTANTS.VTA_WORKSHEET_COMPONENT_MAP['status']['rowId']}_dropdown`}
          value={statusValue}
          onChange={this.props.handleStatusChange.bind(null, 'status')}>
          <option value=''>Select status</option>
          <option value='returnedForCorrection'>Returned</option>
          <option value='closedNoResponse'>Closed No-Response</option>
        </select>
      );
    } else {
      return (
        <select className="form-control form-control-sm"
          id={`${CONSTANTS.VTA_WORKSHEET_COMPONENT_MAP['status']['rowId']}_dropdown`}
          value={statusValue}
          onChange={this.props.handleStatusChange.bind(null, 'status')}
          disabled={(this.props.initialStatus === this.props.statusMap.authorization ||
                        this.props.initialStatus === this.props.statusMap.checkWritten ||
                        this.props.initialStatus === this.props.statusMap.checkVoided)}>
          <option value=''>Select status</option>
          <option value='waitingForAuthorization'>Waiting for authorization</option>
          <option value='pendingDenial'>Pending denial</option>
          <option value='authorization'>Authorization</option>
          <option value='returnedForCorrection'>Returned</option>
          <option value='denied'>Denied</option>
          <option value='closedNoResponse'>Closed No-Response</option>
          {this.renderCheckWrittenOption(claimObject)}
        </select>
      );
    }
  };

  /**
   * This function will render check written option for dropdown based on the status code, This won't be visible all the time.
   * It will be displayed only when the status is 'C - Check written'
   *
   * @param claimObject
   * */

  renderCheckWrittenOption = (claimObject) => {
    if (claimObject.status === this.props.statusMap.checkWritten) {
      return (
        <option value='checkWritten'>Check Written</option>
      );
    }
  };

  renderClaimDeniedReason = () => {
    if (this.props.claimObject.status === this.props.statusMap.denied) {
      return (
        <InputBoxRow labelContainerClass={ this.props.labelCustomClass }
          inputBoxContainerClass={ this.props.inputCustomClass }
          containerClass="justify-content-end"
          hasLabel={ true }
          hasNode={ true }
          label="Reason:"
          renderNode={ this.getDeniedReasonDropdown }
          rowId={ CONSTANTS.VTA_WORKSHEET_COMPONENT_MAP['deniedReason']['rowId'] }
          fieldId={ CONSTANTS.VTA_WORKSHEET_COMPONENT_MAP['deniedReason']['id'] }
          fieldNotes={ this.props.getFieldNotes(this.props.notes, CONSTANTS.VTA_WORKSHEET_COMPONENT_MAP['deniedReason']['id']) }
          fieldDocs={ this.props.getFieldDocs(this.props.attachments, CONSTANTS.VTA_WORKSHEET_COMPONENT_MAP['deniedReason']['id']) }
          selectedFieldNote={ this.props.selectedFieldNote }
          handleItemNoteClick={ this.props.handleItemNoteClick }
          handleMouseEnter={ this.props.handleMouseEnter }
          handleMouseLeave={ this.props.handleMouseLeave }
          handleAttachmentClick={ this.props.handleAttachmentClick }
          deleteAttachment={ this.props.deleteAttachment }
          claimType={ CONSTANTS.CLAIM_TYPE.vta }
          rowType={ this.props.rowType }
          rowStyle={ this.props.rowStyle }/>
      );
    }
  };

  getDeniedReasonDropdown = () => {
    return (
      <select className="form-control form-control-sm"
        value={ this.props.claimObject.denied_reason }
        disabled={this.props.initialStatus === this.props.statusMap.denied}
        onChange={ this.props.onCursorChange.bind(null, 'denied_reason') }>
        <option value=''>Select reason</option>
        <option value='Illegal Act'>Illegal Act</option>
        <option value='family'>Family</option>
        <option value='intentional'>Intentional</option>
        <option value='refinanced'>Refinanced</option>
        <option value='Access to Keys'>Access to Keys</option>
        <option value='other'>Other</option>
      </select>
    );
  };

  render() {
    return (
      <div>
        { this.renderStatusDropdown(this.props.claimObject) }
        { this.renderClaimDeniedReason() }
      </div>
    );
  }
}
