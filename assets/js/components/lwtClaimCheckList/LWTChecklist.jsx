import React from 'react';
import InputBoxRow from "../claimCheckList/InputBoxRow.jsx";
import PropTypes from 'prop-types';
import { CONSTANTS } from '../reusable/Constants/constants';

const ValuationReport = (props) => {
  const COMPONENT_MAP = CONSTANTS.LWT_WORKSHEET_COMPONENT_MAP;
  const {
    claimObject,
    showManagerCheckbox,
    onCursorToggle,
    getFieldNotes,
    getFieldDocs,
    selectedFieldNote,
    handleItemNoteClick,
    handleMouseEnter,
    handleMouseLeave,
    handleAttachmentClick,
    deleteAttachment,
    rowStyle,
    attachments,
    notes,
    isDisabled,
  } = props;

  return (
    <span className="valuation-report">
      <InputBoxRow labelContainerClass="col-5"
        hasLabel={ true }
        hasCheckbox={ true }
        checkBoxAttribute="finance_contract"
        label="Finance Contract / Lease Agreement"
        managerCheckBoxAttribute="finance_contract_available_manager_flag"
        showManagerCheckbox={ showManagerCheckbox }
        claimObject={ claimObject }
        onCursorToggle={ onCursorToggle }
        rowId={ COMPONENT_MAP[ 'financeContract' ][ 'rowId' ] }
        fieldId={ COMPONENT_MAP[ 'financeContract' ][ 'id' ] }
        fieldNotes={ getFieldNotes(notes, COMPONENT_MAP[ 'financeContract' ][ 'id' ]) }
        fieldDocs={ getFieldDocs(attachments, COMPONENT_MAP[ 'financeContract' ][ 'documentType' ]) }
        selectedFieldNote={ selectedFieldNote }
        handleItemNoteClick={ handleItemNoteClick }
        handleMouseEnter={ handleMouseEnter }
        handleMouseLeave={ handleMouseLeave }
        handleAttachmentClick={ handleAttachmentClick }
        deleteAttachment={ deleteAttachment }
        claimType={ CONSTANTS.CLAIM_TYPE.lwt }
        rowType={ CONSTANTS.FIELD_TYPE.static }
        rowStyle={ rowStyle }
        isDisabled={ isDisabled }/>
      <InputBoxRow labelContainerClass="col-5"
        hasLabel={ true }
        hasCheckbox={ true }
        checkBoxAttribute="completed_claim_form"
        label="Completed Claim Form"
        managerCheckBoxAttribute="fcompleted_claim_form_manager_flag"
        showManagerCheckbox={ showManagerCheckbox }
        claimObject={ claimObject }
        onCursorToggle={ onCursorToggle }
        rowId={ COMPONENT_MAP[ 'completedClaimForm' ][ 'rowId' ] }
        fieldId={ COMPONENT_MAP[ 'completedClaimForm' ][ 'id' ] }
        fieldNotes={ getFieldNotes(notes, COMPONENT_MAP[ 'completedClaimForm' ][ 'id' ]) }
        fieldDocs={ getFieldDocs(attachments, COMPONENT_MAP[ 'completedClaimForm' ][ 'documentType' ]) }
        selectedFieldNote={ selectedFieldNote }
        handleItemNoteClick={ handleItemNoteClick }
        handleMouseEnter={ handleMouseEnter }
        handleMouseLeave={ handleMouseLeave }
        handleAttachmentClick={ handleAttachmentClick }
        deleteAttachment={ deleteAttachment }
        claimType={ CONSTANTS.CLAIM_TYPE.lwt }
        rowType={ CONSTANTS.FIELD_TYPE.static }
        rowStyle={ rowStyle }
        isDisabled={ isDisabled }/>
      <InputBoxRow labelContainerClass="col-5"
        hasLabel={ true }
        hasCheckbox={ true }
        checkBoxAttribute="vehicle_condition_report"
        label="Vehicle Condition Report"
        managerCheckBoxAttribute="vehicle_condition_report_manager_flag"
        showManagerCheckbox={ showManagerCheckbox }
        claimObject={ claimObject }
        onCursorToggle={ onCursorToggle }
        rowId={ COMPONENT_MAP[ 'vehicleConditionReport' ][ 'rowId' ] }
        fieldId={ COMPONENT_MAP[ 'vehicleConditionReport' ][ 'id' ] }
        fieldNotes={ getFieldNotes(notes, COMPONENT_MAP[ 'vehicleConditionReport' ][ 'id' ]) }
        fieldDocs={ getFieldDocs(attachments, COMPONENT_MAP[ 'vehicleConditionReport' ][ 'documentType' ]) }
        selectedFieldNote={ selectedFieldNote }
        handleItemNoteClick={ handleItemNoteClick }
        handleMouseEnter={ handleMouseEnter }
        handleMouseLeave={ handleMouseLeave }
        handleAttachmentClick={ handleAttachmentClick }
        deleteAttachment={ deleteAttachment }
        claimType={ CONSTANTS.CLAIM_TYPE.lwt }
        rowType={ CONSTANTS.FIELD_TYPE.static }
        rowStyle={ rowStyle }
        isDisabled={ isDisabled }/>
      <InputBoxRow labelContainerClass="col-5"
        hasLabel={ true }
        hasCheckbox={ true }
        checkBoxAttribute="vin_plate_images"
        label="VIN Plate / Door Sticker Images"
        managerCheckBoxAttribute="vin_plate_images_manager_flag"
        showManagerCheckbox={ showManagerCheckbox }
        claimObject={ claimObject }
        onCursorToggle={ onCursorToggle }
        rowId={ COMPONENT_MAP[ 'vinPlateImages' ][ 'rowId' ] }
        fieldId={ COMPONENT_MAP[ 'vinPlateImages' ][ 'id' ] }
        fieldNotes={ getFieldNotes(notes, COMPONENT_MAP[ 'vinPlateImages' ][ 'id' ]) }
        fieldDocs={ getFieldDocs(attachments, COMPONENT_MAP[ 'vinPlateImages' ][ 'documentType' ]) }
        selectedFieldNote={ selectedFieldNote }
        handleItemNoteClick={ handleItemNoteClick }
        handleMouseEnter={ handleMouseEnter }
        handleMouseLeave={ handleMouseLeave }
        handleAttachmentClick={ handleAttachmentClick }
        deleteAttachment={ deleteAttachment }
        claimType={ CONSTANTS.CLAIM_TYPE.lwt }
        rowType={ CONSTANTS.FIELD_TYPE.static }
        rowStyle={ rowStyle }
        isDisabled={ isDisabled }/>
      <InputBoxRow labelContainerClass="col-5"
        hasLabel={ true }
        hasCheckbox={ true }
        checkBoxAttribute="wear_and_tear_damage_images"
        label="Wear and Tear Damage Images"
        managerCheckBoxAttribute="wear_and_tear_damage_images_manager_flag"
        showManagerCheckbox={ showManagerCheckbox }
        claimObject={ claimObject }
        onCursorToggle={ onCursorToggle }
        rowId={ COMPONENT_MAP[ 'wearAndTearDamageImages' ][ 'rowId' ] }
        fieldId={ COMPONENT_MAP[ 'wearAndTearDamageImages' ][ 'id' ] }
        fieldNotes={ getFieldNotes(notes, COMPONENT_MAP[ 'wearAndTearDamageImages' ][ 'id' ]) }
        fieldDocs={ getFieldDocs(attachments, COMPONENT_MAP[ 'wearAndTearDamageImages' ][ 'documentType' ]) }
        selectedFieldNote={ selectedFieldNote }
        handleItemNoteClick={ handleItemNoteClick }
        handleMouseEnter={ handleMouseEnter }
        handleMouseLeave={ handleMouseLeave }
        handleAttachmentClick={ handleAttachmentClick }
        deleteAttachment={ deleteAttachment }
        claimType={ CONSTANTS.CLAIM_TYPE.lwt }
        rowType={ CONSTANTS.FIELD_TYPE.static }
        rowStyle={ rowStyle }
        isDisabled={ isDisabled }/>
      <InputBoxRow labelContainerClass="col-5"
        hasLabel={ true }
        hasCheckbox={ true }
        checkBoxAttribute="final_invoice_from_lessor"
        label="Final Invoice from Lessor"
        managerCheckBoxAttribute="final_invoice_from_lessor_manager_flag"
        showManagerCheckbox={ showManagerCheckbox }
        claimObject={ claimObject }
        onCursorToggle={ onCursorToggle }
        rowId={ COMPONENT_MAP[ 'finalInvoiceFromLessor' ][ 'rowId' ] }
        fieldId={ COMPONENT_MAP[ 'finalInvoiceFromLessor' ][ 'id' ] }
        fieldNotes={ getFieldNotes(notes, COMPONENT_MAP[ 'finalInvoiceFromLessor' ][ 'id' ]) }
        fieldDocs={ getFieldDocs(attachments, COMPONENT_MAP[ 'finalInvoiceFromLessor' ][ 'documentType' ]) }
        selectedFieldNote={ selectedFieldNote }
        handleItemNoteClick={ handleItemNoteClick }
        handleMouseEnter={ handleMouseEnter }
        handleMouseLeave={ handleMouseLeave }
        handleAttachmentClick={ handleAttachmentClick }
        deleteAttachment={ deleteAttachment }
        claimType={ CONSTANTS.CLAIM_TYPE.lwt }
        rowType={ CONSTANTS.FIELD_TYPE.static }
        rowStyle={ rowStyle }
        isDisabled={ isDisabled }/>
    </span>
  );
};

ValuationReport.propTypes = {
  claimObject: PropTypes.object.isRequired,
  showManagerCheckbox: PropTypes.bool,
  onCursorToggle: PropTypes.func.isRequired,
  getFieldNotes: PropTypes.func.isRequired,
  getFieldDocs: PropTypes.func.isRequired,
  selectedFieldNote: PropTypes.object.isRequired,
  handleItemNoteClick: PropTypes.func.isRequired,
  handleMouseEnter: PropTypes.func.isRequired,
  handleMouseLeave: PropTypes.func.isRequired,
  handleAttachmentClick: PropTypes.func.isRequired,
  deleteAttachment: PropTypes.func.isRequired,
  rowStyle: PropTypes.object.isRequired,
  attachments: PropTypes.object.isRequired,
  notes: PropTypes.object.isRequired,
  isDisabled: PropTypes.bool
};

export default ValuationReport;