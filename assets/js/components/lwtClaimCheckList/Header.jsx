import React from "react";
import moment from "moment";
import dateFormat from "./../reusable/Utilities/dateFormat.js";
import ReactTooltip from "react-tooltip";
import { CONSTANTS } from "./../reusable/Constants/constants.js";
import If from "./../reusable/If/If.jsx";
import PropTypes from "prop-types";
import { getClasses } from "../reusable/Utilities/headerClasses";

const Header = (props, context) => { 
  const pageSpacingStyle = { marginTop: '200px' };
  const {
    claimObject : {
      customer,
      contract_number: contraactNumber,
      updated_by_user_name: updatedByUsername,
      updated_at: updatedAt,
    },
    claimStatus,
    backButtonOnClick,
    nextButtonOnClick,
    nextButtonId,
    nextButtonText,
    showHistoryButtonClick,
    nextButtonDisabled,
    assignButtonOnClick,
    renderAssignOwnerButton,
    isFinanceUser,
    renderAssignMe,
    handleAssignToMe,
    handleOnClickVoidCheck,
    showVoidCheckButton,
    user :{
      banner_info : bannerInfo,
    }
  } = props;


  const redirectToEmail = () => {
    if (props.claimObject && props.claimObject.id && props.claimObject.contract_number
          && props.claimObject.vin && (props.claimObject.customer.first_name || props.claimObject.customer.last_name)) {
      const query = {
        id: props.claimObject.id,
        contractNumber: props.claimObject.contract_number,
        vin: props.claimObject.vin,
        customerName: `${[props.claimObject.customer.last_name, props.claimObject.customer.first_name].filter(name=>name).join(',')}`,
        customerID: props.claimObject.customer.id
      };
      const route = { pathname: "/lwt-email", query };
      if (!context.router.isActive(route)) {
        context.router.push(route);
      }
    }
  };

  const onContractNumberClick = () => {
    window.open(`/gap-contract/${contraactNumber}`, contraactNumber, "width=1000,height=600");
  };

  const renderLastUpdatedBy = () => {
    return (
      <span
        className="row flex-row-reverse pr-3">
          Last updated by: { `${updatedByUsername} - ${ updatedAt ? moment(updatedAt, dateFormat.backendDateFormat).format(dateFormat.displayDateFormat) : ''}` }
      </span>
    );
  };

  const renderAssignButton = () => {
    if ((renderAssignOwnerButton && !isFinanceUser) || renderAssignMe) {
      return (
        <button type="button"
          className={renderAssignMe ? "btn btn-primary mr-2 cursor-pointer" : "btn btn-secondary mr-2 cursor-pointer"}
          id="btn-assign-owner"
          onClick={ renderAssignMe ? handleAssignToMe : assignButtonOnClick }>
          <i className="fa fa-crosshairs"/>&nbsp;{renderAssignMe ? `Assign To Me` : `Assign`}
        </button>
      );
    }
    return (<div></div>);
  };

  const renderVoidCheckButton = () => {
    if (showVoidCheckButton && !isFinanceUser) {
      return (
        <button type="button"
          className="btn btn-primary mr-2 cursor-pointer"
          id="btn-void-check"
          onClick={ handleOnClickVoidCheck }>
          <i className="fa fa-times"/>
              &nbsp;Void Check
        </button>
      );
    }
  };

  const loadPDF = () => {
    window.open(`/api/lwt-claims/${props.claimObject.id}/pdf`);
  };

  const classes = getClasses(bannerInfo);
  let customerName = '';
  if (customer) {
    customerName = customer.last_name.concat(",",customer.first_name);
    customerName = customerName.trim();
    if (customer.is_business) {
      if (customerName) {
        customerName+="/";
      }
      customerName = customerName.concat(customer.business_name);
    } 
  }
  return (
    <section className="page-header" style={ pageSpacingStyle }>
      <div className={classes}>
        <div className="row align-items-center">
          <div className="col-4 text-truncate h2">
            <h3 className="d-inline-block">{`LWT Claim- ${customerName}`}</h3>
          </div>
          <div className="col-4 text-center h3">
            <p className="d-inline-block h4">
              <a href=""
                id="link-contract-number"
                onClick={ onContractNumberClick }>
                { contraactNumber }
              </a>
            </p>
            <div className="d-inline-block h4">&nbsp;&nbsp;&nbsp;
              <span data-tip data-for='claimStatus' data-place="bottom">
                { claimStatus }
              </span>
              <ReactTooltip id='claimStatus' aria-haspopup='true'>
                <span className="text-center">{ CONSTANTS.LWT_CLAIM_STATUS_DISPLAY_MAP[claimStatus] }</span>
              </ReactTooltip>
            </div>
          </div>
          <div className="col-4">
            { renderLastUpdatedBy() }
          </div>
        </div>
        <div className="row py-3">
          <div className="col-4">
            <button onClick={ backButtonOnClick }
              className="btn btn-secondary mr-2 cursor-pointer"
              id="btn-back">
              <i className="fa fa-arrow-left"/>
              &nbsp;Back
            </button>
            <button className={ `btn btn-primary mr-2 cursor-pointer ${nextButtonDisabled ? "disabled" : ""}` }
              onClick={ nextButtonOnClick }
              id={ nextButtonId }
              disabled={ !!nextButtonDisabled }>
              {nextButtonText}
            </button>
            { renderVoidCheckButton() }
            { renderAssignButton() }
          </div>
          <div className="col-8">
            <div className="row flex-row-reverse pr-3">
              <div className="btn-toolbar">
                <button type="button" className="btn btn-secondary mr-2" title="Print" 
                  onClick={ loadPDF }>
                  <i className='fa fa-print'/>
                </button>
                <If condition={ !isFinanceUser }>
                  <button type="button"
                    className="btn btn-secondary mr-2 cursor-pointer"
                    id="btn-letters"
                    onClick={ redirectToEmail.bind() }>
                      Letters
                  </button>
                </If>
                <button type="button"
                  className="btn btn-secondary mr-2 cursor-pointer"
                  id="btn-contract-notes"
                  onClick={ showHistoryButtonClick.bind(null, "contractNotes") }>
                  Contract Notes
                </button>
                <button type="button"
                  className="btn btn-secondary mr-2 cursor-pointer"
                  id="btn-claim-notes"
                  onClick={ showHistoryButtonClick.bind(null, "claimNotes") }>
                  Claim Notes
                </button>
                <button type="button"
                  className="btn btn-secondary mr-2 cursor-pointer"
                  id="btn-claim-events"
                  onClick={ showHistoryButtonClick.bind(null, "automated") }>
                  Claim Events
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

Header.propTypes = {
  claimObject: PropTypes.object,
  nextButtonOnClick: PropTypes.func,
  nextButtonId: PropTypes.string,
  nextButtonText: PropTypes.string,
  backButtonOnClick: PropTypes.func,
  claimStatus: PropTypes.string,
  showHistoryButtonClick: PropTypes.func,
  nextButtonDisabled: PropTypes.bool,
  showSyncGap: PropTypes.bool,
  assignButtonOnClick: PropTypes.func.isRequired,
  renderAssignOwnerButton: PropTypes.bool,
  isFinanceUser: PropTypes.bool,
  renderAssignMe: PropTypes.bool,
  handleAssignToMe: PropTypes.func,
  showVoidCheckButton: PropTypes.bool,
  handleOnClickVoidCheck: PropTypes.func,
  user: PropTypes.shape({
    banner_info: PropTypes.shape({
      header: PropTypes.string.isRequired,
      message: PropTypes.string.isRequired,
      enabled: PropTypes.bool.isRequired,
    }).isRequired,
  }),
};

Header.contextTypes = {
  router: PropTypes.object.isRequired
};
export default Header;
