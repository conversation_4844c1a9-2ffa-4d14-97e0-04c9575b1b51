import React, { useState } from "react";
import AttachmentModal from "../reusable/AttachmentModal/AttachmentModal.jsx";
import { URLS as apiUrls } from "../reusable/Utilities/urls";
import { json as ajax } from "./../../ajax.js";
import { addRecordNote } from "../reusable/RecordNotes/addRecordNotes.js";
import PropTypes from 'prop-types';

const FileAttachment = (props) => {
  const {
    claimId,
    attachmentParams,
    displayModal,
    closeModal,
    
  } = props;

  const [state, setState] = useState( () => initialState());

  // State object to handle operations on UI
  function initialState () {
    return {
      isUploading: false,
    };
  }

  
  const getParams = () => {
    return { "lwt_claim_id": claimId, "document_type_id": attachmentParams.documentTypeId };
  };

  const handleAttachmentUpload = (fileData, fileName, fileExtension) => {
    const params = getParams();
    params.file_content = fileData;
    params.file_name = fileName;
    params.file_type = fileExtension;

    setState({ isUploading: true });
    
    ajax(apiUrls.lwtClaimDocuments, params, { method: 'POST' }, (data, status) => {
      if (status === 200) {
        const noteText = `The file ${fileName} was attached to the ${attachmentParams.title}.`;
        addRecordNote(claimId, noteText, onAddNoteSuccess, null, apiUrls.lwtClaimRecordNotes);
      } else {
        setState({ isUploading: false });
        closeModal(false, true);
      }
    });
  };

  const onAddNoteSuccess = () => {
    setState({ isUploading: false }); 
    closeModal(true, false);
  };

  return (
    <section className="container">
      <AttachmentModal
        displayModal={ displayModal }
        handleUpload={ handleAttachmentUpload }
        closeModal={ closeModal }
        title={ attachmentParams.title }
        maxFileSizeInMBs= {10}
        isUploading={ state.isUploading }/>
    </section>
  );
};

FileAttachment.propTypes = {
  displayModal: PropTypes.bool.isRequired,
  closeModal: PropTypes.func.isRequired,
  maxFileSizeInMBs: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.number
  ]),
  isMultipleAllowed: PropTypes.bool,
  claimId: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.number
  ]),
  attachmentParams: PropTypes.shape({
    title: PropTypes.string,
    documentTypeId: PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.number
    ]),
    rowType: PropTypes.string,
    contractCode: PropTypes.string,
    contractNumber: PropTypes.string,
  })
};

export default FileAttachment;