import moment from "moment";
import React from "react";
import ReactTooltip from "react-tooltip";
import dateFormat from "./../reusable/Utilities/dateFormat.js";
import accounting from "accounting";
import PropTypes from "prop-types";
import If from "./../reusable/If/If.jsx";

export default class ClaimsList extends React.Component {

  constructor(props) {
    super(props);
    this.renderTableHeader = this.renderTableHeader.bind(this);
    this.renderTableBody = this.renderTableBody.bind(this);
    this.redirectToWorksheet = this.redirectToWorksheet.bind(this);
    this.renderTableBodyRow = this.renderTableBodyRow.bind(this);
  }

  static propTypes = {
    claimList: PropTypes.array.isRequired,
    sortClaimListByDOL: PropTypes.func.isRequired,
    lastOutSortOrder: PropTypes.oneOf(['asc', 'desc', '']),
    sortClaimListByOwner: PropTypes.func.isRequired,
    ownerSortOrder: PropTypes.oneOf(['asc', 'desc', '']),
    sortClaimListByInsuredName: PropTypes.func.isRequired,
    insuredNameSortOrder: PropTypes.oneOf(['asc', 'desc', '']),
    sortClaimListByOpened: PropTypes.func.isRequired,
    openedSortOrder: PropTypes.oneOf(['asc', 'desc', '']),
    sortClaimListByLastIn: PropTypes.func.isRequired,
    lastInSortOrder: PropTypes.oneOf(['asc', 'desc', '']),
  };

  static contextTypes = {
    router: PropTypes.object.isRequired
  };

  /**
   * This function will render table header.
   *
   * @return
   *    The table header.
   * */

  renderTableHeader() {
    return (
      <tr>
        <th className="cursor-pointer"
          id="label-insured-name"
          onClick={this.props.sortClaimListByInsuredName}>
          Insured Name&nbsp;
          <If condition={this.props.insuredNameSortOrder == 'asc'}>
            <i className="fa fa-caret-up"/>
          </If>
          <If condition={this.props.insuredNameSortOrder == 'desc'}>
            <i className="fa fa-caret-down"/>
          </If>
          <If condition={this.props.insuredNameSortOrder == ''}>
            <i className="fa fa-sort"/>
          </If>
        </th>
        <th id="label-contract-number">Contract #</th>
        <th className="cursor-pointer"
          id="label-assigned-to"
          onClick={this.props.sortClaimListByOwner}>
          Assigned To&nbsp;
          <If condition={this.props.ownerSortOrder == 'asc'}>
            <i className="fa fa-caret-up"/>
          </If>
          <If condition={this.props.ownerSortOrder == 'desc'}>
            <i className="fa fa-caret-down"/>
          </If>
          <If condition={this.props.ownerSortOrder == ''}>
            <i className="fa fa-sort"/>
          </If>
        </th>
        <th id="label-status">Status</th>
        <th className="cursor-pointer"
          id="label-opened"
          onClick={this.props.sortClaimListByOpened}>
          Opened&nbsp;
          <If condition={this.props.openedSortOrder == 'asc'}>
            <i className="fa fa-caret-up"/>
          </If>
          <If condition={this.props.openedSortOrder == 'desc'}>
            <i className="fa fa-caret-down"/>
          </If>
          <If condition={this.props.openedSortOrder == ''}>
            <i className="fa fa-sort"/>
          </If>
        </th>
        <th className="cursor-pointer"
          id="label-last-in"
          onClick={this.props.sortClaimListByLastIn}>
          Last In&nbsp;
          <If condition={this.props.lastInSortOrder == 'asc'}>
            <i className="fa fa-caret-up"/>
          </If>
          <If condition={this.props.lastInSortOrder == 'desc'}>
            <i className="fa fa-caret-down"/>
          </If>
          <If condition={this.props.lastInSortOrder == ''}>
            <i className="fa fa-sort"/>
          </If>
        </th>
        <th id="label-waiting-for">Waiting For</th>
        <th className="cursor-pointer"
          id="label-last-out"
          onClick={this.props.sortClaimListByDOL}>
          Last Out&nbsp;
          <If condition={this.props.lastOutSortOrder == 'asc'}>
            <i className="fa fa-caret-up"/>
          </If>
          <If condition={this.props.lastOutSortOrder == 'desc'}>
            <i className="fa fa-caret-down"/>
          </If>
          <If condition={this.props.lastOutSortOrder == ''}>
            <i className="fa fa-sort"/>
          </If>
        </th>
        <th id="label-estimate">Estimate</th>
        <th id="label-dol">DOL</th>
        <th id="label-state">State</th>
      </tr>
    );
  }

  /**
   * Provide ability to render table body.
   *
   * @return
   *    The table body.
   *    This function is using {props.claimList} to generate list.
   * */

  renderTableBody() {
    return this.props.claimList.map(this.renderTableBodyRow);
  }

  /**
   * Provide ability to render table body row.
   *
   * @return
   *    The claim list row.
   * */

  renderTableBodyRow(claimData, index) {
    const duration = moment().diff(moment(claimData['date_of_last_out']), 'days');
    let lastOutClassName = undefined;
    if (duration > 14) {
      lastOutClassName = "text-danger font-weight-bold";
    } else if (duration > 7) {
      lastOutClassName = "text-danger";
    }
    return (
      <tr key={index}>
        <td className="cursor-pointer long-text">
          <a href="#!"
            data-tip
            data-for={`insured_name${index}`}
            onClick={ this.redirectToWorksheet.bind(this, claimData) }
            className="users">
            {claimData['insured_name']}
          </a>
          <ReactTooltip id={`insured_name${index}`} aria-haspopup='true'>
            <p className="text-center">{claimData['insured_name']}</p>
          </ReactTooltip>
        </td>
        <td>
          <a href="#!"
            onClick={ this.redirectToWorksheet.bind(this, claimData) }>
            {claimData['contract_number']}
          </a>
        </td>
        <td className="cursor-pointer long-text">
          <span data-tip
            data-for={`assigned_to${index}`}>
            {claimData['assigned_to']}
          </span>
          <ReactTooltip id={`assigned_to${index}`} aria-haspopup='true'>
            <p className="text-center">{claimData['assigned_to']}</p>
          </ReactTooltip>
        </td>
        <td>
          {claimData['status']}
        </td>
        <td>
          {moment(claimData['date_of_claim_received']).format(dateFormat.displayDateFormat)}
        </td>
        <td>
          {moment(claimData['date_of_last_in']).format(dateFormat.displayDateFormat)}
        </td>
        <td className="cursor-pointer long-text">
          <span data-tip
            data-for={`waiting_for${index}`}>
            {claimData['waiting_for']}
          </span>
          <ReactTooltip id={`waiting_for${index}`} aria-haspopup='true'>
            <p className="text-center">{claimData['waiting_for']}</p>
          </ReactTooltip>
        </td>
        <td className={lastOutClassName}>
          {moment(claimData['date_of_last_out']).format(dateFormat.displayDateFormat)}
        </td>
        <td>
          { accounting.formatMoney(claimData['case_reserve'], '$', 2) }
        </td>
        <td>
          {moment.utc(claimData['date_of_loss']).local(true).format(dateFormat.displayDateFormat)}
        </td>
        <td>
          {claimData['state']}
        </td>
      </tr>
    );
  }

  redirectToWorksheet(claimData) {
    if (claimData.contract_number && claimData.id) {
      const query = {
        id: claimData.id,
        contract_number: claimData.contract_number
      };
      const route = { pathname: "/gap-claims", query };
      if (!this.context.router.isActive(route)) {
        this.context.router.push(route);
      }
    }
  }

  render() {
    return (
      <div className="claim-list">
        <table className="table table-striped table-responsive">
          <thead>
            {this.renderTableHeader()}
          </thead>
          <tbody>
            {this.renderTableBody()}
          </tbody>
        </table>
      </div>
    );
  }
}