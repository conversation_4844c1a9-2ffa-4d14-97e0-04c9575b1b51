import React from 'react';
import PropTypes from 'prop-types';
import { getClasses } from "../reusable/Utilities/headerClasses";

export default class PageHeader extends React.Component {

  static propTypes = {
    pageTitle: PropTypes.string.isRequired,
    backButtonText: PropTypes.string,
    onBackButtonClick: PropTypes.func,
    renderTemplate: PropTypes.func,
    user: PropTypes.shape({
      id: PropTypes.number.isRequired,
      first_name: PropTypes.string.isRequired,
      last_name: PropTypes.string.isRequired,
      email: PropTypes.string.isRequired,
      stores: PropTypes.arrayOf(PropTypes.string.isRequired),
      roles: PropTypes.shape({
        Map: PropTypes.object,
      }).isRequired,
      banner_info: PropTypes.shape({
        header: PropTypes.string.isRequired,
        message: PropTypes.string.isRequired,
        enabled: PropTypes.bool.isRequired,
      }).isRequired,
    })
  };

  constructor(props) {
    super(props);
    this.renderBackButton = this.renderBackButton.bind(this);
  }

  renderBackButton() {
    if (this.props.backButtonText) {
      return (
        <button className="btn btn-secondary" onClick={this.props.onBackButtonClick}>
          <i className="fa fa-arrow-left mr-2"/>{ this.props.backButtonText }
        </button>
      );
    }
  }

  renderTemplate() {
    if(this.props.renderTemplate) {
      return this.props.renderTemplate();
    }
  }

  render() {
    const {
      user : {
        banner_info: bannerInfo,
      },
    } = this.props;
    const classes = getClasses(bannerInfo);
    return (
      <section className="page-header">
        <div className={classes}>
          <h2>{ this.props.pageTitle }</h2>
          {this.renderBackButton()}
          {this.renderTemplate()}
        </div>
      </section>
    );
  }
}
