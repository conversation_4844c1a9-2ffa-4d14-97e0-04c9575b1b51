import React from "react";
import PageHeader from "../pageHeader/PageHeader.jsx";
import SearchBox from "../reusable/SearchBox/Search.jsx";
import Alert from "react-s-alert";
import ClaimsList from "./ClaimsList.jsx";
import { json as ajax } from "./../../ajax.js";
import Pagination from "./../../Pagination.jsx";
import Loader from "react-loader-advanced";
import accounting from "accounting";
import { URLS as apiUrls } from "./../reusable/Utilities/urls";
import { CONSTANTS } from "./../reusable/Constants/constants";
import PropTypes from "prop-types";
import If from "./../reusable/If/If.jsx";
import RecoverySubmitModal from "./../claimCheckList/RecoverySubmitModal.jsx";

export default class GapClaimsRecoveryDashboard extends React.Component {

  constructor(props) {
    super(props);
    this.state = {
      claimList: [],
      numberOfClaims: undefined,
      totalDifference: undefined,
      pageLimit: 20,
      showLoader: false,
      createCaseID: "",
      createCaseAction: "",
      recoveryReason: ""
    };
  }

  static contextTypes = {
    router: PropTypes.object.isRequired
  };

  static propTypes = {
    user: PropTypes.shape({
      id: PropTypes.number.isRequired,
      first_name: PropTypes.string.isRequired,
      last_name: PropTypes.string.isRequired,
      email: PropTypes.string.isRequired,
      stores: PropTypes.arrayOf(PropTypes.string.isRequired),
      roles: PropTypes.shape({
        Map: PropTypes.object,
      }).isRequired,
    }),
    location: PropTypes.shape({
      query: PropTypes.shape({
        page: PropTypes.string,
        q: PropTypes.string,
        status: PropTypes.string,
        sortBy: PropTypes.string,
        sortOrder: PropTypes.string,
        activeTab: PropTypes.string
      }).isRequired,
    }).isRequired,
  };

  componentDidMount = () => {
    document.title = 'TCA Portal - GAP Claims Recovery';
    this.loadClaimData();
  };

  UNSAFE_componentWillReceiveProps = nextProps => {
    if (this.currentPage(nextProps) !== this.currentPage(this.props) ||
      this.currentQ(nextProps) !== this.currentQ(this.props) ||
      this.currentStatus(nextProps) !== this.currentStatus(this.props) ||
      this.currentSortOrder(nextProps) !== this.currentSortOrder(this.props) ||
      this.currentSortBy(nextProps) !== this.currentSortBy(this.props) ||
      this.currentTab(nextProps) !== this.currentTab(this.props)) {
      this.setState({ q: nextProps.location.query.q || "" }, () => this.loadClaimData(nextProps));
    }
  };

  /**
   * This function will fetch claim data from backend based on fitlers provided.
   *
   * filters:
   *  q :
   *      Type: {String}
   *      Description: Search query.
   *  page:
   *      Type : {Number}
   *      Description: Page number to provide pagination support.
   *  sortBy:
   *      Type: {String},
   *      Values: oneOf(['asc','desc'])
   *  sortOrder:
   *      Type: {String},
   *      Values: oneOf (['contract_number', 'flagged, 'recovery_status', 'ins_value', 'nada', 'difference', 'insurance_company'])
   *  recovery_status:
   *      Type: {String}
   *      Values: (["All","RI", "IR", "R", "NR"]);
   *      All : All Claims
   *      IR: In Recovery
   *      R : Recovered
   *      NR: No Recovery
   * */
  loadClaimData = (props) => {
    if (!props) {
      props = this.props;
    }
    let url = `${apiUrls.gapRecovery}?page=${window.encodeURIComponent(this.currentPage(props))}`;
    if (this.currentTab(props) === "candidates") {
      url+="&recovery=false";
    }
    if (this.currentTab(props) === "cases") {
      url+="&recovery=true";
    }
    if (this.currentQ(props)) {
      url += (`&q=${window.encodeURIComponent(this.currentQ(props).trim())}`);
    }
    if (this.currentTab(props) === 'cases' && this.currentStatus(props)) {
      url += (`&recovery_status=${window.encodeURIComponent(this.currentStatus(props))}`);
    }
    if(this.currentSortBy(props)) {
      url += (`&sort_by=${window.encodeURIComponent(this.currentSortBy(props))}`);
    }
    if(this.currentSortOrder(props)) {
      url += (`&sort_order=${window.encodeURIComponent(this.currentSortOrder(props))}`);
    }

    this.setState({ showLoader: true }, () => {
      ajax(url, {}, {}, (data, status) => {
        if (status === 200) {
          this.setState({
            claimList: data.gap_claims,
            numberOfClaims: data.count,
            totalDifference: data.totalDifference,
            showLoader: false
          });
        } else {
          this.setState({ showLoader: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  currentPage = (props) => {
    if (!props) {
      props = this.props;
    }
    return parseInt(props.location.query.page, 10) || 1;
  };

  currentQ = (props) => {
    if (!props) {
      props = this.props;
    }
    return props.location.query.q;
  };

  currentTab = (props) => {
    if (!props) {
      props = this.props;
    }
    return props.location.query.activeTab || "candidates";
  };

  currentSortOrder = (props) => {
    if (!props) {
      props = this.props;
    }
    return props.location.query.sortOrder || '';
  };

  currentSortBy = (props) => {
    if (!props) {
      props = this.props;
    }
    return props.location.query.sortBy || '';
  };

  currentStatus = (props) => {
    if (!props) {
      props = this.props;
    }
    return props.location.query.status ? props.location.query.status : 'All';
  };

  setPage = (page) => {
    let query = { page };
    if (this.currentStatus()) {
      query.activeTab = this.currentTab();
    }
    if (this.currentQ()) {
      query.q = this.currentQ();
    }
    if (this.currentStatus()) {
      query.status = this.currentStatus();
    }
    if (this.currentSortBy()) {
      query.sortBy = this.currentSortBy();
    }
    if (this.currentSortOrder()) {
      query.sortOrder = this.currentSortOrder();
    }
    const route = { pathname: "/gap-claims-recovery-list", query };
    if (!this.context.router.isActive(route)) {
      this.context.router.push(route);
    }
  };

  onSearch = (q) => {
    if (this.currentQ() !== q) {
      let query = { q, page: 1 };
      if (this.currentStatus()) {
        query.activeTab = this.currentTab();
      }
      if (this.currentStatus()) {
        query.status = this.currentStatus();
      }
      if (this.currentSortBy()) {
        query.sortBy = this.currentSortBy();
      }
      if (this.currentSortOrder()) {
        query.sortOrder = this.currentSortOrder();
      }
      const route = { pathname: "/gap-claims-recovery-list", query };
      if (!this.context.router.isActive(route)) {
        this.context.router.push(route);
      }
    }
  };

  handleTabSwitch = (activeTab) => {
    let query = { page: 1, activeTab };
    if (this.currentQ()) {
      query.q = this.currentQ();
    }
    if (activeTab !== "candidates" && this.currentStatus()) {
      query.status = this.currentStatus();
    }
    const route = { pathname: "/gap-claims-recovery-list", query };
    if (!this.context.router.isActive(route)) {
      this.context.router.push(route);
    }
  };

  getData = () => {
    let data = {
      recovery_reason: this.state.recoveryReason
    };
    if(this.state.createCaseAction === 'create') {
      data['has_recovery'] = true;
      data['status'] = CONSTANTS.GAP_RECOVERY_STATUS_MAP.inRecovery.code;
    }
    if(this.state.createCaseAction === 'reject') {
      data['has_recovery'] = false;
      data['status'] = CONSTANTS.GAP_RECOVERY_STATUS_MAP.noRecovery.code;
    }
    return data;
  };

  onCreateCase = () => {
    const data = this.getData();
    this.setState({ showLoader: true }, () => {
      ajax(apiUrls.gapClaimRecovery.replace("__claimId__", this.state.createCaseID), data, { method: 'PUT' }, (data, status) => {
        if (status === 200) {
          this.setState({ createCaseAction: '', recoveryReason: '', createCaseID: '' }, () => this.loadClaimData(this.props));
        } else {
          this.setState({ showLoader: false }, ()=> {
            Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  getClaimsByStatus = (event) => {
    let query = { status: event.target.value, page: 1 };
    if (this.currentStatus()) {
      query.activeTab = this.currentTab();
    }
    if (this.currentQ()) {
      query.q = this.currentQ();
    }
    if (this.currentSortBy()) {
      query.sortBy = this.currentSortBy();
    }
    if (this.currentSortOrder()) {
      query.sortOrder = this.currentSortOrder();
    }
    const route = { pathname: "/gap-claims-recovery-list", query };
    if (!this.context.router.isActive(route)) {
      this.context.router.push(route);
    }
  };

  sortClaimList = (sortBy, sortOrder) => {
    let query = { sortBy, sortOrder, page: 1 };
    if (this.currentStatus()) {
      query.activeTab = this.currentTab();
    }
    if (this.currentQ()) {
      query.q = this.currentQ();
    }
    if (this.currentStatus()) {
      query.status = this.currentStatus();
    }
    const route = { pathname: "/gap-claims-recovery-list", query };
    if (!this.context.router.isActive(route)) {
      this.context.router.push(route);
    }
  };

  renderClaimListFilters = () => {
    return (
      <div className="form-inline row my-4 justify-content-end">
        <div className="form-group px-2">
          <strong className="text-muted text-left">
            {this.state.numberOfClaims ? `${this.state.numberOfClaims} Claims - Total Difference: ${accounting.formatMoney(this.state.totalDifference, '$', 2)}` : ""}
          </strong>
        </div>
        <If condition={ this.currentTab() === "cases" }>
          <div className="form-group px-3">
            <label htmlFor="claim-view-filter" className="pr-1">View: </label>
            <select id='claim-view-filter'
              className="form-control"
              value={ this.currentStatus() }
              onChange={ this.getClaimsByStatus }>
              <option value='All'>All</option>
              <option value={ CONSTANTS.GAP_RECOVERY_STATUS_MAP.inRecovery.code }>
                {CONSTANTS.GAP_RECOVERY_STATUS_MAP.inRecovery.name}
              </option>
              <option value={ CONSTANTS.GAP_RECOVERY_STATUS_MAP.possibleRecovery.code }>
                {CONSTANTS.GAP_RECOVERY_STATUS_MAP.possibleRecovery.name}
              </option>
              <option value={ CONSTANTS.GAP_RECOVERY_STATUS_MAP.waitingRecovery.code }>
                {CONSTANTS.GAP_RECOVERY_STATUS_MAP.waitingRecovery.name}
              </option>
              <option value={ CONSTANTS.GAP_RECOVERY_STATUS_MAP.recovered.code }>
                {CONSTANTS.GAP_RECOVERY_STATUS_MAP.recovered.name}
              </option>
              <option value={ CONSTANTS.GAP_RECOVERY_STATUS_MAP.noRecovery.code }>
                {CONSTANTS.GAP_RECOVERY_STATUS_MAP.noRecovery.name}
              </option>
            </select>
          </div>
        </If>
      </div>
    );
  };

  renderClaimList = () => {
    if (this.state.claimList && this.state.claimList.length > 0) {
      return (
        <ClaimsList claimList={ this.state.claimList }
          sortClaimList={ this.sortClaimList }
          sortBy={ this.currentSortBy() }
          activeTab={ this.currentTab() }
          user={ this.props.user }
          handleCaseCreation={ (createCaseID, createCaseAction) => this.setState({ createCaseID, createCaseAction }) }
          sortOrder={ this.currentSortOrder() }/>
      );
    } else {
      return (
        <div className="text-center">
          <p>No Results available.</p>
        </div>
      );
    }
  };

  renderPagination = () => {
    if (this.state.numberOfClaims > this.state.pageLimit) {
      return (
        <div className="pagination-container clearfix col-12 px-0">
          <div className="pull-left my-1">
            <p>Showing {this.currentPage() * this.state.pageLimit - this.state.pageLimit + 1}&nbsp;
              to {this.currentPage() * this.state.pageLimit > this.state.numberOfClaims ? this.state.numberOfClaims : this.currentPage() * this.state.pageLimit}&nbsp;
              of {this.state.numberOfClaims} items
            </p>
          </div>
          <div className="float-right">
            <Pagination page={ this.currentPage() } count={ this.state.numberOfClaims }
              limit={ this.state.pageLimit } setPage={ this.setPage }/>
          </div>
        </div>
      );
    }
  };

  renderTabs = () => {
    return(
      <ul className="nav nav-tabs pt-1 my-4" role="tablist" id="contractsTabPanel">
        <li className="nav-item cursor-pointer">
          <a
            className={ `nav-link ${this.currentTab() === "candidates" ? "active" : ""}` }
            data-toggle="tab"
            role="tab"
            accessKey="candidates"
            aria-controls="candidates"
            onClick={ this.handleTabSwitch.bind(this, "candidates") }>
            Recovery Candidates
          </a>
        </li>
        <li className="nav-item cursor-pointer">
          <a
            className={ `nav-link ${this.currentTab() === "cases" ? "active" : ""}` }
            data-toggle="tab"
            role="tab"
            accessKey="cases"
            aria-controls="cases"
            onClick={ this.handleTabSwitch.bind(this, "cases") }>
            Recovery Cases
          </a>
        </li>
      </ul>
    );
  };

  render() {
    const spinnerMessage = <p className='text-center'><i className="fa fa-refresh fa-spin"/> Loading...</p>;
    const { createCaseAction, createCaseID, recoveryReason, showLoader } = this.state;
    return (
      <section className="gap-dashboard clearfix">
        <Loader show={ showLoader } message={ spinnerMessage }>
          <div className="row">
            <div className="col-12">
              <PageHeader pageTitle="Recovery Investigations" user={this.props.user}/>
              <div className="row">
                <div className="form-inline my-2 col-12">
                  <div className="col-6 px-0">
                    <SearchBox onSearch={ this.onSearch }
                      placeholder="Search Contract #, Insurance Company"
                      value={ this.currentQ() }/>
                  </div>
                </div>
              </div>
              {this.renderClaimListFilters()}
              {this.renderTabs()}
              {this.renderClaimList()}
              {this.renderPagination()}
            </div>
          </div>
        </Loader>
        <RecoverySubmitModal
          displayModal={ !!(createCaseAction && createCaseID) }
          onChange={ (e) => this.setState({ recoveryReason: e.target.value }) }
          hasRecovery={ createCaseAction === 'create' }
          recoveryReason={ recoveryReason }
          onDecline={ () => {this.setState({ createCaseAction: '', recoveryReason: '', createCaseID: '' });} }
          onSubmit={ this.onCreateCase }/>
      </section>
    );
  }
}