import moment from "moment";
import React from "react";
import ReactTooltip from "react-tooltip";
import dateFormat from "./../reusable/Utilities/dateFormat.js";
import accounting from "accounting";
import PropTypes from "prop-types";
import If from "./../reusable/If/If.jsx";
import { CONSTANTS } from "./../reusable/Constants/constants";
import { userHasRole } from '../reusable/Utilities/userHasRole';

export default class ClaimsList extends React.Component {
  static propTypes = {
    claimList: PropTypes.array.isRequired,
    sortClaimList: PropTypes.func.isRequired,
    sortOrder: PropTypes.oneOf(['asc', 'desc', '']),
    sortBy: PropTypes.string,
    activeTab: PropTypes.oneOf(["cases", "candidates"]),
    handleCaseCreation: PropTypes.func,
    user: PropTypes.shape({
      id: PropTypes.number.isRequired,
      first_name: PropTypes.string.isRequired,
      last_name: PropTypes.string.isRequired,
      email: PropTypes.string.isRequired,
      stores: PropTypes.arrayOf(PropTypes.string.isRequired),
      roles: PropTypes.shape({
        Map: PropTypes.object,
      }).isRequired
    })
  };

  static contextTypes = {
    router: PropTypes.object.isRequired
  };

  getGapClosed = ({ status, gap_closed }) => {
    const statusMap = CONSTANTS.STATUS_MAP;
    if([statusMap.checkVoided, statusMap.checkWritten, statusMap.denied, statusMap.closedNoResponse, statusMap.noGap].includes(status)) {
      return moment(gap_closed, dateFormat.backendDateFormat).format(dateFormat.displayDateFormat);
    }
    return null;
  };

  handleSort = (toBeSorted) => {
    const { sortBy, sortOrder, sortClaimList } = this.props;
    if(toBeSorted === sortBy && sortOrder === 'asc') {
      sortClaimList(sortBy, 'desc');
    } else if(toBeSorted === sortBy && sortOrder === 'desc') {
      sortClaimList(sortBy, 'asc');
    } else {
      sortClaimList(toBeSorted, 'asc');
    }
  };

  renderTableBody = (activeTab, claimList) => {
    if(activeTab === "cases") {
      return claimList.map(this.renderCasesTableBodyRow);
    } else if(activeTab === "candidates") {
      return claimList.map(this.renderCandidatesTableBodyRow);
    }
  };

  renderTableHeader = (activeTab) => {
    if(activeTab === "cases") {
      return this.renderCasesTableHeader();
    } else if(activeTab === "candidates") {
      return this.renderCandidatesTableHeader();
    }
  };

  renderCasesTableHeader = () => {
    return (
      <tr>
        <th className="cursor-pointer"
          id="label-cases-customer-name"
          onClick={ this.handleSort.bind(this,"customer_name") }>
          {"Customer Name "}
          <If condition={ this.props.sortBy === 'customer_name' &&  this.props.sortOrder === 'asc' }>
            <i className="fa fa-caret-up"/>
          </If>
          <If condition={ this.props.sortBy === 'customer_name' &&  this.props.sortOrder === 'desc' }>
            <i className="fa fa-caret-down"/>
          </If>
          <If condition={ this.props.sortBy !== 'customer_name' }>
            <i className="fa fa-sort"/>
          </If>
        </th>
        <th className="cursor-pointer"
          id="label-recovery-contract-number"
          onClick={ this.handleSort.bind(this, 'contract_number') }>
          Recovery #&nbsp;
          <If condition={ this.props.sortBy === 'contract_number' &&  this.props.sortOrder === 'asc' }>
            <i className="fa fa-caret-up"/>
          </If>
          <If condition={ this.props.sortBy === 'contract_number' &&  this.props.sortOrder === 'desc' }>
            <i className="fa fa-caret-down"/>
          </If>
          <If condition={ this.props.sortBy !== 'contract_number' }>
            <i className="fa fa-sort"/>
          </If>
        </th>
        <th className="cursor-pointer"
          id="label-status"
          onClick={ this.handleSort.bind(this, 'recovery_status') }>
           Status&nbsp;
          <If condition={ this.props.sortBy === 'recovery_status' &&  this.props.sortOrder === 'asc' }>
            <i className="fa fa-caret-up"/>
          </If>
          <If condition={ this.props.sortBy === 'recovery_status' &&  this.props.sortOrder === 'desc' }>
            <i className="fa fa-caret-down"/>
          </If>
          <If condition={ this.props.sortBy !== 'recovery_status' }>
            <i className="fa fa-sort"/>
          </If>
        </th>
        <th className="cursor-pointer"
          id="label-submitted"
          onClick={ this.handleSort.bind(this, 'submitted') }>
          Submitted&nbsp;
          <If condition={ this.props.sortBy === 'submitted' &&  this.props.sortOrder === 'asc' }>
            <i className="fa fa-caret-up"/>
          </If>
          <If condition={ this.props.sortBy === 'submitted' &&  this.props.sortOrder === 'desc' }>
            <i className="fa fa-caret-down"/>
          </If>
          <If condition={ this.props.sortBy !== 'submitted' }>
            <i className="fa fa-sort"/>
          </If>
        </th>
        <th className="cursor-pointer"
          id="label-updated"
          onClick={ this.handleSort.bind(this, 'updated') }>
          Updated&nbsp;
          <If condition={ this.props.sortBy === 'updated' &&  this.props.sortOrder === 'asc' }>
            <i className="fa fa-caret-up"/>
          </If>
          <If condition={ this.props.sortBy === 'updated' &&  this.props.sortOrder === 'desc' }>
            <i className="fa fa-caret-down"/>
          </If>
          <If condition={ this.props.sortBy !== 'updated' }>
            <i className="fa fa-sort"/>
          </If>
        </th>
        <th className="cursor-pointer"
          id="label-allied-claim-number"
          onClick={ this.handleSort.bind(this, 'allied_claim_number') }>
          Allied Claim #&nbsp;
          <If condition={ this.props.sortBy === 'allied_claim_number' &&  this.props.sortOrder === 'asc' }>
            <i className="fa fa-caret-up"/>
          </If>
          <If condition={ this.props.sortBy === 'allied_claim_number' &&  this.props.sortOrder === 'desc' }>
            <i className="fa fa-caret-down"/>
          </If>
          <If condition={ this.props.sortBy !== 'allied_claim_number' }>
            <i className="fa fa-sort"/>
          </If>
        </th>
        <th className="cursor-pointer"
          id="label-difference"
          onClick={ this.handleSort.bind(this, 'difference') }>
          $ Difference&nbsp;
          <If condition={ this.props.sortBy === 'difference' &&  this.props.sortOrder === 'asc' }>
            <i className="fa fa-caret-up"/>
          </If>
          <If condition={ this.props.sortBy === 'difference' &&  this.props.sortOrder === 'desc' }>
            <i className="fa fa-caret-down"/>
          </If>
          <If condition={ this.props.sortBy !== 'difference' }>
            <i className="fa fa-sort"/>
          </If>
        </th>
        <th className="cursor-pointer"
          id="label-gap-closed"
          onClick={ this.handleSort.bind(this, 'gap_closed') }>
          GAP closed&nbsp;
          <If condition={ this.props.sortBy === 'gap_closed' &&  this.props.sortOrder === 'asc' }>
            <i className="fa fa-caret-up"/>
          </If>
          <If condition={ this.props.sortBy === 'gap_closed' &&  this.props.sortOrder === 'desc' }>
            <i className="fa fa-caret-down"/>
          </If>
          <If condition={ this.props.sortBy !== 'gap_closed' }>
            <i className="fa fa-sort"/>
          </If>
        </th>
      </tr>
    );
  };

  renderCandidatesTableHeader = () => {
    return (
      <tr>
        <th className="cursor-pointer"
          id="label-candidates-customer-name"
          onClick={ this.handleSort.bind(this,"customer_name") }>
          {"Customer Name "}
          <If condition={ this.props.sortBy === 'customer_name' &&  this.props.sortOrder === 'asc' }>
            <i className="fa fa-caret-up"/>
          </If>
          <If condition={ this.props.sortBy === 'customer_name' &&  this.props.sortOrder === 'desc' }>
            <i className="fa fa-caret-down"/>
          </If>
          <If condition={ this.props.sortBy !== 'customer_name' }>
            <i className="fa fa-sort"/>
          </If>
        </th>
        <th className="cursor-pointer"
          id="label-gap-contract-number"
          onClick={ this.handleSort.bind(this, 'contract_number') }>
          GAP #&nbsp;
          <If condition={ this.props.sortBy === 'contract_number' &&  this.props.sortOrder === 'asc' }>
            <i className="fa fa-caret-up"/>
          </If>
          <If condition={ this.props.sortBy === 'contract_number' &&  this.props.sortOrder === 'desc' }>
            <i className="fa fa-caret-down"/>
          </If>
          <If condition={ this.props.sortBy !== 'contract_number' }>
            <i className="fa fa-sort"/>
          </If>
        </th>
        <th className="cursor-pointer"
          id="label-gap-status"
          onClick={ this.handleSort.bind(this, 'status') }>
          GAP Status&nbsp;
          <If condition={ this.props.sortBy === 'status' &&  this.props.sortOrder === 'asc' }>
            <i className="fa fa-caret-up"/>
          </If>
          <If condition={ this.props.sortBy === 'status' &&  this.props.sortOrder === 'desc' }>
            <i className="fa fa-caret-down"/>
          </If>
          <If condition={ this.props.sortBy !== 'status' }>
            <i className="fa fa-sort"/>
          </If>
        </th>
        <th className="cursor-pointer"
          id="label-flagged"
          onClick={ this.handleSort.bind(this, 'flagged') }>
          Flagged&nbsp;
          <If condition={ this.props.sortBy === 'flagged' &&  this.props.sortOrder === 'asc' }>
            <i className="fa fa-caret-up"/>
          </If>
          <If condition={ this.props.sortBy === 'flagged' &&  this.props.sortOrder === 'desc' }>
            <i className="fa fa-caret-down"/>
          </If>
          <If condition={ this.props.sortBy !== 'flagged' }>
            <i className="fa fa-sort"/>
          </If>
        </th>
        <th className="cursor-pointer"
          id="label-gap-closed"
          onClick={ this.handleSort.bind(this, 'gap_closed') }>
          GAP Closed&nbsp;
          <If condition={ this.props.sortBy === 'gap_closed' &&  this.props.sortOrder === 'asc' }>
            <i className="fa fa-caret-up"/>
          </If>
          <If condition={ this.props.sortBy === 'gap_closed' &&  this.props.sortOrder === 'desc' }>
            <i className="fa fa-caret-down"/>
          </If>
          <If condition={ this.props.sortBy !== 'gap_closed' }>
            <i className="fa fa-sort"/>
          </If>
        </th>
        <th className="cursor-pointer"
          id="label-ins-value"
          onClick={ this.handleSort.bind(this, 'ins_value') }>
          Ins Value&nbsp;
          <If condition={ this.props.sortBy === 'ins_value' &&  this.props.sortOrder === 'asc' }>
            <i className="fa fa-caret-up"/>
          </If>
          <If condition={ this.props.sortBy === 'ins_value' &&  this.props.sortOrder === 'desc' }>
            <i className="fa fa-caret-down"/>
          </If>
          <If condition={ this.props.sortBy !== 'ins_value' }>
            <i className="fa fa-sort"/>
          </If>
        </th>
        <th className="cursor-pointer"
          id="label-difference"
          onClick={ this.handleSort.bind(this, 'difference') }>
          $ Difference&nbsp;
          <If condition={ this.props.sortBy === 'difference' &&  this.props.sortOrder === 'asc' }>
            <i className="fa fa-caret-up"/>
          </If>
          <If condition={ this.props.sortBy === 'difference' &&  this.props.sortOrder === 'desc' }>
            <i className="fa fa-caret-down"/>
          </If>
          <If condition={ this.props.sortBy !== 'difference' }>
            <i className="fa fa-sort"/>
          </If>
        </th>
        <th className="cursor-pointer"
          id="label-action">
          Action
        </th>
      </tr>
    );
  };

  renderCasesTableBodyRow = (claimData, index) => {
    return (
      <tr key={ claimData.id }>
        <td className="long-text">
          <span data-tip
            data-for={ `customer_name_${index}` }>
            {claimData['customer_name']}
          </span>
          <ReactTooltip id={ `customer_name_${index}` } aria-haspopup='true'>
            <p className="text-center">{claimData['customer_name']}</p>
          </ReactTooltip>
        </td>
        <td className="cursor-pointer long-text">
          <a href="#!"
            onClick={ this.redirectToWorksheet.bind(this, claimData) }
            className="users">
            {claimData['contract_number']}
          </a>
        </td>
        <td>
          {claimData['recovery_status']}
        </td>
        <td>
          {moment(claimData['submitted'], dateFormat.backendDateFormat).format(dateFormat.displayDateFormat)}
        </td>
        <td>
          {moment(claimData['updated'], dateFormat.backendDateFormat).format(dateFormat.displayDateFormat)}
        </td>
        <td>
          { claimData['allied_claim_number'] }
        </td>
        <td>
          { accounting.formatMoney(claimData['difference'], '$', 2) }
        </td>
        <td>
          {this.getGapClosed(claimData)}
        </td>
      </tr>
    );
  };

  renderCandidatesTableBodyRow = (claimData, index) => {
    return (
      <tr key={ claimData.id }>
        <td className="long-text">
          <span data-tip
            data-for={ `customer_name_${index}` }>
            {claimData['customer_name']}
          </span>
          <ReactTooltip id={ `customer_name_${index}` } aria-haspopup='true'>
            <p className="text-center">{claimData['customer_name']}</p>
          </ReactTooltip>
        </td>
        <td className="long-text">
          <If condition={ userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaims) ||
          userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaimsManager) ||
          userHasRole(this.props.user, CONSTANTS.USER_ROLES.viewOnlyClaims) }>
            <a href="#!"
              onClick={ this.redirectToWorksheet.bind(this, claimData) }
              className="users cursor-pointer">
              {claimData['contract_number']}
            </a>
          </If>
          <If condition={ !(userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaims) ||
          userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaimsManager)) }>
            <span>
              {claimData['contract_number']}
            </span>
          </If>
        </td>
        <td>
          {claimData['status']}
        </td>
        <td>
          {moment(claimData['flagged'], dateFormat.backendDateFormat).format(dateFormat.displayDateFormat)}
        </td>
        <td>
          {this.getGapClosed(claimData)}
        </td>
        <td>
          { accounting.formatMoney(claimData['ins_value'], '$', 2) }
        </td>
        <td>
          { accounting.formatMoney(claimData['difference'], '$', 2) }
        </td>
        <td>
          <div className="row">
            <button type="button"
              className="btn btn-sm btn-primary cursor-pointer mr-3"
              onClick={ () => this.props.handleCaseCreation(claimData.id, 'create') }>
              Create case
            </button>
            <button type="button"
              className="btn btn-sm btn-secondary cursor-pointer"
              onClick={ () => this.props.handleCaseCreation(claimData.id, 'reject') }>
              No
            </button>
          </div>
        </td>
      </tr>
    );
  };

  redirectToWorksheet = (claimData) => {
    if (claimData.contract_number && claimData.id) {
      const query = {
        id: claimData.id,
        contract_number: claimData.contract_number
      };
      let pathname;
      if(this.props.activeTab === 'cases') {
        pathname = "/gap-claims-recovery";
      } else if(this.props.activeTab === 'candidates') {
        pathname = "/gap-claims";
      }
      const route = { pathname, query };
      if (!this.context.router.isActive(route)) {
        this.context.router.push(route);
      }
    }
  };

  render() {
    const { claimList, activeTab } = this.props;
    return (
      <div className="claim-list table-responsive">
        <table className="table table-striped">
          <thead>
            {this.renderTableHeader(activeTab)}
          </thead>
          <tbody>
            {this.renderTableBody(activeTab, claimList)}
          </tbody>
        </table>
      </div>
    );
  }
}