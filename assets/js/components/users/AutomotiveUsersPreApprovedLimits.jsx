import React, {useState, useEffect} from 'react';
import <PERSON>Header from '../pageHeader/PageHeader.jsx';
import Alert from 'react-s-alert';
import { json as ajax } from './../../ajax.js';
import Loader from 'react-loader-advanced';
import { URLS as apiUrls } from "./../reusable/Utilities/urls";
import InputBox from "./../reusable/InputBox/InputBox.jsx";
import PropTypes from 'prop-types';
import { CONSTANTS } from "./../reusable/Constants/constants.js";

AutomotivePreApprovedLimits.contextTypes = {
  router: PropTypes.object.isRequired
};

AutomotivePreApprovedLimits.propTypes = {
  user: PropTypes.shape({
    id: PropTypes.number.isRequired,
    first_name: PropTypes.string.isRequired,
    last_name: PropTypes.string.isRequired,
    email: PropTypes.string.isRequired,
    stores: PropTypes.arrayOf(PropTypes.string.isRequired),
    roles: PropTypes.shape({
      Map: PropTypes.object,
    }).isRequired
  }),
};

const initialState = {
  userList: [],
  changedLimitList: [],
  showLoader: false,
  showInactive: false,
};

export default function AutomotivePreApprovedLimits (props,context) {

  const [state, setState] = useState(initialState);


  useEffect(() => {
    document.title = 'TCA Portal - Automotive Claims Pre Approve Limits';
    loadUsersList();
  },[state.showInactive]);

  const loadUsersList = () => {
    var url = apiUrls.usersPreApprovedLimits+"?role="+CONSTANTS.USER_ROLES.autoClaims+"&show_inactive="+state.showInactive;
    setState({...state, showLoader: true});
    
    ajax(url, {}, {}, (data, status) => {
      if (status === 200) {
        setState({...state, userList: data.users || [],changedLimitList: [],showLoader: false});
      } else {
        setState({...state, showLoader: false }, () => {
          Alert.error("Error loading user approved list");
        });
      }
    });
  };

  const handleLimitChange = (id, newValue) => {
    let changedLimitList = state.changedLimitList.slice(0);
    let userIndex = changedLimitList.findIndex((userData) => {
      return id === userData.user_id;
    });
    if (userIndex !== -1) {
      changedLimitList[userIndex].approved_limit = newValue;
      setState({...state, changedLimitList: changedLimitList });
    } else {
      let userData = state.userList.find((userData) => {
        return id === userData.user_id;
      });
      userData.approved_limit = newValue;
      changedLimitList.push(userData);
      setState({...state, changedLimitList:changedLimitList });
    }
  };

  const renderTableBodyRow = (userData) => {
    return (
      <tr key={userData.user_id}>
        <td>
          {userData.name}
        </td>
        <td>
          <InputBox type="Currency"
            id={ `${userData.user_id}_inputBox` }
            customClass="col-6 pl-0"
            hasDefaultValue={true}
            value={ userData.approved_limit }
            onChange={ (v) => handleLimitChange(userData.user_id, v) }
            onBlur={ (v) => handleLimitChange(userData.user_id, v) }/>
        </td>
      </tr>
    );
  };

  const renderUsersPreApprovedLimits = () => {
    if (state.userList && state.userList.length > 0) {
      return (
        <div className="col-6">
          <table className="table">
            <thead>
              <tr>
                <th>Agent</th>
                <th>$ Limit</th>
              </tr>
            </thead>
            <tbody>
              {state.userList.map(renderTableBodyRow)}
            </tbody>
          </table>
        </div>
      );
    } else {
      return (
        <div className="text-center">
          <p>No Results available for this search query</p>
        </div>
      );
    }
  };

  const onSubmit = () => {
    setState({...state, showLoader: true });
    
    ajax(apiUrls.usersPreApprovedLimits, state.changedLimitList, { method: 'PUT' }, (data, status) => {
      if (status === 200) {
        loadUsersList();
        Alert.success("Updated limit successfully.");
      } else {
        Alert.error(data.message);
        setState({...state, showLoader: false }, () => {
        });
      }
    });
  };

  const renderSubmitButton = () => {
    let isClickable = state.changedLimitList.length === 0;
    return (
      <button className="btn btn-primary ml-3" onClick={onSubmit} disabled={isClickable}>
        Submit
      </button>
    );
  };

  const renderMessage = () => {
    return (
      <div className="row ml-3">
        <p>Claims that are less than the limit you set below will be auto approved when submitted for approval by the
          agent.</p>
      </div>
    );
  };

  const renderCheckBox = () => {
    return (<div>
      <label className="form-check form-check-label col-form-label col">
        <input type="checkbox" className="form-check-input" value="all"
          checked={ state.showInactive }
          onChange={ () => {
            setState({...state, showInactive:!state.showInactive}, ()=>{loadUsersList();});
          } }
        />
        <span><strong>Show Inactive</strong></span>
      </label>
    </div>);
  };

 
  const spinnerMessage = <p className='text-center'><i className="fa fa-refresh fa-spin"/> Loading...</p>;
  return (
    <Loader show={state.showLoader} message={spinnerMessage}>
      <section className="gap-dashboard clearfix">
        <div className="row">
          <div className="col-12">
            <PageHeader pageTitle="Automotive Claim Pre Approved Limits"
              backButtonText="Back"
              onBackButtonClick={context.router.goBack}
              renderTemplate={renderSubmitButton}
              user={props.user}/>

            { renderMessage() }
            { renderCheckBox() }
            { renderUsersPreApprovedLimits() }
          </div>
        </div>
      </section>
    </Loader>
  );
}