import React, { useEffect, useState } from 'react';
import PageHeader from '../pageHeader/PageHeader.jsx';
import Alert from 'react-s-alert';
import { json as ajax } from './../../ajax.js';
import Loader from 'react-loader-advanced';
import { URLS as apiUrls } from "./../reusable/Utilities/urls";
import InputBox from "./../reusable/InputBox/InputBox.jsx";
import PropTypes from 'prop-types';
import { CONSTANTS } from "./../reusable/Constants/constants.js";

const LwtGapUsersPreApprovedLimit = (props, context) => {
  const [users, setUsers] = useState(() => []);
  const [changedList, setChangedList] = useState(() => []);
  const [loader, setLoader] = useState(false);
  const [showInactive, setShowInactive] = useState(false);
  const spinnerMessage = <p className='text-center'><i className="fa fa-refresh fa-spin"/> Loading...</p>;

  useEffect(() => {
    // Set document title
    document.title = 'TCA Portal - Automotive Claims';

    // Load supporting data
    loadSupportingData();
  },[showInactive]);

  const loadSupportingData = () => {
    loadUsersList();
  };

  const loadUsersList = () => {
    setLoader(true);
    var url = apiUrls.usersPreApprovedLimits+"?role="+CONSTANTS.USER_ROLES.gapClaims+"&show_inactive="+showInactive;
    ajax(url, {}, {}, (data, status) => {
      if (status === 200) {
        setUsers(data.users);
        setChangedList([]);
        setLoader(false);
      } else {
        setLoader(false);
        if (data.message) {
          Alert.error(data.message);
        } else {
          Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
        }
      }
    });
  };

  const onSubmit = () => {
    setLoader(true);
    ajax(apiUrls.usersPreApprovedLimits, changedList, { method: 'PUT' }, (data, status) => {
      if (status === 200) {
        loadUsersList();
        Alert.success("Updated limit successfully.");
      } else {
        setLoader(false);
        if (data.message) {
          Alert.error(data.message);
        } else {
          Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
        }
      }
    });
  };

  const handleGapLimitChange = (id, newValue) => {
    let changedLimitList = changedList.slice(0);
    let userIndex = changedLimitList.findIndex((userData) => {
      return id === userData.user_id;
    });
    if (userIndex !== -1) {
      changedLimitList[userIndex].gap_approved_limit = newValue || '0.00';
      setChangedList(changedLimitList);
    } else {
      let userData = users.find((userData) => {
        return id === userData.user_id;
      });
      userData.gap_approved_limit = newValue || '0.00';
      changedLimitList.push(userData);
      setChangedList(changedLimitList);
    }
  };

  const handleLwtLimitChange = (id, newValue) => {
    let changedLimitList = changedList.slice(0);
    let userIndex = changedLimitList.findIndex((userData) => {
      return id === userData.user_id;
    });
    if (userIndex !== -1) {
      changedLimitList[userIndex].lwt_approved_limit = newValue || '0.00';
      setChangedList(changedLimitList);
    } else {
      let userData = users.find((userData) => {
        return id === userData.user_id;
      });
      userData.lwt_approved_limit = newValue || '0.00';
      changedLimitList.push(userData);
      setChangedList(changedLimitList);
    }
  };

  const renderTableBody = () => {
    return users.map(renderTableBodyRow);
  };

  const renderUserLimits = () => {
    if (users && users.length > 0) {
      return (
        <div className="col-12">
          <table className="table">
            <thead>
              <tr>
                <th>Agent</th>
                <th>$ GAP Limit</th>
                <th>$ LWT Limit</th>
              </tr>
            </thead>
            <tbody>
              {renderTableBody()}
            </tbody>
          </table>
        </div>
      );
    } else {
      return (
        <div className="text-center">
          <p>No Results available for this search query</p>
        </div>
      );
    }
  };

  const renderTableBodyRow = (userData) => {
    return (
      <tr key={userData.user_id}>
        <td>
          {userData.name}
        </td>
        <td>
          <InputBox type="Currency"
            id={ `${userData.user_id}_gap_inputBox` }
            customClass="col-6 pl-0"
            hasDefaultValue={true}
            value={ userData.gap_approved_limit }
            onChange={ (v) => handleGapLimitChange(userData.user_id, v) }
          />
        </td>
        <td>
          <InputBox type="Currency"
            id={ `${userData.user_id}_lwt_inputBox` }
            customClass="col-6 pl-0"
            hasDefaultValue={true}
            value={ userData.lwt_approved_limit }
            onChange={ (v) => handleLwtLimitChange(userData.user_id, v) }
          />
        </td>
      </tr>
    );
  };

  const renderSubmitButton = () => {
    return (
      <button className="btn btn-primary ml-3" onClick={onSubmit} disabled={changedList.length === 0}>
        Submit
      </button>
    );
  };

  const renderMessage = () => {
    return (
      <div className="row ml-3">
        <p>Claims that are less than the limit you set below will be auto approved when submitted for approval by the
          agent.</p>
      </div>
    );
  };

  const renderCheckBox = () => {
    return (
      <div>
        <label className="form-check form-check-label col-form-label col">
          <input type="checkbox" className="form-check-input" value="all"
            checked={ showInactive }
            onChange={ () => {setShowInactive(!showInactive);} }
          />
          <span><strong>Show Inactive</strong></span>
        </label>
      </div>
    );
  };

  return (
    <Loader show={loader} message={spinnerMessage}>
      <section className="gap-dashboard clearfix">
        <div className="row">
          <div className="col-12">
            <PageHeader pageTitle="GAP/LWT Claim Pre Approved Limits"
              backButtonText="Back"
              onBackButtonClick={context.router.goBack}
              renderTemplate={renderSubmitButton}
              user={props.user}/>
            { renderMessage() }
            { renderCheckBox() }
            { renderUserLimits() }
          </div>
        </div>
      </section>
    </Loader>
  );

};

LwtGapUsersPreApprovedLimit.propTypes = {
  user: PropTypes.shape({
    id: PropTypes.number.isRequired,
    first_name: PropTypes.string.isRequired,
    last_name: PropTypes.string.isRequired,
    email: PropTypes.string.isRequired,
    stores: PropTypes.arrayOf(PropTypes.string.isRequired),
    roles: PropTypes.shape({
      Map: PropTypes.object,
    }).isRequired,
    banner_info: PropTypes.shape({
      header: PropTypes.string.isRequired,
      message: PropTypes.string.isRequired,
      enabled: PropTypes.bool.isRequired,
    }).isRequired,
  })
};

LwtGapUsersPreApprovedLimit.contextTypes = {
  router: PropTypes.object.isRequired,
};

export default LwtGapUsersPreApprovedLimit;