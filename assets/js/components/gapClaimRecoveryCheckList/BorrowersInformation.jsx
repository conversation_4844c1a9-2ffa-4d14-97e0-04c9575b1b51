import React from "react";
import { CONSTANTS } from "./../reusable/Constants/constants.js";
import PropTypes from "prop-types";
import InputBoxRow from "../claimCheckList/InputBoxRow.jsx";

const BorrowersInformation = ({
  claimObject,
  onCursorValueChange,
  getFieldNotes,
  getFieldDocs,
  selectedFieldNote,
  handleItemNoteClick,
  handleMouseEnter,
  handleMouseLeave,
  handleAttachmentClick,
  deleteAttachment,
  rowType,
  rowStyle,
  attachments,
  notes,
}) => {
  const COMPONENT_MAP = CONSTANTS.RECOVERY_WORKSHEET_COMPONENT_MAP;
  return (
    <section>
      <div className="form-group row my-4">
        <span className="col-form-label col-form-label-sm col-6">
          <strong>Borrowers Information</strong>
        </span>
      </div>
      <InputBoxRow labelContainerClass="col-3"
        inputBoxContainerClass="col-2 text-primary"
        containerClass="justify-content-end"
        hasLabel={ true }
        hideAttachment={ true }
        hideNotes={ true }
        hasClickToCopy={ true }
        showManagerCheckbox={ false }
        label="Last Name:"
        textValue={ claimObject.last_name }/>
      <InputBoxRow labelContainerClass="col-3"
        inputBoxContainerClass="col-2 text-primary"
        containerClass="justify-content-end"
        hasLabel={ true }
        hideAttachment={ true }
        hideNotes={ true }
        hasClickToCopy={ true }
        showManagerCheckbox={ false }
        label="First Name:"
        textValue={ claimObject.first_name }/>
      <InputBoxRow labelContainerClass="col-3"
        inputBoxContainerClass="col-2 text-primary"
        containerClass="justify-content-end"
        hasLabel={ true }
        hideAttachment={ true }
        hideNotes={ true }
        hasClickToCopy={ true }
        showManagerCheckbox={ false }
        label="MI:"
        textValue={ claimObject.middle_initial }/>
      <InputBoxRow labelContainerClass="col-3"
        inputBoxContainerClass="col-2 text-primary"
        containerClass="justify-content-end"
        hasLabel={ true }
        hideAttachment={ true }
        hideNotes={ true }
        hasClickToCopy={ true }
        showManagerCheckbox={ false }
        label="Address:"
        textValue={ claimObject.address }/>
      <InputBoxRow labelContainerClass="col-3"
        inputBoxContainerClass="col-2 text-primary"
        containerClass="justify-content-end"
        hasLabel={ true }
        hideAttachment={ true }
        hideNotes={ true }
        hasClickToCopy={ true }
        showManagerCheckbox={ false }
        label="City:"
        textValue={ claimObject.city }/>
      <InputBoxRow labelContainerClass="col-3"
        inputBoxContainerClass="col-2 text-primary"
        containerClass="justify-content-end"
        hasLabel={ true }
        hideAttachment={ true }
        hideNotes={ true }
        hasClickToCopy={ true }
        showManagerCheckbox={ false }
        label="State:"
        textValue={ claimObject.state }/>
      <InputBoxRow labelContainerClass="col-3"
        inputBoxContainerClass="col-2 text-primary"
        containerClass="justify-content-end"
        hasLabel={ true }
        hideAttachment={ true }
        hideNotes={ true }
        hasClickToCopy={ true }
        showManagerCheckbox={ false }
        label="Zip:"
        textValue={ claimObject.zip }/>
      <InputBoxRow labelContainerClass="col-3"
        inputBoxContainerClass="col-2 text-primary"
        containerClass="justify-content-end"
        hasLabel={ true }
        hideAttachment={ true }
        hideNotes={ true }
        hasClickToCopy={ true }
        showManagerCheckbox={ false }
        label="Home phone:"
        textValue={ claimObject.home_phone && claimObject.home_phone.Valid ?  claimObject.home_phone.String : "" }/>
      <InputBoxRow labelContainerClass="col-3"
        inputBoxContainerClass="col-2 text-primary"
        containerClass="justify-content-end"
        hasLabel={ true }
        hideAttachment={ true }
        hideNotes={ true }
        hasClickToCopy={ true }
        showManagerCheckbox={ false }
        label="Alternate phone number:"
        textValue={ claimObject.alternate_phone_number && claimObject.alternate_phone_number.Valid ?  claimObject.alternate_phone_number.String : "" }/>
      <InputBoxRow labelContainerClass="col-3"
        inputBoxContainerClass="col-2 text-primary"
        containerClass="justify-content-end"
        hasLabel={ true }
        hideAttachment={ true }
        hideNotes={ true }
        hasClickToCopy={ true }
        showManagerCheckbox={ false }
        label="Email address:"
        textValue={ claimObject.email_address }/>
      <InputBoxRow labelContainerClass="col-3"
        inputBoxContainerClass="col-2 text-primary"
        containerClass="justify-content-end"
        hasLabel={ true }
        hideAttachment={ true }
        hideNotes={ true }
        hasClickToCopy={ true }
        showManagerCheckbox={ false }
        label="Loan #:"
        textValue={ claimObject.loan_number }/>
      <InputBoxRow labelContainerClass="col-3"
        inputBoxContainerClass="col-2"
        hasLabel={ true }
        hasCheckbox={ false }
        hasInputBox={ true }
        hideAttachment={ true }
        hasDefaultValue={ true }
        label="Contract balance:"
        inputBoxType="Currency"
        inputBoxAttribute="contract_balance"
        showManagerCheckbox={ false }
        claimObject={ claimObject }
        onInputBoxChange={ onCursorValueChange }
        onInputBoxBlur={ onCursorValueChange }
        rowId={ COMPONENT_MAP['contractBalance']['rowId'] }
        fieldId={ COMPONENT_MAP['contractBalance']['id'] }
        fieldNotes={ getFieldNotes(notes, COMPONENT_MAP['contractBalance']['id']) }
        fieldDocs={ getFieldDocs(attachments, COMPONENT_MAP['contractBalance']['id']) }
        selectedFieldNote={ selectedFieldNote }
        handleItemNoteClick={ handleItemNoteClick }
        handleMouseEnter={ handleMouseEnter }
        handleMouseLeave={ handleMouseLeave }
        handleAttachmentClick={ handleAttachmentClick }
        deleteAttachment={ deleteAttachment }
        claimType="GAP_RECOVERY"
        rowType={ rowType }
        rowStyle={ rowStyle }/>
    </section>
  );
};

BorrowersInformation.propTypes = {
  claimObject: PropTypes.object.isRequired,
  onCursorValueChange: PropTypes.func.isRequired,
  getFieldNotes: PropTypes.func.isRequired,
  getFieldDocs: PropTypes.func.isRequired,
  selectedFieldNote: PropTypes.object,
  handleItemNoteClick: PropTypes.func.isRequired,
  handleMouseEnter: PropTypes.func.isRequired,
  handleMouseLeave: PropTypes.func.isRequired,
  handleAttachmentClick: PropTypes.func.isRequired,
  deleteAttachment: PropTypes.func.isRequired,
  rowType: PropTypes.string.isRequired,
  rowStyle: PropTypes.object.isRequired,
  attachments: PropTypes.object.isRequired,
  notes: PropTypes.object.isRequired,
};

export default BorrowersInformation;