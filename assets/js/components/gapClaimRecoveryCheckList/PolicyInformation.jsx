import React from "react";
import moment from "moment";
import { CONSTANTS } from "./../reusable/Constants/constants.js";
import PropTypes from "prop-types";
import InputBoxRow from "../claimCheckList/InputBoxRow.jsx";
import dateFormat from "./../reusable/Utilities/dateFormat.js";

export default class PolicyInformation extends React.Component {

  renderClaimTypeDropdown = (COMPONENT_MAP, claimObject, onCursorChange) => {
    return (
      <select className="form-control form-control-sm"
        id={ `${COMPONENT_MAP['claimType']['rowId']}_dropdown` }
        value={ claimObject.claim_type }
        onChange={ onCursorChange.bind(null, 'claim_type') }>
        <option value="">Select claim type</option>
        <option value="gap_recovery">GAP Recovery</option>
      </select>
    );
  };

  render() {
    const {
      claimObject,
      onCursorChange,
      getFieldNotes,
      getFieldDocs,
      selectedFieldNote,
      handleItemNoteClick,
      handleMouseEnter,
      handleMouseLeave,
      handleAttachmentClick,
      deleteAttachment,
      rowType,
      rowStyle,
      attachments,
      notes,
    } = this.props;
    const COMPONENT_MAP = CONSTANTS.RECOVERY_WORKSHEET_COMPONENT_MAP;
    return (
      <section>
        <div className="form-group row my-4">
          <span className="col-form-label col-form-label-sm col-6">
            <strong>Policy Information</strong>
          </span>
        </div>
        <InputBoxRow
          labelContainerClass="col-3"
          inputBoxContainerClass="col-2"
          containerClass="justify-content-end"
          hideAttachment={ true }
          claimObject={ claimObject }
          hasLabel={ true }
          hasNode={ true }
          label="Claim type:"
          renderNode={ this.renderClaimTypeDropdown.bind(this, COMPONENT_MAP, claimObject, onCursorChange) }
          rowId={ COMPONENT_MAP['claimType']['rowId'] }
          fieldId={ COMPONENT_MAP['claimType']['id'] }
          fieldNotes={ getFieldNotes(notes, COMPONENT_MAP['claimType']['id']) }
          fieldDocs={ getFieldDocs(attachments, COMPONENT_MAP['claimType']['id']) }
          selectedFieldNote={ selectedFieldNote }
          handleItemNoteClick={ handleItemNoteClick }
          handleMouseEnter={ handleMouseEnter }
          handleMouseLeave={ handleMouseLeave }
          handleAttachmentClick={ handleAttachmentClick }
          deleteAttachment={ deleteAttachment }
          claimType="GAP_RECOVERY"
          rowType={ rowType }
          rowStyle={ rowStyle }/>
        <InputBoxRow labelContainerClass="col-3"
          inputBoxContainerClass="col-2 text-primary"
          containerClass="justify-content-end"
          hasLabel={ true }
          hideAttachment={ true }
          hideNotes={ true }
          hasClickToCopy={ true }
          showManagerCheckbox={ false }
          label="Date of Loss:"
          textValue={ claimObject.date_of_loss ? moment(claimObject.date_of_loss, dateFormat.backendDateFormat).format(dateFormat.displayDateFormat) : '' }/>
      </section>
    );
  }
}


PolicyInformation.propTypes = {
  claimObject: PropTypes.object.isRequired,
  onCursorChange: PropTypes.func.isRequired,
  getFieldNotes: PropTypes.func.isRequired,
  getFieldDocs: PropTypes.func.isRequired,
  selectedFieldNote: PropTypes.object,
  handleItemNoteClick: PropTypes.func.isRequired,
  handleMouseEnter: PropTypes.func.isRequired,
  handleMouseLeave: PropTypes.func.isRequired,
  handleAttachmentClick: PropTypes.func.isRequired,
  deleteAttachment: PropTypes.func.isRequired,
  rowType: PropTypes.string.isRequired,
  rowStyle: PropTypes.object.isRequired,
  attachments: PropTypes.object.isRequired,
  notes: PropTypes.object.isRequired,
};