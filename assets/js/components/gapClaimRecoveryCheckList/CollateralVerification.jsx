import React from "react";
import PropTypes from "prop-types";
import InputBoxRow from "../claimCheckList/InputBoxRow.jsx";

const CollateralVerification = ({
  claimObject,
}) => {
  return (
    <section>
      <div className="form-group row my-4">
        <span className="col-form-label col-form-label-sm col-6">
          <strong>Collateral Verification</strong>
        </span>
      </div>
      <InputBoxRow labelContainerClass="col-3"
        inputBoxContainerClass="col-2 text-primary"
        containerClass="justify-content-end"
        hasLabel={ true }
        hideAttachment={ true }
        hideNotes={ true }
        hasClickToCopy={ true }
        showManagerCheckbox={ false }
        label="Year:"
        textValue={ claimObject.year && claimObject.year.Valid ? claimObject.year.String : "" }/>
      <InputBoxRow labelContainerClass="col-3"
        inputBoxContainerClass="col-2 text-primary"
        containerClass="justify-content-end"
        hasLabel={ true }
        hideAttachment={ true }
        hideNotes={ true }
        hasClickToCopy={ true }
        showManagerCheckbox={ false }
        label="Make:"
        textValue={ claimObject.make && claimObject.make.Valid ? claimObject.make.String : "" }/>
      <InputBoxRow labelContainerClass="col-3"
        inputBoxContainerClass="col-2 text-primary"
        containerClass="justify-content-end"
        hasLabel={ true }
        hideAttachment={ true }
        hideNotes={ true }
        hasClickToCopy={ true }
        showManagerCheckbox={ false }
        label="Model:"
        textValue={ claimObject.model && claimObject.model.Valid ? claimObject.model.String : "" }/>
      <InputBoxRow labelContainerClass="col-3"
        inputBoxContainerClass="col-2 text-primary"
        containerClass="justify-content-end"
        hasLabel={ true }
        hideAttachment={ true }
        hideNotes={ true }
        hasClickToCopy={ true }
        showManagerCheckbox={ false }
        label="VIN:"
        textValue={ claimObject.vin && claimObject.vin.Valid ? claimObject.vin.String : "" }/>
    </section>
  );
};

CollateralVerification.propTypes = {
  claimObject: PropTypes.object.isRequired,
};

export default CollateralVerification;