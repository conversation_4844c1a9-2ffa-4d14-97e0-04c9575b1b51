import React from "react";
import Alert from "react-s-alert";
import { json as ajax } from "./../../ajax.js";
import { CONSTANTS } from "./../reusable/Constants/constants.js";
import { URLS as apiUrls } from "./../reusable/Utilities/urls";
import PropTypes from "prop-types";
import InputBoxRow from "../claimCheckList/InputBoxRow.jsx";
import Select from "react-select";

const menuContainerStyle={
  zIndex: 999
};

export default class InsuranceData extends React.Component {

  constructor(props) {
    super(props);
    this.state = {
      queryString: '',
      searching: false,
      typingTimeOut: 0,
      insuranceCompanies: []
    };
  }

  filterOptions = (options, filter, currentValues) => {
    // If there are not any options available yet, then
    // make the current user input available as an option.
    if (filter !== '' && options.length === 0) {
      return [{ value: -1, label: filter }];
    }
    return options;
  }

  renderInsuranceCompanyDropdown = () => {
    return (
      <div className="col-12 px-0 col-12 d-inline-flex align-items-center">
        <Select
          value={ this.props.claimObject.insurance_company || 0}
          id="insurance-company-dropdown"
          isLoading={ this.state.searching }
          options={ this.getInsuranceCompanyOptions() }
          onChange={ this.onInsuranceCompanySelection }
          onInputChange={ this.updateInsuranceCompany }
          filterOptions={ this.filterOptions }
          menuContainerStyle={ menuContainerStyle }
          className="col-10 px-0"
          tabSelectsValue={ false }
          matchProp='label'/>
        <button
          type="button"
          className="cursor-pointer text-primary col-2 px-2 btn btn-link border-0"
          onClick={ this.props.addInsuranceCompanyName.bind(this) }>
          <i className="fa fa-plus" />
        </button>
      </div>
    );
  };

  searchInsuranceCompany = () => {
    this.setState({ searching: true }, function () {
      ajax(`${apiUrls.insuranceCompanies}?q=${encodeURIComponent(this.state.queryString)}`, {}, {}, (data, status) => {
        if (status === 200) {
          this.setState({ insuranceCompanies: data.insurance_companies, searching: false });
        } else {
          this.setState({ searching: false }, ()=> {
            Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  updateInsuranceCompany = queryString => {
    if (this.state.typingTimeOut) {
      clearTimeout(this.state.typingTimeOut);
    }
    this.setState({
      queryString: queryString,
      typingTimeOut: setTimeout(() => {
        if (this.state.queryString && this.state.queryString.length > 2) {
          this.searchInsuranceCompany();
        }
      }, 500)
    });
  };

  onInsuranceCompanySelection = insuranceCompany => {
    if (insuranceCompany) {
      this.props.onCursorValueChange('insurance_company', insuranceCompany.value);
    } else {
      this.props.onCursorValueChange('insurance_company', '');
    }
  };

  getInsuranceCompanyOptions() {
    let insuranceCompaniesArray = [];
    if (this.state.insuranceCompanies && this.state.insuranceCompanies.length > 0) {
      insuranceCompaniesArray = this.state.insuranceCompanies.map((element) => {
        return { label: element.name, value: element.name };
      });
    }
    if (this.props.claimObject && this.props.claimObject.insurance_company) {
      const insuranceCompany = insuranceCompaniesArray.find((item) => (item.label === this.props.claimObject.insurance_company));
      if (!insuranceCompany) {
        insuranceCompaniesArray = [ {
          value: this.props.claimObject.insurance_company,
          label: this.props.claimObject.insurance_company
        }, ...insuranceCompaniesArray ];
      }
    }
    return insuranceCompaniesArray;
  }

  render() {
    const {
      claimObject,
      onCursorValueChange,
      getFieldNotes,
      getFieldDocs,
      selectedFieldNote,
      handleItemNoteClick,
      handleMouseEnter,
      handleMouseLeave,
      handleAttachmentClick,
      deleteAttachment,
      rowType,
      rowStyle,
      attachments,
      notes,
    } = this.props;
    const COMPONENT_MAP = CONSTANTS.RECOVERY_WORKSHEET_COMPONENT_MAP;
    const GAP_COMPONENT_MAP = CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP;
    return (
      <section>
        <InputBoxRow labelContainerClass="col-3"
          inputBoxContainerClass="col-2"
          hasLabel={ true }
          hasCheckbox={ false }
          hasInputBox={ true }
          hideAttachment={ true }
          label="Allied Claim number:"
          inputBoxType="Text"
          inputBoxAttribute="allied_claim_number"
          showManagerCheckbox={ false }
          claimObject={ claimObject }
          onInputBoxChange={ onCursorValueChange }
          onInputBoxBlur={ onCursorValueChange }
          rowId={ COMPONENT_MAP['alliedClaimNumber']['rowId'] }
          fieldId={ COMPONENT_MAP['alliedClaimNumber']['id'] }
          fieldNotes={ getFieldNotes(notes, COMPONENT_MAP['alliedClaimNumber']['id']) }
          fieldDocs={ getFieldDocs(attachments, COMPONENT_MAP['alliedClaimNumber']['id']) }
          selectedFieldNote={ selectedFieldNote }
          handleItemNoteClick={ handleItemNoteClick }
          handleMouseEnter={ handleMouseEnter }
          handleMouseLeave={ handleMouseLeave }
          handleAttachmentClick={ handleAttachmentClick }
          deleteAttachment={ deleteAttachment }
          claimType="GAP_RECOVERY"
          rowType={ rowType }
          rowStyle={ rowStyle }/>
        <InputBoxRow labelContainerClass="col-3"
          inputBoxContainerClass="col-2"
          containerClass="justify-content-end"
          claimObject={ claimObject }
          showManagerCheckbox={ false }
          hideAttachment={ true }
          hasLabel={ true }
          hasNode={ true }
          label="Insurance Company:"
          renderNode={ this.renderInsuranceCompanyDropdown }
          rowId={ GAP_COMPONENT_MAP['insuranceCompany']['rowId'] }
          fieldId={ GAP_COMPONENT_MAP['insuranceCompany']['id'] }
          fieldNotes={ getFieldNotes(notes, GAP_COMPONENT_MAP['insuranceCompany']['id']) }
          fieldDocs={ getFieldDocs(attachments, GAP_COMPONENT_MAP['insuranceCompany']['id']) }
          selectedFieldNote={ selectedFieldNote }
          handleItemNoteClick={ handleItemNoteClick }
          handleMouseEnter={ handleMouseEnter }
          handleMouseLeave={ handleMouseLeave }
          handleAttachmentClick={ this.handleAttachmentClick }
          deleteAttachment={ this.deleteAttachment }
          rowType={ rowType }
          rowStyle={ rowStyle }/>
        <InputBoxRow labelContainerClass="col-3"
          inputBoxContainerClass="col-2"
          hasLabel={ true }
          hasCheckbox={ false }
          hasInputBox={ true }
          hideAttachment={ true }
          label="Insurance Claim number:"
          inputBoxType="Text"
          inputBoxAttribute="policy_number"
          showManagerCheckbox={ false }
          claimObject={ claimObject }
          onInputBoxChange={ onCursorValueChange }
          onInputBoxBlur={ onCursorValueChange }
          rowId={ COMPONENT_MAP['insuranceClaimNumber']['rowId'] }
          fieldId={ COMPONENT_MAP['insuranceClaimNumber']['id'] }
          fieldNotes={ getFieldNotes(notes, COMPONENT_MAP['insuranceClaimNumber']['id']) }
          fieldDocs={ getFieldDocs(attachments, COMPONENT_MAP['insuranceClaimNumber']['id']) }
          selectedFieldNote={ selectedFieldNote }
          handleItemNoteClick={ handleItemNoteClick }
          handleMouseEnter={ handleMouseEnter }
          handleMouseLeave={ handleMouseLeave }
          handleAttachmentClick={ handleAttachmentClick }
          deleteAttachment={ deleteAttachment }
          claimType="GAP_RECOVERY"
          rowType={ rowType }
          rowStyle={ rowStyle }/>
        <InputBoxRow labelContainerClass="col-5"
          hasLabel={ true }
          hasCheckbox={ false }
          hasInputBox={ false }
          label="Case Attachments:"
          showManagerCheckbox={ false }
          claimObject={ claimObject }
          onInputBoxChange={ onCursorValueChange }
          onInputBoxBlur={ onCursorValueChange }
          rowId={ GAP_COMPONENT_MAP['recoveryReview']['rowId'] }
          fieldId={ GAP_COMPONENT_MAP['recoveryReview']['id'] }
          fieldNotes={ getFieldNotes(notes, GAP_COMPONENT_MAP['recoveryReview']['id']) }
          fieldDocs={ getFieldDocs(attachments, GAP_COMPONENT_MAP['recoveryReview']['id']) }
          selectedFieldNote={ selectedFieldNote }
          handleItemNoteClick={ handleItemNoteClick }
          handleMouseEnter={ handleMouseEnter }
          handleMouseLeave={ handleMouseLeave }
          handleAttachmentClick={ handleAttachmentClick }
          deleteAttachment={ deleteAttachment }
          rowType={ rowType }
          rowStyle={ rowStyle }/>
      </section>
      
    );
  }
}

InsuranceData.propTypes = {
  addInsuranceCompanyName: PropTypes.func.isRequired,
  claimObject: PropTypes.object.isRequired,
  onCursorValueChange: PropTypes.func.isRequired,
  getFieldNotes: PropTypes.func.isRequired,
  getFieldDocs: PropTypes.func.isRequired,
  selectedFieldNote: PropTypes.object,
  handleItemNoteClick: PropTypes.func.isRequired,
  handleMouseEnter: PropTypes.func.isRequired,
  handleMouseLeave: PropTypes.func.isRequired,
  handleAttachmentClick: PropTypes.func.isRequired,
  deleteAttachment: PropTypes.func.isRequired,
  rowType: PropTypes.string.isRequired,
  rowStyle: PropTypes.object.isRequired,
  attachments: PropTypes.object.isRequired,
  notes: PropTypes.object.isRequired,
};