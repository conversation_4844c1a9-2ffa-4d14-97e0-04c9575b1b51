import React from "react";
import Alert from "react-s-alert";
import Loader from "react-loader-advanced";
import immstruct from "immstruct";
import Immutable from "immutable";
import PropTypes from "prop-types";
import { json as ajax } from "./../../ajax.js";
import { scrollTo } from "../reusable/Utilities/Scroll.js";
import { CONSTANTS } from "./../reusable/Constants/constants.js";
import { URLS as apiUrls } from "./../reusable/Utilities/urls";
import Header from "./Header.jsx";
import RecordNotes from "./../reusable/RecordNotes/RecordNotes.jsx";
import AttachmentModal from "../claimCheckList/FileAttachment.jsx";
import InsuranceData from "./InsuranceData.jsx";
import PolicyInformation from "./PolicyInformation.jsx";
import RecoveryProcess from "./RecoveryProcess.jsx";
import BorrowersInformation from "./BorrowersInformation.jsx";
import CollateralVerification from "./CollateralVerification.jsx";
import AddInsuranceCompanyModal from './../claimCheckList/AddInsuranceCompanyModal';

const style = {
  rowStyle: {
    border: "1px solid #ccc",
    borderRadius: "0.25rem 0 0 0.25rem",
    position: "relative",
    borderRight: "none",
    marginRight: "-16px",
    zIndex: "9"
  },
  notesStyle: {
    position: 'absolute',
    right: 0,
    top: 0,
    zIndex: 9
  }
};

export default class GapClaimRecoveryWorksheet extends React.Component {

  IMMS_KEY = 'recovery_claim';

  constructor(props) {
    super(props);
    this.gap_recovery = immstruct(
      this.IMMS_KEY,
      {
        contract_number: '',
        difference: 0,
        last_updated_by: '',
        last_updated_at: '',
        allied_claim_number: '',
        insurance_company: '',
        last_name: '',
        first_name: '',
        middle_initial: '',
        address: '',
        city: '',
        state: '',
        zip: '',
        home_phone: {
          String: '',
          Valid: true
        },
        alternate_phone_number: {
          String: '',
          Valid: false
        },
        email_address: '',
        loan_number: '',
        contract_balance: '',
        policy_number: '',
        claim_type: '',
        date_of_loss: '',
        year: {
          Int64: '',
          Valid: false
        },
        make: {
          String: '',
          Valid:false
        },
        model: {
          String: '',
          Valid:false
        },
        vin: {
          String: '',
          Valid:false
        },
        status: '',
        check_amount: ''
      }
    );
    this.gap_recovery.on('swap', (newStructure, oldStructure, keyPath) => {
      this.setState({ gap_recovery: this.gap_recovery.cursor() });
    });
    this.state = {
      gap_recovery: this.gap_recovery.cursor(),
      showLoader: false,
      showAttachmentLoader: false,
      showNotesLoader: false,
      notes: {},
      attachments: {},
      initialStatus: '',
      insuranceDisplayModal: false,
      attachmentParams: {
        title: '',
        fieldId: '',
        rowType: '',
        contractCode: '',
        contractNumber: ''
      },
      displayAttachmentModal: false,
      worksheetOffset: 0,
      showNotes: false,
      notesURL: '',
      updateRecordNotes: false,
      noteTitle: '',
      selectedFieldNote: {
        itemNoteOffset: '',
        rowType: '',
        isSelected: false,
        contractDetails: {},
        fieldId: '',
        fieldNotes: {}
      },
      hideFieldNoteTextArea: true,
      fieldNoteHovered: false,
      fieldNoteClicked: false,
      noteEventType: '',
      noteType: '',
    };
  }

  componentDidMount = () => {
    document.title = 'TCA Portal - GAP Claims Recovery Worksheet';
    this.loadClaim();
    this.loadAttachments();
    this.loadNotes();
  };

  componentWillUnmount = () => {
    this.gap_recovery.removeAllListeners();
    immstruct.remove(this.IMMS_KEY);
  };

  loadAttachments = (successCallback) => {
    this.setState({ showAttachmentLoader: true }, function () {
      ajax(`${apiUrls.getDocument.replace('__documentId__', this.props.location.query.id)}`, {}, {}, (data, status) => {
        if (status === 200) {
          this.setState({
            attachments: data.docs,
            showAttachmentLoader: false
          }, () => {
            if (successCallback) {
              successCallback();
            }
          });
        } else {
          this.setState({ showAttachmentLoader: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  loadNotes = (successCallback) => {
    this.setState({ showNotesLoader: true }, function () {
      ajax(`${apiUrls.getNotes.replace('__claimId__', this.props.location.query.id)}`, {}, {}, (data, status) => {
        if (status === 200) {
          this.setState({
            notes: data,
            showNotesLoader: false
          }, () => {
            if (successCallback) {
              successCallback();
            }
          });
        } else {
          this.setState({ showNotesLoader: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  loadClaim = () => {
    this.setState({ showLoader: true }, function () {
      ajax(apiUrls.gapRecoveryDetails.replace("__claimId__",this.props.location.query.id), {}, {}, (data, status) => {
        if (status === 200) {
          this.setState({ showLoader: false, initialStatus: data.claim_recovery.status }, ()=>{
            this.state.gap_recovery.update(() => Immutable.fromJS(data.claim_recovery));
          });
        } else {
          this.setState({ showLoader: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  getClaimData = () => {
    let claimObject = this.state.gap_recovery.toJS();
    claimObject['claim_type'] = 'gap_recovery';
    return claimObject;
  };

  updateClaimList = () => {
    const claimObject = this.getClaimData();
    this.setState({ showLoader: true }, function () {
      ajax(apiUrls.gapRecoveryDetails.replace("__claimId__",this.props.location.query.id), claimObject, { method: 'PUT' }, (data, status) => {
        if (status === 200) {
          Alert.success("Update successful.");
          this.setState({ displayModal: false, showLoader: false }, () => this.loadClaim());
        } else {
          this.setState({
            showLoader: false,
          }, () => {
            Alert.error("Click the Update button again. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  onCursorValueChange = (name, value) => {
    this.state.gap_recovery.cursor(name).update(() => value);
  };

  onCursorChange = (name, e) => {
    this.state.gap_recovery.cursor(name).update(() => e.target.value);
  };

  /**
   * This function will change showNotes state which will execute load notes function
   * @param notesType - Type of notes "contract" or "claim"
   */

  onShowHistoryButtonClick = (notesType) => {
    let notesURL;
    let noteTitle = "";
    let hideFieldNoteTextArea = true;
    let noteType = '';
    if (notesType === "case") {
      notesURL = apiUrls.gapClaimRecoveryCaseNotes;
      noteTitle = "Case Notes";
      hideFieldNoteTextArea = false;
      noteType = 'RECOVERY_CASE';
    } else if (notesType === "claim") {
      notesURL = apiUrls.gapClaimNotes;
      noteTitle = "Claim History";
      noteType = 'GAP_CLAIMS';
    }
    this.setState({
      showNotes: true,
      notesURL,
      updateRecordNotes: true,
      noteTitle,
      noteType,
      selectedFieldNote: {
        itemNoteOffset: '',
        rowType: '',
        isSelected: false,
        contractDetails: {},
        fieldId: '',
        fieldNotes: {}
      },
      hideFieldNoteTextArea: hideFieldNoteTextArea,
      fieldNoteHovered: false,
      fieldNoteClicked: false
    }, () => {
      scrollTo(0, 0);
    });
  };

  showRecordNotes = () => {
    const claimObject = this.state.gap_recovery.toJS();
    const location = { "id": this.props.location.query.id };
    if (this.state.showNotes && !this.state.selectedFieldNote.isSelected) {
      if (this.state.notesURL === apiUrls.gapContractNotes) {
        location["id"] = claimObject.contract_number;
      }
      return (
        <RecordNotes location={ location }
          updateRecordNotes={ this.state.updateRecordNotes }
          onRecordNotesUpdate={ () => {
            this.setState({ updateRecordNotes: false });
          } }
          apiURL={ this.state.notesURL }
          type={ this.state.noteType }
          hideAddNoteTextArea={ this.state.hideFieldNoteTextArea }
          hasTitle={ true }
          hasCloseButton={ true }
          title={ this.state.noteTitle }
          containerClass="col-6"
          notesStyle={ style.notesStyle }
          closeButtonCallback={ this.closeRecordNote }/>
      );
    } else if (this.state.showNotes && this.state.selectedFieldNote.isSelected &&
      this.state.selectedFieldNote.rowType === CONSTANTS.FIELD_TYPE.static) {
      return (
        <RecordNotes updateRecordNotes={ this.state.updateRecordNotes }
          location={ location }
          onRecordNotesUpdate={ this.loadNotes }
          apiURL={ apiUrls.fieldNotes }
          hideAddNoteTextArea={ this.state.hideFieldNoteTextArea }
          type={ this.state.selectedFieldNote.rowType }
          excludePagination={ true }
          recordNotes={ this.getFieldNotes(this.state.notes, this.state.selectedFieldNote.fieldId) }
          selectedFieldNote={ this.state.selectedFieldNote }
          hasCloseButton={ !this.state.fieldNoteHovered && true }
          containerClass="col-6"
          notesStyle={ style.notesStyle }
          closeButtonCallback={ this.closeRecordNote }
          worksheetOffset={ this.state.worksheetOffset }
          eventType={ this.state.noteEventType }/>
      );
    }
  };

  getFieldNotes = (claimObject, id) => {
    if (claimObject.field_notes && claimObject.field_notes.length > 0) {
      for (let index = 0; index < claimObject.field_notes.length; index++) {
        if (claimObject.field_notes[index]['field_id'] === id) {
          return claimObject.field_notes[index];
        }
      }
    }
  };

  getFieldDocs = (claimObject, id) => {
    if (claimObject.field_documents && claimObject.field_documents.length > 0) {
      for (let index = 0; index < claimObject.field_documents.length; index++) {
        if (claimObject.field_documents[index]['field_id'] === id && claimObject.field_documents[index]['documents'].length > 0) {
          return claimObject.field_documents[index]['documents'];
        }
      }
    }
  };

  handleFieldNoteClick = (params) => {
    this.setState({
      selectedFieldNote: params,
      showNotes: true,
      hideFieldNoteTextArea: false,
      fieldNoteHovered: false,
      fieldNoteClicked: true,
      updateRecordNotes: false,
      worksheetOffset: this.worksheet.offsetHeight,
      noteEventType: "click"
    }, function () {
      scrollTo(0, this.state.selectedFieldNote.itemNoteOffset);
    });
  };

  handleMouseEnter = (params) => {
    if (!this.state.fieldNoteClicked) {
      this.setState({
        selectedFieldNote: params,
        showNotes: true,
        hideFieldNoteTextArea: true,
        fieldNoteHovered: true,
        updateRecordNotes: false,
        worksheetOffset: this.worksheet.offsetHeight,
        noteEventType: "hover"
      });
    }
  };

  handleMouseLeave = () => {
    if (!this.state.fieldNoteClicked && this.state.fieldNoteHovered) {
      this.setState({
        selectedFieldNote: {
          itemNoteOffset: '',
          rowType: '',
          isSelected: false,
          contractDetails: {},
          fieldId: '',
          fieldNotes: {}
        },
        worksheetOffset: 0,
        showNotes: false,
        fieldNoteHovered: false,
        updateRecordNotes: false,
        noteEventType: ""
      });
    }
  };

  closeRecordNote = () => {
    this.setState({
      selectedFieldNote: {
        itemNoteOffset: '',
        rowType: '',
        isSelected: false,
        contractDetails: {},
        fieldId: '',
        fieldNotes: {}
      },
      worksheetOffset: 0,
      showNotes: false,
      hideFieldNoteTextArea: true,
      fieldNoteHovered: false,
      fieldNoteClicked: false,
      updateRecordNotes: false,
      noteEventType: ""
    });
  };

  handleAttachmentClick = (attachmentParams) => {
    this.setState({
      displayAttachmentModal: true,
      attachmentParams
    });
  };

  closeAttachmentModal = (claimObject, isSuccess, isFailure) => {
    this.setState({
      displayAttachmentModal: false,
      attachmentParams: {
        title: '',
        fieldId: '',
        rowType: '',
        contractCode: '',
        contractNumber: ''
      }
    }, function () {
      if (isSuccess) {
        this.loadAttachments(this.onAttachmentReload.bind(this, 'add', claimObject));
      }
      if (isFailure) {
        Alert.error("Click the browser's Refresh button to reload. If the error continues, contact your system administrator.");
      }
    });
  };

  onAttachmentReload = (type) => {
    this.setState({ showLoader: false }, () => {
      if (type === 'add') {
        Alert.success("Document uploaded successfully.");
      } else {
        Alert.success("Document deleted successfully.");
      }
    });
  };

  deleteAttachment = (id) => {
    this.setState({ showLoader: true }, function () {
      ajax(`${apiUrls.document}/${id}`, {}, { method: 'DELETE' }, (data, status) => {
        if (status === 200) {
          this.setState({ showLoader: false }, function () {
            this.loadAttachments(this.onAttachmentReload.bind(this, 'delete'));
          });
        } else {
          this.setState({ showLoader: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  onInsuranceModalClosed = (companyName) => {
    if(companyName) {
      this.state.gap_recovery.cursor('insurance_company').update(() => companyName);
    }
    this.setState({
      insuranceDisplayModal: false
    });
  };

  addInsuranceCompanyName = () => {
    this.setState({
      insuranceDisplayModal: true
    });
  };

  render() {
    const claimObject = this.state.gap_recovery.toJS();
    const spinnerMessage = <p className='text-center'><i className="fa fa-refresh fa-spin"/> Loading...</p>;
    return (
      <Loader show={ this.state.showLoader || this.state.showAttachmentLoader || this.state.showNotesLoader } message={ spinnerMessage }>
        <section className="claim-worksheet">
          <Header
            claimObject={ claimObject }
            nextButtonOnClick={ this.updateClaimList }
            backButtonOnClick={ () => this.context.router.goBack() }
            showHistoryButtonClick={ this.onShowHistoryButtonClick }
            user={ this.props.user }
            claimStatus={ this.state.initialStatus }/>
          <section className="mt-3 pt-3">
            <section className="row mt-1">
              <section className="col-12" id="parentDiv" ref={ (worksheet) => this.worksheet = worksheet }>
                <form>
                  <fieldset>
                    <InsuranceData
                      addInsuranceCompanyName={ this.addInsuranceCompanyName }
                      claimObject={ claimObject }
                      onCursorValueChange={ this.onCursorValueChange }
                      getFieldNotes={ this.getFieldNotes }
                      getFieldDocs={ this.getFieldDocs }
                      attachments={ this.state.attachments }
                      notes={ this.state.notes }
                      selectedFieldNote={ this.state.selectedFieldNote }
                      handleItemNoteClick={ this.handleFieldNoteClick }
                      handleMouseEnter={ this.handleMouseEnter }
                      handleMouseLeave={ this.handleMouseLeave }
                      handleAttachmentClick={ this.handleAttachmentClick }
                      deleteAttachment={ this.deleteAttachment }
                      rowType={ CONSTANTS.FIELD_TYPE.static }
                      rowStyle={ style.rowStyle }/>
                    <BorrowersInformation
                      claimObject={ claimObject }
                      onCursorValueChange={ this.onCursorValueChange }
                      getFieldNotes={ this.getFieldNotes }
                      getFieldDocs={ this.getFieldDocs }
                      attachments={ this.state.attachments }
                      notes={ this.state.notes }
                      selectedFieldNote={ this.state.selectedFieldNote }
                      handleItemNoteClick={ this.handleFieldNoteClick }
                      handleMouseEnter={ this.handleMouseEnter }
                      handleMouseLeave={ this.handleMouseLeave }
                      handleAttachmentClick={ this.handleAttachmentClick }
                      deleteAttachment={ this.deleteAttachment }
                      rowType={ CONSTANTS.FIELD_TYPE.static }
                      rowStyle={ style.rowStyle }/>
                    <PolicyInformation
                      claimObject={ claimObject }
                      onCursorChange={ this.onCursorChange }
                      getFieldNotes={ this.getFieldNotes }
                      getFieldDocs={ this.getFieldDocs }
                      attachments={ this.state.attachments }
                      notes={ this.state.notes }
                      selectedFieldNote={ this.state.selectedFieldNote }
                      handleItemNoteClick={ this.handleFieldNoteClick }
                      handleMouseEnter={ this.handleMouseEnter }
                      handleMouseLeave={ this.handleMouseLeave }
                      handleAttachmentClick={ this.handleAttachmentClick }
                      deleteAttachment={ this.deleteAttachment }
                      rowType={ CONSTANTS.FIELD_TYPE.static }
                      rowStyle={ style.rowStyle }/>
                    <CollateralVerification
                      claimObject={ claimObject } />
                    <RecoveryProcess
                      claimObject={ claimObject }
                      onCursorChange={ this.onCursorChange }
                      onCursorValueChange={ this.onCursorValueChange }
                      getFieldNotes={ this.getFieldNotes }
                      getFieldDocs={ this.getFieldDocs }
                      attachments={ this.state.attachments }
                      notes={ this.state.notes }
                      selectedFieldNote={ this.state.selectedFieldNote }
                      handleItemNoteClick={ this.handleFieldNoteClick }
                      handleMouseEnter={ this.handleMouseEnter }
                      handleMouseLeave={ this.handleMouseLeave }
                      handleAttachmentClick={ this.handleAttachmentClick }
                      deleteAttachment={ this.deleteAttachment }
                      rowType={ CONSTANTS.FIELD_TYPE.static }
                      rowStyle={ style.rowStyle }/>
                  </fieldset>
                </form>
                {this.showRecordNotes()}
              </section>
            </section>
          </section>
          <AttachmentModal displayModal={ this.state.displayAttachmentModal }
            closeModal={ this.closeAttachmentModal.bind(this, claimObject) }
            claimId={ claimObject.id }
            attachmentParams={ this.state.attachmentParams }/>
          <div className="clearfix"/>
        </section>
        <AddInsuranceCompanyModal displayModal={ this.state.insuranceDisplayModal }
          onCloseModal={ this.onInsuranceModalClosed }/>
      </Loader>
    );
  }
}

GapClaimRecoveryWorksheet.propTypes = {
  location: PropTypes.shape({
    query: PropTypes.shape({
      id: PropTypes.string.isRequired
    }).isRequired,
  }).isRequired,
  user: PropTypes.shape({
    id: PropTypes.number.isRequired,
    first_name: PropTypes.string.isRequired,
    last_name: PropTypes.string.isRequired,
    email: PropTypes.string.isRequired,
    stores: PropTypes.arrayOf(PropTypes.string.isRequired),
    roles: PropTypes.shape({
      Map: PropTypes.object,
    }).isRequired
  })
};

GapClaimRecoveryWorksheet.contextTypes = {
  router: PropTypes.object.isRequired
};
