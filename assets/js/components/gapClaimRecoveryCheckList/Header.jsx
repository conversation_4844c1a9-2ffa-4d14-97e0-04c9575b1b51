import React from "react";
import moment from "moment";
import dateFormat from "./../reusable/Utilities/dateFormat.js";
import ReactTooltip from "react-tooltip";
import { CONSTANTS } from "./../reusable/Constants/constants.js";
import PropTypes from "prop-types";
import If from "./../reusable/If/If.jsx";
import accounting from "accounting";
import { Link } from 'react-router';
import { userHasRole } from "./../reusable/Utilities/userHasRole";
import { getClasses } from "../reusable/Utilities/headerClasses";

export default class Header extends React.Component {
  onContractNumberClick = () => {
    window.open(`/gap-contract/${this.props.claimObject.contract_number}`, this.props.claimObject.contract_number, "width=1000,height=600");
  };

  renderLastUpdatedBy = () => {
    return (
      <span
        className="row flex-row-reverse pr-3">
          Last updated by: { `${this.props.claimObject.last_updated_by} - ${moment(this.props.claimObject.last_updated_at, dateFormat.backendDateFormat).format(dateFormat.displayDateFormat)}` }
      </span>
    );
  };

  render() {
    const pageSpacingStyle = { marginTop: '200px' };
    const {
      claimObject,
      claimStatus,
      backButtonOnClick,
      nextButtonOnClick,
      showHistoryButtonClick,
      user : {
        banner_info: bannerInfo,
      },
    } = this.props;
    const classes = getClasses(bannerInfo);
    return (
      <section className="page-header" style={ pageSpacingStyle }>
        <div className={classes}>
          <div className="row align-items-center">
            <div className="col-4 text-truncate h2">
              <h2 className="d-inline-block">Recovery Case</h2>
            </div>
            <div className="col-4 text-center h3">
              <p className="d-inline-block h4">
                <a href="#!"
                  id="link-contract-number"
                  onClick={ this.onContractNumberClick }>
                  { claimObject.contract_number }
                </a>
              </p>
              <div className="d-inline-block h4">&nbsp;&nbsp;&nbsp;
                <span data-tip data-for='claimStatus' data-place="bottom">
                  { claimStatus }
                </span>
                <ReactTooltip id='claimStatus' aria-haspopup='true'>
                  <span className="text-center">{ CONSTANTS.GAP_RECOVERY_READABLE_STATUS_MAP[claimStatus] }</span>
                </ReactTooltip>
              </div>
            </div>
            <div className="col-4">
              { this.renderLastUpdatedBy() }
            </div>
          </div>
          <div className="row py-3">
            <div className="col-4">
              <button onClick={ backButtonOnClick }
                className="btn btn-secondary mr-2 cursor-pointer"
                id="btn-back">
                <i className="fa fa-arrow-left"/>
                &nbsp;Back
              </button>
              <button className="btn btn-primary mr-2 cursor-pointer"
                onClick={ nextButtonOnClick }
                id="btn-next">
                &nbsp;Update
              </button>
            </div>
            <div className="col-4 col-form-label-sm text-center">
              <label className="h5">
                Value Difference:&nbsp;
              </label>
              <p className="d-inline-block h5">
                { claimStatus === CONSTANTS.GAP_RECOVERY_STATUS_MAP.recovered.code ? accounting.formatMoney(claimObject.check_amount, '$', 2) : accounting.formatMoney(claimObject.difference, '$', 2) }
              </p>
            </div>
            <div className="col-4">
              <div className="row flex-row-reverse pr-3">
                <div className="btn-toolbar">
                  <If condition={ userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaims) || userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaimsManager) }>
                    <Link to={ `/gap-claims?contract_number=${claimObject.contract_number}&id=${claimObject.id}` }
                      className="btn btn-secondary mr-2 cursor-pointer"
                      id="btn-letters"
                      onClick={ this.redirectToEmail }>
                      GAP Claim
                    </Link>
                  </If>
                  <button type="button"
                    className="btn btn-secondary mr-2 cursor-pointer"
                    id="btn-contract-notes"
                    onClick={ showHistoryButtonClick.bind(null, "case") }>
                    Case Notes
                  </button>
                  <button type="button"
                    className="btn btn-secondary mr-2 cursor-pointer"
                    id="btn-claim-history"
                    onClick={ showHistoryButtonClick.bind(null, "claim") }>
                    History
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    );
  }
}

Header.propTypes = {
  claimObject: PropTypes.object,
  nextButtonOnClick: PropTypes.func,
  backButtonOnClick: PropTypes.func,
  claimStatus: PropTypes.string,
  showHistoryButtonClick: PropTypes.func,
  user: PropTypes.shape({
    id: PropTypes.number.isRequired,
    first_name: PropTypes.string.isRequired,
    last_name: PropTypes.string.isRequired,
    email: PropTypes.string.isRequired,
    stores: PropTypes.arrayOf(PropTypes.string.isRequired),
    roles: PropTypes.shape({
      Map: PropTypes.object,
    }).isRequired,
    banner_info: PropTypes.shape({
      header: PropTypes.string.isRequired,
      message: PropTypes.string.isRequired,
      enabled: PropTypes.bool.isRequired,
    }).isRequired,
  })
};

Header.contextTypes = {
  router: PropTypes.object.isRequired
};
