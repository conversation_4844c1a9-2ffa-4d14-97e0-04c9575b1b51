import React from "react";
import { CONSTANTS } from "./../reusable/Constants/constants.js";
import PropTypes from "prop-types";
import InputBoxRow from "../claimCheckList/InputBoxRow.jsx";

export default class RecoveryProcess extends React.Component {

  renderStatusDropdown = (COMPONENT_MAP, claimObject, onCursorChange) => {
    return (
      <select className="form-control form-control-sm"
        id={ `${COMPONENT_MAP['status']['rowId']}_dropdown` }
        value={ claimObject.status }
        onChange={ onCursorChange.bind(null, 'status') }>
        <option value="">Select claim type</option>
        <option value={ CONSTANTS.GAP_RECOVERY_STATUS_MAP.recoveryInquiry.code }>
          {CONSTANTS.GAP_RECOVERY_STATUS_MAP.recoveryInquiry.name}
        </option>
        <option value={ CONSTANTS.GAP_RECOVERY_STATUS_MAP.possibleRecovery.code }>
          {CONSTANTS.GAP_RECOVERY_STATUS_MAP.possibleRecovery.name}
        </option>
        <option value={ CONSTANTS.GAP_RECOVERY_STATUS_MAP.waitingRecovery.code }>
          {CONSTANTS.GAP_RECOVERY_STATUS_MAP.waitingRecovery.name}
        </option>
        <option value={ CONSTANTS.GAP_RECOVERY_STATUS_MAP.inRecovery.code }>
          {CONSTANTS.GAP_RECOVERY_STATUS_MAP.inRecovery.name}
        </option>
        <option value={ CONSTANTS.GAP_RECOVERY_STATUS_MAP.recovered.code }>
          {CONSTANTS.GAP_RECOVERY_STATUS_MAP.recovered.name}
        </option>
        <option value={ CONSTANTS.GAP_RECOVERY_STATUS_MAP.noRecovery.code }>
          {CONSTANTS.GAP_RECOVERY_STATUS_MAP.noRecovery.name}
        </option>
      </select>
    );
  };

  render() {
    const {
      claimObject,
      onCursorChange,
      onCursorValueChange,
      getFieldNotes,
      getFieldDocs,
      selectedFieldNote,
      handleItemNoteClick,
      handleMouseEnter,
      handleMouseLeave,
      handleAttachmentClick,
      deleteAttachment,
      rowType,
      rowStyle,
      attachments,
      notes,
    } = this.props;
    const COMPONENT_MAP = CONSTANTS.RECOVERY_WORKSHEET_COMPONENT_MAP;
    const GAP_COMPONENT_MAP = CONSTANTS.GAP_WORKSHEET_COMPONENT_MAP;
    return (
      <section>
        <div className="form-group row my-4">
          <span className="col-form-label col-form-label-sm col-6">
            <strong>Recovery Process</strong>
          </span>
        </div>
        {/** Added Recovery review id for status field to display notes and attachment added on GAP screen **/}
        <InputBoxRow
          labelContainerClass="col-3"
          inputBoxContainerClass="col-2"
          containerClass="justify-content-end"
          claimObject={ claimObject }
          hasLabel={ true }
          hasNode={ true }
          label="Status:"
          renderNode={ this.renderStatusDropdown.bind(this, COMPONENT_MAP, claimObject, onCursorChange) }
          rowId={ GAP_COMPONENT_MAP['recoveryStatus']['rowId'] }
          fieldId={ GAP_COMPONENT_MAP['recoveryStatus']['id'] }
          fieldNotes={ getFieldNotes(notes, GAP_COMPONENT_MAP['recoveryStatus']['id']) }
          fieldDocs={ getFieldDocs(attachments, GAP_COMPONENT_MAP['recoveryStatus']['id']) }
          selectedFieldNote={ selectedFieldNote }
          handleItemNoteClick={ handleItemNoteClick }
          handleMouseEnter={ handleMouseEnter }
          handleMouseLeave={ handleMouseLeave }
          handleAttachmentClick={ handleAttachmentClick }
          deleteAttachment={ deleteAttachment }
          rowType={ rowType }
          rowStyle={ rowStyle }/>
        <InputBoxRow labelContainerClass="col-3"
          inputBoxContainerClass="col-2"
          hasLabel={ true }
          hasInputBox={ true }
          hasDefaultValue={ true }
          label="Check amount:"
          inputBoxType="Currency"
          inputBoxAttribute="check_amount"
          showManagerCheckbox={ false }
          claimObject={ claimObject }
          onInputBoxChange={ onCursorValueChange }
          onInputBoxBlur={ onCursorValueChange }
          rowId={ COMPONENT_MAP['checkAmount']['rowId'] }
          fieldId={ COMPONENT_MAP['checkAmount']['id'] }
          fieldNotes={ getFieldNotes(notes, COMPONENT_MAP['checkAmount']['id']) }
          fieldDocs={ getFieldDocs(attachments, COMPONENT_MAP['checkAmount']['id']) }
          selectedFieldNote={ selectedFieldNote }
          handleItemNoteClick={ handleItemNoteClick }
          handleMouseEnter={ handleMouseEnter }
          handleMouseLeave={ handleMouseLeave }
          handleAttachmentClick={ handleAttachmentClick }
          deleteAttachment={ deleteAttachment }
          claimType="GAP_RECOVERY"
          rowType={ rowType }
          rowStyle={ rowStyle }/>
      </section>
    );
  }
}


RecoveryProcess.propTypes = {
  claimObject: PropTypes.object.isRequired,
  onCursorChange: PropTypes.func.isRequired,
  onCursorValueChange: PropTypes.func.isRequired,
  getFieldNotes: PropTypes.func.isRequired,
  getFieldDocs: PropTypes.func.isRequired,
  selectedFieldNote: PropTypes.object,
  handleItemNoteClick: PropTypes.func.isRequired,
  handleMouseEnter: PropTypes.func.isRequired,
  handleMouseLeave: PropTypes.func.isRequired,
  handleAttachmentClick: PropTypes.func.isRequired,
  deleteAttachment: PropTypes.func.isRequired,
  rowType: PropTypes.string.isRequired,
  rowStyle: PropTypes.object.isRequired,
  attachments: PropTypes.object.isRequired,
  notes: PropTypes.object.isRequired,
};