import React from "react";
import PropTypes from "prop-types";
import <PERSON>Header from '../pageHeader/PageHeader.jsx';
import immstruct from "immstruct";
import Immutable from "immutable";
import InputBox from "../reusable/InputBox/InputBox.jsx";
import PhoneBox from "../reusable/PhoneNumber/PhoneNumber.jsx";
import SelectBox from "../reusable/SelectBox/SelectBox.jsx";
import { jsonPromise as ajax } from "./../../ajax.js";
import { URLS as apiUrls } from "./../reusable/Utilities/urls";
import moment from "moment";
import Alert from "react-s-alert";
import Modal from "./../../Modal.jsx";
import RecordNotes from "./../reusable/RecordNotes/RecordNotes.jsx";
import Loader from "react-loader-advanced";

export default class CreditManage extends React.Component {
  static contextTypes = {
    router: PropTypes.object.isRequired
  };

  static propTypes = {
    location: PropTypes.shape({
      query: PropTypes.shape({
        id: PropTypes.string.isRequired,
        contractNumber: PropTypes.string.isRequired,
        vin: PropTypes.string.isRequired,
        customerName: PropTypes.string.isRequired,
        customerID: PropTypes.string.isRequired
      }).isRequired,
    }).isRequired,
    user: PropTypes.shape({
      id: PropTypes.number.isRequired,
      first_name: PropTypes.string.isRequired,
      last_name: PropTypes.string.isRequired,
      email: PropTypes.string.isRequired,
      stores: PropTypes.arrayOf(PropTypes.string.isRequired),
      roles: PropTypes.shape({
        Map: PropTypes.object,
      }).isRequired,
    }),
  };

  IMMS_KEY = 'credit-card';

  constructor(props){
    super(props);
    this.creditCardInfo = immstruct(
      this.IMMS_KEY,
      {
        name_on_card: "",
        card_number: "",
        expiration_month: `${moment().utc().month() + 1}`,
        expiration_year: `${moment().utc().year()}`,
        manager_name: "",
        manager_phone:"",
        manager_fax: ""
      }
    );
    this.creditCardInfo.on('swap', (newStructure, oldStructure, keyPath) => {
      this.setState({ creditCardInfo: this.creditCardInfo.cursor() });
    });
    this.state = {
      creditCardInfo: this.creditCardInfo.cursor(),
      showHistoryModal: false,
      showLoader: false,
      isInformationUpdated: false
    };
    [
      "onCardNumberFocus",
      "onCardNumberBlur",
      "renderHistory",
      "handleHistory",
      "handleCancel",
      "handleUpdate"
    ].forEach((func) => {
      this[func] = this[func].bind(this);
    });
    this.previousDetailCreditCard = this.state.creditCardInfo.toJS();
  }

  componentDidMount(){
    this.getCreditCardInfo();
  }

  componentWillUnmount(){
    this.creditCardInfo.removeAllListeners();
    immstruct.remove(this.IMMS_KEY);
  }

  getCreditCardInfo(){
    this.setState({ showLoader: true }, () => {
      ajax(apiUrls.creditCardInfo, {}, { method: "GET" }).then((response) => {
        this.setState({ showLoader: false }, () => {
          if(response) {
            if (response.status === 200) {
              if (response.data && response.data.card) {
                this.previousDetailCreditCard = response.data.card;
                this.creditCardInfo.cursor().update(() => Immutable.fromJS(response.data.card));
                this.setState({
                  isInformationUpdated: false
                });
              }
            }
            else if(response.status === 404){
              //Don't show Credit Card Info not found
            }
            else{
              Alert.error("Failed to get Credit Card Information. If the error continues, contact your system administrator.");
            }
          }
        });
      });
    });
  }

  handleUpdate(){
    const creditCardInfo = this.state.creditCardInfo.toJS();
    creditCardInfo["expiration_month"] = parseInt(creditCardInfo["expiration_month"], 10);
    creditCardInfo["expiration_year"] = parseInt(creditCardInfo["expiration_year"], 10);
    this.setState({ showLoader: true }, () => {
      ajax(apiUrls.creditCardInfo, creditCardInfo, { method: "PUT" }).then((response) => {
        if(response.status === 200){
          Alert.success("Credit Card Information updated successfully.");
          this.getCreditCardInfo();
        }
        else if(response.status === 400){
          this.setState({ showLoader: false }, () => {
            if(response.data && response.data.errors){
              for(let error of Object.keys(response.data.errors)){
                Alert.warning(response.data.errors[error]);
              }
            }
          });
        }
        else{
          this.setState({ showLoader: false }, () => {
            Alert.error("Failed to update Credit Card Information. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  }

  onChange(name, value){
    this.setState({
      isInformationUpdated: true
    });
    this.creditCardInfo.cursor(name).update(() => value);
  }

  onCardNumberFocus(){
    this.creditCardInfo.cursor("card_number").update(() => "");
  }

  onCardNumberBlur(){
    const length = this.state.creditCardInfo.toJS()["card_number"].length;
    if(length === 0){
      this.creditCardInfo.cursor("card_number").update(() => this.previousDetailCreditCard["card_number"]);
    }

    if(length < 13 || length > 18){
      Alert.warning("Enter Valid Card Number.");
    }
  }

  handleHistory(){
    this.setState({ showHistoryModal: true });
  }

  handleCancel() {
    if (this.state.isInformationUpdated) {
      this.setState({
        isInformationUpdated: false
      });
      this.creditCardInfo.cursor().update(() => Immutable.fromJS(this.previousDetailCreditCard));
    }
    this.context.router.push('/automotive-claims-list');
  }

  getMonthList(){
    let arr = [];
    for(let index = 1; index <= 12; index++){
      arr.push({ "name": index, "value": index.toString() });
    }
    return arr;
  }

  getYearList(){
    let arr = [];
    const currentYear = moment().utc().year();
    for(let index = currentYear; index <= currentYear + 15; index++){
      arr.push({ "name": index, "value": index.toString() });
    }
    return arr;
  }

  renderCardInfo(){
    const creditCardInfo = this.state.creditCardInfo.toJS();
    const fontStyle = { fontWeight: 200 };
    return (
      <div className="container pt-3">
        <h3 style={ fontStyle }>Card Information</h3>
        <div>
          <div className="row my-3">
            <div className="col-2">
              <label className="col-form-label col-form-label-sm"
                htmlFor="card_name">
                              Name on Card:
              </label>
            </div>
            <InputBox type="Text"
              id="card_name"
              customClass="col-3"
              value={ creditCardInfo.name_on_card }
              onChange={ this.onChange.bind(this, 'name_on_card') }/>
          </div>
          <div className="row my-3">
            <div className="col-2">
              <label className="col-form-label col-form-label-sm"
                htmlFor="card_number">
                              Card Number:
              </label>
            </div>
            <InputBox type="Text"
              id="card_number"
              customClass="col-3"
              value={ creditCardInfo.card_number }
              onChange={ this.onChange.bind(this, 'card_number') }
              onFocus={ this.onCardNumberFocus }
              onBlur={ this.onCardNumberBlur }/>
          </div>
          <div className="row no-gutters my-3">
            <div className="col-2">
              <label className="col-form-label col-form-label-sm"
                htmlFor="expiration_month">
                              Expiration Date:
              </label>
            </div>
            <div className="row col-6 ml-1 no-gutters">
              <div className="col-2">
                <SelectBox value={ creditCardInfo.expiration_month.toString() }
                  onChange={ this.onChange.bind(this,'expiration_month') }
                  optionsList={ this.getMonthList() }
                  customClassName=""
                  id="expiration_month"/>
              </div>
              <div className="col-1"/>
              <div className="col-2">
                <SelectBox value={ creditCardInfo.expiration_year.toString() }
                  onChange={ this.onChange.bind(this,'expiration_year') }
                  optionsList={ this.getYearList() }
                  customClassName=""
                  id="expiration_year"/>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  renderManagerInfo(){
    let creditCardInfo = this.state.creditCardInfo.toJS();
    const fontStyle = { fontWeight: 200 };
    return (
      <div className="container">
        <h3 style={ fontStyle }>{`Manager's Information`}</h3>
        <div>
          <div className="row my-3">
            <div className="col-2">
              <label className="col-form-label col-form-label-sm"
                htmlFor="manager_name">
                              Name:
              </label>
            </div>
            <InputBox type="Text"
              id="manager_name"
              customClass="col-3"
              value={ creditCardInfo.manager_name }
              onChange={ this.onChange.bind(this, 'manager_name') }/>
          </div>
          <div className="row my-3">
            <div className="col-2">
              <label className="col-form-label col-form-label-sm"
                htmlFor="manager_phone">
                        Phone:
              </label>
            </div>
            <div className="col-3">
              <PhoneBox type="Text"
                id="manager_phone"
                customClass=""
                value={ creditCardInfo.manager_phone }
                onChange={ this.onChange.bind(this, 'manager_phone') }/>
            </div>
          </div>
          <div className="row my-3">
            <div className="col-2">
              <label className="col-form-label col-form-label-sm"
                htmlFor="manager_fax">
                        Fax:
              </label>
            </div>
            <InputBox type="Text"
              id="manager_fax"
              customClass="col-3"
              value={ creditCardInfo.manager_fax }
              onChange={ this.onChange.bind(this, 'manager_fax') }/>
          </div>
        </div>
      </div>
    );
  }

  renderButtons(){
    return (
      <div className="row justify-content-center">
        <button onClick={ this.handleCancel }
          className="btn btn-secondary"
          disabled={ false }>
            Cancel
        </button>
        <button onClick={ this.handleUpdate }
          className="btn btn-primary ml-3"
          disabled={ !this.state.isInformationUpdated }>
          Update
        </button>
      </div>
    );
  }

  renderHistory(){
    return (
      <div className="row justify-content-end pr-4 py-2">
        <button className="btn btn-secondary"
          onClick={ this.handleHistory }>
                  History
        </button>
      </div>
    );
  }

  renderHistoryModal(){
    return (
      <Modal visible={ this.state.showHistoryModal } hideContentWrapper={ true }>
        <RecordNotes apiURL={ apiUrls.creditRecordNotes }
          hideAddNoteTextArea={ true }
          type={ "automated" }
          hasCloseButton={ true }
          containerClass="col-12"
          hideNotes={ false }
          includeLocation={ false }
          closeButtonCallback={ () => {
            this.setState({
              showHistoryModal: false
            });
          } }/>
      </Modal>
    );
  }

  render(){
    const spinnerMessage = <p className='text-center'><i className="fa fa-refresh fa-spin"/> Loading...</p>;
    return(
      <Loader show={ this.state.showLoader } message={ spinnerMessage }>
        <section className="gap-dashboard clearfix">

          <div className="row">
            <div className="col-12">
              <PageHeader pageTitle="Manage Credit Card"
                renderTemplate={ this.renderHistory }
                user={this.props.user}/>
              {this.renderCardInfo()}
              {this.renderManagerInfo()}
              {this.renderButtons()}
            </div>
          </div>
          {this.renderHistoryModal()}
        </section>
      </Loader>
    );
  }
}