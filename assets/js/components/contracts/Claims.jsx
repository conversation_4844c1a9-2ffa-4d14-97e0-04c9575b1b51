import React, { useState } from "react";
import PropTypes from "prop-types";
import moment from "moment";
import { displayDateFormat } from "./../reusable/Utilities/dateFormat";
import { formatCurrency } from "../reusable/Utilities/format";
import { CONSTANTS } from './../reusable/Constants/constants';
import Pagination from "../../Pagination";

const Claims = (props, context) => {
  const {
    claims,
    product_code,
    loadContractHistory,
  } = props;

  const count = (claims && claims.length > 0 && getClaimCount()) || 0;
  const [sortByDate, setSortByDate] = useState(true);
  const [page, setPage] = useState(1);
  const PER_PAGE = 5;

  const getClaimsToRender = () => claims.slice((page-1)*PER_PAGE, page * PER_PAGE);

  const onRowClick = (claimID, chargeback) => {
    if (claimID) {
      let query = {
        id: claimID,
        chargeback: chargeback,
      };

      let route = { pathname: `/automotive-claims`, query };
      if (product_code === 'GAP') {
        route.pathname = `/gap-claims`;
      } else if (product_code === 'LWT') {
        route.pathname = `/lwt-claims`;
      } else if (product_code === 'VTA') {
        route.pathname = `/vta-claims`;
      }
      if (!context.router.isActive(route)) {
        context.router.push(route);
      }
    }
  };

  const getPaymentField = (claim) => {
    let payment = "";
    if(claim.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.ccPaid 
      || claim.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.checkWritten 
      || claim.status === CONSTANTS.CHARGEBACK_CLAIM_STATUS_MAP.chargeback) {
      if (claim.pay_type === CONSTANTS.PAYMENT_TYPE_CODES.CREDIT_CARD) {
        payment = "CCPaid";
      } else if (claim.pay_type === CONSTANTS.PAYMENT_TYPE_CODES.STORE && claim.check_number) {
        payment = `${claim.facility_code} ${claim.check_number}`;
      } else if (claim.pay_type === CONSTANTS.PAYMENT_TYPE_CODES.CUSTOMER && claim.check_number) {
        payment = `Customer ${claim.check_number}`;
      }
    }
    if(payment === "" && claim.check_number && (claim.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.checkWritten 
      || claim.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.reversed
      || claim.status === CONSTANTS.CHARGEBACK_CLAIM_STATUS_MAP.chargeback)) {
      payment = claim.check_number;
    }
    return payment;
  };

  const renderClaimPaidDate = (paymentDates) => {
    return (<div>
      {paymentDates.map((element,key) => {
        return <div key={key}>{moment.utc(element).format(displayDateFormat)}</div>;
      })}
    </div>);
  };

  const handleSortByDate = () => {
    setSortByDate(!sortByDate);
    loadContractHistory(sortByDate);
  };

  const renderSortIcon = () => {
    if (sortByDate) {
      return (<i className="fa fa-caret-up" aria-hidden="true"/>);
    } else {
      return (<i className="fa fa-caret-down" aria-hidden="true"/>);
    }
  };

  function getClaimCount () {
    let tempCount = 0;
    claims.forEach(claim => {
      if (claim.amount < 0 && (claim.status !== "CheckWritten" && claim.status !== "Reversed" && claim.status !== "CCPaid")) {
        tempCount++;
      } else if (claim.amount >= 0) {
        tempCount++;
      }
    });
    return tempCount;
  }

  const renderCenturyClaim = (claim, key) => {
    let payment = getPaymentField(claim);
    return (
      <tr key={ key } onClick={ () => onRowClick(claim.claim_id) } className = { `${claim.claim_id ? "text-primary cursor-pointer" : ""}` }>
        <td>
          {claim.ro}
        </td>
        <td>
          {claim.date.Valid && moment(claim.date.Time).format(displayDateFormat)}
        </td>
        <td>
          {claim.status}
        </td>
        <td>
          {claim.service && claim.service.toUpperCase()}
        </td>
        <td>
          ${formatCurrency(claim.amount || 0.00)}
        </td>
        <td>
          {claim.facility_code}
        </td>
        <td>
          ${formatCurrency(claim.authorized_amount || 0.00)}
        </td>
        <td>
          {claim.authorization_date.Valid && moment(claim.authorization_date.Time).format(displayDateFormat)}
        </td>
        <td>
          {claim.paid_date.Valid && moment(claim.paid_date.Time).format(displayDateFormat)}
        </td>
        <td>
          {payment}
        </td>
      </tr>
    );
  };

  const renderCenturyClaims = () => {
    return (
      <table className="table table-striped">
        <thead>
          <tr>
            <th>RO</th>
            <th onClick={ handleSortByDate }>Date {renderSortIcon()} </th>
            <th>Status</th>
            <th>Product</th>
            <th>Requested</th>
            <th>RepFac</th>
            <th>Authorized</th>
            <th>AuthDate</th>
            <th>Paid</th>
            <th>Payment</th>
          </tr>
        </thead>
        <tbody>
          {claims && getClaimsToRender().map(renderCenturyClaim)}
        </tbody>
      </table>
    );
  };

  const renderDrivePurClaim = (claim, key) => {
    let payment = getPaymentField(claim);
    return (
      <tr key={ key } onClick={ () => onRowClick(claim.claim_id) } className = { `${claim.claim_id ? "text-primary cursor-pointer" : ""}` }>
        <td>
          {claim.date.Valid && moment(claim.date.Time).format(displayDateFormat)}
        </td>
        <td>
          {claim.status}
        </td>
        <td>
          {claim.miles}
        </td>
        <td>
          {claim.failed_components}
        </td>
        <td>
          {claim.ro}
        </td>
        <td>
          {payment}
        </td>
        <td>
          {claim.payment_dates && renderClaimPaidDate(claim.payment_dates) || ''}
        </td>
        <td>
          ${formatCurrency(claim.amount || 0.00)}
        </td>
      </tr>
    );
  };

  const renderDrivePurClaims = () => {
    return (
      <table className="table table-striped">
        <thead>
          <tr>
            <th onClick={ handleSortByDate }>Date {renderSortIcon()} </th>
            <th width="10%">Status</th>
            <th width="10%">Miles</th>
            <th width="30%">Failed Components</th>
            <th width="10%">RO</th>
            <th width="10%">Payment</th>
            <th width="10%">Paid Date</th>
            <th width="10%">Amount</th>
          </tr>
        </thead>
        <tbody>
          {claims && getClaimsToRender().map(renderDrivePurClaim)}
        </tbody>
      </table>
    );
  };

  const renderLeaseWearAndTearClaim = (claim, key) =>  {
    return (
      <tr key={ key } onClick={ () => onRowClick(claim.claim_id)} className = { `${claim.claim_id ? "text-primary cursor-pointer" : ""}` }>
        <td>
          {claim.date && moment(claim.date).format(displayDateFormat)}
        </td>
        <td>
          {claim.status}
        </td>
        <td>
          ${formatCurrency(claim.amount || 0.00)}
        </td>
        <td>
          {claim.payment_dates && renderClaimPaidDate(claim.payment_dates) || ''}
        </td>
      </tr>
    );
  };

  const renderLeaseWearAndTearClaims = () => {
    return (
      <table className="table table-striped">
        <thead>
          <tr>
            <th onClick={ handleSortByDate }>Date {renderSortIcon()} </th>
            <th>Status</th>
            <th>Amount</th>
            <th>Paid Date</th>
          </tr>
        </thead>
        <tbody>
          {claims && getClaimsToRender().map(renderLeaseWearAndTearClaim)}
        </tbody>
      </table>
    );
  };

  const renderOtherClaim = (claim, key) => {
    let payment = getPaymentField(claim);
    if (claim.amount < 0 && (claim.status === "CheckWritten" || claim.status === "Reversed" || claim.status === "CCPaid")) {
      return;
    }

    let claimID = claim.claim_id;
    if (claim.chargeback) {
      claimID = claim.parent_claim_id;
    }

    return (
      <tr key={ key } onClick={ () => onRowClick(claimID, claim.chargeback) } className = { `${claim.claim_id ? "text-primary cursor-pointer" : ""}` }>
        <td>
          {claim.date.Valid && moment.utc(claim.date.Time).format(displayDateFormat)}
        </td>
        <td>
          {claim.status}
        </td>
        <td>
          {claim.miles}
        </td>
        <td>
          {claim.failed_components}
        </td>
        <td>
          {claim.ro}
        </td>
        <td>
          {payment}
        </td>
        <td>
          {claim.payment_dates && renderClaimPaidDate(claim.payment_dates) || ''}
        </td>
        <td>
          ${formatCurrency(claim.amount || 0.00)}
        </td>
      </tr>
    );
  };

  const renderOtherClaims = () => {
    return (
      <table className="table table-striped">
        <thead>
          <tr>
            <th width="10%" onClick={ handleSortByDate }>Date {renderSortIcon()} </th>
            <th width="10%">Status</th>
            <th width="10%">Miles</th>
            <th width="30%">Failed Components</th>
            <th width="10%">RO</th>
            <th width="10%">Payment</th>
            <th width="10%">Paid Date</th>
            <th width="10%">Amount</th>
          </tr>
        </thead>
        <tbody>
          {claims && getClaimsToRender().map(renderOtherClaim)}
        </tbody>
      </table>
    );
  };

  const renderGAPClaim = (claim, key) => {
    if (claim.amount < 0 && (claim.status === "CheckWritten" || claim.status === "Reversed" || claim.status === "CCPaid")) {
      return;
    }
    return (
      <tr key={ key } onClick={ () => onRowClick(claim.claim_id) } className = { `${claim.claim_id ? "text-primary cursor-pointer" : ""}` }>
        <td>
          {claim.date.Valid && moment.utc(claim.date.Time).format(displayDateFormat)}
        </td>
        <td>
          {claim.status}
        </td>
        <td>
          {moment.utc(claim.date_of_loss).format(displayDateFormat)}
        </td>
        <td>
          {claim.miles}
        </td>
        <td>
          {claim.paid_date && claim.paid_date.Valid && moment.utc(claim.paid_date.Time).format(displayDateFormat)}
        </td>
        <td>
          ${formatCurrency(claim.amount || 0.00)}
        </td>
      </tr>
    );
  };

  const renderGAPClaims = () => {
    return (
      <table className="table table-striped">
        <thead>
          <tr>
            <th width="10%" onClick={ handleSortByDate }>Date Received {renderSortIcon()} </th>
            <th width="10%">Status</th>
            <th width="10%">DOL</th>
            <th width="10%">Miles</th>
            <th width="10%">Paid Date</th>
            <th width="10%">Amount</th>
          </tr>
        </thead>
        <tbody>
          {claims && getClaimsToRender().map(renderGAPClaim)}
        </tbody>
      </table>
    );
  };

  const renderVTAClaim = (claim, key) => {
    if (claim.amount < 0 && (claim.status === "CheckWritten" || claim.status === "Reversed" || claim.status === "CCPaid")) {
      return;
    }
    return (
      <tr key={ key } onClick={ () => onRowClick(claim.claim_id) } className = { `${claim.claim_id ? "text-primary cursor-pointer" : ""}` }>
        <td>
          {claim.date.Valid && moment.utc(claim.date.Time).format(displayDateFormat)}
        </td>
        <td>
          {claim.status}
        </td>
        <td>
          {claim.paid_date && claim.paid_date.Valid && moment.utc(claim.paid_date.Time).format(displayDateFormat)}
        </td>
        <td>
          ${formatCurrency(claim.amount || 0.00)}
        </td>
      </tr>
    );
  };

  const renderVTAClaims = () => {
    return (
      <table className="table table-striped">
        <thead>
          <tr>
            <th width="10%" onClick={ handleSortByDate }>Date Received {renderSortIcon()} </th>
            <th width="10%">Status</th>
            <th width="10%">Paid Date</th>
            <th width="10%">Amount</th>
          </tr>
        </thead>
        <tbody>
          {claims && getClaimsToRender().map(renderVTAClaim)}
        </tbody>
      </table>
    );
  };

  return (
    <div className="col-sm-12 col-xs-12">
      <div className="row">
        <div className="col-sm-6 col-xs-12"><label className="control-label ml-10 font-weight-bold">Claims</label></div>
      </div>
      { claims && claims.length > 0 &&
        <div className="row">
          <div className="col-sm-12 col-xs-12">
            {
              product_code === CONSTANTS.PRODUCT_CODE_NEW_MAP.century &&
                renderCenturyClaims()
            }
            {
              product_code === CONSTANTS.PRODUCT_CODE_NEW_MAP.drivePur &&
                renderDrivePurClaims()
            }
            {
              product_code === CONSTANTS.PRODUCT_CODE_NEW_MAP.gap &&
                renderGAPClaims()
            }
            {
              product_code === CONSTANTS.PRODUCT_CODE_NEW_MAP.leaseWearTear &&
                renderLeaseWearAndTearClaims()
            }
            {
              product_code === CONSTANTS.PRODUCT_CODE_NEW_MAP.theftRegistration &&
                renderVTAClaims()
            } 
            {
              product_code !== CONSTANTS.PRODUCT_CODE_NEW_MAP.century &&
              product_code !== CONSTANTS.PRODUCT_CODE_NEW_MAP.drivePur &&
              product_code !== CONSTANTS.PRODUCT_CODE_NEW_MAP.gap &&
              product_code !== CONSTANTS.PRODUCT_CODE_NEW_MAP.leaseWearTear &&
              product_code !== CONSTANTS.PRODUCT_CODE_NEW_MAP.theftRegistration &&
                renderOtherClaims()
            }
          </div>
        </div>
      }
      <div className="row">
        <div>
          <Pagination
            limit={PER_PAGE}
            count={count}
            page={page}
            setPage={setPage} />
        </div>
        <div className="col-sm-6 col-xs-12">
          <label className="control-label ml-10">{`${count > 0 ? count : "No "} Claims`}</label>
        </div>
      </div>
    </div>
  );
};

Claims.propTypes = {
  claims: PropTypes.arrayOf(PropTypes.shape({
    date: PropTypes.shape({
      Time: PropTypes.string,
      Valid: PropTypes.bool,
    }),
    miles: PropTypes.string,
    ro: PropTypes.string,
    failed_components: PropTypes.string,
    service: PropTypes.string,
    amount: PropTypes.string,
    payment_dates: PropTypes.arrayOf(PropTypes.string),
    authorization_date: PropTypes.shape({
      Time: PropTypes.string,
      Valid: PropTypes.bool,
    }),
    facility_code: PropTypes.string,
    authorized_amount: PropTypes.string,
    check_number: PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.arrayOf(PropTypes.string)
    ]),
  })),
  product_code: PropTypes.string,
  loadContractHistory: PropTypes.func,
};

Claims.contextTypes = {
  router: PropTypes.object.isRequired,
};

export default Claims;