import React from 'react';
import { Link } from 'react-router';
import moment from 'moment';
import dateFormat from "./../reusable/Utilities/dateFormat.js";
import PropTypes from 'prop-types';


const sortColKeys = {
  "status": "status",
  "name": "name",
  "vin": "vin",
  "year":"year",
  "make": "make",
  "model": "model",
  "contract": "contract",
  "store": "store",
  "date": "date",
};

export default class ContractList extends React.Component {

  static propTypes = {
    contractList: PropTypes.array.isRequired,
    statusMap: PropTypes.object.isRequired,
    onSort: PropTypes.func.isRequired,
    currentSortOrder: PropTypes.func,
    currentSortBy: PropTypes.func,
  };

  constructor(props) {
    super(props);
    this.renderTableBody = this.renderTableBody.bind(this);
    this.renderTableBodyRow = this.renderTableBodyRow.bind(this);
    this.onSort = this.onSort.bind(this);
  }

  renderSortOrder(sortColName) {
    if (this.props.currentSortBy() !== sortColName) {
      return;
    }
    let sortOrder = this.props.currentSortOrder();
    if (sortOrder === "asc") {
      return <i className="fa fa-sort-asc"></i>;
    } else if (sortOrder === "desc") {
      return <i className="fa fa-sort-desc"></i>;
    }
  }

  onSort(e) {
    e.preventDefault();
    const colList = [
      sortColKeys.status, sortColKeys.date, sortColKeys.store,
      sortColKeys.contract, sortColKeys.model, sortColKeys.make,
      sortColKeys.year, sortColKeys.vin, sortColKeys.name
    ];
    if (e.target.id && colList.find((key) => { return key === e.target.id;})) {
      this.props.onSort(e.target.id);
    }
  }

  renderTableHeader() {
    const textSize = { "fontSize": "13px", "whiteSpace": "nowrap" };
    return (
      <tr style={ textSize } onClick={ this.onSort }>
        <th id={ sortColKeys.status }><i className="fa fa-check"></i>&nbsp;{this.renderSortOrder(sortColKeys.status)}</th>
        <th className="col-4" id={ sortColKeys.name }>Name&nbsp;{this.renderSortOrder(sortColKeys.name)}</th>
        <th className="col-3" id={ sortColKeys.vin }>Vehicle&nbsp;{this.renderSortOrder(sortColKeys.vin)}</th>
        <th className="col-2" id={ sortColKeys.year }>Year&nbsp;{this.renderSortOrder(sortColKeys.year)}</th>
        <th className="col-2" id={ sortColKeys.make }>Make&nbsp;{this.renderSortOrder(sortColKeys.make)}</th>
        <th className="col-2" id={ sortColKeys.model }>Model&nbsp;{this.renderSortOrder(sortColKeys.model)}</th>
        <th className="col-2" id={ sortColKeys.contract }>Contract #&nbsp;{this.renderSortOrder(sortColKeys.contract)}</th>
        <th className="col-3" id={ sortColKeys.store }>Issuing Dealer&nbsp;{this.renderSortOrder(sortColKeys.store)}</th>
        <th className="col-2">Dealer #</th>
        <th className="col-2" id={ sortColKeys.date }>Date&nbsp;{this.renderSortOrder(sortColKeys.date)}</th>
      </tr>
    );
  }

  renderTableBody() {
    return this.props.contractList.map(this.renderTableBodyRow);
  }

  renderTableBodyRow(contractData, index) {
    /**
     * Active and pending contract displayed with 'check' icon and remaining with 'x' icon
     * It indicates contract is available for new claim or not.
     * */
    let contractStatusIcon = <i className="fa fa-plus text-warning"/>;
    if (contractData.status === this.props.statusMap.Active ||
          contractData.status === this.props.statusMap.Remitted) {
      contractStatusIcon = <i className="fa fa-check text-success"/>;
    } else if(contractData.status === this.props.statusMap.Canceled ||
          contractData.status === this.props.statusMap.Expired ||
          contractData.status === this.props.statusMap.Voided) {
      contractStatusIcon = <i className="fa fa-times text-danger"/>;
    } else if(contractData.status === this.props.statusMap.Pending){
      contractStatusIcon = <i className="fa fa-exclamation text-warning"/>;
    }
    const longTextStyle = {
      "maxWidth": "130px",
      "overflow": "hidden",
      "textOverflow": "ellipsis",
      "whiteSpace": "nowrap"
    };
    return (
      <tr key={ index }>
        <td>
          {contractStatusIcon}
        </td>
        <td style={longTextStyle}>
          <Link to={`/gap-contract/${contractData['code']}?product_code=${contractData['product_code']}&id=${contractData['id']}`} className="word-wrap">
            {contractData['customer_name']}
          </Link>
        </td>
        <td className="text-nowrap">
          <Link to={`/gap-contract/${contractData['code']}?product_code=${contractData['product_code']}&id=${contractData['id']}`}>{contractData['vin']}</Link>
        </td>
        <td className="text-nowrap">
          {contractData['vehicle_year']}
        </td>
        <td className="text-nowrap">
          {contractData['vehicle_make']}
        </td>
        <td className="text-nowrap">
          {contractData['vehicle_model']}
        </td>
        <td className="text-nowrap">
          <Link to={`/gap-contract/${contractData['code']}?product_code=${contractData['product_code']}&id=${contractData['id']}`}>{contractData['original_code'] || contractData['code']}</Link>
        </td>
        <td className="text-nowrap">
          {contractData['store_code']}
        </td>
        <td className="text-nowrap">
          {contractData['store_number']}
        </td>
        <td className="text-nowrap">
          {moment(contractData['effective_date']).format(dateFormat.displayDateFormat)}
        </td>
      </tr>
    );
  }

  render() {
    return (
      <table className="table table-striped table-responsive">
        <thead className="thead-light">
          {this.renderTableHeader()}
        </thead>
        <tbody>
          {this.renderTableBody()}
        </tbody>
      </table>
    );
  }
}
