import React from 'react';
import PageHeader from '../pageHeader/PageHeader.jsx';
import SearchBox from '../reusable/SearchBox/Search.jsx';
import ContractList from './ContractList.jsx';
import Loader from 'react-loader-advanced';
import <PERSON>ert from 'react-s-alert';
import { json as ajax } from './../../ajax.js';
import Pagination from './../../Pagination.jsx';
import { URLS as apiUrls } from "./../reusable/Utilities/urls";
import { CONSTANTS } from "./../reusable/Constants/constants.js";
import PropTypes from 'prop-types';

export default class ContractsDashboard extends React.Component {

  static contextTypes = {
    router: PropTypes.object.isRequired
  };

  static propTypes = {
    location: PropTypes.shape({
      query: PropTypes.shape({
        page: PropTypes.string,
        search: PropTypes.string,
        sortBy: PropTypes.string,
        sortOrder: PropTypes.oneOf(["asc", "desc"])
      }).isRequired,
    }).isRequired,
    user: PropTypes.shape({
      id: PropTypes.number.isRequired,
      first_name: PropTypes.string.isRequired,
      last_name: PropTypes.string.isRequired,
      email: PropTypes.string.isRequired,
      stores: PropTypes.arrayOf(PropTypes.string.isRequired),
      roles: PropTypes.shape({
        Map: PropTypes.object
      }).isRequired
    }),
  };

  constructor(props) {
    super(props);
    this.state = {
      searchQuery: undefined,
      contractList: [],
      numberOfContracts: undefined,
      voidedOrGenerate: false,
      pageLimit: 20,
      showLoader: false
    };
    this.currentPage = this.currentPage.bind(this);
    this.currentSearchQuery = this.currentSearchQuery.bind(this);
    this.currentSortBy = this.currentSortBy.bind(this);
    this.currentSortOrder = this.currentSortOrder.bind(this);
    this.setPage = this.setPage.bind(this);
    this.onSearch = this.onSearch.bind(this);
    this.onSortByChange = this.onSortByChange.bind(this);
    this.loadContracts = this.loadContracts.bind(this);
    this.renderPagination = this.renderPagination.bind(this);
    this.renderContractList = this.renderContractList.bind(this);
  }

  componentDidMount() {
    document.title = 'TCA Portal - Contracts';
    this.loadContracts();
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (this.currentPage(nextProps) !== this.currentPage(this.props) ||
      this.currentSearchQuery(nextProps) !== this.currentSearchQuery(this.props) ||
      this.currentSortBy(nextProps) !== this.currentSortBy(this.props) ||
      this.currentSortOrder(nextProps) !== this.currentSortOrder(this.props)) {
      this.setState({
        searchQuery: nextProps.location.query.search || ""
      });
      this.loadContracts(nextProps);
    }
  }

  currentPage(props) {
    if (!props) {
      props = this.props;
    }
    return parseInt(props.location.query.page, 10) || 1;
  }

  currentSearchQuery(props) {
    if (!props) {
      props = this.props;
    }
    return props.location.query.search;
  }

  currentSortBy(props) {
    if (!props) {
      props = this.props;
    }
    return props.location.query.sortBy;
  }

  currentSortOrder(props) {
    if (!props) {
      props = this.props;
    }
    return props.location.query.sortOrder;
  }

  setPage(page) {
    const query = { page };
    if (this.currentSearchQuery()) {
      query.search = this.currentSearchQuery();
    }
    query.sortBy = this.currentSortBy(this.props);
    query.sortOrder = this.currentSortOrder(this.props);
    const route = { pathname: "/contracts", query };
    if (!this.context.router.isActive(route)) {
      this.context.router.push(route);
    }
  }

  onSearch(searchQuery) {
    if (this.currentSearchQuery() !== searchQuery) {
      const query = { search: searchQuery, page: 1 };
      const route = { pathname: "/contracts", query };
      if (!this.context.router.isActive(route)) {
        this.context.router.push(route);
      }
    }
  }

  onSortByChange(sortBy) {
    let currentSortBy = this.currentSortBy(this.props);
    let sortOrder = this.currentSortOrder(this.props);
    if (currentSortBy === sortBy) {
      if (sortOrder === "asc") {
        sortOrder = "desc";
      } else {
        sortOrder = "asc";
      }
    } else {
      sortOrder = "asc";
    }
    const query = {
      search: this.currentSearchQuery(this.props),
      sortBy: sortBy,
      sortOrder: sortOrder,
      page: this.currentPage(this.props)
    };
    const route = { pathname: "/contracts", query };
    this.context.router.push(route);
  }

  /**
   * This function will fetch contract list from backend based on fitlers provided.
   *
   * filters:
   *  Search Query :
   *    Type: {String}
   *    Description: Search query.
   *  page :
   *    Type: {Integer}
   *    Description: Page number
   * */

  loadContracts(props) {
    if (!props) {
      props = this.props;
    }
    if (!this.currentSearchQuery(props)) {
      return;
    }
    this.setState({
      searchQuery: this.currentSearchQuery(props) || ""
    });
    let url = `${apiUrls.contracts}?search=${window.encodeURIComponent(this.currentSearchQuery(props).trim())}`;
    if (this.currentPage(props)) {
      url += (`&page=${window.encodeURIComponent(this.currentPage(props))}`);
    }
    let sortBy = this.currentSortBy(props);
    if (sortBy) {
      url += (`&sortBy=${window.encodeURIComponent(sortBy)}`);
    }
    let sortOrder = this.currentSortOrder(props);
    if (sortOrder) {
      url += (`&sortOrder=${window.encodeURIComponent(sortOrder)}`);
    }

    this.setState({ showLoader: true }, () => {
      ajax(url, {}, {}, (data, status) => {
        if (status === 200) {
          this.setState({
            contractList: data.contracts,
            numberOfContracts: data.count,
            showLoader: false,
            voidedOrGenerated: data.voided_or_generated
          });
        } else if (status === 404) {
          this.setState({
            showLoader: false,
            contractList: [],
            numberOfContracts: 0,
            voidedOrGenerated: false
          });
        } else {
          this.setState({ showLoader: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the data. If the error continues, click the Support button on the right to Open a HelpDesk ticket.");
          });
        }
      });
    });
  }

  renderPagination() {
    if (this.state.numberOfContracts > this.state.pageLimit) {
      const limitPerPage = this.state.pageLimit;
      return (
        <div className="row no-gutters">
          <div className="pagination-container d-flex justify-content-end col-12">
            <div className="mr-auto">
              <p>Showing {this.currentPage() * limitPerPage - limitPerPage + 1}&nbsp;
                to {this.currentPage() * limitPerPage > this.state.numberOfContracts ? this.state.numberOfContracts : this.currentPage() * limitPerPage}&nbsp;
                of {this.state.numberOfContracts} items
              </p>
            </div>
            <div>
              <Pagination page={ this.currentPage() } count={ this.state.numberOfContracts }
                limit={ this.state.pageLimit } setPage={ this.setPage }/>
            </div>
          </div>
        </div>
      );
    }
  }

  renderContractList() {
    if (this.state.contractList && this.state.contractList.length > 0) {
      return (<ContractList contractList={ this.state.contractList }
        statusMap={ CONSTANTS.CONTRACT_STATUS_NEW_MAP }
        onSort={ this.onSortByChange }
        currentSortOrder={ this.currentSortOrder }
        currentSortBy={ this.currentSortBy }/>);
    } else if (this.state.searchQuery && !this.state.showLoader) {
      return (
        <div className="text-center">
          <p>No results available for the search criteria</p>
        </div>
      );
    }
  }

  render() {
    let infoText = 'Search for a contract above.';
    if (this.state.contractList && this.state.contractList.length && this.state.searchQuery) {
      infoText = 'Select a contract from the list above.';
    }
    const spinnerMessage = <p className='text-center'><i className="fa fa-refresh fa-spin"/> Loading...</p>;
    return (
      <Loader show={ this.state.showLoader } message={ spinnerMessage }>
        <section className="row">
          <div className="col-12">
            <PageHeader pageTitle="Contracts" user={this.props.user}/>
            <div className="row">
              <div className="form-inline my-3 col-12 no-gutters">
                <div className="col-6">
                  <SearchBox onSearch={ this.onSearch }
                    placeholder="Search Name or VIN or contract ID"
                    value={ this.currentSearchQuery() }/>
                </div>
              </div>
            </div>
            {
              this.currentSearchQuery() && this.state.voidedOrGenerated &&
                  <div className="row mb-3 pl-3">
                    <small className='text-muted'>The contract you searched for was voided or has not been remitted. Contracts below are associated with the VIN tied to the contract you searched.</small>
                  </div>
            }
            {this.renderContractList()}
            {this.renderPagination()}
            <p className="text-center">{infoText}</p>
          </div>
        </section>
      </Loader>
    );
  }
}
