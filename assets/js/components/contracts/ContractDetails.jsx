import React from "react";
import Alert from "react-s-alert";
import { json as ajax } from "./../../ajax.js";
import Loader from "react-loader-advanced";
import immstruct from "immstruct";
import Immutable from "immutable";
import accounting from "accounting";
import moment from "moment";
import dateFormat from "./../reusable/Utilities/dateFormat.js";
import RecordNotes from "../reusable/RecordNotes/RecordNotes.jsx";
import { URLS as apiUrls } from "./../reusable/Utilities/urls";
import { CONSTANTS } from "./../reusable/Constants/constants.js";
import PropTypes from 'prop-types';
import { userHasRole } from "../reusable/Utilities/userHasRole";
import StateList from "../reusable/StatesList/StateList";
import Claims from "./Claims";
import AttachmentListModal from "../automotiveClaimWorksheet/AttachmentListModal";
import hstore from "../reusable/Utilities/hstore.js";

const autoClaims = [
  CONSTANTS.PRODUCT_CODE_NEW_MAP.service,
  CONSTANTS.PRODUCT_CODE_NEW_MAP.keyReplacement,
  CONSTANTS.PRODUCT_CODE_NEW_MAP.appearanceProtection,
  CONSTANTS.PRODUCT_CODE_NEW_MAP.paintlessDentRepair,
  CONSTANTS.PRODUCT_CODE_NEW_MAP.maintenance,
  CONSTANTS.PRODUCT_CODE_NEW_MAP.drivePur
];

const gapClaims = CONSTANTS.PRODUCT_CODE_NEW_MAP.gap;

const lwtClaims = CONSTANTS.PRODUCT_CODE_NEW_MAP.leaseWearTear;
const vtaClaims = CONSTANTS.PRODUCT_CODE_NEW_MAP.theftRegistration;

const OWNER = "owner";
const CO_OWNER = "co-owner";

export default class Contract extends React.Component {

  IMMS_KEY = 'contract';

  static propTypes = {
    id: PropTypes.number.isRequired,
    contract_number: PropTypes.string.isRequired,
    contractData: PropTypes.shape({
      product_code: PropTypes.string.isRequired,
      deal_number: PropTypes.string.isRequired,
      store_code: PropTypes.string.isRequired,
      product_name: PropTypes.string.isRequired,
    }).isRequired,
    editedContract: PropTypes.func.isRequired,
    redirectToPrevious: PropTypes.func,
    activeTab: PropTypes.string,
    user: PropTypes.shape({
      id: PropTypes.number.isRequired,
      first_name: PropTypes.string.isRequired,
      last_name: PropTypes.string.isRequired,
      email: PropTypes.string.isRequired,
      stores: PropTypes.arrayOf(PropTypes.string.isRequired),
      roles: PropTypes.shape({
        Map: PropTypes.object,
      }).isRequired
    }),
    loadContractHistory: PropTypes.func,
    contractClaims: PropTypes.array
  };

  constructor(props) {
    super(props);
    this.contract_details = immstruct(
      this.IMMS_KEY,
      {
        "contract": {
          "id": 0,
          "code": "",
          "product_variant_display_name": "",
          "effective_date": "",
          "expiration_date": "",
          "effective_mileage": 0,
          "expiration_mileage": 0,
          "plan_name": "",
          "issuing_dealer": "",
          "issuing_dealer_number": "",
          "book_date": "",
          "price": "",
          "plan_cost": "",
          "cost": "",
          "invoiced_at": "",
          "invoice_number": "",
          "options": [],
          "surcharges": [],
          "maintenance_visits": "",
          "minor_coupons_purchased": "",
          "status": "",
          "source": "",
          "fabric": "",
          "leather_or_vinyl": "",
          "paint": ""
        },
        "customer_details": {
          "id": 0,
          "first_name": "",
          "last_name": "",
          "address": "",
          "city": "",
          "state_code": "",
          "postal_code": "",
          "phone": "",
          "alternate_phone": "",
          "email": "",
          "best_contact_method": ""
        },
        "vehicle_details": {
          "vin": "",
          "year": "",
          "make": "",
          "model": "",
          "is_new": false,
          "odometer": ""
        },
        "financing_details": {
          "payment_type": "",
          "lender_name": "",
          "lender_address": "",
          "lender_city": "",
          "lender_state": "",
          "lender_zip": "",
          "sale_type": "",
          "vehicle_price": 0,
          "finance_amount": 0,
          "finance_apr": 0,
          "finance_monthly_payment": 0,
          "first_payment_date": "",
          "contract_date": "",
          "msrp": 0,
          "sales_man": "",
          "term": 0
        },
        "cobuyer": {
          "id": 0,
          "first_name": "",
          "last_name": "",
          "address": "",
          "city": "",
          "state_code": "",
          "postal_code": "",
          "home_phone": "",
          "alt_phone": "",
          "email": ""
        },
        "claims": [],
        "contract_events": null,
        "cancellation": null,
        "transfers":null,
        "attachments" : [],
        "inspectionAttachments" : [],
      }
    );
    this.contract_details.on("swap", (newStructure, oldStructure, keyPath) => {
      this.setState({ contractDetails: this.contract_details.cursor() });
    });
    this.state = {
      contractDetails: this.contract_details.cursor(),
      contractClaims: [],
      cancelContract: void 0,
      reinstateContract: void 0,
      showLoader: false,
      displayContract: this.props.activeTab || "contracts",
      hasContractNotes: false,
      newNoteText: "",
      isCustomerEdited: false,
      redirectToBack: false,
      attachments: [],
      inspectionAttachments : [],
      displayAttachmentListModal: false,
      isInspectionAttachments: false,
    };
  }

  UNSAFE_componentWillReceiveProps = (nextProps) => {
    if (this.props.id !== nextProps.id) {
      this.loadContracts(nextProps.id, nextProps.contract_number);
    }
  };

  componentDidMount = () => {
    this.loadContracts(this.props.id, this.props.contract_number);
  };

  componentWillUnmount = () => {
    this.contract_details.removeAllListeners();
    immstruct.remove(this.IMMS_KEY);
  };

  loadContracts = (id, contract_number) => {
    this.setState({ showLoader: true }, () => {
      ajax(`${apiUrls.contract}/${id}`, {}, {}, (data, status) => {
        if (status === 200) {
          this.state.contractDetails.update(() => Immutable.fromJS(data.contract));
          const isReinstatement = data.contract.reinstate && data.contract.reinstate.reinstate_date;
          if (data.contract.contract.status === CONSTANTS.CONTRACT_STATUS_NEW_MAP.Canceled || isReinstatement) {
            this.setState({ cancelContract: data.contract.cancellation });
          } else {
            this.setState({ cancelContract: null });
          }
          
          if (isReinstatement) {
            this.setState({ reinstateContract: data.contract.reinstate });
          } else {
            this.setState({ reinstateContract: null });
          }

          ajax(`${apiUrls.contract}/${contract_number}/notes`, {}, {}, (data, status) => {
            if (status === 200 && data.notes.length !== 0) {
              this.setState({
                hasContractNotes: true
              });
            }
          });
          ajax(`${apiUrls.contract}/${contract_number}/attachments`, {}, {}, (data, status) => {
            if (status === 200 && data.attachments.length !== 0) {
              this.setState({
                attachments: data.attachments
              });
            } else {
              this.setState({
                attachments: []
              });
            }
          });
          ajax(`${apiUrls.contract}/${contract_number}/inspection/attachments`, {}, {}, (data, status) => {
            if (status === 200 && data.attachments && data.attachments.length !== 0) {
              this.setState({
                inspectionAttachments: data.attachments
              });
            } else {
              this.setState({
                inspectionAttachments: []
              });
            }
          });
          this.setState({ showLoader: false });
          this.props.loadContractHistory(true);
        } else if (status === 404) {
          this.setState({ showLoader: false });
        } else {
          this.setState({ showLoader: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  isContractsTabActive = () => {
    return this.state.displayContract === "contracts";
  };

  isVehicleTabActive = () => {
    return this.state.displayContract === "vehicle";
  };

  isCustomerTabActive = () => {
    return this.state.displayContract === "customer";
  };

  isFinancingTabActive = () => {
    return this.state.displayContract === "financing";
  };

  isEventsTabActive = () => {
    return this.state.displayContract === "events";
  };

  isNotesTabActive = () => {
    return this.state.displayContract === "notes";
  };

  updateCustomerDetails = () => {
    const data = this.state.contractDetails.toJS();
    const customerData = {
      customer: {
        address: data.customer_details.address,
        city: data.customer_details.city,
        state_code: data.customer_details.state_code,
        postal_code: data.customer_details.postal_code,
        email: data.customer_details.email,
        phone: data.customer_details.phone,
      }
    };
    customerData.cobuyer = {};
    const firstName = data.cobuyer.first_name;
    const lastName = data.cobuyer.last_name;
    const address = data.cobuyer.address;
    const postalCode = data.cobuyer.postal_code;
    const city = data.cobuyer.city;
    const stateCode = data.cobuyer.state_code;
    const homePhone = data.cobuyer.home_phone;
    const altPhone = data.cobuyer.alt_phone;
    const email = data.cobuyer.email;

    //Check for if one field is present both firstName and lastName should be present
    if((address || postalCode || city || stateCode || homePhone || altPhone || email || firstName || lastName) && !(firstName && lastName)){
      Alert.error("First Name and Last Name required for co-buyer");
      return;
    }

    customerData.cobuyer.first_name = firstName;
    customerData.cobuyer.last_name = lastName;
    customerData.cobuyer.address = address;
    customerData.cobuyer.postal_code = postalCode;
    customerData.cobuyer.city = city;
    customerData.cobuyer.state_code = stateCode;
    customerData.cobuyer.home_phone = homePhone;
    customerData.cobuyer.alt_phone = altPhone;
    customerData.cobuyer.email = email;
  
    this.setState({ showLoader: true }, () => {
      // Update customer information on SB and DB
      ajax(`${apiUrls.contracts}/${this.props.id}`, customerData, { method: "PUT" }, (data, status) => {
        if (status === 200) {
          Alert.success("Update successful.");
          this.loadContracts(this.props.id, this.props.contract_number);
          this.props.editedContract(false);
          this.setState({ isCustomerEdited: false }, () => {
            this.redirectToBack();
          });
        } else if (status === 404) {
          this.setState({ showLoader: false }, () => {
            Alert.warning("No claim was found");
          });
        } else {
          this.setState({ showLoader: false }, () => {
            Alert.error(" Click the Update button again. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  onBackButtonAction = () => {
    this.setState({ redirectToBack: true }, () => {
      if (this.state.isCustomerEdited) {
        this.updateCustomerDetails();
      }
      if (this.state.newNoteText !== "") {
        this.RecordNotes.saveNote();
      }
    });
  };

  savedNoteCallback = () => {
    this.props.editedContract(false);
    this.setState({ newNoteText: "" }, () => {
      this.redirectToBack();
    });
  };

  redirectToBack = () => {
    if (!this.state.isCustomerEdited && this.state.newNoteText === "" && this.state.redirectToBack) {
      this.props.redirectToPrevious();
    }
  };

  handleSubmit = (event) => {
    event.preventDefault();
    this.updateCustomerDetails();
  };

  onCursorValueChange = (prefix, name, event) => {
    let key = "customer_details";

    if(prefix === CO_OWNER){
      key = "cobuyer";
    }
    this.state.contractDetails.cursor([key, name]).update(() => event.target.value);
    this.setState({ isCustomerEdited: true }, () => {
      this.props.editedContract(true);
    });
  };

  onCursorChange = (prefix, name, value) => {
    let key = "customer_details";

    if(prefix === CO_OWNER){
      key = "cobuyer";
    }
    this.state.contractDetails.cursor([key, name]).update(() => value);
    this.setState({ isCustomerEdited: true }, () => {
      this.props.editedContract(true);
    });
  };

  renderBestContactList = () => {
    const bestContactArray = [
      {
        "method": "Do not contact",
        "symbol": "D"
      },
      {
        "method": "Telephone",
        "symbol": "T"
      },
      {
        "method": "Email",
        "symbol": "E"
      },
      {
        "method": "Mail",
        "symbol": "M"
      },
      {
        "method": "Cellphone",
        "symbol": "C"
      }];
    return bestContactArray.map(this.renderContactMethod);
  };

  renderContactMethod = (contactMethod, index) => {
    return (<option key={ index } value={ contactMethod.symbol }>{contactMethod.method}</option>);
  };

  getContractStatusBlock(contractDetails) {
    let contractStatus = contractDetails.status;
    let contractStatusBlock = "";
    if (contractStatus === CONSTANTS.CONTRACT_STATUS_NEW_MAP.Active || contractStatus === CONSTANTS.CONTRACT_STATUS_NEW_MAP.Remitted) {
      contractStatusBlock = <span className="badge badge-success">{`Active`}</span>;
    } else if (contractStatus === CONSTANTS.CONTRACT_STATUS_NEW_MAP.Pending) {
      if(this.props.contractData.product_code === CONSTANTS.PRODUCT_CODE_NEW_MAP.gap){
        contractStatusBlock = <span className="badge badge-warning">{`Paid`}</span>;
      } else {
        contractStatusBlock = <span className="badge badge-warning">{`Pending`}</span>;
      }
    } else if (contractStatus === CONSTANTS.CONTRACT_STATUS_NEW_MAP.Canceled || contractStatus === CONSTANTS.CONTRACT_STATUS_NEW_MAP.PendingCancel) {
      contractStatusBlock = <span className="badge badge-danger">{`Cancelled`}</span>;
    } else if (contractStatus === CONSTANTS.CONTRACT_STATUS_NEW_MAP.Expired) {
      contractStatusBlock = <span className="badge badge-danger">{`Expired`}</span>;
    } else if (contractStatus === CONSTANTS.CONTRACT_STATUS_NEW_MAP.Generated) {
      contractStatusBlock = <span className="badge badge-success">{`New`}</span>;
    }
    return contractStatusBlock;
  }

  /**
   * getContractTransferBadge() function returns transfer status for contract
   * @returns transfer status span component
   */
  getContractTransferBadge(contract) {
    let contractTransferStatusBlock = "";
    const contractDetails = this.state.contractDetails.toJS();
    
    if (contract.status === CONSTANTS.CONTRACT_STATUS_NEW_MAP.Expired) {
      return contractTransferStatusBlock;
    }
    if (contractDetails.transfers && contractDetails.transfers.length > 0) {
      contractTransferStatusBlock = <span className="badge badge-warning">{`Transferred`}</span>;
    }
    return contractTransferStatusBlock;
  }

  /**
   * getContractReInstateBadge() function returns reinstate status for contract
   * @returns reinstate status span component
   */
  getContractReInstateBadge(contractDetails) {
    const {
      reinstateContract,
    } = this.state;

    let contractReinstateStatusBlock = "";
    if (contractDetails.status === CONSTANTS.CONTRACT_STATUS_NEW_MAP.Expired) {
      return contractReinstateStatusBlock;
    }
    if (reinstateContract && reinstateContract.reinstate_date && !reinstateContract.is_void) {
      contractReinstateStatusBlock = <span className="badge badge-success">{`Reinstated`}</span>;
    } 
    return contractReinstateStatusBlock;
  }

  contractTypeSwitch(contractDetails) {
    switch (this.props.contractData.product_code) {
    case CONSTANTS.PRODUCT_CODE_NEW_MAP.service:
      return this.renderContractSCTable(contractDetails);
    case CONSTANTS.PRODUCT_CODE_NEW_MAP.maintenance:
      return this.renderContractMCTable(contractDetails);
    case CONSTANTS.PRODUCT_CODE_NEW_MAP.appearanceProtection:
    case CONSTANTS.PRODUCT_CODE_NEW_MAP.paintlessDentRepair:
      return this.renderContractCPTable(contractDetails);
    case CONSTANTS.PRODUCT_CODE_NEW_MAP.gap:
      return this.renderContractGPTable(contractDetails);
    case CONSTANTS.PRODUCT_CODE_NEW_MAP.toyotaTireWheel:
    case CONSTANTS.PRODUCT_CODE_NEW_MAP.toyotaGap:
      return this.renderContractTTAndTGTable(contractDetails);
    default:
      return this.renderContractTable(contractDetails);
    }
  }

  renderContractsTabContent(contractDetails, financingDetails) {
    const {
      cancelContract,
      reinstateContract,
    } = this.state;

    let olderCancellation = false;
    if (reinstateContract && reinstateContract.reinstate_date && cancelContract && cancelContract.cancel_date) {
      olderCancellation = moment.utc(reinstateContract.reinstate_date).local(false).isAfter(moment.utc(cancelContract.cancel_date).local(true));
    }

    let renderer = (
      <div className="row p-2">
        <div className="col-12 pt-3 px-3">
          {
            this.contractTypeSwitch(contractDetails)
          }
        </div>
        <div className="col-12 pt-3 px-3">
          {
            this.renderCancellationDetails(contractDetails, financingDetails)
          }
        </div>
        <div className="col-12 pt-3 px-3">
          {
            this.renderReInstateDetails(contractDetails)
          }
        </div>
      </div>
    );

    if (olderCancellation) {
      renderer = (
        <div className="row p-2">
          <div className="col-12 pt-3 px-3">
            {
              this.contractTypeSwitch(contractDetails)
            }
          </div>
          <div className="col-12 pt-3 px-3">
            {
              this.renderReInstateDetails(contractDetails)
            }
          </div>
          <div className="col-12 pt-3 px-3">
            {
              this.renderCancellationDetails(contractDetails, financingDetails)
            }
          </div>
        </div>
      );
    }

    return (
      <div className="container">
        {renderer}
        {
          (autoClaims.includes(this.props.contractData.product_code)
          || gapClaims.includes(this.props.contractData.product_code)
          || lwtClaims.includes(this.props.contractData.product_code)
          || vtaClaims.includes(this.props.contractData.product_code)
          || (CONSTANTS.PRODUCT_CODE_NEW_MAP.tireWheel === this.props.contractData.product_code
              && this.props.contractData.product_name.includes('TCA'))) &&
            <div>
              <div className="row" id="claim-list">
                <Claims
                  claims={ this.props.contractClaims }
                  product_code={ this.props.contractData.product_code }
                  loadContractHistory = { this.props.loadContractHistory }
                />
              </div>
              <div className="row">
                <p className="col-form-label-sm">
                  <strong>
                    Inspection Attachments
                  </strong>
                  <span className="ml-3 cursor-pointer text-primary"
                    id="inspection-attachment-count"
                    onClick={ () => {
                      this.setState({ displayAttachmentListModal: true, isInspectionAttachments: true });
                    } }>
                    { this.state.inspectionAttachments.length }
                  </span>
                </p>
              </div>
              <div className="row">
                <p className="col-form-label-sm">
                  <strong>
                   Attachments
                  </strong>
                  <span className="ml-3 cursor-pointer text-primary"
                    id="attachment-count"
                    onClick={ () => {
                      this.setState({ displayAttachmentListModal: true, isInspectionAttachments: false });
                    } }>
                    { this.state.attachments.length }
                  </span>
                </p>
                <AttachmentListModal displayAttachmentListModal={ this.state.displayAttachmentListModal }
                  closeModal={ () => {
                    this.setState({ displayAttachmentListModal: false, isInspectionAttachments: false });
                  } }
                  attachments={ this.state.isInspectionAttachments ? this.state.inspectionAttachments : this.state.attachments }
                  isContractAttachment={!this.state.isInspectionAttachments && true}
                  isInspectionAttachments = {this.state.isInspectionAttachments}                   
                  contractId={ !this.state.isInspectionAttachments && this.props.id } />
              </div>
            </div>
        }
      </div>
    );
  }

  renderCancellationDetails(contractDetails, financingDetails) {
    const {
      cancelContract,
      reinstateContract,
    } = this.state;

    const isReinstatement = reinstateContract && reinstateContract.reinstate_date;
    if ((contractDetails.status === CONSTANTS.CONTRACT_STATUS_NEW_MAP.Canceled || isReinstatement) && cancelContract) {
      return (
        <>
          <div id="cancellation-detail">
            <h5>Cancellation Details</h5>
            <table className="table table-striped text-wrap no-gutters">
              <tbody>
                <tr className="row">
                  <td className="col-2"><span>Cancel Status</span></td>
                  <td className="col-4"><span><strong>{cancelContract.cancel_status}</strong></span></td>
                </tr>
                <tr className="row">
                  <td className="col-2"><span>Cancel Date</span></td>
                  <td className="col-4">
                    <span><strong>{cancelContract.cancel_date ? moment.utc(cancelContract.cancel_date).format(dateFormat.displayDateFormat) : ""}</strong></span>
                  </td>
                  <td className="col-2"><span>Cancel Mileage</span></td>
                  <td className="col-4">
                    <span><strong>{cancelContract.cancel_mileage ? accounting.formatNumber(cancelContract.cancel_mileage) : ""}</strong></span>
                  </td>
                </tr>
                <tr className="row">
                  <td className="col-2"><span>Cancel Reason</span></td>
                  <td className="col-4"><span><strong>{cancelContract.cancel_reason}</strong></span></td>
                  <td className="col-2"><span>Customer Refund</span></td>
                  <td className="col-4">
                    <span><strong>{cancelContract.customer_refund ? accounting.formatMoney(cancelContract.customer_refund) : ""}</strong></span>
                  </td>
                </tr>
                <tr className="row">
                  <td className="col-2"><span>Cancel Fee</span></td>
                  <td className="col-4">
                    <span><strong>{cancelContract.cancel_fee ? accounting.formatMoney(cancelContract.cancel_fee) : ""}</strong></span>
                  </td>
                  <td className="col-2"><span>Store Refund</span></td>
                  <td className="col-4">
                    <span>
                      <strong>
                        {cancelContract.store_refund ? accounting.formatMoney(cancelContract.store_refund) : ""}
                      </strong>
                    </span>
                  </td>
                </tr>
                <tr className="row">
                  <td className="col-2"><span>Contract Used</span></td>
                  <td className="col-4"><span><strong>{`${cancelContract.cancel_factor_used}%`}</strong></span></td>
                  <td className="col-2"><span>Third Party Remit Refund</span></td>
                  <td className="col-4">
                    <span><strong>{`${accounting.formatMoney(cancelContract.third_party_remit)}`}</strong></span></td>
                </tr>
                <tr className="row">
                  <td className="col-2"><span>Days Used</span></td>
                  <td className="col-4">
                    <span><strong>{`${Number(cancelContract.days_used || 0).toFixed(2)}%`}</strong></span>
                  </td>
                  <td className="col-2"><span>Miles Used</span></td>
                  <td className="col-4">
                    <span><strong>{`${Number(cancelContract.miles_used || 0).toFixed(2)}%`}</strong></span>
                  </td>
                </tr>
                <tr className="row">
                  <td className="col-2">Sales Tax</td>
                  <td className="col-4">
                    <span><strong>{`${accounting.formatMoney(cancelContract.sales_tax)}`}</strong></span></td>

                  <td className="col-2"><span>RSA Refund</span></td>
                  <td className="col-4">
                    <span><strong>{`${accounting.formatMoney(cancelContract.rsa_refund)}`}</strong></span></td>
                </tr>
                <tr className="row">
                  <td className="col-2"><span>Sales Tax Percent</span></td>
                  <td className="col-4"><span><strong>{cancelContract.sales_tax_rate}%</strong></span></td>
                  <td className="col-2"><span>Total Claim Amount Deducted</span></td>
                  <td className="col-4">
                    <span><strong>{accounting.formatMoney(cancelContract.claims_deducted_amount)}</strong></span></td>
                </tr>
                <tr className="row">
                  <td className="col-2"><span>Claims Deducted</span></td>
                  <td className="col-4"><span><strong>{cancelContract.claims_deducted ? `Yes` : `No`}</strong></span></td>
                </tr>
                {
                  financingDetails && financingDetails.payment_type === CONSTANTS.PAYMENT_TYPE_MAP.SPP &&
                <tr className="row">
                  <td className="col-2"><span>SPP Amount Paid</span></td>
                  <td className="col-4">
                    <span><strong>{accounting.formatMoney(cancelContract.spp_customer_paid)}</strong></span>
                  </td>
                  <td className="col-2"><span>SPP Balance</span></td>
                  <td className="col-4">
                    <span><strong>{accounting.formatMoney(cancelContract.spp_balance)}</strong></span>
                  </td>
                </tr>
                }
                {
                  financingDetails && financingDetails.payment_type === CONSTANTS.PAYMENT_TYPE_MAP.SPP &&
                <tr className="row">
                  <td className="col-2"><span>Adjusted Customer Refund</span></td>
                  <td className="col-4">
                    <span><strong>{accounting.formatMoney(cancelContract.adjusted_customer_refund)}</strong></span></td>
                  <td className="col-2"><span>Store Chargeback</span></td>
                  <td className="col-4">
                    <span><strong>{accounting.formatMoney(cancelContract.store_chargeback)}</strong></span></td>
                </tr>
                }
                {
                  financingDetails && financingDetails.payment_type === CONSTANTS.PAYMENT_TYPE_MAP.SPP &&
                <tr className="row">
                  <td className="col-2"><span>SPP Refund</span></td>
                  <td className="col-4">
                    <span><strong>{accounting.formatMoney(cancelContract.spp_refund)}</strong></span>
                  </td>
                </tr>
                }
                <tr className="row">
                  <td className="col-2"><span>Invoice Date</span></td>
                  <td className="col-4">
                    <span><strong>{cancelContract.invoiced_at ? moment(cancelContract.invoiced_at).format(dateFormat.displayDateFormat) : ""}</strong></span>
                  </td>
                  <td className="col-2"><span>Invoice Number</span></td>
                  <td className="col-4"><span><strong>{cancelContract.invoice_number}</strong></span></td>
                </tr>
                <tr className="row">
                  <td className="col-2"><span>Refund Payable To</span></td>
                  <td className="col-4"><span><strong>{cancelContract.payee_type}</strong></span></td>
                  <td className="col-2"><span>Payee Name</span></td>
                  <td className="col-4"><span><strong>{cancelContract.payee_name}</strong></span></td>
                </tr>
                <tr className="row">
                  <td className="col-2"><span>Payee Attention To</span></td>
                  <td className="col-4"><span><strong>{cancelContract.payee_attention_to}</strong></span></td>
                </tr>
                <tr className="row">
                  <td className="col-2"><span>Address</span></td>
                  <td className="col-4"><span><strong>{cancelContract.payee_address}</strong></span></td>
                  <td className="col-2"><span>City</span></td>
                  <td className="col-4"><span><strong>{cancelContract.payee_city}</strong></span></td>
                </tr>
                <tr className="row">
                  <td className="col-2"><span>State</span></td>
                  <td className="col-4"><span><strong>{cancelContract.payee_state}</strong></span></td>
                  <td className="col-2"><span>Zip</span></td>
                  <td className="col-4"><span><strong>{cancelContract.payee_postal_code}</strong></span></td>
                </tr>
              </tbody>
            </table>
          </div>
          {
            (cancelContract.payee_type === 'Lender' || cancelContract.payee_type === 'Customer') && (
              <div id="cancellation-detail">
                <h5>Cancellation Payment Details</h5>
                <table className="table table-striped text-wrap no-gutters">
                  <tbody>
                    <tr className="row">
                      <td className="col-2"><span>Check Type</span></td>
                      <td className="col-4">
                        <span><strong>{cancelContract.is_electronic_check ? 'Electronic Check' : 'Paper Check'}</strong></span>
                      </td>
                      <td className="col-2"><span>Email Address</span></td>
                      <td className="col-4">
                        <span><strong>{cancelContract.email}</strong></span>
                      </td>
                    </tr>
                    {
                      (cancelContract.cancel_status === 'Cancel Complete' || cancelContract.cancel_status === 'Bill Not Issued'
                      || cancelContract.check_number) && (
                        <>
                          <tr className="row">
                            <td className="col-2"><span>Bill Number</span></td>
                            <td className="col-4"><span><strong>{cancelContract.bill_number}</strong></span></td>
                          </tr>
                          <tr className="row">
                            <td className="col-2"><span>Paid Date</span></td>
                            <td className="col-4">
                              <span><strong>{cancelContract.paid_date}</strong></span></td>
                          </tr>
                          <tr className="row">
                            <td className="col-2"><span>Check Number</span></td>
                            <td className="col-4"><span><strong>{cancelContract.check_number}</strong></span></td>
                            <td className="col-2"><span>Check Amount</span></td>
                            <td className="col-4">
                              <span><strong>{accounting.formatMoney(cancelContract.check_amount)}</strong></span>
                            </td>
                          </tr>
                          {
                            cancelContract.manual_update_notes.length > 0 &&
                          <tr className="row">
                            <td className="col-2"><span>Manual Update Notes</span></td>
                            <td className="col-4"><span><strong>{cancelContract.manual_update_notes}</strong></span></td>
                          </tr>
                          }
                        </>
                      )
                    }
                  </tbody>
                </table>
              </div>
            )
          }
        </>
      );
    }
  }

  renderReInstateDetails(contractDetails) {
    const {
      reinstateContract,
    } = this.state;
    if (reinstateContract && reinstateContract.reinstate_date) {
      return (
        <div id="reinstate-detail">
          <h5>Reinstate Details</h5>
          <table className="table table-striped text-wrap no-gutters">
            <tbody>
              <tr className="row">
                <td className="col-2"><span>Reinstate Date</span></td>
                <td className="col-4"><span><strong>{reinstateContract.reinstate_date ? moment.utc(reinstateContract.reinstate_date).local(false).format(dateFormat.displayDateFormat): ""}</strong></span></td>
              </tr>
              <tr className="row">
                <td className="col-2"><span>Invoice Date</span></td>
                <td className="col-4"><span><strong>{reinstateContract.invoice_date ? moment(reinstateContract.invoice_date).format(dateFormat.displayDateFormat): ""}</strong></span></td>
                <td className="col-2"><span>Invoice Number</span></td>
                <td className="col-4"><span><strong>{reinstateContract.invoice_number}</strong></span></td>
              </tr>
            </tbody>
          </table>
        </div>
      );
    }
  }

  renderOptionsAndSurcharges(option, surcharge){
    const options = option.options;
    let optionIndex = option.optionIndex;
    const optionLength = option.optionLength;
    const surcharges = surcharge.surcharges;
    let surchargeIndex = surcharge.surchargeIndex;
    const surchargeLength = surcharge.surchargeLength;
    const length = (optionLength - optionIndex) + (surchargeLength - surchargeIndex);
    let rows = [];

    for(let index = 0; index < length; index++){
      rows.push(
        <tr className="row" key={index}>
          <td className="col-2"></td>
          <td className="col-4"></td>
          <td className="col-4">
            <span>
              {
                optionIndex < optionLength ?
                  options[optionIndex].name || options[optionIndex].code :
                  surchargeIndex < surchargeLength ?
                    surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
              }
            </span>
          </td>
          <td className="col-2">
            <span>
              <strong>
                {
                  optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                    "Yes" : ""
                }
              </strong>
            </span>
          </td>
        </tr>
      );
    }
    return rows;
  }

  renderContractSCTable(contractDetails) {
    const options = contractDetails.options;
    const surcharges = contractDetails.surcharges;
    let optionIndex = 0;
    let surchargeIndex = 0;

    const optionLength = options ? options.length : 0;
    const surchargeLength = surcharges ? surcharges.length : 0;
    return (
      <table className="table table-striped text-nowrap no-gutters">
        <tbody>
          <tr className="row">
            <td className="col-2"><span>Contract Number</span></td>
            <td className="col-4"><span><strong>{contractDetails.original_code || contractDetails.code}</strong>&nbsp;&nbsp;&nbsp;
              {this.getContractStatusBlock(contractDetails)}&nbsp;&nbsp;{this.getContractTransferBadge(contractDetails)}
              {contractDetails.transfers && contractDetails.transfers.length > 0 ?  `&nbsp;&nbsp;`: ''}
              {this.getContractReInstateBadge(contractDetails)}</span></td>
            <td className="col-6"><span><strong>
              {
                (contractDetails.options && contractDetails.options.length) || surchargeLength ?
                  "Included Options" : ""
              }
            </strong></span></td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Issuing Dealer</span></td>
            <td className="col-4"><span><strong>{contractDetails.issuing_dealer}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Issuing Dealer #</span></td>
            <td className="col-4"><span><strong>{contractDetails.issuing_dealer_number}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Plan</span></td>
            <td className="col-4"><span><strong>{contractDetails.plan_name}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Product</span></td>
            <td className="col-4"><span><strong>{contractDetails.product_variant_display_name}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Effective Date</span></td>
            <td className="col-4"><span><strong>{contractDetails.effective_date ? moment.utc(contractDetails.effective_date).format(dateFormat.displayDateFormat) : ""}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Expiration Date</span></td>
            <td className="col-4"><span><strong>{contractDetails.expiration_date ? moment.utc(contractDetails.expiration_date).format(dateFormat.displayDateFormat) : ""}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Effective Mileage</span></td>
            <td className="col-4"><span><strong>{accounting.formatNumber(contractDetails.effective_mileage)}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Expiration Mileage</span></td>
            <td className="col-4"><span><strong>{contractDetails.expiration_mileage ? accounting.formatNumber(contractDetails.expiration_mileage) : ""}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Price</span></td>
            <td className="col-4"><span><strong>{contractDetails.price ? accounting.formatMoney(contractDetails.price) : ""}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Cost</span></td>
            <td className="col-4">
              <span>
                <strong>
                  {accounting.formatMoney(contractDetails.cost)}
                </strong>
              </span>
            </td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Invoice Date</span></td>
            <td className="col-4"><span><strong>{contractDetails.invoiced_at ? moment(contractDetails.invoiced_at).format(dateFormat.displayDateFormat) : ""}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Invoice Number</span></td>
            <td className="col-4"><span><strong>{contractDetails.invoice_number}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Book Date</span></td>
            <td className="col-4"><span><strong>{contractDetails.book_date ? moment.utc(contractDetails.book_date).format(dateFormat.displayDateFormat) : ""}</strong></span></td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Third Party Remit</span></td>
            <td className="col-4"><span><strong>{contractDetails.remit_amount ? accounting.formatMoney(contractDetails.remit_amount) : ""}</strong></span></td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Created Using</span></td>
            <td className="col-4"><span><strong>{contractDetails.source}</strong></span></td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Last Updated At</span></td>
            <td className="col-4"><span><strong>{contractDetails.updated_at ? moment.utc(contractDetails.updated_at).format(dateFormat.displayDateFormat) : ""}</strong></span></td>
          </tr>
        </tbody>
      </table>
    );
  }

  renderContractMCTable(contractDetails) {
    const options = contractDetails.options;
    const surcharges = contractDetails.surcharges;
    let optionIndex = 0;
    let surchargeIndex = 0;

    const optionLength = options ? options.length : 0;
    const surchargeLength = surcharges ? surcharges.length : 0;

    return (
      <table className="table table-striped text-nowrap no-gutters">
        <tbody>
          <tr className="row">
            <td className="col-2"><span>Contract Number</span></td>
            <td className="col-4"><span><strong>{contractDetails.original_code || contractDetails.code}</strong>&nbsp;&nbsp;&nbsp;
              {this.getContractStatusBlock(contractDetails)}&nbsp;&nbsp;{this.getContractTransferBadge(contractDetails)}
              {contractDetails.transfers && contractDetails.transfers.length > 0 ?  `&nbsp;&nbsp;`: ''}
              {this.getContractReInstateBadge(contractDetails)}</span></td>
            <td className="col-6"><span><strong>Included Options</strong></span></td>{/*Showing everytime as Major Coupons and Minor Coupons*/}
          </tr>
          <tr className="row">
            <td className="col-2"><span>Issuing Dealer</span></td>
            <td className="col-4"><span><strong>{contractDetails.issuing_dealer}</strong></span></td>
            <td className="col-2"><span>Major Coupons</span></td>
            <td className="col-4"><span><strong>{contractDetails.maintenance_visits}</strong></span></td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Issuing Dealer #</span></td>
            <td className="col-4"><span><strong>{contractDetails.issuing_dealer_number}</strong></span></td>
            <td className="col-2"><span>Minor Coupons</span></td>
            <td className="col-4"><span><strong>{contractDetails.minor_coupons_purchased || 0}</strong></span></td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Plan</span></td>
            <td className="col-4"><span><strong>{contractDetails.plan_name}</strong></span></td>
            <td className="col-2"><span>Product</span></td>
            <td className="col-4"><span><strong>{contractDetails.product_variant_display_name}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Effective Date</span></td>
            <td className="col-4"><span><strong>{contractDetails.effective_date ? moment.utc(contractDetails.effective_date).format(dateFormat.displayDateFormat) : ""}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Expiration Date</span></td>
            <td className="col-4"><span><strong>{contractDetails.expiration_date ? moment.utc(contractDetails.expiration_date).format(dateFormat.displayDateFormat) : ""}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Effective Mileage</span></td>
            <td className="col-4"><span><strong>{accounting.formatNumber(contractDetails.effective_mileage)}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Expiration Mileage</span></td>
            <td className="col-4"><span><strong>{contractDetails.expiration_mileage ? accounting.formatNumber(contractDetails.expiration_mileage) : ""}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Price</span></td>
            <td className="col-4"><span><strong>{contractDetails.price ? accounting.formatMoney(contractDetails.price) : ""}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Cost</span></td>
            <td className="col-4">
              <span>
                <strong>
                  {contractDetails.cost ? accounting.formatMoney(contractDetails.cost) : ""}
                </strong>
              </span>
            </td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Invoice Date</span></td>
            <td className="col-4"><span><strong>{contractDetails.invoiced_at ? moment(contractDetails.invoiced_at).format(dateFormat.displayDateFormat) : ""}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Invoice Number</span></td>
            <td className="col-4"><span><strong>{contractDetails.invoice_number}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Book Date</span></td>
            <td className="col-4"><span><strong>{contractDetails.book_date ? moment.utc(contractDetails.book_date).format(dateFormat.displayDateFormat) : ""}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Third Party Remit</span></td>
            <td className="col-4"><span><strong>{contractDetails.remit_amount ? accounting.formatMoney(contractDetails.remit_amount) : ""}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Last Updated At</span></td>
            <td className="col-4"><span><strong>{contractDetails.updated_at ? moment.utc(contractDetails.updated_at).format(dateFormat.displayDateFormat) : ""}</strong></span></td>
          </tr>
          {this.renderOptionsAndSurcharges({options, optionIndex, optionLength},{surcharges, surchargeIndex, surchargeLength})}
        </tbody>
      </table>
    );
  }

  renderContractCPTable(contractDetails) {
    const options = contractDetails.options;
    const surcharges = contractDetails.surcharges;
    let optionIndex = 0;
    let surchargeIndex = 0;

    const optionLength = options ? options.length : 0;
    const surchargeLength = surcharges ? surcharges.length : 0;

    return (
      <table className="table table-striped text-nowrap no-gutters">
        <tbody>
          <tr className="row">
            <td className="col-2"><span>Contract Number</span></td>
            <td className="col-4"><span><strong>{contractDetails.original_code || contractDetails.code}</strong>&nbsp;&nbsp;&nbsp;
              {this.getContractStatusBlock(contractDetails)}&nbsp;&nbsp;{this.getContractTransferBadge(contractDetails)}
              {contractDetails.transfers && contractDetails.transfers.length > 0 ?  `&nbsp;&nbsp;`: ''}
              {this.getContractReInstateBadge(contractDetails)}</span></td>
            <td className="col-6"><span><strong>Included Options</strong></span></td>{/*Showing every time for paint, fabric etc*/}
          </tr>
          <tr className="row">
            <td className="col-2"><span>Issuing Dealer</span></td>
            <td className="col-4"><span><strong>{contractDetails.issuing_dealer}</strong></span></td>
            <td className="col-2"><span>Paint</span></td>
            <td className="col-4"><span><strong>{contractDetails.paint !== null ? (contractDetails.paint ? "Yes" : "No" ): ""}</strong></span></td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Issuing Dealer #</span></td>
            <td className="col-4"><span><strong>{contractDetails.issuing_dealer_number}</strong></span></td>
            <td className="col-2"><span>Fabric</span></td>
            <td className="col-4"><span><strong>{contractDetails.fabric !== null ? (contractDetails.fabric ? "Yes" : "No" ): ""}</strong></span></td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Effective Date</span></td>
            <td className="col-4"><span><strong>{contractDetails.effective_date ? moment.utc(contractDetails.effective_date).format(dateFormat.displayDateFormat) : ""}</strong></span></td>
            <td className="col-2"><span>Leather/Vinyl</span></td>
            <td className="col-4"><span><strong>{contractDetails.leather_or_vinyl !== null ? (contractDetails.leather_or_vinyl !== null ? "Yes" : "No") : ""}</strong></span></td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Expiration Date</span></td>
            <td className="col-4"><span><strong>{contractDetails.expiration_date ? moment.utc(contractDetails.expiration_date).format(dateFormat.displayDateFormat) : ""}</strong></span></td>
            <td className="col-2"><span>Dent and Ding</span></td>
            <td className="col-4"><span><strong>{contractDetails.dent_and_ding !== null ? (contractDetails.dent_and_ding !== null ? "Yes" : "No") : ""}</strong></span></td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Price</span></td>
            <td className="col-4"><span><strong>{contractDetails.price ? accounting.formatMoney(contractDetails.price) : ""}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Cost</span></td>
            <td className="col-4">
              <span>
                <strong>
                  {
                    contractDetails.cost ? accounting.formatMoney(contractDetails.cost) : ""
                  }
                </strong>
              </span>
            </td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Invoice Date</span></td>
            <td className="col-4"><span><strong>{contractDetails.invoiced_at ? moment(contractDetails.invoiced_at).format(dateFormat.displayDateFormat) : ""}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Invoice Number</span></td>
            <td className="col-4"><span><strong>{contractDetails.invoice_number}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Book Date</span></td>
            <td className="col-4"><span><strong>{contractDetails.book_date ? moment.utc(contractDetails.book_date).format(dateFormat.displayDateFormat) : ""}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Third Party Remit</span></td>
            <td className="col-4"><span><strong>{contractDetails.remit_amount ? accounting.formatMoney(contractDetails.remit_amount) : ""}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Last Updated At</span></td>
            <td className="col-4"><span><strong>{contractDetails.updated_at ? moment.utc(contractDetails.updated_at).format(dateFormat.displayDateFormat) : ""}</strong></span></td>
          </tr>
          {this.renderOptionsAndSurcharges({options, optionIndex, optionLength},{surcharges, surchargeIndex, surchargeLength})}
        </tbody>
      </table>
    );
  }

  renderContractGPTable(contractDetails) {
    const options = contractDetails.options;
    const surcharges = contractDetails.surcharges;
    let optionIndex = 0;
    let surchargeIndex = 0;

    const optionLength = options ? options.length : 0;
    const surchargeLength = surcharges ? surcharges.length : 0;

    return (
      <table className="table table-striped text-nowrap no-gutters">
        <tbody>
          <tr className="row">
            <td className="col-2"><span>Contract Number</span></td>
            <td className="col-4"><span><strong>{contractDetails.original_code || contractDetails.code}</strong>&nbsp;&nbsp;&nbsp;
              {this.getContractStatusBlock(contractDetails)}&nbsp;&nbsp;{this.getContractTransferBadge(contractDetails)}
              {contractDetails.transfers && contractDetails.transfers.length > 0 ?  `&nbsp;&nbsp;`: ''}
              {this.getContractReInstateBadge(contractDetails)}</span></td>
            <td className="col-6"><span><strong>
              {
                (contractDetails.options && contractDetails.options.length) || surchargeLength ?
                  "Included Options" : ""
              }
            </strong></span></td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Issuing Dealer</span></td>
            <td className="col-4"><span><strong>{contractDetails.issuing_dealer}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Issuing Dealer #</span></td>
            <td className="col-4"><span><strong>{contractDetails.issuing_dealer_number}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Effective Date</span></td>
            <td className="col-4"><span><strong>{contractDetails.effective_date ? moment.utc(contractDetails.effective_date).format(dateFormat.displayDateFormat) : ""}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Expiration Date</span></td>
            <td className="col-4"><span><strong>{contractDetails.expiration_date ? moment.utc(contractDetails.expiration_date).format(dateFormat.displayDateFormat) : ""}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Plan</span></td>
            <td className="col-4"><span><strong>{contractDetails.plan_name}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Price</span></td>
            <td className="col-4"><span><strong>{contractDetails.price ? accounting.formatMoney(contractDetails.price) : ""}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Cost</span></td>
            <td className="col-4">
              <span>
                <strong>
                  {
                    contractDetails.cost ? accounting.formatMoney(contractDetails.cost) : ""
                  }
                </strong>
              </span>
            </td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Invoice Date</span></td>
            <td className="col-4"><span><strong>{contractDetails.invoiced_at ? moment(contractDetails.invoiced_at).format(dateFormat.displayDateFormat) : ""}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Invoice Number</span></td>
            <td className="col-4"><span><strong>{contractDetails.invoice_number}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Book Date</span></td>
            <td className="col-4"><span><strong>{contractDetails.book_date ? moment.utc(contractDetails.book_date).format(dateFormat.displayDateFormat) : ""}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Third Party Remit</span></td>
            <td className="col-4"><span><strong>{contractDetails.remit_amount ? accounting.formatMoney(contractDetails.remit_amount) : ""}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Last Updated At</span></td>
            <td className="col-4"><span><strong>{contractDetails.updated_at ? moment.utc(contractDetails.updated_at).format(dateFormat.displayDateFormat) : ""}</strong></span></td>
          </tr>
          {this.renderOptionsAndSurcharges({options, optionIndex, optionLength},{surcharges, surchargeIndex, surchargeLength})}
        </tbody>
      </table>
    );
  }

  renderContractTTAndTGTable(contractDetails) {
    const options = contractDetails.options;
    const surcharges = contractDetails.surcharges;
    let optionIndex = 0;
    let surchargeIndex = 0;

    const optionLength = options ? options.length : 0;
    const surchargeLength = surcharges ? surcharges.length : 0;

    return (
      <table className="table table-striped text-nowrap no-gutters">
        <tbody>
          <tr className="row">
            <td className="col-2"><span>Contract Number</span></td>
            <td className="col-4"><span><strong>{contractDetails.original_code || contractDetails.code}</strong>&nbsp;&nbsp;&nbsp;
              {this.getContractStatusBlock(contractDetails)}&nbsp;&nbsp;{this.getContractTransferBadge(contractDetails)}
              {contractDetails.transfers && contractDetails.transfers.length > 0 ?  `&nbsp;&nbsp;`: ''}
              {this.getContractReInstateBadge(contractDetails)}</span></td>
            <td className="col-6"><span><strong>
              {
                (contractDetails.options && contractDetails.options.length) || surchargeLength ?
                  "Included Options" : ""
              }
            </strong></span></td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Issuing Dealer</span></td>
            <td className="col-4"><span><strong>{contractDetails.issuing_dealer}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Issuing Dealer #</span></td>
            <td className="col-4"><span><strong>{contractDetails.issuing_dealer_number}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Effective Date</span></td>
            <td className="col-4"><span><strong>{contractDetails.effective_date ? moment.utc(contractDetails.effective_date).format(dateFormat.displayDateFormat) : ""}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Expiration Date</span></td>
            <td className="col-4"><span><strong>{contractDetails.expiration_date ? moment.utc(contractDetails.expiration_date).format(dateFormat.displayDateFormat) : ""}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Price</span></td>
            <td className="col-4"><span><strong>{contractDetails.price ? accounting.formatMoney(contractDetails.price) : ""}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Cost</span></td>
            <td className="col-4">
              <span>
                <strong>
                  {
                    contractDetails.cost ? accounting.formatMoney(contractDetails.cost) : ""
                  }
                </strong>
              </span>
            </td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          {this.renderOptionsAndSurcharges({options, optionIndex, optionLength},{surcharges, surchargeIndex, surchargeLength})}
        </tbody>
      </table>
    );
  }

  renderVTANumber(contractDetails){
    
    if(contractDetails.product_type_code === vtaClaims){
      return (
        <tr className="row">
          <td className="col-2"><span>VTA Number</span></td>
          <td className="col-4"><span><strong>{contractDetails.vehicle_theft_number}</strong></span></td>
        </tr>
      );
    }
    else{
      return (
        <div></div>
      );
    }    
  }

  renderContractTable(contractDetails) {
    const options = contractDetails.options;
    const surcharges = contractDetails.surcharges;
    let optionIndex = 0;
    let surchargeIndex = 0;

    const optionLength = options ? options.length : 0;
    const surchargeLength = surcharges ? surcharges.length : 0;

    return (
      <table className="table table-striped text-nowrap no-gutters">
        <tbody>
          <tr className="row">
            <td className="col-2"><span>Contract Number</span></td>
            <td className="col-4"><span><strong>{contractDetails.original_code || contractDetails.code}</strong>&nbsp;&nbsp;&nbsp;
              {this.getContractStatusBlock(contractDetails)}&nbsp;&nbsp;{this.getContractTransferBadge(contractDetails)}
              {contractDetails.transfers && contractDetails.transfers.length > 0 ?  `&nbsp;&nbsp;`: ''}
              {this.getContractReInstateBadge(contractDetails)}</span></td>
            <td className="col-6"><span><strong>
              {
                (contractDetails.options && contractDetails.options.length) || surchargeLength ?
                  "Included Options" : ""
              }
            </strong></span></td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Issuing Dealer</span></td>
            <td className="col-4"><span><strong>{contractDetails.issuing_dealer}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Issuing Dealer #</span></td>
            <td className="col-4"><span><strong>{contractDetails.issuing_dealer_number}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          {this.renderVTANumber(contractDetails)}
          <tr className="row">
            <td className="col-2"><span>Effective Date</span></td>
            <td className="col-4"><span><strong>{contractDetails.effective_date ? moment.utc(contractDetails.effective_date).format(dateFormat.displayDateFormat) : ""}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Expiration Date</span></td>
            <td className="col-4"><span><strong>{contractDetails.expiration_date ? moment.utc(contractDetails.expiration_date).format(dateFormat.displayDateFormat) : ""}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Price</span></td>
            <td className="col-4"><span><strong>{contractDetails.price ? accounting.formatMoney(contractDetails.price) : ""}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Cost</span></td>
            <td className="col-4">
              <span>
                <strong>
                  {
                    contractDetails.cost ? accounting.formatMoney(contractDetails.cost) : ""
                  }
                </strong>
              </span>
            </td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Invoice Date</span></td>
            <td className="col-4"><span><strong>{contractDetails.invoiced_at ? moment(contractDetails.invoiced_at).format(dateFormat.displayDateFormat) : ""}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Invoice Number</span></td>
            <td className="col-4"><span><strong>{contractDetails.invoice_number}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Book Date</span></td>
            <td className="col-4"><span><strong>{contractDetails.book_date ? moment.utc(contractDetails.book_date).format(dateFormat.displayDateFormat) : ""}</strong></span></td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Third Party Remit</span></td>
            <td className="col-4">
              <span><strong>{contractDetails.remit_amount ? accounting.formatMoney(contractDetails.remit_amount) : ""}</strong></span>
            </td>
            <td className="col-4">
              <span>
                {
                  optionIndex < optionLength ?
                    options[optionIndex].name || options[optionIndex].code :
                    surchargeIndex < surchargeLength ?
                      surcharges[surchargeIndex].name || surcharges[surchargeIndex].code : ""
                }
              </span>
            </td>
            <td className="col-2">
              <span>
                <strong>
                  {
                    optionIndex++ < optionLength || surchargeIndex++ < surchargeLength ?
                      "Yes" : ""
                  }
                </strong>
              </span>
            </td>
          </tr>
          <tr className="row">
            <td className="col-2"><span>Last Updated At</span></td>
            <td className="col-4"><span><strong>{contractDetails.updated_at ? moment.utc(contractDetails.updated_at).format(dateFormat.displayDateFormat) : ""}</strong></span></td>
          </tr>
          {this.renderOptionsAndSurcharges({options, optionIndex, optionLength},{surcharges, surchargeIndex, surchargeLength})}
        </tbody>
      </table>
    );
  }

  renderVehicleTabContent(vehicleDetails) {
    let newOrUsedVehicle = "";
    if (vehicleDetails.is_new) {
      newOrUsedVehicle = "New";
    } else {
      newOrUsedVehicle = "Used";
    }
    return (
      <div className="container py-2">
        <div className="row">
          <div className="col-12">
            <table className="table table-striped text-nowrap no-gutters">
              <tbody>
                <tr className="row">
                  <td className="col-2"><span>VIN</span></td>
                  <td className="col-4"><span><strong>{vehicleDetails.vin}</strong></span></td>
                  <td className="col-2"><span>Condition</span></td>
                  <td className="col-4"><span><strong>{newOrUsedVehicle}</strong></span></td>
                </tr>
                <tr className="row">
                  <td className="col-2"><span>Year</span></td>
                  <td className="col-4"><span><strong>{vehicleDetails.year}</strong></span></td>
                  <td className="col-2"><span>Beginning Miles</span></td>
                  <td className="col-4"><span><strong>{vehicleDetails.odometer !== "" ? accounting.formatNumber(vehicleDetails.odometer) : ""}</strong></span></td>
                </tr>
                <tr className="row">
                  <td className="col-2"><span>Make</span></td>
                  <td className="col-4"><span><strong>{vehicleDetails.make}</strong></span></td>
                </tr>
                <tr className="row">
                  <td className="col-2"><span>Model</span></td>
                  <td className="col-4"><span><strong>{vehicleDetails.model}</strong></span></td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    );
  }

  renderFinancingTabContent(contractDetails) {

    const financingDetails = contractDetails.financing_details;
    let lender_address = financingDetails.lender_address ? financingDetails.lender_address : "";
    if (financingDetails.lender_zip) {
      lender_address += ` ${financingDetails.lender_zip}`;
    }
    return (
      <div className="container py-2">
        <div className="row">
          <div className="col-12">
            <table className="table table-striped text-nowrap no-gutters">
              <tbody>
                <tr className="row">
                  <td className="col-2"><span>Deal Type</span></td>
                  <td className="col-4"><span><strong>{financingDetails.sale_type}</strong></span></td>
                  <td className="col-2"><span>Financed By</span></td>
                  <td className="col-4"><span><strong>{financingDetails.lender_name}</strong></span></td>
                </tr>
                <tr className="row">
                  <td className="col-2"><span>Deal #</span></td>
                  <td className="col-4"><span><strong>{this.props.contractData.deal_number}</strong></span></td>
                  <td className="col-2"><span>Address</span></td>
                  <td className="col-4"><span><strong>{lender_address}</strong></span></td>
                </tr>
                <tr className="row">
                  <td className="col-2"><span>Date</span></td>
                  <td className="col-4"><span><strong>{financingDetails.contract_date ? moment.utc(financingDetails.contract_date).format(dateFormat.displayDateFormat) : ""}</strong></span></td>
                  <td className="col-2"><span>Phone/Fax</span></td>
                  <td className="col-4"><span><strong>{financingDetails.lender_phone}</strong></span></td>
                </tr>
                <tr className="row">
                  <td className="col-2"><span>Status</span></td>
                  <td className="col-4"><span><strong>{contractDetails.contract.status}</strong></span></td>
                  <td className="col-2"><span>MSRP</span></td>
                  <td className="col-4"><span><strong>{financingDetails.msrp ? accounting.formatMoney(financingDetails.msrp) : ""}</strong></span></td>
                </tr>
                <tr className="row">
                  <td className="col-2"><span>Payment Type</span></td>
                  <td className="col-4"><span><strong>{financingDetails.payment_type}</strong></span></td>
                  <td className="col-2"><span>Vehicle Price</span></td>
                  <td className="col-4"><span><strong>{financingDetails.vehicle_price ? accounting.formatMoney(financingDetails.vehicle_price) : ""}</strong></span></td>
                </tr>
                <tr className="row">
                  <td className="col-2"><span>Store #</span></td>
                  <td className="col-4"><span><strong>{this.props.contractData.store_code}</strong></span></td>
                  <td className="col-2"><span>Financed</span></td>
                  <td className="col-4"><span><strong>{financingDetails.finance_amount ? accounting.formatMoney(financingDetails.finance_amount) : ""}</strong></span></td>
                </tr>
                <tr className="row">
                  <td className="col-2"><span>Salesman</span></td>
                  <td className="col-4"><span><strong>{financingDetails.sales_man}</strong></span></td>
                  <td className="col-2"><span>First Payment</span></td>
                  <td className="col-4"><span><strong>{financingDetails.first_payment_date ? moment.utc(financingDetails.first_payment_date).format(dateFormat.displayDateFormat) : ""}</strong></span></td>
                </tr>
                <tr className="row">
                  <td className="col-2"><span>Term</span></td>
                  <td className="col-4"><span><strong>{financingDetails.term}</strong></span></td>
                  <td className="col-2"><span>Payment</span></td>
                  <td className="col-4"><span><strong>{financingDetails.finance_monthly_payment ? accounting.formatMoney(financingDetails.finance_monthly_payment) : ""}</strong></span></td>
                </tr>
                <tr className="row">
                  <td className="col-2"><span>Buy Rate</span></td>
                  <td className="col-4"><span><strong>{`${financingDetails.finance_apr ? `${financingDetails.finance_apr}%` : ""}`}</strong></span></td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    );
  }

  renderCoOwner(customerData) {
    return (
      <div>
        <h5>Co-Buyer</h5>
        <div className="row">{this.renderCustomerTable(customerData, CO_OWNER)}</div>
      </div>
    );
  }

  renderCustomerTabContent(owner, coOwner) {
    return (
      <div className="container py-2">
        <div className="row">{this.renderCustomerTable(owner, OWNER)}</div>
        {
          this.renderCoOwner(coOwner)
        }
      </div>
    );
  }

  onKeyPress = (e) => {
    if (e.key === 'Enter') {
      e.preventDefault();      
    }
  }

  renderCustomerTable (customer, prefix) {
    if(this.isCustomerEditable()){
      const required = prefix === OWNER;

      return (
        <div className="col-12">
          <table className="table table-striped text-nowrap no-gutters">
            <tbody>
              <tr className="row">
                <td className="col-2"><span>First Name</span></td>
                <td className="col-4">
                  <span>
                    <strong>
                      {
                        prefix === CO_OWNER ?
                          (<input id={`${prefix}_first_name`}
                            className="form-control form-control-sm"
                            value={customer.first_name}
                            onKeyPress={this.onKeyPress}
                            onChange={this.onCursorValueChange.bind(null, prefix,'first_name')}/>) :
                          (customer.first_name)
                      }
                    </strong>
                  </span>
                </td>
                <td className="col-2"><span>Phone</span></td>
                <td className="col-4">
                  <strong>
                    <input id={`${prefix}_phone`}
                      className="form-control form-control-sm"
                      value={prefix === CO_OWNER ? customer.home_phone : customer.phone}
                      required={required}
                      onKeyPress={this.onKeyPress}
                      onChange={this.onCursorValueChange.bind(null, prefix, prefix === CO_OWNER ? 'home_phone' : 'phone')}/>
                  </strong>
                </td>
              </tr>
              <tr className="row">
                <td className="col-2"><span>Last Name</span></td>
                <td className="col-4">
                  <span>
                    <strong>
                      {
                        prefix === CO_OWNER ?
                          (<input id={`${prefix}_last_name`}
                            className="form-control form-control-sm"
                            value={customer.last_name}
                            required={required}
                            onKeyPress={this.onKeyPress}
                            onChange={this.onCursorValueChange.bind(null, prefix, 'last_name')}/>) :
                          (customer.last_name)
                      }
                    </strong>
                  </span>
                </td>
                <td className="col-2"><span>Alt Phone</span></td>
                <td className="col-4"><strong>
                  {
                    prefix === CO_OWNER ?
                      (<input id={`${prefix}_alt_phone`}
                        className="form-control form-control-sm"
                        value={customer.alt_phone}
                        onKeyPress={this.onKeyPress}
                        onChange={this.onCursorValueChange.bind(null, prefix, 'alt_phone')}/>):
                      (customer.alt_phone)
                  }
                </strong>
                </td>
              </tr>
              { prefix !== CO_OWNER &&
                <tr className="row">
                  <td className="col-2"><span>Business Name</span></td>
                  <td className="col-4">
                    <span><strong>{customer.business_name}</strong></span>
                  </td>
                </tr>
              }
              <tr className="row">
                <td className="col-2"><span>Address</span></td>
                <td className="col-4">
                  <strong>
                    <input id={`${prefix}_address`}
                      className="form-control form-control-sm"
                      value={customer.address}
                      required={required}
                      onKeyPress={this.onKeyPress}
                      onChange={this.onCursorValueChange.bind(null, prefix, 'address')}/>
                  </strong>
                </td>
                <td className="col-2"><span>Email</span></td>
                <td className="col-4">
                  <strong>
                    <input id={`${prefix}_email`}
                      type="email"
                      className="form-control form-control-sm"
                      value={customer.email}
                      onKeyPress={this.onKeyPress}
                      onChange={this.onCursorValueChange.bind(null, prefix, 'email')}/>
                  </strong>
                </td>
              </tr>
              <tr className="row">
                <td className="col-2"><span>City</span></td>
                <td className="col-4">
                  <strong>
                    <input id={`${prefix}_city`}
                      className="form-control form-control-sm"
                      value={customer.city}
                      required={required}
                      onKeyPress={this.onKeyPress}
                      onChange={this.onCursorValueChange.bind(null, prefix, 'city')}/>
                  </strong>
                </td>
                <td className="col-2"><span>Best Contact</span></td>
                <td className="col-4"><strong>{customer.best_contact_method}</strong></td>
              </tr>
              <tr className="row">
                <td className="col-2"><span>State</span></td>
                <td className="col-4">
                  <strong>
                    <StateList value={customer.state_code}
                      customClassName="form-control form-control-sm"
                      onChange={this.onCursorChange.bind(null, prefix, 'state_code')}/>
                  </strong>
                </td>
              </tr>
              <tr className="row">
                <td className="col-2"><span>Zip</span></td>
                <td className="col-4">
                  <strong>
                    <input  id={`${prefix}_postal_code`}
                      className="form-control form-control-sm"
                      value={customer.postal_code}
                      required={required}
                      onKeyPress={this.onKeyPress}
                      onChange={this.onCursorValueChange.bind(null, prefix, 'postal_code')}/>
                  </strong>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      );
    } else {
      return (
        <div className="col-12">
          <table className="table table-striped text-nowrap no-gutters">
            <tbody>
              <tr className="row">
                <td className="col-2"><span>First Name</span></td>
                <td className="col-4"><span><strong>{customer.first_name}</strong></span></td>
                <td className="col-2"><span>Phone</span></td>
                <td className="col-4"><strong>{prefix === CO_OWNER ? customer.home_phone : customer.phone}</strong></td>
              </tr>
              <tr className="row">
                <td className="col-2"><span>Last Name</span></td>
                <td className="col-4"><span><strong>{customer.last_name}</strong></span></td>
                <td className="col-2"><span>Alt Phone</span></td>
                <td className="col-4"><strong>{customer.alt_phone}</strong></td>
              </tr>
              {  prefix !== CO_OWNER &&
              <tr className="row">
                <td className="col-2"><span>Business Name</span></td>
                <td className="col-4"><span><strong>{customer.business_name}</strong></span></td>
              </tr>
              }
              <tr className="row">
                <td className="col-2"><span>Address</span></td>
                <td className="col-4"><strong>{customer.address}</strong></td>
                <td className="col-2"><span>Email</span></td>
                <td className="col-4"><strong>{customer.email}</strong></td>
              </tr>
              <tr className="row">
                <td className="col-2"><span>City</span></td>
                <td className="col-4"><strong>{customer.city}</strong></td>
                <td className="col-2"><span>Best Contact</span></td>
                <td className="col-4"><strong>{customer.best_contact_method}</strong></td>
              </tr>
              <tr className="row">
                <td className="col-2"><span>State</span></td>
                <td className="col-4"><strong>{customer.state_code}</strong></td>
              </tr>
              <tr className="row">
                <td className="col-2"><span>Zip</span></td>
                <td className="col-4"><strong>{customer.postal_code}</strong></td>
              </tr>
            </tbody>
          </table>
        </div>
      );
    }
  }

  renderEventsTabContent(eventDetails){
    if(eventDetails) {
      return (
        <div className="container py-2">
          <div className="row">
            <div className="col-12">
              <table className="table table-striped text-nowrap no-gutters">
                <thead>
                  <tr>
                    <th>Date</th>
                    <th>User</th>
                    <th>Description</th>
                  </tr>
                </thead>
                <tbody>
                  {
                    eventDetails.map((event) => {
                      return (
                        <tr key={event.id}>
                          <td><span>{event.created_at ? moment(event.created_at).format(dateFormat.displayDateFormat) : ""}</span></td>
                          <td><span>{event.created_by_name}</span></td>
                          <td><span>{event.description}</span></td>
                        </tr>
                      );
                    })
                  }
                </tbody>
              </table>
            </div>
          </div>
        </div>
      );
    } else{
      return (
        <div className="container py-2">
          No events data available
        </div>
      );
    }
  }

  renderContractsTab(contractDetails, financingDetails) {
    if (this.isContractsTabActive()) {
      return (<div role="tabpanel" className="tab-pane in active" id="contracts">
        {this.renderContractsTabContent(contractDetails, financingDetails)}
      </div>);
    }
  }

  renderVehicleTab(contractDetails) {
    if (this.isVehicleTabActive()) {
      return (<div role="tabpanel" className="tab-pane in active" id="vehicle">
        {this.renderVehicleTabContent(contractDetails)}
      </div>);
    }
  }

  renderCustomerTab(owner, coOwner) {
    if (this.isCustomerTabActive()) {
      return (<div role="tabpanel" className="tab-pane in active" id="customer">
        {this.renderCustomerTabContent(owner, coOwner)}
      </div>);
    }
  }

  renderFinancingTab(contractDetails) {
    if (this.isFinancingTabActive()) {
      return (<div role="tabpanel" className="tab-pane in active" id="financing">
        {this.renderFinancingTabContent(contractDetails)}
      </div>);
    }
  }

  renderEventsTab(eventDetails) {
    if (this.isEventsTabActive()) {
      return (<div role="tabpanel" className="tab-pane in active" id="events">
        {this.renderEventsTabContent(eventDetails)}
      </div>);
    }
  }

  renderNoDataMessage(showNoData) {
    if (showNoData && !this.state.showLoader) {
      return (
        <div className="text-center">
          <span>No results available for the search criteria</span>
        </div>
      );
    }
  }

  render() {

    const contractDetails = this.state.contractDetails.toJS();
    const spinnerMessage = <span className="text-center"><i className="fa fa-refresh fa-spin"/> Loading...</span>;
    const isEmpty = Object.entries(contractDetails).length === 0;
    if(isEmpty){
      return this.renderNoDataMessage(isEmpty);
    } else {
      return(
        <Loader show={ this.state.showLoader } message={ spinnerMessage }>
          <section className="row px-2">
            <section className="col-12 justify-content-start">
              <div className="clearfix"></div>
              <form onSubmit={ this.handleSubmit }>
                <ul className="nav nav-tabs pt-3" role="tablist" id="contractsTabPanel">
                  <li className="nav-item">
                    <a className={`nav-link ${this.isContractsTabActive() ? "active" : "text-primary"}`}
                      data-toggle="tab"
                      id="contracts-tab"
                      role="tab" aria-controls="contracts" onClick={this.setActive}>
                        Contracts
                    </a>
                  </li>
                  <li className="nav-item">
                    <a className={`nav-link ${this.isVehicleTabActive() ? "active" : "text-primary"}`}
                      data-toggle="tab"
                      id="vehicle-tab"
                      role="tab" aria-controls="vehicle" onClick={this.setActive}>
                        Vehicle
                    </a>
                  </li>
                  <li className="nav-item">
                    <a className={`nav-link ${this.isCustomerTabActive() ? "active" : "text-primary"}`}
                      data-toggle="tab"
                      id="customer-tab"
                      role="tab" aria-controls="customer" onClick={this.setActive}>
                        Customer
                    </a>
                  </li>
                  <li className="nav-item">
                    <a className={`nav-link ${this.isFinancingTabActive() ? "active" : "text-primary"}`}
                      data-toggle="tab"
                      id="financing-tab"
                      role="tab" aria-controls="financing" onClick={this.setActive}>
                        Financing
                    </a>
                  </li>
                  <li className="nav-item">
                    <a className={`nav-link ${this.isEventsTabActive() ? "active" : "text-primary"}`}
                      data-toggle="tab"
                      id="events-tab"
                      role="tab" aria-controls="events" onClick={this.setActive}>
                        Events
                    </a>
                  </li>
                  <li className="nav-item">
                    <a className={`nav-link ${this.isNotesTabActive() ? "active" : "text-primary"}`}
                      data-toggle="tab"
                      id="notes-tab"
                      role="tab" aria-controls="notes" onClick={this.setActive}>
                      Notes
                    </a>
                  </li>
                  <li className="nav-item">
                    <a className={`nav-link text-primary"}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      href={`/api/contract/${contractDetails.contract.id}/download`}
                      aria-controls="download">
                      Download Contract
                    </a>
                  </li>
                  <div className="ml-auto">
                    {this.renderCustomerUpdateButton()}
                  </div>
                </ul>
                <div className="tab-content">
                  {this.renderContractsTab(contractDetails.contract, contractDetails.financing_details)}
                  {this.renderVehicleTab(contractDetails.vehicle_details)}
                  {this.renderCustomerTab(contractDetails.customer_details, contractDetails.cobuyer)}
                  {this.renderFinancingTab(contractDetails)}
                  {this.renderEventsTab(contractDetails.contract_events)}
                  {this.renderNotesTab()}
                </div>
              </form>
            </section>
          </section>
        </Loader>);
    }
  }

  addedNewNote = (newNote) => {
    this.setState({ newNoteText: newNote }, () => {
      this.props.editedContract(true);
    });
  };

  renderNotesTabContent = () => {
    const location = { "id": this.props.id, "contract_number": this.props.contract_number };
    return (
      <div className="col-12">
        <RecordNotes location={ location }
          apiURL={`${apiUrls.contract}/${this.props.contract_number}/notes` }
          addedNote={ this.addedNewNote }
          type={ CONSTANTS.FIELD_TYPE.contract }
          onRecordNotesUpdate={ this.savedNoteCallback }
          newNoteText={ this.state.newNoteText }
          hideAddNoteTextArea={ this.isViewOnlyRole() && !userHasRole(this.props.user, CONSTANTS.USER_ROLES.viewOnlyClaims) }
          ref={ (recordNotes) => {
            this.RecordNotes = recordNotes;
          } }
        />
      </div>
    );
  };

  setActive = (e) => {
    this.setState({
      displayContract: e.target.getAttribute("aria-controls")
    });
  };

  isViewOnlyRole = () => {
    let r = CONSTANTS.USER_ROLES;
    let u = this.props.user;
    const hasReadRole = hstore.hasAny(u.roles, [r.accounting, r.viewOnlyClaims]);
    const hasWriteRole = hstore.hasAny(u.roles, [r.gapClaims,r.gapClaimsManager, r.autoClaims,r.autoClaimsManager]);
    return hasReadRole && !hasWriteRole;
  };

  isCustomerEditable = () => {
    if ((userHasRole(this.props.user, CONSTANTS.USER_ROLES.autoClaims) || userHasRole(this.props.user, CONSTANTS.USER_ROLES.autoClaimsManager)) &&
      !(userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaims) || userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaimsManager))) {
      return ((
        autoClaims.includes(this.props.contractData.product_code)));
    } else if ((userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaims) || userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaimsManager)) &&
      !(userHasRole(this.props.user, CONSTANTS.USER_ROLES.autoClaims) || userHasRole(this.props.user, CONSTANTS.USER_ROLES.autoClaimsManager))) {
      return ((
        [
          CONSTANTS.PRODUCT_CODE_NEW_MAP.gap,
          CONSTANTS.PRODUCT_CODE_NEW_MAP.theftRegistration,
          CONSTANTS.PRODUCT_CODE_NEW_MAP.leaseWearTear
        ].includes(this.props.contractData.product_code)));
    } else if (userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaims) || userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaimsManager) ||
      userHasRole(this.props.user, CONSTANTS.USER_ROLES.autoClaims) || userHasRole(this.props.user, CONSTANTS.USER_ROLES.autoClaimsManager)) {
      return ((
        [
          CONSTANTS.PRODUCT_CODE_NEW_MAP.service,
          CONSTANTS.PRODUCT_CODE_NEW_MAP.keyReplacement,
          CONSTANTS.PRODUCT_CODE_NEW_MAP.appearanceProtection,
          CONSTANTS.PRODUCT_CODE_NEW_MAP.paintlessDentRepair,
          CONSTANTS.PRODUCT_CODE_NEW_MAP.maintenance,
          CONSTANTS.PRODUCT_CODE_NEW_MAP.drivePur,
          CONSTANTS.PRODUCT_CODE_NEW_MAP.theftRegistration,
          CONSTANTS.PRODUCT_CODE_NEW_MAP.gap,
          CONSTANTS.PRODUCT_CODE_NEW_MAP.leaseWearTear].includes(this.props.contractData.product_code)));
    }
    return false;
  };

  renderCustomerUpdateButton = () => {
    if (this.isCustomerTabActive() && this.isCustomerEditable()) {
      return <button type="submit" className="btn btn-secondary ml-1"><i className="fa fa-check"/>&nbsp;
        Update Customer</button>;
    }
  };

  renderNotesTab = () => {
    if (this.isNotesTabActive()) {
      return (<div role="tabpanel" className="tab-pane in active" id="notes">
        {this.renderNotesTabContent()}
      </div>);
    }
  };
}
