import React from "react";
import PageHeader from "../pageHeader/PageHeader.jsx";
import GapDetailsHeader from "../reusable/Gap/GapDetailsHeader.jsx";
import Alert from "react-s-alert";
import { json as ajax } from "./../../ajax.js";
import Loader from "react-loader-advanced";
import ContractDetails from "./ContractDetails.jsx";
import { URLS as apiUrls } from "./../reusable/Utilities/urls";
import ConfirmationModal from "./../reusable/ConfirmationModal/ConfirmationModal.jsx";
import BankSelectionModal from "./../claimCheckList/BankSelectionModal.jsx";
import moment from "moment";
import dateFormat from "./../reusable/Utilities/dateFormat.js";
import PropTypes from 'prop-types';
import { CONSTANTS } from "./../reusable/Constants/constants.js";
import { userHasRole } from "./../reusable/Utilities/userHasRole";


const PRODUCT_CODE_NAMES = {
  'VSC': 'Service',
  'MNT': 'Maintenance',
  'GAP': 'Gap',
  'AP': 'APP (Century)',
  'PDR': 'PDR (Century)',
  'LWT': 'TCA Lease Wear & Tear',
  'KEY': 'Key',
  'VTA': 'VTA',
  'DP': 'DrivePUR',
  'TW': 'TCA Tire & Wheel',
};

const THIRD_PARTY_PRODUCTS = {
  'CSCI GAP': 'CSCI GAP',
  'Lease Wear & Tear (EWU) - Toyota': 'Toyota Lease Wear & Tear',
  'Lease Wear & Tear (EWU) - Lexus': 'Lexus Lease Wear & Tear',
  'NSD Key Replacement': 'NSD Key',
  'NSD Vehicle Theft Assistance': 'NSD VTA',
  'Toyota GAP': 'Toyota GAP',
  'Toyota Tire & Wheel (Non-WA)': 'Toyota Tire & Wheel',
  'Toyota Tire & Wheel (WA)': 'Toyota Tire & Wheel',
  'Vero GAP': 'Vero GAP',
  'NSD Lease Wear & Tear': 'NSD Lease Wear & Tear',
  'NSD Tire & Wheel': 'NSD Tire & Wheel',
};

const DEDUCTIBLE_100 = "1D";
const DEDUCTIBLE_50 = "5D";
const DISAPPEARING_DEDUCTIBLE = "DD";
const HIGH_TECH = "HT";
const HIGHT_TECH_CODE = "HTC";
const SEALS_GASKET = "SG";
const SEALS_GASKET_CODE = "SGC";
const STANDARD_POWER = "SP";
const SMART_TECH = "ST";
const SMART_TECH_CODE = "STC";
const CANADIAN_VEHICLE = "CV";
const RENTAL_UPGRADE = "rental upgrade";
const COMMERCIAL_USE = "commercial use";
const COMMERCIAL_USE_CODE = "CU";
const COMMERCIAL_USE_COMM = "COMM";

export default class Contract extends React.Component {

  IMMS_KEY = 'newClaim';

  static contextTypes = {
    router: PropTypes.object.isRequired
  };

  static propTypes = {
    params: PropTypes.shape({
      id: PropTypes.string.isRequired
    }).isRequired,
    user: PropTypes.shape({
      id: PropTypes.number.isRequired,
      first_name: PropTypes.string.isRequired,
      last_name: PropTypes.string.isRequired,
      email: PropTypes.string.isRequired,
      stores: PropTypes.arrayOf(PropTypes.string.isRequired),
      roles: PropTypes.shape({
        Map: PropTypes.object,
      }).isRequired
    }),
    location: PropTypes.shape({
      query: PropTypes.shape({
        id: PropTypes.string,
        active_tab: PropTypes.string,
        product_code: PropTypes.string
      })
    })
  };

  static contextTypes = {
    router: PropTypes.object.isRequired
  };

  constructor(props) {
    super(props);
    this.state = {
      contractDetails: {},
      showLoader: false,
      contractList: [],
      displayContract: this.props.params.id,
      displayId: parseInt(this.props.location.query.id || 0,10),
      gapClaimIDList: [],
      vtaClaimIDList: [],
      lwtClaimIDList: [],
      isDropDownOpen: false,
      displayBankUpdateConfirmationModal: false,
      displayBankSelectionModal: false,
      isContractEdited: false,
      displayBackConfirmationModal: false,
      displayNewClaimConfirmationModal: false,
      selectedCoverageProduct: "",
      bankSearchQuery: "",
      bankDetails: {},
      contractClaims:[],
      sortOrder: 'asc'
    };
  }

  componentDidMount() {
    document.title = "TCA Portal - Contracts";
    this.loadContracts();
  }

  loadContracts = () => {
    this.setState({ showLoader: true }, () => {
      const query = new URLSearchParams({
        search: this.props.params.id,
        page_size: 1000
      });
      ajax(`${apiUrls.contracts}?${query.toString()}`, {}, {}, (data, status) => {
        if (status === 200) {
          let activeContract = {};
          if (this.props.location.query.id) {
            activeContract = data.contracts && data.contracts.find(contract => contract.id === parseInt(this.props.location.query.id, 10));
          } else {
            activeContract = data.contracts && data.contracts.find(contract => contract.code === this.props.params.id);
          }
          
          const contractList = data.contracts && data.contracts.filter(contract => activeContract.customer_name.toUpperCase() === contract.customer_name.toUpperCase() && activeContract.vin === contract.vin);
          const currentContract = contractList.find(c => c.code == this.state.displayContract);
          // If there is valid contract then show it other wise show first valid contract
          const displayContract = currentContract ? currentContract.id : contractList[0].id;
          
          this.setState({
            showLoader: false,
            contractList,
            activeTab: activeContract.product_code,
            activeTabKey: this.getContractKey(activeContract),
            displayId: displayContract
          });
          const gapContracts = [], vtaContracts = [], lwtContracts = [];
          this.state.contractList.forEach(contract => {
            if (contract.product_code === CONSTANTS.PRODUCT_CODE_NEW_MAP.gap) {
              return gapContracts.push(contract);
            } else if(contract.product_code === CONSTANTS.PRODUCT_CODE_NEW_MAP.theftRegistration) {
              return vtaContracts.push(contract);
            } else if (contract.product_code === CONSTANTS.PRODUCT_CODE_NEW_MAP.leaseWearTear) {
              return lwtContracts.push(contract);
            }
          });
          gapContracts.forEach(contract => {
            this.fetchClaimDetails(contract.code);
          });
          vtaContracts.forEach(contract => {
            this.fetchVTAClaimDetails(contract.code);
          });
          lwtContracts.forEach(contract => {
            this.fetchLWTClaimDetails(contract.code);
          });
        } else if (status === 404) {
          this.setState({ showLoader: false });
        } else {
          this.setState({ showLoader: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  setActive = (e) => {
    this.setState({
      displayContract: e.target.getAttribute("accessKey"),
      displayId: parseInt(e.target.getAttribute("data-id"),10),
      activeTab: e.target.getAttribute("aria-controls"),
      activeTabKey: e.target.getAttribute("contract-key"),
      isDropDownOpen: false
    });
  };

  /**
   * This function will fetch claim details based on contract number to check if any GAP claim exist with respective contract # or not.
   *  @param contractNumber
   * */

  fetchClaimDetails = (contractNumber) => {
    ajax(`${apiUrls.checkContracts}/${contractNumber}`, {}, {}, (data, status) => {
      if (status === 200) {
        if (data && data.gap_claim) {
          const claimList = this.state.gapClaimIDList;
          claimList.push({
            "contract_number": data.gap_claim.contract_number,
            "id": data.gap_claim.id,
            "customer_id": data.gap_claim.customer_id
          });
          this.setState({ showLoader: false, gapClaimIDList: claimList });
        } else {
          this.setState({ showLoader: false });
        }
      } else {
        this.setState({ showLoader: false });
      }
    });
  };

  fetchVTAClaimDetails = (contractNumber) => {
    ajax(`${apiUrls.checkVTAContracts}/${contractNumber}`, {}, {}, (data, status) => {
      if (status === 200) {
        if (data && data.vta_claim) {
          const claimList = this.state.vtaClaimIDList.slice(0);
          claimList.push({
            "contract_number": data.vta_claim.contract_number,
            "id": data.vta_claim.id,
            "customer_id": data.vta_claim.customer_id
          });
          this.setState({ showLoader: false, vtaClaimIDList: claimList });
        } else {
          this.setState({ showLoader: false });
        }
      } else {
        this.setState({ showLoader: false });
      }
    });
  };

  fetchLWTClaimDetails = (contractNumber) => {
    ajax(`${apiUrls.checkLWTContracts}/${contractNumber}`, {}, {}, (data, status) => {
      if (status === 200) {
        if (data && data.lwt_claim) {
          const claimList = this.state.lwtClaimIDList.slice(0);
          claimList.push({
            "contract_number": data.lwt_claim.contract_number,
            "id": data.lwt_claim.id,
            "customer_id": data.lwt_claim.customer_id
          });
          this.setState({ showLoader: false, lwtClaimIDList: claimList });
        } else {
          this.setState({ showLoader: false });
        }
      } else {
        this.setState({ showLoader: false });
      }
    });
  };

  handleNewClaimClick = (contractDetail) => {
    if (contractDetail.product_code === CONSTANTS.PRODUCT_CODE_NEW_MAP.gap) {
      const gapContract = this.isClaimAvailable(contractDetail.code);
      if (gapContract) {
        this.redirectToClaimWorksheet("/gap-claims", gapContract.id);
      } else {
        this.createNewClaimClick(contractDetail);
      }
    } else if (
      contractDetail.product_code === CONSTANTS.PRODUCT_CODE_NEW_MAP.service ||
      contractDetail.product_code === CONSTANTS.PRODUCT_CODE_NEW_MAP.keyReplacement ||
      contractDetail.product_code === CONSTANTS.PRODUCT_CODE_NEW_MAP.appearanceProtection ||
      contractDetail.product_code === CONSTANTS.PRODUCT_CODE_NEW_MAP.paintlessDentRepair ||
      contractDetail.product_code === CONSTANTS.PRODUCT_CODE_NEW_MAP.maintenance ||
      contractDetail.product_code === CONSTANTS.PRODUCT_CODE_NEW_MAP.drivePur ||
      contractDetail.product_code === CONSTANTS.PRODUCT_CODE_NEW_MAP.tireWheel
    ) {
      this.newAutomotiveClaim(contractDetail);
    } else if (contractDetail.product_code === CONSTANTS.PRODUCT_CODE_NEW_MAP.theftRegistration) {
      const vtaContract = this.isVTAClaimAvailable(contractDetail.code);
      if (vtaContract) {
        this.redirectToClaimWorksheet("/vta-claims", vtaContract.id);
      } else {
        this.createNewVTAClaim(contractDetail);
      }
    } else if (contractDetail.product_code === CONSTANTS.PRODUCT_CODE_NEW_MAP.leaseWearTear) {
      const lwtContract = this.isLWTClaimAvailable(contractDetail.code);
      if (lwtContract) {
        this.redirectToClaimWorksheet("/lwt-claims", lwtContract.id);
      } else {
        this.createNewLWTClaim(contractDetail);
      }
    }
  };

  getActiveClaims = () => {
    let claimList = this.state.contractClaims.filter((claim) => !!claim.claim_id);
    claimList = claimList.filter((claim) => {
      return [
        CONSTANTS.AUTO_CLAIM_STATUS_MAP.preAuthorization,
        CONSTANTS.AUTO_CLAIM_STATUS_MAP.open,
        CONSTANTS.AUTO_CLAIM_STATUS_MAP.returned,
        CONSTANTS.AUTO_CLAIM_STATUS_MAP.payable,
        CONSTANTS.AUTO_CLAIM_STATUS_MAP.approved,
        CONSTANTS.AUTO_CLAIM_STATUS_MAP.invoiceSent,
        CONSTANTS.AUTO_CLAIM_STATUS_MAP.needRentalBill,
        CONSTANTS.AUTO_CLAIM_STATUS_MAP.needSubletBill,
        CONSTANTS.AUTO_CLAIM_STATUS_MAP.needSMToCall,
        CONSTANTS.AUTO_CLAIM_STATUS_MAP.needClosedAccountingRO,
        CONSTANTS.AUTO_CLAIM_STATUS_MAP.needProofOfDeductibleReimbursement,
        CONSTANTS.AUTO_CLAIM_STATUS_MAP.waitingOnVendor
      ].includes(claim.status);
    });
    return claimList;
  };

  newAutomotiveClaim = (contractDetail) => {
    this.setState({ showLoader: true }, () => {
      ajax(`${apiUrls.contract}/${this.state.displayId}`, {}, {}, (data, status) => {
        if (status === 200) {
          let activeClaimList = this.getActiveClaims();
          this.setState({
            showLoader: false,
            contractDetails: JSON.parse(JSON.stringify(data.contract))
          });
          if(activeClaimList.length > 0) {
            this.setState({
              displayNewClaimConfirmationModal: true
            });
          } else {
            this.onNewConfirmNewClaimModal();
          }
        } else if (status === 404) {
          this.setState({ showLoader: false });
        } else {
          this.setState({ showLoader: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  prepareNewAutomotiveClaimData = (contractDetails) => {
    let newClaimDetails = {};
    newClaimDetails.vin = contractDetails.vehicle_details.vin;
    newClaimDetails.product_code = contractDetails.contract.product_type_code;
    newClaimDetails.contract_number = contractDetails.contract.code;
    newClaimDetails.contract_status = contractDetails.contract.status;
    newClaimDetails.model = contractDetails.vehicle_details.model;
    newClaimDetails.make = contractDetails.vehicle_details.make;
    newClaimDetails.year = contractDetails.vehicle_details.year;
    newClaimDetails.beginning_miles = parseInt(contractDetails.contract.effective_mileage);
    newClaimDetails.ending_miles = parseInt(contractDetails.contract.expiration_mileage);
    newClaimDetails.effective_date = moment.utc(contractDetails.contract.effective_date).format(dateFormat.backendDateFormat);
    newClaimDetails.expiration_date = moment.utc(contractDetails.contract.expiration_date).format(dateFormat.backendDateFormat);
    newClaimDetails.coverage = contractDetails.contract.product_variant_display_name;
    newClaimDetails.product_variant_display_name = contractDetails.contract.product_variant_display_name;

    let deductible = 0;
    if(contractDetails.contract.options) {
      let findOption = contractDetails.contract.options.find((option) => option.code === DEDUCTIBLE_100);
      if(findOption){
        deductible = 100;
      }

      findOption = contractDetails.contract.options.find((option) => option.code === DEDUCTIBLE_50);
      if(findOption){
        deductible = 50;
      }
    }

    newClaimDetails.contract_deductible = deductible || 0.0;
    newClaimDetails.term = contractDetails.financing_details.term.toString();
    newClaimDetails.first_name = contractDetails.customer_details.first_name;
    newClaimDetails.last_name = contractDetails.customer_details.last_name;
    newClaimDetails.is_business = contractDetails.customer_details.is_business;
    newClaimDetails.business_name = contractDetails.customer_details.business_name;
    newClaimDetails.email_address = contractDetails.customer_details.email;
    newClaimDetails.phone_number = contractDetails.customer_details.phone;
    newClaimDetails.street_address = contractDetails.customer_details.address;
    newClaimDetails.city = contractDetails.customer_details.city;
    newClaimDetails.state = contractDetails.customer_details.state_code;
    newClaimDetails.postal_code = contractDetails.customer_details.postal_code;
    newClaimDetails.contract_store_code = contractDetails.contract.issuing_dealer;
    newClaimDetails.claim_type = "TCA";

    // coverage flags
    if (contractDetails.contract.product_type_code === CONSTANTS.PRODUCT_CODE_NEW_MAP.service) {
      let disappearingDeductible = false;
      let hightTech = false;
      let sealsAndGasket = false;
      let rentalUpgrade = false;
      let commercialUse = 0;
      let standardPowertrainPlusOption = false;
      let smartTech = false;
      let canadianVehicle = false;

      const options = contractDetails.contract.options;
      const surcharges = contractDetails.contract.surcharges;

      if(options){
        for(let option of options){
          const code = option.code;

          if(code === DISAPPEARING_DEDUCTIBLE){
            disappearingDeductible = true;
          } else if(code ===  HIGH_TECH || code === HIGHT_TECH_CODE){
            hightTech = true;
          } else if(code === SEALS_GASKET || code === SEALS_GASKET_CODE){
            sealsAndGasket = true;
          } else if(code === STANDARD_POWER){
            standardPowertrainPlusOption = true;
          } else if(code === SMART_TECH || code === SMART_TECH_CODE){
            smartTech = true;
          } else if(code === CANADIAN_VEHICLE){
            canadianVehicle = true;
          }
        }
      }

      if(surcharges){
        for(let surcharge of surcharges){
          const code = surcharge.code;
          const name = surcharge.name;

          if(name.toLowerCase().includes(RENTAL_UPGRADE)){
            rentalUpgrade = true;
          } else if(name.toLowerCase().includes(COMMERCIAL_USE)){
            commercialUse = surcharge.cost;
          }

          if(code === COMMERCIAL_USE_CODE || code === COMMERCIAL_USE_COMM){
            commercialUse = surcharge.cost;
          }
        }
      }
      newClaimDetails.disappearing_deductible = disappearingDeductible;
      newClaimDetails.high_tech = hightTech;
      newClaimDetails.seals_and_gasket = sealsAndGasket;
      newClaimDetails.rental_upgrade = rentalUpgrade;
      newClaimDetails.commercial_use = parseInt(commercialUse);
      newClaimDetails.standard_powertrain_plus_option = standardPowertrainPlusOption;
      newClaimDetails.smart_tech_option = smartTech;
      newClaimDetails.canadian_vehicle = canadianVehicle;
    } else if (contractDetails.contract.product_type_code === CONSTANTS.PRODUCT_CODE_NEW_MAP.keyReplacement) {
      newClaimDetails.key_count = parseInt(contractDetails.financing_details.keys_remotes);
    } else if (contractDetails.contract.product_type_code === CONSTANTS.PRODUCT_CODE_NEW_MAP.appearanceProtection ||
        contractDetails.contract.product_type_code === CONSTANTS.PRODUCT_CODE_NEW_MAP.paintlessDentRepair) {
      newClaimDetails[this.state.selectedCoverageProduct] = true;
    } else if (contractDetails.contract.product_type_code === CONSTANTS.PRODUCT_CODE_NEW_MAP.maintenance) {
      newClaimDetails.plan = contractDetails.contract.plan_name;
      newClaimDetails.purchased = parseInt(contractDetails.contract.maintenance_visits);
      newClaimDetails.remaining = contractDetails.contract.remaining_visits;
    }
    return newClaimDetails;
  };

  createNewAutomotiveClaim = () => {
    const {
      contractDetails
    } = this.state;
    const newClaimData = this.prepareNewAutomotiveClaimData(contractDetails);
    ajax(apiUrls.automotiveClaims, newClaimData, { method: "POST" }, (data, status) => {
      if (status === 200) {
        this.setState({ showLoader: false }, () => {
          Alert.success("New claim has been created.");
          this.redirectToClaimWorksheet("/automotive-claims", data.id);
        });
      } else {
        this.setState({ showLoader: false }, () => {
          Alert.error("Click the New Claim button again. If the error continues, contact your system administrator.");
        });
      }
    });
  };

  loadContractHistory = (dateSortOrder) => {
    let sortOrder = 'sort_by_date=desc';
    let stateSortOrder = "desc";
    if (dateSortOrder) {
      sortOrder = 'sort_by_date=asc';
      stateSortOrder = "asc";
    }
    this.setState({ showLoader: true, sortOrder: stateSortOrder }, () => {
      ajax(`${apiUrls.automotiveContractHistory}/${this.state.displayContract}/${this.state.activeTab}?${sortOrder}`, {}, {}, (data, status) => {
        if (status === 200) {
          this.setState({
            showLoader: false,
            contractClaims: data.auto_claim_history || []
          });
        } else if (status === 404){
          this.setState({
            showLoader: false,
            contractClaims: []
          });
        }  else {
          this.setState({ showLoader: false }, () => {
            Alert.error("Loading claim history failed.");
          });
        }
      });
    });
  };

  /**
   * Function to get the specified contract's key used for the Tabs
   * 
   * @memberof Contract
   */
  getContractKey = (contractDetails) => {
    if (contractDetails && 
      (contractDetails.product_name || contractDetails.product_code)&& 
      (contractDetails.product_name != '' || contractDetails.product_code != '')) {
      return contractDetails.product_name && 
        THIRD_PARTY_PRODUCTS[contractDetails.product_name] ? 
        THIRD_PARTY_PRODUCTS[contractDetails.product_name] : 
        PRODUCT_CODE_NAMES[contractDetails.product_code];
    }
    return '';
  }

  redirectToClaimWorksheet = (url, id) => {
    const route = { pathname: url, query: { id } };
    if (!this.context.router.isActive(route)) {
      this.context.router.push(route);
    }
  };

  createNewVTAClaim = (contractDetail) => {
    this.setState({ showLoader: true }, () => {
      const data = {
        "id": contractDetail.id
      };
      ajax(apiUrls.vtaclaims, data, { method: "POST" }, (data, status) => {
        if (status === 200) {
          this.setState({ showLoader: false }, () => {
            Alert.success("New claim has been created.");
            this.redirectToClaimWorksheet("/vta-claims", data.id);
          });
        } else if (status === 404) {
          this.setState({ showLoader: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the data. If the error continues, contact your system administrator.");
          });
        } else {
          this.setState({ showLoader: false }, () => {
            if(data.errors && data.errors.claim_exists) {
              Alert.error(data.errors.claim_exists);
              return;
            }
            Alert.error("Click the browser's Refresh button to reload the data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  createNewLWTClaim = (contractDetail) => {
    this.setState({ showLoader: true }, () => {
      const data = {
        "id": contractDetail.id
      };
      ajax(apiUrls.lwtClaims, data, { method: "POST" }, (data, status) => {
        if (status === 200) {
          this.setState({ showLoader: false }, () => {
            Alert.success("New claim has been created.");
            this.redirectToClaimWorksheet("/lwt-claims", data.id);
          });
        } else if (status === 404) {
          this.setState({ showLoader: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the data. If the error continues, contact your system administrator.");
          });
        } else {
          this.setState({ showLoader: false }, () => {
            if(data.errors && data.errors.claim_exists) {
              Alert.error(data.errors.claim_exists);
              return;
            }
            Alert.error("Click the browser's Refresh button to reload the data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  createNewClaimClick = (contractDetail) => {
    this.setState({ showLoader: true }, () => {
      ajax(`${apiUrls.contract}/${this.state.displayId}`, {}, {}, (data, status) => {
        if (status === 200) {
          this.setState({
            contractDetails: JSON.parse(JSON.stringify(data.contract))
          });
          this.validateBankInformation(data.contract);
        } else if (status === 404) {
          this.setState({ showLoader: false });
        } else {
          this.setState({ showLoader: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  validateBankInformation = (newClaimData) => {
    if (newClaimData.bank_account_name) {
      const url = `${apiUrls.gapIntacct}?name=${window.encodeURIComponent(newClaimData.bank_account_name)}&zip=${window.encodeURIComponent(newClaimData.bank_address_zip)}`;
      ajax(url, {}, {}, (data, status) => {
        if (status === 200) {
          if(data.count) {
            if(data.count === 1) {
              this.createNewClaimWithBankDetails(data.vendors[0]);
            } else {
              this.setState({
                bankSearchQuery: newClaimData.bank_account_name,
                bankDetails: data
              }, () => {
                this.updateBankInformation();
              });
            }
          } else {
            this.updateBankInformation();
          }
        } else if (status === 404) {
          this.updateBankInformation();
        } else {
          Alert.error("Click the browser's Refresh button to reload the data. If the error continues, contact your system administrator.");
        }
      });
    } else {
      this.updateBankInformation();
    }
  };

  updateBankInformation = () => {
    this.setState({ displayBankUpdateConfirmationModal: true });
  };

  onUpdateBank = () => {
    this.setState({ displayBankSelectionModal: true, displayBankUpdateConfirmationModal: false });
  };

  onDeclineUpdateBank = () => {
    this.setState({
      displayBankUpdateConfirmationModal: false,
      bankSearchQuery: "",
      bankDetails: {}
    }, () => {
      this.createNewClaim();
    });
  };

  closeBankSelectionModal = () => {
    this.setState({
      displayBankSelectionModal: false,
      showLoader: false,
      bankSearchQuery: "",
      bankDetails: {}
    });
  };

  createNewClaimWithBankDetails = (data) => {
    const newClaimData = this.getNewClaimData(this.state.contractDetails);
    newClaimData.bank_vendor_id = data.vendor_id;
    newClaimData.bank_account_name = data.name;
    newClaimData.bank_address_street1 = data.address1;
    newClaimData.bank_address_zip = data.zip;
    newClaimData.bank_address_city = data.city;
    newClaimData.bank_address_state = data.state;
    this.closeBankSelectionModal();
    this.createNewClaim(newClaimData);
  };

  createNewClaim = (newClaimData) => {
    if (!newClaimData) {
      newClaimData = this.getNewClaimData(this.state.contractDetails);
    }
    ajax(apiUrls.gapclaims, newClaimData, { method: "POST" }, (data, status) => {
      if (status === 200) {
        this.setState({ showLoader: false }, () => {
          Alert.success("New claim has been created.");
          this.addNewClaimNote(data.id);
        });
      } else {
        this.setState({ showLoader: false }, () => {
          if(data.errors && data.errors.claim_exists) {
            Alert.error(data.errors.claim_exists);
            return;
          }
          Alert.error("Click the New Claim button again. If the error continues, contact your system administrator.");
        });
      }
    });
  };

  addNewClaimNote = (id) => {
    const data = { "id": parseInt(id), "notes_text": "New Claim Started" };
    ajax(apiUrls.gapClaimNotes, data, { method: "POST" }, (data, status) => {
      if (status === 200) {
        this.redirectToClaimWorksheet("/gap-claims", id);
      } else {
        this.setState({ showLoader: false }, () => {
          Alert.error("Click the browser's Refresh button to reload the data. If the error continues, contact your system administrator.");
        });
      }
    });
  };

  getNewClaimData = (contractDetail) => {
    const contracts = [];
    for (let index = 0; index < this.state.contractList.length; index++) {
      const contract = {
        "contract_name": this.state.contractList[index]['product_name'],
        "contract_code": this.state.contractList[index]['product_code'],
        "contract_number": this.state.contractList[index]['code'],
        "contract_value": 0.0
      };

      contracts.push(contract);
    }
    const newClaimDetails = {
      contract_number: contractDetail.contract.code,
      vin: contractDetail.vehicle_details.vin,
      make: contractDetail.vehicle_details.make,
      model: contractDetail.vehicle_details.model,
      year: parseInt(contractDetail.vehicle_details.year),
      first_name: contractDetail.customer_details.first_name,
      last_name: contractDetail.customer_details.last_name,
      is_business: contractDetail.customer_details.is_business,
      business_name: contractDetail.customer_details.business_name,
      state: contractDetail.customer_details.state_code,
      city: contractDetail.customer_details.city,
      postal_code: contractDetail.customer_details.postal_code,
      street_address: contractDetail.customer_details.address,
      email_address: contractDetail.customer_details.email,
      phone_number: contractDetail.customer_details.phone,
      bank_account_name: contractDetail.financing_details.lender_name,
      bank_address_street1: contractDetail.financing_details.lender_address,
      bank_address_zip: contractDetail.financing_details.lender_zip,
      bank_address_city: contractDetail.financing_details.lender_city,
      bank_address_state: contractDetail.financing_details.lender_state,
      created_by_user_id: this.props.user.id,
      owner_id: this.props.user.id,
      contracts
    };
    if (contractDetail.financing_details.finance_amount) {
      newClaimDetails['original_financing_contract_value'] = parseFloat(contractDetail.financing_details.finance_amount);
    }
    if (contractDetail.financing_details.finance_monthly_payment) {
      newClaimDetails['payment_amount'] = parseFloat(contractDetail.financing_details.finance_monthly_payment);
    }
    if (contractDetail.financing_details.term) {
      newClaimDetails['contract_term_months'] = parseInt(contractDetail.financing_details.term);
    }
    if (contractDetail.contract.effective_date) {
      newClaimDetails['contract_deal_date'] = moment.utc(contractDetail.contract.effective_date).format(dateFormat.backendDateFormat);
    }
    return newClaimDetails;
  };

  isClaimAvailable = (contract_number) => {
    return this.state.gapClaimIDList.find(claim => contract_number === claim.contract_number);
  };

  isLWTClaimAvailable = (contract_number) => {
    return this.state.lwtClaimIDList.find(claim => contract_number === claim.contract_number);
  };

  isVTAClaimAvailable = (contract_number) => {
    return this.state.vtaClaimIDList.find(claim => contract_number === claim.contract_number);
  };

  handleBackButtonOnClick = () => {
    if (this.state.isContractEdited) {
      this.setState({ displayBackConfirmationModal: true });
    } else {
      this.context.router.goBack();
    }
  };

  redirectToPrevious = () => {
    this.setState({ displayBackConfirmationModal: false }, () => {
      this.context.router.goBack();
    });
  };

  handleUpdates = () => {
    this.setState({ displayBackConfirmationModal: false }, () => {
      this.ContractDetails.onBackButtonAction();
    });
  };

  renderContractDetailHeader = () => {
    let customerName = "";
    let vehicleNumber = "";
    let nextBtnLabel = "";
    if (this.state.contractList[0]) {
      customerName = this.state.contractList[0].customer_name;
      vehicleNumber = this.state.contractList[0].vin;
    }
    
    const contract = this.state.contractList.find((contract) => {
      if (contract.product_code === this.state.activeTab) {
        if (CONSTANTS.PRODUCT_CODE_NEW_MAP.service === contract.product_code && contract.product_name.includes('Alpha')) {
          return false;
        }
        if (CONSTANTS.PRODUCT_CODE_NEW_MAP.keyReplacement === contract.product_code && contract.product_name.includes('NSD')) {
          return false;
        }
        if (CONSTANTS.PRODUCT_CODE_NEW_MAP.tireWheel === contract.product_code && !contract.product_name.includes('TCA')) {
          return false;
        }
        if (CONSTANTS.PRODUCT_CODE_NEW_MAP.gap === contract.product_code && !contract.product_name.includes('TCA GAP')) {
          return false;
        }
        if ((userHasRole(this.props.user, CONSTANTS.USER_ROLES.autoClaims) ||
          userHasRole(this.props.user, CONSTANTS.USER_ROLES.autoClaimsManager) || 
          userHasRole(this.props.user, CONSTANTS.USER_ROLES.viewOnlyClaims)) &&
          !(userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaims) || userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaimsManager))) {
          return ((
            [
              CONSTANTS.PRODUCT_CODE_NEW_MAP.service,
              CONSTANTS.PRODUCT_CODE_NEW_MAP.keyReplacement,
              CONSTANTS.PRODUCT_CODE_NEW_MAP.appearanceProtection,
              CONSTANTS.PRODUCT_CODE_NEW_MAP.paintlessDentRepair,
              CONSTANTS.PRODUCT_CODE_NEW_MAP.maintenance,
              CONSTANTS.PRODUCT_CODE_NEW_MAP.drivePur,
              CONSTANTS.PRODUCT_CODE_NEW_MAP.tireWheel,
            ].includes(contract.product_code)) && contract.id === this.state.displayId);
        } else if ((userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaims) || userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaimsManager)) &&
          !(userHasRole(this.props.user, CONSTANTS.USER_ROLES.autoClaims) || 
            userHasRole(this.props.user, CONSTANTS.USER_ROLES.autoClaimsManager) || 
            userHasRole(this.props.user, CONSTANTS.USER_ROLES.viewOnlyClaims))) {
          return ((
            [
              CONSTANTS.PRODUCT_CODE_NEW_MAP.theftRegistration,
              CONSTANTS.PRODUCT_CODE_NEW_MAP.gap,
              CONSTANTS.PRODUCT_CODE_NEW_MAP.leaseWearTear,
            ].includes(contract.product_code)) && contract.id === this.state.displayId);
        } else if (userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaims) || userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaimsManager) ||
          userHasRole(this.props.user, CONSTANTS.USER_ROLES.autoClaims) || 
          userHasRole(this.props.user, CONSTANTS.USER_ROLES.autoClaimsManager) ||
          userHasRole(this.props.user, CONSTANTS.USER_ROLES.viewOnlyClaims)) {
          return ((
            [
              CONSTANTS.PRODUCT_CODE_NEW_MAP.service,
              CONSTANTS.PRODUCT_CODE_NEW_MAP.keyReplacement,
              CONSTANTS.PRODUCT_CODE_NEW_MAP.appearanceProtection,
              CONSTANTS.PRODUCT_CODE_NEW_MAP.paintlessDentRepair,
              CONSTANTS.PRODUCT_CODE_NEW_MAP.gap,
              CONSTANTS.PRODUCT_CODE_NEW_MAP.theftRegistration,
              CONSTANTS.PRODUCT_CODE_NEW_MAP.maintenance,
              CONSTANTS.PRODUCT_CODE_NEW_MAP.drivePur,
              CONSTANTS.PRODUCT_CODE_NEW_MAP.leaseWearTear,
              CONSTANTS.PRODUCT_CODE_NEW_MAP.tireWheel,
            ].includes(contract.product_code)) && contract.id === this.state.displayId);
        }
      }
    });
    if (contract) {
      switch (contract.product_code){
      case CONSTANTS.PRODUCT_CODE_NEW_MAP.theftRegistration:
        if((contract.status === CONSTANTS.CONTRACT_STATUS_NEW_MAP.Active || contract.status === CONSTANTS.CONTRACT_STATUS_NEW_MAP.Remitted) &&
            !this.isVTAClaimAvailable(this.state.displayContract)){
          nextBtnLabel = "New Claim";
        }
        else{
          nextBtnLabel = "View Claim";
        }
        break;

      case CONSTANTS.PRODUCT_CODE_NEW_MAP.gap:
        if(!this.isClaimAvailable(this.state.displayContract)){
          nextBtnLabel = "New Claim";
        }
        else{
          nextBtnLabel = "View Claim";
        }
        break;
      
      case CONSTANTS.PRODUCT_CODE_NEW_MAP.leaseWearTear:
        if(!this.isLWTClaimAvailable(this.state.displayContract)){
          nextBtnLabel = "New Claim";
        }
        else{
          nextBtnLabel = "View Claim";
        }
        break;

      default:
        nextBtnLabel = "New Claim";
      }

      return (
        <GapDetailsHeader vehicleNumber={ vehicleNumber }
          customerName={ customerName }
          nextButtonText={ nextBtnLabel }
          nextButtonOnClick={ this.handleNewClaimClick.bind(this, contract) }
          nextButtonDisabled={ false }
          backButtonOnClick={ this.handleBackButtonOnClick }
          contractData={ this.state.contractList }
          user={ this.props.user}
          displayId={this.state.displayId}
          contractClaims={this.state.contractClaims}
        />
      );
    }
    return (
      <GapDetailsHeader vehicleNumber={ vehicleNumber }
        customerName={ customerName }
        backButtonOnClick={ this.handleBackButtonOnClick }
        contractData={ this.state.contractList }
        user={this.props.user}
        contractClaims={this.state.contractClaims}
      />
    );
  };

  renderNoDataMessage = () => {
    if (this.state.contractList.length === 0 && !this.state.showLoader) {
      return (
        <div className="text-center">
          <p>No results available for the search criteria</p>
        </div>
      );
    }
  };

  handleDropDownMenuClick = (e) => {
    this.setState({
      displayContract: e.target.getAttribute("accessKey"),
      displayId: e.target.getAttribute("data-id"),
      activeTab: e.target.getAttribute("aria-controls"),
      activeTabKey: e.target.getAttribute("contract-key"),
      isDropDownOpen: true
    });
  };

  getDropDownList = (contract, key) => {
    const dropDownStyle = {
      "padding": 0,
      "border": "1px solid",
      "position": "absolute",
      "zIndex": 9999,
      "background": "#fff",
      "borderTop": "none",
      "borderColor": "#ddd",
      "boxShadow": "0px 4px 11px lightgrey"
    };
    if (this.state.isDropDownOpen && (this.state.activeTabKey === key)) {
      return (
        <ul style={ dropDownStyle }>
          {
            contract.contracts.map((data, index) => <li className="dropdown-item"
              onClick={ this.setActive }
              accessKey={ data.code }
              data-id={data.id}
              aria-controls={ data.product_code }
              contract-key={key}
              key={ index }>
              {data.original_code}
            </li>)
          }
        </ul>
      );
    }
  };

  renderProductTabList = () => {
    const tabList = [];
    const contractTypes = {};
    if (this.state.contractList.length === 0) {
      return;
    }
    this.state.contractList.forEach(contract => {
      const key = this.getContractKey(contract);
      if (contractTypes[key]) {
        contractTypes[key].contracts.push(contract);
      } else {
        contractTypes[key] = { "contracts": new Array(contract) };
      }
    });
    Object.keys(contractTypes).forEach(key => {
      const contract = contractTypes[key];
      const isTabActive = this.state.activeTabKey && this.state.activeTabKey === key;
      if (contract.contracts.length > 1) {
        tabList.push(
          <li className="nav-item dropdown" key={ key }>
            <a className={ `nav-link dropdown-toggle ${isTabActive ? "active" : ""}` }
              accessKey={ contract.contracts[0].code }
              data-id={contract.contracts[0].id}
              aria-controls={ contract.contracts[0].product_code }
              contract-key ={ key }
              onClick={ this.handleDropDownMenuClick }>
              { key }
            </a>
            {this.getDropDownList(contract, key)}
          </li>
        );
      } else {
        tabList.push(
          <li className="nav-item" key={ key }>
            <a className={ `nav-link ${isTabActive ? "active" : ""}` }
              data-toggle="tab" role="tab"
              accessKey={ contract.contracts[0].code }
              data-id={contract.contracts[0].id}
              aria-controls={ contract.contracts[0].product_code }
              contract-key ={ key }
              onClick={ this.setActive }>
              { key }
            </a>
          </li>
        );
      }
    });
    return tabList;
  };

  setEditContract = (isEdited) => {
    this.setState({ isContractEdited: isEdited });
  };

  onConfirmCenturyModal = () => {
    let activeClaimList = this.getActiveClaims();
    if(activeClaimList.length > 0) {
      this.setState({
        displayNewClaimConfirmationModal: true
      });
    } else {
      this.createNewAutomotiveClaim();
    }
  };

  onNewConfirmNewClaimModal = () => {
    this.createNewAutomotiveClaim();
  };

  onViewConfirmNewClaimModal = () => {
    this.setState({
      displayNewClaimConfirmationModal: false
    });

    let activeClaimList = this.getActiveClaims();

    let index = 0;
    if(this.state.sortOrder === "asc"){
      index = 0;
    } else {
      index = activeClaimList.length - 1;
    }
    const query = {
      id: activeClaimList[index].claim_id
    };

    const route = { pathname: "/automotive-claims", query };
    this.context.router.push(route);
  };

  renderTabContents = () => {
    // Check ID
    const contract = this.state.contractList.find(contract => contract.id === this.state.displayId);
    if (contract) {
      return (
        <div role="tabpanel" className="tab-pane in active" id={ this.state.displayContract }>
          <ContractDetails id={this.state.displayId}
            contract_number={ this.state.displayContract }
            contractData={ contract }
            activeTab={ this.props.location.query.active_tab }
            loadContractHistory={ this.loadContractHistory }
            contractClaims={ this.state.contractClaims }
            editedContract={ this.setEditContract }
            redirectToPrevious={ this.redirectToPrevious }
            ref={ (ContractDetails) => this.ContractDetails = ContractDetails }
            user={ this.props.user }/>
        </div>);
    }
  };

  renderCoverageRadio = (value, label) => {
    return (
      <div className="form-check">
        <label className="form-check-label">
          <input className="form-check-input mr-2"
            type="radio"
            name="centuryCoverageRadios"
            id={ "centuryCoverageRadios_" + value }
            value={ value }
            checked={ this.state.selectedCoverageProduct === value }
            onChange={ (e) => {
              this.setState({ selectedCoverageProduct: e.target.value });
            } }/>
          {label}
        </label>
      </div>
    );
  };

  render() {
    const spinnerMessage = <p className="text-center"><i className="fa fa-refresh fa-spin"/> Loading...</p>;
    return (
      <Loader show={ this.state.showLoader } message={ spinnerMessage } contentStyle={ { "paddingTop": "3rem" } }>
        <section className="row">
          <div className="col-12">
            <PageHeader pageTitle="Contracts" user={this.props.user}/>
            <section className="col-12">
              { this.renderContractDetailHeader() }
              <ul className="nav nav-tabs pt-1" role="tablist" id="contractsTabPanel">
                {this.renderProductTabList()}
              </ul>
              <div className="tab-content">
                {this.renderTabContents()}
              </div>
              { this.renderNoDataMessage() }
            </section>
          </div>
        </section>
        <ConfirmationModal displayConfirmationModal={ this.state.displayBankUpdateConfirmationModal }
          displayMessage={ "The Bank Name does not match our vendor records, do you want to update ?" }
          onConfirm={ this.onUpdateBank }
          onDecline={ this.onDeclineUpdateBank }
          confirmButtonText="Yes"
          declineButtonText="No"/>
        <BankSelectionModal selectBankDetails={ this.createNewClaimWithBankDetails }
          closeBankSelectionModal={ this.closeBankSelectionModal }
          searchQuery={ this.state.bankSearchQuery }
          bankDetails={ this.state.bankDetails }
          displayBankSelectionModal={ this.state.displayBankSelectionModal }/>
        <ConfirmationModal confirmButtonText="Yes"
          declineButtonText="No"
          displayConfirmationModal={ this.state.displayBackConfirmationModal }
          displayMessage="You have unsaved work, do you want to save it before continuing?"
          onConfirm={ this.handleUpdates }
          onDecline={ this.redirectToPrevious }/>
        <ConfirmationModal displayConfirmationModal={ this.state.displayNewClaimConfirmationModal }
          displayMessage={ `There is currently an open ${PRODUCT_CODE_NAMES[this.state.activeTab]} claim for this contract.` }
          declineButtonText="New Claim"
          onDecline={ this.onNewConfirmNewClaimModal }
          onConfirm={ this.onViewConfirmNewClaimModal }
          confirmButtonText="View Claim"/>
      </Loader>
    );
  }
}
