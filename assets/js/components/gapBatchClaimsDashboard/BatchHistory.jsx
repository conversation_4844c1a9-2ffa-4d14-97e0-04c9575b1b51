import React from 'react';
import PageHeader from '../pageHeader/PageHeader.jsx';
import Alert from 'react-s-alert';
import { json as ajax } from './../../ajax.js';
import Loader from 'react-loader-advanced';
import { URLS as apiUrls } from "./../reusable/Utilities/urls";
import accounting from "accounting";
import dateFormat from "./../reusable/Utilities/dateFormat.js";
import moment from 'moment';
import If from "./../reusable/If/If.jsx";
import PropTypes from 'prop-types';

export default class BatchHistory extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      batchList: [],
      showLoader: false
    };
    this.loadBatchList = this.loadBatchList.bind(this);
    this.currentDateSortOrder = this.currentDateSortOrder.bind(this);
    this.sortBatchListByDate = this.sortBatchListByDate.bind(this);
    this.renderBatchList = this.renderBatchList.bind(this);
    this.redirectToBatchDetails = this.redirectToBatchDetails.bind(this);
    this.renderTableBodyRow = this.renderTableBodyRow.bind(this);
    this.renderTableBody = this.renderTableBody.bind(this);
    this.renderTableHeader = this.renderTableHeader.bind(this);
  }

  static contextTypes = {
    router: PropTypes.object.isRequired
  };

  static propTypes = {
    user: PropTypes.shape({
      id: PropTypes.number.isRequired,
      first_name: PropTypes.string.isRequired,
      last_name: PropTypes.string.isRequired,
      email: PropTypes.string.isRequired,
      stores: PropTypes.arrayOf(PropTypes.string.isRequired),
      roles: PropTypes.shape({
        Map: PropTypes.object,
      }).isRequired,
    }),
    location: PropTypes.shape({
      query: PropTypes.shape({
        DateSortOrder: PropTypes.string,
      }).isRequired,
    }).isRequired,
  };

  componentDidMount() {
    document.title = 'TCA Portal - GAP Claims';
    this.loadBatchList();
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (this.currentDateSortOrder(nextProps) !== this.currentDateSortOrder(this.props)) {
      this.loadBatchList(nextProps);
    }
  }

  loadBatchList(props) {
    if (!props) {
      props = this.props;
    }
    let url = apiUrls.gapBatchHistory;
    if (this.currentDateSortOrder(props)) {
      url += (`?order_by_date=${window.encodeURIComponent(this.currentDateSortOrder(props))}`);
    }
    this.setState({ showLoader: true }, () => {
      ajax(url, {}, {}, (data, status) => {
        if (status === 200) {
          this.setState({
            batchList: data.batchList,
            showLoader: false
          });
        } else {
          this.setState({ showLoader: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  }

  currentDateSortOrder(props) {
    if (!props) {
      props = this.props;
    }
    return props.location.query.DateSortOrder;
  }

  sortBatchListByDate() {
    let query;
    if (this.currentDateSortOrder() == 'asc') {
      query = { DateSortOrder: 'desc' };
    } else {
      query = { DateSortOrder: 'asc' };
    }
    const route = { pathname: "/gap-batch-history", query };
    if (!this.context.router.isActive(route)) {
      this.context.router.push(route);
    }
  }

  renderTableHeader() {
    return (
      <tr>
        <th className="cursor-pointer" onClick={this.sortBatchListByDate}>
          Batch Date&nbsp;
          <If condition={this.currentDateSortOrder() === 'asc'}>
            <i className="fa fa-caret-up"/>
          </If>
          <If condition={this.currentDateSortOrder() === 'desc'}>
            <i className="fa fa-caret-down"/>
          </If>
          <If condition={!this.currentDateSortOrder()}>
            <i className="fa fa-sort"/>
          </If>
        </th>
        <th>Name</th>
        <th>$ Amount</th>
        <th>Status</th>
        <th>Notes</th>
      </tr>
    );
  }

  renderTableBody() {
    return this.state.batchList.map(this.renderTableBodyRow);
  }

  renderTableBodyRow(batchData, index) {
    return (
      <tr key={index}>
        <td>{moment(batchData.created_at, dateFormat.backendDateFormat).format(dateFormat.displayDateFormat)}</td>
        <td>
          <a href="#!"
            className="users"
            onClick={ this.redirectToBatchDetails.bind(this, batchData.id) }>
            {batchData.id}
          </a>
        </td>
        <td>
          { accounting.formatMoney(batchData.amount, '$', 2) }
        </td>
        <td>{batchData.status}</td>
        <td>{batchData.note}</td>
      </tr>
    );
  }

  renderBatchList() {
    if (this.state.batchList.length > 0) {
      return (
        <div className="claim-list">
          <table className="table table-striped">
            <thead>{this.renderTableHeader()}</thead>
            <tbody>{this.renderTableBody()}</tbody>
          </table>
        </div>);
    } else {
      return (
        <div className="text-center">
          <p>No Results available for this search query</p>
        </div>
      );
    }
  }

  redirectToBatchDetails(batchID) {
    const route = { pathname: "/gap-batch-claims/" + batchID };
    if (!this.context.router.isActive(route)) {
      this.context.router.push(route);
    }
  }

  render() {
    const spinnerMessage = <p className='text-center'><i className="fa fa-refresh fa-spin"/> Loading...</p>;
    return (
      <Loader show={this.state.showLoader} message={spinnerMessage}>
        <section className="gap-dashboard clearfix">
          <div className="row">
            <div className="col-12">
              <PageHeader pageTitle="GAP Batch History" user={this.props.user}/>
              {this.renderBatchList()}
            </div>
          </div>
        </section>
      </Loader>
    );
  }
}