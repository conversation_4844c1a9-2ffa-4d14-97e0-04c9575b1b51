import React from 'react';
import PageHeader from '../pageHeader/PageHeader.jsx';
import Al<PERSON> from 'react-s-alert';
import ClaimsList from './ClaimsList.jsx';
import { json as ajax } from './../../ajax.js';
import Loader from 'react-loader-advanced';
import { URLS as apiUrls } from "./../reusable/Utilities/urls";
import dateFormat from "./../reusable/Utilities/dateFormat.js";
import moment from 'moment';
import PropTypes from 'prop-types';

export default class GapBatchDetails extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      claimList: [],
      successCount: undefined,
      failureCount: undefined,
      batchDate: undefined,
      showLoader: false
    };
    this.loadClaimData = this.loadClaimData.bind(this);
    this.currentNameSortOrder = this.currentNameSortOrder.bind(this);
    this.sortClaimListByName = this.sortClaimListByName.bind(this);
    this.renderClaimList = this.renderClaimList.bind(this);
    this.renderSuccessFailureCount = this.renderSuccessFailureCount.bind(this);
  }

  static contextTypes = {
    router: PropTypes.object.isRequired
  };

  static propTypes = {
    params: PropTypes.shape({
      id: PropTypes.string.isRequired
    }).isRequired,
    user: PropTypes.shape({
      id: PropTypes.number.isRequired,
      first_name: PropTypes.string.isRequired,
      last_name: PropTypes.string.isRequired,
      email: PropTypes.string.isRequired,
      stores: PropTypes.arrayOf(PropTypes.string.isRequired),
      roles: PropTypes.shape({
        Map: PropTypes.object,
      }).isRequired,
    }),
    location: PropTypes.shape({
      query: PropTypes.shape({
        NameSortOrder: PropTypes.string,
      }).isRequired,
    }).isRequired,
  };

  componentDidMount() {
    document.title = 'TCA Portal - GAP Claims';
    this.loadClaimData();
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (this.currentNameSortOrder(nextProps) !== this.currentNameSortOrder(this.props)) {
      this.loadClaimData(nextProps);
    }
  }

  loadClaimData(props) {
    if (!props) {
      props = this.props;
    }

    let url = apiUrls.gapBatchDetails.replace('__batchId__', this.props.params.id);
    if (this.currentNameSortOrder(props)) {
      url += (`?order_by_name=${window.encodeURIComponent(this.currentNameSortOrder(props))}`);
    }
    this.setState({ showLoader: true }, () => {
      ajax(url, {}, {}, (data, status) => {
        if (status === 200) {
          this.setState({
            claimList: data.batchClaims,
            successCount: data.successCount,
            failureCount: data.failureCount,
            batchDate: data.batchDate,
            showLoader: false
          });
        } else {
          this.setState({ showLoader: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  }

  currentNameSortOrder(props) {
    if (!props) {
      props = this.props;
    }
    return props.location.query.NameSortOrder;
  }

  sortClaimListByName() {
    let query;
    if (this.currentNameSortOrder() == 'asc') {
      query = { NameSortOrder: 'desc' };
    } else {
      query = { NameSortOrder: 'asc' };
    }
    const route = { pathname: "/gap-batch-claims/" + this.props.params.id, query };
    if (!this.context.router.isActive(route)) {
      this.context.router.push(route);
    }
  }

  renderClaimList() {
    if (this.state.claimList.length > 0) {
      return (<ClaimsList claimList={this.state.claimList}
        renderStatus={true}
        ref={(claimList) => {
          this.claimList = claimList;
        }}
        sortClaimListByName={this.sortClaimListByName}
        insuredNameSortOrder={this.currentNameSortOrder()}/>);
    } else {
      return (
        <div className="text-center">
          <p>No Results available for this search query</p>
        </div>
      );
    }
  }

  renderSuccessFailureCount() {
    return (
      <div className="row ml-3">
        <p>{this.state.successCount}&nbsp;Successful</p>
        <p className="text-danger ml-5">{this.state.failureCount}&nbsp;Failed</p>
      </div>
    );
  }

  render() {
    const spinnerMessage = <p className='text-center'><i className="fa fa-refresh fa-spin"/> Loading...</p>;
    return (
      <Loader show={this.state.showLoader} message={spinnerMessage}>
        <section className="gap-dashboard clearfix">
          <div className="row">
            <div className="col-12">
              <PageHeader
                pageTitle={`GAP Batch History Details - ${this.props.params.id} - ${moment(this.state.batchDate, dateFormat.backendDateFormat).format(dateFormat.displayDateFormat)}`}
                backButtonText="Back"
                user={this.props.user}
                onBackButtonClick={this.context.router.goBack}/>
              { this.renderSuccessFailureCount() }
              {this.renderClaimList()}
            </div>
          </div>
        </section>
      </Loader>
    );
  }

}
