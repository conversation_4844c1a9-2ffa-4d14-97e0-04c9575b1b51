import React from 'react';
import accounting from "accounting";
import If from "./../reusable/If/If.jsx";
import PropTypes from 'prop-types';

export default class ClaimsList extends React.Component {

  constructor(props) {
    super(props);
    this.state = {
      selectedClaims: [],
      selectAll: false
    };
    this.renderTableHeader = this.renderTableHeader.bind(this);
    this.renderTableBody = this.renderTableBody.bind(this);
    this.redirectToWorksheet = this.redirectToWorksheet.bind(this);
    this.renderTableBodyRow = this.renderTableBodyRow.bind(this);
    this.selectClaim = this.selectClaim.bind(this);
    this.selectAll = this.selectAll.bind(this);
    this.renderCheckBoxHeader = this.renderCheckBoxHeader.bind(this);
  }

  static propTypes = {
    claimList: PropTypes.array.isRequired,
    sortClaimListByName: PropTypes.func.isRequired,
    insuredNameSortOrder: PropTypes.oneOf(['asc', 'desc']),
    renderStatus: PropTypes.bool
  };

  static contextTypes = {
    router: PropTypes.object.isRequired
  };

  selectClaim(id) {
    let selectedClaims = this.state.selectedClaims.slice(0);
    if (selectedClaims.indexOf(id) === -1) {
      selectedClaims.push(id);
    } else {
      selectedClaims.splice(selectedClaims.indexOf(id), 1);
    }
    var selectAll = this.state.selectAll;
    if (selectedClaims.length === this.props.claimList.length) {
      selectAll = true;
    } else {
      selectAll = false;
    }
    this.setState({ selectedClaims, selectAll });
  }

  selectAll() {
    let selectedClaims = [];
    if (this.state.selectAll) {
      for (let index = 0; index < this.props.claimList.length; index++) {
        selectedClaims.push(this.props.claimList[index].id);
      }
    }
    this.setState({ selectedClaims });
  }

  renderCheckBoxHeader() {
    return (
      <input type="checkbox" value="all"
        checked={this.state.selectAll}
        onChange={() => {
          this.setState({ selectAll: !this.state.selectAll }, () => {
            this.selectAll();
          });
        }}/>
    );
  }

  renderTableHeader() {
    return (
      <tr>
        <th>
          <If condition={!this.props.renderStatus}>
            {this.renderCheckBoxHeader()}
          </If>
        </th>
        <th className="cursor-pointer"
          onClick={this.props.sortClaimListByName}>
          Insured Name&nbsp;
          <If condition={this.props.insuredNameSortOrder === 'asc'}>
            <i className="fa fa-caret-up"/>
          </If>
          <If condition={this.props.insuredNameSortOrder === 'desc'}>
            <i className="fa fa-caret-down"/>
          </If>
          <If condition={!this.props.insuredNameSortOrder}>
            <i className="fa fa-sort"/>
          </If>
        </th>
        <th>Contract #</th>
        <th>$ Amount</th>
        <If condition={this.props.renderStatus}>
          <th>Notes</th>
        </If>
      </tr>
    );
  }

  renderTableBody() {
    return this.props.claimList.map(this.renderTableBodyRow);
  }

  renderTableBodyRow(claimData, index) {
    return (
      <tr key={index}>
        <td>
          <If condition={this.props.renderStatus}>
            {this.renderStatus(claimData)}
          </If>
          <If condition={!this.props.renderStatus}>
            {this.renderCheckBox(claimData)}
          </If>
        </td>
        <td>
          <a href="#!"
            className="users"
            onClick={ this.redirectToWorksheet.bind(this, claimData) }>
            {claimData['insured_name']}
          </a>
        </td>
        <td>
          <a href="#!"
            onClick={ this.redirectToWorksheet.bind(this, claimData) }>
            {claimData['contract_number']}
          </a>
        </td>
        <td>
          { accounting.formatMoney(claimData['amount'], '$', 2) }
        </td>
        <If condition={this.props.renderStatus}>
          <td>{ claimData.note }</td>
        </If>
      </tr>
    );
  }

  renderStatus(claimData) {
    let claimStatusIcon = <i className="fa fa-times text-danger"/>;
    if (claimData.is_success) {
      claimStatusIcon = <i className="fa fa-check text-success"/>;
    }
    return claimStatusIcon;
  }

  renderCheckBox(claimData) {
    return (
      <input type="checkbox" value="all"
        checked={this.state.selectedClaims.indexOf(claimData['id']) !== -1}
        onChange={this.selectClaim.bind(this, claimData['id'])}/>
    );
  }

  redirectToWorksheet(claimData) {
    if (claimData.contract_number && (claimData.id || claimData.gap_claim_id)) {
      const query = {
        id: claimData.id || claimData.gap_claim_id,
        contract_number: claimData.contract_number
      };
      const route = { pathname: "/gap-claims", query };
      if (!this.context.router.isActive(route)) {
        this.context.router.push(route);
      }
    }
  }

  render() {
    return (
      <div className="claim-list">
        <table className="table table-striped">
          <thead>
            {this.renderTableHeader()}
          </thead>
          <tbody>
            {this.renderTableBody()}
          </tbody>
        </table>
      </div>
    );
  }
}