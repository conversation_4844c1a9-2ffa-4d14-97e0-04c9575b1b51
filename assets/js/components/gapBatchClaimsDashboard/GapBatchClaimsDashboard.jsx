import React from 'react';
import PageHeader from '../pageHeader/PageHeader.jsx';
import Alert from 'react-s-alert';
import ClaimsList from './ClaimsList.jsx';
import { json as ajax } from './../../ajax.js';
import Loader from 'react-loader-advanced';
import { URLS as apiUrls } from "./../reusable/Utilities/urls";
import PropTypes from 'prop-types';
import ConfirmationModal from "./../reusable/ConfirmationModal/ConfirmationModal.jsx";
import { Link } from 'react-router';

export default class GapBatchClaimsDashboard extends React.Component {

  constructor(props) {
    super(props);
    this.state = {
      claimList: [],
      userList: [],
      showLoader: false,
      showSuccessModal: false,
      claimsSubmitted: '',
      claimFailed: ''
    };
    this.loadClaimData = this.loadClaimData.bind(this);
    this.currentNameSortOrder = this.currentNameSortOrder.bind(this);
    this.sortClaimListByName = this.sortClaimListByName.bind(this);
    this.renderClaimList = this.renderClaimList.bind(this);
    this.renderSubmitButton = this.renderSubmitButton.bind(this);
    this.onSubmit = this.onSubmit.bind(this);
  }

  static contextTypes = {
    router: PropTypes.object.isRequired
  };

  static propTypes = {
    user: PropTypes.shape({
      id: PropTypes.number.isRequired,
      first_name: PropTypes.string.isRequired,
      last_name: PropTypes.string.isRequired,
      email: PropTypes.string.isRequired,
      stores: PropTypes.arrayOf(PropTypes.string.isRequired),
      roles: PropTypes.shape({
        Map: PropTypes.object,
      }).isRequired,
    }),
    location: PropTypes.shape({
      query: PropTypes.shape({
        q: PropTypes.string,
        userId: PropTypes.string,
        NameSortOrder: PropTypes.string,
        status: PropTypes.string
      }).isRequired,
    }).isRequired,
  };

  componentDidMount() {
    document.title = 'TCA Portal - GAP Claims';
    this.loadClaimData();
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (this.currentNameSortOrder(nextProps) !== this.currentNameSortOrder(this.props)) {
      this.setState({ q: nextProps.location.query.q || "" });
      this.loadClaimData(nextProps);
    }
  }

  loadClaimData(props) {
    if (!props) {
      props = this.props;
    }
    let url = `${apiUrls.gapClaimsFinance}?status=${window.encodeURIComponent('In Review')}`;
    if (this.currentNameSortOrder(props)) {
      url += (`&order_by_name=${window.encodeURIComponent(this.currentNameSortOrder(props))}`);
    }
    this.setState({ showLoader: true }, () => {
      ajax(url, {}, {}, (data, status) => {
        if (status === 200) {
          this.setState({ claimList: data.claims, showLoader: false });
        } else {
          this.setState({ showLoader: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  }

  onSubmit() {
    if (this.claimList && this.claimList.state.selectedClaims.length < 1) {
      Alert.error('Please select at least one claim to submit the batch.');
      return;
    }
    let dataObject = {
      claim_id_list: this.claimList.state.selectedClaims
    };
    this.setState({ showLoader: true }, () => {
      ajax(apiUrls.gapClaimsBatchAuthorize, dataObject, { method: 'PUT' }, (data, status) => {
        if (status === 200) {
          this.setState({ showLoader: false }, ()=> {
            this.loadClaimData();
            if (data.failed === 0) {
              Alert.success('Batch Authorized successfully.');
            } else {
              this.setState({ showSuccessModal: true, claimsSubmitted: data.success, claimFailed: data.failed });
            }
          });
        } else {
          this.setState({ showLoader: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  }

  currentNameSortOrder(props) {
    if (!props) {
      props = this.props;
    }
    return props.location.query.NameSortOrder || 'asc';
  }

  sortClaimListByName() {
    let query;
    let route;
    if (this.currentNameSortOrder() == 'asc') {
      query = { NameSortOrder: 'desc' };
      route = { pathname: "/gap-batch-claims", query };
      if (!this.context.router.isActive(route)) {
        this.context.router.push(route);
      }
    } else {
      query = { NameSortOrder: 'asc' };
      route = { pathname: "/gap-batch-claims", query };
      if (!this.context.router.isActive(route)) {
        this.context.router.push(route);
      }
    }
  }

  renderClaimList() {
    if (this.state.claimList.length > 0) {
      return (<ClaimsList claimList={this.state.claimList}
        onSubmit={this.onSubmit}
        ref={(claimList) => {
          this.claimList = claimList;
        }}
        sortClaimListByName={this.sortClaimListByName}
        insuredNameSortOrder={this.currentNameSortOrder()}/>);
    } else {
      return (
        <div className="text-center">
          <p>No Results available for this search query</p>
        </div>
      );
    }

  }

  renderSubmitButton() {
    return (
      <button className="btn btn-primary"
        onClick={this.onSubmit}>
        <i className="fa fa-check"/>&nbsp;Submit Batch
      </button>
    );
  }

  renderConfirmationModal() {
    return (
      <div className="col-12">
        <p className="text-center">for detail insights visit <Link to='/gap-batch-history'>GAP Batch History</Link>.</p>
      </div>
    );
  }

  render() {
    const spinnerMessage = <p className='text-center'><i className="fa fa-refresh fa-spin"/> Loading...</p>;
    return (
      <Loader show={this.state.showLoader} message={spinnerMessage}>
        <section className="gap-dashboard clearfix">
          <div className="row">
            <div className="col-12 mt-5">
              <PageHeader pageTitle="Create GAP Claim Batch" renderTemplate={this.renderSubmitButton} user={this.props.user}/>
              {this.renderClaimList()}
            </div>
          </div>
        </section>
        <ConfirmationModal displayConfirmationModal={this.state.showSuccessModal}
          onConfirm={()=> {
            this.setState({ showSuccessModal: false, claimsSubmitted: '', claimFailed: '' });
          }}
          displayMessage={`${this.state.claimsSubmitted} out of ${parseInt(this.state.claimFailed) + parseInt(this.state.claimsSubmitted)} claims submitted successfully.`}
          confirmButtonText="Ok"
          renderTemplate={this.renderConfirmationModal}
          type="node"/>
      </Loader>
    );
  }
}