import React from 'react';
import accounting from "accounting";
import If from "./../reusable/If/If.jsx";
import ConfirmationModal from "./../reusable/ConfirmationModal/ConfirmationModal.jsx";
import { CONSTANTS } from "./../reusable/Constants/constants.js";
import PropTypes from 'prop-types';
import ClaimsList from "./ClaimsList.jsx";


export default class ProductBatchPanel extends React.Component {

  constructor(props) {
    super(props);
    this.state = {
      selectedClaims: [],
      totalBatchAmount: 0,
      isSelectedAll: false,
      isExpanded: true,
      displaySubmitConfirmationModal: false,
      negativeClaims: [],
      isNegativeClaim: false,
      isSelectMax: false,
      maxSelectedClaims:[]
    };
  }

  static propTypes = {
    productDetails: PropTypes.object.isRequired,
    facilityDetails: PropTypes.object.isRequired,
    handleBatchSubmit: PropTypes.func.isRequired,
    disabled: PropTypes.bool.isRequired,
    sortBy: PropTypes.string,
    sortOrder: PropTypes.string,
    sortClaimList: PropTypes.func
  };

  selectClaim = (id, amount) => {
    let selectedClaims = this.state.selectedClaims.slice(0);
    let negativeClaims = this.state.negativeClaims.slice(0);
    let totalBatchAmount = this.state.totalBatchAmount;
    amount = parseFloat(amount);

    if (selectedClaims.indexOf(id) === -1) {
      selectedClaims.push(id);
      totalBatchAmount += amount;
      if (amount < 0) {
        negativeClaims.push({id, amount});
      }
    } else {
      selectedClaims.splice(selectedClaims.indexOf(id), 1);
      totalBatchAmount -= amount;
      if (amount < 0) {
        negativeClaims.splice(negativeClaims.findIndex(obj => obj.id === id), 1);
      }
    }

    let isNegativeClaim = false;
    if (negativeClaims.length > 0) {
      isNegativeClaim = true;
    }

    const isSelectedAll = selectedClaims.length === this.props.productDetails.claims.length;
    this.setState({ selectedClaims, isSelectedAll, totalBatchAmount, negativeClaims, isNegativeClaim });
  };

  selectAll = (isSelectedAll) => {
    let selectedClaims = [];
    let totalBatchAmount = 0;
    let isNegativeClaim = false;
    if (isSelectedAll) {
      this.props.productDetails.claims.forEach((claim) => {
        selectedClaims.push(claim.id);
        totalBatchAmount += parseFloat(claim.estimate);
        if (parseFloat(claim.estimate) < 0) {
          isNegativeClaim = true;
        }
      });
    }
    this.setState({ selectedClaims, totalBatchAmount, isSelectedAll, isNegativeClaim });
  };

  selectMax = (isSelectMax) => {
    const {
      selectedClaims,
      maxSelectedClaims
    } = this.state;
    let totalBatchAmount = 0;

    // If we already have selected claims more than or equal to max allowed
    // then just keep original and unselected if nealy selecte any
    if (selectedClaims && selectedClaims.length >= CONSTANTS.MAX_BATCH_CLAIM_COUNT && !isSelectMax && maxSelectedClaims.length > 0) {
      let newClaimSelection =[];
      if (selectedClaims.length > CONSTANTS.MAX_BATCH_CLAIM_COUNT) {
        newClaimSelection = selectedClaims.filter(c => !maxSelectedClaims.includes(c));
        totalBatchAmount = this.props.productDetails.claims.reduce((sum, c) => {
          if (newClaimSelection.includes(c.id)) {
            return sum + parseFloat(c.estimate);
          }
          return sum;
        }, 0);
      }

      this.setState({isSelectMax, selectedClaims: newClaimSelection, maxSelectedClaims: [], totalBatchAmount});
      return;
    }

    let newSelectedClaims = [...selectedClaims];
    let isNegativeClaim = false;
    let maxClaims= selectedClaims.length <= CONSTANTS.MAX_BATCH_CLAIM_COUNT ? [...selectedClaims] : [];
    const claimForSelection = this.props.productDetails.claims.filter(c => !selectedClaims.includes(c.id));
    const selectionCount = CONSTANTS.MAX_BATCH_CLAIM_COUNT - selectedClaims.length;
    totalBatchAmount = this.props.productDetails.claims
      .filter(c => selectedClaims.includes(c.id))
      .reduce((sum, c) => {
        return sum += parseFloat(c.estimate);
      }, 0);

    if (isSelectMax) {
      if (selectionCount >= 0) {
        claimForSelection.slice(0, selectionCount).forEach((claim) => {
          newSelectedClaims.push(claim.id);
          maxClaims.push(claim.id);
          totalBatchAmount += parseFloat(claim.estimate);
          if (parseFloat(claim.estimate) < 0) {
            isNegativeClaim = true;
          }
        });
      } else {
        maxClaims = selectedClaims.slice(0, CONSTANTS.MAX_BATCH_CLAIM_COUNT);
      }
    }
    this.setState({ selectedClaims: newSelectedClaims, totalBatchAmount, isSelectMax, isNegativeClaim, isSelectedAll: false, maxSelectedClaims: maxClaims });
  };

  renderClaimList = () => {
    return (
      <ClaimsList claimList={ this.props.productDetails.claims }
        selectedClaims={ this.state.selectedClaims }
        isSelectedAll={ this.state.isSelectedAll }
        selectClaim={ this.selectClaim }
        disabled={ this.props.disabled }
        selectAll={ this.selectAll }
        isSelectMax={this.state.isSelectMax}
        selectMax={this.selectMax}
        sortBy={ this.props.sortBy }
        sortOrder={ this.props.sortOrder }
        sortClaimList={ this.props.sortClaimList }/>
    );
  };
  
  renderNegativeClaimWarning = () => {
    if (this.state.isNegativeClaim) {
      return (
        <div className="col-8 offset-2" style={{color: 'red'}}>
          <p>
            This batch contains negative claims or the batch total is a negative amount.
          </p>
        </div>
      );
    }
    return null;
  }

  render() {
    return (
      <div className="col mt-3">
        <div className="card">
          <div className="card-header d-flex align-items-center cursor-pointer" onClick={ (e) => {
            if (e.target.accessKey !== "submit-button") {
              this.setState({ isExpanded: !this.state.isExpanded });
            }
          } }>
            <div className="col-4 d-flex justify-content-start">
              <span className="text-left">
                {`${CONSTANTS.PRODUCT_CODE_NAME_MAP[ this.props.productDetails.product_code ]}`}
              </span>
            </div>
            <div className="col-4 d-flex justify-content-center">
              <span className="text-center">
                {`${this.state.selectedClaims.length} Selected - ${accounting.formatMoney(this.state.totalBatchAmount, '$', 2)}`}
              </span>
            </div>
            <div className="col-4 d-flex justify-content-end">
              <If condition={ !this.props.disabled }>
                <button className="btn btn-primary"
                  accessKey="submit-button"
                  disabled={ this.state.selectedClaims.length <= 0 || this.props.disabled }
                  onClick={ () => {
                    this.setState({ displaySubmitConfirmationModal: true });
                  } }>Submit
                </button>
              </If>
              &nbsp;
              <i
                className={ this.state.isExpanded ? "fa fa-minus-circle fa-lg cursor-pointer d-flex align-items-center" : "fa fa-plus-circle fa-lg cursor-pointer d-flex align-items-center" }/>
            </div>
          </div>
          <If condition={ this.state.isExpanded }>
            <div className="card-body">
              {this.renderClaimList()}
            </div>
          </If>
          <ConfirmationModal
            displayConfirmationModal={ this.state.displaySubmitConfirmationModal }
            displayMessage={ `Are you sure you want to submit this batch of claims for:
                            ${this.props.facilityDetails.pay_type === CONSTANTS.PAYMENT_TYPE_CODES.CUSTOMER ? this.props.facilityDetails.customer_payee_name : this.props.facilityDetails.facility_name} - ${CONSTANTS.PRODUCT_CODE_NAME_MAP[ this.props.productDetails.product_code ]} - ${this.state.selectedClaims.length} Claims : ${accounting.formatMoney(this.state.totalBatchAmount, '$', 2)}` }
            onConfirm={ () => {
              this.props.handleBatchSubmit(
                this.props.productDetails,
                this.props.facilityDetails,
                this.state.selectedClaims,
                this.state.totalBatchAmount);
              this.setState({
                displaySubmitConfirmationModal: false,
                selectedClaims: [],
                totalBatchAmount: 0,
                isSelectMax: false,
              });
            } }
            onDecline={ () => {
              this.setState({ displaySubmitConfirmationModal: false });
            } }
            renderTemplate= {this.renderNegativeClaimWarning}
            type={'node'}
            confirmButtonText="Submit Batch"
            declineButtonText="Cancel"/>
        </div>
      </div>
    );
  }
}
