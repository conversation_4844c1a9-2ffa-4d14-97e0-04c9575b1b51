import React from 'react';
import PropTypes from 'prop-types';
import PageHeader from '../pageHeader/PageHeader.jsx';
import UpdateFacilityModal from "../automotiveClaimWorksheet/FacilityUpdateModal.jsx";
import SearchFacility from "../automotiveClaimWorksheet/SearchFacility.jsx";
import NewFacilityModal from "../automotiveClaimWorksheet/FacilityCreateModal.jsx";
import { userHasRole } from '../reusable/Utilities/userHasRole.js';
import { CONSTANTS } from '../reusable/Constants/constants.js';

export default class SearchFacilityPage extends React.Component {

  constructor(props) {
    super(props);
    this.state = {
      showUpdateModal: false,
      viewFacility: false,
      showAddFacilityModal: false,
      selectedFacilityID: void 0,
      refreshSearch: false
    };
    [
      "onNewFacilityClick",
      "onCloseNewFacilityModal",
      "onRowSelect",
      "onUpdateFacilityClick",
      "onPositiveUpdateModal",
      "onCloseUpdateModal"
    ].forEach((func) => {this[func] = this[func].bind(this);});
  }

  static contextTypes = {
    router: PropTypes.object.isRequired
  };

  static propTypes = {
    user: PropTypes.shape({
      id: PropTypes.number.isRequired,
      first_name: PropTypes.string.isRequired,
      last_name: PropTypes.string.isRequired,
      email: PropTypes.string.isRequired,
      stores: PropTypes.arrayOf(PropTypes.string.isRequired),
      roles: PropTypes.shape({
        Map: PropTypes.object,
      }).isRequired
    })
  };

  componentDidMount() {
    document.title = 'TCA Portal - Automotive Facility Management';
  }

  onRowSelect(id){
    if(id) {
      this.setState({
        selectedFacilityID: parseInt(id),
        refreshSearch: false
      });
    }
    else{
      this.setState({
        selectedFacilityID: void 0,
        refreshSearch: false
      });
    }
  }

  onNewFacilityClick(){
    this.setState({
      showAddFacilityModal: true
    });
  }

  onUpdateFacilityClick(viewFacility){
    this.setState({
      showUpdateModal: true,
      viewFacility: viewFacility,
    });
  }

  onPositiveUpdateModal(){
    this.setState({
      showUpdateModal: false,
      viewFacility: false,
      refreshSearch: true
    });
  }

  onCloseNewFacilityModal(){
    this.setState({
      showAddFacilityModal: false
    });
  }

  onCloseUpdateModal(){
    this.setState({
      showUpdateModal: false,
      viewFacility: false
    });
  }

  renderSearchFacility(){
    return (
      <SearchFacility refresh={ this.state.refreshSearch }
        onActionButtonClick={ this.onUpdateFacilityClick }
        actionButtonText="Update Facility"
        showActionButton={ userHasRole(this.props.user, CONSTANTS.USER_ROLES.autoClaimsManager) }
        onAddNewFacilityClick={ this.onNewFacilityClick }
        selectedFacilityID={ this.state.selectedFacilityID }
        onRowSelect={ this.onRowSelect }
        isActionButtonDisable={ !this.state.selectedFacilityID }
        viewButtonDisabled={ !this.state.selectedFacilityID }
      />
    );
  }

  renderUpdateModal(){
    const {
      viewFacility,
      showUpdateModal,
      selectedFacilityID,
    } = this.state;
    
    return (
      <UpdateFacilityModal message={ `New rates will be applicable on all open claims and future claims.` }
        displayModal={ showUpdateModal }
        user={ this.props.user }
        onClose={ this.onCloseUpdateModal }
        onFacilitySelect={ this.onPositiveUpdateModal }
        viewFacility={viewFacility}
        facilityID={ showUpdateModal ? selectedFacilityID : 0 }
      />
    );
  }

  render() {
    return (
      <section className="gap-dashboard clearfix">
        <div className="row">
          <div className="col-12 mt-5">
            <PageHeader pageTitle="Facility Management" user={this.props.user}/>
            {this.renderSearchFacility()}
            {this.renderUpdateModal()}
          </div>
        </div>
        <NewFacilityModal displayModal={ this.state.showAddFacilityModal }
          onClose={ this.onCloseNewFacilityModal }
          user={ this.props.user }
          shouldLoadFacility={ false }
          onFacilitySelect={ () => {} }
          onUseCurrentFacility={ this.onCloseNewFacilityModal }/>
      </section>
    );
  }
}