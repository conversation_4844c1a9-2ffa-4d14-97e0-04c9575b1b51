import React from 'react';
import accounting from "accounting";
import If from "./../reusable/If/If.jsx";
import PropTypes from 'prop-types';
import ProductBatchPanel from "./ProductBatchPanel.jsx";
import { CONSTANTS } from "../reusable/Constants/constants";

export default class FacilityPanel extends React.Component {

  constructor(props) {
    super(props);
    this.state = {
      isExpanded: false
    };
  }

  static propTypes = {
    facilityDetails: PropTypes.object.isRequired,
    handleBatchSubmit: PropTypes.func.isRequired,
    sortClaimList: PropTypes.func,
    disabled: PropTypes.bool.isRequired
  };

  renderProductList = () => {
    if(this.props.facilityDetails.products) {
      return this.props.facilityDetails.products.map((product, index) => {
        return (
          <div className="col-12 p-0" key={ `product-panel-${product.product_code}-${index}` }
            id={ `product-panel-${index}` }>
            <ProductBatchPanel
              disabled={ this.props.disabled }
              productDetails={ product }
              handleBatchSubmit={ this.props.handleBatchSubmit }
              facilityDetails={ this.props.facilityDetails }
              sortBy={ product.sortBy }
              sortOrder={ product.sortOrder }
              sortClaimList={ this.props.sortClaimList.bind(null, index) }/>
          </div>
        );
      });
    }
  };

  render() {
    let productLength = (this.props.facilityDetails.products && this.props.facilityDetails.products.length) || 0;
    let totalClaims = this.props.facilityDetails.total_claims || 0;
    let totalAmount = this.props.facilityDetails.total_amount || 0;
    return (
      <div>
        <div className="row d-flex cursor-pointer" onClick={ () => {
          this.setState({ isExpanded: !this.state.isExpanded });
        } }>
          <div className="col-6 d-flex justify-content-start">
            <i
              className={ this.state.isExpanded ? "fa fa-caret-down fa-lg cursor-pointer text-muted" : "fa fa-caret-right fa-lg cursor-pointer text-muted" }>
              &nbsp;<strong>
                {
                  this.props.facilityDetails.pay_type === CONSTANTS.PAYMENT_TYPE_CODES.CUSTOMER ?
                    this.props.facilityDetails.customer_payee_name :
                    `${this.props.facilityDetails.facility_name} (${CONSTANTS.PAYMENT_TYPE_NAME_MAP[this.props.facilityDetails.pay_type]})`
                }
              </strong>
            </i>
          </div>
          <div className="col-6 d-flex justify-content-end">
            <span className="text-primary">
              {`${productLength} Products - ${totalClaims} Claims - ${accounting.formatMoney(totalAmount, '$', 2)}`}
            </span>
          </div>
        </div>
        <If condition={ this.state.isExpanded }>
          <div className="row">
            {this.renderProductList()}
          </div>
        </If>
      </div>
    );
  }
}
