import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import PageHeader from '../pageHeader/PageHeader.jsx';
import moment from "moment";
import DatePicker from "react-datepicker";
import dateFormat from "./../reusable/Utilities/dateFormat.js";
import Alert from "react-s-alert";
import { json as ajax } from "./../../ajax.js";
import ErrorSummary from 'components/reusable/ErrorSummary/ErrorSummary.jsx';

export default function FacilityAuditReport (props) {

  const [begin_date, setBegin] = useState(moment().subtract(7, "days"));
  const [end_date, setEnd] = useState(moment());
  const [validation_errors, setError] = useState({});
  const [export_disabled, setDisabled] = useState(false);

  useEffect(() => {
    document.title = 'TCA Portal - Automotive Facility Audit Report';
  }, [false]);

  function handleBeginDateChange(date) {
    if (!date || !date.isValid()) {
      setBegin(null);
      validation_errors.begin_date = "Begin date needs to be valid.";
      setError(validation_errors);
      setDisabled(true);
      return; 
    }

    if (end_date && end_date.isBefore(date)) {
      setBegin(date);
      validation_errors.begin_date = "End date cannot be before start date.";
      setError(validation_errors);
      setDisabled(true);
    } else {
      setBegin(date);
      delete validation_errors.begin_date;
      setError(validation_errors);
      if(!validation_errors.begin_date && !validation_errors.end_date)
        setDisabled(false);
    }
  }
  
  function handleEndDateChange(date) {
    if (!date || !date.isValid()) {
      setEnd(null);
      validation_errors.end_date = "End date needs to be valid.";
      setError(validation_errors);
      setDisabled(true);
      return; 
    }
  
    if (begin_date && date.isBefore(begin_date)) {
      setEnd(date);
      validation_errors.end_date = "End date cannot be before start date.";
      setError(validation_errors);
      setDisabled(true);
    } else {
      setEnd(date);
      delete validation_errors.end_date;
      setError(validation_errors);
      if(!validation_errors.begin_date && !validation_errors.end_date)
        setDisabled(false);
    }
  }
  
  function handleSearchByDateRange() {
    if(!begin_date || !end_date)
      return;
    
    let url = '/api/facilities/csv?begin_date=' 
    + begin_date.format(dateFormat.displayDateFormat)
    + '&end_date='
    + end_date.format(dateFormat.displayDateFormat);

    ajax(url, {}, {}, (data, status) => {
      if (status === 200) {
        window.URL = window.URL || window.webkitURL;
        const csvData = new Blob([data.facilities_csv], { type: 'text/csv' });
        const csvURL = window.URL.createObjectURL(csvData);
        const fileName = `Facilities_${moment(new Date()).format("YYYY-MM-DD_HH_mm_ss").toString()}.csv`;
        const downloadLink = document.createElement('a');
        downloadLink.setAttribute("href", csvURL);
        downloadLink.setAttribute("download", fileName);
        downloadLink.style.cssText = 'display:none';
        document.body.appendChild(downloadLink);
        downloadLink.click();
      } else {
        Alert.error("Click the browser's Refresh button to reload the data. If the error continues, contact your system administrator.");
      }
    });
  }

  function handleSubmit(e) {
    e.preventDefault();
    handleSearchByDateRange();
  }

  return (
    <div>
      <section className="gap-dashboard clearfix">
        <form onSubmit={handleSubmit}>
          <div className="row">
            <div className="col-12 mt-5">
              <PageHeader pageTitle="Facility Audit Report" user={props.user}/>
              <div className="row">
                <ErrorSummary errors={validation_errors} />
              </div>
              <div className="row">
                <div className="col-4">
                  <label style={{marginLeft: 12, marginBottom: 5}}>*Start Date</label>
                  <DatePicker className="form-control"
                    id="begin_date"
                    selected={begin_date}
                    onChange={handleBeginDateChange}
                    required={true}
                  />
                </div>
                <div className="col-4">
                  <label style={{marginLeft: 12, marginBottom: 5}}>*End Date</label>
                  <DatePicker className="form-control"
                    id="end_date"
                    selected={end_date}
                    onChange={handleEndDateChange}
                    required={true}
                  />
                </div>
                <div className="col-2">
                  <button className="btn btn-primary"
                    style={{marginTop: 28}}
                    type="submit"
                    disabled={export_disabled}>
                    <span className="fa fa-file-excel-o" />{` Export`}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </form>
      </section>
    </div>
  );
}

FacilityAuditReport.propTypes = {
  user: PropTypes.shape({
    id: PropTypes.number.isRequired,
    first_name: PropTypes.string.isRequired,
    last_name: PropTypes.string.isRequired,
    email: PropTypes.string.isRequired,
    stores: PropTypes.arrayOf(PropTypes.string.isRequired),
    roles: PropTypes.shape({
      Map: PropTypes.object,
    }).isRequired}).isRequired
};