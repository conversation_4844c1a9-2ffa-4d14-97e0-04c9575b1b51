import React from 'react';
import accounting from "accounting";
import If from "./../reusable/If/If.jsx";
import PropTypes from 'prop-types';
import SortIcon from "./../reusable/SortIcon/SortIcon.jsx";
import { CONSTANTS } from "./../reusable/Constants/constants.js";

export default class ClaimsList extends React.Component {

  constructor(props) {
    super(props);
  }

  static propTypes = {
    claimList: PropTypes.array.isRequired,
    selectedClaims: PropTypes.array.isRequired,
    isSelectedAll: PropTypes.bool.isRequired,
    selectClaim: PropTypes.func.isRequired,
    selectAll: PropTypes.func.isRequired,
    disabled: PropTypes.bool,
    sortBy: PropTypes.string,
    sortOrder: PropTypes.string,
    sortClaimList: PropTypes.func,
    isSelectMax: PropTypes.bool.isRequired,
    selectMax: PropTypes.func.isRequired,
  };

  handleSort = (toBeSorted) => {
    const { sortBy, sortOrder, sortClaimList } = this.props;
    if(toBeSorted === sortBy && sortOrder === 'asc') {
      sortClaimList(sortBy, 'desc');
    } else if(toBeSorted === sortBy && sortOrder === 'desc') {
      sortClaimList(sortBy, 'asc');
    } else {
      sortClaimList(toBeSorted, 'asc');
    }
  };

  renderCheckBoxHeader = () => {
    return (
      <input type="checkbox" value="all"
        checked={ this.props.isSelectedAll }
        onChange={ () => {
          this.props.selectAll(!this.props.isSelectedAll);
        } }
      />
    );
  };

  renderTableHeader = () => {
    const {
      sortBy,
      sortOrder
    } = this.props;
    return (
      <tr key="table-header" id="table-header">
        <If condition={ !this.props.disabled }>
          <th>
            {this.renderCheckBoxHeader()}
          </th>
        </If>
        <th className="cursor-pointer" onClick={ this.handleSort.bind(null, CONSTANTS.AUTO_CLAIM_BATCH_FIELD.CONTRACT) }>
          {"Contract # "}
          <SortIcon fieldName={ CONSTANTS.AUTO_CLAIM_BATCH_FIELD.CONTRACT }
            sortBy={ sortBy }
            sortOrder={ sortOrder }/>
        </th>
        <th onClick={ this.handleSort.bind(null, CONSTANTS.AUTO_CLAIM_BATCH_FIELD.RO) }>
          {"RO # "}
          <SortIcon fieldName={ CONSTANTS.AUTO_CLAIM_BATCH_FIELD.RO }
            sortBy={ sortBy }
            sortOrder={ sortOrder }/>
        </th>
        <th onClick={ this.handleSort.bind(null, CONSTANTS.AUTO_CLAIM_BATCH_FIELD.AMOUNT) }>
          {"$ Amount "}
          <SortIcon fieldName={ CONSTANTS.AUTO_CLAIM_BATCH_FIELD.AMOUNT }
            sortBy={ sortBy }
            sortOrder={ sortOrder }/>
        </th>
        <th onClick={ this.handleSort.bind(null, CONSTANTS.AUTO_CLAIM_BATCH_FIELD.AGENT) }>
          {"Agent "}
          <SortIcon fieldName={ CONSTANTS.AUTO_CLAIM_BATCH_FIELD.AGENT }
            sortBy={ sortBy }
            sortOrder={ sortOrder }/>
        </th>
        <th onClick={ this.handleSort.bind(null, CONSTANTS.AUTO_CLAIM_BATCH_FIELD.APPROVER) }>
          {"Approver "}
          <SortIcon fieldName={ CONSTANTS.AUTO_CLAIM_BATCH_FIELD.APPROVER }
            sortBy={ sortBy }
            sortOrder={ sortOrder }/>
        </th>
      </tr>
    );
  };

  renderTableBody = () => {
    const {
      claimList,
    } = this.props;
    return claimList && claimList.map(this.renderTableBodyRow);
  };

  renderTableBodyRow = (claimData, index) => {
    return (
      <tr key={ index } id={ claimData.id }>
        <If condition={ !this.props.disabled }>
          <td>
            {this.renderCheckBox(claimData)}
          </td>
        </If>
        <td>
          {claimData.contract_number}
        </td>
        <td>
          {claimData.ro.String}
        </td>
        <td>
          {accounting.formatMoney(claimData.estimate, '$', 2)}
        </td>
        <td>
          {claimData.auto_approved ? (claimData.claim_type === "SB" ? "SB Claim" : claimData.approver.String) : claimData.agent.String}
        </td>
        <td>
          <If condition={ !claimData.auto_approved }>
            <span>{claimData.approver.String}</span>
          </If>
          <If condition={ claimData.auto_approved }>
            <span>
              Auto Approved &nbsp;&nbsp;<i className="fa fa-check-square fa-lg text-success"/>
            </span>
          </If>
        </td>
      </tr>
    );
  };

  renderCheckBox = (claimData) => {
    return (
      <input type="checkbox" value="all"
        checked={ this.props.selectedClaims.indexOf(claimData.id) !== -1 }
        onChange={ this.props.selectClaim.bind(this, claimData.id, claimData.estimate) }/>
    );
  };

  renderSelectCheckBox = () => {
    return (
      <div style={{marginLeft: 12, marginBottom: 5}}>
        <input type="checkbox" value="all"
          checked={ this.props.isSelectMax }
          onChange={ () => {
            this.props.selectMax(!this.props.isSelectMax);
          } }
          className="mr-3"
        />
        {`Select ${CONSTANTS.MAX_BATCH_CLAIM_COUNT} Claims`}
      </div>
    );
  };

  render() {
    return (
      <div className="claim-list">
        {this.props.claimList.length > CONSTANTS.MAX_BATCH_CLAIM_COUNT && this.renderSelectCheckBox()}
        <table className="table table-striped">
          <thead>
            {this.renderTableHeader()}
          </thead>
          <tbody>
            {this.renderTableBody()}
          </tbody>
        </table>
      </div>
    );
  }
}
