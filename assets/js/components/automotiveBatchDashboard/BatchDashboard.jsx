import React from 'react';
import PageHeader from '../pageHeader/PageHeader.jsx';
import <PERSON><PERSON> from "react-s-alert";
import { json as ajax } from './../../ajax.js';
import Loader from 'react-loader-advanced';
import { URLS as apiUrls } from "./../reusable/Utilities/urls";
import SelectBox from "./../reusable/SelectBox/SelectBox.jsx";
import FacilityPanel from "./FacilityPanel.jsx";
import PropTypes from 'prop-types';
import Select from "react-select";
import { userHasRole } from '../reusable/Utilities/userHasRole';
import { notify } from '../reusable/Utilities/notify';
import { CONSTANTS } from "../reusable/Constants/constants";

export default class BatchDashboard extends React.Component {

  constructor(props) {
    super(props);
    this.state = {
      facilities: [],
      showLoader: false,
      searching: false,
      facilityOptions: [],
      typingTimeOut: 0,
      productList: this.getProductList(),
      companyGroups: [],
    };
  }

  static contextTypes = {
    router: PropTypes.object.isRequired
  };

  static propTypes = {
    user: PropTypes.shape({
      id: PropTypes.number.isRequired,
      first_name: PropTypes.string.isRequired,
      last_name: PropTypes.string.isRequired,
      email: PropTypes.string.isRequired,
      stores: PropTypes.arrayOf(PropTypes.string.isRequired),
      roles: PropTypes.shape({
        Map: PropTypes.object,
      }).isRequired,
      banner_info: PropTypes.shape({
        header: PropTypes.string.isRequired,
        message: PropTypes.string.isRequired,
        enabled: PropTypes.bool.isRequired,
      }).isRequired,
    }),
    location: PropTypes.shape({
      query: PropTypes.shape({
        Facility: PropTypes.string,
        Product: PropTypes.string,
        PayType: PropTypes.string,
        CompanyGroup: PropTypes.number,
      }).isRequired,
    }).isRequired
  };

  componentDidMount() {
    document.title = 'TCA Portal - Automotive Claims';
    this.loadData(this.props);
    this.loadSupportingData();
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (this.currentFacility(nextProps) !== this.currentFacility(this.props) ||
      this.currentProduct(nextProps) !== this.currentProduct(this.props) ||
      this.currentPayType(nextProps) !== this.currentPayType(this.props) ||
      this.currentCompanyGroup(nextProps) !== this.currentCompanyGroup(this.props)) {
      this.loadData(nextProps);
    }
  }

  populateSort = (facilities) => {
    facilities.forEach((facility) => {
      if(facility.products){
        facility.products.forEach((product) => {
          if(product.claims){
            product.claims.sort(this.sortAsc("contract"));
          }
          product["sortBy"] = "contract";
          product["sortOrder"] = "asc";
        });
      }
    });
    return facilities;
  };

  loadData = (props) => {
    let url = `${apiUrls.automotiveApprovedClaims}?`;
    if (this.currentFacility(props)) {
      url += (`&facility=${window.encodeURIComponent(this.currentFacility(props))}`);
    }
    if (this.currentProduct(props)) {
      url += (`&product=${window.encodeURIComponent(this.currentProduct(props))}`);
    }
    
    const companyGroup = this.currentCompanyGroup(props);
    if (companyGroup) {
      url += companyGroup && (`&companyGroup=${window.encodeURIComponent(companyGroup)}`);
    }

    let payType = this.currentPayType(props);

    if(!payType){
      payType = "all";
    }
    url +=`&pay_type=${window.encodeURIComponent(payType)}`;

    this.setState({ showLoader: true }, () => {
      ajax(url, {}, {}, (data, status) => {
        if (status === 200) {
          if (!Array.isArray(data.facilities)) {
            data.facilities = [];
          }
          data.facilities = this.populateSort(data.facilities);
          this.setState({ facilities: data.facilities, showLoader: false });
          notify(data.message, Alert.warning);
        } else if (status === 404) {
          notify(data.message, Alert.error);
          this.setState({
            facilities: [],
            showLoader: false
          });
        } else {
          this.setState({ showLoader: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  loadSupportingData = () => {
    let url = apiUrls.automotiveApprovedClaimsSupportingData;
    this.setState({ showLoader: true }, () => {
      ajax(url, {}, {}, (data, status) => {
        if (status === 200) {
          this.setState({ companyGroups: data.company_groups, showLoader: false });
          notify(data.message, Alert.warning);
        } else if (status === 404) {
          notify(data.message, Alert.error);
          this.setState({
            companyGroups: [],
            showLoader: false
          });
        } else {
          this.setState({ showLoader: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  onSubmit = (product, facility, claimsList, totalBatchAmount) => {
    const {
      facilities,
    } = this.state;
    
    if (claimsList && claimsList.length < 1) {
      Alert.error('Please select at least one claim to submit the batch.');
      return;
    }

    const allClaims = facilities.find(f => f.facility_id === facility.facility_id
      && f.pay_type === facility.pay_type).products.find(p => p.product_code === product.product_code).claims;
    const formattedClaimList = claimsList.map(c => {
      const selectedClaim = allClaims.find(sc => sc.id === c);
      if (selectedClaim && selectedClaim.claim_payment_id) {
        return {
          id: c,
          claim_payment_id: selectedClaim.claim_payment_id,
        };
      }
      return {
        id: c,
      };
    });

    let dataObject = {
      claim_id_list: formattedClaimList,
      facility_id: facility.facility_id,
      product_code: product.product_code,
      pay_type: facility.pay_type,
      total_amount: totalBatchAmount,
    };
    this.setState({ showLoader: true }, () => {
      ajax(apiUrls.automotiveClaimsBatchAuthorize, dataObject, { method: 'PUT' }, (data, status) => {
        if (status === 200) {
          this.setState({ showLoader: false }, () => {
            if (data.batchErrors && data.batchErrors.length > 0) {
              data.batchErrors.forEach((batchError)=>{
                Alert.warning(batchError);
              });
            } else {
              Alert.success(`#${data.batchID} ${facility.pay_type === CONSTANTS.PAYMENT_TYPE_CODES.CUSTOMER ? facility.customer_payee_name : facility.facility_name} ${data.success} Claims with total amount $${totalBatchAmount} submitted successfully`);
            }
            this.loadData(this.props);
          });
        } else {
          this.setState({ showLoader: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  currentFacility = (props) => {
    return props.location.query.Facility;
  };

  currentProduct = (props) => {
    return props.location.query.Product;
  };

  currentPayType = (props) => {
    return props.location.query.PayType;
  };

  currentCompanyGroup = (props) => {
    return props.location.query.CompanyGroup;
  };

  searchFacilities = () => {
    this.setState({ searching: true }, function () {
      ajax(`${apiUrls.facilityList}?search=${encodeURIComponent(this.state.facilityString)}`, {}, {}, (data, status) => {
        if (status === 200) {
          let facilityList = [];
          if (Array.isArray(data.facilities)) {
            facilityList = data.facilities;
          }
          this.setState({
            facilityOptions: facilityList.map(element => {
              return { label: `${element.name} - ${element.facility_code}`, value: element.id };
            }),
            searching: false
          });
        } else {
          this.setState({ searching: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  updateFilters = (query) => {
    const route = { pathname: "/automotive-batch-dashboard", query };
    this.context.router.push(route);
  };

  onFacilitySelection = (facilityObject) => {
    let query = {};
    if (this.currentProduct(this.props)) {
      query.Product = this.currentProduct(this.props);
    }

    const companyGroup = this.currentCompanyGroup(this.props);
    if (companyGroup) {
      query.CompanyGroup = companyGroup;
    }

    const payType = this.currentPayType(this.props);
    if(payType){
      query.PayType = payType;
    }

    if (facilityObject) {
      query.Facility = facilityObject.value;
    }
    this.updateFilters(query);
  };

  updateFacility = (facilityString) => {
    if (this.state.typingTimeOut) {
      clearTimeout(this.state.typingTimeOut);
    }
    this.setState({
      facilityString: facilityString,
      typingTimeOut: setTimeout(() => {
        if (this.state.facilityString && this.state.facilityString.length > 3) {
          this.searchFacilities();
        }
      }, 500)
    });
  };

  sortAsc = (sortBy) => {
    if(sortBy === CONSTANTS.AUTO_CLAIM_BATCH_FIELD.AGENT ||
        sortBy === CONSTANTS.AUTO_CLAIM_BATCH_FIELD.APPROVER ||
        sortBy === CONSTANTS.AUTO_CLAIM_BATCH_FIELD.RO){
      return function(claimA, claimB){
        if(claimA[sortBy].String < claimB[sortBy].String){
          return -1;
        } else if(claimA[sortBy].String > claimB[sortBy].String){
          return 1;
        } else{
          return 0;
        }
      };
    } else if(sortBy === CONSTANTS.AUTO_CLAIM_BATCH_FIELD.CONTRACT){
      return function(claimA, claimB){
        if(claimA["contract_number"] < claimB["contract_number"]){
          return -1;
        } else if(claimA["contract_number"] > claimB["contract_number"]){
          return 1;
        } else{
          return 0;
        }
      };
    } else if(sortBy === CONSTANTS.AUTO_CLAIM_BATCH_FIELD.AMOUNT){
      return function(claimA, claimB){
        if(parseFloat(claimA[sortBy]) < parseFloat(claimB[sortBy])){
          return -1;
        } else if(parseFloat(claimA[sortBy]) > parseFloat(claimB[sortBy])){
          return 1;
        } else{
          return 0;
        }
      };
    }
  };

  sortDesc = (sortBy) => {
    if(sortBy === CONSTANTS.AUTO_CLAIM_BATCH_FIELD.AGENT ||
        sortBy === CONSTANTS.AUTO_CLAIM_BATCH_FIELD.APPROVER ||
        sortBy === CONSTANTS.AUTO_CLAIM_BATCH_FIELD.RO){
      return function(claimA, claimB){
        if(claimA[sortBy].String > claimB[sortBy].String){
          return -1;
        } else if(claimA[sortBy].String < claimB[sortBy].String){
          return 1;
        } else{
          return 0;
        }
      };
    } else if(sortBy === CONSTANTS.AUTO_CLAIM_BATCH_FIELD.CONTRACT){
      return function(claimA, claimB){
        if(claimA["contract_number"] > claimB["contract_number"]){
          return -1;
        } else if(claimA["contract_number"] < claimB["contract_number"]){
          return 1;
        } else{
          return 0;
        }
      };
    } else if(sortBy === CONSTANTS.AUTO_CLAIM_BATCH_FIELD.AMOUNT){
      return function(claimA, claimB){
        if(parseFloat(claimA[sortBy]) > parseFloat(claimB[sortBy])){
          return -1;
        } else if(parseFloat(claimA[sortBy]) < parseFloat(claimB[sortBy])){
          return 1;
        } else{
          return 0;
        }
      };
    }
  };

  handleSortFacility = (facilityIndex, productIndex, sortBy, sortOrder) => {
    let { facilities } = this.state;
    let facility = facilities[facilityIndex];
    let product;
    if(facility.products){
      product = facility.products[productIndex];
    }

    if(product.claims){
      product.sortBy = sortBy;
      product.sortOrder = sortOrder;
      if(sortOrder === "asc"){
        product.claims.sort(this.sortAsc(sortBy));
      } else if(sortOrder === "desc"){
        product.claims.sort(this.sortDesc(sortBy));
      }
    }

    this.setState({
      facilities
    });
  };

  renderFacilityList = () => {
    if (this.state.facilities.length > 0) {
      return this.state.facilities.map((facility, index) => {
        return (
          <div className="col mt-3" key={ `${facility.facility_id}-${index}` } id={ `facility-panel-${index}` }>
            <FacilityPanel facilityDetails={ facility }
              handleBatchSubmit={ this.onSubmit }
              sortClaimList={ this.handleSortFacility.bind(null, index) }
              disabled={ !userHasRole(this.props.user, CONSTANTS.USER_ROLES.accounting) }
            />
          </div>
        );
      });
    } else {
      return (
        <div className="text-center">
          <p>No Results available for this search query</p>
        </div>
      );
    }

  };

  onProductSelection = (value) => {
    let query = {};
    if (this.currentFacility(this.props)) {
      query.Facility = this.currentFacility(this.props);
    }

    const companyGroup = this.currentCompanyGroup(this.props);
    if (companyGroup) {
      query.CompanyGroup = companyGroup;
    }

    const payType = this.currentPayType(this.props);

    if(payType){
      query.PayType = payType;
    }

    if (value) {
      query.Product = value;
    }
    this.updateFilters(query);
  };

  onPayTypeSelection = (value) => {
    let query = {};

    const facility = this.currentFacility(this.props);
    if (facility) {
      query.Facility = facility;
    }

    const product = this.currentProduct(this.props);
    if(product){
      query.Product = product;
    }

    const companyGroup = this.currentCompanyGroup(this.props);
    if (companyGroup) {
      query.CompanyGroup = companyGroup;
    }

    if (value) {
      query.PayType = value;
    }

    this.updateFilters(query);
  };

  onCompanyGroupSelection = (value) => {
    let query = {};

    const facility = this.currentFacility(this.props);
    if (facility) {
      query.Facility = facility;
    }

    const product = this.currentProduct(this.props);
    if(product) {
      query.Product = product;
    }

    const payType = this.currentPayType(this.props);
    if(payType) {
      query.PayType = payType;
    }
    
    if (value) {
      query.CompanyGroup = value;
    }

    this.updateFilters(query);
  };

  getProductList = () => {
    let productList = Object.keys(CONSTANTS.PRODUCT_CODE_NAME_MAP).map((element) => {
      return { name: CONSTANTS.PRODUCT_CODE_NAME_MAP[element], value: element };
    });
    productList.unshift({ name: "All", value: "" });
    return productList;
  };
  
  companyGroupList = () => {
    let companyGroups =  !this.state.showLoader && 
      this.state.companyGroups.length > 0 ?
      this.state.companyGroups.map((group) => {
        return { name: group.name, value: group.id };
      }) : [];

    companyGroups.unshift({ name: "All", value: "" });
    
    return companyGroups;
  }

  filterOptions = (options, filter, currentValues) => {
    // If there are not any options available yet, then
    // make the current user input available as an option.
    if (filter !== '' && options.length === 0) {
      return [{ value: -1, label: filter }];
    }
    return options;
  }

  renderBatchFilters = () => {
    return (
      <div className="form-group row px-4">
        <div className="col-1">
          <label htmlFor="facility-name" className="pr-1">Facility: </label>
        </div>
        <div className="col-2">
          <Select id="facility-name"
            value={ parseInt(this.props.location.query.Facility || 0) }
            isLoading={ this.state.searching }
            options={ this.state.facilityOptions }
            onChange={ this.onFacilitySelection }
            onInputChange={ this.updateFacility }
            filterOptions={ this.filterOptions }
            tabSelectsValue={ false }
            autosize={ true }/>
        </div>
        <div className="col-1">
          <label htmlFor="product-code" className="pr-1">Product: </label>
        </div>
        <div className="col-2">
          <SelectBox
            id="product-code-dropdown"
            value={ this.props.location.query.Product }
            onChange={ this.onProductSelection }
            optionsList={ this.state.productList }
            customClassName="form-control-sm"/>
        </div>
        <div className="col-1">
          <label htmlFor="pay-type" className="pr-1">Pay Type: </label>
        </div>
        <div className="col-2">
          <SelectBox
            id="product-code-dropdown"
            value={ this.props.location.query.PayType }
            onChange={ this.onPayTypeSelection }
            optionsList={ [
              {name: "All", value: "all"},
              {name: CONSTANTS.PAYMENT_TYPE_NAMES.CREDIT_CARD, value: CONSTANTS.PAYMENT_TYPE_CODES.CREDIT_CARD},
              {name: CONSTANTS.PAYMENT_TYPE_NAMES.CUSTOMER, value: CONSTANTS.PAYMENT_TYPE_CODES.CUSTOMER},
              {name: CONSTANTS.PAYMENT_TYPE_NAMES.STORE, value: CONSTANTS.PAYMENT_TYPE_CODES.STORE}
            ] }
            customClassName="form-control-sm"/>
        </div>
        <div className="col-1">
          <label htmlFor="company-group" className="pr-1">Company Group: </label>
        </div>
        <div className="col-2">
          <SelectBox id="company-group-dropdown"
            value={ this.props.location.query.CompanyGroup || 0 }
            optionsList={ this.companyGroupList() }
            onChange={ this.onCompanyGroupSelection }
            customClassName="form-control-sm"/>
        </div>
      </div>
    );
  };

  render() {
    const style = { minHeight: '500px' };
    const spinnerMessage = <p className='text-center'><i className="fa fa-refresh fa-spin"/> Loading...</p>;
    return (
      <Loader show={ this.state.showLoader } message={ spinnerMessage } style={ style }>
        <section className="gap-dashboard clearfix">
          <div className="row">
            <div className="col-12 mt-5">
              <PageHeader pageTitle="Submit Approved Automotive Claims"
                renderTemplate={ this.renderBatchFilters }
                user={this.props.user}/>
              {this.renderFacilityList()}
            </div>
          </div>
        </section>
      </Loader>
    );
  }
}
