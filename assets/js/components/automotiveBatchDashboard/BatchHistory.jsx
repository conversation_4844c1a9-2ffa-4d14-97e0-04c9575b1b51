import React from 'react';
import PageHeader from '../pageHeader/PageHeader.jsx';
import Al<PERSON> from 'react-s-alert';
import { json as ajax } from './../../ajax.js';
import Loader from 'react-loader-advanced';
import { URLS as apiUrls } from "./../reusable/Utilities/urls";
import { CONSTANTS } from "./../reusable/Constants/constants.js";
import accounting from "accounting";
import dateFormat from "./../reusable/Utilities/dateFormat.js";
import moment from 'moment';
import SortIcon from "./../reusable/SortIcon/SortIcon.jsx";
import Select from "react-select";
import PropTypes from 'prop-types';
import Pagination from "../../Pagination";

const PAGE_LIMIT = 20;

export default class BatchHistory extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      batchList: [],
      numberOfBatches: 0,
      pageLimit: PAGE_LIMIT,
      showLoader: false,
      searching: false,
      facilityString: "",
      facilityList: [],
      typingTimeOut: 0,
      productList: this.getProductList()
    };
  }

  static contextTypes = {
    router: PropTypes.object.isRequired
  };

  static propTypes = {
    user: PropTypes.shape({
      id: PropTypes.number.isRequired,
      first_name: PropTypes.string.isRequired,
      last_name: PropTypes.string.isRequired,
      email: PropTypes.string.isRequired,
      stores: PropTypes.arrayOf(PropTypes.string.isRequired),
      roles: PropTypes.shape({
        Map: PropTypes.object,
      }).isRequired,
    }),
    location: PropTypes.shape({
      query: PropTypes.shape({
        DateSortOrder: PropTypes.string,
        Facility: PropTypes.string,
        Product: PropTypes.string
      }).isRequired,
    }).isRequired
  };

  componentDidMount() {
    document.title = 'TCA Portal - Automotive Claims';
    this.loadBatchList();
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (this.currentSortOrder(nextProps) !== this.currentSortOrder(this.props) ||
      this.currentSortBy(nextProps) !== this.currentSortBy(this.props) ||
      this.currentFacility(nextProps) !== this.currentFacility(this.props) ||
      this.currentProduct(nextProps) !== this.currentProduct(this.props) ||
      this.currentPage(nextProps) !== this.currentPage(this.props)) {
      this.loadBatchList(nextProps);
    }
  }

  getProductList = () => {
    return Object.keys(CONSTANTS.PRODUCT_CODE_NAME_MAP).map((element) => {
      return { label: CONSTANTS.PRODUCT_CODE_NAME_MAP[ element ], value: element };
    });
  };

  loadBatchList = (props) => {
    if (!props) {
      props = this.props;
    }
    let url = `${apiUrls.automotiveBatchHistory}?page=${window.encodeURIComponent(this.currentPage(props))}`;
    const sortBy = this.currentSortBy(props);
    if (sortBy) {
      url += (`&sort_by=${window.encodeURIComponent(sortBy)}`);
    }
    const sortOrder = this.currentSortOrder(props);
    if (sortOrder) {
      url += (`&sort_order=${window.encodeURIComponent(sortOrder)}`);
    }
    if (this.currentFacility(props)) {
      url += (`&facility=${window.encodeURIComponent(this.currentFacility(props))}`);
    }
    if (this.currentProduct(props)) {
      url += (`&product=${window.encodeURIComponent(this.currentProduct(props))}`);
    }
    this.setState({ showLoader: true }, () => {
      ajax(url, {}, {}, (data, status) => {
        if (status === 200) {
          this.setState({
            batchList: data.batchList,
            numberOfBatches: data.count,
            showLoader: false
          });
        } else if (status === 400) {
          this.setState({ showLoader: false }, () => {
            Alert.warning(data.message);
          });
        } else {
          this.setState({ showLoader: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  currentSortOrder = (props) => {
    return props.location.query.SortOrder || '';
  };

  currentSortBy = (props) => {
    return props.location.query.SortBy || '';
  };

  currentFacility = (props) => {
    return props.location.query.Facility;
  };

  currentProduct = (props) => {
    return props.location.query.Product;
  };

  handleSort = (sortBy) => {
    const currentSortBy = this.currentSortBy(this.props);
    const currentSortOrder = this.currentSortOrder(this.props);
    let sortOrder = 'desc';
    const Facility = this.currentFacility(this.props);
    const Product = this.currentProduct(this.props);
    if (currentSortBy === sortBy) {
      if(currentSortOrder && currentSortOrder === "desc") {
        sortOrder = "asc";
      }
    }
    const query = {
      SortOrder: sortOrder,
      SortBy: sortBy,
      Facility,
      Product,
      page : this.currentPage()
    };
    this.updateBatchListQuery(query);
  };

  updateBatchListQuery = (query) => {
    const route = { pathname: "/automotive-batch-history", query };
    this.context.router.push(route);
  };

  renderTableHeader = () => {
    const sortOrder = this.currentSortOrder(this.props);
    const sortBy = this.currentSortBy(this.props);
    return (
      <tr>
        <th className="cursor-pointer"
          onClick={ this.handleSort.bind(this, "created_at") }
        >Batch Date&nbsp;
          <SortIcon fieldName='created_at'
            sortBy={ sortBy }
            sortOrder={ sortOrder }/>
        </th>
        <th className="cursor-pointer"
          onClick={ this.handleSort.bind(this, "id") }
        >Name&nbsp;
          <SortIcon fieldName='id'
            sortBy={ sortBy }
            sortOrder={ sortOrder }/>
        </th>
        <th className="cursor-pointer"
          onClick={ this.handleSort.bind(this, "facility") }
        >Facility&nbsp;
          <SortIcon fieldName='facility'
            sortBy={ sortBy }
            sortOrder={ sortOrder }/></th>
        <th className="cursor-pointer"
          onClick={ this.handleSort.bind(this, "amount") }
        >$ Amount&nbsp;
          <SortIcon fieldName='amount'
            sortBy={ sortBy }
            sortOrder={ sortOrder }/>
        </th>
        <th className="cursor-pointer"
          onClick={ this.handleSort.bind(this, "check_number") }
        >Check #&nbsp;
          <SortIcon fieldName='check_number'
            sortBy={ sortBy }
            sortOrder={ sortOrder }/>
        </th>
        <th className="cursor-pointer"
          onClick={ this.handleSort.bind(this, "pay_type") }
        >Pay Type&nbsp;
          <SortIcon fieldName='pay_type'
            sortBy={ sortBy }
            sortOrder={ sortOrder }/>
        </th>
        <th className="cursor-pointer"
          onClick={ this.handleSort.bind(this, "status") }
        >Status#&nbsp;
          <SortIcon fieldName='status'
            sortBy={ sortBy }
            sortOrder={ sortOrder }/>
        </th>
        <th>Notes</th>
      </tr>
    );
  };

  renderTableBody = () => {
    return this.state.batchList.map(this.renderTableBodyRow);
  };

  renderTableBodyRow = (batchData, index) => {
    return (
      <tr key={ index }>
        <td>{moment(batchData.created_at, dateFormat.backendDateFormat).format(dateFormat.displayDateFormat)}</td>
        <td>
          <a href="#!"
            className="users"
            onClick={ this.redirectToBatchDetails.bind(this, batchData.id) }>
            {batchData.id}
          </a>
        </td>
        <td>{batchData.facility}</td>
        <td>
          {accounting.formatMoney(batchData.amount, '$', 2)}
        </td>
        <td>{batchData.check_number ? batchData.check_number : ""}</td>
        <td>{CONSTANTS.PAYMENT_TYPE_NAME_MAP[batchData.pay_type]}</td>
        <td>{batchData.status}</td>
        <td>{batchData.note}</td>
      </tr>
    );
  };

  renderBatchList = () => {
    if (this.state.batchList && this.state.batchList.length > 0) {
      return (
        <div className="claim-list">
          <table className="table table-striped" id="batch-list">
            <thead>{this.renderTableHeader()}</thead>
            <tbody>{this.renderTableBody()}</tbody>
          </table>
        </div>);
    } else {
      return (
        <div className="text-center">
          <p>No batches found</p>
        </div>
      );
    }
  };

  redirectToBatchDetails = (batchID) => {
    const route = { pathname: "/automotive-batch-claims/" + batchID };
    if (!this.context.router.isActive(route)) {
      this.context.router.push(route);
    }
  };

  onFacilitySelection = (facilityObject) => {
    let query = { page : 1 };
    const sortBy = this.currentSortBy(this.props);
    if (sortBy) {
      query.SortBy = sortBy;
    }
    const sortOrder = this.currentSortOrder(this.props);
    if(sortOrder){
      query.SortOrder = sortOrder;
    }
    if (this.currentProduct(this.props)) {
      query.Product = this.currentProduct(this.props);
    }
    if (facilityObject) {
      query.Facility = facilityObject.value;
    }
    this.updateBatchListQuery(query);
  };

  updateFacility = (facilityString) => {
    if (this.state.typingTimeOut) {
      clearTimeout(this.state.typingTimeOut);
    }
    this.setState({
      facilityString: facilityString,
      typingTimeOut: setTimeout(() => {
        if (this.state.facilityString && this.state.facilityString.length > 3) {
          this.searchFacilities();
        }
      }, 500)
    });
  };

  searchFacilities = () => {
    this.setState({ searching: true }, function () {
      ajax(`${apiUrls.facilityList}?search=${encodeURIComponent(this.state.facilityString)}`, {}, {}, (data, status) => {
        if (status === 200) {
          let facilityList = [];
          if (Array.isArray(data.facilities)) {
            facilityList = data.facilities;
          }
          this.setState({
            facilityList: facilityList.map(element => {
              return { label: `${element.name} - ${element.facility_code}`, value: element.id };
            }),
            searching: false
          });
        } else {
          this.setState({ searching: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  onProductSelection = (productObject) => {
    let query = { page : 1 };
    const sortBy = this.currentSortBy(this.props);
    if (sortBy) {
      query.SortBy = sortBy;
    }
    const sortOrder = this.currentSortOrder(this.props);
    if(sortOrder){
      query.SortOrder = sortOrder;
    }
    if (this.currentFacility(this.props)) {
      query.Facility = this.currentFacility(this.props);
    }
    if (productObject) {
      query.Product = productObject.value;
    }
    this.updateBatchListQuery(query);
  };

  setPage = (page) => {
    const query = { page };
    const sortBy = this.currentSortBy(this.props);
    if (sortBy) {
      query.SortBy = sortBy;
    }
    const sortOrder = this.currentSortOrder(this.props);
    if(sortOrder){
      query.SortOrder = sortOrder;
    }
    if (this.currentFacility(this.props)) {
      query.Facility = this.currentFacility(this.props);
    }
    if (this.currentProduct(this.props)) {
      query.Product = this.currentProduct(this.props);
    }
    this.updateBatchListQuery(query);
  };

  filterOptions = (options, filter, currentValues) => {
    // If there are not any options available yet, then
    // make the current user input available as an option.
    if (filter !== '' && options.length === 0) {
      return [{ value: -1, label: filter }];
    }
    return options;
  }

  renderFilters = () => {
    return (
      <div className="form-group row px-4">
        <div className="col-1">
          <label htmlFor="facility-name" className="pr-1">Facility: </label>
        </div>
        <div className="col-4">
          <Select id="facility-name"
            value={ parseInt(this.props.location.query.Facility || 0) }
            isLoading={ this.state.searching }
            options={ this.state.facilityList }
            onChange={ this.onFacilitySelection }
            onInputChange={ this.updateFacility }
            filterOptions={ this.filterOptions }
            tabSelectsValue={ false }
            matchProp='label'
            autosize={ true }/>
        </div>
        <div className="col-1">
          <label htmlFor="product-code" className="pr-1">Product: </label>
        </div>
        <div className="col-2">
          <Select id="product-code"
            value={ this.props.location.query.Product }
            isLoading={ this.state.searching }
            options={ this.state.productList }
            onChange={ this.onProductSelection }
            tabSelectsValue={ false }
            matchProp='label'
            autosize={ true }/>
        </div>
      </div>
    );
  };

  currentPage = (props) => {
    if (!props) {
      props = this.props;
    }
    return parseInt(props.location.query.page || 1, 10);
  };

  renderPagination = () => {
    if (this.state.numberOfBatches > this.state.pageLimit) {
      return (
        <div className="pagination-container clearfix col-12 px-0">
          <div className="pull-left my-1">
            <p>Showing {this.currentPage() * this.state.pageLimit - this.state.pageLimit + 1}&nbsp;
              to {this.currentPage() * this.state.pageLimit > this.state.numberOfBatches ? this.state.numberOfBatches : this.currentPage() * this.state.pageLimit}&nbsp;
              of {this.state.numberOfBatches} items
            </p>
          </div>
          <div className="float-right">
            <Pagination page={ this.currentPage() } count={ this.state.numberOfBatches }
              limit={ this.state.pageLimit } setPage={ this.setPage }/>
          </div>
        </div>
      );
    }
  };
  
  render() {
    const spinnerMessage = <p className='text-center'><i className="fa fa-refresh fa-spin"/> Loading...</p>;
    return (
      <Loader show={ this.state.showLoader } message={ spinnerMessage }>
        <section className="gap-dashboard clearfix">
          <div className="row">
            <div className="col-12">
              <PageHeader pageTitle="Automotive Batch History" user={this.props.user}/>
              {this.renderFilters()}
              {this.renderBatchList()}
              {this.renderPagination()}
            </div>
          </div>
        </section>
      </Loader>
    );
  }
}