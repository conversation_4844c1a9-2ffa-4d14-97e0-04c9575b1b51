import React from 'react';
import PageHeader from '../pageHeader/PageHeader.jsx';
import <PERSON><PERSON> from 'react-s-alert';
import { json as ajax } from './../../ajax.js';
import Loader from 'react-loader-advanced';
import { URLS as apiUrls } from "./../reusable/Utilities/urls";
import { CONSTANTS } from "./../reusable/Constants/constants";
import dateFormat from "./../reusable/Utilities/dateFormat.js";
import If from "./../reusable/If/If.jsx";
import moment from 'moment';
import accounting from "accounting";
import PropTypes from 'prop-types';

export default class AutomotiveBatchDetails extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      batchDetails: {
        created_at: undefined,
        amount: undefined,
        vendor_id: undefined,
        facility_name: "",
        product_code: "",
        total_claims: undefined,
        batch_claims: []
      },
      showLoader: false
    };
  }

  static contextTypes = {
    router: PropTypes.object.isRequired
  };

  static propTypes = {
    params: PropTypes.shape({
      id: PropTypes.string.isRequired
    }).isRequired,
    user: PropTypes.shape({
      id: PropTypes.number.isRequired,
      first_name: PropTypes.string.isRequired,
      last_name: PropTypes.string.isRequired,
      email: PropTypes.string.isRequired,
      stores: PropTypes.arrayOf(PropTypes.string.isRequired),
      roles: PropTypes.shape({
        Map: PropTypes.object,
      }).isRequired
    }),
    location: PropTypes.shape({
      query: PropTypes.shape({
        ROSortOrder: PropTypes.string
      }).isRequired,
    }).isRequired
  };

  componentDidMount() {
    document.title = 'TCA Portal - Automotive Claims';
    this.loadBatchData(this.props);
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (this.currentROSortOrder(nextProps) !== this.currentROSortOrder(this.props)) {
      this.loadBatchData(nextProps);
    }
  }

  loadBatchData = (props) => {
    let url = apiUrls.automotiveBatchDetails.replace('__batchId__', props.params.id);
    if (this.currentROSortOrder(props)) {
      url += (`?order_by_ro=${window.encodeURIComponent(this.currentROSortOrder(props))}`);
    }
    this.setState({ showLoader: true }, () => {
      ajax(url, {}, {}, (data, status) => {
        if (status === 200) {
          this.setState({
            batchDetails: data.batchDetails,
            showLoader: false
          });
        } else {
          this.setState({ showLoader: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  currentROSortOrder = (props) => {
    return props.location.query.ROSortOrder;
  };

  sortClaimListByRO = () => {
    let query;
    if (this.currentROSortOrder(this.props) === 'asc') {
      query = { ROSortOrder: 'desc' };
    } else {
      query = { ROSortOrder: 'asc' };
    }
    const route = { pathname: "/automotive-batch-claims/" + this.props.params.id, query };
    if (!this.context.router.isActive(route)) {
      this.context.router.push(route);
    }
  };

  renderTableHeaders = () => {
    return (
      <tr className="row">
        <th className="cursor-pointer col-2" onClick={ this.sortClaimListByRO }>
          RO #&nbsp;
          <If condition={ this.currentROSortOrder(this.props) === 'asc' }>
            <i className="fa fa-sort-asc"/>
          </If>
          <If condition={ this.currentROSortOrder(this.props) === 'desc' }>
            <i className="fa fa-sort-desc"/>
          </If>
          <If condition={ !this.currentROSortOrder(this.props) }>
            <i className="fa fa-sort"/>
          </If>
        </th>
        <th className="col-3">Contract #</th>
        <th className="col-2">$ Amount</th>
        <th className="col-5">Notes</th>
      </tr>
    );
  };

  renderTableRows = () => {
    return this.state.batchDetails.batch_claims.map((claim) => {
      return (
        <tr className="row" key={ claim.automotive_claim_id } id={ claim.automotive_claim_id }>
          <td className="col-2">{claim.ro}</td>
          <td className="col-3">{claim.contract_number}</td>
          <td className="col-2">{claim.amount}</td>
          <td className="col-5">{claim.note}</td>
        </tr>
      );
    });
  };

  renderClaimList = () => {
    if (this.state.batchDetails.batch_claims.length > 0) {
      return (
        <div className="row">
          <div className="claim-list col-12">
            <table className="table table-striped" id="batch-claim-list">
              <thead>{this.renderTableHeaders()}</thead>
              <tbody>{this.renderTableRows()}</tbody>
            </table>
          </div>
        </div>
      );
    } else {
      return (
        <div className="text-center">
          <p>No Results available for this batch</p>
        </div>
      );
    }
  };

  renderBatchDetails = () => {

    const batchDetails = this.state.batchDetails;
    if (!batchDetails.facility_name) {
      return;
    }
    return (
      <div className="row mt-3 mb-3">
        <strong className="col-4 text-left">{`${batchDetails.facility_name} (${CONSTANTS.PAYMENT_TYPE_NAME_MAP[batchDetails.pay_type]})`}</strong>
        <div className="col-4 text-center text-muted">
          {`${CONSTANTS.PRODUCT_CODE_NAME_MAP[batchDetails.product_code]} - ${batchDetails.total_claims} Claims - ${accounting.formatMoney(batchDetails.amount, '$', 2)}`}
        </div>
        <div className="col-4 text-right">
          <If condition={batchDetails.check_number}>
            <span className="row">{`Check #: ${batchDetails.check_number}`}</span>
          </If>
          <span className="row">{`Vendor #: ${batchDetails.vendor_id.String}`}</span>
        </div>
      </div>
    );
  };

  onExport = () => {
    const props = this.props;
    let url = `${apiUrls.automotiveBatchDetails.replace('__batchId__', props.params.id)}?csv=true`;
    if (this.currentROSortOrder(props)) {
      url += (`&order_by_ro=${window.encodeURIComponent(this.currentROSortOrder(props))}`);
    }
    this.setState({ showLoader: true }, () => {
      ajax(url, {}, {}, (data, status) => {
        if (status === 200) {
          this.setState({ showLoader: false });
          window.URL = window.URL || window.webkitURL;
          const csvData = new Blob([data.batchDetails], { type: 'text/csv' });
          const csvURL = window.URL.createObjectURL(csvData);
          const fileName = `Batch Details - ${this.state.batchDetails.facility_name} - ${this.props.params.id} - ${moment(this.state.batchDetails.created_at, dateFormat.backendDateFormat).format(dateFormat.displayDateFormat)}.csv`;
          const downloadLink = document.createElement('a');
          downloadLink.setAttribute("href", csvURL);
          downloadLink.setAttribute("download", fileName);
          downloadLink.style.cssText = 'display:none';
          document.body.appendChild(downloadLink);
          downloadLink.click();
        } else {
          this.setState({ showLoader: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  renderExportButton = () => {
    return (
      <button onClick={ this.onExport }
        className="btn btn-secondary ml-4"
        id="btn-export-batch-details">
        <i className="fa fa-file-excel-o"/>
        &nbsp;Export
      </button>
    );
  };

  render() {
    const spinnerMessage = <p className='text-center'><i className="fa fa-refresh fa-spin"/> Loading...</p>;
    return (
      <Loader show={ this.state.showLoader } message={ spinnerMessage }>
        <section className="gap-dashboard clearfix">
          <div className="row">
            <div className="col-12">
              <PageHeader
                pageTitle={ `Batch Details - ${this.state.batchDetails.facility_name} - ${this.props.params.id} - ${moment(this.state.batchDetails.created_at, dateFormat.backendDateFormat).format(dateFormat.displayDateFormat)}` }
                backButtonText="Back"
                onBackButtonClick={ this.context.router.goBack }
                user={this.props.user}
                renderTemplate={ this.renderExportButton }/>
              { this.renderBatchDetails() }
              {this.renderClaimList()}
            </div>
          </div>
        </section>
      </Loader>
    );
  }

}
