import React from "react";
import PageHeader from "../pageHeader/PageHeader.jsx";
import SearchBox from "../reusable/SearchBox/Search.jsx";
import Alert from "react-s-alert";
import ClaimsList from "./ClaimsList.jsx";
import { json as ajax, raw as ajaxRaw } from "./../../ajax.js";
import Pagination from "./../../Pagination.jsx";
import Loader from "react-loader-advanced";
import moment from "moment";
import { URLS as apiUrls } from "./../reusable/Utilities/urls";
import { userHasRole } from "./../reusable/Utilities/userHasRole";
import { CONSTANTS } from "./../reusable/Constants/constants";
import DonutChart from "./../reusable/Charts/DonutChart/DonutChart.jsx";
import PropTypes from "prop-types";
import * as d3 from "d3";

export default class VTAClaimsDashboard extends React.Component {

  constructor(props) {
    super(props);
    this.state = {
      claimList: [],
      numberOfClaims: undefined,
      pageLimit: 20,
      userList: [],
      claimOwners: [],
      showLoader: false,
      showChartForStatusLoader: false,
      claimsCountByStatus: [],
      showChartForAgentsLoader: false,
      claimsCountByAgents: [],
      showChartForAgeLoader: false,
      claimsCountByAge: [],
      averageWaitingAge: '',
      selectedChartLegendFilter: ''
    };
  }

  componentDidMount() {
    document.title = 'TCA Portal - VTA Claims';
    this.loadClaimData();
    this.getClaimOwnersList();
    this.setState({
      showChartForStatusLoader: true,
      showChartForAgentsLoader: true,
      showChartForAgeLoader: true
    }, () => {
      if (userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaimsManager) ||
      userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaims) ||
      userHasRole(this.props.user, CONSTANTS.USER_ROLES.viewOnlyClaims)
      ) {
        this.loadClaimsCountByStatus();
        this.loadClaimsCountByAgents();
        this.loadClaimsCountByAge();
      }
    });
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (this.currentPage(nextProps) !== this.currentPage(this.props) ||
      this.currentQ(nextProps) !== this.currentQ(this.props) ||
      this.currentStatus(nextProps) !== this.currentStatus(this.props) ||
      this.currentUserId(nextProps) !== this.currentUserId(this.props) ||
      this.currentClaimAge(nextProps) !== this.currentClaimAge(this.props) ||
      this.currentSortOrder(nextProps) !== this.currentSortOrder(this.props) ||
      this.currentSortBy(nextProps) !== this.currentSortBy(this.props)) {
      this.setState({ q: nextProps.location.query.q || "" });
      this.loadClaimData(nextProps);
    }
  }

  /**
   * This function will fetch claim data from backend based on fitlers provided.
   *
   * filters:
   *  q :
   *      Type: {String}
   *      Description: Search query.
   *  page:
   *      Type : {Number}
   *      Description: Page number to provide pagination support.
   *  user_id :
   *      Type: {Number}
   *      Description: To fetch claims list based on claim owner.
   *  status:
   *      Type: {String}
   *      Values: (['All Active', 'In Inquiry', 'In Process', 'In Review', 'In Finance', 'Closed']);
   *      All Active" : All Active status.
   *      In Inquiry: 'I'
   *      In Process: 'P', 'RO-RP', 'RC'.
   *      In Review: 'RC',
   *      In Finance: 'WC', 'WP',
   *      Closed: 'D', 'C-NR', 'NG', 'C'
   *      Description: To handle filtering based on status of claim.
   *  Age: Claim Age
   *  Sort by: Sort by column
   *  Sort order: sorting order
   *
   * */
  loadClaimData = (props) => {
    if (!props) {
      props = this.props;
    }
    let url = `${apiUrls.vtaclaims}?page=${window.encodeURIComponent(this.currentPage(props))}`;
    if (this.currentQ(props)) {
      url += (`&q=${window.encodeURIComponent(this.currentQ(props).trim())}`);
    }
    if (this.currentStatus(props)) {
      url += (`&status=${window.encodeURIComponent(this.currentStatus(props))}`);
    }
    if (this.currentUserId(props)) {
      url += (`&user_id=${window.encodeURIComponent(this.currentUserId(props))}`);
    }
    if (this.currentClaimAge(props)) {
      url += (`&age=${window.encodeURIComponent(this.currentClaimAge(props))}`);
    }
    if(this.currentSortBy(props)) {
      url += (`&sort_by=${window.encodeURIComponent(this.currentSortBy(props))}`);
    }
    if(this.currentSortOrder(props)) {
      url += (`&sort_order=${window.encodeURIComponent(this.currentSortOrder(props))}`);
    }
    this.setState({ showLoader: true }, () => {
      ajax(url, {}, {}, (data, status) => {
        if (status === 200) {
          this.setState({
            claimList: data.vta_claims,
            numberOfClaims: data.count,
            showLoader: false
          });
        } else {
          this.setState({ showLoader: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  loadClaimsCountByStatus = () => {
    ajax(apiUrls.vtaClaimsCountStatus, {}, {}, (data, status) => {
      if (status === 200) {
        this.setState({ showChartForStatusLoader: false, claimsCountByStatus: data.vta_claims || [] });
      } else {
        this.setState({ showChartForStatusLoader: false }, () => {
          Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
        });
      }
    });
  };

  loadClaimsCountByAgents = () => {
    ajax(apiUrls.vtaClaimsCountAgents, {}, {}, (data, status) => {
      if (status === 200) {
        this.setState({ showChartForAgentsLoader: false, claimsCountByAgents: data.vta_claims || [] });
      } else {
        this.setState({ showChartForAgentsLoader: false }, () => {
          Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
        });
      }
    });
  };

  loadClaimsCountByAge = () => {
    ajax(apiUrls.vtaClaimsCountAge, {}, {}, (data, status) => {
      if (status === 200) {
        this.setState({
          showChartForAgeLoader: false,
          claimsCountByAge: data.vta_claims || [],
          averageWaitingAge: data.average_waiting_period
        });
      } else {
        this.setState({ showChartForAgeLoader: false }, () => {
          Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
        });
      }
    });
  };

  onExport = () => {
    const props = this.props;
    let url = `${apiUrls.vtaclaims}?csv=true`;
    if (this.currentQ(props)) {
      url += (`&q=${window.encodeURIComponent(this.currentQ(props).trim())}`);
    }
    if (this.currentStatus(props)) {
      url += (`&status=${window.encodeURIComponent(this.currentStatus(props))}`);
    }
    if (this.currentUserId(props)) {
      url += (`&user_id=${window.encodeURIComponent(this.currentUserId(props))}`);
    }
    if (this.currentClaimAge(props)) {
      url += (`&age=${window.encodeURIComponent(this.currentClaimAge(props))}`);
    }
    if(this.currentSortBy(props)) {
      url += (`&sort_by=${window.encodeURIComponent(this.currentSortBy(props))}`);
    }
    if(this.currentSortOrder(props)) {
      url += (`&sort_order=${window.encodeURIComponent(this.currentSortOrder(props))}`);
    }
    this.setState({ showLoader: false }, () => {
      ajaxRaw(url, {responseType: 'blob'}, (data) => {
        if (data.status === 200) {
          this.setState({ showLoader: false });
          window.URL = window.URL || window.webkitURL;
          const csvURL = window.URL.createObjectURL(data.response);
          const fileName = `Worksheet_${moment(new Date()).format("YYYY-MM-DDTHH:mm:ss").toString()}.csv`;
          const downloadLink = document.createElement('a');
          downloadLink.setAttribute("href", csvURL);
          downloadLink.setAttribute("download", fileName);
          downloadLink.style.cssText = 'display:none';
          document.body.appendChild(downloadLink);
          downloadLink.click();
        } else {
          this.setState({ showLoader: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  getClaimOwnersList = () => {
    ajax(apiUrls.vtaClaimOwners, {}, {}, (data, status) => {
      if (status === 200) {
        this.setState({ claimOwners: data.claim_owners });
      } else {
        Alert.error("Click the browser's Refresh button to reload the user list. If the error continues, contact your system administrator.");
      }
    });
  };

  currentPage = (props) => {
    if (!props) {
      props = this.props;
    }
    return parseInt(props.location.query.page, 10) || 1;
  };

  currentQ = (props) => {
    if (!props) {
      props = this.props;
    }
    return props.location.query.q || '';
  };

  currentUserId = (props) => {
    if (!props) {
      props = this.props;
    }
    if (props.location.query.userId === 'all') {
      return 'all';
    } else if (!props.location.query.userId) {
      return this.props.user.id;
    } else {
      return props.location.query.userId;
    }
  };

  currentSortOrder = (props) => {
    if (!props) {
      props = this.props;
    }
    return props.location.query.sortOrder || '';
  };

  currentSortBy = (props) => {
    if (!props) {
      props = this.props;
    }
    return props.location.query.sortBy || '';
  };

  currentStatus = (props) => {
    if (!props) {
      props = this.props;
    }
    return props.location.query.status ? props.location.query.status : 'All Active';
  };

  currentClaimAge = (props) => {
    if (!props) {
      props = this.props;
    }
    return props.location.query.age;
  };

  setPage = (page) => {
    const query = { page };
    if (this.currentQ()) {
      query.q = this.currentQ();
    }
    if (this.currentStatus()) {
      query.status = this.currentStatus();
    }
    if (this.currentUserId()) {
      query.userId = this.currentUserId();
    }
    if (this.currentClaimAge()) {
      query.age = this.currentClaimAge();
    }
    if (this.currentSortBy()) {
      query.sortBy = this.currentSortBy();
    }
    if (this.currentSortOrder()) {
      query.sortOrder = this.currentSortOrder();
    }
    const route = { pathname: "/vta-claims-list", query };
    if (!this.context.router.isActive(route)) {
      this.context.router.push(route);
    }
  };

  onSearch = (q) => {
    if (this.currentQ() !== q) {
      const query = { q, page: 1 };
      if (this.currentStatus()) {
        query.status = this.currentStatus();
      }
      if (this.currentUserId()) {
        query.userId = this.currentUserId();
      }
      if (this.currentClaimAge()) {
        query.age = this.currentClaimAge();
      }
      if (this.currentSortBy()) {
        query.sortBy = this.currentSortBy();
      }
      if (this.currentSortOrder()) {
        query.sortOrder = this.currentSortOrder();
      }
      const route = { pathname: "/vta-claims-list", query };
      if (!this.context.router.isActive(route)) {
        this.context.router.push(route);
      }
    }
  }

  getClaimsByStatus = (event) => {
    const query = { status: event.target.value, page: 1 };
    if (this.currentQ()) {
      query.q = this.currentQ();
    }
    if (this.currentUserId()) {
      query.userId = this.currentUserId();
    }
    if (this.currentClaimAge()) {
      query.age = this.currentClaimAge();
    }
    if (this.currentSortBy()) {
      query.sortBy = this.currentSortBy();
    }
    if (this.currentSortOrder()) {
      query.sortOrder = this.currentSortOrder();
    }
    const route = { pathname: "/vta-claims-list", query };
    if (!this.context.router.isActive(route)) {
      this.context.router.push(route);
    }
  };

  sortClaimList = (sortBy, sortOrder) => {
    let query = { sortBy, sortOrder, page: 1 };
    if (this.currentQ()) {
      query.q = this.currentQ();
    }
    if (this.currentStatus()) {
      query.status = this.currentStatus();
    }
    if (this.currentUserId()) {
      query.userId = this.currentUserId();
    }
    if (this.currentClaimAge()) {
      query.age = this.currentClaimAge();
    }
    const route = { pathname: "/vta-claims-list", query };
    if (!this.context.router.isActive(route)) {
      this.context.router.push(route);
    }
  };

  handleClaimOwnerChange = (e) => {
    const query = { page: 1, userId: e.target.value };
    if (this.currentQ()) {
      query.q = this.currentQ();
    }
    if (this.currentStatus()) {
      query.status = this.currentStatus();
    }
    if (this.currentClaimAge()) {
      query.age = this.currentClaimAge();
    }
    if (this.currentSortBy()) {
      query.sortBy = this.currentSortBy();
    }
    if (this.currentSortOrder()) {
      query.sortOrder = this.currentSortOrder();
    }
    const route = { pathname: "/vta-claims-list", query };
    if (!this.context.router.isActive(route)) {
      this.context.router.push(route);
    }
  };

  handleAgeFilterChange = (e) => {
    const query = { page: 1, age: e.target.value };
    if (this.currentQ()) {
      query.q = this.currentQ();
    }
    if (this.currentStatus()) {
      query.status = this.currentStatus();
    }
    if (this.currentUserId()) {
      query.userId = this.currentUserId();
    }
    if (this.currentSortBy()) {
      query.sortBy = this.currentSortBy();
    }
    if (this.currentSortOrder()) {
      query.sortOrder = this.currentSortOrder();
    }
    const route = { pathname: "/vta-claims-list/", query };
    if (!this.context.router.isActive(route)) {
      this.context.router.push(route);
    }
  };

  onStatusChartLegendClick = (status) => {
    this.setState({ selectedChartLegendFilter: status }, () => {
      const query = { status, page: 1, age: "all", userId: "all" };
      const route = { pathname: "/vta-claims-list", query };
      if (!this.context.router.isActive(route)) {
        this.context.router.push(route);
      }
    });
  };

  onAgentChartLegendClick = (agentName, owner_id) => {
    this.setState({ selectedChartLegendFilter: owner_id }, () => {
      const query = { status: "All Active", page: 1, userId: owner_id, age: "" };
      const route = { pathname: "/vta-claims-list", query };
      if (!this.context.router.isActive(route)) {
        this.context.router.push(route);
      }
    });
  };

  onAgeChartLegendClick = (age) => {
    this.setState({ selectedChartLegendFilter: age }, () => {
      let ageText = "";
      switch (age) {
      case "< 1 Week":
        ageText = "LessThan1Week";
        break;
      case "1-2 Weeks":
        ageText = "OneToTwoWeeks";
        break;
      case "2-3 Weeks":
        ageText = "TwoToThreeWeeks";
        break;
      case "3-4 Weeks":
        ageText = "ThreeToFourWeeks";
        break;
      case "> Month":
        ageText = "GreaterThan1Month";
        break;
      }
      const query = { age: ageText, status: "All Active", page: 1, userId: "all" };
      const route = { pathname: "/vta-claims-list", query };
      if (!this.context.router.isActive(route)) {
        this.context.router.push(route);
      }
    });
  };

  renderStatusDonutChart = (spinnerMessage) => {
    const openClaims = this.state.claimsCountByStatus.reduce((sum, value) => sum + value.count, 0);
    const chartConfig = {
      width: 350,
      height: 150,
      innerRadius: 35,
      outerRadius: 50,
      sectorStrokeWidth: "0px",
      centerTextTitle: "Open",
      centerTextCount: openClaims.toString(),
      legendBulletSize: 10,
      legendStyle: { fontSize: "12px" },
      legendHeaderStyle: { fontSize: "14px" },
      legendColumnHeaders: ["Status", "Claims"],
      legendColumnXPositions: [15, 100],
      barColors: ['#f5a623', '#4a90e2', '#2646bb', '#7ed321'],
      selectedBar: this.state.selectedChartLegendFilter.toString()
    };
    return (
      <Loader show={ this.state.showChartForStatusLoader } message={ spinnerMessage }>
        <div id="DonutChart_Status">
          <h6>Claim Status</h6>
          <DonutChart config={ chartConfig } data={ this.state.claimsCountByStatus }
            onLegendClick={ this.onStatusChartLegendClick }/>
        </div>
      </Loader>
    );
  };

  renderAgentsDonutChart = (spinnerMessage) => {
    const total = this.state.claimsCountByAgents.reduce((sum, value) => sum + value.count, 0);
    const avgClaims = this.state.claimsCountByAgents.length && total ? Math.floor(total / this.state.claimsCountByAgents.length) : "";
    const chartConfig = {
      width: 350,
      height: 150,
      innerRadius: 35,
      outerRadius: 50,
      sectorStrokeWidth: "0px",
      centerTextTitle: "Avg Per",
      centerTextCount: avgClaims.toString(),
      legendBulletSize: 10,
      legendStyle: { fontSize: "12px" },
      legendHeaderStyle: { fontSize: "14px" },
      legendColumnHeaders: ["Agent/Mgr", "Claims"],
      legendColumnXPositions: [15, 150],
      barColors: d3.schemeCategory20,
      selectedBar: this.state.selectedChartLegendFilter.toString()
    };
    // Add ellipsis to overflowing names
    let data = this.state.claimsCountByAgents.map(function (element) {
      if (element.name.length > 18) {
        element.name = element.name.slice(0, 18) + "...";
      }
      return element;
    });
    return (
      <Loader show={ this.state.showChartForStatusLoader } message={ spinnerMessage }>
        <div id="DonutChart_Status">
          <h6>Claim Distribution</h6>
          <DonutChart config={ chartConfig } data={ data } onLegendClick={ this.onAgentChartLegendClick }/>
        </div>
      </Loader>
    );
  };

  renderAgeDonutChart = (spinnerMessage) => {
    const chartConfig = {
      width: 350,
      height: 150,
      innerRadius: 35,
      outerRadius: 50,
      sectorStrokeWidth: "0px",
      centerTextTitle: "Week Avg",
      centerTextCount: this.state.averageWaitingAge,
      legendBulletSize: 10,
      legendStyle: { fontSize: "12px" },
      legendHeaderStyle: { fontSize: "14px" },
      legendColumnHeaders: ["Age", "Claims"],
      legendColumnXPositions: [15, 100],
      barColors: ['#7ed321', '#4a90e2', '#f8e71c', '#f5a623', '#d0021b'],
      selectedBar: this.state.selectedChartLegendFilter.toString()
    };
    return (
      <Loader show={ this.state.showChartForStatusLoader } message={ spinnerMessage }>
        <div id="DonutChart_Status">
          <h6>Claim Age</h6>
          <DonutChart config={ chartConfig } data={ this.state.claimsCountByAge }
            onLegendClick={ this.onAgeChartLegendClick }/>
        </div>
      </Loader>
    );
  };

  renderManagerView = (spinnerMessage) => {
    if (userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaimsManager) ||
    userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaims) ||
    userHasRole(this.props.user, CONSTANTS.USER_ROLES.viewOnlyClaims)
    ) {
      return (
        <div className="row my-4">
          <div className="col-4">
            {this.renderStatusDonutChart(spinnerMessage)}
          </div>
          <div className="col-4">
            {this.renderAgentsDonutChart(spinnerMessage)}
          </div>
          <div className="col-4">
            {this.renderAgeDonutChart(spinnerMessage)}
          </div>
        </div>
      );
    }
  };

  renderClaimOwnerDropdown = () => {
    if (this.state.claimOwners.length > 0) {
      return this.state.claimOwners.map((user) => {
        return (<option value={ user.id } key={ user.id }>{`${user.first_name} ${user.last_name}`}</option>);
      });
    }
  };

  renderClaimListFilters = () => {
    return (
      <div className="form-inline row my-4">
        <div className="form-group px-3">
          <label htmlFor="claim-view-filter" className="pr-1">View: </label>
          <select id='claim-view-filter'
            className="form-control"
            value={ this.currentStatus() }
            onChange={ this.getClaimsByStatus }>
            <option value='All Active'>All Active</option>
            <option value='In Inquiry'>In Inquiry</option>
            <option value='In Process'>In Process</option>
            <option value='In Review'>In Review</option>
            <option value='In Finance'>In Finance</option>
            <option value='Closed'>Closed</option>
          </select>
        </div>
        <div className="form-group px-2">
          <label htmlFor="claim-owner-filter" className="pr-1">Assigned to: </label>
          <select id='claim-owner-filter'
            className="form-control"
            value={ this.currentUserId() }
            onChange={ this.handleClaimOwnerChange }>
            <option value='all'>All</option>
            {this.renderClaimOwnerDropdown()}
          </select>
        </div>
        <div className="form-group px-2">
          <label htmlFor="claim-age-filter" className="pr-1">Claim age: </label>
          <select id='claim-age-filter'
            className="form-control"
            value={ this.currentClaimAge() }
            onChange={ this.handleAgeFilterChange }>
            <option value='all'>All</option>
            <option value='LessThan1Week'>{"< 1 Week"}</option>
            <option value='OneToTwoWeeks'>{"1-2 Weeks"}</option>
            <option value='TwoToThreeWeeks'>{"2-3 Weeks"}</option>
            <option value='ThreeToFourWeeks'>{"3-4 Weeks"}</option>
            <option value='GreaterThan1Month'>{"> Month"}</option>
          </select>
        </div>
      </div>
    );
  };

  renderClaimList = () => {
    if (this.state.claimList.length > 0) {
      return (<ClaimsList claimList={ this.state.claimList }
        sortClaimList={ this.sortClaimList }
        sortBy={ this.currentSortBy() }
        sortOrder={ this.currentSortOrder() }/>);
    } else if (this.currentQ()) {
      return (
        <div className="text-center">
          <p>No Results available for this search query</p>
        </div>
      );
    }
  };

  renderPagination = () => {
    if (this.state.numberOfClaims > this.state.pageLimit) {
      return (
        <div className="pagination-container clearfix col-12 px-0">
          <div className="pull-left my-1">
            <p>Showing {this.currentPage() * this.state.pageLimit - this.state.pageLimit + 1}&nbsp;
              to {this.currentPage() * this.state.pageLimit > this.state.numberOfClaims ? this.state.numberOfClaims : this.currentPage() * this.state.pageLimit}&nbsp;
              of {this.state.numberOfClaims} items
            </p>
          </div>
          <div className="float-right">
            <Pagination page={ this.currentPage() } count={ this.state.numberOfClaims }
              limit={ this.state.pageLimit } setPage={ this.setPage }/>
          </div>
        </div>
      );
    }
  };

  renderExportButton = () => {
    return (
      <button onClick={ this.onExport }
        className="btn btn-secondary mr-4"
        id="btn-export"
        disabled={ this.props.location.query.userId !== 'all' }>
        <i className="fa fa-file-excel-o"/>
        &nbsp;Export
      </button>
    );
  };

  render() {
    const spinnerMessage = <p className='text-center'><i className="fa fa-refresh fa-spin"/> Loading...</p>;
    const searchRowStyle = { borderBottom: "1px solid #f5f5f5" };
    let pageTitle =  "VTA Claims";

    return (
      <Loader show={ this.state.showLoader } message={ spinnerMessage }>
        <section className="gap-dashboard clearfix">
          <div className="row">
            <div className="col-12">
              <PageHeader pageTitle={ pageTitle } user={this.props.user}/>
              <div className="row" style={ searchRowStyle }>
                <div className="form-inline my-2 col-12">
                  <div className="pull-left col-6 px-0">
                    <SearchBox onSearch={ this.onSearch }
                      placeholder="Search Name, Contract #, VIN (10)"
                      value={ this.currentQ() }/>
                  </div>
                  <div className="col-6 px-0 float-right">
                    <div className="form-group float-right">
                      {this.renderExportButton()}
                      <a href='/contracts'
                        className="btn btn-primary"
                        id="btn-search-contracts">
                        <span className="fa fa-search"/>
                        &nbsp;Search Contracts
                      </a>
                    </div>
                  </div>
                </div>
              </div>
              {this.renderManagerView(spinnerMessage)}
              {this.renderClaimListFilters()}
              {this.renderClaimList()}
              {this.renderPagination()}
            </div>
          </div>
        </section>
      </Loader>
    );
  }
}

VTAClaimsDashboard.propTypes = {
  user: PropTypes.shape({
    id: PropTypes.number.isRequired,
    first_name: PropTypes.string.isRequired,
    last_name: PropTypes.string.isRequired,
    email: PropTypes.string.isRequired,
    stores: PropTypes.arrayOf(PropTypes.string.isRequired),
    roles: PropTypes.shape({
      Map: PropTypes.object,
    }).isRequired,
  }),
  location: PropTypes.shape({
    query: PropTypes.shape({
      page: PropTypes.string,
      q: PropTypes.string,
      userId: PropTypes.string,
      status: PropTypes.string,
      age: PropTypes.string,
      sortBy: PropTypes.string,
      sortOrder: PropTypes.string,
    }).isRequired,
  }).isRequired,
};

VTAClaimsDashboard.contextTypes = {
  router: PropTypes.object.isRequired
};