import moment from "moment";
import React from "react";
import ReactTooltip from "react-tooltip";
import dateFormat from "./../reusable/Utilities/dateFormat.js";
import PropTypes from "prop-types";
import SortIcon from "./../reusable/SortIcon/SortIcon.jsx";

export default class ClaimsList extends React.Component {
  handleSort = (toBeSorted) => {
    const { sortBy, sortOrder, sortClaimList } = this.props;
    if(toBeSorted === sortBy && sortOrder === 'asc') {
      sortClaimList(sortBy, 'desc');
    } else if(toBeSorted === sortBy && sortOrder === 'desc') {
      sortClaimList(sortBy, 'asc');
    } else {
      sortClaimList(toBeSorted, 'asc');
    }
  };

  renderTableHeader = () => {
    const {
      sortBy,
      sortOrder
    } = this.props;
    return (
      <tr>
        <th className="cursor-pointer"
          id="label-insured-name"
          onClick={ this.handleSort.bind(this, 'insured_name') }>
          Insured Name&nbsp;
          <SortIcon fieldName='insured_name'
            sortBy={ sortBy }
            sortOrder={ sortOrder }/>
        </th>
        <th className="cursor-pointer"
          id="label-contract-number"
          onClick={ this.handleSort.bind(this, 'contract_number') }>
          Contract #&nbsp;
          <SortIcon fieldName='contract_number'
            sortBy={ sortBy }
            sortOrder={ sortOrder }/>
        </th>
        <th className="cursor-pointer"
          id="label-status"
          onClick={ this.handleSort.bind(this, 'status') }>
          Status&nbsp;
          <SortIcon fieldName='status'
            sortBy={ sortBy }
            sortOrder={ sortOrder }/>
        </th>
        <th className="cursor-pointer"
          id="label-assigned-to"
          onClick={ this.handleSort.bind(this, 'assigned_to') }>
          Assigned To&nbsp;
          <SortIcon fieldName='assigned_to'
            sortBy={ sortBy }
            sortOrder={ sortOrder }/>
        </th>
        <th className="cursor-pointer"
          id="label-opened"
          onClick={ this.handleSort.bind(this, 'opened') }>
          Opened&nbsp;
          <SortIcon fieldName='opened'
            sortBy={ sortBy }
            sortOrder={ sortOrder }/>
        </th>
        <th className="cursor-pointer"
          id="label-last-in"
          onClick={ this.handleSort.bind(this, 'last_in') }>
          Last In&nbsp;
          <SortIcon fieldName='last_in'
            sortBy={ sortBy }
            sortOrder={ sortOrder }/>
        </th>
      </tr>
    );
  };

  renderTableBody = () => {
    return this.props.claimList.map(this.renderTableBodyRow);
  };

  renderTableBodyRow = (claimData, index) => {
    return (
      <tr key={ index }>
        <td className="cursor-pointer long-text">
          <a href="#!"
            data-tip
            data-for={ `insured_name${index}` }
            onClick={ this.redirectToWorksheet.bind(this, claimData) }
            className="users">
            {claimData['insured_name']}
          </a>
          <ReactTooltip id={ `insured_name${index}` } aria-haspopup='true'>
            <p className="text-center">{claimData['insured_name']}</p>
          </ReactTooltip>
        </td>
        <td>
          <a href="#!"
            onClick={ this.redirectToWorksheet.bind(this, claimData) }>
            {claimData['contract_number']}
          </a>
        </td>
        <td>
          {claimData['status']}
        </td>
        <td className="cursor-pointer long-text">
          <span data-tip
            data-for={ `assigned_to${index}` }>
            {claimData['assigned_to']}
          </span>
          <ReactTooltip id={ `assigned_to${index}` } aria-haspopup='true'>
            <p className="text-center">{claimData['assigned_to']}</p>
          </ReactTooltip>
        </td>
        <td>
          {moment(claimData['date_of_claim_received']).format(dateFormat.displayDateFormat)}
        </td>
        <td>
          {moment(claimData['date_of_last_in']).format(dateFormat.displayDateFormat)}
        </td>
      </tr>
    );
  };

  redirectToWorksheet = (claimData) => {
    if (claimData.contract_number && claimData.id) {
      const query = {
        id: claimData.id,
        contract_number: claimData.contract_number
      };
      const route = { pathname: "/vta-claims", query };
      if (!this.context.router.isActive(route)) {
        this.context.router.push(route);
      }
    }
  };

  render() {
    return (
      <div className="claim-list table-responsive">
        <table className="table table-striped">
          <thead>
            {this.renderTableHeader()}
          </thead>
          <tbody>
            {this.renderTableBody()}
          </tbody>
        </table>
      </div>
    );
  }
}

ClaimsList.propTypes = {
  claimList: PropTypes.array.isRequired,
  sortClaimList: PropTypes.func.isRequired,
  sortOrder: PropTypes.oneOf(['asc', 'desc', '']),
  sortBy: PropTypes.string,
  user: PropTypes.shape({
    id: PropTypes.number.isRequired,
    first_name: PropTypes.string.isRequired,
    last_name: PropTypes.string.isRequired,
    email: PropTypes.string.isRequired,
    stores: PropTypes.arrayOf(PropTypes.string.isRequired),
    roles: PropTypes.shape({
      Map: PropTypes.object,
    }).isRequired
  })
};

ClaimsList.contextTypes = {
  router: PropTypes.object.isRequired
};