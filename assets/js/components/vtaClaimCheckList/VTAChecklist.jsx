import React from 'react';
import InputBoxRow from "../claimCheckList/InputBoxRow.jsx";
import InputBox from "../reusable/InputBox/InputBox.jsx";
import PropTypes from 'prop-types';
import { CONSTANTS } from '../reusable/Constants/constants';
import ReactTooltip from 'react-tooltip';

export default class ValuationReport extends React.Component {

  static propTypes = {
    claimObject: PropTypes.object.isRequired,
    showManagerCheckbox: PropTypes.bool,
    onCursorToggle: PropTypes.func.isRequired,
    onVendorChange: PropTypes.func.isRequired,
    searchVendor: PropTypes.func.isRequired,
    onCursorValueChange: PropTypes.func.isRequired,
    onCursorChange: PropTypes.func.isRequired,
    getFieldNotes: PropTypes.func.isRequired,
    getFieldDocs: PropTypes.func.isRequired,
    selectedFieldNote: PropTypes.object.isRequired,
    handleItemNoteClick: PropTypes.func.isRequired,
    handleMouseEnter: PropTypes.func.isRequired,
    handleMouseLeave: PropTypes.func.isRequired,
    handleAttachmentClick: PropTypes.func.isRequired,
    deleteAttachment: PropTypes.func.isRequired,
    rowType: PropTypes.string.isRequired,
    rowStyle: PropTypes.object.isRequired,
    attachments: PropTypes.object.isRequired,
    notes: PropTypes.object.isRequired,
    isDisabled: PropTypes.bool
  };

  renderCaseReserveDropdown = (COMPONENT_MAP) => {
    return (
      <select className="form-control form-control-sm"
        id={ `${COMPONENT_MAP[ 'caseReserve' ][ 'rowId' ]}_dropdown` }
        value={ this.props.claimObject.case_reserve }
        onChange={ this.props.onCursorChange.bind(null, 'case_reserve') }
        disabled={ this.props.isDisabled }>
        <option value="">Select Amount</option>
        <option value="2500">2500</option>
        <option value="5000">5000</option>
      </select>
    );
  };

  renderVendorSearchBox = () => {
    const COMPONENT_MAP = CONSTANTS.VTA_WORKSHEET_COMPONENT_MAP;
    return (
      <div className="col-12 px-0 col-12 d-inline-flex align-items-center">
        <InputBox type="Text"
          id={ `${COMPONENT_MAP[ 'vendorId' ][ 'rowId' ]}_inputBox` }
          customClass="col-10 px-0"
          hasDefaultValue={ true }
          isDisabled={ this.props.isDisabled }
          value={ this.props.claimObject[ "vendor_id" ] }
          onChange={ this.onVendorIdChange }
          onKeyUp={ this.onVendorSearchEnter }
          onBlur={ this.onVendorIdChange }/>
        <button
          disabled={ this.props.isDisabled }
          type="button"
          className={ `cursor-pointer col-2 px-0 btn ${!this.props.isDisabled ? "text-primary" : ""} ` }
          onClick={ this.props.searchVendor }>
          <i className="fa fa-search"/>
        </button>
      </div>
    );
  };

  onVendorIdChange = (vendorId) => {
    this.props.onVendorChange(vendorId);
  };

  onVendorSearchEnter = (e) => {
    if (e.keyCode === 13 || e.which === 13) {
      this.props.searchVendor();
    }
  };

  renderCustomerInformation() {
    const longTextStyle = {
      "maxWidth": "180px",
      "overflow": "hidden",
      "textOverflow": "ellipsis",
      "whiteSpace": "nowrap"
    };
    const { claimObject } = this.props;
    return (
      <div className="form-group row">
        <div className="col-3">
          <label className="form-check-label col-form-label col-form-label-sm">
            <span>Customer Information:</span>
          </label>
        </div>
        <div className="col-2 pl-0 ml-0">
          <section className="col">
            <div className="d-inline-flex">
              <a href={ `/gap-contract/${claimObject.contract_number}?active_tab=customer` }
                id="link-customer-name"
                data-tip
                data-for="customer_name"
                style={ longTextStyle }
                target="_blank"
                rel="noopener noreferrer">
                {claimObject.first_name + " " + claimObject.last_name}
                <ReactTooltip id={ `customer_name` } aria-haspopup='true'>
                  <p className="text-center">{claimObject.first_name + " " + claimObject.last_name}</p>
                </ReactTooltip>
              </a>
            </div>
            <p className="mb-0 col-form-label-sm">
              {claimObject.street_address}
            </p>
            <p className="mb-0 col-form-label-sm">
              {`${claimObject.city}, ${claimObject.state} ${claimObject.postal_code}`}
            </p>
            <p className="col-form-label-sm">
              {claimObject.phone_number}
            </p>
            <a href={ `/gap-contract/${claimObject.contract_number}?active_tab=vehicle` }
              id="link-vin"
              target="_blank"
              rel="noopener noreferrer"
              className="col-form-label-sm">
              {claimObject.vin}
            </a>
          </section>
        </div>
      </div>
    );
  }

  render() {
    const COMPONENT_MAP = CONSTANTS.VTA_WORKSHEET_COMPONENT_MAP;
    return (
      <span className="valuation-report">
        <InputBoxRow labelContainerClass="col-5"
          hasLabel={ true }
          hasCheckbox={ true }
          checkBoxAttribute="is_police_report_available"
          label="Police report"
          managerCheckBoxAttribute="police_report_available_manager_flag"
          showManagerCheckbox={ this.props.showManagerCheckbox }
          claimObject={ this.props.claimObject }
          onCursorToggle={ this.props.onCursorToggle }
          rowId={ COMPONENT_MAP[ 'policeReport' ][ 'rowId' ] }
          fieldId={ COMPONENT_MAP[ 'policeReport' ][ 'id' ] }
          fieldNotes={ this.props.getFieldNotes(this.props.notes, COMPONENT_MAP[ 'policeReport' ][ 'id' ]) }
          fieldDocs={ this.props.getFieldDocs(this.props.attachments, COMPONENT_MAP[ 'policeReport' ][ 'id' ]) }
          selectedFieldNote={ this.props.selectedFieldNote }
          handleItemNoteClick={ this.props.handleItemNoteClick }
          handleMouseEnter={ this.props.handleMouseEnter }
          handleMouseLeave={ this.props.handleMouseLeave }
          handleAttachmentClick={ this.props.handleAttachmentClick }
          deleteAttachment={ this.props.deleteAttachment }
          claimType={ CONSTANTS.CLAIM_TYPE.vta }
          rowType={ CONSTANTS.FIELD_TYPE.static }
          rowStyle={ this.props.rowStyle }
          isDisabled={ this.props.isDisabled }/>
        <InputBoxRow labelContainerClass="col-3"
          inputBoxContainerClass="col-2"
          hasLabel={ true }
          hasCheckbox={ true }
          hasInputBox={ true }
          checkBoxAttribute="has_settlement_check"
          label="Settlement check:"
          inputBoxType="Currency"
          inputBoxAttribute="settlement_check_value"
          managerCheckBoxAttribute="settlement_check_manager_flag"
          showManagerCheckbox={ this.props.showManagerCheckbox }
          hasDefaultValue={ true }
          claimObject={ this.props.claimObject }
          onCursorToggle={ this.props.onCursorToggle }
          onInputBoxChange={ this.props.onCursorValueChange }
          onInputBoxBlur={ this.props.onCursorValueChange }
          rowId={ COMPONENT_MAP[ 'settlementCheck' ][ 'rowId' ] }
          fieldId={ COMPONENT_MAP[ 'settlementCheck' ][ 'id' ] }
          fieldNotes={ this.props.getFieldNotes(this.props.notes, COMPONENT_MAP[ 'settlementCheck' ][ 'id' ]) }
          fieldDocs={ this.props.getFieldDocs(this.props.attachments, COMPONENT_MAP[ 'settlementCheck' ][ 'id' ]) }
          selectedFieldNote={ this.props.selectedFieldNote }
          handleItemNoteClick={ this.props.handleItemNoteClick }
          handleMouseEnter={ this.props.handleMouseEnter }
          handleMouseLeave={ this.props.handleMouseLeave }
          handleAttachmentClick={ this.props.handleAttachmentClick }
          deleteAttachment={ this.props.deleteAttachment }
          claimType={ CONSTANTS.CLAIM_TYPE.vta }
          rowType={ CONSTANTS.FIELD_TYPE.static }
          rowStyle={ this.props.rowStyle }
          isDisabled={ this.props.isDisabled }/>
        <InputBoxRow labelContainerClass="col-3"
          inputBoxContainerClass="col-2"
          hasLabel={ true }
          hasCheckbox={ true }
          hasInputBox={ true }
          checkBoxAttribute="has_original_financing"
          label="Original financing:"
          inputBoxType="Currency"
          inputBoxAttribute="original_financing_value"
          managerCheckBoxAttribute="original_financing_manager_flag"
          showManagerCheckbox={ this.props.showManagerCheckbox }
          hasDefaultValue={ true }
          claimObject={ this.props.claimObject }
          onCursorToggle={ this.props.onCursorToggle }
          onInputBoxChange={ this.props.onCursorValueChange }
          onInputBoxBlur={ this.props.onCursorValueChange }
          rowId={ COMPONENT_MAP[ 'originalFinancing' ][ 'rowId' ] }
          fieldId={ COMPONENT_MAP[ 'originalFinancing' ][ 'id' ] }
          fieldNotes={ this.props.getFieldNotes(this.props.notes, COMPONENT_MAP[ 'originalFinancing' ][ 'id' ]) }
          fieldDocs={ this.props.getFieldDocs(this.props.attachments, COMPONENT_MAP[ 'originalFinancing' ][ 'id' ]) }
          selectedFieldNote={ this.props.selectedFieldNote }
          handleItemNoteClick={ this.props.handleItemNoteClick }
          handleMouseEnter={ this.props.handleMouseEnter }
          handleMouseLeave={ this.props.handleMouseLeave }
          handleAttachmentClick={ this.props.handleAttachmentClick }
          deleteAttachment={ this.props.deleteAttachment }
          claimType={ CONSTANTS.CLAIM_TYPE.vta }
          rowType={ CONSTANTS.FIELD_TYPE.static }
          rowStyle={ this.props.rowStyle }
          isDisabled={ this.props.isDisabled }/>
        <InputBoxRow labelContainerClass="col-5"
          hasLabel={ true }
          hasCheckbox={ true }
          checkBoxAttribute="has_vta_contract"
          label="VTA Contract"
          managerCheckBoxAttribute="vta_contract_manager_flag"
          showManagerCheckbox={ this.props.showManagerCheckbox }
          claimObject={ this.props.claimObject }
          onCursorToggle={ this.props.onCursorToggle }
          rowId={ COMPONENT_MAP[ 'vtaContract' ][ 'rowId' ] }
          fieldId={ COMPONENT_MAP[ 'vtaContract' ][ 'id' ] }
          fieldNotes={ this.props.getFieldNotes(this.props.notes, COMPONENT_MAP[ 'vtaContract' ][ 'id' ]) }
          fieldDocs={ this.props.getFieldDocs(this.props.attachments, COMPONENT_MAP[ 'vtaContract' ][ 'id' ]) }
          selectedFieldNote={ this.props.selectedFieldNote }
          handleItemNoteClick={ this.props.handleItemNoteClick }
          handleMouseEnter={ this.props.handleMouseEnter }
          handleMouseLeave={ this.props.handleMouseLeave }
          handleAttachmentClick={ this.props.handleAttachmentClick }
          deleteAttachment={ this.props.deleteAttachment }
          claimType={ CONSTANTS.CLAIM_TYPE.vta }
          rowType={ CONSTANTS.FIELD_TYPE.static }
          rowStyle={ this.props.rowStyle }
          isDisabled={ this.props.isDisabled }/>
        <InputBoxRow labelContainerClass="col-5"
          hasLabel={ true }
          hasCheckbox={ true }
          checkBoxAttribute="has_insurance_not_recovered"
          label="Insurance - not recovered"
          managerCheckBoxAttribute="insurance_not_recovered_manager_flag"
          showManagerCheckbox={ this.props.showManagerCheckbox }
          claimObject={ this.props.claimObject }
          onCursorToggle={ this.props.onCursorToggle }
          rowId={ COMPONENT_MAP[ 'insuranceNotRecovered' ][ 'rowId' ] }
          fieldId={ COMPONENT_MAP[ 'insuranceNotRecovered' ][ 'id' ] }
          fieldNotes={ this.props.getFieldNotes(this.props.notes, COMPONENT_MAP[ 'insuranceNotRecovered' ][ 'id' ]) }
          fieldDocs={ this.props.getFieldDocs(this.props.attachments, COMPONENT_MAP[ 'insuranceNotRecovered' ][ 'id' ]) }
          selectedFieldNote={ this.props.selectedFieldNote }
          handleItemNoteClick={ this.props.handleItemNoteClick }
          handleMouseEnter={ this.props.handleMouseEnter }
          handleMouseLeave={ this.props.handleMouseLeave }
          handleAttachmentClick={ this.props.handleAttachmentClick }
          deleteAttachment={ this.props.deleteAttachment }
          claimType={ CONSTANTS.CLAIM_TYPE.vta }
          rowType={ CONSTANTS.FIELD_TYPE.static }
          rowStyle={ this.props.rowStyle }
          isDisabled={ this.props.isDisabled }/>
        <InputBoxRow labelContainerClass="col-3"
          containerClass="mb-1 pb-1"
          inputBoxContainerClass="col-2"
          hasLabel={ true }
          hasCheckbox={ true }
          label="Vendor #:"
          checkBoxAttribute="has_vendor_id"
          hasNode={ true }
          renderNode={ this.renderVendorSearchBox }
          managerCheckBoxAttribute="vendor_id_manager_flag"
          showManagerCheckbox={ this.props.showManagerCheckbox }
          claimObject={ this.props.claimObject }
          onCursorToggle={ this.props.onCursorToggle }
          onInputBoxChange={ this.props.onCursorValueChange }
          onInputBoxBlur={ this.props.onCursorValueChange }
          rowId={ COMPONENT_MAP[ 'vendorId' ][ 'rowId' ] }
          fieldId={ COMPONENT_MAP[ 'vendorId' ][ 'id' ] }
          fieldNotes={ this.props.getFieldNotes(this.props.notes, COMPONENT_MAP[ 'vendorId' ][ 'id' ]) }
          fieldDocs={ this.props.getFieldDocs(this.props.attachments, COMPONENT_MAP[ 'vendorId' ][ 'id' ]) }
          selectedFieldNote={ this.props.selectedFieldNote }
          handleItemNoteClick={ this.props.handleItemNoteClick }
          handleMouseEnter={ this.props.handleMouseEnter }
          handleMouseLeave={ this.props.handleMouseLeave }
          handleAttachmentClick={ this.props.handleAttachmentClick }
          deleteAttachment={ this.props.deleteAttachment }
          claimType={ CONSTANTS.CLAIM_TYPE.vta }
          rowType={ CONSTANTS.FIELD_TYPE.static }
          rowStyle={ this.props.rowStyle }
          isDisabled={ this.props.isDisabled }/>
        {this.renderCustomerInformation()}
        <InputBoxRow labelContainerClass="col-3"
          inputBoxContainerClass="col-2"
          hasLabel={ true }
          hasCheckbox={ true }
          checkBoxAttribute="has_case_reserve"
          label="Case Reserve:"
          managerCheckBoxAttribute="case_reserve_manager_flag"
          hasNode={ true }
          renderNode={ this.renderCaseReserveDropdown.bind(this, COMPONENT_MAP) }
          showManagerCheckbox={ this.props.showManagerCheckbox }
          claimObject={ this.props.claimObject }
          onCursorToggle={ this.props.onCursorToggle }
          onCursorChange={ this.props.onCursorChange }
          rowId={ COMPONENT_MAP[ 'caseReserve' ][ 'rowId' ] }
          fieldId={ COMPONENT_MAP[ 'caseReserve' ][ 'id' ] }
          fieldNotes={ this.props.getFieldNotes(this.props.notes, COMPONENT_MAP[ 'caseReserve' ][ 'id' ]) }
          fieldDocs={ this.props.getFieldDocs(this.props.attachments, COMPONENT_MAP[ 'caseReserve' ][ 'id' ]) }
          selectedFieldNote={ this.props.selectedFieldNote }
          handleItemNoteClick={ this.props.handleItemNoteClick }
          handleMouseEnter={ this.props.handleMouseEnter }
          handleMouseLeave={ this.props.handleMouseLeave }
          handleAttachmentClick={ this.props.handleAttachmentClick }
          deleteAttachment={ this.props.deleteAttachment }
          claimType={ CONSTANTS.CLAIM_TYPE.vta }
          rowType={ CONSTANTS.FIELD_TYPE.static }
          rowStyle={ this.props.rowStyle }
          isDisabled={ this.props.isDisabled }/>
      </span>
    );
  }
}
