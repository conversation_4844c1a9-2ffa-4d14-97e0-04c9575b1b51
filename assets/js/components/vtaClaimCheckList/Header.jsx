import React from "react";
import moment from "moment";
import dateFormat from "./../reusable/Utilities/dateFormat.js";
import ReactTooltip from "react-tooltip";
import { CONSTANTS } from "./../reusable/Constants/constants.js";
import PropTypes from "prop-types";
import If from "../reusable/If/If.jsx";
import { getClasses } from "../reusable/Utilities/headerClasses";

export default class Header extends React.Component {
  onContractNumberClick = () => {
    window.open(`/gap-contract/${this.props.claimObject.contract_number}`, this.props.claimObject.contract_number, "width=1000,height=600");
  };

  renderLastUpdatedBy = () => {
    return (
      <span
        className="row flex-row-reverse pr-3">
          Last updated by: { `${this.props.claimObject.updated_by_user_name} - ${ this.props.claimObject.updated_at ? moment(this.props.claimObject.updated_at, dateFormat.backendDateFormat).format(dateFormat.displayDateFormat) : ''}` }
      </span>
    );
  };
    loadPDF = () => {
      window.open(`/api/vta-claims/${this.props.claimObject.id}/pdf`);
    };

    renderVoidCheckButton() {
      if (this.props.showVoidCheckButton && !this.props.isFinanceUser) {
        return (
          <button type="button"
            className="btn btn-primary mr-2 cursor-pointer"
            id="btn-void-check"
            onClick={ this.props.handleOnClickVoidCheck }>
            <i className="fa fa-times"/>
            &nbsp;Void Check
          </button>
        );
      }
    }

    render() {
      const pageSpacingStyle = { marginTop: '200px' };
      const {
        claimObject,
        claimStatus,
        backButtonOnClick,
        nextButtonOnClick,
        nextButtonId,
        nextButtonText,
        showHistoryButtonClick,
        onUpdateFromGapClick,
        user : {
          banner_info: bannerInfo,
        },
      } = this.props;
      const classes = getClasses(bannerInfo);
      let customerName = claimObject.last_name.concat(",",claimObject.first_name);
      customerName = customerName.trim();
      if (claimObject.is_business) {
        if (customerName) {
          customerName+="/";
        }
        customerName = customerName.concat(claimObject.business_name);
      } 
      return (
        <section className="page-header" style={ pageSpacingStyle }>
          <div className={classes}>
            <div className="row align-items-center">
              <div className="col-4 text-truncate h2">
                <h3 className="d-inline-block">{`VTA Claim - ${customerName}`}</h3>
              </div>
              <div className="col-4 text-center h3">
                <p className="d-inline-block h4">
                  <a href="#!"
                    id="link-contract-number"
                    onClick={ this.onContractNumberClick }>
                    { claimObject.contract_number }
                  </a>
                </p>
                <div className="d-inline-block h4">&nbsp;&nbsp;&nbsp;
                  <span data-tip data-for='claimStatus' data-place="bottom">
                    { claimStatus }
                  </span>
                  <ReactTooltip id='claimStatus' aria-haspopup='true'>
                    <span className="text-center">{ CONSTANTS.READABLE_STATUS_MAP[claimStatus] }</span>
                  </ReactTooltip>
                </div>
              </div>
              <div className="col-4">
                { this.renderLastUpdatedBy() }
              </div>
            </div>
            <div className="row py-3">
              <div className="col-8">
                <button onClick={ backButtonOnClick }
                  className="btn btn-secondary mr-2 cursor-pointer"
                  id="btn-back">
                  <i className="fa fa-arrow-left"/>
                &nbsp;Back
                </button>
                { this.renderVoidCheckButton() }
                <button className={ `btn btn-primary mr-2 cursor-pointer ${this.props.nextButtonDisabled ? "disabled" : ""}` }
                  onClick={ nextButtonOnClick }
                  id={ nextButtonId }
                  disabled={ !!this.props.nextButtonDisabled }>
                  {nextButtonText}
                </button>
                <If condition={ this.props.showSyncGap }>
                  <button
                    className={ `btn btn-primary mr-2 cursor-pointer ${this.props.nextButtonDisabled ? "disabled" : ""}` }
                    onClick={ onUpdateFromGapClick }
                    id="btn-update-from-gap"
                    disabled={ !!this.props.nextButtonDisabled }>
                  Sync gap
                  </button>
                </If>
              </div>
              <div className="col-4">
                <div className="row flex-row-reverse pr-3">
                  <div className="btn-toolbar">
                    <button type="button" className="btn btn-secondary mr-2" title="Print" 
                      onClick={ this.loadPDF.bind() }>
                      <i className='fa fa-print'/>
                    </button>
                    <button type="button"
                      className="btn btn-secondary mr-2 cursor-pointer"
                      id="btn-contract-notes"
                      onClick={ showHistoryButtonClick.bind(null, "contract") }>
                    Contract Notes
                    </button>
                    <button type="button"
                      className="btn btn-secondary mr-2 cursor-pointer"
                      id="btn-claim-history"
                      onClick={ showHistoryButtonClick.bind(null, "claim") }>
                    History
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      );
    }
}

Header.propTypes = {
  claimObject: PropTypes.object,
  nextButtonOnClick: PropTypes.func,
  nextButtonId: PropTypes.string,
  nextButtonText: PropTypes.string,
  backButtonOnClick: PropTypes.func,
  claimStatus: PropTypes.string,
  showHistoryButtonClick: PropTypes.func,
  nextButtonDisabled: PropTypes.bool,
  onUpdateFromGapClick: PropTypes.func,
  showSyncGap: PropTypes.bool,
  showVoidCheckButton: PropTypes.bool,
  handleOnClickVoidCheck: PropTypes.func,
  isFinanceUser: PropTypes.bool,
  user: PropTypes.shape({
    id: PropTypes.number.isRequired,
    first_name: PropTypes.string.isRequired,
    last_name: PropTypes.string.isRequired,
    email: PropTypes.string.isRequired,
    stores: PropTypes.arrayOf(PropTypes.string.isRequired),
    roles: PropTypes.shape({
      Map: PropTypes.object,
    }).isRequired,
    banner_info: PropTypes.shape({
      header: PropTypes.string.isRequired,
      message: PropTypes.string.isRequired,
      enabled: PropTypes.bool.isRequired,
    }).isRequired,
  })
};

Header.contextTypes = {
  router: PropTypes.object.isRequired
};
