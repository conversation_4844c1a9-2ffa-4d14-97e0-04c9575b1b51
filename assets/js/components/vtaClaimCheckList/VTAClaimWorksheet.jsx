import React from "react";
import Alert from "react-s-alert";
import Loader from "react-loader-advanced";
import immstruct from "immstruct";
import Immutable from "immutable";
import PropTypes from "prop-types";
import { json as ajax } from "./../../ajax.js";
import { scrollTo } from "../reusable/Utilities/Scroll.js";
import { CONSTANTS } from "./../reusable/Constants/constants.js";
import { URLS as apiUrls } from "./../reusable/Utilities/urls";
import Header from "./Header.jsx";
import RecordNotes from "./../reusable/RecordNotes/RecordNotes.jsx";
import AttachmentModal from "./FileAttachment.jsx";
import VTAChecklist from "./VTAChecklist.jsx";
import { userHasRole } from '../reusable/Utilities/userHasRole';
import StatusDropdown from "./StatusDropdown.jsx";
import ConfirmationModal from "./../reusable/ConfirmationModal/ConfirmationModal.jsx";
import UserSelectionModal from "./../claimCheckList/UserSelectionModal.jsx";
import VendorSelectionModal from "./../claimCheckList/BankSelectionModal.jsx";
import PaymentInfo from "./PaymentInfo.jsx";
import VoidCheck from "./VoidCheck.jsx";
import hstore from '../reusable/Utilities/hstore.js';

const style = {
  rowStyle: {
    border: "1px solid #ccc",
    borderRadius: "0.25rem 0 0 0.25rem",
    position: "relative",
    borderRight: "none",
    marginRight: "-16px",
    zIndex: "9"
  },
  notesStyle: {
    position: 'absolute',
    right: 0,
    top: 0,
    zIndex: 9
  }
};

export default class VTAClaimChecklist extends React.Component {

  IMMS_KEY = 'vta_claim';

  constructor(props) {
    super(props);
    this.vta_claim = immstruct(
      this.IMMS_KEY,
      {
        id: '',
        contract_number: '',
        last_updated_by: '',
        last_updated_at: '',
        insured_name: '',
        is_police_report_available: false,
        police_report_available_manager_flag: false,
        has_settlement_check: false,
        settlement_check_value: '',
        settlement_check_manager_flag: false,
        has_original_financing: false,
        original_financing_value: '',
        original_financing_manager_flag: false,
        has_vta_contract: false,
        vta_contract_manager_flag: false,
        has_insurance_not_recovered: false,
        insurance_not_recovered_manager_flag: false,
        status: '',
        denied_reason: '',
        created_by_user_id: '',
        date_of_claim_received: '',
        date_of_last_in: '',
        owner_id: '',
        vin: '',
        has_vendor_id: false,
        vendor_id_manager_flag: false,
        case_reserve: '',
        has_case_reserve: false,
        case_reserve_manager_flag: false,
        customer_id: '',
        first_name: '',
        last_name: '',
        is_business: false,
        business_name:'',
        state: '',
        city: '',
        postal_code: '',
        street_address: '',
        email_address: '',
        phone_number: '',
        updated_at: '',
        updated_by_user_id: '',
        updated_by_user_name: '',
        vendor_id: '',
        is_in_progress: false,
      }
    );
    this.vta_claim.on('swap', (newStructure, oldStructure, keyPath) => {
      this.setState({ vta_claim: this.vta_claim.cursor() });
    });
    this.state = {
      vta_claim: this.vta_claim.cursor(),
      userList: [],
      showLoader: false,
      notes: {},
      attachments: {},
      initialStatus: '',
      attachmentParams: {
        title: '',
        fieldId: '',
        rowType: '',
        contractCode: '',
        contractNumber: ''
      },
      displayAttachmentModal: false,
      worksheetOffset: 0,
      showNotes: false,
      notesURL: '',
      updateRecordNotes: false,
      noteTitle: '',
      selectedFieldNote: {
        itemNoteOffset: '',
        rowType: '',
        isSelected: false,
        contractDetails: {},
        fieldId: '',
        fieldNotes: {}
      },
      hideFieldNoteTextArea: true,
      fieldNoteHovered: false,
      fieldNoteClicked: false,
      noteEventType: '',
      isWorksheetUpdated: false,
      navigateHomeAfterSave: false,
      status: '',
      showAuthorizeConfirmationModal: false,
      displayUserSelectionModal: false,
      showUserListLoader: false,
      displayBackConfirmationModal: false,
      displayVendorSelectionModal: false,
      vendorId: '',
      showSyncGap: false,
      showVoidCheckConfirmationModal: false
    };
  }

  componentDidMount = () => {
    document.title = 'TCA Portal - VTA Claims Worksheet';
    this.loadClaim();
    this.loadAttachments();
    this.loadNotes();
    this.getAssociatedGap();

    // Set up unsaved changes tracking
    if (this.props.setUnsavedChanges) {
      this.props.setUnsavedChanges(false, this.handleSaveAndNavigateHome);
    }
  };

  componentWillUnmount = () => {
    // Clear unsaved changes tracking
    if (this.props.setUnsavedChanges) {
      this.props.setUnsavedChanges(false);
    }

    this.vta_claim.removeAllListeners();
    immstruct.remove(this.IMMS_KEY);
  };

  loadAttachments = (successCallback) => {
    this.setState({ showLoader: true }, () => {
      ajax(`${apiUrls.vtaClaimGetDocuments.replace('__documentId__', this.props.location.query.id)}`, {}, {}, (data, status) => {
        if (status === 200) {
          this.setState({
            attachments: data.docs,
            showLoader: false
          }, () => {
            if (successCallback) {
              successCallback();
            }
          });
        } else {
          this.setState({ showLoader: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  loadNotes = (successCallback) => {
    this.setState({ showLoader: true }, () => {
      ajax(`${apiUrls.vtaClaimGetFieldNotes.replace('__claimId__', this.props.location.query.id)}`, {}, {}, (data, status) => {
        if (status === 200) {
          this.setState({
            notes: data,
            showLoader: false
          }, () => {
            if (successCallback) {
              successCallback();
            }
          });
        } else {
          this.setState({ showLoader: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  loadClaim = () => {
    this.setState({ showLoader: true }, () => {
      ajax(`${apiUrls.vtaclaims}/${this.props.location.query.id}`, {}, {}, (data, status) => {
        if (status === 200) {
          this.setState({ showLoader: false, initialStatus: data.vta_claim.status }, ()=>{
            this.state.vta_claim.update(() => Immutable.fromJS(data.vta_claim));
          });
        } else {
          this.setState({ showLoader: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  getAssociatedGap = () => {
    this.setState({ showLoader: true }, () => {
      ajax(`${apiUrls.vtaClaimUpdateFromGap.replace('__claimId__', this.props.location.query.id)}`, {}, { method: 'GET' }, (data, status) => {
        if(status === 200){
          this.setState({
            showSyncGap: true
          });
        }
        else if(status === 500){
          if(data.message) {
            Alert.warning(data.message);
          }
          this.setState({
            showSyncGap: false
          });
        }
        else{
          this.setState({
            showSyncGap: false
          });
        }
      });
    });
  };

  updateClaimList = () => {
    const claimObject = this.state.vta_claim.toJS();
    if (claimObject.status === CONSTANTS.STATUS_MAP.inquiry && this.hasAttachment()) {
      claimObject.status = CONSTANTS.STATUS_MAP.pending;
    }
    this.setState({ showLoader: true,  displayUserSelectionModal: false, displayBackConfirmationModal : false, isWorksheetUpdated : false }, () => {
      ajax(`${apiUrls.vtaclaims}/${this.props.location.query.id}`, claimObject, { method: 'PUT' }, (data, status) => {
        if (status === 200) {
          Alert.success("Update successful.");
          if (this.state.navigateHomeAfterSave) {
            this.setState({ displayModal: false, showLoader: false, navigateHomeAfterSave: false }, () => {
              this.context.router.push('/');
            });
          } else {
            this.setState({ displayModal: false, showLoader: false }, () => {this.loadClaim();});
          }
        } else {
          this.setState({
            showLoader: false,
          }, () => {
            if (data.errors && data.errors.vendor_id) {
              Alert.warning(data.errors.vendor_id);
              return;
            }
            Alert.error("Click the Update button again. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  onCursorValueChange = (name, value) => {
    this.setWorksheetUpdated(true);
    this.state.vta_claim.cursor(name).update(() => value);
  };

  onCursorChange = (name, e) => {
    this.setWorksheetUpdated(true);
    this.state.vta_claim.cursor(name).update(() => e.target.value);
  };

  onCursorToggle = (name) => {
    this.setWorksheetUpdated(true);
    this.state.vta_claim.cursor(name).update(oldValue => !oldValue);
  };

  onUpdateFromGap = () => {
    this.setState({ showLoader: true }, () => {
      ajax(`${apiUrls.vtaClaimUpdateFromGap.replace('__claimId__', this.props.location.query.id)}`, {}, { method: 'PUT' }, (data, status) => {
        this.setState({ showLoader: false }, () => {
          if (status === 200) {
            this.loadClaim();
            this.loadAttachments();
            Alert.success("Synced with gap successfully");
          } else if(status === 404) {
            Alert.warning("No gap claim available.");
          } else if(status === 400) {
            Alert.warning("Could not sync with gap, multiple gap claims found.");
          } else {
            Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
          }
        });
      });
    });
  };

  handleStatusChange = (statusType, e) => {
    if (e.target.value) {
      if (!this.hasAttachment()) {
        Alert.error('A status change can not be completed until a document is attached to the claim.  Please attach a document and try again.');
      } else if (((e.target.value === 'waitingForAuthorization' || e.target.value === 'pendingDenial') &&
        userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaims) && !userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaimsManager) && !this.hasRequiredDetails()) ||
        ((userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaimsManager) && !this.hasRequiredDetails())
          && e.target.value !== 'denied'
          && e.target.value !== 'closedNoResponse'
          && this.state.initialStatus !== CONSTANTS.STATUS_MAP.closedNoResponse
        )) {
        Alert.error('Check all the required checkboxes before submitting the claim.');
      } else if (e.target.value === 'authorization' && !this.hasRequiredDetails()) {
        Alert.error('Check all the required checkboxes before submitting the claim.');
      } else if (e.target.value === 'authorization' && this.hasRequiredDetails()) {
        this.setState({
          showAuthorizeConfirmationModal: true,
          statusType: statusType,
          statusValue: e.target.value
        });
      } else {
        this.changeStatus(statusType, e.target.value);
      }
    }
  };

  hasRequiredDetails = () => {
    const claimObject = this.state.vta_claim.toJS();
    return (
      claimObject.is_police_report_available &&
      claimObject.has_settlement_check &&
      claimObject.has_original_financing &&
      claimObject.has_vta_contract &&
      claimObject.has_insurance_not_recovered
    );
  };

  hasAttachment = () => {
    return (this.state.attachments && this.state.attachments.field_documents && this.state.attachments.field_documents.length);
  };

  changeStatus = (statusType, statusValue) => {
    const statusCode = CONSTANTS.STATUS_MAP[statusValue];
    this.setState({
      status: statusValue
    }, () => {
      this.onCursorValueChange(statusType, statusCode);
    });
  };

  /**
   * This function will change showNotes state which will execute load notes function
   * @param notesType - Type of notes "contract" or "claim"
   */

  onShowHistoryButtonClick = (notesType) => {
    let notesURL;
    let noteTitle = "";
    let hideFieldNoteTextArea = false;
    if (notesType === "contract") {
      const claimObject = this.state.vta_claim.toJS();
      notesURL = `${apiUrls.contract}/${claimObject.contract_number}/notes`;
      noteTitle = "Contract Notes";
      hideFieldNoteTextArea = true;
    } else if (notesType === "claim") {
      notesURL = apiUrls.vtaClaimRecordNotes;
      noteTitle = "Claim History";
    }
    if (this.isViewOnlyRole() && !userHasRole(this.props.user, CONSTANTS.USER_ROLES.viewOnlyClaims)) {
      hideFieldNoteTextArea = true;
    }
    this.setState({
      showNotes: true,
      notesURL,
      updateRecordNotes: true,
      noteTitle,
      selectedFieldNote: {
        itemNoteOffset: '',
        rowType: '',
        isSelected: false,
        contractDetails: {},
        fieldId: '',
        fieldNotes: {}
      },
      hideFieldNoteTextArea: hideFieldNoteTextArea,
      fieldNoteHovered: false,
      fieldNoteClicked: false
    }, () => {
      scrollTo(0, 0);
    });
  };

  showRecordNotes = () => {
    const claimObject = this.state.vta_claim.toJS();
    const location = { "id": this.props.location.query.id };
    if (this.state.showNotes && !this.state.selectedFieldNote.isSelected) {
      if (this.state.notesURL === `${apiUrls.contract}/${claimObject.contract_number}/notes`) {
        location["id"] = "";
        location["contract_number"] = claimObject.contract_number;
        location["productCode"] = CONSTANTS.PRODUCT_CODE_NEW_MAP.theftRegistration;
      }
      return (
        <RecordNotes location={ location }
          updateRecordNotes={ this.state.updateRecordNotes }
          onRecordNotesUpdate={ () => {
            this.setState({ updateRecordNotes: false });
          } }
          type={ this.state.notesURL === `${apiUrls.contract}/${claimObject.contract_number}/notes` ? CONSTANTS.FIELD_TYPE.contract : null }
          apiURL={ this.state.notesURL }
          hideAddNoteTextArea={ this.state.hideFieldNoteTextArea }
          hasTitle={ true }
          hasCloseButton={ true }
          title={ this.state.noteTitle }
          containerClass="col-6"
          notesStyle={ style.notesStyle }
          closeButtonCallback={ this.closeRecordNote }/>
      );
    } else if (CONSTANTS.FIELD_TYPE.static === this.state.selectedFieldNote.rowType && this.state.selectedFieldNote.isSelected && this.state.showNotes) {
      return (
        <RecordNotes updateRecordNotes={ this.state.updateRecordNotes }
          location={ location }
          onRecordNotesUpdate={ this.loadNotes }
          apiURL={ apiUrls.vtaClaimFieldNotes }
          hideAddNoteTextArea={ this.state.hideFieldNoteTextArea }
          type={ this.state.selectedFieldNote.rowType }
          excludePagination={ true }
          recordNotes={ this.getFieldNotes(this.state.notes, this.state.selectedFieldNote.fieldId) }
          selectedFieldNote={ this.state.selectedFieldNote }
          hasCloseButton={ !this.state.fieldNoteHovered && true }
          containerClass="col-6"
          notesStyle={ style.notesStyle }
          closeButtonCallback={ this.closeRecordNote }
          worksheetOffset={ this.state.worksheetOffset }
          eventType={ this.state.noteEventType }/>
      );
    }
  };

  getFieldNotes = (notes, id) => {
    if (notes.field_notes && notes.field_notes.length > 0) {
      for (let index = 0; index < notes.field_notes.length; index++) {
        if (notes.field_notes[index]['field_id'] === id) {
          return notes.field_notes[index];
        }
      }
    }
  };

  getFieldDocs = (docs, id) => {
    if (docs.field_documents && docs.field_documents.length > 0) {
      for (let index = 0; index < docs.field_documents.length; index++) {
        if (docs.field_documents[index]['field_id'] === id && docs.field_documents[index]['documents'].length > 0) {
          return docs.field_documents[index]['documents'];
        }
      }
    }
  };

  handleFieldNoteClick = (params) => {
    this.setState({
      selectedFieldNote: params,
      showNotes: true,
      hideFieldNoteTextArea: false,
      fieldNoteHovered: false,
      fieldNoteClicked: true,
      updateRecordNotes: false,
      worksheetOffset: this.worksheet.offsetHeight,
      noteEventType: "click"
    }, () => {
      scrollTo(0, this.state.selectedFieldNote.itemNoteOffset);
    });
  };

  handleMouseEnter = (params) => {
    if (!this.state.fieldNoteClicked) {
      this.setState({
        selectedFieldNote: params,
        showNotes: true,
        hideFieldNoteTextArea: true,
        fieldNoteHovered: true,
        updateRecordNotes: false,
        worksheetOffset: this.worksheet.offsetHeight,
        noteEventType: "hover"
      });
    }
  };

  handleMouseLeave = () => {
    if (!this.state.fieldNoteClicked && this.state.fieldNoteHovered) {
      this.setState({
        selectedFieldNote: {
          itemNoteOffset: '',
          rowType: '',
          isSelected: false,
          contractDetails: {},
          fieldId: '',
          fieldNotes: {}
        },
        worksheetOffset: 0,
        showNotes: false,
        fieldNoteHovered: false,
        updateRecordNotes: false,
        noteEventType: ""
      });
    }
  };

  closeRecordNote = () => {
    this.setState({
      selectedFieldNote: {
        itemNoteOffset: '',
        rowType: '',
        isSelected: false,
        contractDetails: {},
        fieldId: '',
        fieldNotes: {}
      },
      worksheetOffset: 0,
      showNotes: false,
      hideFieldNoteTextArea: true,
      fieldNoteHovered: false,
      fieldNoteClicked: false,
      updateRecordNotes: false,
      noteEventType: ""
    });
  };

  handleAttachmentClick = (attachmentParams) => {
    this.setState({
      displayAttachmentModal: true,
      attachmentParams
    });
  };

  closeAttachmentModal = (claimObject, isSuccess, isFailure) => {
    this.setState({
      displayAttachmentModal: false,
      attachmentParams: {
        title: '',
        fieldId: '',
        rowType: '',
        contractCode: '',
        contractNumber: ''
      }
    }, () => {
      if (isSuccess) {
        this.loadAttachments(this.onAttachmentReload.bind(this, 'add', claimObject));
      }
      if (isFailure) {
        Alert.error("Click the browser's Refresh button to reload. If the error continues, contact your system administrator.");
      }
    });
  };

  onAttachmentReload = (type, claimObject) => {
    this.setState({ showLoader: false }, () => {
      if (type === 'add') {
        if (claimObject && claimObject.status === CONSTANTS.STATUS_MAP.inquiry) {
          this.setState({ isWorksheetUpdated: true }, () => {
            this.handleUpdateSubmit();
          });
        }
        Alert.success("Document uploaded successfully.");
      } else {
        Alert.success("Document deleted successfully.");
      }
    });
  };

  deleteAttachment = (id) => {
    this.setState({ showLoader: true }, () => {
      ajax(`${apiUrls.vtaClaimDocuments}/${id}`, {}, { method: 'DELETE' }, (data, status) => {
        if (status === 200) {
          this.setState({ showLoader: false }, () => {
            this.loadAttachments(this.onAttachmentReload.bind(this, 'delete'));
          });
        } else {
          this.setState({ showLoader: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  showManagerCheckbox = () => {
    return (!!(userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaimsManager) &&
      (this.state.initialStatus === CONSTANTS.STATUS_MAP.waitingForAuthorization ||
        this.state.initialStatus === CONSTANTS.STATUS_MAP.pendingDenial)));
  };

  handleUpdateSubmit = () => {
    const claimObject = this.state.vta_claim.toJS();
    if (this.state.initialStatus !== CONSTANTS.STATUS_MAP.waitingForAuthorization && claimObject.status === CONSTANTS.STATUS_MAP.waitingForAuthorization) {
      this.setState({ displayUserSelectionModal : true, displayBackConfirmationModal : false }, () => {
        this.getClaimUserList();
      });
    } else {
      this.updateClaimList();
    }
  };

  handleReOpen = () => {
    this.setState({ showLoader: true,  displayUserSelectionModal: false, displayBackConfirmationModal : false, isWorksheetUpdated : false }, () => {
      ajax(`${apiUrls.vtaclaims}/${this.props.location.query.id}/reopen`, {}, { method: 'PUT' }, (data, status) => {
        if (status === 200) {
          Alert.success("Update successful.");
          this.setState({ displayModal: false, showLoader: false }, () => {this.loadClaim();});
        } else {
          this.setState({
            showLoader: false,
          }, () => {
            if (data.errors.vendor_id) {
              Alert.warning(data.errors.vendor_id);
              return;
            }
            Alert.error("Click the Re-Open button again. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  handleOwnerChange = (e) => {
    if (e.target.value) {
      const claimObject = this.state.vta_claim.toJS();
      this.setState({
        newOwnerName: e.target.textContent,
        prevOwnerID: claimObject.owner_id,
        isWorksheetUpdated: true
      });
      this.state.vta_claim.cursor('owner_id').update(() => parseInt(e.target.value));
    }
  };

  renderClaimDetails = (claimObject, isDisabled, showReOpenButton) => {
    return (
      <Header
        claimObject={ claimObject }
        nextButtonOnClick={ showReOpenButton ? this.handleReOpen : this.handleUpdateSubmit }
        backButtonOnClick={ this.handleBackButtonOnClick }
        showHistoryButtonClick={ this.onShowHistoryButtonClick }
        nextButtonDisabled={ isDisabled && !showReOpenButton }
        nextButtonText={ showReOpenButton ? 'Re-Open' : 'Update' }
        nextButtonId={ showReOpenButton ? 'btn-next' : 'btn-reopen' }
        claimStatus={ this.state.initialStatus }
        onUpdateFromGapClick = { this.onUpdateFromGap }
        showSyncGap={ this.state.showSyncGap && !showReOpenButton }
        showVoidCheckButton={ userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaimsManager) && this.state.initialStatus === CONSTANTS.STATUS_MAP.checkWritten }
        handleOnClickVoidCheck={ () => {
          if (claimObject.status === CONSTANTS.STATUS_MAP.checkWritten) {
            this.setState({ showVoidCheckConfirmationModal: true });
          }
        } }
        user={this.props.user}/>
    );
  };

  getClaimUserList = () => {
    this.setState({ showUserListLoader: true }, () => {
      ajax(apiUrls.userList, {}, {}, (data, status) => {
        if (status === 200) {
          this.setState({ userList: data.users, showUserListLoader: false });
        } else {
          this.setState({ showUserListLoader: false });
          Alert.error("Click the browser's Refresh button to reload the user list. If the error continues, contact your system administrator.");
        }
      });
    });
  };

  handleBackButtonOnClick = () => {
    if (this.state.isWorksheetUpdated) {
      this.setState({ displayBackConfirmationModal: true });
    } else {
      this.context.router.goBack();
    }
  };

  handleSaveAndNavigateHome = () => {
    this.setState({
      isWorksheetUpdated: true,
      navigateHomeAfterSave: true
    }, () => {
      this.handleUpdateSubmit();
    });
  };

  setWorksheetUpdated = (isUpdated) => {
    this.setState({ isWorksheetUpdated: isUpdated }, () => {
      if (this.props.setUnsavedChanges) {
        this.props.setUnsavedChanges(isUpdated, this.handleSaveAndNavigateHome);
      }
    });
  };

  onVendorChange = (vendorId) => {
    this.onCursorValueChange('vendor_id', vendorId);
  };

  searchVendor = () => {
    this.setState({ displayVendorSelectionModal: true, vendorId:  this.state.vta_claim.get("vendor_id") });
  };

  onVendorSelect = (data) => {
    this.onCursorValueChange('vendor_id', data.vendor_id);
    this.setState({ displayVendorSelectionModal: false });
  };

  redirectToPrevious = () => {
    this.setState({ displayBackConfirmationModal: false }, () => {
      this.context.router.goBack();
    });
  };

  isViewOnlyRole = () => {
    let r = CONSTANTS.USER_ROLES;
    let u = this.props.user;
    const hasReadRole = hstore.hasAny(u.roles, [r.accounting, r.viewOnlyClaims]);
    const hasWriteRole = hstore.hasAny(u.roles, [r.gapClaims,r.gapClaimsManager]);
    return hasReadRole && !hasWriteRole;
  };

  isFormDisabled = () => {
    if (this.isViewOnlyRole()) {
      return true;
    }
    if ((userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaims) && !userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaimsManager) &&
      (this.state.initialStatus === CONSTANTS.STATUS_MAP.waitingForAuthorization || this.state.initialStatus === CONSTANTS.STATUS_MAP.pendingDenial))) {
      return true;
    }
    return (this.state.initialStatus === CONSTANTS.STATUS_MAP.authorization ||
      this.state.initialStatus === CONSTANTS.STATUS_MAP.checkWritten ||
      this.state.initialStatus === CONSTANTS.STATUS_MAP.checkVoided ||
      this.state.initialStatus === CONSTANTS.STATUS_MAP.denied ||
      this.state.initialStatus === CONSTANTS.STATUS_MAP.closedNoResponse) || false;
  };

  render() {
    const claimObject = this.state.vta_claim.toJS();
    const spinnerMessage = <p className='text-center'><i className="fa fa-refresh fa-spin"/> Loading...</p>;
    const showManagerCheckbox = this.showManagerCheckbox();
    const isDisabled = this.isFormDisabled();
    const isNextDisabled = isDisabled && !(userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaimsManager) &&
                            this.state.initialStatus === CONSTANTS.STATUS_MAP.closedNoResponse);
    const showReOpenButton = this.state.initialStatus === CONSTANTS.STATUS_MAP.denied &&
        userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaimsManager);
    return (
      <Loader show={ this.state.showLoader } message={ spinnerMessage }>
        <section className="claim-worksheet">
          {this.renderClaimDetails(claimObject, isNextDisabled, showReOpenButton)}
          <section className="mt-3 pt-3">
            <section className="row mt-1">
              <section className="col-12" id="parentDiv" ref={ (worksheet) => this.worksheet = worksheet }>
                <form>
                  <fieldset>
                    <label className="form-check form-check-label col-form-label col-form-label-sm">
                      <input type="checkbox"
                        className="form-check-input"
                        id="inProgressCheckBox"
                        checked={ claimObject.is_in_progress }
                        onChange={ this.onCursorToggle.bind(null, 'is_in_progress') }
                        disabled={ isDisabled }/>
                      <span>In Progress</span>
                    </label>
                    <VTAChecklist
                      claimObject={ claimObject }
                      onVendorChange={ this.onVendorChange }
                      showManagerCheckbox={ showManagerCheckbox }
                      searchVendor={ this.searchVendor }
                      onCursorToggle={ this.onCursorToggle }
                      onCursorValueChange={ this.onCursorValueChange }
                      onCursorChange={ this.onCursorChange }
                      getFieldNotes={ this.getFieldNotes }
                      getFieldDocs={ this.getFieldDocs }
                      attachments={ this.state.attachments }
                      notes={ this.state.notes }
                      selectedFieldNote={ this.state.selectedFieldNote }
                      handleItemNoteClick={ !this.isViewOnlyRole() && this.handleFieldNoteClick }
                      handleMouseEnter={ this.handleMouseEnter }
                      handleMouseLeave={ this.handleMouseLeave }
                      handleAttachmentClick={ !this.isViewOnlyRole() && this.handleAttachmentClick }
                      deleteAttachment={ !this.isViewOnlyRole() && this.deleteAttachment }
                      rowType={ CONSTANTS.FIELD_TYPE.static }
                      rowStyle={ style.rowStyle }
                      isDisabled={ isDisabled }
                    />
                    <StatusDropdown claimObject={ claimObject }
                      userHasRole={ userHasRole }
                      handleStatusChange={ this.handleStatusChange }
                      initialStatus={ this.state.initialStatus }
                      status={ this.state.status }
                      user={ this.props.user }
                      statusMap={ CONSTANTS.STATUS_MAP }
                      onCursorChange={ this.onCursorChange }
                      onCursorValueChange={ this.onCursorValueChange }
                      labelCustomClass='col-3'
                      inputCustomClass='col-2'
                      attachments={ this.state.attachments }
                      notes={ this.state.notes }
                      getFieldNotes={ this.getFieldNotes }
                      getFieldDocs={ this.getFieldDocs }
                      selectedFieldNote={ this.state.selectedFieldNote }
                      handleItemNoteClick={ this.handleFieldNoteClick }
                      handleMouseEnter={ this.handleMouseEnter }
                      handleMouseLeave={ this.handleMouseLeave }
                      handleAttachmentClick={ this.handleAttachmentClick }
                      deleteAttachment={ this.deleteAttachment }
                      rowType={ CONSTANTS.FIELD_TYPE.static }
                      rowStyle={ style.rowStyle }/>
                    <PaymentInfo initialStatus={ this.state.initialStatus } claimObject={ claimObject } />
                  </fieldset>
                </form>
                {this.showRecordNotes()}
              </section>
            </section>
          </section>
          <AttachmentModal
            displayModal={ this.state.displayAttachmentModal }
            closeModal={ this.closeAttachmentModal.bind(this, claimObject) }
            claimId={ claimObject.id }
            attachmentParams={ this.state.attachmentParams }/>
          <ConfirmationModal confirmButtonText="Save/Submit"
            declineButtonText="Cancel"
            displayConfirmationModal={ this.state.showAuthorizeConfirmationModal }
            displayMessage={ `Do you want to save and continue to authorize. If not, please press cancel to return to the checklist` }
            onConfirm={ () => {
              this.setState({ showAuthorizeConfirmationModal: false }, () => {
                this.changeStatus(this.state.statusType, this.state.statusValue);
              });
            } }
            onDecline={ () => {
              this.setState({ showAuthorizeConfirmationModal: false });
            } }/>
          <UserSelectionModal userList={ this.state.userList }
            displayModal={ this.state.displayUserSelectionModal }
            showUserListLoader={ this.state.showUserListLoader }
            userHasRole={ userHasRole }
            handleOwnerChange={ this.handleOwnerChange }
            handleModalSubmit={ this.updateClaimList }/>
          <div className="clearfix"/>
          <ConfirmationModal confirmButtonText="Yes"
            declineButtonText="No"
            displayConfirmationModal={ this.state.displayBackConfirmationModal }
            displayMessage="You have unsaved work, do you want to save it before continuing?"
            onConfirm={ this.handleUpdateSubmit }
            onDecline={ this.redirectToPrevious }/>
          <VendorSelectionModal
            searchQuery={ this.state.vendorId  }
            selectBankDetails={ this.onVendorSelect }
            closeBankSelectionModal={ () => {
              this.setState({ displayVendorSelectionModal: false });
            } }
            displayBankSelectionModal={ this.state.displayVendorSelectionModal }/>
          <VoidCheck
            showVoidCheckConfirmationModal={ this.state.showVoidCheckConfirmationModal }
            claimObject={ claimObject }
            onVoidCheckSuccess={ () => { this.setState({showVoidCheckConfirmationModal: false}, () => this.loadClaim()); }}
            initiateLoader={ () => {
              this.setState({ showLoader: true });
            } }
            onVoidCheckError={ () => {
              this.setState({ showLoader: false, showVoidCheckConfirmationModal: false });
            } }
            onDeclineVoid={ () => {
              this.setState({ showVoidCheckConfirmationModal: false });
            } }/>
        </section>
      </Loader>
    );
  }
}

VTAClaimChecklist.propTypes = {
  location: PropTypes.shape({
    query: PropTypes.shape({
      id: PropTypes.string.isRequired,
      contract_number: PropTypes.string,
    }).isRequired,
  }).isRequired,
  user: PropTypes.shape({
    id: PropTypes.number.isRequired,
    first_name: PropTypes.string.isRequired,
    last_name: PropTypes.string.isRequired,
    email: PropTypes.string.isRequired,
    stores: PropTypes.arrayOf(PropTypes.string.isRequired),
    roles: PropTypes.shape({
      Map: PropTypes.object,
    }).isRequired
  })
};

VTAClaimChecklist.contextTypes = {
  router: PropTypes.object.isRequired
};
