import React, { useState } from "react";
import { json as ajax } from "./../../ajax.js";
import { URLS as apiUrls } from "./../reusable/Utilities/urls";
import Alert from "react-s-alert";
import PropTypes from "prop-types";
import Modal from "./../../Modal.jsx";

const VoidCheck = (props) => {
  const [reason, setReason] = useState('');
  const [submitting, setSubmitting] = useState(false);
  const {
    showVoidCheckConfirmationModal,
    onDeclineVoid,
    onVoidCheckSuccess,
    onVoidCheckError,
    claimObject,
  } = props;

  const voidClaim = () => {
    const url = apiUrls.vtaClaimVoid.replace('__claimId__', claimObject.id);
    setSubmitting(true);
    ajax(url, { 'reason': reason }, { method: 'PUT' }, (data, status) => {
      if (status === 200) {
        onVoidCheckSuccess();
      } else {
        onVoidCheckError();
        if (data.message) {
          Alert.error(data.message);  
        } else {
          Alert.error("Click the Void Check button again. If the error continues, contact your system administrator.");
        }
      }
      setSubmitting(false);
    });
  };

  return (
    <div>
      <Modal visible={showVoidCheckConfirmationModal}
        size="small">
        <div className="row">
          <div className="col-12 my-4">
            <p className="text-center">
            Please insert reason for the check void.
            </p>
          </div>
          <div className="row col-12 justify-content-center">
            <div className="col-10 justify-content-start form-group row">
              <label className="col-4">
              Reason:
              </label>
              <textarea type="text"
                id="void_textBox"
                className="form-control form-control-sm col-8"
                placeholder="Reason"
                value={reason}
                onChange={(event) => {
                  setReason(event.target.value);
                }}/>
            </div>
          </div>
          <div className="col-12 text-center my-4">
            <button type="button"
              className="btn btn-secondary cursor-pointer  mr-3"
              onClick={onDeclineVoid}>
                Cancel
            </button>
            <button type="button"
              className="btn btn-primary cursor-pointer"
              onClick={voidClaim}
              disabled={!reason || submitting}>
                Void Check
            </button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

VoidCheck.propTypes = {
  initiateLoader: PropTypes.func,
  onVoidCheckSuccess: PropTypes.func,
  onVoidCheckError: PropTypes.func,
  claimObject: PropTypes.object,
  onDeclineVoid: PropTypes.func,
  showVoidCheckConfirmationModal: PropTypes.bool
};

export default VoidCheck;