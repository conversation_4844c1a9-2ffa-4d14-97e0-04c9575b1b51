import React from "react";
import PropTypes from "prop-types";
import moment from "moment";
import dateFormat from "./../reusable/Utilities/dateFormat.js";
import accounting from "accounting";
import { CONSTANTS } from "../reusable/Constants/constants.js";
import { URLS as apiUrls } from "./../reusable/Utilities/urls";
import { json as ajax } from "./../../ajax.js";
import Alert from "react-s-alert";
import Loader from "react-loader-advanced";

export default class PaymentInfo extends React.Component {

  static propTypes = {
    claimObject: PropTypes.object.isRequired,
    initialStatus: PropTypes.string.isRequired
  };

  constructor(props) {
    super(props);
    this.state = {
      paymentObject: null,
      showLoader: false
    };
  }

  componentDidMount() {
    if ((this.props.initialStatus === CONSTANTS.STATUS_MAP.authorization ||
      this.props.initialStatus === CONSTANTS.STATUS_MAP.checkWritten) && this.props.claimObject && this.props.claimObject.id) {
      this.getPaymentDetails();
    }
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if ((nextProps.initialStatus === CONSTANTS.STATUS_MAP.authorization ||
      nextProps.initialStatus === CONSTANTS.STATUS_MAP.checkWritten) && !this.state.paymentObject && nextProps.claimObject && nextProps.claimObject.id && !this.state.showLoader) {
      this.getPaymentDetails();
    }
  }

  getPaymentDetails = () => {
    this.setState({ showLoader: true }, () => {
      ajax(`${apiUrls.vtaPayments.replace('__claimId__', this.props.claimObject.id)}`, {}, {}, (data, status) => {
        if (status === 200) {
          this.setState({ showLoader: false, paymentObject: data.vta_claim_payment });
        } else {
          this.setState({
            showLoader: false
          }, () => {
            Alert.error("Click the browser's Refresh button to reload the payment data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  renderBillDetails = () => {
    if (this.props.initialStatus === CONSTANTS.STATUS_MAP.authorization ||
      this.props.initialStatus === CONSTANTS.STATUS_MAP.checkWritten) {
      return (
        <div>
          <div className="form-group row">
            <div className="col-6 col-form-label-sm">
              Authorization #:
            </div>
            <div className="col-6 col-form-label-sm">
              {this.state.paymentObject && this.state.paymentObject.authorization_number}
            </div>
          </div>
          <div className="form-group row">
            <div className="col-6 col-form-label-sm">
              Bill Number:
            </div>
            <div className="col-6 col-form-label-sm">
              {this.state.paymentObject && this.state.paymentObject.bill_number}
            </div>
          </div>
        </div>
      );
    }
  };

  renderPaymentInfo = () => {
    if (this.props.initialStatus === CONSTANTS.STATUS_MAP.checkWritten) {
      return (
        <div>
          <div className="form-group row">
            <div className="col-6 col-form-label-sm">
              Check #:
            </div>
            <div className="col-6 col-form-label-sm">
              {this.state.paymentObject && this.state.paymentObject.check_number}
            </div>
          </div>
          <div className="form-group row">
            <div className="col-6 col-form-label-sm">
              Amount:
            </div>
            <div className="col-6 col-form-label-sm">
              {this.state.paymentObject && accounting.formatMoney(this.state.paymentObject.amount, '$', 2)}
            </div>
          </div>
          <div className="form-group row">
            <div className="col-6 col-form-label-sm">
              Paid Date:
            </div>
            <div className="col-6 col-form-label-sm">
              {this.state.paymentObject && moment.utc(this.state.paymentObject.paid_date).local(true).format(dateFormat.displayDateFormat)}
            </div>
          </div>
        </div>
      );
    }
  };

  render() {
    const spinnerMessage = <p className='text-center'><i className="fa fa-refresh fa-spin"/> Loading...</p>;
    return (
      <div  className="col-6 pl-0">
        <Loader show={ this.state.showLoader } message={ spinnerMessage }>
          {this.renderBillDetails()}
          {this.renderPaymentInfo()}
        </Loader>
      </div>
    );
  }
}