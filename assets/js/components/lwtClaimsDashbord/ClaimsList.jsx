import moment from 'moment';
import React from 'react';
import ReactTooltip from 'react-tooltip';
import dateFormat from "./../reusable/Utilities/dateFormat.js";
import PropTypes from 'prop-types';
import If from "./../reusable/If/If.jsx";
import SortIcon from "./../reusable/SortIcon/SortIcon";

const ClaimsList = (props, context) => {
  
  const handleSort = (toBeSorted) => {
    const { sortBy, sortOrder, sortClaimList, enableSorting } = props;
    if (enableSorting) {
      if(toBeSorted === sortBy && sortOrder === 'asc') {
        sortClaimList(sortBy, 'desc');
      } else if(toBeSorted === sortBy && sortOrder === 'desc') {
        sortClaimList(sortBy, 'asc');
      } else {
        sortClaimList(toBeSorted, 'asc');
      }
    }
  };

  const renderTableHeader = () => {
    const {
      sortBy,
      sortOrder,
      enableSorting,
    } = props;
    return (
      <tr>
        <th
          className={ enableSorting? "cursor-pointer": "" }
          id="label-contract-holder"
          onClick={ () => handleSort('customer_name') }>
          Customer Name&nbsp;
          <If condition={ enableSorting }>
            <SortIcon fieldName='customer_name'
              sortBy={ sortBy }
              sortOrder={ sortOrder }/>
          </If>
        </th>
        <th
          className={ enableSorting? "cursor-pointer": "" }
          id="label-contract-holder"
          onClick={ () => handleSort('contract_number') }
        >Contract #&nbsp;
          <SortIcon fieldName='contract_number'
            sortBy={ sortBy }
            sortOrder={ sortOrder }/>
        </th>
        <th 
          className={"cursor-pointer"}
          id="label-contract-holder"
          onClick={ () => handleSort('status') }
        >Status
          <SortIcon fieldName='status'
            sortBy={ sortBy }
            sortOrder={ sortOrder }/>
        </th>
        <th 
          className={"cursor-pointer"}
          id="label-contract-holder"
          onClick={ () => handleSort('assigned_to')}
        >Assigned To
          <SortIcon fieldName='assigned_to'
            sortBy={ sortBy }
            sortOrder={ sortOrder }/>
        </th>
        <th
          className={ enableSorting? "cursor-pointer": "" }
          id="label-contract-holder"
          onClick={ () => handleSort('date_of_claim_received') }
        >Opened&nbsp;
          <SortIcon
            fieldName='date_of_claim_received'
            sortBy={ sortBy }
            sortOrder={ sortOrder }/>
        </th>
        <th
          className={ enableSorting? "cursor-pointer": "" }
          id="label-contract-holder"
          onClick={ () => handleSort('paid_date') }
        >Paid Date&nbsp;
          <SortIcon
            fieldName='paid_date'
            sortBy={ sortBy }
            sortOrder={ sortOrder }/>
        </th>
      </tr>
    );
  };

  const renderTableBody = () => {
    return props.claimList.map(renderTableBodyRow);
  };

  const renderTableBodyRow = (claimData, index) => {
    const longTextStyle = {
      "maxWidth": "130px",
      "overflow": "hidden",
      "textOverflow": "ellipsis",
      "whiteSpace": "nowrap"
    };
    let paidDate = claimData.paid_date;
    if (paidDate) {
      paidDate = moment(paidDate).format(dateFormat.displayDateFormat);
    } 
    return (
      <tr key={ index }>
        <td style={ longTextStyle }>
          <a data-tip
            data-for={ `auto_claims${index}` }
            onClick={ (e) => redirectToWorksheet(e, claimData) }
            href={''}
            className="users">
            {claimData['customer_name']}
          </a>
          <ReactTooltip id={ `auto_claims${index}` } aria-haspopup='true'>
            <p className="text-center">{claimData['customer_name']}</p>
          </ReactTooltip>
        </td>
        <td>
          <a 
            onClick={ (e) => redirectToWorksheet(e, claimData) } 
            href={''}>
            {claimData['contract_number']}
          </a>
        </td>
        <td>
          {claimData['status']}
        </td>
        <td>
          {claimData.assigned_to}
        </td>
        <td>
          {moment(claimData['date_of_claim_received']).format(dateFormat.displayDateFormat)}
        </td>
        <td>
          {paidDate}
        </td>
      </tr>
    );
  };

  const redirectToWorksheet = (e, claimData) => {
    e.preventDefault();
    
    if (claimData.contract_number && claimData.id) {
      const query = {
        id: claimData.id,
        contract_number: claimData.contract_number
      };
      const route = { pathname: "/lwt-claims", query };
      if (!context.router.isActive(route)) {
        context.router.push(route);
      }
    }
  };

  return (
    <div className="table-responsive claim-list">
      <table className="table table-striped">
        <thead>
          {renderTableHeader()}
        </thead>
        <tbody>
          {renderTableBody()}
        </tbody>
      </table>
    </div>
  );
};

ClaimsList.propTypes = {
  claimList: PropTypes.array.isRequired,
  displayReassignment: PropTypes.bool,
  renderReassignmentColumn: PropTypes.func,
  displayAgent: PropTypes.bool,
  displayVIN: PropTypes.bool,
  sortBy: PropTypes.string,
  sortOrder: PropTypes.string,
  sortClaimList: PropTypes.func,
  enableSorting: PropTypes.bool,
};

ClaimsList.contextTypes = {
  router: PropTypes.object.isRequired
};

export default ClaimsList;