import React, { useEffect, useState } from "react";
import PropTypes from "prop-types";
import Alert from "react-s-alert";
import Loader from "react-loader-advanced";
import moment from "moment";
import PageHeader from "../pageHeader/PageHeader.jsx";
import SearchBox from "../reusable/SearchBox/Search.jsx";
import ClaimsList from "./ClaimsList.jsx";
import { json as ajax, raw as ajaxRaw } from "./../../ajax.js";
import Pagination from "./../../Pagination.jsx";
import { CONSTANTS } from "./../reusable/Constants/constants";
import DonutChart from "./../reusable/Charts/DonutChart/DonutChart.jsx";
import * as d3 from "d3";

const LWTClaimsDashboard = (props, context) => {
  const [state, setState] = useState( () => initialState());
  const [claimCount, setClaimCount] = useState(() => initialClaimCount());
  const [claimData, setClaimData] = useState(() => intialClaimsData());
  const [claimOwners, setClaimOwners] = useState(() => []);

  useEffect(() => {
    // Load supporting data
    loadSupportingData();
    
    // Set document title
    document.title = 'TCA Portal - LWT Claims';
  }, [currentPage(), currentQ(), currentStatus(), currentUserId(), currentClaimAge(), currentPayType(), currentSortBy(), currentSortOrder()]);

  // State object to maintain claim count 
  function initialClaimCount () {
    return {
      claimsCountByAge: [],
      claimsCountByAgents: [],
      claimsCountByStatus: [],
      averageWaitingAge: '',
    };
  }

  // State object to maintain claims data 
  function intialClaimsData () {
    return {
      claimList: [],
      numberOfClaims: undefined,
    };
  }

  // State object to handle operations on UI
  function initialState () {
    return {
      pageLimit: 20,
      showLoader: false,
      showChartForStatusLoader: false,
      showChartForAgentsLoader: false,
      showChartForAgeLoader: false,
    };
  }

  // Function to load all required supporting data
  const loadSupportingData = () => {
    loadClaimData();
    claimOwnersList();
  };

  // Method to load the claim list according to filters applied
  const loadClaimData = () => {
    let url = `/api/lwt?page=${window.encodeURIComponent(currentPage())}`;
    if (currentQ()) {
      url += (`&q=${window.encodeURIComponent(currentQ().trim())}`);
    }
    const status = currentStatus();
    if (status) {
      url += (`&status=${window.encodeURIComponent(currentStatus())}`);
    }
    if (currentUserId()) {
      url += (`&user_id=${window.encodeURIComponent(currentUserId())}`);
    }
    if (currentClaimAge()) {
      url += (`&age=${window.encodeURIComponent(currentClaimAge())}`);
    }
    const sortBy = currentSortBy();
    if (sortBy) {
      url += (`&sort_by=${window.encodeURIComponent(sortBy)}`);
    }
    const sortOrder = currentSortOrder();
    if (sortOrder) {
      url += (`&sort_order=${window.encodeURIComponent(sortOrder)}`);
    }
    const payType = currentPayType();
    if(payType){
      url += (`&pay_type=${window.encodeURIComponent(payType)}`);
    }
    setState({ ...state, showLoader: true });
    ajax(url, {}, {}, (data, status) => {
      if (status === 200) {
        setClaimData({claimList: data.lwt_claims, numberOfClaims: data.count});
        setState({
          ...state,
          showLoader: false,
          showChartForStatusLoader: true,
          showChartForAgentsLoader: true,
          showChartForAgeLoader: true
        });
        loadClaimsCount();
      } else {
        setState({ ...state, showLoader: false });
        Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
      }
    });
  };

  // method to load owners list
  const claimOwnersList = () => {
    ajax(`/api/users/claim-owners/lwt`, {}, {}, (data, status) => {
      if (status === 200) {
        setClaimOwners(data.claim_owners);
      } else {
        Alert.error("Click the browser's Refresh button to reload the user list. If the error continues, contact your system administrator.");
      }
    });
  };
  
  // Returns current page to load claims
  function currentPage () {
    return parseInt(props.location.query.page, 10) || 0;
  }

  // Returns current query to load claims
  function currentQ  () {
    return props.location.query.q || '';
  }

  // Returns current user id to load claims
  function currentUserId  ()  {
    if (props.location.query.userId === 'all') {
      return 'all';
    } else if (!props.location.query.userId) {
      return props.user.id;
    } else {
      return props.location.query.userId;
    }
  }

  // Returns current pay type to load claims
  function currentPayType () {
    return props.location.query.payType || "all";
  }

  // Returns current status to load claims
  function currentStatus (){
    return props.location.query.status || "all";
  }

  // Returns current claim age to laod claims
  function currentClaimAge ()  {
    return props.location.query.age;
  }

  // Returns current sort order for claim list
  function currentSortOrder () {
    return props.location.query.sortOrder || 'desc';
  }

  // Returns current sort by property for claim list
  function currentSortBy () {
    return props.location.query.sortBy || 'date_of_claim_received';
  }

  // method to fetch claim count by status, agent and age
  const loadClaimsCount = () => {
    const url = '/api/lwt/count';
    ajax(url, {}, {}, (results, status) => {
      if (status === 200) {
        setClaimCount({
          claimsCountByAge: results.age_count, 
          claimsCountByAgents: results.agents_count,
          claimsCountByStatus: results.status_count,
          averageWaitingAge: results.average_waiting_period,
        });
          
        setState({
          ...state,
          showChartForStatusLoader: false,
          showChartForAgentsLoader: false,
          showChartForAgeLoader: false,
        });
      } else {
        Alert.error('Error loading claim count data for dashboard.'); 
      }
    });
  };

  // method to set the page for the claim list
  const setPage = (page) => {
    const query = { page };
    if (currentQ()) {
      query.q = currentQ();
    }
    if (currentStatus()) {
      query.status = currentStatus();
    }
    if (currentUserId()) {
      query.userId = currentUserId();
    }
    if (currentClaimAge()) {
      query.age = currentClaimAge();
    }
    let sortBy = currentSortBy();
    if (sortBy) {
      query.sortBy = sortBy;
    }
    const sortOrder = currentSortOrder();
    if (sortOrder) {
      query.sortOrder = sortOrder;
    }
    const payType = currentPayType();
    if (payType) {
      query.payType = payType;
    }
    const route = { pathname: "/lwt-claims-list/", query };
    if (!context.router.isActive(route)) {
      context.router.push(route);
    }
  };

  // method to filter the claim list according to parameters
  const onSearch = (q) => {
    if (currentQ() !== q) {
      const query = { q, page: 1 };
      if (currentStatus()) {
        query.status = currentStatus();
      }
      if (currentUserId()) {
        query.userId = currentUserId();
      }
      if (currentClaimAge()) {
        query.age = currentClaimAge();
      }
      let sortBy = currentSortBy();
      if (sortBy) {
        query.sortBy = sortBy;
      }
      const sortOrder = currentSortOrder();
      if (sortOrder) {
        query.sortOrder = sortOrder;
      }
      const payType = currentPayType();
      if (payType) {
        query.payType = payType;
      }
      const route = { pathname: "/lwt-claims-list/", query };
      if (!context.router.isActive(route)) {
        context.router.push(route);
      }
    }
  };

  // method to get claims by status
  const getClaimsByStatus = (event) => {
    const query = { status: event.target.value, page: 1 };
    if (currentQ()) {
      query.q = currentQ();
    }
    if (currentUserId()) {
      query.userId = currentUserId();
    }
    if (currentClaimAge()) {
      query.age = currentClaimAge();
    }
    let sortBy = currentSortBy();
    if (sortBy) {
      query.sortBy = sortBy;
    }
    const sortOrder = currentSortOrder();
    if (sortOrder) {
      query.sortOrder = sortOrder;
    }
    const payType = currentPayType();
    if (payType) {
      query.payType = payType;
    }
    const route = { pathname: "/lwt-claims-list/", query };
    if (!context.router.isActive(route)) {
      context.router.push(route);
    }
  };

  // method to get claism by owners
  const handleClaimOwnerChange = (e) => {
    const query = { page: 1, userId: e.target.value };
    if (currentQ()) {
      query.q = currentQ();
    }
    if (currentStatus()) {
      query.status = currentStatus();
    }
    if (currentClaimAge()) {
      query.age = currentClaimAge();
    }
    let sortBy = currentSortBy();
    if (sortBy) {
      query.sortBy = sortBy;
    }
    const sortOrder = currentSortOrder();
    if (sortOrder) {
      query.sortOrder = sortOrder;
    }
    const payType = currentPayType();
    if (payType) {
      query.payType = payType;
    }
    const route = { pathname: "/lwt-claims-list/", query };
    if (!context.router.isActive(route)) {
      context.router.push(route);
    }
  };

  // method to sort the claims list by property
  const sortClaimList = (sortBy, sortOrder) => {
    let query = { sortBy, sortOrder };
    
    if (currentQ()) {
      query.q = currentQ();
    }

    if (currentStatus()) {
      query.status = currentStatus();
    }

    if (currentPage()) {
      query.page = currentPage();
    }

    if (currentClaimAge()) {
      query.age = currentClaimAge();
    }

    if (currentUserId()) {
      query.userId = currentUserId();
    }

    const route = { pathname: "/lwt-claims-list/", query };
    if (!context.router.isActive(route)) {
      context.router.push(route);
    }
  };
  
  // method to handle filter the claims by age
  const handleAgeFilterChange = (e) => {
    const query = { page: 1, age: e.target.value };
    if (currentQ()) {
      query.q = currentQ();
    }
    if (currentStatus()) {
      query.status = currentStatus();
    }
    if (currentUserId()) {
      query.userId = currentUserId();
    }
    const payType = currentPayType();
    if (payType) {
      query.payType = payType;
    }
    const route = { pathname: "/lwt-claims-list/", query };
    if (!context.router.isActive(route)) {
      context.router.push(route);
    }
  };

  // handles export functionality for the claims
  const onExport = () => {
    let url = `/api/lwt?csv=true`;
    if (currentQ()) {
      url += (`&q=${window.encodeURIComponent(currentQ().trim())}`);
    }
    if (currentStatus()) {
      url += (`&status=${window.encodeURIComponent(currentStatus())}`);
    }
    if (currentUserId()) {
      url += (`&user_id=${window.encodeURIComponent(currentUserId())}`);
    }
    setState({ ...state, showLoader: true });
    ajaxRaw(url, {responseType: 'blob'}, (data) => {
      if (data.status === 200) {
        setState({ showLoader: false });
        window.URL = window.URL || window.webkitURL;
        const csvURL = window.URL.createObjectURL(data.response);
        const fileName = `Worksheet_${moment(new Date()).format("YYYY-MM-DDTHH:mm:ss").toString()}.csv`;
        const downloadLink = document.createElement('a');
        downloadLink.setAttribute("href", csvURL);
        downloadLink.setAttribute("download", fileName);
        downloadLink.style.cssText = 'display:none';
        document.body.appendChild(downloadLink);
        downloadLink.click();
      } else {
        setState({ ...state, showLoader: false });
        Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
      }
    });
  };

  // loads data according to status chart legend selection
  const onStatusChartLegendClick = (status) => {
    const query = { status, page: 1, userId: "All", age: "", donutFilter: status };
    const route = { pathname: "/lwt-claims-list", query };
    if (!context.router.isActive(route)) {
      context.router.push(route);
    }
  };

  // loads data according to agent chart legend selection
  const onAgentChartLegendClick = (agentName, owner_id) => {
    const query = {
      status: "all",
      page: 1,
      userId: owner_id,
      age: "",
      donutFilter: owner_id
    };
    const route = { pathname: "/lwt-claims-list", query };
    if (!context.router.isActive(route)) {
      context.router.push(route);
    }
  };

  // loads data according to age chart legend selection
  const onAgeChartLegendClick = (age) => {
    let ageText = "";
    switch (age) {
    case "< 1 Week":
      ageText = "LessThan1Week";
      break;
    case "1-2 Weeks":
      ageText = "OneToTwoWeeks";
      break;
    case "2-3 Weeks":
      ageText = "TwoToThreeWeeks";
      break;
    case "3-4 Weeks":
      ageText = "ThreeToFourWeeks";
      break;
    case "> Month":
      ageText = "GreaterThan1Month";
      break;
    }
    const query = {
      age: ageText,
      status: "all",
      page: 1,
      userId: "All",
      donutFilter: age
    };
    const route = { pathname: "/lwt-claims-list", query };
    if (!context.router.isActive(route)) {
      context.router.push(route);
    }
  };

  // Render claim owners dropdown
  const renderClaimOwnerDropdown = () => {
    if (claimOwners.length > 0) {
      return claimOwners.map((user) => {
        return (<option value={ user.id } key={ user.id }>{`${user.first_name} ${user.last_name}`}</option>);
      });
    }
  };

  // renders filters for the claim list
  const renderClaimListFilters = () => {
    return (
      <div>
        <div className="d-flex flex-row my-4">
          <div className="form-inline">
            <div className="form-group">
              <label htmlFor="list-claim-status" className="pr-1">Status: </label>
              <select id='list-claim-status'
                className="form-control"
                value={ currentStatus() }
                onChange={ getClaimsByStatus }>
                <option value='all'>All</option>
                <option value={ CONSTANTS.LWT_CLAIM_STATUS_MAP.inquiry }>
                  {CONSTANTS.LWT_CLAIM_STATUS_DISPLAY_MAP.Inquiry}
                </option>
                <option value={ CONSTANTS.LWT_CLAIM_STATUS_MAP.claimInProcess }>
                  {CONSTANTS.LWT_CLAIM_STATUS_DISPLAY_MAP.ClaimInProcess}
                </option>
                <option value={ CONSTANTS.LWT_CLAIM_STATUS_MAP.pendingApproval }>
                  {CONSTANTS.LWT_CLAIM_STATUS_DISPLAY_MAP.PendingApproval}
                </option>
                <option value={ CONSTANTS.LWT_CLAIM_STATUS_MAP.approved }>
                  {CONSTANTS.LWT_CLAIM_STATUS_DISPLAY_MAP.Approved}
                </option>
                <option value={ CONSTANTS.LWT_CLAIM_STATUS_MAP.checkWritten }>
                  {CONSTANTS.LWT_CLAIM_STATUS_DISPLAY_MAP.CheckWritten}
                </option>
                <option value={ CONSTANTS.LWT_CLAIM_STATUS_MAP.noResponse }>
                  {CONSTANTS.LWT_CLAIM_STATUS_DISPLAY_MAP.NoResponse}
                </option>
                <option value={ CONSTANTS.LWT_CLAIM_STATUS_MAP.returnedClaim }>
                  {CONSTANTS.LWT_CLAIM_STATUS_DISPLAY_MAP.ReturnedClaim}
                </option>
                <option value={ CONSTANTS.LWT_CLAIM_STATUS_MAP.waitingForCheck }>
                  {CONSTANTS.LWT_CLAIM_STATUS_DISPLAY_MAP.WaitingForCheck}
                </option>
                <option value={ CONSTANTS.LWT_CLAIM_STATUS_MAP.denied }>
                  {CONSTANTS.LWT_CLAIM_STATUS_DISPLAY_MAP.Denied}
                </option>
                <option value={ CONSTANTS.LWT_CLAIM_STATUS_MAP.deactivated }>
                  {CONSTANTS.LWT_CLAIM_STATUS_DISPLAY_MAP.Deactivated}
                </option>
              </select>
            </div>
            <div className="form-group px-3">
              <label htmlFor="list-claim-owner" className="pr-1">Assigned to: </label>
              <select id='list-claim-owner'
                className="form-control"
                value={ currentUserId() }
                onChange={ handleClaimOwnerChange }>
                <option value='all'>All</option>
                {renderClaimOwnerDropdown()}
              </select>
            </div>
            <div className="form-group">
              <label htmlFor="list-claim-age" className="pr-1">Age: </label>
              <select id='list-claim-age' className="form-control" value={ currentClaimAge() }
                onChange={ handleAgeFilterChange }>
                <option value='all'>All</option>
                <option value='LessThan1Week'>{"< 1 Week"}</option>
                <option value='OneToTwoWeeks'>{"1-2 Weeks"}</option>
                <option value='TwoToThreeWeeks'>{"2-3 Weeks"}</option>
                <option value='ThreeToFourWeeks'>{"3-4 Weeks"}</option>
                <option value='GreaterThan1Month'>{"> Month"}</option>
              </select>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // renders claim list for the selected filter and sort order
  const renderClaimList = () => {
    if (claimData.claimList && claimData.claimList.length > 0) {
      return (
        <ClaimsList
          claimList={ claimData.claimList }
          displayAgent={ true }
          sortClaimList={ sortClaimList }
          sortOrder={ currentSortOrder() }
          sortBy={ currentSortBy() }
          enableSorting={ true }
        />);
    } else if (currentQ()) {
      return (
        <div className="text-center">
          <p>No Results available for this search query</p>
        </div>
      );
    }
  };

  // renders pagination details for the available claim data
  const renderPagination = () => {
    return (
      <div className="pagination-container clearfix col-12 px-0" style={{marginBottom: 20}}>
        <div className="pull-left my-1">
          <p>Showing {currentPage() * state.pageLimit - state.pageLimit + 1}&nbsp;
              to {currentPage() * state.pageLimit > state.numberOfClaims ? claimData.numberOfClaims : currentPage() * state.pageLimit}&nbsp;
              of {claimData.numberOfClaims} items
          </p>
        </div>
        <div className="float-right">
          <Pagination page={ currentPage() } count={ claimData.numberOfClaims }
            limit={ state.pageLimit } setPage={ setPage }/>
        </div>
      </div>
    );
  };

  // method renders export button on UI
  const renderExportButton = () => {
    return (
      <button onClick={ onExport }
        className="btn btn-secondary mr-4"
        id="btn-export"
        disabled={ (props.location.query.userId !== 'all') }>
        <i className="fa fa-file-excel-o"/>
        &nbsp;Export
      </button>
    );
  };

  // renders the donut chart according to claim status
  const renderStatusDonutChart = (spinnerMessage) => {
    const { claimsCountByStatus } = claimCount;
    const openClaims = claimsCountByStatus && claimsCountByStatus.length ? claimsCountByStatus[ 0 ].count.toString() : '';
    const chartConfig = {
      width: 350,
      height: 200,
      innerRadius: 35,
      outerRadius: 50,
      sectorStrokeWidth: "0px",
      centerTextTitle: "Open",
      centerTextCount: openClaims,
      legendBulletSize: 10,
      legendStyle: { fontSize: "12px" },
      legendHeaderStyle: { fontSize: "14px" },
      legendColumnHeaders: [ "Status", "Claims" ],
      legendColumnXPositions: [ 15, 120 ],
      barColors: [ '#41a6cb', '#2bac8b', '#8b9bac', '#efd448', '#b13b6b', '#a83224', '#e66b26' ],
      selectedBar: props.location.query.donutFilter ? props.location.query.donutFilter.toString() : ''
    };
    return (
      <Loader show={ state.showChartForStatusLoader } message={ spinnerMessage }>
        <div id="DonutChart_Status">
          <h6>Claim Status</h6>
          <DonutChart config={ chartConfig }
            data={ claimsCountByStatus || [] }
            onLegendClick={ onStatusChartLegendClick }/>
        </div>
      </Loader>
    );
  };

  // renders the donut chart according to claim agents
  const renderAgentsDonutChart = (spinnerMessage) => {
    const claimsCountByAgents = claimCount.claimsCountByAgents || [];
    const total = claimsCountByAgents.reduce((sum, value) => sum + value.count, 0);
    const avgClaims = claimsCountByAgents.length && total ? Math.floor(total / claimsCountByAgents.length) : "";
    const chartConfig = {
      width: 350,
      height: 200,
      innerRadius: 35,
      outerRadius: 50,
      sectorStrokeWidth: "0px",
      centerTextTitle: "Avg Per",
      centerTextCount: avgClaims.toString(),
      legendBulletSize: 10,
      legendStyle: { fontSize: "12px" },
      legendHeaderStyle: { fontSize: "14px" },
      legendColumnHeaders: [ "Agent", "Claims" ],
      legendColumnXPositions: [ 15, 150 ],
      barColors: d3.schemeCategory20,
      selectedBar: props.location.query.donutFilter ? props.location.query.donutFilter.toString() : ''
    };
    // Add ellipsis to overflowing names
    let data = claimsCountByAgents.map(function (element) {
      if (element.name.length > 18) {
        element.name = element.name.slice(0, 18) + "...";
      }
      return element;
    });
    return (
      <Loader show={ state.showChartForStatusLoader } message={ spinnerMessage }>
        <div id="DonutChart_Status">
          <h6>Agent Claim Distribution</h6>
          <DonutChart config={ chartConfig } data={ data } onLegendClick={ onAgentChartLegendClick }/>
        </div>
      </Loader>
    );
  };

  // renders the donut chart according to claim age
  const renderAgeDonutChart = (spinnerMessage) => {
    const chartConfig = {
      width: 350,
      height: 200,
      innerRadius: 35,
      outerRadius: 50,
      sectorStrokeWidth: "0px",
      centerTextTitle: "Week Avg",
      centerTextCount: claimCount.averageWaitingAge.toString(),
      legendBulletSize: 10,
      legendStyle: { fontSize: "12px" },
      legendHeaderStyle: { fontSize: "14px" },
      legendColumnHeaders: [ "Age", "Claims" ],
      legendColumnXPositions: [ 15, 100 ],
      barColors: [ '#7ed321', '#4a90e2', '#f8e71c', '#f5a623', '#d0021b' ],
      selectedBar: props.location.query.donutFilter ? props.location.query.donutFilter.toString() : ''
    };
    return (
      <Loader show={ state.showChartForStatusLoader } message={ spinnerMessage }>
        <div id="DonutChart_Status">
          <h6>Claim Age</h6>
          <DonutChart config={ chartConfig } data={ claimCount.claimsCountByAge || [] }
            onLegendClick={ onAgeChartLegendClick }/>
        </div>
      </Loader>
    );
  };


  // method renders all the graphs and charts on UI
  const renderGraphView = (spinnerMessage) => {
    return (
      <div className="row my-4">
        <div className="col-4">
          {renderStatusDonutChart(spinnerMessage)}
        </div>
        <div className="col-4">
          {renderAgentsDonutChart(spinnerMessage)}
        </div>
        <div className="col-4">
          {renderAgeDonutChart(spinnerMessage)}
        </div>
      </div>
    );
  };

  // Renders quote form on UI
  const renderDashboard = () => {
    const spinnerMessage = <p className='text-center'><i className="fa fa-refresh fa-spin"/> Loading...</p>;
    return (
      <Loader show={ state.showLoader } message={ spinnerMessage }>
        <div className="row gap-dashboard">
          <div className="col-12">
            <PageHeader pageTitle="LWT Claims" user={props.user}/>
            <div className="row">
              <div className="form-inline my-2 col-12">
                <div className="pull-left col-6 px-0">
                  <SearchBox onSearch={ onSearch }
                    placeholder="Search Name, Contract #, VIN (10), RO"
                    value={ currentQ() }/>
                </div>
                <div className="col-6 px-0 float-right">
                  <div className="form-group float-right">
                    {renderExportButton()}
                    <a href='/contracts' className="btn btn-primary" id="btn-search-contracts">
                      <i className="fa fa-search pr-1"/>
                      Search Contracts
                    </a>
                  </div>
                </div>
              </div>
            </div>
            {renderGraphView(spinnerMessage)}
            {renderClaimListFilters()}
            {renderClaimList()}
            {renderPagination()}
          </div>
        </div>
      </Loader>
    );
  };

  return (
    <div>
      { renderDashboard() }
    </div>
  );
};


LWTClaimsDashboard.contextTypes = {
  router: PropTypes.object.isRequired,
};
  
LWTClaimsDashboard.propTypes = {
  user: PropTypes.shape({
    id: PropTypes.number.isRequired,
    first_name: PropTypes.string.isRequired,
    last_name: PropTypes.string.isRequired,
    email: PropTypes.string.isRequired,
    stores: PropTypes.arrayOf(PropTypes.string.isRequired),
    roles: PropTypes.shape({
      Map: PropTypes.object
    }).isRequired
  }),
  location: PropTypes.shape({
    query: PropTypes.shape({
      page: PropTypes.string,
      q: PropTypes.string,
      userId: PropTypes.string,
      status: PropTypes.string,
      age: PropTypes.string,
      donutFilter: PropTypes.string,
      sortBy: PropTypes.string,
      sortOrder: PropTypes.string,
      payType: PropTypes.string,
    }).isRequired,
  }).isRequired,
};

export default LWTClaimsDashboard;