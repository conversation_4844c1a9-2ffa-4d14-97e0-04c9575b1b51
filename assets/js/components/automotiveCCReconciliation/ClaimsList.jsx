import React, { Component } from "react";
import ReactTooltip from "react-tooltip";
import dateFormat from "./../reusable/Utilities/dateFormat.js";
import PropTypes from "prop-types";
import accounting from "accounting";
import moment from "moment";
import SortIcon from "./../reusable/SortIcon/SortIcon.jsx";
import InputBox from './../reusable/InputBox/InputBox.jsx';
import NoteModal from "../automotiveClaimWorksheet/NoteModal.jsx";
import If from "../reusable/If/If.jsx";

export default class ClaimsList extends Component {

  constructor(props) {
    super(props);
    this.state = {
      showNoteModal: false,
      selectedClaim: {},
    };
  }

  handleSort = (toBeSorted) => {
    const { sortBy, sortOrder, sortClaimList } = this.props;
    if(toBeSorted === sortBy && sortOrder === 'asc') {
      sortClaimList(sortBy, 'desc');
    } else if(toBeSorted === sortBy && sortOrder === 'desc') {
      sortClaimList(sortBy, 'asc');
    } else {
      sortClaimList(toBeSorted, 'asc');
    }
  };

  closeNoteModal = () => {
    this.setState({ showNoteModal: false, selectedClaim: {} });
  };

  displayNoteModal = (selectedClaim) => {
    this.setState({ showNoteModal: true, selectedClaim });
  };

  renderTableHeader = () => {
    const {
      sortBy,
      sortOrder,
      isHistory
    } = this.props;
    return (
      <tr>
        <If condition={ !isHistory }>
          <th />
        </If>
        <th className="cursor-pointer"
          id="label-date-of-payment-received"
          onClick={ this.handleSort.bind(this, 'date_of_payment_received') }>
          {`Date `}
          <SortIcon fieldName='date_of_payment_received'
            sortBy={ sortBy }
            sortOrder={ sortOrder }/>
        </th>
        <th
          id="label-amount">
          $ Amount
        </th>
        <th className="cursor-pointer"
          id="label-ven-number"
          onClick={ this.handleSort.bind(this, 'vendor_id') }>
          {`Vendor # `}
          <SortIcon fieldName='vendor_id'
            sortBy={ sortBy }
            sortOrder={ sortOrder }/>
        </th>
        <th className="cursor-pointer"
          id="label-facility"
          onClick={ this.handleSort.bind(this, 'facility_name') }>
          {`Facility `}
          <SortIcon fieldName='facility_name'
            sortBy={ sortBy }
            sortOrder={ sortOrder }/>
        </th>
        <th className="cursor-pointer"
          id="label-contract-number"
          onClick={ this.handleSort.bind(this, 'contract_number') }>
          {`Contract # `}
          <SortIcon fieldName='contract_number'
            sortBy={ sortBy }
            sortOrder={ sortOrder }/>
        </th>
        <th className="cursor-pointer"
          id="label-ro-number"
          onClick={ this.handleSort.bind(this, 'ro') }>
          {`RO # `}
          <SortIcon fieldName='ro'
            sortBy={ sortBy }
            sortOrder={ sortOrder }/>
        </th>
        <th
          id="label-statement-amount">
          Statement Amount
        </th>
        <th
          id="label-difference">
          Difference
        </th>
        <If condition={ !isHistory }>
          <th />
        </If>
      </tr>
    );
  };

  renderTableBody = () => {
    return this.props.claimList.map(this.renderTableBodyRow);
  };

  renderTableBodyRow = (claimData, index) => {
    const { isHistory } = this.props;
    return (
      <tr key={ index }>
        <If condition={ !isHistory }>
          <td>
            <input type='checkbox'
              className='mr-2'
              checked={ claimData.is_reconciled }
              onChange={ this.props.handleToggle.bind(null, claimData.id) } />
          </td>
        </If>
        <td>
          {moment(claimData['date_of_payment_received']).format(dateFormat.displayDateFormat)}
        </td>
        <td>
          { accounting.formatMoney(claimData.estimate, '$', 2) }
        </td>
        <td>
          { claimData.vendor_id.Valid ? claimData.vendor_id.String : '' }
        </td>
        <td className="cursor-pointer long-text">
          <a href="#!"
            data-tip
            data-for={ `facility_name${index}` }
            onClick={ this.redirectToWorksheet.bind(this, claimData) }
            className="users">
            {claimData['facility_name']}
          </a>
          <ReactTooltip id={ `facility_name${index}` } aria-haspopup='true'>
            <p >{claimData['facility_name']}</p>
          </ReactTooltip>
        </td>
        <td>
          <a href="#!"
            onClick={ this.redirectToWorksheet.bind(this, claimData) }>
            {claimData['contract_number']}
          </a>
        </td>
        <td>
          {claimData['ro']}
        </td>
        <td>
          <If condition={ !isHistory }>
            <div>
              <div className="d-inline-block">
                <InputBox type="Currency"
                  id={ `${claimData.id}_inputBox` }
                  customClass="pl-0"
                  hasDefaultValue={ true }
                  value={ claimData.statement_amount }
                  onChange={ this.props.handleChange.bind(null, claimData.id) }
                  onBlur={ this.props.handleChange.bind(null, claimData.id) }/>
              </div>
              <div className="d-inline-block ml-2 h5">
                <i className={ (claimData.estimate && !isNaN(claimData.statement_amount) && (claimData.estimate - claimData.statement_amount) !== 0) ? "fa fa-times-rectangle-o text-danger" : "fa fa-times-rectangle-o" } />
              </div>
            </div>
          </If>
          <If condition={ isHistory }>
            <div>
              { accounting.formatMoney(!isNaN(claimData.statement_amount) ? claimData.statement_amount : 0, '$', 2) }
            </div>
          </If>
        </td>
        <td className={ (claimData.estimate && !isNaN(claimData.statement_amount) && (claimData.estimate - claimData.statement_amount) < 0) ? "text-danger" : "" }>
          { accounting.formatMoney(!isNaN(claimData.statement_amount) ? (claimData.estimate - claimData.statement_amount) : '', '$', 2) }
        </td>
        <If condition={ !isHistory }>
          <td>
            <i aria-hidden="true"
              id={ `${claimData.id}_itemNote` }
              className="fa fa-file-text-o cursor-pointer ml-3"
              onClick={ this.displayNoteModal.bind(this, claimData) }/>
          </td>
        </If>
      </tr>
    );
  };

  redirectToWorksheet = (claimData) => {
    if (claimData.contract_number && claimData.id) {
      const query = {
        id: claimData.id,
        contract_number: claimData.contract_number
      };
      const route = { pathname: "/automotive-claims", query };
      if (!this.context.router.isActive(route)) {
        this.context.router.push(route);
      }
    }
  };

  render() {
    return (
      <div className="claim-list table-responsive">
        <table className="table table-striped">
          <thead>
            {this.renderTableHeader()}
          </thead>
          <tbody>
            {this.renderTableBody()}
          </tbody>
        </table>
        <NoteModal
          closeNoteModal={ this.closeNoteModal }
          showNoteModal={ this.state.showNoteModal }
          noteType="manual"
          hideNotes={ true }
          createdByUserID={ this.props.user.id }
          claimObject={ this.state.selectedClaim }/>
      </div>
    );
  }
}

ClaimsList.propTypes = {
  claimList: PropTypes.array.isRequired,
  sortClaimList: PropTypes.func.isRequired,
  sortOrder: PropTypes.oneOf(['asc', 'desc', '']),
  sortBy: PropTypes.string,
  handleChange: PropTypes.func,
  handleToggle: PropTypes.func,
  isHistory: PropTypes.bool,
  user: PropTypes.shape({
    id: PropTypes.number.isRequired,
    first_name: PropTypes.string.isRequired,
    last_name: PropTypes.string.isRequired,
    email: PropTypes.string.isRequired,
    stores: PropTypes.arrayOf(PropTypes.string.isRequired),
    roles: PropTypes.shape({
      Map: PropTypes.object,
    }).isRequired
  })
};

ClaimsList.defaultProps = {
  handleChange: () => {},
  handleToggle: () => {}
};

ClaimsList.contextTypes = {
  router: PropTypes.object.isRequired
};