import React, { Component } from 'react';
import PageHeader from '../pageHeader/PageHeader.jsx';
import <PERSON><PERSON> from 'react-s-alert';
import { json as ajax } from './../../ajax.js';
import Loader from 'react-loader-advanced';
import { URLS as apiUrls } from "./../reusable/Utilities/urls";
import PropTypes from 'prop-types';
import SearchBox from "../reusable/SearchBox/Search.jsx";
import ClaimsList from "./ClaimsList.jsx";
import If from '../reusable/If/If.jsx';

export default class AutomotiveCCReconciliation extends Component {
  constructor(props) {
    super(props);
    this.state = {
      claimList: [],
      showLoader: false,
      isWorksheetUpdated: false
    };
  }

  static contextTypes = {
    router: PropTypes.object.isRequired
  };

  static propTypes = {
    user: PropTypes.shape({
      id: PropTypes.number.isRequired,
      first_name: PropTypes.string.isRequired,
      last_name: PropTypes.string.isRequired,
      email: PropTypes.string.isRequired,
      stores: PropTypes.arrayOf(PropTypes.string.isRequired),
      roles: PropTypes.shape({
        Map: PropTypes.object,
      }).isRequired,
    }),
    location: PropTypes.shape({
      query: PropTypes.shape({
        sortOrder: PropTypes.string,
        sortBy: PropTypes.string,
        q: PropTypes.string,
      }).isRequired,
    }).isRequired
  };

  componentDidMount() {
    document.title = 'TCA Portal - Automotive Credit Card Reconciliation';
    this.loadList();
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (this.currentQ(nextProps) !== this.currentQ(this.props) ||
      this.currentSortOrder(nextProps) !== this.currentSortOrder(this.props) ||
      this.currentSortBy(nextProps) !== this.currentSortBy(this.props)) {
      this.loadList(nextProps);
    }
  }

  loadList = (props) => {
    if (!props) {
      props = this.props;
    }
    let url = `${apiUrls.automotiveReconciliation}?`;
    if (this.currentQ(props)) {
      url += (`q=${window.encodeURIComponent(this.currentQ(props).trim())}`);
    }
    if(this.currentSortBy(props)) {
      url += (`&sort_by=${window.encodeURIComponent(this.currentSortBy(props))}`);
    }
    if(this.currentSortOrder(props)) {
      url += (`&sort_order=${window.encodeURIComponent(this.currentSortOrder(props))}`);
    }
    this.setState({ showLoader: true }, () => {
      ajax(url, {}, {}, (data, status) => {
        if (status === 200) {
          this.setState({
            claimList: data.reconciliations || [],
            showLoader: false,
            isWorksheetUpdated: false
          });
        } else {
          this.setState({ showLoader: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  updateList = () => {
    this.setState({ showLoader: true }, () => {
      ajax(apiUrls.automotiveReconciliation, this.state.claimList, { method: 'PUT' }, (data, status) => {
        if (status === 200) {
          this.loadList(this.props);
        } else {
          this.setState({ showLoader: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  currentQ = (props) => {
    return props.location.query.q;
  };

  currentSortOrder = (props) => {
    return props.location.query.sortOrder || '';
  };

  currentSortBy = (props) => {
    return props.location.query.sortBy || '';
  };
  
  onSearch = (q) => {
    if (this.currentQ(this.props) !== q) {
      const query = { q };
      if (this.currentSortBy(this.props)) {
        query.sortBy = this.currentSortBy(this.props);
      }
      if (this.currentSortOrder(this.props)) {
        query.sortOrder = this.currentSortOrder(this.props);
      }
      const route = { pathname: "/cc-reconciliation", query };
      if (!this.context.router.isActive(route)) {
        this.context.router.push(route);
      }
    }
  }

  sortClaimList = (sortBy, sortOrder) => {
    let query = { sortBy, sortOrder };
    if (this.currentQ(this.props)) {
      query.q = this.currentQ(this.props);
    }
    const route = { pathname: "/cc-reconciliation", query };
    if (!this.context.router.isActive(route)) {
      this.context.router.push(route);
    }
  };

  handleChange = (id, value) => {
    let claimList = this.state.claimList.map((claim) => {
      if(claim.id === id) {
        claim.statement_amount = value;
      }
      return claim;
    });
    this.setState({ claimList, isWorksheetUpdated: true });
  };

  handleToggle = (id) => {
    let claimList = this.state.claimList.map((claim) => {
      if(claim.id === id) {
        claim.is_reconciled = !claim.is_reconciled;
      }
      return claim;
    });
    this.setState({ claimList, isWorksheetUpdated: true });
  };

  renderList = () => {
    if (this.state.claimList.length > 0) {
      return (
        <ClaimsList claimList={ this.state.claimList }
          sortClaimList={ this.sortClaimList }
          handleChange={ this.handleChange }
          handleToggle={ this.handleToggle }
          isHistory={ false }
          user={ this.props.user }
          sortOrder={ this.currentSortOrder(this.props) }
          sortBy={ this.currentSortBy(this.props) }  />);
    } else {
      return (
        <div className="text-center">
          <p>No Results available.</p>
        </div>
      );
    }
  };

  render() {
    const spinnerMessage = <p className='text-center'><i className="fa fa-refresh fa-spin"/> Loading...</p>;
    return (
      <Loader show={ this.state.showLoader } message={ spinnerMessage }>
        <section className="gap-dashboard clearfix">
          <div className="row">
            <div className="col-12">
              <PageHeader pageTitle="Credit Card Reconciliation" user={this.props.user}/>
              <div className="row">
                <div className="form-inline my-2 col-12">
                  <div className="pull-left col-6 px-0">
                    <SearchBox onSearch={ this.onSearch }
                      placeholder="Search Facility, Contract #, VEN # or Amount"
                      value={ this.currentQ(this.props) }/>
                  </div>
                </div>
              </div>
              {this.renderList()}
              <If condition={ this.state.claimList.length > 0 }>
                <div className="row justify-content-end ">
                  <button type="button"
                    className="btn btn-primary cursor-pointer mr-3"
                    onClick={ this.updateList }
                    disabled={ !this.state.isWorksheetUpdated }>
                  Save
                  </button>
                </div>
              </If>
            </div>
          </div>
        </section>
      </Loader>
    );
  }
}