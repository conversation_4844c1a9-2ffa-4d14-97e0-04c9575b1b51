import moment from 'moment';
import React from 'react';
import ReactTooltip from 'react-tooltip';
import dateFormat from "./../reusable/Utilities/dateFormat.js";
import accounting from "accounting";
import PropTypes from 'prop-types';
import { CONSTANTS } from "./../reusable/Constants/constants";
import If from "./../reusable/If/If.jsx";
import SortIcon from "./../reusable/SortIcon/SortIcon";

export default class ClaimsList extends React.Component {

  static propTypes = {
    claimList: PropTypes.array.isRequired,
    displayCheckBox: PropTypes.bool,
    displayReassignment: PropTypes.bool,
    allChecked: PropTypes.bool,
    checkedClaims: PropTypes.array,
    onCheckBoxClick: PropTypes.func,
    renderReassignmentColumn: PropTypes.func,
    displayAgent: PropTypes.bool,
    renderAgentColumn: PropTypes.func,
    displayVIN: PropTypes.bool,
    sortBy: PropTypes.string,
    sortOrder: PropTypes.string,
    sortClaimList: PropTypes.func,
    enableSorting: PropTypes.bool,
  };

  static contextTypes = {
    router: PropTypes.object.isRequired
  };

  static defaultProps = {
    displayVIN: true,
    allChecked: false
  };

  constructor(props) {
    super(props);
  }

  handleSort = (toBeSorted) => {
    if (this.props.enableSorting) {
      const { sortBy, sortOrder, sortClaimList } = this.props;
      if(toBeSorted === sortBy && sortOrder === 'asc') {
        sortClaimList(sortBy, 'desc');
      } else if(toBeSorted === sortBy && sortOrder === 'desc') {
        sortClaimList(sortBy, 'asc');
      } else {
        sortClaimList(toBeSorted, 'asc');
      }
    }
  };

  renderTableHeader = () => {
    const {
      sortBy,
      sortOrder,
      enableSorting
    } = this.props;
    return (
      <tr>
        {this.props.displayCheckBox && <th><input type="checkbox" value="all"
          checked={ this.props.allChecked }
          onChange={ this.props.onCheckBoxClick.bind(null, "all") }/></th>}
        <th
          className={ enableSorting? "cursor-pointer": "" }
          id="label-contract-holder"
          onClick={ this.handleSort.bind(this, 'customer_name') }>
          Contract Holder&nbsp;
          <If condition={ enableSorting }>
            <SortIcon fieldName='customer_name'
              sortBy={ sortBy }
              sortOrder={ sortOrder }/>
          </If>
        </th>
        <th
          className={ enableSorting? "cursor-pointer": "" }
          id="label-contract-holder"
          onClick={ this.handleSort.bind(this, 'contract_number') }
        >Contract #&nbsp;
          <If condition={ enableSorting }>
            <SortIcon fieldName='contract_number'
              sortBy={ sortBy }
              sortOrder={ sortOrder }/>
          </If>
        </th>
        <If condition={ this.props.displayVIN }>
          <th
            className={ enableSorting? "cursor-pointer": "" }
            id="label-contract-holder"
            onClick={ this.handleSort.bind(this, 'vin') }
          >VIN&nbsp;
            <If condition={ enableSorting }>
              <SortIcon
                fieldName='vin'
                sortBy={ sortBy }
                sortOrder={ sortOrder }/>
            </If>
          </th>
        </If>
        <th>Status</th>
        <th>PreAuth</th>
        <th>RO</th>
        <th
          className={ enableSorting? "cursor-pointer": "" }
          id="label-contract-holder"
          onClick={ this.handleSort.bind(this, 'date_of_claim_received') }
        >Submitted&nbsp;
          <If condition={ enableSorting }>
            <SortIcon
              fieldName='date_of_claim_received'
              sortBy={ sortBy }
              sortOrder={ sortOrder }/>
          </If>
        </th>
        <th>Days In<br/>Process</th>
        <th
          className={ enableSorting? "cursor-pointer": "" }
          id="label-contract-holder"
          onClick={ this.handleSort.bind(this, 'facility') }
        >Facility&nbsp;
          <If condition={ enableSorting }>
            <SortIcon
              fieldName='facility'
              sortBy={ sortBy }
              sortOrder={ sortOrder }/>
          </If>
        </th>
        <th>$ Estimate</th>
        <If condition={ this.props.displayAgent }>
          <th>Agent</th>
        </If>
        <If condition={ this.props.displayReassignment }>
          <th>Reassignment</th>
        </If>
      </tr>
    );
  };

  renderTableBody = () => {
    return this.props.claimList.map(this.renderTableBodyRow);
  };

  renderCheckBox = (claimID, reassignStatus) => {
    if (this.props.displayCheckBox) {
      return (
        <td>
          <input type="checkbox" value={ claimID }
            checked={ this.props.checkedClaims.indexOf(claimID) !== -1 }
            onChange={ this.props.onCheckBoxClick.bind(null, claimID) }
            disabled={ CONSTANTS.AUTO_CLAIM_REASSIGNMENT_STATUS_MAP.pending === reassignStatus }/>
        </td>
      );
    }
  };

  renderTableBodyRow = (claimData, index) => {
    const longTextStyle = {
      "maxWidth": "130px",
      "overflow": "hidden",
      "textOverflow": "ellipsis",
      "whiteSpace": "nowrap"
    };
    return (
      <tr key={ index }>
        {this.renderCheckBox(claimData.id, claimData.reassignment_status.String)}
        <td style={ longTextStyle }>
          <a href="#!"
            data-tip
            data-for={ `auto_claims${index}` }
            onClick={ this.redirectToWorksheet.bind(this, claimData) }
            className="users">
            {claimData['customer_name']}
          </a>
          <ReactTooltip id={ `auto_claims${index}` } aria-haspopup='true'>
            <p className="text-center">{claimData['customer_name']}</p>
          </ReactTooltip>
        </td>
        <td>
          <a href="#!"
            onClick={ this.redirectToWorksheet.bind(this, claimData) }>
            {getContractCode(claimData['contract_number'])}
          </a>
        </td>
        {this.renderVINColumn(claimData['vin'])}
        <td>
          {claimData['status']}
        </td>
        <td>
          {claimData['pre_auth_number']}
        </td>
        <td>
          {claimData['ro']}
        </td>
        <td>
          {moment(claimData['date_of_claim_received']).format(dateFormat.displayDateFormat)}
        </td>
        <td>
          {moment.utc().diff(moment.utc(claimData['date_of_claim_received']), 'days')}
        </td>
        <td className="cursor-pointer long-text">
          <span data-tip
            data-for={ 'facility' + index }>
            {claimData['facility'].String}
          </span>
          <ReactTooltip id={ 'facility' + index } aria-haspopup='true'>
            <p className="text-center">{claimData['facility'].String}</p>
          </ReactTooltip>
        </td>
        <td>
          {accounting.formatMoney(claimData['estimate'], '$', 2)}
        </td>
        {this.renderAgentColumn(claimData.owner_id)}
        {this.renderReassignmentStatusColumn(claimData.reassignment_status.String, claimData.reassigned_owner_id.Int64, claimData.id)}
      </tr>
    );
  };

  renderVINColumn = (vin) => {
    if (this.props.displayVIN) {
      return <td>{vin}</td>;
    }
  };

  renderAgentColumn = (ownerID) => {
    if (this.props.displayAgent) {
      return (
        <td>
          {this.props.renderAgentColumn(ownerID)}
        </td>
      );
    }
  };

  renderReassignmentStatusColumn = (reassignmentStatus, reassignedOwnerID, claimID) => {
    if (this.props.displayReassignment) {
      return (
        <td>
          {this.props.renderReassignmentColumn(reassignmentStatus, reassignedOwnerID, claimID)}
        </td>
      );
    }
  };

  redirectToWorksheet = (claimData) => {
    if (claimData.contract_number && claimData.id) {
      const query = {
        id: claimData.id,
        contract_number: claimData.contract_number
      };
      if (claimData.chargeback) {
        query.chargeback = true;
        query.id = claimData.parent_claim_id;
      }
      const route = { pathname: "/automotive-claims", query };
      if (!this.context.router.isActive(route)) {
        this.context.router.push(route);
      }
    }
  };

  render = () => {
    return (
      <div className="table-responsive claim-list">
        <table className="table table-striped">
          <thead>
            {this.renderTableHeader()}
          </thead>
          <tbody>
            {this.renderTableBody()}
          </tbody>
        </table>
      </div>
    );
  }
}

/**
 * Function to ignore (specific) substring from the contract code. (by default it will ignore VSC substring)
 * @TODO - Need to remove this function once we migrate the automotive_claims table and place original_code with the table itself.
 * @param {*} contractCode 
 */
export const getContractCode = (contractCode, substringToIgnore = 'VSC') => (
  contractCode.endsWith(substringToIgnore) &&
    contractCode.slice(0, contractCode.length - substringToIgnore.length) ||
    contractCode
);