import React from 'react';
import PageHeader from '../pageHeader/PageHeader.jsx';
import <PERSON><PERSON> from 'react-s-alert';
import ClaimsList from './ClaimsList.jsx';
import { json as ajax } from './../../ajax.js';
import Pagination from './../../Pagination.jsx';
import Loader from 'react-loader-advanced';
import { URLS as apiUrls } from "./../reusable/Utilities/urls";
import { userHasRole } from "./../reusable/Utilities/userHasRole";
import { CONSTANTS } from "./../reusable/Constants/constants";
import DonutChart from "./../reusable/Charts/DonutChart/DonutChart.jsx";
import SearchBox from "./../reusable/SearchBox/Search.jsx";
import PropTypes from 'prop-types';

export default class ReassignClaims extends React.Component {

  static contextTypes = {
    router: PropTypes.object.isRequired
  };

  static propTypes = {
    user: PropTypes.shape({
      id: PropTypes.number.isRequired,
      first_name: PropTypes.string.isRequired,
      last_name: PropTypes.string.isRequired,
      email: PropTypes.string.isRequired,
      stores: PropTypes.arrayOf(PropTypes.string.isRequired),
      roles: PropTypes.shape({
        Map: PropTypes.object
      }).isRequired
    }),
    location: PropTypes.shape({
      query: PropTypes.shape({
        page: PropTypes.string,
        q: PropTypes.string,
        userId: PropTypes.string,
        status: PropTypes.string
      }).isRequired,
    }).isRequired,
  };

  constructor(props) {
    super(props);
    this.state = {
      claimList: [],
      selectedClaimList: [],
      numberOfClaims: undefined,
      pageLimit: 20,
      userList: [],
      showLoader: false,
      showChartForAgentsLoader: false,
      claimsCountByAgents: [],
      claimsCountByStatus: [],
      selectedChartLegendFilter: '',
      selectedChartStatusFilter: '',
      reassignedOwnerID: ''
    };
  }

  componentDidMount = () => {
    document.title = 'TCA Portal - Automotive Claims';
    this.loadClaimData();
    this.claimOwnersList();
  };

  UNSAFE_componentWillReceiveProps = (nextProps) => {
    if (this.currentPage(nextProps) !== this.currentPage(this.props) ||
      this.currentUserId(nextProps) !== this.currentUserId(this.props) ||
      this.currentStatus(nextProps) !== this.currentStatus(this.props) ||
      this.currentQ(nextProps) !== this.currentQ(this.props)) {
      this.loadClaimData(nextProps);
    }
  };

  loadClaimData = (props) => {
    if (!props) {
      props = this.props;
    }
    let url = `${apiUrls.automotiveClaimsReassign}?page=${window.encodeURIComponent(this.currentPage(props))}`;
    if (this.currentUserId(props)) {
      url += (`&user_id=${window.encodeURIComponent(this.currentUserId(props))}&reassigned_user_id=${window.encodeURIComponent(this.currentUserId(props))}`);
    }
    const currentQuery = this.currentQ(props);
    if (currentQuery) {
      url += (`&q=${window.encodeURIComponent(currentQuery).trim()}`);
    }
    const status = this.currentStatus(props);
    if (status) {
      url += (`&status=${window.encodeURIComponent(status)}`);
    }
    this.setState({ showLoader: true }, () => {
      ajax(url, {}, {}, (data, status) => {
        if (status === 200) {
          this.setState({
            claimList: data.automotive_claims,
            numberOfClaims: data.count,
            showLoader: false,
            showChartForAgentsLoader: true
          }, () => {
            if (userHasRole(this.props.user, CONSTANTS.USER_ROLES.autoClaimsManager)) {
              this.loadClaimsCountByAgents();
              this.loadClaimsCountByStatus();
            }
            let selectedClaimList = this.state.selectedClaimList.slice(0);
            if (!selectedClaimList[this.currentPage() - 1]) {
              selectedClaimList[this.currentPage() - 1] = [];
            }
            this.setState({ selectedClaimList });
          });
        } else {
          this.setState({ showLoader: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  loadClaimsCountByAgents = () => {
    ajax(apiUrls.automotiveClaimsCountAgents, {}, {}, (data, status) => {
      if (status === 200) {
        this.setState({ showChartForAgentsLoader: false, claimsCountByAgents: data.automotive_claims || [] });
      } else {
        this.setState({ showChartForAgentsLoader: false }, () => {
          Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
        });
      }
    });
  };

  loadClaimsCountByStatus = () => {
    ajax(apiUrls.automotiveClaimsCountStatus, {}, {}, (data, status) => {
      if (status === 200) {
        this.setState({ showChartForStatusLoader: false, claimsCountByStatus: data.automotive_claims });
      } else {
        this.setState({ showChartForStatusLoader: false }, () => {
          Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
        });
      }
    });
  };

  currentPage = (props) => {
    if (!props) {
      props = this.props;
    }
    return parseInt(props.location.query.page, 10) || 1;
  };

  currentUserId = (props) => {
    if (!props) {
      props = this.props;
    }
    if (userHasRole(this.props.user, CONSTANTS.USER_ROLES.autoClaimsManager)) {
      if (props.location.query.userId === '') {
        return '';
      } else {
        return props.location.query.userId;
      }
    } else {
      return props.user.id;
    }
  };

  currentStatus = (props) => {
    if (!props) {
      props = this.props;
    }
    return props.location.query.status;
  };

  claimOwnersList = () => {
    ajax(apiUrls.userList, {}, {}, (data, status) => {
      if (status === 200) {
        this.setState({ userList: data.users });
      } else {
        Alert.error("Click the browser's Refresh button to reload the user list. If the error continues, contact your system administrator.");
      }
    });
  };

  setPage = (page) => {
    const query = { page };
    const currentUserID = this.currentUserId();
    if (currentUserID) {
      query.userId = currentUserID;
    }
    const currentQuery = this.currentQ();
    if (currentQuery) {
      query.q = currentQuery;
    }
    this.redirectToPage("/automotive-claims-list/reassign", query);
  };

  redirectToPage = (pathname, query) => {
    const route = { pathname, query };
    if (!this.context.router.isActive(route)) {
      this.context.router.push(route);
    }
  };

  onCheckBoxClick = (claimID) => {
    let selectedClaimList = this.state.selectedClaimList.slice(0);
    if (claimID === "all") {
      selectedClaimList[this.currentPage() - 1] = this.state.claimList.length === selectedClaimList[this.currentPage() - 1].length ? [] : this.state.claimList.map((claimData) => {
        if (claimData.reassignment_status.String !== CONSTANTS.AUTO_CLAIM_REASSIGNMENT_STATUS_MAP.pending) {
          return claimData.id;
        }
      });
      this.setState({ selectedClaimList });
    } else {
      if (selectedClaimList[this.currentPage() - 1].indexOf(claimID) === -1) {
        selectedClaimList[this.currentPage() - 1].push(claimID);
        this.setState({ selectedClaimList });
      } else {
        selectedClaimList[this.currentPage() - 1].splice(selectedClaimList[this.currentPage() - 1].indexOf(claimID), 1);
        this.setState({ selectedClaimList });
      }
    }
  };

  onAcceptOrRejectClick = (action, claimID) => {
    let requestData = {};
    requestData.action = action;
    requestData.claim_id = claimID;
    this.setState({ showLoader: true }, () => {
      ajax(apiUrls.automotiveClaimsReassignAction, requestData, { method: 'PUT' }, (data, status) => {
        if (status === 200) {
          this.setState({ showLoader: false, reassignedOwnerID: '', selectedClaimList: [] }, () => {
            if (action === "accept") {
              Alert.success("Successfully accepted claim");
            } else if (action === "reject") {
              Alert.success("Successfully rejected claim");
            }
            this.loadClaimData();
          });
        } else if (status === 403 || status === 400) {
          this.setState({ showLoader: false }, () => {
            Alert.error(data.message);
          });
        } else {
          this.setState({ showLoader: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  renderReassignmentColumn = (reassignmentStatus, reassignedOwnerID, claimID) => {
    const style = { width: "-webkit-fill-available" };
    if (reassignmentStatus) {
      if (reassignmentStatus === CONSTANTS.AUTO_CLAIM_REASSIGNMENT_STATUS_MAP.new) {
        return <h5><span className="badge badge-primary" style={style}>New</span></h5>;
      } else if (reassignmentStatus === CONSTANTS.AUTO_CLAIM_REASSIGNMENT_STATUS_MAP.pending) {
        if (reassignedOwnerID === this.props.user.id) {
          return (
            <h5>
              <button className="btn btn-primary mr-1"
                onClick={this.onAcceptOrRejectClick.bind(null, "accept", claimID)}>Accept
              </button>
              <button className="btn btn-secondary" onClick={this.onAcceptOrRejectClick.bind(null, "reject", claimID)}>
                Reject
              </button>
            </h5>
          );
        } else {
          return <h5><span className="badge badge-primary" style={style}>Pending</span></h5>;
        }
      } else if (reassignmentStatus === CONSTANTS.AUTO_CLAIM_REASSIGNMENT_STATUS_MAP.rejected) {
        return <h5><span className="badge badge-warning" style={style}>Rejected</span></h5>;
      }
    }
  };

  renderClaimOwnerDropdown = () => {
    if (this.state.userList.length > 0) {
      return this.state.userList.map((user) => {
        if (userHasRole(user, CONSTANTS.USER_ROLES.autoClaims) && this.props.location.query.userId !== user.id.toString()) {
          return (<option value={user.id} key={user.id}>{`${user.first_name} ${user.last_name}`}</option>);
        }
      });
    }
  };

  renderClaimList = () => {
    if (this.state.claimList.length > 0) {
      let checkedClaims = this.state.selectedClaimList[this.currentPage() - 1];
      return (<ClaimsList claimList={this.state.claimList}
        displayCheckBox={true}
        displayVIN={false}
        displayAgent={true}
        displayReassignment={true}
        checkedClaims={checkedClaims ? checkedClaims : []}
        allChecked={checkedClaims && this.state.claimList && this.state.claimList.length === checkedClaims.length}
        onCheckBoxClick={this.onCheckBoxClick.bind(this)}
        renderReassignmentColumn={this.renderReassignmentColumn}
        renderAgentColumn={this.renderAgentName} />);
    } else {
      return (
        <div className="text-center">
          <p>No Results available for this search query</p>
        </div>
      );
    }
  };

  renderAgentName = (ownerID) => {
    if (ownerID) {
      let agent = this.state.userList.find((user) => {
        return ownerID === user.id;
      });
      if (agent) {
        return `${agent.first_name} ${agent.last_name}`;
      }
    }
  };

  renderPagination = () => {
    if (this.state.numberOfClaims > this.state.pageLimit) {
      return (
        <div className="pagination-container clearfix col-12 px-0">
          <div className="pull-left my-1">
            <p>Showing {this.currentPage() * this.state.pageLimit - this.state.pageLimit + 1}&nbsp;
              to {this.currentPage() * this.state.pageLimit > this.state.numberOfClaims ? this.state.numberOfClaims : this.currentPage() * this.state.pageLimit}&nbsp;
              of {this.state.numberOfClaims} items
            </p>
          </div>
          <div className="float-right">
            <Pagination page={this.currentPage()} count={this.state.numberOfClaims}
              limit={this.state.pageLimit} setPage={this.setPage} />
          </div>
        </div>
      );
    }
  };


  onAgentChartLegendClick = (agentName, owner_id) => {
    this.setState({ selectedChartLegendFilter: owner_id }, () => {
      const query = { page: 1, q: this.currentQ(), userId: owner_id, status: CONSTANTS.AUTO_CLAIM_STATUS_MAP.open };
      this.redirectToPage("/automotive-claims-list/reassign", query);
    });
  };

  onStatusChartLegendClick = (trimmedStatus) => {
    let status = '';
    if (trimmedStatus.length > 15) {
      for (let key of Object.keys(CONSTANTS.AUTO_CLAIM_STATUS_MAP)) {
        if (CONSTANTS.AUTO_CLAIM_STATUS_MAP[key].slice(0,15) === trimmedStatus.slice(0,15)) {
          status = CONSTANTS.AUTO_CLAIM_STATUS_MAP[key];
          break;
        }
      }
    } else {
      status = trimmedStatus;
    }

    this.setState({ selectedChartStatusFilter: trimmedStatus }, () => {
      const query = { page: 1, q: this.currentQ(), userId: this.currentUserId(), status };
      this.redirectToPage("/automotive-claims-list/reassign", query);
    });
  };

  onUpdateAssignment = () => {
    let requestData = {};
    requestData.owner_id = this.state.reassignedOwnerID;
    requestData.claims_list = ([].concat.apply([], this.state.selectedClaimList)).filter(id => Number.isInteger(id));
    if (requestData.claims_list.length === 0) {
      Alert.warning("No claim was selected");
      return;
    }
    let url = '';
    if (userHasRole(this.props.user, CONSTANTS.USER_ROLES.autoClaimsManager)) {
      url = apiUrls.automotiveClaimsReassignManager;
    } else {
      url = apiUrls.automotiveClaimsReassignAgent;
    }
    this.setState({ showLoader: true }, () => {
      ajax(url, requestData, { method: 'PUT' }, (data, status) => {
        if (status === 200) {
          this.setState({ showLoader: false, reassignedOwnerID: '', selectedClaimList: [] }, () => {
            Alert.success("Successfully reassigned claims");
            this.loadClaimData();
          });
        } else if (status === 403 || status === 400) {
          this.setState({ showLoader: false }, () => {
            Alert.error(data.message);
          });
        } else {
          this.setState({ showLoader: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  renderAgentsDonutChart = (spinnerMessage) => {
    if (!userHasRole(this.props.user, CONSTANTS.USER_ROLES.autoClaimsManager)) {
      return;
    }
    const total = this.state.claimsCountByAgents.reduce((sum, value) => sum + value.count, 0);
    const avgClaims = this.state.claimsCountByAgents.length && total ? Math.floor(total / this.state.claimsCountByAgents.length) : "";
    const chartConfig = {
      width: 350,
      height: 200,
      innerRadius: 35,
      outerRadius: 50,
      sectorStrokeWidth: "0px",
      centerTextTitle: "Average",
      centerTextCount: avgClaims.toString(),
      legendBulletSize: 16,
      legendStyle: { fontSize: "12px" },
      legendHeaderStyle: { fontSize: "14px" },
      legendColumnHeaders: ["Agent", "Claims"],
      legendColumnXPositions: [20, 150],
      selectedBar: this.state.selectedChartLegendFilter.toString()
    };
    return (
      <div className="col-4">
        <Loader show={this.state.showChartForAgentsLoader} message={spinnerMessage}>
          <div id="DonutChart_Status">
            <DonutChart config={chartConfig} data={this.state.claimsCountByAgents}
              onLegendClick={this.onAgentChartLegendClick} />
          </div>
        </Loader>
      </div>
    );
  };

  renderStatusDonutChart = (spinnerMessage) => {
    if (!userHasRole(this.props.user, CONSTANTS.USER_ROLES.autoClaimsManager)) {
      return;
    }
    const { claimsCountByStatus } = this.state;
    const openClaims = claimsCountByStatus && claimsCountByStatus.length ? claimsCountByStatus[0].count.toString() : '';
    const chartConfig = {
      width: 350,
      height: 200,
      innerRadius: 35,
      outerRadius: 50,
      sectorStrokeWidth: "0px",
      centerTextTitle: "Open",
      centerTextCount: openClaims,
      legendBulletSize: 10,
      legendStyle: { fontSize: "12px" },
      legendHeaderStyle: { fontSize: "14px" },
      legendColumnHeaders: ["Status", "Claims"],
      legendColumnXPositions: [15, 145],
      barColors: ['#41a6cb', '#2bac8b', '#8b9bac', '#efd448', '#b13b6b', '#a83224', '#e66b26'],
      selectedBar: this.state.selectedChartStatusFilter
    };
    // Add ellipsis to overflowing names
    let data = claimsCountByStatus.map(function (element) {
      if (element.name.length > 15) {
        element.name = element.name.slice(0, 15) + "...";
      }
      return element;
    });
        
    return (
      <div className="col-4">
        <Loader show={this.state.showChartForStatusLoader} message={spinnerMessage}>
          <div id="DonutChart_Status">
            <DonutChart config={chartConfig}
              data={data || []}
              onLegendClick={this.onStatusChartLegendClick} />
          </div>
        </Loader>
      </div>
    );
  };

  renderFiltersView = (spinnerMessage) => {
    return (
      <div className="row my-4">
        {this.renderAgentsDonutChart(spinnerMessage)}
        {this.renderStatusDonutChart(spinnerMessage)}
      </div>);
  };

  renderSearchView = (spinnerMessage) => {
    return (
      <div className="row">
        <div className="form-inline my-2 col-12">
          <div className="pull-left col-6 px-0">
            <SearchBox onSearch={this.onSearch}
              placeholder="Search Name, Contract #, facility code"
              value={this.currentQ()} />
          </div>
          <div className="col-4 form-inline">
            <div className="flex-row form-group my-2">
              <label className="pr-2">Assign to:</label>
              <select id='claim-owner'
                className="form-control"
                value={this.state.reassignedOwnerID}
                onChange={(e) => {
                  this.setState({ reassignedOwnerID: e.target.value });
                }}>
                <option value=''>Select Agent</option>
                {this.renderClaimOwnerDropdown()}
              </select>
              <div className="pl-2">
                <button className="btn btn-primary"
                  onClick={this.onUpdateAssignment}
                  disabled={!(this.state.reassignedOwnerID && this.state.selectedClaimList.length !== 0)}>
                  {userHasRole(this.props.user, CONSTANTS.USER_ROLES.autoClaimsManager) ? "Update Assignments" : "Request Reassignment"}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  currentQ = (props) => {
    if (!props) {
      props = this.props;
    }
    return props.location.query.q;
  };

  onSearch = (q) => {
    if (this.currentQ() !== q) {
      const query = { q, page: 1 };
      if (this.currentUserId()) {
        query.userId = this.currentUserId();
      }
      const route = { pathname: "/automotive-claims-list/reassign", query };
      if (!this.context.router.isActive(route)) {
        this.context.router.push(route);
      }
    }
  };

  render = () => {
    const spinnerMessage = <p className='text-center'><i className="fa fa-refresh fa-spin" /> Loading...</p>;
    return (
      <Loader show={this.state.showLoader} message={spinnerMessage}>
        <div className="row gap-dashboard">
          <div className="col-12">
            <PageHeader pageTitle="Reassign Service Claims" backButtonText="Back"
              onBackButtonClick={this.context.router.goBack} user={this.props.user} />
            {this.renderSearchView(spinnerMessage)}
            {this.renderFiltersView(spinnerMessage)}
            {this.renderClaimList()}
            {this.renderPagination()}
          </div>
        </div>
      </Loader>
    );
  }
}
