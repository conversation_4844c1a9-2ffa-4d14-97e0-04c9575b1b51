import React from "react";
import PageHeader from "../pageHeader/PageHeader.jsx";
import SearchBox from "../reusable/SearchBox/Search.jsx";
import Alert from "react-s-alert";
import ClaimsList from "./ClaimsList.jsx";
import { json as ajax } from "./../../ajax.js";
import Pagination from "./../../Pagination.jsx";
import Loader from "react-loader-advanced";
import moment from "moment";
import { URLS as apiUrls } from "./../reusable/Utilities/urls";
import { userHasRole } from "./../reusable/Utilities/userHasRole";
import { CONSTANTS } from "./../reusable/Constants/constants";
import DonutChart from "./../reusable/Charts/DonutChart/DonutChart.jsx";
import PropTypes from "prop-types";
import * as d3 from "d3";

const payTypeList = [
  { name: "All", value: "all" },
  { name: CONSTANTS.PAYMENT_TYPE_NAMES.CREDIT_CARD, value: CONSTANTS.PAYMENT_TYPE_CODES.CREDIT_CARD },
  { name: CONSTANTS.PAYMENT_TYPE_NAMES.CUSTOMER, value: CONSTANTS.PAYMENT_TYPE_CODES.CUSTOMER },
  { name: CONSTANTS.PAYMENT_TYPE_NAMES.STORE, value: CONSTANTS.PAYMENT_TYPE_CODES.STORE }
];

export default class AutomotiveClaimsDashboard extends React.Component {

  static contextTypes = {
    router: PropTypes.object.isRequired
  };

  static propTypes = {
    user: PropTypes.shape({
      id: PropTypes.number.isRequired,
      first_name: PropTypes.string.isRequired,
      last_name: PropTypes.string.isRequired,
      email: PropTypes.string.isRequired,
      stores: PropTypes.arrayOf(PropTypes.string.isRequired),
      roles: PropTypes.shape({
        Map: PropTypes.object
      }).isRequired
    }),
    location: PropTypes.shape({
      query: PropTypes.shape({
        page: PropTypes.string,
        q: PropTypes.string,
        userId: PropTypes.string,
        status: PropTypes.string,
        age: PropTypes.string,
        donutFilter: PropTypes.string
      }).isRequired,
    }).isRequired,
  };

  constructor(props) {
    super(props);
    this.state = {
      claimList: [],
      numberOfClaims: undefined,
      pageLimit: 20,
      userList: [],
      claimOwners: [],
      showLoader: false,
      showChartForStatusLoader: false,
      claimsCountByStatus: [],
      showChartForAgentsLoader: false,
      claimsCountByAgents: [],
      showChartForAgeLoader: false,
      claimsCountByAge: [],
      averageWaitingAge: ''
    };
  }

  componentDidMount() {
    document.title = 'TCA Portal - Automotive Claims';
    this.loadClaimData();
    this.claimOwnersList();
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (this.currentPage(nextProps) !== this.currentPage(this.props) ||
      this.currentQ(nextProps) !== this.currentQ(this.props) ||
      this.currentStatus(nextProps) !== this.currentStatus(this.props) ||
      this.currentUserId(nextProps) !== this.currentUserId(this.props) ||
      this.currentClaimAge(nextProps) !== this.currentClaimAge(this.props) ||
      this.currentPayType(nextProps) !== this.currentPayType(this.props) ||
      this.currentProductType(nextProps) !== this.currentProductType(this.props) ||
      this.currentSortOrder(nextProps) !== this.currentSortOrder(this.props) ||
      this.currentSortBy(nextProps) !== this.currentSortBy(this.props)) {
      this.setState({ q: nextProps.location.query.q || "" });
      this.loadClaimData(nextProps);
    }
  }

  /**
   * This function will fetch claim data from backend based on filters provided.
   *
   * filters:
   *  q :
   *      Type: {String}
   *      Description: Search query.
   *  page:
   *      Type : {Number}
   *      Description: Page number to provide pagination support.
   *  user_id :
   *      Type: {Number}
   *      Description: To fetch claims list based on claim owner.
   *  status:
   *      Type: {String}
   *  pay_type:
   *      Type: {String}
   *      Description: To fetch claims list based on pay type.
   * */

  loadClaimData = (props) => {
    if (!props) {
      props = this.props;
    }
    let url = `${apiUrls.automotiveClaims}?page=${window.encodeURIComponent(this.currentPage(props))}`;
    if (this.currentQ(props)) {
      url += (`&q=${window.encodeURIComponent(this.currentQ(props).trim())}`);
    }
    const status = this.currentStatus(props);
    if (status) {
      url += (`&status=${window.encodeURIComponent(this.currentStatus(props))}`);
    }
    if (this.currentUserId(props)) {
      url += (`&user_id=${window.encodeURIComponent(this.currentUserId(props))}`);
    }
    if (this.currentClaimAge(props)) {
      url += (`&age=${window.encodeURIComponent(this.currentClaimAge(props))}`);
    }
    const sortBy = this.currentSortBy(props);
    if (sortBy) {
      url += (`&sort_by=${window.encodeURIComponent(sortBy)}`);
    }
    const sortOrder = this.currentSortOrder(props);
    if (sortOrder) {
      url += (`&sort_order=${window.encodeURIComponent(sortOrder)}`);
    }
    const payType = this.currentPayType(props);
    if(payType){
      url += (`&pay_type=${window.encodeURIComponent(payType)}`);
    }

    const productType = this.currentProductType(props);
    if (productType) {
      url += (`&product=${window.encodeURIComponent(productType)}`);
    }

    this.setState({ showLoader: true }, () => {
      ajax(url, {}, {}, (data, status) => {
        if (status === 200) {
          this.setState({
            claimList: data.automotive_claims,
            numberOfClaims: data.count,
            showLoader: false,
            showChartForStatusLoader: true,
            showChartForAgentsLoader: true,
            showChartForAgeLoader: true
          }, () => {
            this.loadClaimsCountByStatus();
            this.loadClaimsCountByAgents();
            this.loadClaimsCountByAge();
          });
        } else {
          this.setState({ showLoader: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  loadClaimsCountByStatus = () => {
    ajax(apiUrls.automotiveClaimsCountStatus, {}, {}, (data, status) => {
      if (status === 200) {
        this.setState({ showChartForStatusLoader: false, claimsCountByStatus: data.automotive_claims });
      } else {
        this.setState({ showChartForStatusLoader: false }, () => {
          Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
        });
      }
    });
  };

  loadClaimsCountByAgents = () => {
    ajax(apiUrls.automotiveClaimsCountAgents, {}, {}, (data, status) => {
      if (status === 200) {
        this.setState({ showChartForAgentsLoader: false, claimsCountByAgents: data.automotive_claims });
      } else {
        this.setState({ showChartForAgentsLoader: false }, () => {
          Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
        });
      }
    });
  };

  loadClaimsCountByAge = () => {
    ajax(apiUrls.automotiveClaimsCountAge, {}, {}, (data, status) => {
      if (status === 200) {
        this.setState({
          showChartForAgeLoader: false,
          claimsCountByAge: data.automotive_claims,
          averageWaitingAge: data.average_waiting_period
        });
      } else {
        this.setState({ showChartForAgeLoader: false }, () => {
          Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
        });
      }
    });
  };

  currentPage = (props) => {
    if (!props) {
      props = this.props;
    }
    let page = props.location.query.page || 1;
    return parseInt(page, 10);
  };

  currentQ = (props) => {
    if (!props) {
      props = this.props;
    }
    return props.location.query.q || '';
  };

  currentUserId = (props) => {
    if (!props) {
      props = this.props;
    }
    if (props.location.query.userId === 'all') {
      return 'all';
    } else if (!props.location.query.userId) {
      if (this.state.claimOwners && this.state.claimOwners.length > 0) {
        const exists = this.state.claimOwners.find(obj => obj.id === props.user.id);
        if (!exists) {
          return 'all';
        }
      }
      return this.props.user.id;
    } else {
      return props.location.query.userId;
    }
  };

  currentPayType = (props) => {
    if(!props){
      props = this.props;
    }

    return props.location.query.payType || "all";
  };

  currentProductType = (props) => {
    if (!props) {
      props = this.props;
    }

    return props.location.query.product || "all";
  };

  currentStatus = (props) => {
    if (!props) {
      props = this.props;
    }
    return props.location.query.status || CONSTANTS.AUTO_CLAIM_STATUS_MAP.open;
  };

  claimOwnersList = () => {
    ajax(apiUrls.autoClaimOwners, {}, {}, (data, status) => {
      if (status === 200) {
        this.setState({ claimOwners: data.claim_owners });
      } else {
        Alert.error("Click the browser's Refresh button to reload the user list. If the error continues, contact your system administrator.");
      }
    });
  };

  currentClaimAge = (props) => {
    if (!props) {
      props = this.props;
    }
    return props.location.query.age;
  };

  setPage = (page) => {
    const query = { page };
    if (this.currentQ()) {
      query.q = this.currentQ();
    }
    if (this.currentStatus()) {
      query.status = this.currentStatus();
    }
    if (this.currentUserId()) {
      query.userId = this.currentUserId();
    }
    if (this.currentClaimAge()) {
      query.age = this.currentClaimAge();
    }
    let sortBy = this.currentSortBy();
    if (sortBy) {
      query.sortBy = sortBy;
    }
    const sortOrder = this.currentSortOrder();
    if (sortOrder) {
      query.sortOrder = sortOrder;
    }
    const payType = this.currentPayType();
    if (payType) {
      query.payType = payType;
    }
    const product = this.currentProductType();
    if (product) {
      query.product = product;
    }
    const route = { pathname: "/automotive-claims-list/", query };
    if (!this.context.router.isActive(route)) {
      this.context.router.push(route);
    }
  };

  onSearch = (q) => {
    if (this.currentQ() !== q) {
      const query = { q, page: 1 };
      query.status = 'all';
      query.userId = 'all';
      query.payType = 'all';
      query.product = 'all';
      query.age = '';
      let sortBy = this.currentSortBy();
      if (sortBy) {
        query.sortBy = sortBy;
      }
      const sortOrder = this.currentSortOrder();
      if (sortOrder) {
        query.sortOrder = sortOrder;
      }
      const route = { pathname: "/automotive-claims-list/", query };
      if (!this.context.router.isActive(route)) {
        this.context.router.push(route);
      }
    }
  };

  getClaimsByStatus = (event) => {
    const query = { status: event.target.value, page: 1 };
    if (this.currentQ()) {
      query.q = this.currentQ();
    }
    if (this.currentUserId()) {
      query.userId = this.currentUserId();
    }
    if (this.currentClaimAge()) {
      query.age = this.currentClaimAge();
    }
    let sortBy = this.currentSortBy();
    if (sortBy) {
      query.sortBy = sortBy;
    }
    const sortOrder = this.currentSortOrder();
    if (sortOrder) {
      query.sortOrder = sortOrder;
    }
    const payType = this.currentPayType();
    if (payType) {
      query.payType = payType;
    }
    const product = this.currentProductType();
    if (product) {
      query.product = product;
    }
    const route = { pathname: "/automotive-claims-list/", query };
    if (!this.context.router.isActive(route)) {
      this.context.router.push(route);
    }
  };

  handleClaimOwnerChange = (e) => {
    const query = { page: 1, userId: e.target.value };
    if (this.currentQ()) {
      query.q = this.currentQ();
    }
    if (this.currentStatus()) {
      query.status = this.currentStatus();
    }
    if (this.currentClaimAge()) {
      query.age = this.currentClaimAge();
    }
    let sortBy = this.currentSortBy();
    if (sortBy) {
      query.sortBy = sortBy;
    }
    const sortOrder = this.currentSortOrder();
    if (sortOrder) {
      query.sortOrder = sortOrder;
    }
    const payType = this.currentPayType();
    if (payType) {
      query.payType = payType;
    }
    const product = this.currentProductType();
    if (product) {
      query.product = product;
    }
    const route = { pathname: "/automotive-claims-list/", query };
    if (!this.context.router.isActive(route)) {
      this.context.router.push(route);
    }
  };

  handleClaimPayTypeChange = (e) => {
    const query = { page: 1, payType: e.target.value };
    if (this.currentQ()) {
      query.q = this.currentQ();
    }
    if (this.currentStatus()) {
      query.status = this.currentStatus();
    }
    if (this.currentUserId()) {
      query.userId = this.currentUserId();
    }
    if (this.currentClaimAge()) {
      query.age = this.currentClaimAge();
    }
    let sortBy = this.currentSortBy();
    if (sortBy) {
      query.sortBy = sortBy;
    }
    const sortOrder = this.currentSortOrder();
    if (sortOrder) {
      query.sortOrder = sortOrder;
    }
    const product = this.currentProductType();
    if (product) {
      query.product = product;
    }
    const route = { pathname: "/automotive-claims-list/", query };
    if (!this.context.router.isActive(route)) {
      this.context.router.push(route);
    }
  };

  handleProductCodeChange = (e) => {
    const query = { page: 1, product: e.target.value };
    if (this.currentQ()) {
      query.q = this.currentQ();
    }
    if (this.currentStatus()) {
      query.status = this.currentStatus();
    }
    if (this.currentUserId()) {
      query.userId = this.currentUserId();
    }
    if (this.currentClaimAge()) {
      query.age = this.currentClaimAge();
    }
    let sortBy = this.currentSortBy();
    if (sortBy) {
      query.sortBy = sortBy;
    }
    const sortOrder = this.currentSortOrder();
    if (sortOrder) {
      query.sortOrder = sortOrder;
    }
    const route = { pathname: "/automotive-claims-list/", query };
    if (!this.context.router.isActive(route)) {
      this.context.router.push(route);
    }
  }

  sortClaimList = (sortBy, sortOrder) => {
    let query = { sortBy, sortOrder };

    if (this.currentQ(this.props)) {
      query.q = this.currentQ(this.props);
    }

    if (this.currentPage()) {
      query.page = this.currentPage();
    }

    if (this.currentStatus()) {
      query.status = this.currentStatus();
    }
    if (this.currentClaimAge()) {
      query.age = this.currentClaimAge();
    }
    const payType = this.currentPayType();
    if (payType) {
      query.payType = payType;
    }
    const product = this.currentProductType();
    if (product) {
      query.product = product;
    }
    if (this.currentUserId()) {
      query.userId = this.currentUserId();
    }
    const route = { pathname: "/automotive-claims-list/", query };
    if (!this.context.router.isActive(route)) {
      this.context.router.push(route);
    }
  };

  currentSortOrder = (props) => {
    if (!props) {
      props = this.props;
    }
    return props.location.query.sortOrder || 'asc';
  };

  currentSortBy = (props) => {
    if (!props) {
      props = this.props;
    }
    return props.location.query.sortBy || 'date_of_claim_received';
  };

  renderClaimOwnerDropdown = () => {
    if (this.state.claimOwners.length > 0) {
      return this.state.claimOwners.map((user) => {
        return (<option value={ user.id } key={ user.id }>{`${user.first_name} ${user.last_name}`}</option>);
      });
    }
  };

  renderPayTypeDropdown = () => {
    return payTypeList.map((payType) => {
      return (<option key={ payType.value } value = { payType.value }>{payType.name}</option>);
    });
  };

  handleAgeFilterChange = (e) => {
    const query = { page: 1, age: e.target.value };
    if (this.currentQ()) {
      query.q = this.currentQ();
    }
    if (this.currentStatus()) {
      query.status = this.currentStatus();
    }
    if (this.currentUserId()) {
      query.userId = this.currentUserId();
    }
    const payType = this.currentPayType();
    if (payType) {
      query.payType = payType;
    }
    const product = this.currentProductType();
    if (product) {
      query.product = product;
    }
    const route = { pathname: "/automotive-claims-list/", query };
    if (!this.context.router.isActive(route)) {
      this.context.router.push(route);
    }
  };

  renderClaimListFilters = () => {
    return (
      <div>
        <div className="d-flex flex-row my-4">
          <div className="row pl-4">
            <div className="form-group col-4 pl-0">
              <label htmlFor="list-claim-status" className="pr-1">View: </label>
              <select id='list-claim-status'
                className="form-control"
                value={ this.currentStatus() }
                onChange={ this.getClaimsByStatus }>
                <option value='all'>All</option>
                <option value={ CONSTANTS.AUTO_CLAIM_STATUS_MAP.open }>
                  {CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.open}
                </option>
                <option value={ CONSTANTS.AUTO_CLAIM_STATUS_MAP.preAuthorization }>
                  {CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.preAuthorization}
                </option>
                <option value={ CONSTANTS.AUTO_CLAIM_STATUS_MAP.returned }>
                  {CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.returned}
                </option>
                <option value={ CONSTANTS.AUTO_CLAIM_STATUS_MAP.ccPaid }>
                  {CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.ccPaid}
                </option>
                <option value={ CONSTANTS.AUTO_CLAIM_STATUS_MAP.needRentalBill }>
                  {CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.needRentalBill}
                </option>
                <option value={ CONSTANTS.AUTO_CLAIM_STATUS_MAP.needSubletBill }>
                  {CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.needSubletBill}
                </option>
                <option value={ CONSTANTS.AUTO_CLAIM_STATUS_MAP.needSMToCall }>
                  {CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.needSMToCall}
                </option>
                <option value={ CONSTANTS.AUTO_CLAIM_STATUS_MAP.needClosedAccountingRO }>
                  {CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.needClosedAccountingRO}
                </option>
                <option value={ CONSTANTS.AUTO_CLAIM_STATUS_MAP.needProofOfDeductibleReimbursement }>
                  {CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.needProofOfDeductibleReimbursement}
                </option>
                <option value={ CONSTANTS.AUTO_CLAIM_STATUS_MAP.payable }>
                  {CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.payable}
                </option>
                <option value={ CONSTANTS.AUTO_CLAIM_STATUS_MAP.approved }>
                  {CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.approved}
                </option>
                <option value={CONSTANTS.AUTO_CLAIM_STATUS_MAP.authorizedCCClaim}>
                  {CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.authorizedCCClaim}
                </option>
                <option value={ CONSTANTS.AUTO_CLAIM_STATUS_MAP.dealerChargedBack }>
                  {CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.dealerChargedBack}
                </option>
                <option value={ CONSTANTS.AUTO_CLAIM_STATUS_MAP.invoiceSent }>
                  {CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.invoiceSent}
                </option>
                <option value={ CONSTANTS.AUTO_CLAIM_STATUS_MAP.denied }>
                  {CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.denied}
                </option>
                <option value={ CONSTANTS.AUTO_CLAIM_STATUS_MAP.deactivated }>
                  {CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.deactivated}
                </option>
                <option value={ CONSTANTS.AUTO_CLAIM_STATUS_MAP.waitingForCheck }>
                  {CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.waitingForCheck}
                </option>
                <option value={ CONSTANTS.AUTO_CLAIM_STATUS_MAP.waitingForReversed }>
                  {CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.waitingForReversed}
                </option>
                <option value={ CONSTANTS.CHARGEBACK_CLAIM_STATUS_MAP.waitingForChargeback }>
                  {CONSTANTS.CHARGEBACK_CLAIM_STATUS_DISPLAY_MAP.waitingForChargeback}
                </option>
                <option value={ CONSTANTS.AUTO_CLAIM_STATUS_MAP.adjusted }>
                  {CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.adjusted}
                </option>
                <option value={ CONSTANTS.AUTO_CLAIM_STATUS_MAP.checkWritten }>
                  {CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.checkWritten}
                </option>
                <option value={ CONSTANTS.CHARGEBACK_CLAIM_STATUS_MAP.chargeback }>
                  {CONSTANTS.CHARGEBACK_CLAIM_STATUS_DISPLAY_MAP.chargeback}
                </option>
                <option value={ CONSTANTS.CHARGEBACK_CLAIM_STATUS_MAP.chargebackCollected }>
                  {CONSTANTS.CHARGEBACK_CLAIM_STATUS_DISPLAY_MAP.chargebackCollected}
                </option>
                <option value={ CONSTANTS.AUTO_CLAIM_STATUS_MAP.reversed }>
                  {CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.reversed}
                </option>
                <option value={ CONSTANTS.AUTO_CLAIM_STATUS_MAP.waitingOnVendor }>
                  {CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.waitingOnVendor}
                </option>
              </select>
            </div>
            <div className="form-group px-3 col-2">
              <label htmlFor="list-claim-owner" className="pr-1">Assigned to: </label>
              <select id='list-claim-owner'
                className="form-control"
                value={ this.currentUserId() }
                onChange={ this.handleClaimOwnerChange }>
                <option value='all'>All</option>
                {this.renderClaimOwnerDropdown()}
              </select>
            </div>
            <div className="form-group col-2">
              <label htmlFor="list-claim-age" className="pr-1">Age: </label>
              <select id='list-claim-age' className="form-control" value={ this.currentClaimAge() }
                onChange={ this.handleAgeFilterChange }>
                <option value=''>All ages</option>
                <option value='LessThan4Hours'>{"< 4 hour"}</option>
                <option value='LessThan8Hours'>{"< 8 hour"}</option>
                <option value='LessThan1Day'>{"< Day"}</option>
                <option value='LessThan1Week'>{"< Week"}</option>
                <option value='LessThan1Month'>{"< Month"}</option>
                <option value='GreaterThan1Month'>{"> Month"}</option>
              </select>
            </div>
            <div className="form-group px-3 col-md-2">
              <label htmlFor="list-pay-type" className="pr-1">Pay Type:</label>
              <select id='list-pay-type'
                className="form-control"
                value={ this.currentPayType() }
                onChange={ this.handleClaimPayTypeChange }>
                {this.renderPayTypeDropdown()}
              </select>
            </div>
            <div className="form-group px-3 col-2">
              <label htmlFor="list-product-type" className="pr-1">Product Type:</label>
              <select id='list-product-type'
                className="form-control"
                value={this.currentProductType()}
                onChange={this.handleProductCodeChange}>
                <option value='all'>All</option>
                <option value={CONSTANTS.PRODUCT_CODE_NEW_MAP.service}>
                  {CONSTANTS.PRODUCT_CODE_NEW_MAP.service}
                </option>
                <option value={CONSTANTS.PRODUCT_CODE_NEW_MAP.maintenance}>
                  {CONSTANTS.PRODUCT_CODE_NEW_MAP.maintenance}
                </option>
                <option value={CONSTANTS.PRODUCT_CODE_NEW_MAP.appearanceProtection}>
                  {CONSTANTS.PRODUCT_CODE_NEW_MAP.appearanceProtection}
                </option>
                <option value={CONSTANTS.PRODUCT_CODE_NEW_MAP.paintlessDentRepair}>
                  {CONSTANTS.PRODUCT_CODE_NEW_MAP.paintlessDentRepair}
                </option>
                <option value={CONSTANTS.PRODUCT_CODE_NEW_MAP.keyReplacement}>
                  {CONSTANTS.PRODUCT_CODE_NEW_MAP.keyReplacement}
                </option>
                <option value={CONSTANTS.PRODUCT_CODE_NEW_MAP.drivePur}>
                  {CONSTANTS.PRODUCT_CODE_NEW_MAP.drivePur}
                </option>
                <option value={CONSTANTS.PRODUCT_CODE_NEW_MAP.tireWheel}>
                  {CONSTANTS.PRODUCT_CODE_NEW_MAP.tireWheel}
                </option>
                <option value={CONSTANTS.PRODUCT_CODE_NEW_MAP.century}>
                  {CONSTANTS.PRODUCT_CODE_NEW_MAP.century}
                </option>
                <option value={CONSTANTS.PRODUCT_CODE_NEW_MAP.toyotaTireWheel}>
                  {CONSTANTS.PRODUCT_CODE_NEW_MAP.toyotaTireWheel}
                </option>
                <option value={CONSTANTS.PRODUCT_CODE_NEW_MAP.key}>
                  {CONSTANTS.PRODUCT_CODE_NEW_MAP.key}
                </option>
              </select>
            </div>
            <div className="form-group">
              {this.renderReassignClaimsButton()}
            </div>
          </div>
        </div>
      </div>
    );
  };

  renderReassignClaimsButton = () => {
    let newClaimsCount = 0;
    this.state.claimList.forEach(function (claimData) {
      if (claimData.reassignment_status.String === CONSTANTS.AUTO_CLAIM_REASSIGNMENT_STATUS_MAP.new) {
        newClaimsCount++;
      }
    });
    let icon = '';
    if (newClaimsCount !== 0) {
      icon = <span className="badge badge-info rounded-circle ml-2">{newClaimsCount}</span>;
    }
    let query = {};
    if (!userHasRole(this.props.user, CONSTANTS.USER_ROLES.autoClaimsManager)) {
      query.userId = this.props.user.id;
    }
    return (
      <button className="btn btn-secondary"
        id="btn-reassign"
        onClick={ this.redirectToPage.bind(this, '/automotive-claims-list/reassign', query) }>
        Reassign Claims
        {icon}
      </button>);
  };

  redirectToPage = (pathname, query) => {
    if (!this.context.router.isActive({ pathname, query })) {
      this.context.router.push({ pathname, query });
    }
  };

  renderAgentName = (ownerID) => {
    if (ownerID) {
      let agent = this.state.claimOwners.find((user) => {
        return ownerID === user.id;
      });
      if (agent) {
        return `${agent.first_name} ${agent.last_name}`;
      }
    }
  };

  renderClaimList = () => {
    if (this.state.claimList.length > 0) {
      return (
        <ClaimsList
          claimList={ this.state.claimList }
          displayAgent={ true }
          renderAgentColumn={ this.renderAgentName }
          sortClaimList={ this.sortClaimList }
          sortOrder={ this.currentSortOrder(this.props) }
          sortBy={ this.currentSortBy(this.props) }
          enableSorting={ true }
        />);
    } else if (this.currentQ()) {
      return (
        <div className="text-center">
          <p>No Results available for this search query</p>
        </div>
      );
    }
  };

  renderPagination = () => {
    if (this.state.numberOfClaims > this.state.pageLimit) {
      return (
        <div className="pagination-container clearfix col-12 px-0">
          <div className="pull-left my-1">
            <p>Showing {this.currentPage() * this.state.pageLimit - this.state.pageLimit + 1}&nbsp;
              to {this.currentPage() * this.state.pageLimit > this.state.numberOfClaims ? this.state.numberOfClaims : this.currentPage() * this.state.pageLimit}&nbsp;
              of {this.state.numberOfClaims} items
            </p>
          </div>
          <div className="float-right">
            <Pagination page={ this.currentPage() } count={ this.state.numberOfClaims }
              limit={ this.state.pageLimit } setPage={ this.setPage }/>
          </div>
        </div>
      );
    }
  };

  onExport = () => {
    const props = this.props;
    let url = `${apiUrls.automotiveClaims}?csv=true`;
    if (this.currentQ(props)) {
      url += (`&q=${window.encodeURIComponent(this.currentQ(props).trim())}`);
    }
    if (this.currentStatus(props)) {
      url += (`&status=${window.encodeURIComponent(this.currentStatus(props))}`);
    }
    if (this.currentUserId(props)) {
      url += (`&user_id=${window.encodeURIComponent(this.currentUserId(props))}`);
    }
    if (this.currentPayType(props)) {
      url += (`&pay_type=${window.encodeURIComponent(this.currentPayType(props))}`);
    }
    if (this.currentProductType(props)) {
      url += (`&product=${window.encodeURIComponent(this.currentProductType(props))}`);
    }
    if (this.currentClaimAge(props)) {
      url += (`&age=${window.encodeURIComponent(this.currentClaimAge(props))}`);
    }

    this.setState({ showLoader: true }, () => {
      ajax(url, {}, {}, (data, status) => {
        if (status === 200) {
          this.setState({ showLoader: false });
          window.URL = window.URL || window.webkitURL;
          const csvData = new Blob([ data.automotive_claims ], { type: 'text/csv' });
          const csvURL = window.URL.createObjectURL(csvData);
          const fileName = `Worksheet_${moment(new Date()).format("YYYY-MM-DDTHH:mm:ss").toString()}.csv`;
          const downloadLink = document.createElement('a');
          downloadLink.setAttribute("href", csvURL);
          downloadLink.setAttribute("download", fileName);
          downloadLink.style.cssText = 'display:none';
          document.body.appendChild(downloadLink);
          downloadLink.click();
        } else {
          this.setState({ showLoader: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  renderExportButton = () => {
    let nClaims = this.state.numberOfClaims || 0;
    return (
      <button onClick={ this.onExport }
        className="btn btn-secondary mr-4"
        id="btn-export"
        disabled={ (nClaims == 0 ) }>
        <i className="fa fa-file-excel-o"/>
        &nbsp;Export
      </button>
    );
  };

  onStatusChartLegendClick = (trimmedStatus) => {
    let status = '';
    if (this.props.location.query.status && (this.props.location.query.status.slice(0,15) === trimmedStatus.slice(0,15))) {
      status = '';
    } else {
      if (trimmedStatus.length > 15) {
        for (let key of Object.keys(CONSTANTS.AUTO_CLAIM_STATUS_MAP)) {
          if (CONSTANTS.AUTO_CLAIM_STATUS_MAP[key].slice(0,15) === trimmedStatus.slice(0,15)) {
            status = CONSTANTS.AUTO_CLAIM_STATUS_MAP[key];
            break;
          }
        }
      } else {
        status = trimmedStatus;
      }
    }

    const query = { status, page: 1, userId: "All", age: "", donutFilter: status ? trimmedStatus : '' };
    const route = { pathname: "/automotive-claims-list", query };
    if (!this.context.router.isActive(route)) {
      this.context.router.push(route);
    }
  };

  onAgentChartLegendClick = (agentName, owner_id) => {
    let newOwnerId = owner_id;
    if (parseInt(this.props.location.query.userId, 10) === owner_id) {
      newOwnerId = 'All';
    }

    const query = {
      status: CONSTANTS.AUTO_CLAIM_STATUS_MAP.open,
      page: 1,
      userId: newOwnerId,
      age: "",
      donutFilter: newOwnerId
    };
    const route = { pathname: "/automotive-claims-list", query };
    if (!this.context.router.isActive(route)) {
      this.context.router.push(route);
    }
  };

  onAgeChartLegendClick = (age) => {
    let ageText = "";
    let donutFilter = age;
    switch (age) {
    case "< 4 hour":
      ageText = "LessThan4Hours";
      break;
    case "< 8 hour":
      ageText = "LessThan8Hours";
      break;
    case "< Day":
      ageText = "LessThan1Day";
      break;
    case "< Week":
      ageText = "LessThan1Week";
      break;
    case "< Month":
      ageText = "LessThan1Month";
      break;
    case "> Month":
      ageText = "GreaterThan1Month";
      break;
    }

    if (this.props.location.query.age === ageText) {
      ageText = '';
      donutFilter = '';
    }

    const query = {
      age: ageText,
      status: CONSTANTS.AUTO_CLAIM_STATUS_MAP.open,
      page: 1,
      userId: "All",
      donutFilter: donutFilter,
    };
    const route = { pathname: "/automotive-claims-list", query };
    if (!this.context.router.isActive(route)) {
      this.context.router.push(route);
    }
  };

  renderStatusDonutChart = (spinnerMessage) => {
    const { claimsCountByStatus } = this.state;
    let count = 0;
    let centerText = '';
    let filteredStatus = this.props.location.query.donutFilter || CONSTANTS.AUTO_CLAIM_STATUS_MAP.open;
    for (var i = 0; i < claimsCountByStatus.length; i++) {
      if (claimsCountByStatus[i].name.slice(0, 15) === filteredStatus.slice(0, 15)){
        count = claimsCountByStatus[i].count;
        centerText = claimsCountByStatus[i].name;
        if (centerText.length > 8) {
          centerText = centerText.slice(0, 8) + "...";
        }
        break;
      }
    }

    const chartConfig = {
      width: 350,
      height: 200,
      innerRadius: 35,
      outerRadius: 50,
      sectorStrokeWidth: "0px",
      centerTextTitle: centerText,
      centerTextCount: count,
      legendBulletSize: 10,
      legendStyle: { fontSize: "12px" },
      legendHeaderStyle: { fontSize: "14px" },
      legendColumnHeaders: [ "Status", "Claims" ],
      legendColumnXPositions: [ 15, 140 ],
      barColors: [ '#41a6cb', '#2bac8b', '#8b9bac', '#efd448', '#b13b6b', '#a83224', '#e66b26' ],
      selectedBar: this.props.location.query.donutFilter ? this.props.location.query.donutFilter.toString() : ''
    };
    // Add ellipsis to overflowing names
    let data = claimsCountByStatus.map(function (element) {
      if (element.name.length > 15) {
        element.name = element.name.slice(0, 15) + "...";
      }
      return element;
    });

    return (
      <Loader show={ this.state.showChartForStatusLoader } message={ spinnerMessage }>
        <div id="DonutChart_Status">
          <h6>Claim Status</h6>
          <DonutChart config={ chartConfig }
            data={ data || [] }
            onLegendClick={ this.onStatusChartLegendClick }/>
        </div>
      </Loader>
    );
  };

  renderAgentsDonutChart = (spinnerMessage) => {
    const claimsCountByAgents = this.state.claimsCountByAgents || [];
    const total = claimsCountByAgents.reduce((sum, value) => sum + value.count, 0);
    const avgClaims = claimsCountByAgents.length && total ? Math.floor(total / claimsCountByAgents.length) : "";
    const chartConfig = {
      width: 350,
      height: 200,
      innerRadius: 35,
      outerRadius: 50,
      sectorStrokeWidth: "0px",
      centerTextTitle: "Avg Per",
      centerTextCount: avgClaims.toString(),
      legendBulletSize: 10,
      legendStyle: { fontSize: "12px" },
      legendHeaderStyle: { fontSize: "14px" },
      legendColumnHeaders: [ "Agent", "Claims" ],
      legendColumnXPositions: [ 15, 150 ],
      barColors: d3.schemeCategory20,
      selectedBar: parseInt(this.props.location.query.donutFilter,10) ? this.props.location.query.donutFilter.toString() : ''
    };
    // Add ellipsis to overflowing names
    let data = claimsCountByAgents.map(function (element) {
      if (element.name.length > 18) {
        element.name = element.name.slice(0, 18) + "...";
      }
      return element;
    });
    return (
      <Loader show={ this.state.showChartForStatusLoader } message={ spinnerMessage }>
        <div id="DonutChart_Status">
          <h6>Agent Claim Distribution</h6>
          <DonutChart config={ chartConfig } data={ data } onLegendClick={ this.onAgentChartLegendClick }/>
        </div>
      </Loader>
    );
  };

  renderAgeDonutChart = (spinnerMessage) => {
    const chartConfig = {
      width: 350,
      height: 200,
      innerRadius: 35,
      outerRadius: 50,
      sectorStrokeWidth: "0px",
      centerTextTitle: "Week Avg",
      centerTextCount: this.state.averageWaitingAge,
      legendBulletSize: 10,
      legendStyle: { fontSize: "12px" },
      legendHeaderStyle: { fontSize: "14px" },
      legendColumnHeaders: [ "Age", "Claims" ],
      legendColumnXPositions: [ 15, 100 ],
      barColors: [ '#7ed321', '#4a90e2', '#f8e71c', '#f5a623', '#d0021b' ],
      selectedBar: this.props.location.query.donutFilter ? this.props.location.query.donutFilter.toString() : ''
    };
    return (
      <Loader show={ this.state.showChartForStatusLoader } message={ spinnerMessage }>
        <div id="DonutChart_Status">
          <h6>Claim Age</h6>
          <DonutChart config={ chartConfig } data={ this.state.claimsCountByAge || [] }
            onLegendClick={ this.onAgeChartLegendClick }/>
        </div>
      </Loader>
    );
  };

  renderGraphView = (spinnerMessage) => {
    return (
      <div className="row my-4">
        <div className="col-4">
          {this.renderStatusDonutChart(spinnerMessage)}
        </div>
        <div className="col-4">
          {this.renderAgentsDonutChart(spinnerMessage)}
        </div>
        <div className="col-4">
          {this.renderAgeDonutChart(spinnerMessage)}
        </div>
      </div>
    );
  };

  render() {
    const spinnerMessage = <p className='text-center'><i className="fa fa-refresh fa-spin"/> Loading...</p>;
    return (
      <Loader show={ this.state.showLoader } message={ spinnerMessage }>
        <div className="row gap-dashboard">
          <div className="col-12">
            <PageHeader pageTitle="Automotive Claims" user={this.props.user}/>
            <div className="row">
              <div className="form-inline my-2 col-12">
                <div className="pull-left col-6 px-0">
                  <SearchBox onSearch={ this.onSearch }
                    placeholder="Search Name, Contract #, VIN (10), RO, Pre-Auth#"
                    value={ this.currentQ() }/>
                </div>
                <div className="col-6 px-0 float-right">
                  <div className="form-group float-right">
                    {this.renderExportButton()}
                    <a href='/contracts' className="btn btn-primary" id="btn-search-contracts">
                      <i className="fa fa-search pr-1"/>
                      Search Contracts
                    </a>
                  </div>
                </div>
              </div>
            </div>
            {this.renderGraphView(spinnerMessage)}
            {this.renderClaimListFilters()}
            {this.renderClaimList()}
            {this.renderPagination()}
          </div>
        </div>
      </Loader>
    );
  }
}
