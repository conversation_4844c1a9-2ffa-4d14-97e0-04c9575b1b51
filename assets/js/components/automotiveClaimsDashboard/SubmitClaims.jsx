import React from 'react';
import ReactTooltip from 'react-tooltip';
import <PERSON>Header from '../pageHeader/PageHeader.jsx';
import Alert from 'react-s-alert';
import { json as ajax } from './../../ajax.js';
import Loader from 'react-loader-advanced';
import { URLS as apiUrls } from "./../reusable/Utilities/urls";
import { CONSTANTS } from "./../reusable/Constants/constants";
import accounting from "accounting";
import PropTypes from 'prop-types';
import ConfirmationModal from "../reusable/ConfirmationModal/ConfirmationModal.jsx";

export default class SubmitClaims extends React.Component {

  static contextTypes = {
    router: PropTypes.object.isRequired
  };

  static propTypes = {
    user: PropTypes.shape({
      id: PropTypes.number.isRequired,
      first_name: PropTypes.string.isRequired,
      last_name: PropTypes.string.isRequired,
      email: PropTypes.string.isRequired,
      stores: PropTypes.arrayOf(PropTypes.string.isRequired),
      roles: PropTypes.shape({
        Map: PropTypes.object
      }).isRequired
    }),
    location: PropTypes.shape({
      query: PropTypes.shape({
        product: PropTypes.string,
      }).isRequired,
    }).isRequired,
  };

  constructor(props) {
    super(props);
    this.state = {
      claimList: [],
      selectedClaimList: [],
      selectedProductCode: '',
      totalClaimAmount: 0,
      numberOfClaims: undefined,
      showLoader: false,
      submitClaimsConfirmationModal: false
    };
  }

  componentDidMount() {
    document.title = 'TCA Portal - Automotive Claims';
    this.loadClaimData();
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (this.currentProductCode(nextProps) !== this.currentProductCode(this.props)) {
      this.loadClaimData(nextProps);
    }
  }

  currentProductCode = (props) => {
    if (!props) {
      props = this.props;
    }
    return props.location.query.product;
  };

  loadClaimData = () => {
    let url = `${apiUrls.automotiveClaims}?status=${window.encodeURIComponent(CONSTANTS.AUTO_CLAIM_STATUS_MAP.payable)}`;
    if (this.state.selectedProductCode) {
      url += (`&product=${window.encodeURIComponent(this.state.selectedProductCode)}`);
    }
    this.setState({ showLoader: true }, () => {
      ajax(url, {}, {}, (data, status) => {
        if (status === 200) {
          this.setState({
            claimList: data.automotive_claims,
            numberOfClaims: data.count,
            showLoader: false
          });
        } else {
          this.setState({ showLoader: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  onCheckBoxClick = (claimID, claimAmount) => {
    if (claimID === "all") {
      this.setState({
        selectedClaimList: this.state.claimList.length === this.state.selectedClaimList.length ? [] : this.state.claimList.map((claimData) => {
          return claimData.id;
        }),
        totalClaimAmount: this.state.claimList.reduce((prev, curr) => {
          if (prev.estimate) {
            prev = prev.estimate;
          }
          return prev + curr.estimate;
        })
      });
    } else {
      let selectedClaims = this.state.selectedClaimList.slice(0);
      if (selectedClaims.indexOf(claimID) === -1) {
        selectedClaims.push(claimID);
        this.setState({
          selectedClaimList: selectedClaims,
          totalClaimAmount: this.state.totalClaimAmount + parseFloat(claimAmount)
        });
      } else {
        selectedClaims.splice(selectedClaims.indexOf(claimID), 1);
        this.setState({
          selectedClaimList: selectedClaims,
          totalClaimAmount: this.state.totalClaimAmount - parseFloat(claimAmount)
        });
      }
    }
  };

  renderProductDropDown = () => {
    let productCodeMap = [
      {
        name: "Automotive Service",
        code: CONSTANTS.PRODUCT_CODE_NEW_MAP.service
      }, {
        name: "Automotive Key",
        code: CONSTANTS.PRODUCT_CODE_NEW_MAP.key
      }, {
        name: "Automotive Century",
        code: CONSTANTS.PRODUCT_CODE_NEW_MAP.century
      }];
    return productCodeMap.map((product, index) => {
      return (<option value={product.code} key={index}>{product.name}</option>);
    });
  };

  renderTableHeader = () => {
    return (
      <tr>
        <th><input type="checkbox" value="all"
          checked={this.state.allChecked}
          onChange={ this.onCheckBoxClick.bind(null, "all") }/>
        </th>
        <th>VEN #</th>
        <th>Facility</th>
        <th>Contract #</th>
        <th>RO #</th>
        <th>$ Amount</th>
      </tr>
    );
  };

  renderTableBody = () => {
    return this.state.claimList.map(this.renderTableBodyRow);
  };

  renderTableBodyRow = (claimData, index) => {
    return (
      <tr key={index}>
        { this.renderCheckBox(claimData.id, claimData.estimate) }
        <td>
          {claimData['vendor_id']}
        </td>
        <td className="cursor-pointer long-text">
          <span data-tip data-for={'facility' + index}>
            {claimData['facility'].String}
          </span>
          <ReactTooltip id={'facility' + index} aria-haspopup='true'>
            <p className="text-center">{claimData['facility'].String}</p>
          </ReactTooltip>
        </td>
        <td>
          <a href="#!" onClick={ this.redirectToWorksheet.bind(this, claimData) }>
            {claimData['contract_number']}
          </a>
        </td>
        <td>
          <a href="#!" onClick={ this.redirectToWorksheet.bind(this, claimData) }>
            {claimData['ro']}
          </a>
        </td>
        <td>
          { accounting.formatMoney(claimData['estimate'], '$', 2) }
        </td>
      </tr>
    );
  };

  renderCheckBox = (claimID, claimAmount) => {
    return (
      <td>
        <input type="checkbox" value={claimID}
          checked={this.state.allChecked || this.state.selectedClaimList.indexOf(claimID) !== -1}
          onChange={this.onCheckBoxClick.bind(null, claimID, claimAmount)}/>
      </td>
    );
  };

  redirectToWorksheet = (claimData) => {
    if (claimData.contract_number && claimData.id) {
      const query = {
        id: claimData.id,
        contract_number: claimData.contract_number
      };
      const route = { pathname: "/automotive-claims", query };
      if (!this.context.router.isActive(route)) {
        this.context.router.push(route);
      }
    }
  };

  renderClaimList = () => {
    if (this.state.claimList.length > 0) {
      return (
        <div className="table-responsive claim-list">
          <table className="table table-striped">
            <thead>{this.renderTableHeader()}</thead>
            <tbody>{this.renderTableBody()}</tbody>
          </table>
        </div>);
    } else {
      return (
        <div className="text-center">
          <p>No Results available for this search query</p>
        </div>
      );
    }
  };

  onSubmitClaims = () => {
    // TODO Submit when backend API is available
    // let requestData = {};
    // requestData.owner_id = this.state.selectedProductCode;
    // requestData.claims_list = this.state.selectedClaimList;
    // let url = apiUrls.automotiveClaimsBatchSubmit;
    // this.setState({ showLoader: true }, () => {
    //   ajax(url, requestData, { method: 'PUT' }, (data, status) => {
    //     if (status === 200) {
    //       this.setState({ showLoader: false, selectedProductCode: '', selectedClaimList: [] }, () => {
    //         Alert.success("Successfully submitted claims");
    //         this.loadClaimData();
    //       });
    //     } else if (status === 403 || status === 400) {
    //       this.setState({ showLoader: false }, () => {
    //         Alert.error(data.message);
    //       });
    //     } else {
    //       this.setState({ showLoader: false }, () => {
    //         Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
    //       });
    //     }
    //   });
    // });
  };

  redirectToPage = (pathname, query) => {
    const route = { pathname, query };
    if (!this.context.router.isActive(route)) {
      this.context.router.push(route);
    }
  };

  renderFiltersView = () => {
    return (
      <div className="row my-4">
        <div className="col-8 form-inline">
          <div className="flex-row form-group">
            <label className="pr-2">Product:</label>
            <select id='product'
              className="form-control"
              value={ this.state.selectedProductCode }
              onChange={(e) => {
                this.setState({ selectedProductCode: e.target.value }, () => {
                  const query = { product: this.state.selectedProductCode };
                  this.redirectToPage("/automotive-claims-list/submit", query);
                });
              }}>
              <option value=''>Select Product</option>
              {this.renderProductDropDown()}
            </select>
            <div className="pl-2">
              <button className="btn btn-primary"
                onClick={() => {
                  this.setState({ submitClaimsConfirmationModal: true });
                }}
                disabled={!(this.state.selectedProductCode && this.state.selectedClaimList.length !== 0)}>
                Submit Claims
              </button>
            </div>
          </div>
        </div>
      </div>);
  };

  renderFooter = () => {
    const borderStyle = { borderTop: "1px solid #979797" };
    return (
      <div className="row" style={borderStyle}>
        <div className="form-inline my-2 col-12">
          <div className="text-left col-10 px-0">
            <p>{`${this.state.selectedClaimList.length} of ${this.state.numberOfClaims} items`}</p>
          </div>
          <div className="col-2 px-0 text-center">
            <strong>{accounting.formatMoney(this.state.totalClaimAmount, '$', 2)}</strong>
          </div>
        </div>
      </div>
    );
  };

  render() {
    const spinnerMessage = <p className='text-center'><i className="fa fa-refresh fa-spin"/> Loading...</p>;
    return (
      <Loader show={this.state.showLoader} message={spinnerMessage}>
        <div className="row gap-dashboard">
          <div className="col-12">
            <PageHeader pageTitle="Submit Authorized Automotive Claims" backButtonText="Back"
              onBackButtonClick={this.context.router.goBack}
              user={this.props.user}/>
            {this.renderFiltersView(spinnerMessage)}
            {this.renderClaimList()}
            {this.renderFooter()}
          </div>
        </div>
        <ConfirmationModal confirmButtonText="Submit Batch"
          declineButtonText="Cancel"
          displayConfirmationModal={ this.state.submitClaimsConfirmationModal }
          displayMessage={`Are you sure you want to submit ${this.state.selectedClaimList.length} Claims for ${accounting.formatMoney(this.state.totalClaimAmount, '$', 2)}`}
          onConfirm={this.onSubmitClaims}
          onDecline={() => {
            this.setState({ submitClaimsConfirmationModal: false });
          }}/>
      </Loader>
    );
  }
}
