import React from 'react';
import ReactDOM from 'react-dom';
import PropTypes from 'prop-types';
import { DropTarget, DragSource } from 'react-dnd';
import moment from 'moment';
import dateFormat from '../reusable/Utilities/dateFormat';
import If from '../reusable/If/If';

const actionOptions = [
  { "name": "Edit", "value": "edit" },
  { "name": "Delete", "value": "delete" }
];

const ItemType = "email-list-item";

const source = {
  beginDrag: (props)=> {
    return {
      id: props.id,
      index: props.index
    };
  }
};

const target = {
  hover: (props, monitor, component)=> {
    const dragIndex = monitor.getItem().index;
    const hoverIndex = props.index;

    // Don't replace items with themselves
    if (dragIndex === hoverIndex) {
      return;
    }

    /* eslint "react/no-find-dom-node": "off" */
    // Cannot figure out how to handle this with refs due to the component being owned by react-dnd.
    // So, using `ReactDOM.findDOMNode` for now.
    // Determine rectangle on screen
    const hoverBoundingRect = ReactDOM.findDOMNode(component).getBoundingClientRect();

    // Get vertical middle
    const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;

    // Determine mouse position
    const clientOffset = monitor.getClientOffset();

    // Get pixels to the top
    const hoverClientY = clientOffset.y - hoverBoundingRect.top;

    // Only perform the move when the mouse has crossed half of the items height
    // When dragging downwards, only move when the cursor is below 50%
    // When dragging upwards, only move when the cursor is above 50%

    // Dragging downwards
    if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) {
      return;
    }

    // Dragging upwards
    if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) {
      return;
    }

    // Time to actually perform the action
    props.move(dragIndex, hoverIndex);

    // Note: we're mutating the monitor item here!
    // Generally it's better to avoid mutations,
    // but it's good here for the sake of performance
    // to avoid expensive index searches.
    monitor.getItem().index = hoverIndex;
  }
};

const collectSource = (connect, monitor)=> {
  return {
    connectDragSource: connect.dragSource(),
    connectDragPreview: connect.dragPreview(),
    isDragging: monitor.isDragging(),
  };
};

const collectTarget = (connect)=> {
  return {
    connectDropTarget: connect.dropTarget()
  };
};

export default DropTarget(ItemType, target, collectTarget)(DragSource(ItemType, source, collectSource)(class EmailListItem extends React.Component {

  static propTypes = {
    template: PropTypes.object.isRequired,
    index: PropTypes.number.isRequired,
    move: PropTypes.func,
    refreshList: PropTypes.func,
    connectDragSource: PropTypes.func.isRequired,
    connectDragPreview: PropTypes.func.isRequired,
    connectDropTarget: PropTypes.func.isRequired,
    isDragging: PropTypes.bool.isRequired,
    onEditTemplate: PropTypes.func,
    onActionChange: PropTypes.func,
    showOrder: PropTypes.bool
  };

  constructor(props){
    super(props);

    this.state = {
      indexInput: this.props.index + 1
    };
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    this.setState({ indexInput: nextProps.index + 1 });
  }

  moveToTop(e) {
    this.props.move(this.props.index, 0);
  }

  onIndexChange(e) {
    this.setState({ indexInput: e.target.value });
  }

  onIndexBlur(e) {
    const value = parseInt(this.state.indexInput);
    this.props.move(this.props.index, value - 1);
  }

  renderActionOption() {
    return actionOptions.map(this.getOption);
  }

  getOption(state, index) {
    return (
      <option key={ index } value={ state.value }>{state.name}</option>
    );
  }

  renderMoveToTop() {
    if (this.props.index > 0) {
      return <i onClick={ this.moveToTop.bind(this) } className="fa fa-chevron-up" style={ { cursor: "pointer" } } />;
    }
  }
    
  render(){
    const { template } = this.props;
    return this.props.connectDragPreview(this.props.connectDropTarget(
      <tr style={ { opacity: (this.props.isDragging ? "0.2" : "1.0") } }>
        <If condition={ this.props.showOrder }>
          <td className="form-inline">
            { this.props.connectDragSource(<i className="fa fa-reorder" style={ { cursor: "move" } } />) }
                &nbsp;
            <input type="number" value={ this.state.indexInput } onChange={ this.onIndexChange.bind(this) } onBlur={ this.onIndexBlur.bind(this) } className="form-control form-control-sm" style={ { width: "3rem" } } />
                &nbsp;
            { this.renderMoveToTop() }
          </td>
        </If>
        <td>
          <a href="#!" onClick={ this.props.onEditTemplate }>
            {template.name}
          </a>
        </td>
        <td>
          {moment(template.updated_at).format(dateFormat.displayDateFormat)}
        </td>
        <td>
          {template.template_type}
        </td>
        <td>
          {template.language}
        </td>
        <td>
          <div className="row">
            <div>
              <select className="form-control form-control-sm"
                onChange={ this.props.onActionChange }>
                <option value=''>----Select Action----</option>
                {this.renderActionOption()}
              </select>
            </div>
          </div>
        </td>
      </tr>
    ));
  }
}));