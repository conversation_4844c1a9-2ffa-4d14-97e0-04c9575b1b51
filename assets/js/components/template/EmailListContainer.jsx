import React from 'react';
import PropTypes from 'prop-types';
import { DragDropContext } from 'react-dnd';
import HTML5Backend from 'react-dnd-html5-backend';

export default DragDropContext(HTML5Backend)(class extends React.Component {
    static displayName = "ContractFormContainer";

    static propTypes = {
      children: PropTypes.arrayOf(PropTypes.element.isRequired).isRequired,
    };

    render() {
      return (
        <tbody>
          { this.props.children }
        </tbody>
      );
    }
});