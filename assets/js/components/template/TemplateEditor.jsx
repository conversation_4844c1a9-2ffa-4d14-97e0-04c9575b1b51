import React from 'react';
import Modal from '../../Modal.jsx';
import SelectBox from '../reusable/SelectBox/SelectBox.jsx';
import Editor from "../reusable/WYSIWYGEditor/Editor.jsx";
import PropTypes from 'prop-types';

export default class TemplateEditor extends React.Component {

  static propTypes = {
    templateTypeOptions: PropTypes.array.isRequired,
    templateData: PropTypes.shape({
      id: PropTypes.number,
      name: PropTypes.string,
      template_text: PropTypes.string,
      template_type: PropTypes.string,
      language: PropTypes.string,
      updated_at: PropTypes.string,
      updated_by_user_id: PropTypes.number,
      updated_by_user_name: PropTypes.string
    }),
    templateTypeMap: PropTypes.object.isRequired,
    languageMap: PropTypes.object.isRequired,
    languageOptions: PropTypes.array.isRequired,
    displayCreateTemplateFormModal: PropTypes.bool.isRequired,
    closeTemplateModalDOM: PropTypes.func,
    createUpdateTemplate: PropTypes.func.isRequired,
    type: PropTypes.oneOf(['create', 'edit', ''])
  };

  constructor(props) {
    super(props);
    this.state = {
      templateData: this.props.templateData || {
        id: 0,
        name: '',
        template_text: '',
        template_type: this.props.templateTypeMap.Gap,
        language: this.props.languageMap.English,
        updated_at: '',
        updated_by_user_id: '',
        updated_by_user_name: ''
      }
    };
    this.createUpdateTemplate = this.createUpdateTemplate.bind(this);
    this.updateState = this.updateState.bind(this);
    this.isCreateDisabled = this.isCreateDisabled.bind(this);
    this.getTitle = this.getTitle.bind(this);
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    this.setState({
      templateData: nextProps.templateData
    });
  }

  createUpdateTemplate() {
    const templateObject = {
      "name": this.state.templateData.name.trim(),
      "template_text": btoa(unescape(encodeURIComponent(this.state.templateData.template_text))),
      "template_type": this.state.templateData.template_type,
      "language": this.state.templateData.language
    };
    this.props.createUpdateTemplate(templateObject);
  }

  updateState(state, value) {
    const templateData = this.state.templateData;
    templateData[state] = value;
    this.setState({ templateData });
  }

  isCreateDisabled() {
    return !(this.state.templateData.name && this.state.templateData.template_text && this.state.templateData.template_type && this.state.templateData.language);
  }

  getTitle() {
    if (this.props.type === 'edit') {
      return 'Update Template';
    } else {
      return "Create New Template";
    }
  }

  render() {
    return (
      <section>
        <Modal visible={this.props.displayCreateTemplateFormModal} close={this.props.closeTemplateModalDOM}
          size="large">
          <div className="row justify-content-center">
            <div className="col-11">
              <h3>{ this.getTitle() }</h3>
              <div className="container pt-3">
                <div className="row">
                  <div className="col-5">
                    <div className="form-group row justify-content-end">
                      <label className="col-form-label col-form-label-sm col-2 px-0">
                        Name:
                      </label>
                      <div className="col-10">
                        <input type="text"
                          className="form-control"
                          disabled={ this.props.type === 'edit' }
                          value={ this.state.templateData.name }
                          onChange={ (event)=>{this.updateState('name', event.target.value);} }/>
                      </div>
                    </div>
                  </div>
                  <div className="col-3">
                    <div className="form-group row justify-content-end">
                      <label className="col-form-label col-form-label-sm col-4 text-right">
                        Type:
                      </label>
                      <div className="col-8">
                        <SelectBox value={this.state.templateData.template_type}
                          disabled={ this.props.type === 'edit' }
                          onChange={(templateType)=>{this.updateState('template_type', templateType);}}
                          optionsList={this.props.templateTypeOptions}/>
                      </div>
                    </div>
                  </div>
                  <div className="col-4">
                    <div className="form-group row justify-content-end">
                      <label className="col-form-label col-form-label-sm col-4 text-right">
                        Language:
                      </label>
                      <div className="col-8">
                        <SelectBox value={this.state.templateData.language}
                          disabled={ this.props.type === 'edit' }
                          onChange={(language)=>{this.updateState('language', language);}}
                          optionsList={this.props.languageOptions}/>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div>
                <div>
                  <Editor content={this.state.templateData.template_text}
                    title={ this.state.templateData.name }
                    onChange={(event)=>{this.updateState('template_text', event.target.value);}}/>
                </div>
              </div>
              <div className="row justify-content-end py-3 pr-3">
                <button className="btn btn-secondary mr-3 cursor-pointer" onClick={this.props.closeTemplateModalDOM}>Cancel
                </button>
                <button type="submit"
                  className="btn btn-primary cursor-pointer"
                  onClick={ this.createUpdateTemplate }
                  disabled={ this.isCreateDisabled() }>{ this.props.type === 'create' ? 'Create' : 'Update'}</button>
              </div>
            </div>
          </div>
        </Modal>
      </section>
    );
  }
}