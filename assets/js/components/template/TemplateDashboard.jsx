import React from 'react';
import PageHeader from '../pageHeader/PageHeader.jsx';
import SelectBox from '../reusable/SelectBox/SelectBox.jsx';
import Loader from 'react-loader-advanced';
import Alert from 'react-s-alert';
import ListView from './ListView.jsx';
import TemplateEditor from './TemplateEditor.jsx';
import ConfirmationModal from "../reusable/ConfirmationModal/ConfirmationModal.jsx";
import { json as ajax } from './../../ajax.js';
import { URLS as apiUrls } from "../reusable/Utilities/urls.js";
import PropTypes from 'prop-types';
import { userHasRole } from "./../reusable/Utilities/userHasRole";
import { CONSTANTS } from "./../reusable/Constants/constants";
import If from "../reusable/If/If";

const templateType = Object.freeze({
  'Dealer': 'Dealer',
  'Gap': 'GAP',
  'Service': 'Service',
  'LWT': 'LWT',
});

const language = Object.freeze({
  'All': 'All',
  'English': 'English',
  'Spanish': 'Spanish'
});

const templateTypeOptions = [
  { "name": "Dealer", "value": templateType.Dealer },
  { "name": "GAP", "value": templateType.Gap },
  { "name": "Service", "value": templateType.Service },
  { "name": "LWT", "value": templateType.LWT },
];

const languageOptions = [
  { "name": "All", "value": language.All },
  { "name": "English", "value": language.English },
  { "name": "Spanish", "value": language.Spanish }
];

export default class TemplateDashboard extends React.Component {

  static contextTypes = {
    router: PropTypes.object.isRequired
  };

  static propTypes = {
    user: PropTypes.shape({
      id: PropTypes.number.isRequired,
      first_name: PropTypes.string.isRequired,
      last_name: PropTypes.string.isRequired,
      email: PropTypes.string.isRequired,
      stores: PropTypes.arrayOf(PropTypes.string.isRequired),
      roles: PropTypes.shape({
        Map: PropTypes.object,
      }).isRequired,
    }),
    location: PropTypes.shape({
      query: PropTypes.shape({
        language: PropTypes.string,
        templateType: PropTypes.string,
        sortByNameOrder: PropTypes.string
      }).isRequired,
    }).isRequired,
  };

  constructor(props) {
    super(props);
    this.state = {
      templates: [],
      showLoader: false,
      displayCreateTemplateFormModal: false,
      templateType: templateType.Gap,
      language: language.English,
      sortByNameOrder: 'asc',
      templateObject: {
        id: 0,
        name: '',
        template_text: '',
        template_type: templateType.Gap,
        language: language.English,
        updated_at: '',
        updated_by_user_id: 0,
        updated_by_user_name: ''
      },
      type: '',
      displayDeleteConfirmationModal: false,
      toBeDeletedId: 0,
      toBeDeletedName: ''
    };
    this.pushRouter = this.pushRouter.bind(this);
    this.loadTemplates = this.loadTemplates.bind(this);
    this.getTemplate = this.getTemplate.bind(this);
    this.onDeleteTemplate = this.onDeleteTemplate.bind(this);
    this.onDeleteDecline = this.onDeleteDecline.bind(this);
    this.onDeleteTemplateConfirmation = this.onDeleteTemplateConfirmation.bind(this);
    this.createUpdateTemplate = this.createUpdateTemplate.bind(this);
    this.onEditTemplate = this.onEditTemplate.bind(this);
    this.onCreateTemplate = this.onCreateTemplate.bind(this);
    this.onTemplateTypeChange = this.onTemplateTypeChange.bind(this);
    this.onLanguageChange = this.onLanguageChange.bind(this);
    this.sortListByTemplateName = this.sortListByTemplateName.bind(this);
    this.closeTemplateModalDOM = this.closeTemplateModalDOM.bind(this);
    this.renderToolBar = this.renderToolBar.bind(this);
    this.renderTemplatesList = this.renderTemplatesList.bind(this);
  }

  componentDidMount() {
    document.title = 'TCA Portal ADMIN';
    this.loadTemplates();
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if ((nextProps.location.query.templateType !== this.props.location.query.templateType) ||
      (nextProps.location.query.language !== this.props.location.query.language) ||
      (nextProps.location.query.sortByNameOrder !== this.props.location.query.sortByNameOrder)) {
      this.setState({
        templateType: nextProps.location.query.templateType || templateType.Gap,
        language: nextProps.location.query.language || language.English,
        sortByNameOrder: nextProps.location.query.sortByNameOrder || 'asc'
      }, function () {
        this.loadTemplates();
      });
    }
  }

  pushRouter() {
    const query = {
      templateType: this.state.templateType,
      language: this.state.language,
      sortByNameOrder: this.state.sortByNameOrder
    };
    const route = { pathname: "/admin", query };
    if (!this.context.router.isActive(route)) {
      this.context.router.push(route);
    }
  }

  loadTemplates() {
    let query = '?';
    if (this.state.templateType) {
      query = `${query}template_type=${this.state.templateType}`;
    }
    if (this.state.language && this.state.language !== language.All) {
      query = `${query}&language=${this.state.language}`;
    }
    const url = `${apiUrls.templates + query}&sort_by_name=${this.state.sortByNameOrder}`;
    this.setState({ showLoader: true }, () => {
      this.getTemplate(url);
    });
  }

  getTemplate(url) {
    ajax(url, {}, {}, (data, status) => {
      if (status === 200) {
        const state = {
          showLoader: false
        };
        if (data.email_templates) {
          state.templates = data.email_templates;
        } else if (data.email_template) {
          state.templateObject = data.email_template;
        }
        this.setState(state);
      } else {
        this.setState({ showLoader: false }, () => {
          Alert.error("Click the browser's Refresh button to reload the data. If the error continues, contact your system administrator.");
        });
      }
    });
  }

  onDeleteTemplate(templateId, name) {
    this.setState({ toBeDeletedId: templateId, toBeDeletedName: name, displayDeleteConfirmationModal: true });
  }

  onDeleteDecline() {
    this.setState({ toBeDeletedId: 0, toBeDeletedName:'', displayDeleteConfirmationModal: false });
  }

  onDeleteTemplateConfirmation() {
    this.setState({ showLoader: true }, function () {
      ajax(`${apiUrls.templates}/${this.state.toBeDeletedId}`, {}, { method: 'DELETE' }, (data, status) => {
        if (status === 200) {
          this.setState({ showLoader: false, toBeDeletedId: 0, toBeDeletedName: '', displayDeleteConfirmationModal: false }, function () {
            this.loadTemplates();
          });
        } else {
          this.setState({ showLoader: false, toBeDeletedId: 0, toBeDeletedName:'', displayDeleteConfirmationModal: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  }

  createUpdateTemplate(object) {
    let url = apiUrls.templates;
    const method = { method: 'POST' };
    if (this.state.type === "edit") {
      url += `/${this.state.templateObject.id}`;
      method.method = 'PUT';
    }
    this.setState({ showLoader: true }, function () {
      ajax(url, object, method, (data, status) => {
        if (status === 200) {
          this.setState({ showLoader: false }, function () {
            this.closeTemplateModalDOM();
            this.loadTemplates();
          });
        } else {
          this.setState({ showLoader: false }, function () {
            this.closeTemplateModalDOM();
            Alert.error("Click the browser's Refresh button to reload the data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  }

  onEditTemplate(templateId) {
    this.setState({ showLoader: true }, function () {
      ajax(`${apiUrls.templates}/${templateId}`, {}, {}, (data, status) => {
        if (status === 200) {
          this.setState({
            showLoader: false,
            templateObject: data.email_template,
            displayCreateTemplateFormModal: true,
            type: 'edit'
          });
        } else {
          this.setState({ showLoader: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  }

  onCreateTemplate() {
    const templateObject = {
      id: 0,
      name: '',
      template_text: '',
      template_type: templateType.Gap,
      language: language.English,
      updated_at: '',
      updated_by_user_id: 0,
      updated_by_user_name: ''
    };
    this.setState({ templateObject, displayCreateTemplateFormModal: true, type: 'create' });
  }

  onTemplateTypeChange(templateType) {
    this.setState({ templateType }, () => {
      this.pushRouter();
    });
  }

  onLanguageChange(language) {
    this.setState({ language }, () => {
      this.pushRouter();
    });
  }

  sortListByTemplateName(sortingOrder) {
    this.setState({ sortByNameOrder: sortingOrder }, () => {
      this.pushRouter();
    });
  }

  closeTemplateModalDOM() {
    if (event) {
      event.preventDefault();
    }
    this.setState({ displayCreateTemplateFormModal: false });
  }

  onSaveClick = () => {
    this.setState({ showLoader: true }, function () {
      let data = this.state.templates.map((template) => template.id);
      ajax(`${apiUrls.templatesReorder}`, { "template_ids": data }, { method: "PUT" }, (data, status) => {
        if (status === 200) {
          this.setState({
            showLoader: false
          }, this.loadTemplates);
        } else {
          this.setState({ showLoader: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  renderToolBar() {
    return (
      <div className="row">
        <div className="col-2">
          <SelectBox value={ this.state.templateType }
            onChange={ this.onTemplateTypeChange }
            optionsList={ templateTypeOptions }
            customClassName="form-control-sm"/>
        </div>
        <div className="col-2">
          <SelectBox value={ this.state.language }
            onChange={ this.onLanguageChange }
            optionsList={ languageOptions }
            customClassName="form-control-sm"/>
        </div>
        <div className="ml-auto pr-3">
          <button onClick={ this.onCreateTemplate } className="btn btn-primary btn-sm cursor-pointer">
            <i className="fa fa-arrow-circle-up"></i>
            &nbsp;New Template
          </button>
        </div>
      </div>
    );
  }

  renderTemplatesList() {
    return (
      <ListView onEditTemplate={ this.onEditTemplate }
        onDeleteTemplate={ this.onDeleteTemplate }
        sortListByTemplateName={ this.sortListByTemplateName }
        templateList={ this.state.templates }
        showOrder={ this.state.language === language.All && (this.state.templateType === templateType.Gap || this.state.templateType === templateType.LWT) && userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaimsManager) }
        updateTemplateList={ (templates) => this.setState({ templates }) }/>
    );
  }

  render() {
    const spinnerMessage = <p className='text-center'><i className="fa fa-refresh fa-spin"/> Loading...</p>;
    return (
      <Loader show={ this.state.showLoader } message={ spinnerMessage }>
        <section className="row">
          <div className="col-12">
            <PageHeader pageTitle="Templates" user={this.props.user}/>
            <div className="container">
              <div className="row justify-content-center">
                <div className="col-12">
                  {this.renderToolBar()}
                </div>
              </div>
              <div className="row justify-content-center">
                <div className="col-12 mt-3">
                  {this.renderTemplatesList()}
                </div>
              </div>
            </div>
          </div>
        </section>
        <If condition={ this.state.language === language.All && (this.state.templateType === templateType.Gap || this.state.templateType === templateType.LWT ) && userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaimsManager) }>
          <div className="row no-gutters col-12 justify-content-end">
            <button className="btn btn-secondary mr-2" onClick={ this.loadTemplates }>
                    Reset
            </button>
            <button className="btn btn-primary"
              onClick={ this.onSaveClick }>
                    Save
            </button>
          </div>
        </If>
        <TemplateEditor templateTypeOptions={ templateTypeOptions }
          type={ this.state.type }
          languageOptions={ languageOptions }
          displayCreateTemplateFormModal={ this.state.displayCreateTemplateFormModal }
          closeTemplateModalDOM={ this.closeTemplateModalDOM }
          templateTypeMap={ templateType }
          languageMap={ language }
          templateData={ this.state.templateObject }
          createUpdateTemplate={ this.createUpdateTemplate }/>
        <ConfirmationModal displayConfirmationModal={ this.state.displayDeleteConfirmationModal }
          displayMessage={ `Are you sure you want to delete ${this.state.toBeDeletedName} ?` }
          onConfirm={ this.onDeleteTemplateConfirmation }
          onDecline={ this.onDeleteDecline }
          confirmButtonText="Yes"
          declineButtonText="No"/>
      </Loader>
    );
  }
}
