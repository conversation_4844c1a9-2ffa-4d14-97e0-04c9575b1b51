import React from 'react';
import PropTypes from 'prop-types';
import EmailListContainer from './EmailListContainer.jsx';
import EmailListItem from './EmailListItem.jsx';
import If from '../reusable/If/If';

export default class ListView extends React.Component {

  static propTypes = {
    onEditTemplate: PropTypes.func.isRequired,
    onDeleteTemplate: PropTypes.func.isRequired,
    sortListByTemplateName: PropTypes.func.isRequired,
    templateList: PropTypes.array,
    updateTemplateList: PropTypes.func,
    showOrder: PropTypes.bool
  };

  constructor(props) {
    super(props);
    this.state = {
      nameSortOrder: 'asc'
    };
    this.sortListByTemplateName = this.sortListByTemplateName.bind(this);
    this.onActionChange = this.onActionChange.bind(this);
    this.renderTemplateListBody = this.renderTemplateListBody.bind(this);
    this.renderTemplateListHeader = this.renderTemplateListHeader.bind(this);
    this.renderTemplateList = this.renderTemplateList.bind(this);
  }

  sortListByTemplateName() {
    if (this.state.nameSortOrder == 'asc') {
      this.setState({ nameSortOrder: 'desc' }, function () {
        this.props.sortListByTemplateName(this.state.nameSortOrder);
      });
    } else {
      this.setState({ nameSortOrder: 'asc' }, function () {
        this.props.sortListByTemplateName(this.state.nameSortOrder);
      });
    }
  }

  onActionChange(id, name, event) {
    if (event.target.value === 'edit') {
      this.props.onEditTemplate(id, name);
    } else if (event.target.value === 'delete') {
      this.props.onDeleteTemplate(id, name);
    }
    event.target.value = '';
  }

  moveTemplates = (dragIndex, hoverIndex) => {
    const { templateList } = this.props;
    const length = templateList.length;

    if(dragIndex < 0){
      dragIndex = 0;
    } else if(dragIndex >= length){
      dragIndex = length - 1;
    }

    if(hoverIndex < 0){
      hoverIndex = 0;
    } else if(hoverIndex >= length){
      hoverIndex = length - 1;
    }

    if (templateList[dragIndex] === undefined || templateList[hoverIndex] === undefined) {
      return;
    }

    const templates = templateList.slice();
    const element = templateList[dragIndex];

    templates.splice(dragIndex,1);
    templates.splice(hoverIndex, 0, element);

    this.props.updateTemplateList(templates);
  };
  
  renderTemplateListBody() {
    return (<EmailListContainer>
      {this.props.templateList.map((template, i)=> {
        return (<EmailListItem key={ template.id }
          index={ i }
          template={ template }
          move={ this.moveTemplates }
          showOrder={ this.props.showOrder }
          onEditTemplate={ this.props.onEditTemplate.bind(null,template.id) }
          onActionChange={ this.onActionChange.bind(this, template.id, template.name) }
        />);
      })}
    </EmailListContainer>);
  }

  renderTemplateListHeader() {
    return (
      <tr>
        <If condition={ this.props.showOrder }>
          <th/>
        </If>
        <th className="cursor-pointer"
          onClick={this.sortListByTemplateName}>
          Name&nbsp;
          <i className={this.state.nameSortOrder == 'asc' ? "fa fa-caret-up" : "fa fa-caret-down"}/>
        </th>
        <th>Date</th>
        <th>Type</th>
        <th>Language</th>
        <th>Action</th>
      </tr>
    );
  }

  renderTemplateList() {
    return (
      <div>
        <table className="table table-striped">
          <thead>
            {this.renderTemplateListHeader()}
          </thead>
          {this.renderTemplateListBody()}
        </table>
      </div>
    );
  }

  render() {
    return (
      <section>
        { this.renderTemplateList() }
      </section>
    );
  }
}
