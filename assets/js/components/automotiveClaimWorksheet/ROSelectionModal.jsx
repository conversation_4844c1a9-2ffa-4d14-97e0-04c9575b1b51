import React from "react";
import Modal from "../../Modal.jsx";
import PropTypes from "prop-types";
import moment from "moment";
import dateFormat from "./../reusable/Utilities/dateFormat.js";

export default class ROSelectionModal extends React.Component {

  static propTypes = {
    roList: PropTypes.array.isRequired,
    displayModal: PropTypes.bool.isRequired,
    closeModal: PropTypes.func,
    onROSelect: PropTypes.func
  };

  renderROTable = () => {
    return(<table className="table table-hover table-striped">
      <thead>
        {this.renderTableHeader()}
      </thead>
      <tbody>
        {this.renderTableBody()}
      </tbody>
    </table>);
  };

  renderTableHeader = () => {
    return (<tr>
      <th>Customer</th>
      <th>VIN Number</th>
      <th>Service Date</th>
    </tr>);
  };

  renderTableBody = () => {
    return this.props.roList.map(this.renderTableBodyRow);
  };

  renderTableBodyRow = (ro) => {
    const { customer } = ro;
    const vin = ro.vehicle.vin;
    return (<tr key={ vin } className="cursor-pointer"
      onClick={ this.props.onROSelect.bind(null,ro) }>
      <td>{`${customer.first_name} ${customer.last_name}`}</td>
      <td>{vin}</td>
      <td>{ro.date ? moment(ro.date).format(dateFormat.displayDateFormat) : ""}</td>
    </tr>);
  };

  render() {
    return (
      <section>
        <Modal visible={ this.props.displayModal }
          close={ this.props.closeModal }
          title={ `Select RO` }>
          <div className="my-3">
            <p>
              Select the customer for the RO you want to process
            </p>
          </div>
          <div>
            {this.renderROTable()}
          </div>
        </Modal>
      </section>
    );
  }
}