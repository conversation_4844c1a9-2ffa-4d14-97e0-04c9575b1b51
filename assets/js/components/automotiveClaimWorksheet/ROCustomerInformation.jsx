import React from "react";
import PropTypes from "prop-types";
import InputBox from "../reusable/InputBox/InputBox.jsx";
import StateList from "../reusable/StatesList/StateList.jsx";
import ZipCodeBox from "../reusable/ZipCodeBox/ZipCodeBox.jsx";
import EmailBox from "../reusable/EmailBox/EmailBox.jsx";
import PhoneNumberBox from "../reusable/PhoneNumber/PhoneNumber.jsx";
import immstruct from "immstruct";
import Immutable from "immutable";

export default class ROCustomerInformation extends React.Component{

  IMMS_KEY = "customer";

  static propTypes = {
    ro: PropTypes.object,
    contractCustomer: PropTypes.object,
    disableUpdate: PropTypes.bool,
    onClose: PropTypes.func,
    onUpdate: PropTypes.func
  };

  static defaultProps = {
    contractCustomer: {},
    ro: {},
    disableUpdate: false
  };

  constructor(props){
    super(props);

    this.contractCustomer = immstruct(
      this.IMMS_KEY,
      {
        customer_name: props.contractCustomer && props.contractCustomer.customer_name,
        street_address: props.contractCustomer && props.contractCustomer.street_address,
        city: props.contractCustomer && props.contractCustomer.city,
        state: props.contractCustomer && props.contractCustomer.state,
        postal_code: props.contractCustomer && props.contractCustomer.postal_code,
        phone_number: props.contractCustomer && props.contractCustomer.phone_number,
        alternate_phone_number: props.contractCustomer && props.contractCustomer.alternate_phone_number,
        email_address: props.contractCustomer && props.contractCustomer.email_address,
        best_contact_method: props.contractCustomer && props.contractCustomer.best_contact_method,
        product_code: props.contractCustomer && props.contractCustomer.product_code,
        expiration_date: props.contractCustomer && props.contractCustomer.expiration_date,
        contract_status: props.contractCustomer && props.contractCustomer.contract_status,
        contract_number: props.contractCustomer && props.contractCustomer.contract_number,
        customer_id: props.contractCustomer && props.contractCustomer.customer_id
      }
    );

    this.contractCustomer.on("swap", (newStructure, oldStructure, keyPath) => {
      this.setState({ contractCustomer: this.contractCustomer.cursor() });
    });

    this.state = {
      contractCustomer: this.contractCustomer.cursor(),
      note: ""
    };
  }

  componentWillUnmount() {
    this.contractCustomer.removeAllListeners();
    immstruct.remove(this.IMMS_KEY);
  }

  onValueChange = (name, e) => {
    let note = {};
    note[name] = e.target.value;

    this.setState(note);
  };

  onCursorChange = (name, e) => {
    this.onCursorValueChange(name, e.target.value);
  };

  onCursorValueChange = (name, value) => {
    this.contractCustomer.cursor(name).update(() => value);
  };

  onUpdate = () => {
    let updatedCustomer = this.state.contractCustomer.toJS();
    updatedCustomer["notes"] = this.state.note;
    this.props.onUpdate(updatedCustomer);
  };

  copy(source, target, sourceKey, targetKey) {
    if(source[sourceKey]){
      target[targetKey] = source[sourceKey];
    }
    return target;
  }

  onUseRO = () => {
    const { ro } = this.props;
    let contractCustomer = this.state.contractCustomer.toJS();

    contractCustomer = this.copy(ro.customer, contractCustomer, "address", "street_address");
    contractCustomer = this.copy(ro.customer, contractCustomer, "city", "city");
    contractCustomer = this.copy(ro.customer, contractCustomer, "state", "state");
    contractCustomer = this.copy(ro.customer, contractCustomer, "postal_code", "postal_code");
    contractCustomer = this.copy(ro.customer, contractCustomer, "phone", "phone_number");
    contractCustomer = this.copy(ro.customer, contractCustomer, "email", "email_address");

    this.contractCustomer.cursor().update(() => Immutable.fromJS(contractCustomer));
  };

  renderBestContactList() {
    const bestContactArray = [
      {
        "method": "Do not contact",
        "symbol": "D"
      },
      {
        "method": "Telephone",
        "symbol": "T"
      },
      {
        "method": "Email",
        "symbol": "E"
      },
      {
        "method": "Mail",
        "symbol": "M"
      },
      {
        "method": "Cellphone",
        "symbol": "C"
      }];
    return bestContactArray.map(this.renderContactMethod);
  }

  renderContactMethod(contactMethod, index) {
    return (<option key={ index } value={ contactMethod.symbol }>{contactMethod.method}</option>);
  }

  renderCustomerInformation = () => {
    const contractCustomer = this.state.contractCustomer.toJS();
    return (
      <div className="row">
        <h6 className="col-12">Contract Information</h6>
        <div className="col-6">
          <div className="row my-3">
            <div className="col-5">Customer Name</div>
            <div className="col-7">
              <InputBox type="Text"
                isDisabled={ true }
                value={ contractCustomer.customer_name } />
            </div>
          </div>
          <div className="row my-3">
            <div className="col-5">Address</div>
            <div className="col-7">
              <InputBox type="Text"
                value={ contractCustomer.street_address }
                onChange={ this.onCursorValueChange.bind(this, "street_address") }/>
            </div>
          </div>
          <div className="row my-3">
            <div className="col-5">City</div>
            <div className="col-7">
              <InputBox type="Text"
                value={ contractCustomer.city }
                onChange={ this.onCursorValueChange.bind(this, "city") }/>
            </div>
          </div>
          <div className="row my-3">
            <div className="col-5">State</div>
            <div className="col-7">
              <StateList customClassName="form-control-sm"
                value={ contractCustomer.state }
                onChange={ this.onCursorValueChange.bind(this, "state") }/>
            </div>
          </div>
          <div className="row my-3">
            <div className="col-5">ZIP</div>
            <div className="col-7">
              <ZipCodeBox customClassName="form-control-sm"
                value={ contractCustomer.postal_code }
                onChange={ this.onCursorValueChange.bind(this, "postal_code") }/>
            </div>
          </div>
        </div>
        <div className="col-6">
          <div className="row my-3">
            <div className="col-5">Phone</div>
            <div className="col-7">
              <PhoneNumberBox customClassName="form-control-sm"
                value={ contractCustomer.phone_number }
                onChange={ this.onCursorValueChange.bind(this, "phone_number") }/>
            </div>
          </div>
          <div className="row my-3">
            <div className="col-5">Alt Phone</div>
            <div className="col-7">
              <PhoneNumberBox customClassName="form-control-sm"
                value={ contractCustomer.alternate_phone_number }
                onChange={ this.onCursorValueChange.bind(this, "alternate_phone_number") }/>
            </div>
          </div>
          <div className="row my-3">
            <div className="col-5">Email</div>
            <div className="col-7">
              <EmailBox customClassName="form-control-sm"
                value={ contractCustomer.email_address }
                onChange={ this.onCursorValueChange.bind(this, "email_address") }/>
            </div>
          </div>
          <div className="row my-3">
            <div className="col-5">Best Contact</div>
            <div className="col-7">
              <select className="form-control form-control-sm"
                value={ contractCustomer.best_contact_method }
                onChange={ this.onCursorChange.bind(this,"best_contact_method") }>
                <option value="">Select Contact Method</option>
                {this.renderBestContactList()}
              </select>
            </div>
          </div>
        </div>
      </div>
    );
  };

  render(){
    const { ro } = this.props;
    return(
      <div className="col-12">
        <div className="row">
          <h4 className="col-12">Customer Information</h4>
          <div className="col-4">
            <div className="row">
              <h6 className="col-12">RO Information</h6>
              <div className="col-12 my-3">{`${ro.customer.first_name} ${ro.customer.last_name}`}</div>
              <div className="col-6">
                {`${ro.customer.address} ${ro.customer.city}, ${ro.customer.state} ${ro.customer.postal_code}`}
              </div>
              <div className="col-12">{ro.phone}</div>
              <div className="col-12 mt-2">
                <button className="btn btn-secondary" disabled={this.props.disableUpdate} onClick={ this.onUseRO }>Use RO Information</button>
              </div>
            </div>
          </div>
          <div className="col-8">
            {this.renderCustomerInformation()}
          </div>
        </div>
        <div className="row my-3 mx-1">
          <h6>Notes</h6>
          <textarea type="text"
            className="form-control"
            rows="5"
            value={ this.state.note }
            onChange={ this.onValueChange.bind(this,"note") }/>
        </div>
        <div className="col-12 my-3 mx-1">
          <div className="row my-3">
            Choosing update without making any changes will clear the warning.
          </div>
          <div className="row justify-content-end">
            <button className="btn btn-secondary mr-2"
              onClick={ this.props.onClose }>
                  Cancel
            </button>
            <button className="btn btn-primary" disabled={this.props.disableUpdate}
              onClick={ this.onUpdate }>
                  Update
            </button>
          </div>
        </div>
      </div>
    );
  }
}
