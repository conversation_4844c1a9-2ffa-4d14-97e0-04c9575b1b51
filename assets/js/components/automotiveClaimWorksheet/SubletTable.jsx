import React from "react";
import SubletTableRow from "./SubletTableRow.jsx";
import PropTypes from "prop-types";

const SubletTable = (props) => {

  const renderSubletRows = (sublets) => {
    if (sublets && Array.isArray(sublets) && sublets.length > 0) {
      return sublets.map((sublet, index) => <SubletTableRow sublet={sublet}
        claimObject={props.claimObject}
        key={sublet.id}
        complaintIndex={props.index}
        index={index}
        isDisabled={props.isDisabled}
        showRequiredNoteError={props.showRequiredNoteError}
        handlePartLaborUpdate={props.handlePartLaborUpdate}
        removePartOrLabor={props.removePartOrLabor} />);
    }
  };
    
  return (
    <div className="small mb-4">
      <table className="table table-sm">
        <thead className="thead-light">
          <tr className="row col-12 pr-0">
            <th className="col-4">
              Sublet Description
            </th>
            <th className="col">
              Requested
            </th>
            <th className="col" colSpan="2">
              Approved
            </th>
          </tr>
        </thead>
        {renderSubletRows(props.sublets)}
      </table>
      <span className="text-primary cursor-pointer"
        id={`add-labor-${props.index}`}
        onClick={!props.isDisabled ? props.addPartOrLabor : void 0}>
          Add item
      </span>
    </div>
  );
};

SubletTable.propTypes = {
  sublets: PropTypes.array,
  claimObject: PropTypes.object.isRequired,
  handlePartLaborUpdate: PropTypes.func.isRequired,
  addPartOrLabor: PropTypes.func.isRequired,
  removePartOrLabor: PropTypes.func.isRequired,
  isDisabled: PropTypes.bool,
  index: PropTypes.number,
  showRequiredNoteError: PropTypes.bool
};

export default SubletTable;