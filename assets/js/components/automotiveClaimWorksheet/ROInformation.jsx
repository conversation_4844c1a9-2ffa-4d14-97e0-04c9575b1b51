import React from "react";
import PropTypes from "prop-types";
import RODetails from "./RODetails.jsx";
import InputBox from "../reusable/InputBox/InputBox.jsx";
import Loader from "react-loader-advanced";
import { formatCurrency } from "../reusable/Utilities/format.js";

export default class ROInformation extends React.Component {

  static propTypes = {
    claimObject: PropTypes.object.isRequired,
    facilityDetails: PropTypes.object,
    updateState: PropTypes.func.isRequired,
    isDisabled: PropTypes.bool.isRequired,
    handleMultipleChange: PropTypes.func.isRequired,
    handleDateChange: PropTypes.func.isRequired,
    handleOnChange: PropTypes.func.isRequired,
    hasRO: PropTypes.bool.isRequired,
    showFacilityLoader: PropTypes.bool.isRequired,
    manuallyEnterRO: PropTypes.bool,
    handleManuallyEnterRO: PropTypes.func,
    usedRo: PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.number, PropTypes.string])).isRequired,
    isManager: PropTypes.bool.isRequired,
  };

  constructor(props) {
    super(props);
    this.state = {};
  }

  render() {
    const spinnerMessage = <p className='text-center'><i className="fa fa-refresh fa-spin"/> Loading...</p>;
    return (
      <section className="col">
        <RODetails claimObject={ this.props.claimObject }
          facilityDetails={ this.props.facilityDetails }
          hasRO={ this.props.hasRO }
          isDisabled={ this.props.isDisabled }
          onROUpdate={ this.props.updateState }
          handleDateChange={ this.props.handleDateChange }
          handleOnChange={ this.props.handleOnChange }
          manuallyEnterRO={ this.props.manuallyEnterRO }
          handleManuallyEnterRO={ this.props.handleManuallyEnterRO }
          isManager={this.props.isManager}
          usedRo={this.props.usedRo}/>
        <div className="form-group row">
          <div className="col-5">
            <label className="col-form-label-sm">
              Advisor
            </label>
          </div>
          <InputBox type="Text"
            id="advisor_inputBox"
            isDisabled={ this.props.isDisabled }
            customClass="col-7"
            value={ this.props.claimObject.advisor || ""}
            onBlur={ this.props.handleOnChange.bind(null, 'advisor') }
            onChange={ this.props.handleOnChange.bind(null, 'advisor') }/>
        </div>
        <Loader show={ this.props.showFacilityLoader } message={ spinnerMessage }>
          <div className="form-group row">
            <div className="col-5">
              <label className="col-form-label-sm">
                Labor Rate
              </label>
            </div>
            <InputBox type="Currency"
              id="labor_rate_inputBox"
              isDisabled={ this.props.isDisabled }
              customClass="col-7"
              hasDefaultValue={ true }
              value={ formatCurrency(this.props.claimObject.labor_rate) }
              onBlur={ this.props.handleOnChange.bind(null, 'labor_rate') }
            />
          </div>
          <div className="form-group row">
            <div className="col-5">
              <label className="col-form-label-sm">
                Tax: Parts
              </label>
            </div>
            <InputBox type="Percentage"
              id="tax_parts_inputBox"
              isDisabled={ this.props.isDisabled }
              customClass="col-7"
              hasDefaultValue={ true }
              shouldRoundOff={ false }
              value={ this.props.claimObject.tax_parts || 0 }
              onBlur={ this.props.handleOnChange.bind(null, 'tax_parts') }
              onChange={ this.props.handleOnChange.bind(null, 'tax_parts') }/>
          </div>
          <div className="form-group row">
            <div className="col-5">
              <label className="col-form-label-sm">
                Tax: Labor
              </label>
            </div>
            <InputBox type="Percentage"
              id="tax_labor_inputBox"
              isDisabled={ this.props.isDisabled }
              customClass="col-7"
              hasDefaultValue={ true }
              shouldRoundOff={ false }
              value={ this.props.claimObject.tax_labor || 0}
              onBlur={ this.props.handleOnChange.bind(null, 'tax_labor') }
              onChange={ this.props.handleOnChange.bind(null, 'tax_labor') }/>
          </div>
          <div className="form-group row">
            <div className="col-5">
              <label className="col-form-label-sm">
                Pre Auth
              </label>
            </div>
            <InputBox type="Currency"
              id="pre_auth_amount_inputBox"
              isDisabled={ this.props.isDisabled }
              customClass="col-7"
              hasDefaultValue={ true }
              value={ formatCurrency(this.props.claimObject.pre_auth_amount || 0) }
              onBlur={ this.props.handleOnChange.bind(null, 'pre_auth_amount') }/>
          </div>
          <div className="form-group row">
            <div className="col-5">
              <label className="col-form-label-sm">
                Pre-Auth#
              </label>
            </div>
            <InputBox type="Text"
              id="pre_auth_number_inputBox"
              isDisabled={ true }
              customClass="col-7"
              value={ this.props.claimObject.pre_auth_number || ""}/>
          </div>
          <div className="form-group row">
            <div className="col-12">
              <label className="col-form-label-sm">
                Repairing Facility Labor Rate
              </label>
            </div>
          </div>
          <div className="form-group row">
            <InputBox type="Currency"
              id="repair_facility_labor_rate_inputBox"
              isDisabled={ false }
              customClass="col-12"
              value={ this.props.claimObject.repairing_facility_labor_rate || ""}
              onBlur={ this.props.handleOnChange.bind(null, 'repairing_facility_labor_rate') }
              onChange={ this.props.handleOnChange.bind(null, 'repairing_facility_labor_rate') }/>
          </div>
        </Loader>
      </section>
    );
  }
}