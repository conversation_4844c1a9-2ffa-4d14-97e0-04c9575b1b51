import React from 'react';
import PropTypes from 'prop-types';

export default class WarningPresentational extends React.Component{

  static propTypes = {
    message: PropTypes.string.isRequired,
    onActionCallBack: PropTypes.func.isRequired
  };

  render(){
    return (
      <div className="row">
        <div className="col-12 my-4">
          <p className="text-center">
            {this.props.message}
          </p>
        </div>
        <div className="col-12 text-center my-4">
          <button type="button"
            className="btn btn-primary cursor-pointer mr-3"
            onClick={ this.props.onActionCallBack }>
                      Ok
          </button>
        </div>
      </div>
    );
  }
}