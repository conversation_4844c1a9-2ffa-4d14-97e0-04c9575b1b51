import React, { useEffect, useState } from 'react';
import immstruct from "immstruct";
import DatePicker from "react-datepicker";
import Alert from "react-s-alert";
import Loader from 'react-loader-advanced';
import PropTypes from 'prop-types';
import Immutable from "immutable";
import moment from "moment";

import Modal from "../../Modal.jsx";
import { URLS as apiUrls } from "./../reusable/Utilities/urls";
import { json as ajax } from "./../../ajax.js";
import InputBox from "../reusable/InputBox/InputBox.jsx";
import SelectBox from "../reusable/SelectBox/SelectBox.jsx";
import StateList from "../reusable/StatesList/StateList.jsx";
import CountryList from "../reusable/CountryList/CountryList.jsx";
import { CONSTANTS } from "./../reusable/Constants/constants.js";
import dateFormat from "./../reusable/Utilities/dateFormat.js";

const optionList = [
  { name: CONSTANTS.PAYMENT_TYPE_NAMES.DEFAULT, value: "" },
  { name: CONSTANTS.PAYMENT_TYPE_NAMES.CREDIT_CARD, value: CONSTANTS.PAYMENT_TYPE_CODES.CREDIT_CARD },
  { name: CONSTANTS.PAYMENT_TYPE_NAMES.CUSTOMER, value: CONSTANTS.PAYMENT_TYPE_CODES.CUSTOMER },
  { name: CONSTANTS.PAYMENT_TYPE_NAMES.STORE, value: CONSTANTS.PAYMENT_TYPE_CODES.STORE }
];

const ModelType = Object.freeze({
  LABORRATE:   "LR",
  LABORTAX:  "LT",
  PARTSTAX: "PT",
});

export default class FacilityUpdateModal extends React.Component {

  IMMS_KEY = 'facility';

  static propTypes = {
    displayModal: PropTypes.bool.isRequired,
    onClose: PropTypes.func.isRequired,
    onFacilitySelect: PropTypes.func.isRequired,
    facilityID: PropTypes.number,
    message: PropTypes.string.isRequired,
    viewFacility: PropTypes.bool,
    user: PropTypes.shape({
      id: PropTypes.number.isRequired,
      first_name: PropTypes.string.isRequired,
      last_name: PropTypes.string.isRequired,
      email: PropTypes.string.isRequired,
      stores: PropTypes.arrayOf(PropTypes.string.isRequired),
      roles: PropTypes.shape({
        Map: PropTypes.object,
      }).isRequired
    })
  };

  constructor(props) {
    super(props);
    this.facility = immstruct(
      this.IMMS_KEY,
      {
        name: "",
        phone: "",
        postal_code: "",
        state_code: "",
        facility_code: "",
        labor_rate: [],
        labor_tax: [],
        parts_tax: [],
        address: "",
        city: "",
        country: "",
        fax: "",
        vendor_id: "",
        contact: "",
        is_active: true,
        ein: "",
        tax_type: "",
        payment_type: ""
      }
    );
    this.facility.on('swap', (newStructure, oldStructure, keyPath) => {
      this.setState({ facility: this.facility.cursor() });
    });
    this.state = {
      facility: this.facility.cursor(),
      showLoader: false,
      displayConfirmationModal: false,
      modalSize: 'extra-large',
      isUpdated: false
    };
  }

  componentDidMount(){
    if(this.props.facilityID) {
      this.loadFacility(this.props.facilityID);
    }
  }

  UNSAFE_componentWillReceiveProps(nextProps){
    if (nextProps.facilityID && this.props.facilityID !== nextProps.facilityID) {
      this.setState({
        isUpdated: false
      });
      this.loadFacility(nextProps.facilityID);
    }
  }

  componentWillUnmount = () => {
    this.facility.removeAllListeners();
    immstruct.remove(this.IMMS_KEY);
  };

  loadFacility = (facilityID) => {
    this.setState({ showLoader: true }, () => {
      ajax(`${apiUrls.facility}/${facilityID}`, {}, {}, (data, status) => {
        if (status === 200) {
          const facilityData = {
            name: data.facility.name,
            facility_code: data.facility.facility_code,
            labor_rate: data.facility.labor_rate || [],
            labor_tax: data.facility.labor_tax || [],
            parts_tax: data.facility.parts_tax || [],
            address: data.facility.address.String,
            city: data.facility.city,
            state_code: data.facility.state_code,
            postal_code: data.facility.postal_code,
            country: data.facility.country,
            phone: data.facility.phone.String,
            fax: data.facility.fax.String,
            vendor_id: data.facility.vendor_id.String,
            contact: data.facility.contact.String,
            is_active: data.facility.is_active,
            ein: data.facility.ein,
            tax_type: data.facility.tax_type,
            payment_type: data.facility.payment_type
          };
          this.facility.cursor().update(() => Immutable.fromJS(facilityData));
          this.setState({ showLoader: false });
        } else {
          this.setState({ showLoader: false, isWorksheetUpdated: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  onCursorToggle = (name) => {
    this.facility.cursor(name).update(oldValue => !oldValue);
    this.setState({
      isUpdated: true
    });
  };

  onChange = (name, value) => {
    this.facility.cursor(name).update(oldValue => value);
    this.setState({
      isUpdated: true
    });
  };

  onUpdateFacility = (facility) => {
    facility.id = this.props.facilityID;
    this.setState({ showLoader: true }, () => {
      ajax(`${apiUrls.facility}/${this.props.facilityID}`, facility, { method: 'PUT' }, (data, status) => {
        this.setState({ showLoader: false }, () => {
          if (status === 200) {
            this.props.onClose();
            this.props.onFacilitySelect(this.props.facilityID);
            Alert.success("Facility updated successfully");
          } else if (status === 400) {
            if (data.message) {
              Alert.error(data.message);
            }
            if (data.errors) {
              for (let key in data.errors) {
                if (key) {
                  Alert.error(data.errors[key]);
                }
              }
            }
          } else if (status === 504) {
            Alert.error("Server timeout error");
          } else {
            this.props.onClose();
            Alert.error("Failed to update facility. If the error continues, contact your system administrator.");
            if (data.message) {
              Alert.error(data.message);
            }
          }
        });
      });
    });
  };

  getFormStatus = (facility) => {
    return (this.state.isUpdated && facility.name &&
        facility.city &&
      facility.state_code &&
      facility.postal_code);
  };

  onSaveClick = (facility) => {
    if ((facility.facility_code.length) < 5 || (facility.facility_code.length > 7)) {
      Alert.warning("The Facility Code needs to be between five and seven characters long, please correct.");
      return;
    }
    this.onUpdateFacility(facility);
  };

  handleTaxModalUpdates = (facility, modelDetails, handleCloseTaxModal) => {
    let existingRates = [];
    let name = "labor_rate";
    let value = {
      facility_code: facility.facility_code,
      effective_rate: modelDetails.textValue,
      effective_date: modelDetails.effectiveDate.format('YYYY-MM-DD'),
      rate_type: ModelType.LABORRATE,
      is_new: true,
      active: true,
    };

    if (modelDetails.modelType === ModelType.LABORRATE) {
      if (facility.labor_rate) {
        existingRates = facility.labor_rate.filter(obj => !obj.is_new);
      }
    } else if (modelDetails.modelType === ModelType.LABORTAX) {
      if (facility.labor_tax) {
        existingRates = facility.labor_tax.filter(obj => !obj.is_new);
      }
      name = "labor_tax";
      value = {...value, rate_type: ModelType.LABORTAX};
    } else if (modelDetails.modelType === ModelType.PARTSTAX) {
      if (facility.parts_tax) {
        existingRates = facility.parts_tax.filter(obj => !obj.is_new);
      }
      name = "parts_tax";
      value = {...value, rate_type: ModelType.PARTSTAX};
    }
    this.onChange(name, [value, ...existingRates]);
    handleCloseTaxModal();
  }

  render() {
    const {
      viewFacility,
      message,
      displayModal,
      onClose,
    } = this.props;

    const facility = this.state.facility.toJS();
    const spinnerMessage = <p className='text-center'><i className="fa fa-refresh fa-spin"/> Loading...</p>;
    return (
      <section>
        <Modal visible={ displayModal } close={ onClose } size={ this.state.modalSize }>
          <Loader show={ this.state.showLoader } message={ spinnerMessage }>
            <span><strong>{viewFacility ? `View ` : `Update ` }Repair Facility</strong></span>
            <div className="col-12 my-4">
              <p className="text-center">
                {message}
              </p>
            </div>
            <div className="col-12 row justify-content-center mt-4">
              <div className="col-6">
                <div className="form-group row col-12">
                  <div className="col-4">
                    <label className="col-form-label col-form-label-sm">
                      Facility Name:
                    </label>
                  </div>
                  <InputBox type="Text"
                    id="facility_inputBox"
                    customClass="col-8"
                    value={ facility.name }
                    onChange={ this.onChange.bind(this, 'name') }
                    isDisabled={ viewFacility }/>
                </div>
                <div className="form-group row col-12">
                  <div className="col-4">
                    <label className="col-form-label col-form-label-sm">
                            Facility Code:
                    </label>
                  </div>
                  <InputBox type="Text"
                    id="facility_code_inputBox"
                    customClass="col-8"
                    value={ facility.facility_code }
                    onChange={ this.onChange.bind(this, 'facility_code') }
                    isDisabled={ viewFacility }/>
                </div>
                <div className="form-group row col-12">
                  <div className="col-4">
                    <label className="col-form-label col-form-label-sm">
                              Active:
                    </label>
                  </div>
                  <div className="col-1 ml-4">
                    <input type="checkbox"
                      className="form-check-input"
                      id="active_checkBox"
                      disabled={ viewFacility }
                      checked={ facility.is_active }
                      onChange={ this.onCursorToggle.bind(null, 'is_active') }/>
                  </div>
                </div>
                <div className="form-group row col-12">
                  <div className="col-4">
                    <label className="col-form-label col-form-label-sm">
                              Country:
                    </label>
                  </div>
                  <div className="col-8">
                    <CountryList id="country_selectBox"
                      customClassName="form-control-sm"
                      isDisabled={ viewFacility }
                      value={ facility.country }
                      onChange={ this.onChange.bind(null, 'country') }/>
                  </div>
                </div>
                <div className="form-group row col-12" key="address">
                  <div className="col-4">
                    <label className="col-form-label col-form-label-sm">
                            Address:
                    </label>
                  </div>
                  <InputBox type="Text"
                    id="address_inputBox"
                    customClass="col-8"
                    isDisabled={ viewFacility }
                    value={ facility.address }
                    onChange={ this.onChange.bind(this, 'address') }/>
                </div>
                <div className="form-group row col-12" key="city">
                  <div className="col-4">
                    <label className="col-form-label col-form-label-sm">
                            City:
                    </label>
                  </div>
                  <InputBox type="Text"
                    id="city_inputBox"
                    customClass="col-8"
                    isDisabled={ viewFacility }
                    value={ facility.city }
                    onChange={ this.onChange.bind(this, 'city') }/>
                </div>
                <div className="form-group row col-12" key="state">
                  <div className="col-4">
                    <label className="col-form-label col-form-label-sm">
                        State/Province:
                    </label>
                  </div>
                  <div className='input-group-sm col-8'>
                    <StateList defaultText="Select State"
                      customClassName="form-control-sm"
                      isDisabled={ viewFacility }
                      country={ facility.country }
                      value={ facility.state_code }
                      onChange={ this.onChange.bind(this, 'state_code') }/>
                  </div>
                </div>
                <div className="form-group row col-12" key="zip">
                  <div className="col-4">
                    <label className="col-form-label col-form-label-sm">
                            ZIP:
                    </label>
                  </div>
                  <InputBox placeholder="Zip"
                    type="Text"
                    customClass="col-8"
                    isDisabled={ viewFacility }
                    value={ facility.postal_code }
                    onChange={ this.onChange.bind(this, 'postal_code') }/>
                </div>
                <div className="form-group row col-12" key="phone">
                  <div className="col-4">
                    <label className="col-form-label col-form-label-sm">
                            Phone:
                    </label>
                  </div>
                  <InputBox type="Text"
                    id="phone_inputBox"
                    customClass="col-8"
                    isDisabled={ viewFacility }
                    value={ facility.phone }
                    onChange={ this.onChange.bind(this, 'phone') }/>
                </div>
                <div className="form-group row col-12" key="fax">
                  <div className="col-4">
                    <label className="col-form-label col-form-label-sm">
                            Fax:
                    </label>
                  </div>
                  <InputBox type="Text"
                    id="fax_inputBox"
                    customClass="col-8"
                    isDisabled={ viewFacility }
                    value={ facility.fax }
                    onChange={ this.onChange.bind(this, 'fax') }/>
                </div>
              </div>
              <div className="col-6">
                <div className="form-group row col-12">
                  <div className="col-4">
                    <label className="col-form-label col-form-label-sm">
                              Vendor #:
                    </label>
                  </div>
                  <InputBox type="Text"
                    id="vendor_inputBox"
                    customClass="col-8"
                    value={ facility.vendor_id }
                    isDisabled={ viewFacility }
                    onChange={ this.onChange.bind(null, 'vendor_id') }/>
                </div>
                <div className="form-group row col-12">
                  <div className="col-4">
                    <label className="col-form-label col-form-label-sm">
                      Advisor:
                    </label>
                  </div>
                  <InputBox type="Text"
                    id="contact_inputBox"
                    customClass="col-8"
                    value={ facility.contact }
                    isDisabled={ viewFacility }
                    onChange={ this.onChange.bind(null, 'contact') }/>
                </div>
                <div className="form-group row col-12">
                  <div className="col-4">
                    <label className="col-form-label col-form-label-sm">
                              EIN #:
                    </label>
                  </div>
                  <InputBox type="Text"
                    id="ein_inputBox"
                    customClass="col-8"
                    value={ facility.ein }
                    isDisabled={ viewFacility }
                    onChange={ this.onChange.bind(null, 'ein') }/>
                </div>
                <div className="form-group row col-12">
                  <div className="col-4">
                    <label className="col-form-label col-form-label-sm">
                              Tax Type:
                    </label>
                  </div>
                  <InputBox type="AlphaNumeric"
                    id="tax_type_inputBox"
                    customClass="col-8"
                    value={ facility.tax_type }
                    isDisabled={ viewFacility }
                    onChange={ this.onChange.bind(null, 'tax_type') }/>
                </div>
                <TaxComponent facility={facility} viewOnly={viewFacility} handleRatesUpdate={this.handleTaxModalUpdates} />
                <div className="form-group row col-12">
                  <div className="col-4">
                    <label className="col-form-label col-form-label-sm">
                    Payment type:
                    </label>
                  </div>
                  <div className="col-8">
                    <SelectBox customClassName="form-control-sm"
                      value={ facility.payment_type }
                      disabled={ viewFacility }
                      onChange={ this.onChange.bind(null, 'payment_type') }
                      optionsList={ optionList }/>
                  </div>
                </div>
              </div>
              <div className="row col-12 justify-content-end">
                <button className="btn btn-secondary mr-2" onClick={ this.props.onClose }>
                    Cancel
                </button>
                {!viewFacility && 
                  (<button className="btn btn-primary"
                    onClick={ this.onSaveClick.bind(this, facility) }
                    disabled={ !this.getFormStatus(facility) }>
                      Save
                  </button>)
                }
              </div>
            </div>
          </Loader>
        </Modal>
      </section>
    );
  }
}

const TaxComponent = ({ facility, viewOnly, handleRatesUpdate }) => {
  const [showRateTaxModal, setShowRateTaxModal] = useState(false);
  const [modelDetails, setModalDetails] = useState(() => initialModalDetails());
  const [dirty, setDirty] = useState(false);

  function initialModalDetails () {
    return {
      modelType: ModelType.LABORRATE,
      title: '',
      history: [],
      effectiveDate: moment(),
      textValue: '',
    };
  }

  // we dont want re-render each time something changes on form
  useEffect(() => {}, [facility]);

  const onLaborRateClick = () => {
    handleShowTaxModal(ModelType.LABORRATE, "Labor Rate Update", facility.labor_rate || []);
  };

  const onPartsTaxClick = () => {
    handleShowTaxModal(ModelType.PARTSTAX, "Parts Tax Update", facility.parts_tax || []);
  };

  const onLaborTaxClick = () => {
    handleShowTaxModal(ModelType.LABORTAX, "Labor Tax Update", facility.labor_tax || []);
  };

  const handleShowTaxModal = (modelType, title, history) => {
    setShowRateTaxModal(true);
    const rateObj = history.find(obj => obj.active);
    if (rateObj) {
      setModalDetails({
        modelType,
        title,
        history: history,
        effectiveDate: moment(rateObj.effective_date, "YYYY/MM/DD") || moment(),
        textValue: rateObj.effective_rate,
      });
    } else {
      setModalDetails({
        modelType,
        title,
        history: history,
        effectiveDate: moment(),
        textValue: '',
      });
    }
  };

  const handleCloseTaxModal = () =>  {
    setShowRateTaxModal(false);
    setDirty(false);
  };

  const handleEffectiveRateChange = (e) => {
    setModalDetails({...modelDetails, textValue: e.target.value});
    setDirty(true);
  };

  const handleEffectiveDateChange = (e) => {
    setModalDetails({...modelDetails, effectiveDate: e});
    setDirty(true);
  };

  const renderButtons = () => {
    return (
      <div className='float-right my-3'>
        <button type='button'
          onClick={handleCloseTaxModal}
          className={`btn btn-secondary`}
        >
          <i className='fa fa-ban' /> Cancel
        </button>

        &nbsp;
        <button type='button'
          className={`btn btn-primary`}
          disabled={!dirty}
          onClick={() => handleRatesUpdate(facility, modelDetails, handleCloseTaxModal)}
        >
          <i className='fa fa-check' /> OK
        </button>
      </div>
    );
  };

  const renderHistory = () => {
    if (modelDetails.history && (modelDetails.history.length > 0)) {
      return (
        <div className="col-12">
          <table className="table table-striped table-hover">
            <thead>
              <tr>
                <th>Rate</th>
                <th>Effective Date</th>
                <th>End Date</th>
                <th>Added By</th>
              </tr>
            </thead>
            <tbody>
              {renderTableBody()}
            </tbody>
          </table>
        </div>
      );
    } else {
      return (
        <div className="text-center">
          <p>No Results available </p>
        </div>
      );
    }
  };

  const renderTableBody = () => {
    return modelDetails.history.map((obj, index) =>renderTableBodyRow(obj, index));
  };

  const renderTableBodyRow = (historyRow, index) => {
    let createdBy = `${historyRow.created_by}`;
    if (historyRow.created_at) {
      createdBy = `${historyRow.created_by}  ${moment(historyRow.created_at, dateFormat.backendDateFormat).format(dateFormat.displayDateFormat)}`;
    } 
    return (
      <tr key={index}>
        <td>
          {historyRow.effective_rate}
        </td>
        <td>
          {historyRow.effective_date}
        </td>
        <td>
          {historyRow.end_date}
        </td>
        <td>
          {createdBy}
        </td>
      </tr>
    );
  };

  const renderRateTaxModal = () => {
    return (
      <Modal visible={ showRateTaxModal } close={ handleCloseTaxModal } manualOverflow={false}>
        <h4>{modelDetails.title}</h4>
        <div className='m-1'>
          <form onSubmit={() => {}}>
            <div className='row mt-4'>
              <div className='col-4 form-group'>
                <label htmlFor={`field-rate`}>* Rate </label>
                <input id={`field-rate`}
                  type='text'
                  className='form-control'
                  required={true}
                  disabled={viewOnly}
                  value={modelDetails.textValue}
                  onChange={handleEffectiveRateChange} />
              </div>
              <div className='col-4 form-group'>
                <label htmlFor={`field-effective_date`}>* Effective Date</label>
                <DatePicker
                  disabled={ viewOnly }
                  selected={ modelDetails.effectiveDate }
                  id="effective_date_dateInputBox"
                  dateFormat={ dateFormat.displayDateFormat }
                  onChange={ handleEffectiveDateChange }
                  style={{zIndex: 999999 }}
                  className="form-control date-field"/>
              </div>
            </div>
            <div className='row'>
              {renderHistory()}
            </div>
            {!viewOnly && renderButtons()}
          </form>
        </div>
      </Modal>
    );
  };

  const renderPartsTax = () => {
    const partsTax = facility.parts_tax.find(obj => obj.active);
    return (
      <div className="form-group row col-12">
        <div className="col-4">
          <label className="col-form-label col-form-label-sm">
            Parts tax:
          </label>
        </div>
        <input type="text"
          id="parts_tax_inputBox"
          readOnly={true}
          disabled={ viewOnly }
          className="form-control-plaintext col-sm-2 ml-3"
          value={ (partsTax && `${partsTax.effective_rate}%`) || '0%' }/>
        <input type="text"
          id="parts_tax_effective_date_inputBox"
          readOnly={true}
          disabled={ viewOnly }
          className="form-control-plaintext col-sm-3 ml-2"
          value={ (partsTax && partsTax.effective_date) || moment().format(dateFormat.displayDateFormat) }/>
        <button className="btn btn-secondary col-sm-2 ml-2" style={{fontSize: 'small', fontWeight: 540 }} onClick={ onPartsTaxClick }>
          {viewOnly ? `View` : `Update` }
        </button>
      </div>
    );
  };

  const renderLaborTax = () => {
    const laborTax = facility.labor_tax.find(obj => obj.active);
    return (
      <div className="form-group row col-12">
        <div className="col-4">
          <label className="col-form-label col-form-label-sm">
            Labor tax:
          </label>
        </div>
        <input type="text"
          id="labor_tax_inputBox"
          readOnly={true}
          disabled={ viewOnly }
          className="form-control-plaintext col-sm-2 ml-3"
          value={ (laborTax && `${laborTax.effective_rate}%`) || '0%' }/>
        <input type="text"
          id="labor_tax_effective_date_inputBox"
          readOnly={true}
          disabled={ viewOnly }
          className="form-control-plaintext col-sm-3 ml-2"
          value={ (laborTax && laborTax.effective_date) || moment().format(dateFormat.displayDateFormat) }/>
        <button className="btn btn-secondary col-sm-2 ml-2" style={{fontSize: 'small', fontWeight: 540 }} onClick={ onLaborTaxClick }>
          {viewOnly ? `View` : `Update` }
        </button>
      </div>
    );
  };

  const renderlaborRate = () => {
    const labor_rate = facility.labor_rate.find(obj => obj.active);
    if (!labor_rate) {
      return;
    }
    let rate = labor_rate.effective_rate;
    if(rate) {
      rate = parseFloat(rate).toFixed(2);
    }
    return (
      <div className="form-group row col-12">
        <div className="col-4">
          <label className="col-form-label col-form-label-sm">
            Labor Rate:
          </label>
        </div>
        <input type="text"
          id="labor_rate_inputBox"
          readOnly={true}
          disabled={ viewOnly }
          className="form-control-plaintext col-sm-2 ml-3"
          value={ rate }/>
        <input type="text"
          id="labor_rate_effective_date_inputBox"
          readOnly={true}
          disabled={ viewOnly }
          className="form-control-plaintext col-sm-3 ml-2"
          value={ labor_rate && labor_rate.effective_date }/>
        <button className="btn btn-secondary col-sm-2 ml-2" style={{fontSize: 'small', fontWeight: 540 }} onClick={ onLaborRateClick }>
          {viewOnly ? `View` : `Update` }
        </button>
      </div>
    );
  };

  return (
    <div>
      {renderRateTaxModal()}
      {renderlaborRate()}
      {renderPartsTax()}
      {renderLaborTax()}
    </div>
  );
};

TaxComponent.propTypes = {
  facility: PropTypes.object.isRequired,
  viewOnly: PropTypes.bool.isRequired,
  handleRatesUpdate: PropTypes.func.isRequired,
};

TaxComponent.defaultProps = {
  facility: {
    name: "",
    phone: "",
    postal_code: "",
    state_code: "",
    facility_code: "",
    labor_rate: [],
    labor_tax: [],
    parts_tax: [],
    address: "",
    city: "",
    country: "",
    fax: "",
    vendor_id: "",
    contact: "",
    is_active: true,
    ein: "",
    tax_type: "",
    payment_type: ""
  },
};