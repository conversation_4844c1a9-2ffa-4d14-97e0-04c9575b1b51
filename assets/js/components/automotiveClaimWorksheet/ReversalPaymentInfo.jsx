import React from "react";
import PropTypes from "prop-types";
import accounting from "accounting";
import moment from "moment";
import dateFormat from "../reusable/Utilities/dateFormat";

export default class ReversalPaymentInfo extends React.Component {

  static propTypes = {
    paymentInfo: PropTypes.arrayOf(PropTypes.shape({
      authorization_number: PropTypes.number,
      automotive_claim_id: PropTypes.number,
      automotive_intacct_batch_id: PropTypes.number,
      batch_key: PropTypes.number,
      bill_key: PropTypes.number,
      bill_memo: PropTypes.string,
      bill_number: PropTypes.string,
      amount: PropTypes.string,
      id: PropTypes.number,
      payment_key: PropTypes.number,
      check_details: PropTypes.arrayOf(PropTypes.shape({
        paid_date: PropTypes.string,
        check_number: PropTypes.string,
        amount: PropTypes.string
      })),
    })),
    claimObject: PropTypes.object.isRequired
  };

  renderPaymentInfo = (payment, i) => {
    return (
      <div key={payment.authorization_number}>
        {this.props.paymentInfo.length > 0 ? this.renderLine(i) : null}
        <div className="form-group row">
          <div className="col-6">
                Authorization #
          </div>
          <div className="col-6">
            {payment.authorization_number}
          </div>
        </div>
        <div className="form-group row">
          <div className="col-6">
                Bill #
          </div>
          <div className="col-6">
            {payment.bill_number}
          </div>
        </div>
        <div className="form-group row">
          <div className="col-6">
              Batch #
          </div>
          <div className="col-6">
            {payment && (payment.automotive_intacct_batch_id || "")}
          </div>
        </div>
        <div className="form-group row">
          <div className="col-6">
            Total Amount
          </div>
          <div className="col-6">
            {accounting.formatMoney(Math.abs(payment.amount), '$', 2)}
          </div>
        </div>
        <div className="form-group row">
          <div className="col-6">
                  Reason
          </div>
          <div className="col-6">
            {payment.bill_memo.replace(this.props.claimObject.contract_number + " " + this.props.claimObject.customer_name + " ", "")}
          </div>
        </div>
        {
          payment.check_details && payment.check_details.map((check_detail, index) => {
            return ([
              <div className="form-group row" key={ `check_number_${index}` }>
                <div className="col-6">
                  Check #
                </div>
                <div className="col-6">
                  {check_detail.check_number}
                </div>
              </div>,
              <div className="form-group row" key={ `amount_${index}` }>
                <div className="col-6">
                  Check Amount
                </div>
                <div className="col-6">
                  {accounting.formatMoney(Math.abs(check_detail.amount), '$', 2)}
                </div>
              </div>,
              <div className="form-group row" key={ `paid_date_${index}` }>
                <div className="col-6">
                  Paid Date
                </div>
                <div className="col-6">
                  {moment.utc(check_detail.paid_date).format(dateFormat.displayDateFormat)}
                </div>
              </div>
            ]);
          })
        }
      </div>
    );
  };

  renderLine = (index) => {
    if (index===0) {
      return (
        <h2 className='horizentalLineHeaderStyle' > <span className='horizentalLineSpanStyle'>Negative Claim</span></h2 >
      );
    }

    return (
      <hr style={{ borderTop: 2, backgroundColor: 'black', borderStyle: 'ridge' }} />
    );
  };


  render() {
    return (
      <div>
        {
          this.props.paymentInfo.map((p, i) => {
            return this.renderPaymentInfo(p, i);
          })
        }
      </div>
    );
  }
}