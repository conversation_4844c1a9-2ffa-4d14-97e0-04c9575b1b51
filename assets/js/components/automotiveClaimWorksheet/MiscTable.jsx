import React from "react";
import MiscTableRow from "./MiscTableRow.jsx";
import PropTypes from "prop-types";

const MiscTable = (props) => {

  const renderMiscRows = (miscs) => {
    if (miscs && Array.isArray(miscs) && miscs.length > 0) {
      return miscs.map((misc, index) => <MiscTableRow misc={misc}
        claimObject={props.claimObject}
        key={misc.id}
        complaintIndex={props.index}
        index={index}
        isDisabled={props.isDisabled}
        showRequiredNoteError={props.showRequiredNoteError}
        handlePartLaborUpdate={props.handlePartLaborUpdate}
        removePartOrLabor={props.removePartOrLabor} />);
    }
  };

  return (
    <div className="small mb-4">
      <table className="table table-sm">
        <thead className="thead-light">
          <tr className="row col-12 pr-0">
            <th className="col-4">
               Misc Description
            </th>
            <th className="col">
              Requested
            </th>
            <th className="col" colSpan="2">
              Approved
            </th>
          </tr>
        </thead>
        {renderMiscRows(props.miscs)}
      </table>
      <span className="text-primary cursor-pointer"
        id={`add-labor-${props.index}`}
        onClick={!props.isDisabled ? props.addPartOrLabor : void 0}>
        Add item
      </span>
    </div>
  );
};

MiscTable.propTypes = {
  miscs: PropTypes.array,
  claimObject: PropTypes.object.isRequired,
  handlePartLaborUpdate: PropTypes.func.isRequired,
  addPartOrLabor: PropTypes.func.isRequired,
  removePartOrLabor: PropTypes.func.isRequired,
  isDisabled: PropTypes.bool,
  index: PropTypes.number,
  showRequiredNoteError: PropTypes.bool
};

export default MiscTable;