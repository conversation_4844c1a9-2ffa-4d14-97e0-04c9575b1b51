import React, { useState } from "react";
import InputBox from "../reusable/InputBox/InputBox.jsx";
import If from "../reusable/If/If.jsx";
import Alert from "react-s-alert";
import Loader from "react-loader-advanced";
import dateFormat from "./../reusable/Utilities/dateFormat.js";
import ConfirmationModal from "../reusable/ConfirmationModal/ConfirmationModal.jsx";
import moment from "moment";
import DatePicker from "react-datepicker";
import PropTypes from "prop-types";

const RODetails = (props) => {
  const {
    claimObject,
    facilityDetails,
    handleOnChange,
    hasRO,
    onROUpdate,
    handleDateChange,
    isDisabled,
    manuallyEnterRO,
    handleManuallyEnterRO,
    usedRo,
  } = props;

  const [displayWarning, setDisplayWarning] = useState(false);

  const onFindROClick = () => {
    if (!claimObject.facility_id) {
      Alert.warning("Please select a facility.");
    } else if (!facilityDetails.store_id) {
      Alert.warning("Store not available for facility");
    } else if (!claimObject.ro) {
      Alert.warning("Please enter RO number.");
    } else {
      onROUpdate({ "is_ro_auto": true });
    }
  };

  const handleManualRoEntry = () => {
    handleManuallyEnterRO();
  };

  const handleROBlur = () => {
    if(usedRo && usedRo.length > 0 && claimObject.ro) {
      const exists = usedRo.find(ro => claimObject.ro === ro);
      if (exists) {
        setDisplayWarning(true);
      }
    }
  };

  const handleOnConfirm = () => {
    // We want to proceed with the duplicate ro for manager role
    setDisplayWarning(false);
  };

  const handleOnDecline = () => {
    // We will clear the values from ro
    handleOnChange('ro', '');
    setDisplayWarning(false);
  };
  
  const renderRO = () => {
    if (hasRO || manuallyEnterRO) {
      return renderRODetails(claimObject);
    } else {
      return renderGetRO(claimObject);
    }
  };

  const renderRODetails = () => {
    const date = claimObject.ro_opened_date ? moment.utc(claimObject.ro_opened_date).format(dateFormat.displayDateFormat) : moment();
    return (
      <div>
        <div className="form-group row">
          <div className="col-5">
            <label
              className="col-form-label-sm">
              Opened
            </label>
          </div>
          <span className="input-group input-group-sm col-7">
            <DatePicker
              disabled={ isDisabled }
              selected={ moment(date, dateFormat.displayDateFormat) }
              id="ro_opened_dateInputBox"
              dateFormat={ dateFormat.displayDateFormat }
              onChange={ (e) => handleDateChange('ro_opened_date', e) }
              className="form-control form-control-sm date-field"/>
          </span>
        </div>
        <div className="form-group row">
          <div className="col-5">
            <label
              className="col-form-label-sm">
              Mileage
            </label>
          </div>
          <InputBox type="Text"
            id="mileage-inputBox"
            customClass="col-7"
            isDisabled={ isDisabled }
            hasDefaultValue={ true }
            value={ claimObject.ro_mileage }
            onChange={ (e) => handleOnChange('ro_mileage', e) }
            onBlur={ (e) => handleOnChange('ro_mileage', e) }/>
        </div>
      </div>
    );
  };

  const renderGetRO = () => {
    return (
      <div>
        <If condition={ !claimObject.facility_id }>
          <p className="col-form-label-sm">Select a Facility.</p>
        </If>
        <p className="col-form-label-sm">If an RO exists enter the RO and click Find RO.</p>
        <p className="col-form-label-sm">If an RO has not yet been generated, click Enter
          Manually.</p>
        <div className="my-3">
          <button type="button"
            className="btn btn-primary"
            id="btn-find-ro"
            disabled={ isDisabled }
            onClick={ () => onFindROClick() }>
            Find RO
          </button>
          <button type="button"
            id="btn-enter-manually"
            className="btn btn-secondary ml-3"
            disabled={ isDisabled }
            onClick={ () => handleManualRoEntry() }>
            Enter Manually
          </button>
        </div>
      </div>
    );
  };

  const renderWarningModal = () => {
    return (
      <ConfirmationModal confirmButtonText="Yes"
        declineButtonText="No"
        displayConfirmationModal={ displayWarning }
        displayMessage={`Claim already exists with that RO number. Do you still want to proceed with the same RO number ?`}
        onConfirm={ handleOnConfirm }
        onDecline={ handleOnDecline }/>
    );
  };

  const spinnerMessage = <p className='text-center'><i className="fa fa-refresh fa-spin"/> Loading...</p>;
  return (
    <Loader show={ false } message={ spinnerMessage }>
      <section>
        <div className="form-group row">
          <div className="col-5">
            <label
              className="col-form-label col-form-label-sm">
              RO
            </label>
          </div>
          <InputBox type="AlphaNumeric"
            id="ro_inputBox"
            customClass="col-7"
            isDisabled={ isDisabled }
            hasDefaultValue={ true }
            value={ claimObject.ro || ''}
            onChange={ (value) => handleOnChange('ro', value) }
            onBlur={handleROBlur}/>
        </div>
        {renderRO()}
        {renderWarningModal()}
      </section>
    </Loader>
  );
};

RODetails.propTypes = {
  claimObject: PropTypes.object,
  facilityDetails: PropTypes.object,
  handleOnChange: PropTypes.func,
  hasRO: PropTypes.bool.isRequired,
  onROUpdate: PropTypes.func.isRequired,
  handleDateChange: PropTypes.func.isRequired,
  isDisabled: PropTypes.bool,
  manuallyEnterRO: PropTypes.bool,
  handleManuallyEnterRO: PropTypes.func,
  usedRo: PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.number, PropTypes.string])).isRequired,
  isManager: PropTypes.bool.isRequired,
};

export default RODetails;
