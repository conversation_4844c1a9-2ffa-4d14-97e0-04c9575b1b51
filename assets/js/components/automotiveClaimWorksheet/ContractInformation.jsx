import React from "react";
import PropTypes from "prop-types";
import moment from "moment";
import { CONSTANTS } from "../reusable/Constants/constants.js";
import InputBox from "../reusable/InputBox/InputBox.jsx";
import dateFormat from "./../reusable/Utilities/dateFormat.js";
import { formatCurrency } from "../reusable/Utilities/format.js";

export default class ContractInformation extends React.Component {

  static propTypes = {
    claimObject: PropTypes.object,
    handleOnChange: PropTypes.func,
    isDisabled: PropTypes.bool,
  };

  constructor(props) {
    super(props);
  }

  renderCoverageList = (claimObject) => {
    if (claimObject.coverage_list && claimObject.coverage_list.length > 0) {
      if (claimObject.product_code === CONSTANTS.PRODUCT_CODE_NEW_MAP.maintenance) {
        return claimObject.coverage_list.map(this.getMaintenanceCoverage);
      } else {
        return claimObject.coverage_list.map(this.getCoverage);
      }
    }
  };

  getCoverage = (coverage, index) => {
    return (
      <div className="form-group form-check row justify-content-end" key={ index }>
        <label
          className="form-check-label col-form-label col-form-label-sm col-11">
          <input type="checkbox"
            className="form-check-input"
            id="goodwill_checkBox"
            checked={ coverage.value }
            disabled="disabled"/>
          <span>{coverage.flag}</span>
        </label>
      </div>);
  };

  getMaintenanceCoverage = (coverage, index) => {
    return (
      <div className="form-group row" key={ index }>
        <div className="col-4">
          <label
            className="col-form-label col-form-label-sm">
            {coverage.flag}
          </label>
        </div>
        <div className="col-8">
          <p className="col-form-label-sm mb-0">
            {coverage.string}
          </p>
        </div>
      </div>
    );
  };

  renderMiles = (claimObject) => {
    if (claimObject.product_code === CONSTANTS.PRODUCT_CODE_NEW_MAP.key ||
      claimObject.product_code === CONSTANTS.PRODUCT_CODE_NEW_MAP.century ||
      claimObject.product_code === CONSTANTS.PRODUCT_CODE_NEW_MAP.drivePur) {
      return (
        <p className="col-form-label-sm mb-0">
          {claimObject.beginning_miles}
        </p>
      );
    } else {
      return (
        <p className="col-form-label-sm mb-0">
          {`${claimObject.beginning_miles} to ${claimObject.ending_miles}`}
        </p>
      );
    }
  };

  renderDeductible = (claimObject) => {
    if (claimObject.product_code === CONSTANTS.PRODUCT_CODE_NEW_MAP.service) {
      return (
        <div className="form-group row">
          <div className="col-4">
            <label
              className="col-form-label-sm">
              Deductible
            </label>
          </div>
          <div className="col-8">
            <p className="col-form-label-sm mb-0">
              <InputBox type="Currency"
                id={ `deductible-inputBox` }
                customClass=""
                hasDefaultValue={ true }
                isDisabled={ this.props.isDisabled }
                value={ formatCurrency(this.props.claimObject.deductible) }
                onBlur={  this.props.handleOnChange.bind(this, "deductible") }/>
            </p>
          </div>
        </div>
      );
    }
  };

  renderMaintenance = (claimObject) => {
    if (claimObject.product_code === CONSTANTS.PRODUCT_CODE_NEW_MAP.service) {
      return (
        <div className="form-group row">
          <div className="col-4">
            <label
              className="col-form-label-sm">
              Maintenance
            </label>
          </div>
          <div className="col-8">
            <p className="form-control-static mb-0">
              {`${claimObject.term} ${claimObject.contract_status}`}
            </p>
          </div>
        </div>
      );
    }
  };

  renderCoverage = () => {
    if (this.props.claimObject.product_code === CONSTANTS.PRODUCT_CODE_NEW_MAP.drivePur) {
      return;
    }
    return (
      <div className="form-group row">
        <div className="col-4">
          <label
            className="col-form-label-sm">
           Coverage
          </label>
        </div>
        <div className="col-8">
          <p className="col-form-label-sm mb-0">{this.props.claimObject.coverage}</p>
        </div>
      </div>
    );
  };

  render() {
    return (
      <section className="col no-gutters">
        <div className="form-group row">
          <div className="col-4">
            <label
              className="col-form-label-sm">
                Originating Dealership
            </label>
          </div>
          <div className="col-form-label-sm col-8">
            { this.props.claimObject.originating_dealership }
          </div>
        </div>
        <div className="form-group row">
          <div className="col-4">
            <label
              className="col-form-label-sm">
              Miles
            </label>
          </div>
          <div className="col-8">
            { this.renderMiles(this.props.claimObject) }
          </div>
        </div>
        <div className="form-group row">
          <div className="col-4">
            <label
              className="col-form-label-sm">
              Dates
            </label>
          </div>
          <div className="col-8 pr-0">
            <p className="col-form-label-sm mb-0">
              {`${moment.utc(this.props.claimObject.effective_date).format(dateFormat.displayDateFormat)} to ${moment.utc(this.props.claimObject.expiration_date).format(dateFormat.displayDateFormat)}`}
            </p>
          </div>
        </div>
        {this.renderCoverage()}
        {this.renderCoverageList(this.props.claimObject)}
        {this.renderDeductible(this.props.claimObject)}
        {this.renderMaintenance(this.props.claimObject)}
      </section>
    );
  }
}
