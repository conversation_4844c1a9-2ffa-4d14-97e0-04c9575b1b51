import React from "react";
import TowingTableRow from "./TowingTableRow.jsx";
import PropTypes from "prop-types";

const TowingTable = (props) => {

  const renderTowingRows = (towings) => {
    if (towings && Array.isArray(towings) && towings.length > 0) {
      return towings.map((towing, index) => <TowingTableRow towing={towing}
        claimObject={props.claimObject}
        key={towing.id}
        complaintIndex={props.index}
        index={index}
        isDisabled={props.isDisabled}
        showRequiredNoteError={props.showRequiredNoteError}
        handlePartLaborUpdate={props.handlePartLaborUpdate}
        removePartOrLabor={props.removePartOrLabor} />);
    }
  };
  
  return (
    <div className="small mb-4">
      <table className="table table-sm">
        <thead className="thead-light">
          <tr className="row col-12 pr-0">
            <th className="col-4">
              Towing Description
            </th>
            <th className="col">
              Requested
            </th>
            <th className="col" colSpan="2">
              Approved
            </th>
          </tr>
        </thead>
        {renderTowingRows(props.towings)}
      </table>
      <span className="text-primary cursor-pointer"
        id={`add-labor-${props.index}`}
        onClick={!props.isDisabled ? props.addPartOrLabor : void 0}>
        Add item
      </span>
    </div>
  );
};

TowingTable.propTypes = {
  towings: PropTypes.array,
  claimObject: PropTypes.object.isRequired,
  handlePartLaborUpdate: PropTypes.func.isRequired,
  addPartOrLabor: PropTypes.func.isRequired,
  removePartOrLabor: PropTypes.func.isRequired,
  isDisabled: PropTypes.bool,
  index: PropTypes.number,
  showRequiredNoteError: PropTypes.bool
};

export default TowingTable;