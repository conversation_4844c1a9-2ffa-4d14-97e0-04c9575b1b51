import React from "react";
import SelectBox from "../reusable/SelectBox/SelectBox.jsx";
import accounting from "accounting";
import PropTypes from "prop-types";
import { CONSTANTS } from "../reusable/Constants/constants.js";
import Alert from "react-s-alert";
import { json as ajax } from "./../../ajax.js";
import { URLS as apiUrls } from "./../reusable/Utilities/urls";
import UserSelectionModal from "./UserSelectionModal.jsx";
import { userHasRole } from "../reusable/Utilities/userHasRole";
import PaymentInfo from "./PaymentInfo.jsx";
import ConfirmationModal from "../reusable/ConfirmationModal/ConfirmationModal.jsx";
import Modal from "../../Modal.jsx";
import InputBox from "../reusable/InputBox/InputBox.jsx";
import VendorSelectionModal from "./../claimCheckList/BankSelectionModal.jsx";
import PayeeInformation from "./PayeeInformation";
import If from "../reusable/If/If";
import Select from "react-select";
import ReactTooltip from 'react-tooltip';
import { formatCurrency } from "../reusable/Utilities/format.js";
import AdminClaimUpdateModal from "./AdminClaimUpdate.jsx";

const optionList = {
  claimStatusManager: [
    { name: CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.open, value: CONSTANTS.AUTO_CLAIM_STATUS_MAP.open, isDisabled: false },
    { name: CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.preAuthorization, value: CONSTANTS.AUTO_CLAIM_STATUS_MAP.preAuthorization, isDisabled: false },
    { name: CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.needRentalBill, value: CONSTANTS.AUTO_CLAIM_STATUS_MAP.needRentalBill, isDisabled: false },
    { name: CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.needSubletBill, value: CONSTANTS.AUTO_CLAIM_STATUS_MAP.needSubletBill, isDisabled: false },
    { name: CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.needSMToCall, value: CONSTANTS.AUTO_CLAIM_STATUS_MAP.needSMToCall, isDisabled: false },
    { name: CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.needClosedAccountingRO, value: CONSTANTS.AUTO_CLAIM_STATUS_MAP.needClosedAccountingRO, isDisabled: false },
    { name: CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.needProofOfDeductibleReimbursement, value: CONSTANTS.AUTO_CLAIM_STATUS_MAP.needProofOfDeductibleReimbursement, isDisabled: false },
    { name: CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.waitingOnVendor, value: CONSTANTS.AUTO_CLAIM_STATUS_MAP.waitingOnVendor, isDisabled: false },
    { name: CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.payable, value: CONSTANTS.AUTO_CLAIM_STATUS_MAP.payable, isDisabled: false },
    { name: CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.returned, value: CONSTANTS.AUTO_CLAIM_STATUS_MAP.returned, isDisabled: false },
    { name: CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.approved, value: CONSTANTS.AUTO_CLAIM_STATUS_MAP.approved, isDisabled: false },
    { name: CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.authorizedCCClaim, value: CONSTANTS.AUTO_CLAIM_STATUS_MAP.authorizedCCClaim, isDisabled: false },
    { name: CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.dealerChargedBack, value: CONSTANTS.AUTO_CLAIM_STATUS_MAP.dealerChargedBack, isDisabled: false },
    { name: CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.denied, value: CONSTANTS.AUTO_CLAIM_STATUS_MAP.denied, isDisabled: false },
    { name: CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.deactivated, value: CONSTANTS.AUTO_CLAIM_STATUS_MAP.deactivated, isDisabled: false },
    { name: CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.ccPaid, value: CONSTANTS.AUTO_CLAIM_STATUS_MAP.ccPaid, isDisabled: true },
    { name: CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.waitingForCheck, value: CONSTANTS.AUTO_CLAIM_STATUS_MAP.waitingForCheck, isDisabled: true },
    { name: CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.checkWritten, value: CONSTANTS.AUTO_CLAIM_STATUS_MAP.checkWritten, isDisabled: true },
    { name: CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.reversed, value: CONSTANTS.AUTO_CLAIM_STATUS_MAP.reversed, isDisabled: true },
    { name: CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.waitingForReversed, value: CONSTANTS.AUTO_CLAIM_STATUS_MAP.waitingForReversed, isDisabled: true },
    { name: CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.adjusted, value: CONSTANTS.AUTO_CLAIM_STATUS_MAP.adjusted, isDisabled: true },
  ],
  claimStatusAgent: [
    { name: CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.open, value: CONSTANTS.AUTO_CLAIM_STATUS_MAP.open, isDisabled: false },
    { name: CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.preAuthorization, value: CONSTANTS.AUTO_CLAIM_STATUS_MAP.preAuthorization, isDisabled: false },
    { name: CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.needRentalBill, value: CONSTANTS.AUTO_CLAIM_STATUS_MAP.needRentalBill, isDisabled: false },
    { name: CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.needSubletBill, value: CONSTANTS.AUTO_CLAIM_STATUS_MAP.needSubletBill, isDisabled: false },
    { name: CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.needSMToCall, value: CONSTANTS.AUTO_CLAIM_STATUS_MAP.needSMToCall, isDisabled: false },
    { name: CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.needClosedAccountingRO, value: CONSTANTS.AUTO_CLAIM_STATUS_MAP.needClosedAccountingRO, isDisabled: false },
    { name: CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.needProofOfDeductibleReimbursement, value: CONSTANTS.AUTO_CLAIM_STATUS_MAP.needProofOfDeductibleReimbursement, isDisabled: false },
    { name: CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.waitingOnVendor, value: CONSTANTS.AUTO_CLAIM_STATUS_MAP.waitingOnVendor, isDisabled: false },
    { name: CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.payable, value: CONSTANTS.AUTO_CLAIM_STATUS_MAP.payable, isDisabled: false },
    { name: CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.denied, value: CONSTANTS.AUTO_CLAIM_STATUS_MAP.denied, isDisabled: false },
    { name: CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.deactivated, value: CONSTANTS.AUTO_CLAIM_STATUS_MAP.deactivated, isDisabled: false },
    { name: CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.returned, value: CONSTANTS.AUTO_CLAIM_STATUS_MAP.returned, isDisabled: false },
    { name: CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.approved, value: CONSTANTS.AUTO_CLAIM_STATUS_MAP.approved, isDisabled: true },
    { name: CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.authorizedCCClaim, value: CONSTANTS.AUTO_CLAIM_STATUS_MAP.authorizedCCClaim, isDisabled: true },
    { name: CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.dealerChargedBack, value: CONSTANTS.AUTO_CLAIM_STATUS_MAP.dealerChargedBack, isDisabled: true },
    { name: CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.ccPaid, value: CONSTANTS.AUTO_CLAIM_STATUS_MAP.ccPaid, isDisabled: true },
    { name: CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.waitingForCheck, value: CONSTANTS.AUTO_CLAIM_STATUS_MAP.waitingForCheck, isDisabled: true },
    { name: CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.checkWritten, value: CONSTANTS.AUTO_CLAIM_STATUS_MAP.checkWritten, isDisabled: true },
    { name: CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.reversed, value: CONSTANTS.AUTO_CLAIM_STATUS_MAP.reversed, isDisabled: true },
    { name: CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.waitingForReversed, value: CONSTANTS.AUTO_CLAIM_STATUS_MAP.waitingForReversed, isDisabled: true },
    { name: CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.adjusted, value: CONSTANTS.AUTO_CLAIM_STATUS_MAP.adjusted, isDisabled: true },
  ],
  payType: [
    { name: CONSTANTS.PAYMENT_TYPE_NAMES.DEFAULT, value: "" },
    { name: CONSTANTS.PAYMENT_TYPE_NAMES.CREDIT_CARD, value: CONSTANTS.PAYMENT_TYPE_CODES.CREDIT_CARD },
    { name: CONSTANTS.PAYMENT_TYPE_NAMES.CUSTOMER, value: CONSTANTS.PAYMENT_TYPE_CODES.CUSTOMER },
    { name: CONSTANTS.PAYMENT_TYPE_NAMES.STORE, value: CONSTANTS.PAYMENT_TYPE_CODES.STORE }
  ]
};

const returnedReasonList = [
  { value: CONSTANTS.AUTO_CLAIM_DENIED_REASON.over60days, label: CONSTANTS.AUTO_CLAIM_DENIED_REASON.over60days }
];

const deniedReasonList = [
  { name: '--Select status--', value: "" },
  { name: CONSTANTS.AUTO_CLAIM_COMPLAINTS_DENIED_REASON.expiredByTime,
    value: CONSTANTS.AUTO_CLAIM_COMPLAINTS_DENIED_REASON.expiredByTime },
  { name: CONSTANTS.AUTO_CLAIM_COMPLAINTS_DENIED_REASON.expiredByMileage,
    value: CONSTANTS.AUTO_CLAIM_COMPLAINTS_DENIED_REASON.expiredByMileage },
  { name: CONSTANTS.AUTO_CLAIM_COMPLAINTS_DENIED_REASON.failedInspectionComponent,
    value: CONSTANTS.AUTO_CLAIM_COMPLAINTS_DENIED_REASON.failedInspectionComponent },
  { name: CONSTANTS.AUTO_CLAIM_COMPLAINTS_DENIED_REASON.notCovered,
    value: CONSTANTS.AUTO_CLAIM_COMPLAINTS_DENIED_REASON.notCovered },
  { name: CONSTANTS.AUTO_CLAIM_COMPLAINTS_DENIED_REASON.notPreAuthorized,
    value: CONSTANTS.AUTO_CLAIM_COMPLAINTS_DENIED_REASON.notPreAuthorized },
  { name: CONSTANTS.AUTO_CLAIM_COMPLAINTS_DENIED_REASON.stillInFactoryWarranty,
    value: CONSTANTS.AUTO_CLAIM_COMPLAINTS_DENIED_REASON.stillInFactoryWarranty },
  { name: CONSTANTS.AUTO_CLAIM_COMPLAINTS_DENIED_REASON.underWritingPeriod30,
    value: CONSTANTS.AUTO_CLAIM_COMPLAINTS_DENIED_REASON.underWritingPeriod30 },
  { name: CONSTANTS.AUTO_CLAIM_COMPLAINTS_DENIED_REASON.underWritingPeriod60,
    value: CONSTANTS.AUTO_CLAIM_COMPLAINTS_DENIED_REASON.underWritingPeriod60 },
  { name: CONSTANTS.AUTO_CLAIM_COMPLAINTS_DENIED_REASON.underWritingPeriod90,
    value: CONSTANTS.AUTO_CLAIM_COMPLAINTS_DENIED_REASON.underWritingPeriod90 },
  { name: CONSTANTS.AUTO_CLAIM_DENIED_REASON.over60days,
    value: CONSTANTS.AUTO_CLAIM_DENIED_REASON.over60days },
  { name: CONSTANTS.AUTO_CLAIM_COMPLAINTS_DENIED_REASON.other,
    value: CONSTANTS.AUTO_CLAIM_COMPLAINTS_DENIED_REASON.other },
];

export default class ClaimSummary extends React.Component {

  ZINDEX = 10;

  static propTypes = {
    claimObject: PropTypes.object,
    facilityDetails: PropTypes.shape({
      id: PropTypes.number,
      name: PropTypes.string,
      vendor_id: PropTypes.shape({
        String: PropTypes.string
      }),
      address: PropTypes.shape({
        String: PropTypes.string
      }),
      city: PropTypes.string,
      state: PropTypes.string,
      zip: PropTypes.string
    }),
    handleOnChange: PropTypes.func,
    handleUpdate: PropTypes.func,
    printFaxCCSheet: PropTypes.func,
    updateCCClaim: PropTypes.func,
    showFaxCCSheet: PropTypes.bool,
    disableFaxCCSheet: PropTypes.bool,
    showInvoiceSent: PropTypes.bool,
    isDisabled: PropTypes.bool,
    manuallyEnterTax: PropTypes.bool,
    setManuallyEnterTax: PropTypes.func,
    setShowRequiredNoteError: PropTypes.func,
    initialStatus: PropTypes.string,
    contractStatus: PropTypes.string,
    showLoader: PropTypes.func,
    hideLoader: PropTypes.func,
    loadFacilityDetails: PropTypes.func,
    handleReasonNoteUpdate: PropTypes.func,
    statusNoteText: PropTypes.string,
    isDifferentOwner: PropTypes.bool,
    coverageDetails: PropTypes.array,
    loadClaim: PropTypes.func,
    usedRo: PropTypes.bool,
    user: PropTypes.shape({
      id: PropTypes.number.isRequired,
      first_name: PropTypes.string.isRequired,
      last_name: PropTypes.string.isRequired,
      email: PropTypes.string.isRequired,
      stores: PropTypes.arrayOf(PropTypes.string.isRequired),
      roles: PropTypes.shape({
        Map: PropTypes.object,
      }).isRequired,
      approval_limit: PropTypes.string,
    }),
    attachments: PropTypes.array,
  };

  constructor(props) {
    super(props);
    this.state = {
      userList: [],
      displayUserSelectionModal: false,
      showUserListLoader: false,
      displayNoVendorConfirmationModal: false,
      displayVendorSelectionModal: false,
      displayNegativeClaimModal: false,
      displayPayeeWarningModal: false,
      displayClaimAdjustmentModal: false,
      negativeAmount: "0.00",
      adjustmentAmount: "0.00",
      negativeReason: '',
      adjustmentReason: '',
      status: '',
      statusValue: '',
      showWarningMessageNote: false,
      warningMessageNote: (props.claimObject && props.claimObject.warning_message_note) || '',
      showUsedRoAgentWarning: false,
      showUsedRoManagerWarning: false,
      blockApprovalOnUsedRo: false,
      showAdminClaimUpdateModal: false,
    };
  }

  getClaimUserList = () => {
    this.setState({ showUserListLoader: true }, function () {
      ajax(apiUrls.userList, {}, {}, (data, status) => {
        if (status === 200) {
          this.setState({ userList: data.users.filter(user => userHasRole(user, CONSTANTS.USER_ROLES.autoClaimsManager)), showUserListLoader: false });
        } else {
          this.setState({ showUserListLoader: false });
          Alert.error("Click the browser's Refresh button to reload the user list. If the error continues, contact your system administrator.");
        }
      });
    });
  };

  calculatePartsLaborTotal = (type) => {
    let total = 0;
    if (this.props.claimObject.complaints && this.props.claimObject.complaints.length > 0) {
      for (let complaintIndex = 0; complaintIndex < this.props.claimObject.complaints.length; complaintIndex++) {
        if (this.props.claimObject.complaints[complaintIndex]['status'] === 'Payable' && this.props.claimObject.complaints[complaintIndex][type] && this.props.claimObject.complaints[complaintIndex][type].length > 0) {
          for (let index = 0; index < this.props.claimObject.complaints[complaintIndex][type].length; index++) {
            total += this.props.claimObject.complaints[complaintIndex][type][index].approved ? parseFloat(this.props.claimObject.complaints[complaintIndex][type][index].approved) : 0;
          }
        }
      }
      return total;
    } else { // no complaints, this is store entered claim
      if (type === 'labors') {
        return this.props.claimObject.total_labor;
      } else if (type === 'parts') {
        return this.props.claimObject.total_parts;
      } else if (type === 'rentals') {
        return this.props.claimObject.total_rentals;
      } else if (type === 'sublets') {
        return this.props.claimObject.total_sublets;
      } else if (type === 'miscs') {
        return this.props.claimObject.total_miscs;
      } else if (type === 'towings') {
        return this.props.claimObject.total_towings;
      }
    }
    return 0;
  };

  calculateOtherTotal = (type) => {
    let total = 0;
    if (this.props.claimObject.complaints && this.props.claimObject.complaints.length > 0) {
      for (let complaintIndex = 0; complaintIndex < this.props.claimObject.complaints.length; complaintIndex++) {

        if (!(type === 'goodwill_amount' && !this.props.claimObject.complaints[complaintIndex]['goodwill_flag']) &&
          this.props.claimObject.complaints[complaintIndex]['status'] === 'Payable'
          && this.props.claimObject.complaints[complaintIndex][type]
          && this.props.claimObject.complaints[complaintIndex][type].length > 0) {

          for (let index = 0; index < this.props.claimObject.complaints[complaintIndex][type].length; index++) {
            total += this.props.claimObject.complaints[complaintIndex][type][index].approved ? parseFloat(this.props.claimObject.complaints[complaintIndex][type][index].approved) : 0;
          }
        }
      }
      return total;
    } else { // no complaints, this is store entered claim
      if (type === 'rentals') {
        return this.props.claimObject.total_rentals;
      } else if (type === 'sublets') {
        return this.props.claimObject.total_sublets;
      } else if (type === 'miscs') {
        return this.props.claimObject.total_miscs;
      } else if (type === 'towings') {
        return this.props.claimObject.total_towings;
      }
    }
    return 0;
  };

  handleStatusReasonChange = (value) => {
    if (this.props.claimObject.status !== CONSTANTS.AUTO_CLAIM_STATUS_MAP.denied) {
      Alert.warning("Please enter valid status.");
      return;
    }

    this.props.handleOnChange('canceled_reason', value);
  };

  handleStatusOtherReasonChange = (value) => {
    if (this.props.claimObject.canceled_reason !== CONSTANTS.AUTO_CLAIM_COMPLAINTS_DENIED_REASON.other) {
      Alert.warning("Please enter valid status reason.");
      return;
    }

    this.props.handleOnChange('canceled_other_reason', value);
  };

  calculateGoodwillAmountTotal = (type) => {
    let total = 0;
    if (this.props.claimObject.complaints && this.props.claimObject.complaints.length > 0) {
      for (let complaintIndex = 0; complaintIndex < this.props.claimObject.complaints.length; complaintIndex++) {
        if (!(type === 'goodwill_amount' && !this.props.claimObject.complaints[complaintIndex]['goodwill_flag']) && this.props.claimObject.complaints[complaintIndex]['status'] === 'Payable') {
          total += this.props.claimObject.complaints[complaintIndex][type] ? parseFloat(this.props.claimObject.complaints[complaintIndex][type]) : 0;
        }
      }
      return total;
    }
    return 0;
  };

  calculateRequestedPartsTotal = () => {
    const {
      claimObject
    } = this.props;

    let total = 0;
    if (claimObject.complaints && claimObject.complaints.length > 0) {
      for (let complaintIndex = 0; complaintIndex < claimObject.complaints.length; complaintIndex++) {
        if ((claimObject.complaints[complaintIndex]['status'] === 'Payable' || claimObject.complaints[complaintIndex]['status'] === 'Denied') && claimObject.complaints[complaintIndex]['parts'] && claimObject.complaints[complaintIndex]['parts'].length > 0) {
          for (let index = 0; index < claimObject.complaints[complaintIndex]['parts'].length; index++) {
            total += claimObject.complaints[complaintIndex]['parts'][index].requested ? parseFloat(claimObject.complaints[complaintIndex]['parts'][index].requested) : 0;
          }
        }
      }
      return total;
    }
    return 0;
  };

  calculateRequestedLaborsTotal = () => {
    const {
      claimObject
    } = this.props;

    let total = 0;
    if (claimObject.complaints && claimObject.complaints.length > 0) {
      for (let complaintIndex = 0; complaintIndex < claimObject.complaints.length; complaintIndex++) {
        if ((claimObject.complaints[complaintIndex]['status'] === 'Payable' || claimObject.complaints[complaintIndex]['status'] === 'Denied') && claimObject.complaints[complaintIndex]['labors'] && claimObject.complaints[complaintIndex]['labors'].length > 0) {
          for (let index = 0; index < claimObject.complaints[complaintIndex]['labors'].length; index++) {
            total += claimObject.complaints[complaintIndex]['labors'][index].billed ? parseFloat(claimObject.complaints[complaintIndex]['labors'][index].billed) : 0;
          }
        }
      }
      return total;
    }
    return 0;
  };

  calculateRequestedOtherTotal = (type) => {
    const {
      claimObject
    } = this.props;

    let total = 0;
    if (claimObject.complaints && claimObject.complaints.length > 0) {
      for (let complaintIndex = 0; complaintIndex < claimObject.complaints.length; complaintIndex++) {

        if (!(type === 'goodwill_amount' && !this.props.claimObject.complaints[complaintIndex]['goodwill_flag']) &&
          (claimObject.complaints[complaintIndex]['status'] === 'Payable' || claimObject.complaints[complaintIndex]['status'] === 'Denied')
          && claimObject.complaints[complaintIndex][type] && claimObject.complaints[complaintIndex][type].length > 0) {

          for (let index = 0; index < claimObject.complaints[complaintIndex][type].length; index++) {
            total += claimObject.complaints[complaintIndex][type][index].requested ? parseFloat(claimObject.complaints[complaintIndex][type][index].requested) : 0;
          }
        }
      }
      return total;
    }
    return 0;
  };

  calculateGrandTotal = (totalTax) => {
    if (this.props.claimObject.complaints && this.props.claimObject.complaints.length == 0) {
      return this.props.claimObject.estimate;
    }
    let total = 0;
    let parsedTotalTax = parseFloat(totalTax) || 0;
    total += parseFloat(this.calculatePartsLaborTotal('parts')) +
      parseFloat(this.calculatePartsLaborTotal('labors')) +
      parseFloat(this.calculateOtherTotal('towings')) +
      parseFloat(this.calculateOtherTotal('rentals')) +
      parseFloat(this.calculateOtherTotal('sublets')) +
      parseFloat(this.calculateOtherTotal('miscs')) +
      parsedTotalTax;

    if (this.props.claimObject.product_code === CONSTANTS.PRODUCT_CODE_NEW_MAP.service) {
      total -= parseFloat(this.props.claimObject.deductible);
    }

    return total || this.props.claimObject.estimate;
  };

  calculateRequestedGrandTotal = (totalTax) => {
    let total = 0;
    let parsedTotalTax = parseFloat(totalTax) || 0;
    total += parseFloat(this.calculateRequestedPartsTotal()) +
      parseFloat(this.calculateRequestedLaborsTotal()) +
      parseFloat(this.calculateRequestedOtherTotal('towings')) +
      parseFloat(this.calculateRequestedOtherTotal('rentals')) +
      parseFloat(this.calculateRequestedOtherTotal('sublets')) +
      parseFloat(this.calculateRequestedOtherTotal('miscs')) +
      parsedTotalTax;

    if (this.props.claimObject.product_code === CONSTANTS.PRODUCT_CODE_NEW_MAP.service && !this.props.claimObject.deductible_collect) {
      total -= parseFloat(this.props.claimObject.deductible);
    }

    return total || this.props.claimObject.requested_total;
  };

  hasProcessedComplaints = () => {
    if (this.props.claimObject.complaints && this.props.claimObject.complaints.length > 0) {
      for (let complaintIndex = 0; complaintIndex < this.props.claimObject.complaints.length; complaintIndex++) {
        if (!(this.props.claimObject.complaints[complaintIndex]['status'] === 'Payable' || this.props.claimObject.complaints[complaintIndex]['status'] === 'Denied')) {
          return false;
        }
      }
    }
    return true;
  };

  checkNoteEmpty = () => {
    if (this.props.claimObject.complaints && this.props.claimObject.complaints.length > 0) {
      for (let complaint of this.props.claimObject.complaints) {
        for (let labor of complaint.labors) {
          if (parseFloat(labor.billed) > parseFloat(labor.approved) && !labor.notes) {
            Alert.warning(`Approved amount is less than billed amount, please add note for it.`);
            this.props.setShowRequiredNoteError(true);
            return true;
          }
        }

        for (let part of complaint.parts) {
          if (parseFloat(part.requested) > parseFloat(part.approved) && !part.notes) {
            Alert.warning(`Approved amount is less than requested amount, please add note for it.`);
            this.props.setShowRequiredNoteError(true);
            return true;
          }
        }
        
        if (complaint.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.denied &&  !complaint.status_reason) {
          Alert.warning(`Reason is required for denied status.`);
          this.props.setShowRequiredNoteError(true);
          return true;
        }
        if (complaint.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.denied && 
          complaint.status_reason === CONSTANTS.AUTO_CLAIM_COMPLAINTS_DENIED_REASON.other && 
          !complaint.status_other_reason) {
          Alert.warning(`Denial note is required for other reason.`);
          this.props.setShowRequiredNoteError(true);
          return true;
        }
      }
      return false;
    }
    return false;
  };


  handleNonCoveringConfirmation = () => {
    const {
      statusValue,
    } = this.state;

    if (this.checkNoteEmpty()) {
      return;
    }

    this.setState({
      status: statusValue,
    }, () => {
      if (this.props.claimObject.pay_type !== "" && this.props.claimObject.pay_type !== CONSTANTS.PAYMENT_TYPE_CODES.STORE) {
        this.setState({
          displayPayeeWarningModal: true
        });
      } else {
        this.validationForStatus();
      }
    });
  }

  renderRoAgentWarningModal = () => {
    return (
      <ConfirmationModal confirmButtonText="Ok"
        displayConfirmationModal={this.state.showUsedRoAgentWarning}
        displayMessage={`Claim already exists with that RO number. Claim must be assigned to manager for approval.`}
        onConfirm={this.handleOnConfirmUsedRoAgent} />
    );
  };

  handleOnConfirmUsedRoAgent = () => {
    this.setState({ showUsedRoAgentWarning: false });

    if (this.checkNoteEmpty()) {
      return;
    }

    this.setState({
      status: this.state.statusValue,
    }, () => {
      if (this.props.claimObject.pay_type !== "" && this.props.claimObject.pay_type !== CONSTANTS.PAYMENT_TYPE_CODES.STORE) {
        this.setState({
          displayPayeeWarningModal: true
        });
      } else {
        this.validationForStatus();
      }
    });
  };

  renderRoManagerWarningModal = () => {
    return (
      <ConfirmationModal confirmButtonText="Ok"
        declineButtonText="No"
        displayConfirmationModal={this.state.showUsedRoManagerWarning}
        displayMessage={`Claim already exists with that RO number. Do you still want to proceed with Approval ?`}
        onConfirm={this.handleOnConfirmUsedRoManager}
        onDecline={this.handleOnDeclineUsedRoManager} />
    );
  };

  handleOnConfirmUsedRoManager = () => {
    this.setState({
      status: this.state.statusValue,
      showUsedRoManagerWarning: false,
    }, () => {
      if (this.props.claimObject.pay_type !== "" && this.props.claimObject.pay_type !== CONSTANTS.PAYMENT_TYPE_CODES.STORE) {
        this.setState({
          displayPayeeWarningModal: true
        });
      } else {
        this.validationForStatus();
      }
      this.props.handleOnChange('status', this.state.statusValue);
    });
  }

  handleOnDeclineUsedRoManager = () => this.setState({ showUsedRoManagerWarning: false, blockApprovalOnUsedRo: true });

  closeMismatchWarningModal = () => this.setState({ showWarningMessageNote: false, statusValue: '', warningMessageNote: '' });

  renderMismatchWarningNotes = () => {
    const { showWarningMessageNote } = this.state;
    return (
      <ConfirmationModal confirmButtonText="Add Note"
        declineButtonText="Cancel"
        displayConfirmationModal={showWarningMessageNote}
        displayMessage="Customer/VIN details mismatch on the claim, please add a note."
        renderTemplate={this.renderWarningNotesText}
        type={'node'}
        onConfirm={this.handleMismatchWarningNotes}
        onDecline={this.closeMismatchWarningModal} />
    );
  };

  renderWarningNotesText = () => {
    const { warningMessageNote } = this.state;
    return (
      <div className="col-8 offset-2">
        <textarea type="text"
          className="form-control"
          rows="5"
          value={warningMessageNote}
          onChange={this.handleNoteOnChange} />
      </div>
    );
  }

  handleNoteOnChange = (e) => {
    this.setState({ warningMessageNote : e.target.value });
  }

  handleMismatchWarningNotes = () => {
    const {
      statusValue,
      warningMessageNote,
    } = this.state;

    if (this.checkNoteEmpty()) {
      return;
    }
    this.props.handleOnChange('warning_message_note', warningMessageNote);

    this.setState({
      status: statusValue,
      showWarningMessageNote: false,
    }, () => {
      if (this.props.claimObject.pay_type !== "" && this.props.claimObject.pay_type !== CONSTANTS.PAYMENT_TYPE_CODES.STORE) {
        this.setState({
          displayPayeeWarningModal: true
        });
      } else {
        this.validationForStatus();
      }
    });
  }

  handleStatusChange = (value) => {
    if (value === CONSTANTS.AUTO_CLAIM_STATUS_MAP.payable || value === CONSTANTS.AUTO_CLAIM_STATUS_MAP.approved || value === CONSTANTS.AUTO_CLAIM_STATUS_MAP.invoiceSent) {
      const {
        claimObject: {
          is_ro_customer_valid,
          is_ro_contract_vin_match,
          is_ro_contract_mileage_valid,
        },
        initialStatus,
      } = this.props;

      if ((userHasRole(this.props.user, CONSTANTS.USER_ROLES.autoClaims)
        && !userHasRole(this.props.user, CONSTANTS.USER_ROLES.autoClaimsManager))
        && value === CONSTANTS.AUTO_CLAIM_STATUS_MAP.payable && this.props.usedRo) {
        this.setState({
          showUsedRoAgentWarning: this.props.usedRo,
          statusValue: value,
        });
        return;
      }

      if (userHasRole(this.props.user, CONSTANTS.USER_ROLES.autoClaimsManager)
        && (value === CONSTANTS.AUTO_CLAIM_STATUS_MAP.payable || value === CONSTANTS.AUTO_CLAIM_STATUS_MAP.approved)
        && this.props.usedRo
      ) {
        this.setState({
          showUsedRoManagerWarning: this.props.usedRo,
          statusValue: value,
        });
        return;
      }

      if ((is_ro_customer_valid === "false" ||
        is_ro_contract_vin_match === "false" ||
        is_ro_contract_mileage_valid === "false") &&
        this.state.warningMessageNote === '' &&
        initialStatus !== CONSTANTS.AUTO_CLAIM_STATUS_MAP.payable) {
        this.setState({
          showWarningMessageNote: true,
          statusValue: value,
        });
        return;
      }

      if (this.checkNoteEmpty()) {
        return;
      }

      this.setState({
        status: value
      }, () => {
        if (this.props.claimObject.pay_type !== "" && this.props.claimObject.pay_type !== CONSTANTS.PAYMENT_TYPE_CODES.STORE) {
          this.setState({
            displayPayeeWarningModal: true
          });
        } else {
          this.validationForStatus();
        }
      });
    } else {
      this.props.handleOnChange('status', value);
    }
  };

  handleOwnerChange = (e) => {
    this.props.handleOnChange('owner_id', e.target.value);
  };

  getStatusOptions = () => {
    let statusOptionList = [];
    if (userHasRole(this.props.user, CONSTANTS.USER_ROLES.autoClaims) && !userHasRole(this.props.user, CONSTANTS.USER_ROLES.autoClaimsManager)) {
      statusOptionList = optionList.claimStatusAgent;
    } else if (userHasRole(this.props.user, CONSTANTS.USER_ROLES.autoClaimsManager) ||
    userHasRole(this.props.user, CONSTANTS.USER_ROLES.accountingClaimHandler) ||
    userHasRole(this.props.user, CONSTANTS.USER_ROLES.viewOnlyClaims)) {
      statusOptionList = optionList.claimStatusManager;
    }
    
    if (this.props.claimObject.pay_type === CONSTANTS.PAYMENT_TYPE_CODES.CREDIT_CARD) {
      const index = statusOptionList.findIndex((element) => element.value === CONSTANTS.AUTO_CLAIM_STATUS_MAP.approved);
      if (index !== -1) {
        statusOptionList.splice(index, 1, {
          name: CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.invoiceSent,
          value: CONSTANTS.AUTO_CLAIM_STATUS_MAP.invoiceSent,
          isDisabled: true,
        });
      }
    } else {
      const index = statusOptionList.findIndex((element) => element.value === CONSTANTS.AUTO_CLAIM_STATUS_MAP.invoiceSent);
      if (index !== -1) {
        statusOptionList.splice(index, 1, {
          name: CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.approved,
          value: CONSTANTS.AUTO_CLAIM_STATUS_MAP.approved
        });
      }
    }

    return statusOptionList;
  };

  renderTaxNote = () => {
    let totalTax = ((parseFloat(this.calculatePartsLaborTotal('parts')) * (this.props.claimObject.tax_parts / 100)) + (parseFloat(this.calculatePartsLaborTotal('labors')) * (this.props.claimObject.tax_labor / 100))).toFixed(2);
    if (this.props.manuallyEnterTax && parseFloat(this.props.claimObject.total_tax) != totalTax) {
      return (
        <div className="form-group row col-12 border-top-0">
          <span className="text-danger">Calculated total tax is ${totalTax}</span>
        </div>
      );
    }
  };

  onVendorSelect = (data) => {
    let dataToUpdate = {};
    dataToUpdate.id = this.props.facilityDetails.id;
    dataToUpdate.vendor_id = data.vendor_id;
    this.setState({ showFacilityLoader: true, displayVendorSelectionModal: false }, () => {
      ajax(apiUrls.facilityVendorUpdate, dataToUpdate, { method: 'PUT' }, (data, status) => {
        if (status === 200) {
          Alert.success("Updated Vendor successfully");
          this.setState({ showFacilityLoader: false }, () => {
            this.props.loadFacilityDetails(this.props.facilityDetails.id, true);
          });
        } else {
          this.setState({ showFacilityLoader: false });
          Alert.error("Click the browser's Refresh button to reload the user list. If the error continues, contact your system administrator.");
        }
      });
    });
  };

  addVendor = () => {
    if (this.props.isDisabled && !userHasRole(this.props.user, CONSTANTS.USER_ROLES.accounting)) {
      return;
    }
    return (
      <button className="btn btn-primary" onClick={() => { this.setState({ displayVendorSelectionModal: true }); }}>
        Add Vendor Number
      </button>
    );
  };

  changeTotalTax = (value) => {
    this.props.setManuallyEnterTax(true);
    this.props.handleOnChange('total_tax', value);
  };

  submitClaimAdjustment = () => {
    const {
      adjustmentAmount,
      adjustmentReason,
    } = this.state;

    if (!adjustmentReason) {
      Alert.warning(`Claim adjustment reason can't be empty.`);
      return;
    }

    const data = {
      id: this.props.claimObject.id,
      adjustment_amount: adjustmentAmount,
      reason: adjustmentReason,
    };

    this.showClaimAdjustmentModal(false);
    this.props.showLoader();
    ajax(apiUrls.automotiveAdjust, data, { method: 'POST' }, (data, status) => {
      this.props.hideLoader();
      if (status === 200) {
        Alert.success("Claim adjustment successful");
        this.props.loadClaim(() => {
          this.paymentInfo.getPaymentDetails();
        });
      } else {
        Alert.error("Click the browser's Refresh button to reload the user list. If the error continues, contact your system administrator.");
      }
    });
  }

  submitNegativeClaim = () => {
    const estimate = parseFloat(this.props.claimObject.estimate);
    const reversePaymentInfo = this.paymentInfo.getReversePaymentDetail() || [];
    const negativeValue = parseFloat(this.state.negativeAmount);

    const totalReverse = reversePaymentInfo.reduce((total, payment) => {
      return total + parseFloat(payment.amount);
    }, 0);

    if (totalReverse + negativeValue > estimate) {
      Alert.warning(`Can't reverse payment more than approved.`);
      return;
    }

    if (!this.state.negativeReason) {
      Alert.warning(`Claim reversal reason can't be empty.`);
      return;
    }

    const data = {
      id: this.props.claimObject.id,
      negative_amount: parseFloat(this.state.negativeAmount),
      reason: this.state.negativeReason
    };

    this.showNegativeClaimModal(false);
    this.props.showLoader();
    ajax(apiUrls.automotiveReverse, data, { method: 'POST' }, (data, status) => {
      this.props.hideLoader();
      if (status === 200) {
        Alert.success("Claim Reversal successful");
        this.props.loadClaim(() => {
          this.paymentInfo.getPaymentDetails();
        });
      } else {
        Alert.error("Click the browser's Refresh button to reload the user list. If the error continues, contact your system administrator.");
      }
    });
  };

  changeNegativeAmount = (value) => {
    this.setState({
      negativeAmount: value
    });
  };

  changeAdjustmentAmount = (value) => this.setState({ adjustmentAmount: value });

  changeNegativeReason = (event) => {
    this.setState({
      negativeReason: event.target.value
    });
  };

  changeAdjustmentReason = (e) => this.setState({ adjustmentReason: e.target.value });

  showNegativeClaimModal = (displayNegativeClaimModal) => {
    this.setState({
      displayNegativeClaimModal
    });
  };

  showClaimAdjustmentModal = (displayClaimAdjustmentModal) => {
    this.setState({
      displayClaimAdjustmentModal: displayClaimAdjustmentModal
    });
  };

  closePayeeWarningModal = () => {
    this.setState({
      displayPayeeWarningModal: false
    });
  };

  positivePayeeWarningModal = () => {
    this.closePayeeWarningModal();
    this.validationForStatus();
  };

  validationForStatus = () => {
    const {
      status,
    } = this.state;

    const {
      initialStatus,
      contractStatus,
      user,
      claimObject,
    } = this.props;

    // If we are updating status from non-approved to approved and user is not 
    // manager and claim is in non active status then we should block the update with warning message
    if (status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.payable
      && initialStatus !== 'Payable' && contractStatus !== 'Active'
      && !userHasRole(user, CONSTANTS.USER_ROLES.autoClaimsManager)
      && parseFloat(claimObject.requested_total) < parseFloat(user.approval_limit)) {
      Alert.warning('Contract is in non active status. Contact your manager.');
      return;
    }

    if (status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.payable) {
      if (((this.props.facilityDetails && this.props.facilityDetails.vendor_id &&
        this.props.facilityDetails.vendor_id.String === "")
         || (this.props.claimObject.pay_type === CONSTANTS.PAYMENT_TYPE_CODES.CUSTOMER && this.customerPayee.getVendor() === ""))
         && this.props.claimObject.pay_type !== CONSTANTS.PAYMENT_TYPE_CODES.CREDIT_CARD) {
        this.setState({ displayNoVendorConfirmationModal: true });
      } else {
        this.props.handleOnChange('status', status);
        this.getClaimUserList();
        this.setState({ displayUserSelectionModal: true });
      }
    } else if (status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.approved || status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.invoiceSent) {
      if (this.props.claimObject.complaints && this.props.claimObject.complaints.length > 0) {
        for (let complaint of this.props.claimObject.complaints) {
          for (let labor of complaint.labors) {
            if (parseFloat(labor.billed) < parseFloat(labor.approved)) {
              Alert.warning(`Approved amount is greater than billed amount, please correct it.`);
              return;
            }
          }

          for (let part of complaint.parts) {
            if (parseFloat(part.requested) < parseFloat(part.approved)) {
              Alert.warning(`Approved amount is greater than requested amount, please correct it.`);
              return;
            }
          }
        }
      }
      switch (this.props.claimObject.pay_type) {
      case CONSTANTS.PAYMENT_TYPE_CODES.CUSTOMER:
        if (this.customerPayee.getVendor() === "") {
          Alert.warning('Before claim can be updated to Approved status, you need to provide customer vendor. Please add vendor to continue');
        } else {
          this.props.handleOnChange('status', status);
        }
        break;
      default:
        if ((this.props.facilityDetails.vendor_id && this.props.facilityDetails.vendor_id.String === "") || !this.props.facilityDetails.vendor_id) {
          Alert.warning('Before claim can be updated to Approved status, you need to provide facility vendor. Please add vendor to continue');
        } else {
          this.props.handleOnChange('status', status);
        }
      }
    }
  };

  renderNegativeClaimModal = () => {
    return (
      <Modal visible={this.state.displayNegativeClaimModal}
        size="medium"
        title="Negative Claim"
        close={this.showNegativeClaimModal.bind(this, false)}>
        <div className="row justify-content-md-center">
          <div className="col-8">
            <div className="row my-2">
              <label className="col-4">
                Amount:
              </label>
              <div className="col-8">
                <InputBox type="Currency"
                  id="negative-amount-inputBox"
                  customClass=""
                  hasDefaultValue={true}
                  value={this.state.negativeAmount}
                  onBlur={this.changeNegativeAmount}
                />
              </div>
            </div>
            <div className="row my-2">
              <label className="col-2">
                Reason:
              </label>
            </div>
            <div className="row my-2">
              <div className="col-12">
                <textarea type="text"
                  id="negative-text-area"
                  className="form-control"
                  rows="5"
                  value={this.state.negativeReason}
                  onChange={this.changeNegativeReason} />
              </div>
            </div>
            <div className="row my-4 justify-content-center">
              <button className="btn btn-secondary mr-3"
                onClick={this.showNegativeClaimModal.bind(this, false)}>
                Cancel
              </button>
              <button className="btn btn-primary"
                onClick={this.submitNegativeClaim}>
                Submit
              </button>
            </div>
          </div>
        </div>
      </Modal>
    );
  };

  renderClaimAdjustmentModal = () => {
    const {
      displayClaimAdjustmentModal,
      adjustmentReason,
      adjustmentAmount,
    } = this.state;

    return (
      <Modal visible={displayClaimAdjustmentModal}
        size="medium"
        title="Claim Adjustment"
        close={this.showClaimAdjustmentModal.bind(this, false)}>
        <div className="row justify-content-md-center">
          <div className="col-8">
            <div className="row my-2">
              <label className="col-4">
                Amount:
              </label>
              <div className="col-8">
                <InputBox type="Currency"
                  id="adjustment-amount-inputBox"
                  customClass=""
                  hasDefaultValue={true}
                  value={adjustmentAmount}
                  onBlur={this.changeAdjustmentAmount}
                />
              </div>
            </div>
            <div className="row my-2">
              <label className="col-2">
                Reason:
              </label>
            </div>
            <div className="row my-2">
              <div className="col-12">
                <textarea type="text"
                  id="negative-text-area"
                  className="form-control"
                  rows="5"
                  value={adjustmentReason}
                  onChange={this.changeAdjustmentReason} />
              </div>
            </div>
            <div className="row my-4 justify-content-center">
              <button className="btn btn-secondary mr-3"
                onClick={this.showClaimAdjustmentModal.bind(this, false)}>
                Cancel
              </button>
              <button className="btn btn-primary"
                onClick={this.submitClaimAdjustment}>
                Submit
              </button>
            </div>
          </div>
        </div>
      </Modal>
    );
  };

  renderFacilityDetails = () => {
    if (!this.props.facilityDetails) {
      return;
    }
    const longTextStyle = {
      "maxWidth": "180px",
      "overflow": "hidden",
      "textOverflow": "ellipsis",
      "whiteSpace": "nowrap"
    };
    return (
      <section className="col text-sm-left">
        <div className="d-flex">
          <span style={longTextStyle} title={this.props.claimObject.facility_name}>{this.props.claimObject.facility_name}</span>
        </div>
        <p className="mb-0 col-form-label-sm">
          {this.props.claimObject.facility_address}
        </p>
        <p className="mb-0 col-form-label-sm">
          {`${this.props.claimObject.facility_city}, ${this.props.claimObject.facility_state_code} ${this.props.claimObject.facility_postal_code} ${this.props.claimObject.facility_country}`}
        </p>
        <div className="d-flex">
          {this.props.facilityDetails.vendor_id && (this.props.facilityDetails.vendor_id.String ?
            `Vendor # ${this.props.facilityDetails.vendor_id.String}` : this.addVendor())}
        </div>
        <VendorSelectionModal selectBankDetails={this.onVendorSelect}
          closeBankSelectionModal={() => {
            this.setState({ displayVendorSelectionModal: false });
          }}
          displayBankSelectionModal={this.state.displayVendorSelectionModal} />
      </section>
    );
  };

  renderWarningPayeeNotStore = () => {
    return (
      <Modal visible={this.state.displayPayeeWarningModal}
        size="medium"
        title="Warning"
        close={this.closePayeeWarningModal}>
        <p>Pay type is {CONSTANTS.PAYMENT_TYPE_NAME_MAP[this.props.claimObject.pay_type]}. Are you sure you want to continue?</p>
        <div className="row my-2 justify-content-center">
          <button className="btn btn-secondary mr-3"
            onClick={this.closePayeeWarningModal}>
            No
          </button>
          <button className="btn btn-primary"
            onClick={this.positivePayeeWarningModal}>
            Yes
          </button>
        </div>
      </Modal>
    );
  };

  handleDeductableCollect = (v, totalTax) => {
    this.props.handleOnChange('deductible_collect', v);
    this.props.handleOnChange.bind('requested_total', this.calculateRequestedGrandTotal(totalTax));
  }

  onAdminClaimUpdate = () => {
    this.setState({
      showAdminClaimUpdateModal: true,
    });
  }

  closeAdminClaimUpdateModal = () => {
    this.setState({
      showAdminClaimUpdateModal: false,
    });
  }

  renderAdminClaimUpdateModal = (p) => {
    return (<AdminClaimUpdateModal
      show = {this.state.showAdminClaimUpdateModal}
      closeFunc = {this.closeAdminClaimUpdateModal.bind(this)}
      reloadClaimFunc = {this.props.loadClaim.bind(this)}
      reloadPaymentFunc = {this.paymentInfo.getPaymentDetails.bind(this)}
      claimId = {this.props.claimObject.id}
      status={this.props.claimObject.status}
      totalClaimPaid={this.calculateGrandTotal(this.props.claimObject.total_tax)}
    />);
  };
  

  render() {
    let totalTax = formatCurrency(this.props.claimObject.total_tax);
    //If user editing it can enter empty string that's why totalTax initialize to ""
    //Priority is given to total_tax in claimObject
    if (!this.props.manuallyEnterTax && this.props.claimObject.complaints && this.props.claimObject.complaints.length > 0) {
      totalTax = ((parseFloat(this.calculatePartsLaborTotal('parts')) * (this.props.claimObject.tax_parts / 100)) + (parseFloat(this.calculatePartsLaborTotal('labors')) * (this.props.claimObject.tax_labor / 100)));
      totalTax = formatCurrency(totalTax);
    }

    let canceledReason;

    if (this.props.claimObject.canceled_reason) {
      canceledReason = {
        "label": this.props.claimObject.canceled_reason,
        "value": this.props.claimObject.canceled_reason
      };
    }
    let adminClaimUpdate = false;
    if (userHasRole(this.props.user, CONSTANTS.USER_ROLES.accountingClaimAdmin)) {
      switch (this.props.claimObject.status) {
      case CONSTANTS.AUTO_CLAIM_STATUS_MAP.waitingForReversed:
      case CONSTANTS.AUTO_CLAIM_STATUS_MAP.waitingForCheck:
      case CONSTANTS.AUTO_CLAIM_STATUS_MAP.checkWritten:
      case CONSTANTS.AUTO_CLAIM_STATUS_MAP.ccPaid:
        adminClaimUpdate = true;
        break;
      }
    }

    const { initialStatus } = this.props;

    return (
      <div className="col mt-3">
        <h6 className="row">Claim Summary</h6>
        <div className="row p-2">
          <div className="col-4">
            <div className="form-group row">
              <div className="col-4">
                Pay Type
              </div>
              <div className="col-8">
                <SelectBox
                  id="pay-type-dropdown"
                  value={this.props.claimObject.pay_type}
                  disabled={this.props.isDisabled}
                  onChange={this.props.handleOnChange.bind(null, 'pay_type')}
                  customClassName="form-control-sm"
                  optionsList={optionList.payType} />
                <If condition={this.props.claimObject.pay_type === CONSTANTS.PAYMENT_TYPE_CODES.CUSTOMER}>
                  <PayeeInformation
                    claimObject={this.props.claimObject}
                    isDisabled={this.props.isDisabled}
                    showLoader={this.props.showLoader}
                    hideLoader={this.props.hideLoader}
                    ref={(customerPayee) => {
                      this.customerPayee = customerPayee;
                    }} />
                </If>
                <If condition={this.props.claimObject.pay_type === CONSTANTS.PAYMENT_TYPE_CODES.STORE || this.props.claimObject.pay_type === CONSTANTS.PAYMENT_TYPE_CODES.CREDIT_CARD}>
                  {this.renderFacilityDetails()}
                </If>
                {
                  this.props.showFaxCCSheet &&
                  (<div>
                    <button className="mt-5 btn btn-secondary"
                      onClick={this.props.printFaxCCSheet}
                      disabled={initialStatus === CONSTANTS.AUTO_CLAIM_STATUS_MAP.invoiceSent || initialStatus === CONSTANTS.AUTO_CLAIM_STATUS_MAP.ccPaid || this.props.disableFaxCCSheet}>
                      Print Fax CC Sheet
                    </button>
                  </div>
                  )
                }
                {this.props.showInvoiceSent && (this.props.attachments && this.props.attachments.length > 0) &&
                  (
                    <button className="mt-1 btn btn-secondary"
                      onClick={this.props.updateCCClaim}
                      disabled={initialStatus === CONSTANTS.AUTO_CLAIM_STATUS_MAP.invoiceSent || initialStatus === CONSTANTS.AUTO_CLAIM_STATUS_MAP.ccPaid}>
                      Invoice Sent
                    </button>
                  )
                }
              </div>
            </div>
          </div>
          <div className="col-4">
            <div className="form-group row">
              <div className="col-6">
                Parts
              </div>
              <div className="col-6">
                {accounting.formatMoney(this.calculatePartsLaborTotal('parts'), '$', 2)}
              </div>
            </div>
            <div className="form-group row">
              <div className="col-6">
                Labor
              </div>
              <div className="col-6">
                {accounting.formatMoney(this.calculatePartsLaborTotal('labors'), '$', 2)}
              </div>
            </div>
            <div className="form-group row">
              <div className="col-6">
                Towing
              </div>
              <div className="col-6">
                {accounting.formatMoney(this.calculatePartsLaborTotal('towings'), '$', 2)}
              </div>
            </div>
            <div className="form-group row">
              <div className="col-6">
                Rental
              </div>
              <div className="col-6">
                {accounting.formatMoney(this.calculatePartsLaborTotal('rentals'), '$', 2)}
              </div>
            </div>
            <div className="form-group row">
              <div className="col-6">
                Sublet
              </div>
              <div className="col-6">
                {accounting.formatMoney(this.calculatePartsLaborTotal('sublets'), '$', 2)}
              </div>
            </div>
            <div className="form-group row">
              <div className="col-6">
                Misc
              </div>
              <div className="col-6">
                {accounting.formatMoney(this.calculatePartsLaborTotal('miscs'), '$', 2)}
              </div>
            </div>
            <div className="form-group row">
              <div className="col-6">
                Tax
              </div>
              <div className="col-6">
                <InputBox type="Currency"
                  id={`total-tax-inputBox`}
                  customClass=""
                  hasDefaultValue={true}
                  isDisabled={this.props.isDisabled}
                  value={totalTax}
                  onBlur={this.changeTotalTax}
                />
              </div>
            </div>
            {this.renderTaxNote()}
            <div className="form-group row">
              <div className="col-6">
                Exception
              </div>
              <div className="col-6">
                {accounting.formatMoney(this.calculateGoodwillAmountTotal('goodwill_amount'), '$', 2)}
              </div>
            </div>
            <If condition={this.props.claimObject.product_code === CONSTANTS.PRODUCT_CODE_NEW_MAP.service}>
              <div>
                <div className="form-group row">
                  <div className="col-6">
                    Deductible
                  </div>
                  <div className="col-6">
                    {accounting.formatMoney(this.props.claimObject.deductible, '$', 2)}
                  </div>
                </div>
                <div className="form-group row">
                  <div className="col-8">
                    <input type="checkbox"
                      className=""
                      id="deductible_collect"
                      checked={this.props.claimObject.deductible_collect}
                      onChange={(e) => this.handleDeductableCollect(e.target.checked, totalTax)}
                      disabled={this.props.isDisabled} />
                    <label className="pl-1"> Deductible not collected </label>
                  </div>
                </div>
              </div>
            </If>
            <div className="form-group row">
              <div className="col-6">
                Requested Total
              </div>
              <div className="col-6">
                <InputBox type="Currency"
                  id={`requested-total-inputBox`}
                  customClass=""
                  hasDefaultValue={ true }
                  isDisabled={ true }
                  value={ formatCurrency(this.calculateRequestedGrandTotal(totalTax)) }
                />
              </div>
            </div>
            <div className="form-group row">
              <div className="col-6">
                Total Claim Paid
              </div>
              <div className="col-6">
                {accounting.formatMoney(this.calculateGrandTotal(totalTax), '$', 2)}
              </div>
            </div>
            <div className="form-group row">
              <div className="col-6">
                Payment Adjustment
              </div>
              <div className="col-6">
                {accounting.formatMoney(this.props.claimObject.payment_adjustment, '$', 2)}
              </div>
            </div>
            <div className="form-group row">
              <div className="col-6">
                Actual Paid Amount
              </div>
              <div className="col-6">
                {accounting.formatMoney(
                  (this.props.claimObject.actual_paid_amount === undefined || parseFloat(this.props.claimObject.actual_paid_amount)===0 ? (this.calculateGrandTotal(totalTax)) +
                  parseFloat(this.props.claimObject.payment_adjustment):this.props.claimObject.actual_paid_amount), '$', 2)}
              </div>
            </div>
            {parseFloat(this.props.claimObject.adjustments) !== 0 ? (<div className="form-group row">
              <div className="col-6">
                Adjustments
              </div>
              <div className="col-6">
                {accounting.formatMoney(this.props.claimObject.adjustments, '$', 2)}
              </div>
            </div>) : null}
            {parseFloat(this.props.claimObject.grand_total_amount) !== 0 ? (<div className="form-group row">
              <div className="col-6">
                Adjusted Total
              </div>
              <div className="col-6">
                {accounting.formatMoney(this.props.claimObject.grand_total_amount, '$', 2)}
              </div>
            </div>) : null}
          </div>
          <div className="col-4">
            <div className="form-group row">
              <div className="col-4">
                Status
              </div>
              <div className="col-6">
                <SelectBox
                  id="status-dropdown"
                  value={this.props.claimObject.status}
                  disabled={this.props.isDisabled}
                  onChange={this.handleStatusChange}
                  customClassName="form-control-sm"
                  optionsList={this.getStatusOptions()} />
              </div>
              <div className="col-2">
                <button className="btn btn-secondary"
                  style={{fontSize: 'small', fontWeight: 540 }}
                  onClick={()=>{this.onAdminClaimUpdate();}}
                  disabled={!adminClaimUpdate}
                >
                  <i className="fa fa-edit" />
                </button>  
              </div>
            </div>
            <If condition={
              this.props.claimObject.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.returned
            }>
              <div className="form-group row">
                <div className="col-5">
                  Reason
                </div>
                <div className="col-7" data-tip data-for="canceled-reason">
                  <Select.Creatable value={canceledReason}
                    id={`reason-inputbox`}
                    disabled={this.props.isDisabled}
                    options={returnedReasonList}
                    onChange={(objReason) => {
                      this.props.handleOnChange("canceled_reason", objReason.value);
                    }}
                    menuContainerStyle={{ zIndex: this.ZINDEX }}
                    placeholder="Type or select reason"
                    tabSelectsValue={false}
                    matchProp='label' />
                  <ReactTooltip id={`canceled-reason`} aria-haspopup='true'>
                    <p className="text-center">{canceledReason && canceledReason.value}</p>
                  </ReactTooltip>
                </div>
              </div>
            </If>
            {this.props.claimObject.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.denied && (
              <div>
                <div className="form-group row">
                  <div className="col-5">
                Reason
                  </div>
                  <div className="col-7">
                    <SelectBox
                      id="reason-inputbox"
                      disabled={this.props.isDisabled}
                      customClassName="form-control-sm"
                      value={this.props.claimObject.canceled_reason  || ''}
                      onChange={this.handleStatusReasonChange}
                      optionsList={deniedReasonList}/>
                  </div>
                </div>
                {!this.props.claimObject.canceled_reason && (
                  <div className="row justify-content-end">
                    <div className="col-7 text-danger text-sm-left">Reason is required</div>
                  </div>)}
              </div>
            )}
            {this.props.claimObject.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.denied &&
            this.props.claimObject.canceled_reason === CONSTANTS.AUTO_CLAIM_COMPLAINTS_DENIED_REASON.other &&
            (<div>
              <div className="form-group row">
                <div className="col-5">
                Denial Note
                </div>
                <div className="col-7">
                  <InputBox type="Text"
                    id="canceled-other-reason"
                    className="form-control"
                    isDisabled={this.props.isDisabled}
                    value={this.props.claimObject.canceled_other_reason || ''}
                    onChange={this.handleStatusOtherReasonChange} />
                </div>
              </div>
              {!this.props.claimObject.canceled_other_reason && (
                <div className="row justify-content-end">
                  <div className="col-7 text-danger text-sm-left">Denial note is required</div>
                </div>)}
            </div>)}
            <PaymentInfo claimObject={this.props.claimObject}
              ref={(paymentInfo) => { this.paymentInfo = paymentInfo; }}
              initialStatus={this.props.initialStatus} 
              user={this.props.user}/>
            <div className="form-group row">
              <div className="col-6">
                <If condition={
                  (this.props.claimObject.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.waitingForCheck ||
                    this.props.claimObject.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.checkWritten ||
                    this.props.claimObject.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.reversed ||
                    this.props.claimObject.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.waitingForReversed ||
                    this.props.claimObject.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.adjusted) &&
                  this.props.claimObject.pay_type !== CONSTANTS.PAYMENT_TYPE_CODES.CREDIT_CARD &&
                  userHasRole(this.props.user, CONSTANTS.USER_ROLES.autoClaimsManager)
                }>
                  <div className="form-group row">
                    <div className="col-12">
                      <button className="btn btn-secondary cursor-pointer"
                        id="btn-negative-claim"
                        disabled={this.props.isDifferentOwner || (this.props.claimObject.status !== CONSTANTS.AUTO_CLAIM_STATUS_MAP.checkWritten)}
                        onClick={this.showNegativeClaimModal.bind(this, true)}>
                        Negative Claim
                      </button>
                    </div>
                  </div>
                </If>
              </div>
              <div className="col-6">
                <If condition={
                  (this.props.claimObject.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.waitingForCheck ||
                    this.props.claimObject.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.checkWritten ||
                    this.props.claimObject.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.reversed ||
                    this.props.claimObject.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.waitingForReversed ||
                    this.props.claimObject.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.adjusted) &&
                  this.props.claimObject.pay_type !== CONSTANTS.PAYMENT_TYPE_CODES.CREDIT_CARD &&
                  this.props.claimObject.product_code === 'MNT' &&
                  userHasRole(this.props.user, CONSTANTS.USER_ROLES.autoClaimsManager)
                }>
                  <div className="form-group row">
                    <div className="col-12">
                      <button className="btn btn-secondary cursor-pointer"
                        id="btn-negative-claim"
                        disabled={this.props.isDifferentOwner || (this.props.claimObject.status !== CONSTANTS.AUTO_CLAIM_STATUS_MAP.checkWritten)}
                        onClick={this.showClaimAdjustmentModal.bind(this, true)}>
                        Claim Adjustment
                      </button>
                    </div>
                  </div>
                </If>
              </div>
            </div>
          </div>
        </div>
        <UserSelectionModal userList={this.state.userList}
          displayModal={this.state.displayUserSelectionModal}
          showUserListLoader={this.state.showUserListLoader}
          handleModalSubmit={() => {
            this.setState({
              displayUserSelectionModal: false,
              showUserListLoader: false
            }, () => {
              this.props.handleUpdate();
            });
          }}
          claimObject={this.props.claimObject}
          handleOwnerChange={this.handleOwnerChange} />
        <ConfirmationModal confirmButtonText="OK"
          displayConfirmationModal={this.state.displayNoVendorConfirmationModal}
          displayMessage="Vendor is missing for given facility."
          onConfirm={() => {
            this.setState({ displayUserSelectionModal: true, displayNoVendorConfirmationModal: false }, () => {
              this.props.handleOnChange('status', CONSTANTS.AUTO_CLAIM_STATUS_MAP.payable);
              this.getClaimUserList();
            });
          }} />
        {this.state.showWarningMessageNote && this.renderMismatchWarningNotes()}
        {this.renderNegativeClaimModal()}
        {this.renderClaimAdjustmentModal()}
        {this.renderWarningPayeeNotStore()}
        {this.state.showUsedRoAgentWarning && this.renderRoAgentWarningModal()}
        {this.state.showUsedRoManagerWarning && this.renderRoManagerWarningModal()}
        {this.state.showAdminClaimUpdateModal && this.renderAdminClaimUpdateModal()}
      </div >
    );
  }
}
