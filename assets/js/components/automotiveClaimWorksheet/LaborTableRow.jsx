import React from "react";
import InputBox from "../reusable/InputBox/InputBox.jsx";
import If from "../reusable/If/If.jsx";
import PropTypes from "prop-types";
import Select from "react-select";
import { CONSTANTS } from "../reusable/Constants/constants";
import { formatCurrency } from "../reusable/Utilities/format.js";

export default class LaborTableRow extends React.Component {

    ZINDEX = 10;

  static propTypes = {
    labor: PropTypes.object,
    claimObject: PropTypes.object.isRequired,
    index: PropTypes.number,
    complaintIndex: PropTypes.number,
    handlePartLaborUpdate: PropTypes.func.isRequired,
    removePartOrLabor: PropTypes.func.isRequired,
    isDisabled: PropTypes.bool,
    showRequiredNoteError: PropTypes.bool
  };

  constructor(props) {
    super(props);
  }

  componentDidUpdate(prevProps, prevState){
    if(this.props.claimObject.labor_rate !== prevProps.claimObject.labor_rate){
      const approvedValue = parseFloat(this.getApprovedAmount(this.props.claimObject, this.props.labor));
      this.props.handlePartLaborUpdate('labors', this.props.labor.id, 'approved', approvedValue);
    }
  }

  onBlur = (field, value) => {
    let approvedAmount = parseFloat(this.props.labor.approved),
      billedAmount = parseFloat(this.props.labor.billed);

    if (field === 'hours') {
      approvedAmount = parseFloat(this.getApprovedAmount(this.props.claimObject, this.props.labor));
      this.props.handlePartLaborUpdate('labors', this.props.labor.id, 'approved', approvedAmount.toFixed(2));
    } else if(field === 'requested'){
      billedAmount = this.getBilledAmount(this.props.claimObject, this.props.labor);
      this.props.handlePartLaborUpdate('labors', this.props.labor.id, 'billed', billedAmount.toFixed(2));
    } else if(field === 'approved') {
      approvedAmount = parseFloat(value);
      this.props.handlePartLaborUpdate('labors', this.props.labor.id, field, value);
    } else if(field === 'billed') {
      billedAmount = parseFloat(value);
      this.props.handlePartLaborUpdate('labors', this.props.labor.id, field, value);
    }
  };

  getApprovedAmount = ({ labor_rate }, { hours }) => {
    if(labor_rate && hours) {
      return (parseFloat(labor_rate) * parseFloat(hours)).toFixed(2);
    } else {
      return 0;
    }
  };

  getBilledAmount = ({ labor_rate },{ requested }) => {
    if(labor_rate && requested){
      return (parseFloat(labor_rate) * parseFloat(requested));
    } else {
      return 0;
    }
  };

  getOptions = () => {
    return CONSTANTS.LABOR_PAID_SHORT.map((element) => {
      return {
        value: element,
        label: element
      };
    });
  };

  onLaborNoteChange = (laborObject) => {
    if(laborObject){
      this.props.handlePartLaborUpdate('labors', this.props.labor.id, 'notes', laborObject.value);
    } else {
      this.props.handlePartLaborUpdate('labors', this.props.labor.id, 'notes', '');
    }
  };

  render() {
    const billed = parseFloat(this.props.labor.billed);
    const approved = parseFloat(this.props.labor.approved);
    let borderStyle = {};

    if(billed < approved){
      borderStyle = {
        "border": "1px solid #dc3545",
        "borderRadius": "0.2rem"
      };
    }

    let selectedNoteOption;
    const note = this.props.labor.notes;

    if(note){
      selectedNoteOption = {
        label: note,
        value: note
      };
    }

    return (
      <tbody className="border-top-0">
        <tr className="row col-12 pr-0">
          <td className="col-4 border-bottom-0">
            <InputBox type="Text"
              autoExpand={ true }
              id={ `complaint-${this.props.complaintIndex}-labor-description-inputBox-${this.props.index}` }
              isDisabled={ this.props.isDisabled }
              value={ this.props.labor.labor_description }
              onChange={ this.props.handlePartLaborUpdate.bind(null, 'labors', this.props.labor.id, 'labor_description') }/>
          </td>
          <td className="col border-bottom-0">
            <InputBox type="Number"
              hasDefaultValue={ true }
              id={ `complaint-${this.props.complaintIndex}-requested-inputBox-${this.props.index}` }
              isDisabled={ this.props.isDisabled }
              value={ this.props.labor.requested }
              onChange={ this.props.handlePartLaborUpdate.bind(null, 'labors', this.props.labor.id, 'requested') }
              onBlur={ this.onBlur.bind(null, 'requested') }/>
          </td>
          <td className="col border-bottom-0">
            <InputBox type="Number"
              hasDefaultValue={ true }
              id={ `complaint-${this.props.complaintIndex}-hours-inputBox-${this.props.index}` }
              isDisabled={ this.props.isDisabled }
              value={ this.props.labor.hours }
              onChange={ this.props.handlePartLaborUpdate.bind(null, 'labors', this.props.labor.id, 'hours') }
              onBlur={ this.onBlur.bind(null, 'hours') }/>
          </td>
          <td className="col border-bottom-0">
            <InputBox type="Currency"
              id={ `complaint-${this.props.complaintIndex}-labor-rate-inputBox-${this.props.index}` }
              isDisabled={ true }
              value={ formatCurrency(this.props.claimObject.labor_rate) }/>
          </td>
          <td className="col border-bottom-0">
            <InputBox type="Currency"
              style={ borderStyle }
              id={ `complaint-${this.props.complaintIndex}-billed-inputBox-${this.props.index}` }
              isDisabled={ this.props.isDisabled }
              value={ formatCurrency(this.props.labor.billed) }
              onBlur={ this.onBlur.bind(null, 'billed') }/>
          </td>
          <td className="col border-bottom-0">
            <InputBox type="Currency"
              style={ borderStyle }
              hasDefaultValue={ true }
              id={ `complaint-${this.props.complaintIndex}-approved-inputBox-${this.props.index}` }
              isDisabled={ this.props.isDisabled }
              value={ formatCurrency(this.props.labor.approved) }
              onBlur={ this.onBlur.bind(null, 'approved') }/>
          </td>
          <td className="h5">
            <i className="fa fa-times cursor-pointer"
              id={ `complaint-${this.props.complaintIndex}-delete-labor-${this.props.index}` }
              onClick={ !this.props.isDisabled ? this.props.removePartOrLabor.bind(null, this.props.labor.id) : () => {}}/>
          </td>
        </tr>
        <If condition={ billed > approved }>
          <tr className="row col-12">
            <td className="col border-top-0">
              <Select.Creatable value={ selectedNoteOption }
                id={ `complaint-${this.props.complaintIndex}-labor-note-inputBox-${this.props.index}` }
                disabled={ this.props.isDisabled }
                options={ this.getOptions() }
                onChange={ this.onLaborNoteChange }
                menuContainerStyle={ { zIndex: this.ZINDEX } }
                placeholder="Type or select reason"
                tabSelectsValue={ false }
                matchProp='label'/>
              <If condition={ this.props.showRequiredNoteError && !this.props.labor.notes }>
                <span className="text-danger">Required Note for approved amount less than billed amount.</span>
              </If>
            </td>
          </tr>
        </If>
        <If condition={ billed < approved }>
          <tr className="row col-12">
            <td className="col border-top-0 text-right">
              <span className="text-danger">Approved amount is greater than billed amount.</span>
            </td>
          </tr>
        </If>
      </tbody>
    );
  }
}