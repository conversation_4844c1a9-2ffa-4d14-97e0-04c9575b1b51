import React, { useEffect, useState } from 'react';
import Loader from 'react-loader-advanced';
import PropTypes from 'prop-types';
import moment from "moment";
import DatePicker from "react-datepicker";

import InputBox from "../reusable/InputBox/InputBox.jsx";
import SelectBox from "../reusable/SelectBox/SelectBox.jsx";
import StateList from "../reusable/StatesList/StateList.jsx";
import PhoneNumber from "../reusable/PhoneNumber/PhoneNumber.jsx";
import Email from "../reusable/EmailBox/EmailBox.jsx";
import { CONSTANTS } from "./../reusable/Constants/constants.js";
import { userHasRole } from "./../reusable/Utilities/userHasRole";
import CountryList from "../reusable/CountryList/CountryList";
import dateFormat from "./../reusable/Utilities/dateFormat.js";

import Modal from "../../Modal.jsx";

const optionList = [
  { name: CONSTANTS.PAYMENT_TYPE_NAMES.DEFAULT, value: "" },
  { name: CONSTANTS.PAYMENT_TYPE_NAMES.CREDIT_CARD, value: CONSTANTS.PAYMENT_TYPE_CODES.CREDIT_CARD },
  { name: CONSTANTS.PAYMENT_TYPE_NAMES.CUSTOMER, value: CONSTANTS.PAYMENT_TYPE_CODES.CUSTOMER },
  { name: CONSTANTS.PAYMENT_TYPE_NAMES.STORE, value: CONSTANTS.PAYMENT_TYPE_CODES.STORE }
];

const ModelType = Object.freeze({
  LABORRATE:   "LR",
  LABORTAX:  "LT",
  PARTSTAX: "PT",
});


const NewFacility = (props) => {
  const { facility, onChange } = props;
  const isManagerRole = userHasRole(props.user, CONSTANTS.USER_ROLES.autoClaimsManager);

  const handleTaxModalUpdates = (modelDetails, handleCloseTaxModal) => {
    let name = "labor_rate";
    let value = {
      facility_code: facility.facility_code,
      effective_rate: modelDetails.textValue,
      effective_date: modelDetails.effectiveDate.format('YYYY-MM-DD'),
      rate_type: ModelType.LABORRATE,
      is_new: true,
      active: true
    };
    if (modelDetails.modelType === ModelType.LABORTAX) {
      value = {...value, rate_type: ModelType.LABORTAX };
      name = "labor_tax";
    } else if (modelDetails.modelType === ModelType.PARTSTAX) {
      value = {...value, rate_type: ModelType.PARTSTAX };
      name = "parts_tax";
    }
    onChange(name, [value]);
    handleCloseTaxModal();
  };

  useEffect(() => {

  },[true]);

  return (
    <Loader show={ props.showLoader } message={ props.loaderMessage }>
      <span><strong>New Repair Facility</strong></span>
      <div className="col-12 row justify-content-center mt-4">
        <div className="col-6">
          <div className="form-group row col-12">
            <div className="col-4">
              <label className="col-form-label col-form-label-sm">
                <span>*</span>Facility Name:
              </label>
            </div>
            <InputBox type="Text"
              id="facility_inputBox"
              customClass="col-8"
              value={ facility.name }
              onChange={ (e) => onChange('name', e) }/>
          </div>
          <div className="form-group row col-12">
            <div className="col-4">
              <label className="col-form-label col-form-label-sm">
                <span>*</span>Facility code:
              </label>
            </div>
            <InputBox type="Text"
              id="code_inputBox"
              customClass="col-8"
              value={ facility.facility_code }
              onChange={ (e) => onChange('facility_code', e) }/>
          </div>
          <div className="form-group row col-12">
            <div className="col-4">
              <label className="col-form-label col-form-label-sm">
                        Active:
              </label>
            </div>
            <div className="col-1 ml-4">
              <input type="checkbox"
                className="form-check-input"
                id="active_checkBox"
                data-toggle="toggle"
                disabled={ !isManagerRole }
                checked={ facility.is_active }
                onChange={ (e) => props.onCursorToggle('is_active', e) }/>
            </div>
          </div>
          <div className="form-group row col-12">
            <div className="col-4">
              <label className="col-form-label col-form-label-sm">
                <span>*</span>Country:
              </label>
            </div>
            <div className="col-8">
              <CountryList id="country_selectBox"
                customClassName="form-control-sm"
                value={ facility.country }
                onChange={ (e) => onChange('country', e) }/>
            </div>
          </div>
          <div className="form-group row col-12">
            <div className="col-4">
              <label className="col-form-label col-form-label-sm">
                <span>*</span>Address:
              </label>
            </div>
            <InputBox type="Text"
              id="address_inputBox"
              customClass="col-8"
              value={ facility.address }
              onChange={ (e) => onChange('address', e) }/>
          </div>
          <div className="form-group row col-12">
            <div className="col-4">
              <label className="col-form-label col-form-label-sm">
                <span>*</span>City:
              </label>
            </div>
            <InputBox type="Text"
              id="city_inputBox"
              customClass="col-8"
              value={ facility.city }
              onChange={ (e) => onChange('city', e) }/>
          </div>
          <div className="form-group row col-12">
            <div className="col-4">
              <label className="col-form-label col-form-label-sm">
                <span>*</span>State/Province:
              </label>
            </div>
            <div className="col-8">
              <StateList defaultText="Select State"
                value={ facility.state_code }
                country={ facility.country }
                customClassName="form-control-sm"
                onChange={ (e) => onChange('state_code', e) }/>
            </div>
          </div>
          <div className="form-group row col-12">
            <div className="col-4">
              <label className="col-form-label col-form-label-sm">
                <span>*</span>ZIP:
              </label>
            </div>
            <InputBox placeholder="Zip"
              type="Text"
              customClass="col-8"
              value={ facility.postal_code }
              onChange={ (e) => onChange('postal_code', e) }/>
          </div>
          <div className="form-group row col-12">
            <div className="col-4">
              <label className="col-form-label col-form-label-sm">
                <span>*</span>Phone:
              </label>
            </div>
            <div className="col-8">
              <PhoneNumber placeholder="PhoneNumber"
                customClassName=""
                value={ facility.phone }
                onChange={ (e) => onChange('phone', e) }/>
            </div>
          </div>
          <div className="form-group row col-12">
            <div className="col-4">
              <label className="col-form-label col-form-label-sm">
                              Fax:
              </label>
            </div>
            <InputBox type="Text"
              id="fax_inputBox"
              customClass="col-8"
              value={ facility.fax }
              onChange={ (e) => onChange('fax', e) }/>
          </div>
          <div className="form-group row col-12">
            <div className="col-4">
              <label className="col-form-label col-form-label-sm">
                              Email:
              </label>
            </div>
            <div className="col-8">
              <Email placeholder="Email"
                customClassName=""
                value={ facility.email }
                onChange={ (e) => onChange('email', e) }/>
            </div>
          </div>
        </div>
        <div className="col-6">
          <div className="form-group row col-12">
            <div className="col-4">
              <label className="col-form-label col-form-label-sm">
                        Vendor #:
              </label>
            </div>
            <InputBox type="Text"
              id="vendor_inputBox"
              customClass="col-8"
              value={ facility.vendor_id }
              onChange={ (e) => onChange('vendor_id', e) }/>
          </div>
          <div className="form-group row col-12">
            <div className="col-4">
              <label className="col-form-label col-form-label-sm">
                <span>*</span>Advisor:
              </label>
            </div>
            <InputBox type="Text"
              id="contact_inputBox"
              customClass="col-8"
              value={ facility.contact }
              onChange={ (e) => onChange('contact', e) }/>
          </div>
          <div className="form-group row col-12">
            <div className="col-4">
              <label className="col-form-label col-form-label-sm">
                        EIN #:
              </label>
            </div>
            <InputBox type="Text"
              id="ein_inputBox"
              customClass="col-8"
              value={ facility.ein }
              onChange={ (e) => onChange('ein', e) }/>
          </div>
          <div className="form-group row col-12">
            <div className="col-4">
              <label className="col-form-label col-form-label-sm">
                        Tax Type:
              </label>
            </div>
            <InputBox type="AlphaNumeric"
              id="tax_type_inputBox"
              customClass="col-8"
              value={ facility.tax_type }
              onChange={ (e) => onChange('tax_type', e) }/>
          </div>
          <TaxComponent facility={facility} handleRatesUpdate={handleTaxModalUpdates} />
          <div className="form-group row col-12">
            <div className="col-4">
              <label className="col-form-label col-form-label-sm">
                <span>*</span>Payment type:
              </label>
            </div>
            <div className="col-8">
              <SelectBox customClassName="form-control-sm"
                value={ facility.payment_type }
                onChange={ (e) => onChange('payment_type', e) }
                optionsList={ optionList }/>
            </div>
          </div>
          <div className="form-group row col-12">
            <div className="col-4">
              <label
                className="form-check form-check-label col-form-label col-form-label-sm">
                <input type="checkbox"
                  className="form-check-input"
                  id="addLine_checkBox"
                  data-toggle="toggle"
                  checked={ facility.pre_auth }
                  onChange={ (e) => props.onCursorToggle('pre_auth', e) }/>
                <span>Pre auth</span>
              </label>
            </div>
            <InputBox type="Currency"
              id="pre_auth_limit_inputBox"
              customClass="col-8"
              value={ facility.pre_auth_limit }
              onChange={ (e) => onChange('pre_auth_limit', e) }
              onBlur={ (e) => onChange('pre_auth_limit', e) }/>
          </div>
        </div>
        <div className="row col-12 justify-content-end">
          <button className="btn btn-secondary mr-2" onClick={ props.onNoActionCallBack }>
                          Cancel
          </button>
          <button className="btn btn-primary"
            onClick={ props.onActionCallBack }
            disabled={ !props.isActionEnabled }>
                          Save
          </button>
        </div>

      </div>
    </Loader>
  );
};

NewFacility.propTypes ={
  facility: PropTypes.object.isRequired,
  onChange: PropTypes.func.isRequired,
  showLoader: PropTypes.bool,
  loaderMessage: PropTypes.element,
  isActionEnabled: PropTypes.bool,
  onActionCallBack: PropTypes.func.isRequired,
  onNoActionCallBack: PropTypes.func.isRequired,
  onCursorToggle: PropTypes.func.isRequired,
  user: PropTypes.shape({
    id: PropTypes.number.isRequired,
    first_name: PropTypes.string.isRequired,
    last_name: PropTypes.string.isRequired,
    email: PropTypes.string.isRequired,
    stores: PropTypes.arrayOf(PropTypes.string.isRequired),
    roles: PropTypes.shape({
      Map: PropTypes.object,
    }).isRequired
  }),
};

NewFacility.defaultProps = {
  isActionEnabled: true,
};

export default NewFacility;

const TaxComponent = ({ facility, handleRatesUpdate }) => {
  const [showRateTaxModal, setShowRateTaxModal] = useState(false);
  const [modelDetails, setModalDetails] = useState(() => initialModalDetails());
  const [dirty, setDirty] = useState(false);

  function initialModalDetails () {
    return {
      modelType: ModelType.LABORRATE,
      title: '',
      history: [],
      effectiveDate: '',
      textValue: '',
    };
  }

  const onLaborRateClick = (e) => {
    e.preventDefault();
    handleShowTaxModal(ModelType.LABORRATE, "Labor Rate Update", facility.labor_rate || []);
  };

  const onPartsTaxClick = (e) => {
    e.preventDefault();
    handleShowTaxModal(ModelType.PARTSTAX, "Parts Tax Update", facility.parts_tax || []);
  };

  const onLaborTaxClick = (e) => {
    e.preventDefault();
    handleShowTaxModal(ModelType.LABORTAX, "Labor Tax Update", facility.labor_tax || []);
  };

  const handleShowTaxModal = (modelType, title, history) => {
    setShowRateTaxModal(true);
    const rateObj = history.find(obj => obj.active);
    if (rateObj) {
      setModalDetails({
        modelType,
        title,
        history: history,
        effectiveDate: moment(rateObj.effective_date) || moment(),
        textValue: rateObj.effective_rate,
      });
    } else {
      setModalDetails({
        modelType,
        title,
        history: history,
        effectiveDate: moment(),
        textValue: '',
      });
    }
  };

  const handleCloseTaxModal = () =>  {
    setShowRateTaxModal(false);
    setDirty(false);
  };

  const handleEffectiveRateChange = (e) => {
    setModalDetails({...modelDetails, textValue: e.target.value});
    setDirty(true);
  };

  const handleEffectiveDateChange = (e) => {
    setModalDetails({...modelDetails, effectiveDate: e});
    setDirty(true);
  };

  const renderButtons = () => {
    return (
      <div className='float-right my-3'>
        <button type='button'
          onClick={handleCloseTaxModal}
          className={`btn btn-secondary`}
        >
          <i className='fa fa-ban' /> Cancel
        </button>

        &nbsp;
        <button type='button'
          className={`btn btn-primary`}
          disabled={!dirty}
          onClick={() => handleRatesUpdate(modelDetails, handleCloseTaxModal)}
        >
          <i className='fa fa-check' /> OK
        </button>
      </div>
    );
  };

  const renderHistory = () => {
    if (modelDetails.history && (modelDetails.history.length > 0)) {
      return (
        <div className="col-12">
          <table className="table table-striped table-hover">
            <thead>
              <tr>
                <th>Rate</th>
                <th>Effective Date</th>
                <th>End Date</th>
                <th>Added By</th>
              </tr>
            </thead>
            <tbody>
              {renderTableBody()}
            </tbody>
          </table>
        </div>
      );
    } else {
      return (
        <div className="text-center">
          <p>No Results available </p>
        </div>
      );
    }
  };

  const renderTableBody = () => {
    return modelDetails.history.map((obj, index) =>renderTableBodyRow(obj, index));
  };

  const renderTableBodyRow = (historyRow, index) => {
    let createdBy = `${historyRow.created_by}`;
    if (historyRow.created_at) {
      createdBy = `${historyRow.created_by}  ${moment(historyRow.created_at, dateFormat.backendDateFormat).format(dateFormat.displayDateFormat)}`;
    } 
    return (
      <tr key={index}>
        <td>
          {historyRow.effective_rate}
        </td>
        <td>
          {historyRow.effective_date}
        </td>
        <td>
          {historyRow.end_date}
        </td>
        <td>
          {createdBy}
        </td>
      </tr>
    );
  };

  const renderRateTaxModal = () => {
    return (
      <Modal visible={ showRateTaxModal } close={ handleCloseTaxModal } manualOverflow={false}>
        <h4>{modelDetails.title}</h4>
        <div className='m-1'>
          <form onSubmit={() => {}}>
            <div className='row mt-4'>
              <div className='col-4 form-group'>
                <label htmlFor={`field-rate`}>* Rate </label>
                <input id={`field-rate`}
                  type='text'
                  className='form-control'
                  required={true}
                  value={modelDetails.textValue}
                  onChange={handleEffectiveRateChange} />
              </div>
              <div className='col-4 form-group'>
                <label htmlFor={`field-effective_date`}>* Effective Date</label>
                <DatePicker
                  selected={ modelDetails.effectiveDate || moment() }
                  id="effective_date_dateInputBox"
                  dateFormat={ dateFormat.displayDateFormat }
                  onChange={ handleEffectiveDateChange }
                  style={{zIndex: 999999 }}
                  className="form-control date-field"/>
              </div>
            </div>
            <div className='row'>
              {renderHistory()}
            </div>
            {renderButtons()}
          </form>
        </div>
      </Modal>
    );
  };

  const renderPartsTax = () => {
    const partsTax = facility.parts_tax && facility.parts_tax.find(obj => obj.active);
    return (
      <div className="form-group row col-12">
        <div className="col-4">
          <label className="col-form-label col-form-label-sm">
            Parts tax:
          </label>
        </div>
        <input type="text"
          id="parts_tax_inputBox"
          readOnly={true}
          disabled={ true }
          className="form-control-plaintext col-sm-2 ml-3"
          value={ (partsTax && `${partsTax.effective_rate}%`) || '0%' }/>
        <input type="text"
          id="parts_tax_effective_date_inputBox"
          readOnly={true}
          disabled={ true }
          className="form-control-plaintext col-sm-3 ml-2"
          value={ (partsTax && partsTax.effective_date) || moment().format(dateFormat.displayDateFormat) }/>
        <button className="btn btn-secondary col-sm-2 ml-2" style={{fontSize: 'small', fontWeight: 540 }} onClick={ onPartsTaxClick }>
          {`Create` }
        </button>
      </div>
    );
  };

  const renderLaborTax = () => {
    const laborTax = facility.labor_tax &&  facility.labor_tax.find(obj => obj.active);
    return (
      <div className="form-group row col-12">
        <div className="col-4">
          <label className="col-form-label col-form-label-sm">
            Labor tax:
          </label>
        </div>
        <input type="text"
          id="labor_tax_inputBox"
          readOnly={true}
          disabled={ true }
          className="form-control-plaintext col-sm-2 ml-3"
          value={ (laborTax && `${laborTax.effective_rate }%`) || '0%' }/>
        <input type="text"
          id="labor_tax_effective_date_inputBox"
          readOnly={true}
          disabled={ true }
          className="form-control-plaintext col-sm-3 ml-2"
          value={ (laborTax && laborTax.effective_date) || moment().format(dateFormat.displayDateFormat)  }/>
        <button className="btn btn-secondary col-sm-2 ml-2" style={{fontSize: 'small', fontWeight: 540 }} onClick={ onLaborTaxClick }>
          {`Create` }
        </button>
      </div>
    );
  };

  const renderlaborRate = () => {
    const labor_rate = facility.labor_rate && facility.labor_rate.find(obj => obj.active);
    let rate = 0;
    if (labor_rate) {
      rate = labor_rate.effective_rate;
    }
     
    if(rate) {
      rate = parseFloat(rate).toFixed(2);
    }
    return (
      <div className="form-group row col-12">
        <div className="col-4">
          <label className="col-form-label col-form-label-sm">
            Labor Rate:
          </label>
        </div>
        <input type="text"
          id="labor_rate_inputBox"
          readOnly={true}
          disabled={ true }
          className="form-control-plaintext col-sm-2 ml-3"
          value={ rate }/>
        <input type="text"
          id="labor_rate_effective_date_inputBox"
          readOnly={true}
          disabled={ true }
          className="form-control-plaintext col-sm-3 ml-2"
          value={ (labor_rate && labor_rate.effective_date) || moment().format(dateFormat.displayDateFormat)  }/>
        <button className="btn btn-secondary col-sm-2 ml-2" style={{fontSize: 'small', fontWeight: 540 }} onClick={ onLaborRateClick }>
          {`Create` }
        </button>
      </div>
    );
  };

  return (
    <div>
      {renderRateTaxModal()}
      {renderlaborRate()}
      {renderPartsTax()}
      {renderLaborTax()}
    </div>
  );
};

TaxComponent.propTypes = {
  facility: PropTypes.object.isRequired,
  handleRatesUpdate: PropTypes.func.isRequired,
};