import React from "react";
import PropTypes from "prop-types";
import immstruct from "immstruct";

export default class ROVehicleInformation extends React.Component{

  IMMS_KEY = "customer";

  static propTypes = {
    ro: PropTypes.object,
    contractVehicle: PropTypes.object,
    onClose: PropTypes.func,
    onUpdate: PropTypes.func
  };

  static defaultProps = {
    contractVehicle: {},
    ro: {}
  };

  constructor(props){
    super(props);

    this.contractVehicle = immstruct(
      this.IMMS_KEY,
      {
        vin: props.contractVehicle && props.contractVehicle.vin,
        beginning_miles: props.contractVehicle && props.contractVehicle.beginning_miles,
        ending_miles: props.contractVehicle && props.contractVehicle.ending_miles,
      }
    );

    this.contractVehicle.on("swap", (newStructure, oldStructure, keyPath) => {
      this.setState({ contractVehicle: this.contractVehicle.cursor() });
    });

    this.state = {
      contractVehicle: this.contractVehicle.cursor(),
      note: ""
    };
  }

  componentWillUnmount() {
    this.contractVehicle.removeAllListeners();
    immstruct.remove(this.IMMS_KEY);
  }

  onValueChange = (name, e) => {
    let note = {};
    note[name] = e.target.value;

    this.setState(note);
  };


  renderContractVehicleInformation = () => {
    const contractVehicle = this.state.contractVehicle.toJS();
    return (
      <div className="col">
        <h6 className="col-12">Contract Information</h6>
        <div className="col-6">
          <div className="row my-3">
            <div className="col-5">VIN</div>
            <div className="col-7">
              <div className="row my-12">{contractVehicle.vin}</div>
            </div>
          </div>
          <div className="row my-3">
            <div className="col-5">Beginning Miles</div>
            <div className="col-7">
              <div className="row my-12">{contractVehicle.beginning_miles}</div>
            </div>
          </div>
          <div className="row my-3">
            <div className="col-5">Ending Miles</div>
            <div className="col-7">
              <div className="row my-12">{contractVehicle.ending_miles}</div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  renderROVehicleInformation = () => {
    const roVehicle = this.props.ro.vehicle;
    return (
      <div className="col">
        <h4 className="col-12">Vehicle Information</h4>
        <div className="row">
          <h6 className="col-12">RO Information</h6>
          <div className="col-8">
            <div className="row my-12">
              <div className="col-5">VIN</div>
              <div className="col-7">
                <div className="row my-12">{roVehicle.vin}</div>
              </div>
            </div>
            <div className="row my-12">
              <div className="col-5">Odometer</div>
              <div className="col-4">
                <div className="row my-12">{roVehicle.odometer}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  render(){
    return(
      <div className="row">
        {this.renderROVehicleInformation()}    
        {this.renderContractVehicleInformation()}
        <div className="col-12 my-2 mx-1">
          <div className="row justify-content-end">
            <button className="btn btn-secondary mr-4"
              onClick={ this.props.onClose }>
                  OK
            </button>
          </div>
        </div>
      </div>
    );
  }
}