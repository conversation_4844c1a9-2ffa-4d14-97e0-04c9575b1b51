import React from "react";
import Modal from "./../../Modal.jsx";
import RecordNotes from "./../reusable/RecordNotes/RecordNotes.jsx";
import { URLS as apiUrls } from "./../reusable/Utilities/urls";
import PropTypes from 'prop-types';
import { CONSTANTS } from "../reusable/Constants/constants";

export default class NoteModal extends React.Component {

  static propTypes = {
    showNoteModal: PropTypes.bool.isRequired,
    closeNoteModal: PropTypes.func.isRequired,
    noteType: PropTypes.string.isRequired,
    claimObject: PropTypes.object.isRequired,
    createdByUserID: PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.number
    ]),
    hideNotes: PropTypes.bool,
    warningText: PropTypes.string,
    closeOnModel: PropTypes.bool,
    onRecordNotesUpdate: PropTypes.func,
    hideAddNoteTextArea: PropTypes.bool,
  };

  constructor(props) {
    super(props);
  }

  render() {
    const location = {
      "id": this.props.noteType === CONSTANTS.FIELD_TYPE.contract ? "" : this.props.claimObject.id,
      "contract_number": this.props.noteType === CONSTANTS.FIELD_TYPE.contract ? this.props.claimObject.contract_number : "",
      "productCode" : this.props.claimObject.product_code
    };
    return (
      <section>
        <Modal visible={ this.props.showNoteModal } hideContentWrapper={ true }>
          <RecordNotes location={ location }
            apiURL={
              this.props.noteType === CONSTANTS.FIELD_TYPE.contract ?
                `${apiUrls.contract}/${this.props.claimObject.contract_number}/notes` :
                apiUrls.automotiveClaimsNotes
            }
            hideAddNoteTextArea={this.props.hideAddNoteTextArea || this.props.noteType !== 'manual'}
            type={ this.props.noteType }
            hasCloseButton={ true }
            containerClass="col-12"
            hideNotes={ this.props.hideNotes }
            createdByUserID={ this.props.createdByUserID }
            closeButtonCallback={ this.props.closeNoteModal }/>
        </Modal>
      </section>
    );
  }
}
