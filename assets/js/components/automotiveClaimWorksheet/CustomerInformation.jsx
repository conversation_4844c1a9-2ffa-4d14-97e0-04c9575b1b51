import React from "react";
import PropTypes from "prop-types";
import Select from "react-select";
import AttachmentListModal from "./AttachmentListModal.jsx";
import AttachmentModal from "./FileAttachment.jsx";
import ConfirmationModal from "../reusable/ConfirmationModal/ConfirmationModal.jsx";
import Alert from "react-s-alert";
import { json as ajax } from "./../../ajax.js";
import { URLS as apiUrls } from "./../reusable/Utilities/urls";
import Modal from "../../Modal.jsx";
import ROCustomerInformation from "./ROCustomerInformation.jsx";
import ROVehicleInformation from "./ROVehicleInformation.jsx";
import ReactTooltip from 'react-tooltip';

const inspectionReviewedOptions = ['Yes - No Failed Items Found', 'Yes - Failed Items Found', 'No Inspection Found', 'Not Applicable'];

export default class CustomerInformation extends React.Component {

  static propTypes = {
    claimObject: PropTypes.object,
    isDisabled: PropTypes.bool,
    isDifferentOwner: PropTypes.bool,
    showLoader: PropTypes.func.isRequired,
    hideLoader: PropTypes.func.isRequired,
    onCustomerUpdate: PropTypes.func,
    reloadAttachments: PropTypes.bool,
    updateReloader: PropTypes.func,
    isClaimHandler: PropTypes.bool,
    reviewInspection: PropTypes.string,
    onChangeReviewInspection: PropTypes.func,
  };

  constructor(props) {
    super(props);
    this.state = {
      displayAttachmentListModal: false,
      deleteAttachmentConfirmationModal: false,
      attachmentToBeDeleted: '',
      displayAddAttachmentModal: false,
      attachments: [],
      inspectionAttachments: [],
      isInspectionAttachment: false,
      showModal: false,
      showVehicleModal: false,
      ro: {}
    };
  }

  UNSAFE_componentWillReceiveProps = (nextProps) => {
    if (nextProps.claimObject.id !== this.props.claimObject.id || (nextProps.reloadAttachments && !this.props.reloadAttachments)) {
      this.loadAttachments(nextProps);
      this.loadInspectionAttachments(nextProps);
    }
  };

  handleDeleteAttachment = (id) => {
    if (!this.props.isDisabled || this.props.isClaimHandler) {
      this.setState({
        deleteAttachmentConfirmationModal: true,
        attachmentToBeDeleted: id,
        displayAttachmentListModal: false
      });
    }
  };

  closeConfirmationModal = () => {
    this.setState({
      deleteAttachmentConfirmationModal: false,
      attachmentToBeDeleted: '',
      displayAttachmentListModal: true
    });
  };

  loadAttachments = (props) => {
    if (!props) {
      props = this.props;
    }
    this.props.showLoader();
    const url = apiUrls.autoClaimDocumentIndex.replace('__claimId__', props.claimObject.id);
    ajax(url, {}, { method: 'GET' }, (data, status) => {
      if (status === 200) {
        this.setState({ attachments: data.docs });
        this.props.hideLoader();
      } else {
        this.props.hideLoader();
        Alert.error("Click the browser's Refresh button to reload the data. If the error continues, contact your system administrator.");
      }
      if (props.reloadAttachments) {
        props.updateReloader(false);
      }
    });
  };

  loadInspectionAttachments = (props) => {
    if (!props) {
      props = this.props;
    }
    this.props.showLoader();
    const url = apiUrls.autoClaimInspectionDocumentIndex.replace('__claimId__', props.claimObject.id);
    ajax(url, {}, { method: 'GET' }, (data, status) => {
      if (status === 200) {
        this.setState({ inspectionAttachments: data.docs });
        this.props.hideLoader();
      } else {
        this.props.hideLoader();
        Alert.error("Failed to get attachments.");
      }
      if (props.reloadAttachments) {
        props.updateReloader(false);
      }
    });
  };

  deleteAttachment = () => {
    this.props.showLoader();
    this.setState({ deleteAttachmentConfirmationModal: false }, function () {
      ajax(`${apiUrls.automotiveClaimDocument}/${this.state.attachmentToBeDeleted}`, {}, { method: 'DELETE' }, (data, status) => {
        if (status === 200) {
          if (this.state.isInspectionAttachment) {
            this.loadInspectionAttachments();
          } else {
            this.loadAttachments();
          }
          this.setState({ attachmentToBeDeleted: '', displayAttachmentListModal: true });
        } else {
          this.props.hideLoader();
          Alert.error("Failed to get attachments: Click the browser's Refresh button to reload the data. If the error continues, contact your system administrator.");
        }
      });
    });
  };

  closeAttachmentModal = (type) => {
    this.setState({
      displayAddAttachmentModal: false
    }, function () {
      if (type === 'success') {
        Alert.success("Document uploaded successfully.");
        if (this.state.isInspectionAttachment) {
          this.loadInspectionAttachments();
        } else {
          this.loadAttachments();
        }
      } else if (type === 'error') {
        Alert.error("Click the browser's Refresh button to reload. If the error continues, contact your system administrator.");
      }
    });
  };


  onWarningClick = () => {
    this.props.showLoader();
    ajax(`${apiUrls.getROCustomer}?facility_id=${this.props.claimObject.facility_id}&ro_number=${this.props.claimObject.ro}`, {}, {},
      (data, status) => {
        this.props.hideLoader();
        if (status === 200) {
          this.setState({
            showModal: true,
            ro: data.ro
          });
        } else {
          Alert.error("Click the browser's Refresh button to reload the data. If the error continues, contact your system administrator.");
        }
      });
  };

  onVehicleWarningClick = () => {
    this.props.showLoader();
    ajax(`${apiUrls.getROCustomer}?facility_id=${this.props.claimObject.facility_id}&ro_number=${this.props.claimObject.ro}`, {}, {},
      (data, status) => {
        this.props.hideLoader();
        if (status === 200) {
          this.setState({
            showVehicleModal: true,
            ro: data.ro
          });
        } else {
          Alert.error("Click the browser's Refresh button to reload the data. If the error continues, contact your system administrator.");
        }
      });
  };

  closeModal = () => {
    this.setState({
      showModal: false
    });
  };
  closeVehicleModal = () => {
    this.setState({
      showVehicleModal: false
    });
  };

  onUpdate = (updatedCustomer) => {
    this.setState({
      showModal: false
    });
    this.props.onCustomerUpdate(updatedCustomer);
  };

  renderUpdateModal = () => {
    return (
      <ROCustomerInformation onClose={this.closeModal}
        ro={this.state.ro}
        contractCustomer={this.props.claimObject}
        disableUpdate={this.props.isDisabled}
        onUpdate={this.onUpdate} />
    );
  };
  renderVehicleInfoModal = () => {
    return (
      <ROVehicleInformation onClose={this.closeVehicleModal}
        ro={this.state.ro}
        contractVehicle={this.props.claimObject}
        onUpdate={this.onUpdate} />
    );
  };
  renderModal = () => {
    return (
      <Modal visible={this.state.showModal}
        close={this.closeModal} size="extra-large">
        {this.renderUpdateModal()}
      </Modal>
    );
  };

  renderVehicleModal = () => {
    return (
      <Modal visible={this.state.showVehicleModal}
        close={this.closeVehicleModal} size="extra-large">
        {this.renderVehicleInfoModal()}
      </Modal>
    );
  };

  getInspectioReviewOptions = () => {
    return inspectionReviewedOptions.map(obj => {
      return { label: obj, value: obj };
    });
  }

  render() {
    let customerIcon = <i className="fa fa-check-square fa-lg text-success ml-3 mt-1" aria-hidden="true" />;
    let vehicleIcon = <i className="fa fa-check-square fa-lg text-success ml-3 mt-1" aria-hidden="true" />;
    let isCustomerROValid = this.props.claimObject.is_ro_customer_valid;
    let isROContractVinMatch = this.props.claimObject.is_ro_contract_vin_match;
    let isROContractMileageValid = this.props.claimObject.is_ro_contract_mileage_valid;

    if (isCustomerROValid === "false") {
      customerIcon = <i className="fa fa-exclamation-triangle fa-lg text-warning ml-3 cursor-pointer mt-1" aria-hidden="true" onClick={this.onWarningClick} />;
    }
    if (isROContractVinMatch === "false" || isROContractMileageValid === "false") {
      vehicleIcon = <i className="fa fa-exclamation-triangle fa-lg text-warning ml-3 cursor-pointer mt-1" aria-hidden="true" onClick={this.onVehicleWarningClick} />;
    }

    const longTextStyle = {
      "maxWidth": "180px",
      "overflow": "hidden",
      "textOverflow": "ellipsis",
      "whiteSpace": "nowrap"
    };
    
    return (
      <section className="col">
        <div className="d-inline-flex">
          <a href={`/gap-contract/${this.props.claimObject.contract_number}?active_tab=customer`}
            id="link-customer-name"
            data-tip
            data-for="customer_name"
            style={longTextStyle}
            target="_blank"
            rel="noopener noreferrer">
            {this.props.claimObject.customer_name}
            <ReactTooltip id={`customer_name`} aria-haspopup='true'>
              <p className="text-center">{this.props.claimObject.customer_name}</p>
            </ReactTooltip>
          </a>
          {customerIcon}
        </div>
        <p className="mb-0 col-form-label-sm">
          {this.props.claimObject.street_address}
        </p>
        <p className="mb-0 col-form-label-sm">
          {`${this.props.claimObject.city}, ${this.props.claimObject.state} ${this.props.claimObject.postal_code}`}
        </p>
        <p className="col-form-label-sm">
          {this.props.claimObject.phone_number}
        </p>
        <a href={`/gap-contract/${this.props.claimObject.contract_number}?active_tab=vehicle`}
          id="link-vin"
          rel="noopener noreferrer"
          className="col-form-label-sm">
          {this.props.claimObject.vin}
        </a>
        {vehicleIcon}
        <p className="col-form-label-sm mb-2">
          {`${this.props.claimObject.year} ${this.props.claimObject.make} ${this.props.claimObject.model}`}
        </p>
        <p>
          <label>*Reviewed Inspection</label>
          <Select value={this.props.reviewInspection}
            id={`inspection_review-inputBox`}
            disabled={false}
            clearable={false}
            options={this.getInspectioReviewOptions()}
            onChange={this.props.onChangeReviewInspection}
            menuContainerStyle={{ zIndex: 10 }}
            tabSelectsValue={false}
            matchProp='label' />
        </p>
        <p className="col-form-label-sm">
          Add Inspection Information
          <span className="ml-3 cursor-pointer text-primary"
            id="inspection-attachment-count"
            onClick={() => {
              this.setState({ displayAttachmentListModal: true, isInspectionAttachment: true });
            }}>
            {this.state.inspectionAttachments.length}
          </span>
          <span className="ml-3 fa fa-plus cursor-pointer text-primary"
            id="add-inspection-attachment"
            onClick={() => {
              if (!this.props.isDifferentOwner || this.props.isClaimHandler) {
                this.setState({ displayAddAttachmentModal: true, isInspectionAttachment: true  });
              }
            }} />
        </p>
        <p className="col-form-label-sm">
          Attachments
          <span className="ml-3 cursor-pointer text-primary"
            id="attachment-count"
            onClick={() => {
              this.setState({ displayAttachmentListModal: true, isInspectionAttachment: false });
            }}>
            {this.state.attachments.length}
          </span>
          <span className="ml-3 fa fa-plus cursor-pointer text-primary"
            id="add-attachment"
            onClick={() => {
              if (!this.props.isDifferentOwner || this.props.isClaimHandler) {
                this.setState({ displayAddAttachmentModal: true, isInspectionAttachment: false });
              }
            }} />
        </p>
        <AttachmentListModal displayAttachmentListModal={this.state.displayAttachmentListModal}
          closeModal={() => {
            this.setState({ displayAttachmentListModal: false });
          }}
          handleDeleteAttachment={this.handleDeleteAttachment}
          isInspectionAttachments={this.state.isInspectionAttachment}
          attachments={this.state.isInspectionAttachment ? this.state.inspectionAttachments : this.state.attachments} />
        <AttachmentModal displayModal={this.state.displayAddAttachmentModal}
          closeModal={this.closeAttachmentModal}
          claimId={this.props.claimObject.id} 
          isInspectionAttachment={this.state.isInspectionAttachment}
        />
        <ConfirmationModal confirmButtonText="Yes"
          declineButtonText="No"
          displayConfirmationModal={this.state.deleteAttachmentConfirmationModal}
          displayMessage="You have selected to delete this attachment, do you want to continue?"
          onConfirm={this.deleteAttachment}
          onDecline={this.closeConfirmationModal} />
        {this.renderModal()}
        {this.renderVehicleModal()}
      </section>
    );
  }
}
