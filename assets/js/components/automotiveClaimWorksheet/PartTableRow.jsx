import React from "react";
import InputBox from "../reusable/InputBox/InputBox.jsx";
import If from "../reusable/If/If.jsx";
import PropTypes from "prop-types";
import Select from "react-select";
import { CONSTANTS } from "../reusable/Constants/constants";
import { formatCurrency } from "../reusable/Utilities/format.js";

export default class PartTableRow extends React.Component {

  ZINDEX = 10;

  static propTypes = {
    part: PropTypes.object,
    index: PropTypes.number,
    complaintIndex: PropTypes.number,
    handlePartLaborUpdate: PropTypes.func.isRequired,
    removePartOrLabor: PropTypes.func.isRequired,
    isDisabled: PropTypes.bool,
    showRequiredNoteError: PropTypes.bool
  };

  constructor(props) {
    super(props);
  }

  onBlur = (field, value) => {
    this.props.handlePartLaborUpdate('parts', this.props.part.id, field, value);
  };

  getOptions = () => {
    return CONSTANTS.PART_PAID_SHORT.map((element) => {
      return {
        value: element,
        label: element
      };
    });
  };

  onPartNoteChange = (partObject) => {
    if(partObject){
      this.props.handlePartLaborUpdate('parts', this.props.part.id, 'notes', partObject.value);
    } else {
      this.props.handlePartLaborUpdate('parts', this.props.part.id, 'notes', '');
    }
  };

  render() {
    const requested = parseFloat(this.props.part.requested);
    const approved = parseFloat(this.props.part.approved);
    let borderStyle = {};

    if(requested < approved){
      borderStyle = {
        "border": "1px solid #dc3545",
        "borderRadius": "0.2rem"
      };
    }

    let selectedNoteOption;
    const note = this.props.part.notes;

    if(note){
      selectedNoteOption = {
        label: note,
        value: note
      };
    }

    return (
      <tbody className="border-top-0">
        <tr className="row col-12 pr-0">
          <td className="col-2 border-bottom-0">
            <InputBox type="Text"
              id={ `complaint-${this.props.complaintIndex}-part-number-inputBox-${this.props.index}` }
              isDisabled={ this.props.isDisabled }
              value={ this.props.part.part_number }
              onChange={ this.props.handlePartLaborUpdate.bind(null, 'parts', this.props.part.id, 'part_number') }/>
          </td>
          <td className="col-3 border-bottom-0">
            <InputBox type="Text"
              id={ `complaint-${this.props.complaintIndex}-description-inputBox-${this.props.index}` }
              isDisabled={ this.props.isDisabled }
              value={ this.props.part.description }
              onChange={ this.props.handlePartLaborUpdate.bind(null, 'parts', this.props.part.id, 'description') }/>
          </td>
          <td className="col-1 border-bottom-0">
            <InputBox type="Number"
              hasDefaultValue={ true }
              id={ `complaint-${this.props.complaintIndex}-quantity-inputBox-${this.props.index}` }
              isDisabled={ this.props.isDisabled }
              value={ this.props.part.quantity }
              onChange={ this.props.handlePartLaborUpdate.bind(null, 'parts', this.props.part.id, 'quantity') }/>
          </td>
          <td className="col border-bottom-0">
            <InputBox type="Currency"
              hasDefaultValue={ true }
              id={ `complaint-${this.props.complaintIndex}-cost-inputBox-${this.props.index}` }
              isDisabled={ this.props.isDisabled }
              value={ formatCurrency(this.props.part.cost) }
              onBlur={ this.props.handlePartLaborUpdate.bind(null, 'parts', this.props.part.id, 'cost') }/>
          </td>
          <td className="col border-bottom-0">
            <InputBox type="Currency"
              hasDefaultValue={ true }
              id={ `complaint-${this.props.complaintIndex}-msrp-inputBox-${this.props.index}` }
              isDisabled={ this.props.isDisabled }
              value={ formatCurrency(this.props.part.msrp) }
              onBlur={ this.props.handlePartLaborUpdate.bind(null, 'parts', this.props.part.id, 'msrp') }/>
          </td>
          <td className="col border-bottom-0">
            <InputBox type="Currency"
              hasDefaultValue={ true }
              style={ borderStyle }
              id={ `complaint-${this.props.complaintIndex}-requested-inputBox-${this.props.index}` }
              isDisabled={ this.props.isDisabled }
              value={ formatCurrency(this.props.part.requested) }
              onBlur={ this.onBlur.bind(null, 'requested') }/>
          </td>
          <td className="col border-bottom-0">
            <InputBox type="Currency"
              hasDefaultValue={ true }
              style={ borderStyle }
              id={ `complaint-${this.props.complaintIndex}-approved-inputBox-${this.props.index}` }
              isDisabled={ this.props.isDisabled }
              value={ formatCurrency(this.props.part.approved) }
              onBlur={ this.onBlur.bind(null, 'approved') }/>
          </td>
          <td className="h5">
            <i className="fa fa-times cursor-pointer"
              id={ `complaint-${this.props.complaintIndex}-delete-parts-${this.props.index}` }
              onClick={ !this.props.isDisabled ? this.props.removePartOrLabor.bind(null, this.props.part.id) : () => {} }/>
          </td>
        </tr>
        <If condition={ requested > approved }>
          <tr className="row col-12">
            <td className="col border-top-0">
              <Select.Creatable value={ selectedNoteOption }
                id={ `complaint-${this.props.complaintIndex}-part-note-inputBox-${this.props.index}` }
                disabled={ this.props.isDisabled }
                options={ this.getOptions() }
                onChange={ this.onPartNoteChange }
                menuContainerStyle={ { zIndex: this.ZINDEX } }
                placeholder="Type or select reason"
                tabSelectsValue={ false }
                matchProp='label'/>
              <If condition={ this.props.showRequiredNoteError && !this.props.part.notes }>
                <span className="text-danger">Required Note for approved amount less than requested amount.</span>
              </If>
            </td>
          </tr>
        </If>
        <If condition={ requested < approved }>
          <tr className="row col-12">
            <td className="col border-top-0 text-right">
              <span className="text-danger">Approved amount is greater than requested amount.</span>
            </td>
          </tr>
        </If>
      </tbody>
    );
  }
}