import React from 'react';
import PropTypes from 'prop-types';

export default class ConfirmationPresentational extends React.Component {

  static propTypes = {
    message: PropTypes.string.isRequired,
    facilityDetails: PropTypes.object.isRequired,
    onActionCallBack: PropTypes.func.isRequired,
    onNoActionCallBack: PropTypes.func.isRequired,
    shouldShowTwoButton: PropTypes.bool.isRequired
  };

  renderActionButton(){
    if(this.props.shouldShowTwoButton) {
      return (
        <div className="col-12 text-center my-4">
          <button type="button"
            className="btn btn-primary cursor-pointer mr-3"
            onClick={ this.props.onActionCallBack }>
                    Yes
          </button>
          <button type="button"
            className="btn btn-secondary cursor-pointer"
            onClick={ this.props.onNoActionCallBack }>
                    No
          </button>
        </div>
      );
    } else {
      return (
        <div className="col-12 text-center my-4">
          <button type="button"
            className="btn btn-primary cursor-pointer mr-3"
            onClick={ this.props.onActionCallBack }>
                  Back
          </button>
        </div>
      );
    }
  }

  render() {
    const { facilityDetails } = this.props;

    return (
      <div className="row">
        <div className="col-12 my-2">
          <p className="d-inline-block mr-auto">
            <strong>
              Facility Already Exists
            </strong>
          </p>
        </div>
        <div className="col-12">
          <p className="text-left ml-3">
            {this.props.message}
          </p>
        </div>
        <div className="col-12">
          <div className="table-responsive claim-list">
            <table className="table table-hover">
              <thead>
                <tr>
                  <th>Facility Name</th>
                  <th>Facility Code</th>
                  <th>City</th>
                  <th>State</th>
                  <th>Zip</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>
                    {facilityDetails['name']}
                  </td>
                  <td>
                    {facilityDetails['facility_code']}
                  </td>
                  <td>
                    {facilityDetails['city']}
                  </td>
                  <td>
                    {facilityDetails['state_code']}
                  </td>
                  <td>
                    {facilityDetails['postal_code']}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        {this.renderActionButton()}
      </div>
    );
  }
}