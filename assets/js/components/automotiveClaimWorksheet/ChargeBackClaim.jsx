import React, { useState } from "react";
import PropTypes from "prop-types";
import Alert from "react-s-alert";
import DatePicker from "react-datepicker";
import moment from "moment";

import InputBox from "../reusable/InputBox/InputBox.jsx";
import { formatCurrency } from "../reusable/Utilities/format.js";
import { URLS as apiUrls } from "./../reusable/Utilities/urls";
import DocumentLinkList from "../reusable/DocumentLinkList/DocumentLinkList.jsx";
import SelectBox from "../reusable/SelectBox/SelectBox.jsx";
import { CONSTANTS } from "../reusable/Constants/constants.js";
import { userHasRole } from "../reusable/Utilities/userHasRole";
import If from "../reusable/If/If";

import { json as ajax } from "./../../ajax.js";
import Modal from "../../Modal.jsx";

import FacilityInformation from "./FacilityInformation.jsx";
import AttachmentModal from "./FileAttachment.jsx";
import ChargeBackPaymentInfo from "./ChargeBackPaymentInfo.jsx";
import PayeeInformation from "./PayeeInformation";
import dateFormat from "./../reusable/Utilities/dateFormat.js";
import hstore from '../reusable/Utilities/hstore.js';

const optionList = {
  claimStatusManager: [
    { name: CONSTANTS.CHARGEBACK_CLAIM_STATUS_DISPLAY_MAP.open, value: CONSTANTS.CHARGEBACK_CLAIM_STATUS_MAP.open, isDisabled: false },
    { name: CONSTANTS.CHARGEBACK_CLAIM_STATUS_DISPLAY_MAP.approved, value: CONSTANTS.CHARGEBACK_CLAIM_STATUS_MAP.approved, isDisabled: false },
    { name: CONSTANTS.CHARGEBACK_CLAIM_STATUS_DISPLAY_MAP.waitingForChargeback, value: CONSTANTS.CHARGEBACK_CLAIM_STATUS_MAP.waitingForChargeback, isDisabled: false },
    { name: CONSTANTS.CHARGEBACK_CLAIM_STATUS_DISPLAY_MAP.chargebackCollected, value: CONSTANTS.CHARGEBACK_CLAIM_STATUS_MAP.chargebackCollected, isDisabled: false },
    { name: CONSTANTS.CHARGEBACK_CLAIM_STATUS_DISPLAY_MAP.chargeback, value: CONSTANTS.CHARGEBACK_CLAIM_STATUS_MAP.chargeback, isDisabled: true },
    { name: CONSTANTS.CHARGEBACK_CLAIM_STATUS_DISPLAY_MAP.deactivated, value: CONSTANTS.CHARGEBACK_CLAIM_STATUS_MAP.deactivated, isDisabled: false },
  ],
  claimStatusAgent: [
    { name: CONSTANTS.CHARGEBACK_CLAIM_STATUS_DISPLAY_MAP.open, value: CONSTANTS.CHARGEBACK_CLAIM_STATUS_MAP.open, isDisabled: true },
    { name: CONSTANTS.CHARGEBACK_CLAIM_STATUS_DISPLAY_MAP.approved, value: CONSTANTS.CHARGEBACK_CLAIM_STATUS_MAP.approved, isDisabled: true },
    { name: CONSTANTS.CHARGEBACK_CLAIM_STATUS_DISPLAY_MAP.waitingForChargeback, value: CONSTANTS.CHARGEBACK_CLAIM_STATUS_MAP.waitingForChargeback, isDisabled: false },
    { name: CONSTANTS.CHARGEBACK_CLAIM_STATUS_DISPLAY_MAP.chargebackCollected, value: CONSTANTS.CHARGEBACK_CLAIM_STATUS_MAP.chargebackCollected, isDisabled: false },
    { name: CONSTANTS.CHARGEBACK_CLAIM_STATUS_DISPLAY_MAP.chargeback, value: CONSTANTS.CHARGEBACK_CLAIM_STATUS_MAP.chargeback, isDisabled: true },
    { name: CONSTANTS.CHARGEBACK_CLAIM_STATUS_DISPLAY_MAP.deactivated, value: CONSTANTS.CHARGEBACK_CLAIM_STATUS_MAP.deactivated, isDisabled: false },
  ],
  payType: [
    { name: CONSTANTS.PAYMENT_TYPE_NAMES.DEFAULT, value: "" },
    { name: CONSTANTS.PAYMENT_TYPE_NAMES.CREDIT_CARD, value: CONSTANTS.PAYMENT_TYPE_CODES.CREDIT_CARD },
    { name: CONSTANTS.PAYMENT_TYPE_NAMES.CUSTOMER, value: CONSTANTS.PAYMENT_TYPE_CODES.CUSTOMER },
    { name: CONSTANTS.PAYMENT_TYPE_NAMES.STORE, value: CONSTANTS.PAYMENT_TYPE_CODES.STORE }
  ]
};

const ChargbackClaim = (props, context) => {
  const [showAttachmentModal, setShowAttchmentModal] = useState(false);
  const [initialStatus] = useState(props.claimObject.status);
  const [facility, setFacility] = useState(() => {});
  const [claimObject, setClaimObject] = useState(() => initialState());
  const isDisabled = (claimObject.status === CONSTANTS.CHARGEBACK_CLAIM_STATUS_MAP.approved
    || claimObject.status === CONSTANTS.CHARGEBACK_CLAIM_STATUS_MAP.waitingForChargeback
    || claimObject.status === CONSTANTS.CHARGEBACK_CLAIM_STATUS_MAP.chargeback
    || claimObject.status === CONSTANTS.CHARGEBACK_CLAIM_STATUS_MAP.deactivated
    || !hstore.hasAny(props.user.roles, [CONSTANTS.USER_ROLES.autoClaimsManager, CONSTANTS.USER_ROLES.accounting]));

  function initialState () {
    return {...props.claimObject};
  }

  const loadFacilityDetails = (facilityID) => {
    ajax(`${apiUrls.facility}/${facilityID}`, {}, {}, (data, status) => {
      if (status === 200) {
        setFacility({...data.facility});
        loadFacilityToClaim(data.facility);
      } else {
        if(data.message) {
          Alert.error(data.message);
        } else {
          Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
        }
      }
    });
  };

  const loadDocuments = () => {
    ajax(`api/automotive-claims/${claimObject.id}/documents`, {}, {}, (data, status) => {
      if (status === 200) {
        setClaimObject({...claimObject, documents: data.docs});
      } else {
        if(data.message) {
          Alert.error(data.message);
        } else {
          Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
        }
      }
    });
  };

  const handleDeleteAttachment = (toBeDeleted) => {
    if (isDisabled) {
      return;
    }
    ajax(`api/automotive-claims/documents/${toBeDeleted}`, {}, { method: 'DELETE' }, (data, status) => {
      if (status === 200) {
        loadDocuments();
        Alert.success('Document deleted sucessfully.');
      } else {
        if(data.message) {
          Alert.error(data.message);
        } else {
          Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
        }
      }
    });
  };

  const loadFacilityToClaim = (facilityDetails) => {
    const claim = {
      ...claimObject, 
      facility_id: facilityDetails.id,
      facility_name: facilityDetails.name,
      facility_address: facilityDetails.address.String,
      facility_postal_code: facilityDetails.postal_code,
      facility_city: facilityDetails.city,
      facility_state_code: facilityDetails.state_code,
      facility_country: facilityDetails.country,
      facility_phone: facilityDetails.phone.String,
      facility_fax: facilityDetails.fax.String
    };
    setClaimObject({...claim});
  };

  const onUseCurrentFacility = (facilityDetails) => {
    loadFacilityToClaim(facilityDetails);
  };

  const closeAttachmentModal = () =>  {
    loadDocuments();
    setShowAttchmentModal(false);
  };

  const showClaimAttachmentModal = () => setShowAttchmentModal(true);

  const submitChargebackClaim = (status) => {
    let claimData = { ...claimObject };
    if (status) {
      claimData = {
        ...claimObject,
        status,
      };
    }
    ajax(`api/automotive-chargeback-claims/${claimObject.id}`, {...claimData}, { method: 'PUT' }, (data, status) => {
      if (status === 200) {
        Alert.success('Claim submitted successfully.');
        props.closeModal();
      } else {
        if(data.message) {
          Alert.error(data.message);
        } else {
          Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
        }
      }
    });
  };

  const reOpenClaim = () => {
    submitChargebackClaim(CONSTANTS.CHARGEBACK_CLAIM_STATUS_MAP.open);
  };

  const getStatusOptions = () => {
    return userHasRole(props.user, CONSTANTS.USER_ROLES.autoClaimsManager) ? optionList.claimStatusManager : optionList.claimStatusAgent;
  };

  const handleStatusChange = (e) => {
    setClaimObject({
      ...claimObject,
      status: e,
    });
  };

  const handleNoteChange = (e) => {
    setClaimObject({
      ...claimObject,
      notes: e.target.value
    });
  };

  const handleChargebackCheckNumberChange = (e) => {
    setClaimObject({
      ...claimObject,
      chargeback_check_number: e
    });
  };
  const handleDateChange = (e) => {
    setClaimObject({
      ...claimObject,
      chargeback_paid_date: e
    });
  };
  const handleChargebackCollectedNoteChange = (e) => {
    setClaimObject({
      ...claimObject,
      chargeback_collected_note: e.target.value
    });
  };

  const handleAmountChange = (e) => {
    if(e) {
      setClaimObject({
        ...claimObject,
        amount: parseFloat(e),
      });
    }
  };

  const hanldePayTypeChange = (e) => {
    if(e) {
      setClaimObject({
        ...claimObject,
        pay_type: e,
      });
    }
  };

  const getDocumentList = () => {
    return claimObject.documents;
  };

  const renderClaimAttachmentModal = () => {
    return (
      <AttachmentModal
        displayModal={ showAttachmentModal }
        closeModal={ closeAttachmentModal }
        claimId={ claimObject.id }/>
    );
  };
  
  const loadPDF = () => {
    window.open(`/api/automotive-claims/${props.claimObject.id}/pdf`);
  };

  const renderClaimModel = () => {
    const disableStatus = (claimObject.status === CONSTANTS.CHARGEBACK_CLAIM_STATUS_MAP.waitingForChargeback
      || claimObject.status === CONSTANTS.CHARGEBACK_CLAIM_STATUS_MAP.chargeback
      || initialStatus === CONSTANTS.CHARGEBACK_CLAIM_STATUS_MAP.deactivated
      || initialStatus === CONSTANTS.CHARGEBACK_CLAIM_STATUS_MAP.chargebackCollected
    || !hstore.hasAny(props.user.roles, [CONSTANTS.USER_ROLES.autoClaimsManager, CONSTANTS.USER_ROLES.accounting]));

    const disableChargebackCollection = ( disableStatus ||
    claimObject.status === CONSTANTS.CHARGEBACK_CLAIM_STATUS_MAP.open ||
      claimObject.status === CONSTANTS.CHARGEBACK_CLAIM_STATUS_MAP.approved);

    return (
      <Modal visible={ true } close={ props.closeModal } size={ `extra-large` } >
        <div className="col-12 form-group row">
          <div className="col-6">
            <h5 style={{position:'relative', top: -30, marginRight: 45}}>Add Chargeback Claim</h5>
          </div>
          <div className="col-6 text-right">
            {initialStatus === CONSTANTS.CHARGEBACK_CLAIM_STATUS_MAP.deactivated ?
              (<button type="button" className="btn btn-primary" title="Print"
                onClick={reOpenClaim}>
              Re-Open
              </button>) : null}
            &nbsp;&nbsp;&nbsp;
            <button type="button" className="btn btn-secondary" title="Print" 
              onClick={ loadPDF }>
              <i className='fa fa-print'/>
            </button>
          </div>
        </div>

        <div className="col-12 form-group row">
          <div className="col-6">
            <div className="form-group row">
              <div className="col-3 col-form-label col-form-label-sm">
                Pay Type
              </div>
              <SelectBox
                id="pay-type-dropdown"
                value={ claimObject.pay_type }
                disabled={ isDisabled || disableStatus}
                onChange={ hanldePayTypeChange }
                customClassName="form-control-sm"
                optionsList={ optionList.payType }/>
            </div>
            <div className="form-group row" style={ {marginRight: 60}}>
              <If condition={ claimObject.pay_type === CONSTANTS.PAYMENT_TYPE_CODES.CUSTOMER }>
                <PayeeInformation
                  claimObject={claimObject }
                  isDisabled={ isDisabled }
                  showLoader={ () => {} }
                  hideLoader={ () => {} }
                  ref={ (customerPayee) => {
                    this.customerPayee = customerPayee;
                  } }/>
              </If>
            </div>
            <div className="form-group row">
              <If condition={ claimObject.pay_type === CONSTANTS.PAYMENT_TYPE_CODES.STORE ||claimObject.pay_type === CONSTANTS.PAYMENT_TYPE_CODES.CREDIT_CARD }>
                <FacilityInformation showFacilityLoader={ false }
                  onUseCurrentFacility={ onUseCurrentFacility }
                  claimObject={ claimObject || {}}
                  facilityDetails={ facility || {} }
                  isDisabled={ isDisabled || disableStatus}
                  loadFacilityDetails={ loadFacilityDetails }
                  user={ props.user }/>
              </If>
            </div>
          </div>
          <div className="col-6">
            <div className="form-group row">
              <div className="col-2 col-form-label col-form-label-sm">
                Amount
              </div>
              <div className="col-10">
                <InputBox type="Currency"
                  id={`requested-total-inputBox`}
                  hasDefaultValue={ true }
                  isDisabled={ isDisabled || disableStatus}
                  value={ formatCurrency(claimObject.amount) }
                  onBlur={handleAmountChange}
                />
              </div>
            </div>
            <div className="form-group row">
              <div className="col-2 col-form-label col-form-label-sm">
                Note
              </div>
              <div className="col-10">
                <textarea type="text"
                  id="note-text-area"
                  className="form-control"
                  rows="3"
                  disabled={isDisabled || disableStatus || claimObject.status === CONSTANTS.CHARGEBACK_CLAIM_STATUS_MAP.chargebackCollected}
                  value={ claimObject.notes }
                  onChange={ handleNoteChange } />
              </div>
            </div>
            <div className="form-group row">
              <div className="col-2 col-form-label col-form-label-sm">
                Status
              </div>
              <div className="col-10">
                <SelectBox
                  id="status-dropdown"
                  value={ claimObject.status }
                  disabled={ disableStatus && claimObject.status !== CONSTANTS.CHARGEBACK_CLAIM_STATUS_MAP.waitingForChargeback}
                  onChange={ handleStatusChange }
                  customClassName="form-control-sm"
                  optionsList={ getStatusOptions() }/>
              </div>
            </div>
            <div className="form-group row">
              <div className="col-2 col-form-label col-form-label-sm">
                Check#
              </div>
              <div className="col-10">
                <InputBox type="Text"
                  id="chargeback-collected-text-area"
                  hasDefaultValue={ false }
                  isDisabled={ disableChargebackCollection }
                  value={ claimObject.chargeback_check_number }
                  onChange={ handleChargebackCheckNumberChange } 
                />
              </div>
            </div>
            <div className="form-group row">
              <div className="col-2 col-form-label col-form-label-sm">
                Paid date
              </div>
              <div className="col-10">
                <DatePicker
                  selected={ claimObject.chargeback_paid_date ? moment(claimObject.chargeback_paid_date) : moment() }
                  id={ "amortization_deal_dateInputBox" }
                  dateFormat={dateFormat.displayDateFormat}
                  onChange={ handleDateChange }
                  className="form-control form-control-sm date-field"
                  disabled={disableChargebackCollection}/>
              </div>
            </div>
            <div className="form-group row">
              <div className="col-2 col-form-label col-form-label-sm">
                Chargeback Collected Note
              </div>
              <div className="col-10">
                <textarea type="text"
                  id="chargeback-collected-note-text-area"
                  className="form-control"
                  rows="3"
                  disabled={disableChargebackCollection}
                  value={ claimObject.chargeback_collected_note }
                  onChange={ handleChargebackCollectedNoteChange } />
              </div>
            </div>
            <div className="form-group row">
              <div className="col-2 col-form-label col-form-label-sm">
                Attachments
              </div>
              <div className="col-9">
                <button
                  className={ `btn btn-secondry cursor-pointer` }
                  id={ `chargeback_claim_attachment_btn` }
                  onClick={ showClaimAttachmentModal }
                  disabled={isDisabled}
                >
                  {`Add Attachment`}
                  <i className={ `fa fa-paperclip cursor-pointer text-muted ml-3` }/>
                </button>
                <DocumentLinkList fieldDocs={getDocumentList()}
                  deleteAttachment={ handleDeleteAttachment }
                  claimType={ 'AUTOMOTIVE' }
                  isFinanceUser={ true }/>
              </div>
              {showAttachmentModal && renderClaimAttachmentModal()}
            </div>
          </div>
        </div>  
        <div className="col-12 form-group row">
          <ChargeBackPaymentInfo claimObject={ claimObject }
            initialStatus={ claimObject.status }/>
        </div>
        <div className="form-group row my-4 justify-content-center">
          <button className="btn btn-secondary mr-3"
            onClick={props.closeModal }>
              Cancel
          </button>
          <button className="btn btn-primary"
            disabled={disableStatus || (initialStatus === CONSTANTS.CHARGEBACK_CLAIM_STATUS_MAP.deactivated)}
            onClick={() => submitChargebackClaim()}>
              Submit
          </button>
        </div>
      </Modal>
    );
  };

  return (
    <div style={{display: "inline"}}>
      {renderClaimModel()}
    </div>
  );
};

ChargbackClaim.contextTypes = {
  router: PropTypes.object.isRequired,
};
    
ChargbackClaim.propTypes = {
  user: PropTypes.shape({
    id: PropTypes.number.isRequired,
    first_name: PropTypes.string.isRequired,
    last_name: PropTypes.string.isRequired,
    email: PropTypes.string.isRequired,
    stores: PropTypes.arrayOf(PropTypes.string.isRequired),
    roles: PropTypes.shape({
      Map: PropTypes.object
    }).isRequired
  }),
  location: PropTypes.shape({
    query: PropTypes.shape({
      page: PropTypes.string,
      q: PropTypes.string,
      userId: PropTypes.string,
      status: PropTypes.string,
      age: PropTypes.string,
      donutFilter: PropTypes.string,
      sortBy: PropTypes.string,
      sortOrder: PropTypes.string,
      payType: PropTypes.string,
    }).isRequired,
  }).isRequired,
  claimObject: PropTypes.object,
  closeModal: PropTypes.func,
};
  
export default ChargbackClaim;