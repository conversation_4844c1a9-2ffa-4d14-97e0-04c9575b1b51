import React from "react";
import PropTypes from "prop-types";
import Complaint from "./Complaint.jsx";

export default class ComplaintsInformation extends React.Component {

  static propTypes = {
    claimObject: PropTypes.object.isRequired,
    isDisabled: PropTypes.bool.isRequired,
    addPartOrLabor: PropTypes.func.isRequired,
    removePartOrLaborPrompt: PropTypes.func.isRequired,
    handleComplaintUpdate: PropTypes.func.isRequired,
    handleComplaintDelete: PropTypes.func.isRequired,
    handlePartLaborUpdate: PropTypes.func.isRequired,
    createNewComplaint: PropTypes.func.isRequired,
    showRequiredNoteError: PropTypes.bool,
    coverageDetails: PropTypes.array,
    hasRO: PropTypes.bool,
    manuallyEnterRO: PropTypes.bool,
    user: PropTypes.shape({
      id: PropTypes.number.isRequired,
      first_name: PropTypes.string.isRequired,
      last_name: PropTypes.string.isRequired,
      email: PropTypes.string.isRequired,
      stores: PropTypes.arrayOf(PropTypes.string.isRequired),
      roles: PropTypes.shape({
        Map: PropTypes.object,
      }).isRequired,
      approval_limit: PropTypes.string,
    })
  };

  constructor(props) {
    super(props);
  }

  renderComplaints = (claimObject, isDisabled) => {
    if (claimObject.complaints && claimObject.complaints.length > 0) {
      return claimObject.complaints.map(this.getComplaints.bind(this, claimObject, isDisabled));
    }
  };

  getComplaints = (claimObject, isDisabled, complaint, index) => {
    return (
      <Complaint complaint={ complaint }
        claimObject={ this.props.claimObject }
        index={ index }
        isDisabled={ isDisabled }
        key={ complaint.id }
        addPartOrLabor={ this.props.addPartOrLabor.bind(this, claimObject) }
        removePartOrLabor={ this.props.removePartOrLaborPrompt.bind(this, claimObject.id) }
        handleComplaintUpdate={ this.props.handleComplaintUpdate }
        handleComplaintDelete={ this.props.handleComplaintDelete }
        handlePartLaborUpdate={ this.props.handlePartLaborUpdate }
        productCode={ claimObject.product_code }
        showRequiredNoteError={ this.props.showRequiredNoteError }
        coverageList={ claimObject.coverage_list }
        coverageDetails={this.props.coverageDetails}
        user={this.props.user}/>
    );
  };

  isROExist = () => {
    return (this.props.hasRO || this.props.manuallyEnterRO);
  };

  onNewComplaint = () => {
    if (!this.props.isDisabled && this.isROExist()) {
      this.props.createNewComplaint(this.props.claimObject);
    }
  };

  render() {
    const iconStyle = { color:"grey" };
    return (
      <section>
        {this.renderComplaints(this.props.claimObject, this.props.isDisabled)}
        <div className="col mt-3"
          style={ (this.props.isDisabled || !this.isROExist()) ? iconStyle : {} }
          id="add-complaint"
          onClick={ this.onNewComplaint }>
          <div className="card">
            <div className={ `card-header d-flex ${this.props.isDisabled ? "" : "cursor-pointer"}` }>
              <span>
                <i className="fa fa-plus-circle mr-2"/>
                  Add new Complaint
              </span>
            </div>
          </div>
        </div>
      </section>
    );
  }
}