import React from "react";
import RentalTableRow from "./RentalTableRow.jsx";
import PropTypes from "prop-types";

const RentalTable = (props) => {

  const renderRentalRows = (rentals) => {
    if (rentals && Array.isArray(rentals) && rentals.length > 0) {
      return rentals.map((rental, index) => <RentalTableRow rental={rental}
        claimObject={props.claimObject}
        key={rental.id}
        complaintIndex={props.index}
        index={index}
        isDisabled={props.isDisabled}
        showRequiredNoteError={props.showRequiredNoteError}
        handlePartLaborUpdate={props.handlePartLaborUpdate}
        removePartOrLabor={props.removePartOrLabor} />);
    }
  };

  return (
    <div className="small mb-4">
      <table className="table table-sm">
        <thead className="thead-light">
          <tr className="row col-12 pr-0">
            <th className="col-4">
              Rental Description
            </th>
            <th className="col">
              Requested
            </th>
            <th className="col" colSpan="2">
              Approved
            </th>
          </tr>
        </thead>
        {renderRentalRows(props.rentals)}
      </table>
      <span className="text-primary cursor-pointer"
        id={`add-labor-${props.index}`}
        onClick={!props.isDisabled ? props.addPartOrLabor : void 0}>
                Add item
      </span>
    </div>
  );
};

RentalTable.propTypes = {
  rentals: PropTypes.array,
  claimObject: PropTypes.object.isRequired,
  handlePartLaborUpdate: PropTypes.func.isRequired,
  addPartOrLabor: PropTypes.func.isRequired,
  removePartOrLabor: PropTypes.func.isRequired,
  isDisabled: PropTypes.bool,
  index: PropTypes.number,
  showRequiredNoteError: PropTypes.bool
};

export default RentalTable;