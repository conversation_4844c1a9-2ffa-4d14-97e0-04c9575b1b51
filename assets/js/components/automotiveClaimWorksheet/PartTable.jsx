import React from "react";
import PartTableRow from "./PartTableRow.jsx";
import PropTypes from "prop-types";

export default class PartTable extends React.Component {

  static propTypes = {
    parts: PropTypes.array,
    handlePartLaborUpdate: PropTypes.func.isRequired,
    addPartOrLabor: PropTypes.func.isRequired,
    removePartOrLabor: PropTypes.func.isRequired,
    isDisabled: PropTypes.bool,
    index: PropTypes.number,
    showRequiredNoteError: PropTypes.bool
  };

  constructor(props) {
    super(props);
  }

  renderPartRows = (parts) => {
    if (parts && Array.isArray(parts) && parts.length > 0) {
      return parts.map((part, index) => <PartTableRow part={ part }
        key={ part.id }
        complaintIndex={ this.props.index }
        index={ index }
        isDisabled={ this.props.isDisabled }
        showRequiredNoteError={ this.props.showRequiredNoteError }
        handlePartLaborUpdate={ this.props.handlePartLaborUpdate }
        removePartOrLabor={ this.props.removePartOrLabor }/>);
    }
  };

  render() {
    return (
      <div className="small mb-4">
        <table className="table table-sm">
          <thead className="thead-light">
            <tr className="row col-12 pr-0">
              <th className="col-2">
              Part Number
              </th>
              <th className="col-3">
              Description
              </th>
              <th className="col-1">
              Quantity
              </th>
              <th className="col">
              Cost
              </th>
              <th className="col">
              MSRP
              </th>
              <th className="col">
              Requested
              </th>
              <th className="col" colSpan="2">
              Approved
              </th>
            </tr>
          </thead>
          {this.renderPartRows(this.props.parts)}
        </table>
        <span className="text-primary cursor-pointer"
          id={ `add-part-${this.props.index}` }
          onClick={ !this.props.isDisabled ? this.props.addPartOrLabor : void 0 }>
          Add item
        </span>
      </div>
    );
  }
}