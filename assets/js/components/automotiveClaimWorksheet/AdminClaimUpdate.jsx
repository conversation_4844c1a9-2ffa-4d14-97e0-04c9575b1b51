import React, { useState } from 'react';
import Modal from "../../Modal.jsx";
import SelectBox from "../reusable/SelectBox/SelectBox.jsx";
import InputBox from "../reusable/InputBox/InputBox.jsx";
import PropTypes from 'prop-types';
import DatePicker from "react-datepicker";
import moment from "moment";
import { CONSTANTS } from "../reusable/Constants/constants.js";
import <PERSON><PERSON> from "react-s-alert";
import { json as ajax } from './../../ajax.js';

const optionList = 
    [
      { name: CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.authorizedCCClaim, value: CONSTANTS.AUTO_CLAIM_STATUS_MAP.authorizedCCClaim, isDisabled: false },
      { name: CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.dealerChargedBack, value: CONSTANTS.AUTO_CLAIM_STATUS_MAP.dealerChargedBack, isDisabled: false },
      { name: CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.ccPaid, value: CONSTANTS.AUTO_CLAIM_STATUS_MAP.ccPaid, isDisabled: false },
      { name: CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.waitingForReversed, value: CONSTANTS.AUTO_CLAIM_STATUS_MAP.waitingForReversed, isDisabled: false },
      { name: CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.waitingForCheck, value: CONSTANTS.AUTO_CLAIM_STATUS_MAP.waitingForCheck, isDisabled: false },
      { name: CONSTANTS.AUTO_CLAIM_STATUS_DISPLAY_MAP.checkWritten, value: CONSTANTS.AUTO_CLAIM_STATUS_MAP.checkWritten, isDisabled: false }
    ];

const AdminClaimUpdate = (props, context) => {
  const [status, setStatus] = useState(props.status);
  const [updateCheck, setUpdateCheck] = useState(false);
  const [checkNumber, setCheckNumber] = useState("");
  const [amount, setAmount] = useState(0.0);
  const [paymentAdjustment, setPaymentAdjustment] = useState(0.0);
  const [date, setDate] = useState(moment());
  const [notes, setNotes] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleStatusChange = (value) => {
    switch (props.status){
    case CONSTANTS.AUTO_CLAIM_STATUS_MAP.waitingForCheck:
    case CONSTANTS.AUTO_CLAIM_STATUS_MAP.waitingForReversed:
      if (value === CONSTANTS.AUTO_CLAIM_STATUS_MAP.checkWritten) {
        setStatus(value);
      } else {
        Alert.error("Invalid status change");
      }
      break;
    default:
      Alert.error("Invalid status change");
    }
  };

  const statusOptions = () => {
    return optionList;
  };
  
  const renderModal = () => {
    let disableSubmit = false;
    if ((props.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.waitingForCheck && status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.checkWritten) ||
       (status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.checkWritten && updateCheck)) {
      if (checkNumber === "" || amount === 0.0 || date === "" ) {
        disableSubmit = true;
      }
    }
    return (
      <Modal visible={props.show}
        size="medium"
        title="Update Payment Information"
        close={props.closeFunc}>
        <div className="row justify-content-md-center">
          <div className="col-8">
            <div className="row my-2">
              <label className="col-4">
                    Status
              </label>
              <div className="col-8">
                <SelectBox
                  id="status-dropdown"
                  value={status}
                  disabled={false}
                  onChange={handleStatusChange}
                  customClassName="form-control-sm"
                  optionsList={statusOptions()} />
              </div>
            </div>
            { props.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.checkWritten ?
              (<>
                <div className="row my-2">
                  <div className="col-4">
                    <input type="checkbox"
                      id="update-check-checkbox"
                      name="update-check-checkbox"
                      value="update-check"
                      checked={updateCheck}
                      onChange={(e) => {setUpdateCheck(!updateCheck); }}
                    />
                  </div>
                  <label className="col-8">
                Update Check
                  </label>
                  <small className='text-muted'>Use this if only you want to update check</small>
                </div>
                <hr />
              </>) : null }
            { (updateCheck || props.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.waitingForCheck ||
              props.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.waitingForReversed) ? (
                <>
                  <div className="row my-2">
                    <label className="col-4">
              Check Amount
                    </label>
                    <div className="col-8">
                      <InputBox type="Number"
                        id="amount-inputBox"
                        customClass=""
                        hasDefaultValue={true}
                        onChange={(value) => {setAmount(value); }}
                      />
                    </div>
                  </div>
                  <div className="row my-2">
                    <label className="col-4">
                    Check#:
                    </label>
                    <div className="col-8">
                      <InputBox type="Text"
                        id="check-number-inputBox"
                        isDisabled={props.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.ccPaid}
                        customClass=""
                        hasDefaultValue={true}
                        onChange={(value) => {setCheckNumber(value); }}
                      />
                    </div>
                  </div>
                  <div className="row my-2">
                    <label className="col-4">
                    Paid Date
                    </label>
                    <div className="col-8">
                      <DatePicker className="form-control"
                        id="begin_date"
                        selected={date}
                        onChange={(date) => {setDate(date); }}
                        required={true}
                      />
                    </div>
                  </div>
                  <hr />
                </>) : null }
            <div className = "row my-2">
              <label className="col-4">
                Payment Adjustment
              </label>
              <div className="col-8">
                <span className={ `input-group input-group-sm col-form-label-sm` }>
                  <div className="input-group-prepend">
                    <span className="input-group-text">$</span>
                  </div>
                  <input type="number"
                    className="form-control form-control-sm"
                    id="payment-adjustment-inputBox"
                    value={paymentAdjustment}
                    onChange={(e) => {setPaymentAdjustment(e.target.value); }}
                  />
                </span>
              </div>
            </div>
            <div className="row my-2">
              <label className="col-4">
                Actual Paid Amount
              </label>
              <div className="col-8">
                <InputBox type="Currency"
                  id="actual-paid-amount-inputBox"
                  customClass=""
                  hasDefaultValue={true}
                  isDisabled={true}
                  value={(parseFloat(props.totalClaimPaid) + parseFloat(paymentAdjustment?paymentAdjustment:0)).toFixed(2)}
                />
              </div>
            </div>
            <div className="row my-2">
              <label className="col-4">
                    Notes
              </label>
              <div className="col-8">
                <textarea className="form-control"
                  id="notes-textarea"
                  rows="3"
                  placeholder="Enter notes here"
                  onChange={(e) => {setNotes(e.target.value); }}
                />
              </div>
            </div>
            <div className="row my-4 justify-content-center">
              <button className="btn btn-secondary mr-3"
                onClick={props.closeFunc}>
                    Cancel
              </button>
              <button className="btn btn-primary"
                onClick={submit}
                disabled={isSubmitting || disableSubmit} >
                    Submit
              </button>
            </div>
          </div>
        </div>
      </Modal>
    );
  };

  const submit = () => {
    let url = `/api/automotive-claims/${props.claimId}/admin-update`;
    setIsSubmitting(true);
    let data = {
      id: props.claimId,
      previous_status: props.status,
      new_status: status,
      update_check: updateCheck,
      check_number: checkNumber,
      amount: amount,
      payment_adjustment: paymentAdjustment,
      actual_paid_amount: parseFloat(props.totalClaimPaid) + parseFloat(paymentAdjustment?paymentAdjustment:0),
      date: date.format("YYYY-MM-DD"),
      notes: notes,
    };

    ajax(url, {...data}, { method: "PUT" }, (data, status) => {
      if (status === 200) {
        Alert.success('Claim updated successfully');
        props.closeFunc();
        props.reloadClaimFunc();
        props.reloadPaymentFunc();
      } else {
        if (data && data.message) {
          Alert.error(data.message);
        } else {
          Alert.error('Error updating claim');
        }
      }
      setIsSubmitting(false);
    });
  };

  return (
    <div>
      {renderModal()}
    </div>
  );
};

AdminClaimUpdate.propTypes = {
  show: PropTypes.bool,
  closeFunc: PropTypes.func,
  reloadClaimFunc: PropTypes.func,
  reloadPaymentFunc: PropTypes.func,
  claimId: PropTypes.number,
  status: PropTypes.string,
  totalClaimPaid: PropTypes.number,
};

  

export default AdminClaimUpdate;
