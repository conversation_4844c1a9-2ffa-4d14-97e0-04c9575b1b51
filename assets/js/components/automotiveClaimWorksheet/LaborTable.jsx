import React from "react";
import LaborTableRow from "./LaborTableRow.jsx";
import PropTypes from "prop-types";

export default class LaborTable extends React.Component {

  static propTypes = {
    labors: PropTypes.array,
    claimObject: PropTypes.object.isRequired,
    handlePartLaborUpdate: PropTypes.func.isRequired,
    addPartOrLabor: PropTypes.func.isRequired,
    removePartOrLabor: PropTypes.func.isRequired,
    isDisabled: PropTypes.bool,
    index: PropTypes.number,
    showRequiredNoteError: PropTypes.bool
  };

  constructor(props) {
    super(props);
  }

  renderLaborRows = (labors) => {
    if (labors && Array.isArray(labors) && labors.length > 0) {
      return labors.map((labor, index) => <LaborTableRow labor={ labor }
        claimObject={ this.props.claimObject }
        key={ labor.id }
        complaintIndex={ this.props.index }
        index={ index }
        isDisabled={ this.props.isDisabled }
        showRequiredNoteError={ this.props.showRequiredNoteError }
        handlePartLaborUpdate={ this.props.handlePartLaborUpdate }
        removePartOrLabor={ this.props.removePartOrLabor }/>);
    }
  };

  render() {
    return (
      <div className="small mb-4">
        <table className="table table-sm">
          <thead className="thead-light">
            <tr className="row col-12 pr-0">
              <th className="col-4">
              Labor Description
              </th>
              <th className="col">
              Requested
              </th>
              <th className="col">
              Hours
              </th>
              <th className="col">
              Rate
              </th>
              <th className="col">
              Billed
              </th>
              <th className="col" colSpan="2">
              Approved
              </th>
            </tr>
          </thead>
          {this.renderLaborRows(this.props.labors)}
        </table>
        <span className="text-primary cursor-pointer"
          id={ `add-labor-${this.props.index}` }
          onClick={ !this.props.isDisabled ? this.props.addPartOrLabor : void 0 }>
          Add item
        </span>
      </div>
    );
  }
}