import React from "react";
import moment from "moment";
import dateFormat from "./../reusable/Utilities/dateFormat.js";
import PropTypes from "prop-types";
import ContractHistoryModal from "./ContractHistoryModal.jsx";
import NoteModal from "./NoteModal.jsx";
import ReactTooltip from 'react-tooltip';
import { getContractCode } from "../automotiveClaimsDashboard/ClaimsList.jsx";
import { getClasses } from "../reusable/Utilities/headerClasses";
import { CONSTANTS } from "./../reusable/Constants/constants.js";
import hstore from '../reusable/Utilities/hstore.js';

export default class Header extends React.Component {

  static contextTypes = {
    router: PropTypes.object.isRequired
  };

  static propTypes = {
    claimObject: PropTypes.shape({
      id: PropTypes.oneOfType([
        PropTypes.number,
        PropTypes.string
      ]).isRequired,
      contract_number: PropTypes.string.isRequired,
      vin: PropTypes.string.isRequired,
      last_updated_by: PropTypes.string.isRequired,
      last_updated_at: PropTypes.string.isRequired,
      customer_name: PropTypes.string.isRequired,
      invoiced_at: PropTypes.string,
    }),
    nextButtonClassName: PropTypes.string,
    nextButtonId: PropTypes.string,
    nextButtonText: PropTypes.string.isRequired,
    nextButtonDisabled: PropTypes.bool,
    nextButtonOnClick: PropTypes.func,
    backButtonOnClick: PropTypes.func,
    extraButtonNodes: PropTypes.func,
    pageTitle: PropTypes.string.isRequired,
    showLastUpdated: PropTypes.bool,
    lastUpdatedBy: PropTypes.string,
    lastUpdatedAt: PropTypes.string,
    claimStatus: PropTypes.string,
    renderChargeBack: PropTypes.func,
    user: PropTypes.shape({
      id: PropTypes.number.isRequired,
      first_name: PropTypes.string.isRequired,
      last_name: PropTypes.string.isRequired,
      email: PropTypes.string.isRequired,
      stores: PropTypes.arrayOf(PropTypes.string.isRequired),
      roles: PropTypes.shape({
        Map: PropTypes.object,
      }).isRequired,
      banner_info: PropTypes.shape({
        header: PropTypes.string.isRequired,
        message: PropTypes.string.isRequired,
        enabled: PropTypes.bool.isRequired,
      }).isRequired,
    })
  };

  constructor(props) {
    super(props);
    this.state = {
      showContractHistory: false,
      showNoteModal: false,
      noteType: ''
    };
  }

  onContractNumberClick = () => {
    // TODO : Change URL name
    window.open(`/gap-contract/${this.props.claimObject.contract_number}`, this.props.claimObject.contract_number, "width=1000,height=600");
  };

  renderLastUpdatedBy = () => {
    if (this.props.showLastUpdated) {
      return (
        <span className="row flex-row-reverse pr-3">
          Last updated by: { `${this.props.claimObject.last_updated_by} - ${moment(new Date(this.props.claimObject.last_updated_at)).format(dateFormat.displayDateFormat)}` }
        </span>
      );
    }
  };

  renderInvoiceDate = () => {
    if (this.props.claimObject.invoiced_at) {
      return (
        <span className="row flex-row">
          <strong>Invoice Date: {`${moment(new Date(this.props.claimObject.invoiced_at)).format(dateFormat.displayDateFormat)}`}</strong>
        </span>
      );
    }
  };

  renderNextButton = () => {
    if (this.props.nextButtonText) {
      return (
        <button
          className={ `btn btn-primary mr-2 cursor-pointer${this.props.nextButtonDisabled  ? " disabled" : ""}` }
          id={ this.props.nextButtonId }
          onClick={ this.props.nextButtonOnClick }
          disabled={ !!this.props.nextButtonDisabled  }>
          <i className={ `fa ${this.props.nextButtonClassName}` }/>
          &nbsp;{this.props.nextButtonText}
        </button>
      );
    }
  };

  closeNoteModal = () => {
    this.setState({ showNoteModal: false, noteType: '' });
  };

  showNotes = (notesType) => {
    this.setState({ showNoteModal: true, noteType: notesType });
  };

  loadPDF = () => {
    window.open(`/api/automotive-claims/${this.props.claimObject.id}/pdf`);
  };

  isViewOnlyRole = () => {
    let r = CONSTANTS.USER_ROLES;
    let u = this.props.user;
    const hasReadRole = hstore.hasAny(u.roles, [r.accounting, r.viewOnlyClaims]);
    const hasWriteRole = hstore.hasAny(u.roles, [r.gapClaims,r.gapClaimsManager]);
    return hasReadRole && !hasWriteRole;
  };

  render() {
    const {
      user : {
        banner_info: bannerInfo,
      },
    } = this.props;
    const classes = getClasses(bannerInfo);
    const pageSpacingStyle = { marginTop: '200px' };
    return (
      <section className="page-header" style={ pageSpacingStyle }>
        <div className={classes}>
          <div className="row align-items-center">
            <div className="col-5 text-truncate h2">
              <h2 className="d-inline-block">{ this.props.pageTitle }&nbsp;-&nbsp;</h2>
              <span data-tip data-for="auto_claim_customer_name">
                {this.props.claimObject.customer_name}
              </span>
              <ReactTooltip id={ `auto_claim_customer_name` } aria-haspopup='true' place="bottom">
                <p className="text-center">{this.props.claimObject.customer_name}</p>
              </ReactTooltip>
            </div>
            <div className="col-2 text-center h3">
              <p className="d-inline-block h4">
                <a href="#"
                  id="link-contract-number"
                  onClick={ this.onContractNumberClick }>{getContractCode(this.props.claimObject.contract_number)}</a>
              </p>
            </div>
            <div className="col-5">
              { this.renderLastUpdatedBy() }
            </div>
          </div>
          <div className="row align-items-center">
            <div className="col-5">
            </div>
            <div className="col-2 col-form-label-sm text-center">
              <label>
                {this.renderInvoiceDate()}
              </label>
            </div>
          </div>
          <div className="row py-3">
            <div className="col-5">
              <button onClick={ this.props.backButtonOnClick }
                id="btn-back"
                className="btn btn-secondary mr-2 cursor-pointer">
                <i className="fa fa-arrow-left"/>
                &nbsp;Back
              </button>
              { this.renderNextButton() }
              { this.props.extraButtonNodes && this.props.extraButtonNodes() }
              { this.props.renderChargeBack()}
            </div>
            <div className="col-2 text-center">
              <span className="h4">
                { this.props.claimStatus }
              </span>
            </div>
            <div className="col-5">
              <div className="row flex-row-reverse pr-3">
                <div className="btn-toolbar">
                  <button type="button" className="btn btn-secondary mr-2" title="Print" 
                    onClick={ this.loadPDF.bind() }>
                    <i className='fa fa-print'/>
                  </button>
                  <button type="button"
                    id="btn-history"
                    className="btn btn-secondary mr-2 cursor-pointer"
                    onClick={ ()=> {
                      this.setState({ showContractHistory: true }, function () {
                        this.contractDetails.loadContractDetails();
                      });
                    } }>
                    History
                  </button>
                  <button type="button"
                    id="btn-activity"
                    className="btn btn-secondary mr-2 cursor-pointer"
                    onClick={ this.showNotes.bind(null, "automated") }>
                    Activity
                  </button>
                  <button type="button"
                    id="btn-notes"
                    className="btn btn-secondary mr-2 cursor-pointer"
                    onClick={ this.showNotes.bind(null, "manual") }>
                    Notes
                  </button>
                  <button type="button"
                    id="btn-contract-notes"
                    className="btn btn-secondary mr-2 cursor-pointer"
                    onClick={ this.showNotes.bind(null, "contract") }>
                    Contract Notes
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
        <ContractHistoryModal claimObject={ this.props.claimObject }
          showContractHistory={ this.state.showContractHistory }
          closeHistoryModal={ ()=> {
            this.setState({ showContractHistory: false });
          } }
          ref={ (contractDetails) => {
            this.contractDetails = contractDetails;
          } }/>
        <NoteModal closeNoteModal={ this.closeNoteModal }
          showNoteModal={ this.state.showNoteModal }
          noteType={ this.state.noteType }
          hideAddNoteTextArea={ this.isViewOnlyRole()  && !hstore.has(this.props.user.roles, CONSTANTS.USER_ROLES.viewOnlyClaims) }
          createdByUserID={ this.props.user.id }
          claimObject={ this.props.claimObject }/>
      </section>
    );
  }
}
