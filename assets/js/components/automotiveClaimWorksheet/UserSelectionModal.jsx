import React from "react";
import Modal from "../../Modal.jsx";
import PropTypes from "prop-types";

export default class UserSelectionModal extends React.Component {

  static propTypes = {
    userList: PropTypes.array.isRequired,
    displayModal: PropTypes.bool.isRequired,
    closeModal: PropTypes.func,
    showUserListLoader: PropTypes.bool.isRequired,
    handleOwnerChange: PropTypes.func.isRequired,
    handleModalSubmit: PropTypes.func.isRequired,
    claimObject: PropTypes.object
  };

  constructor(props) {
    super(props);
  }

  renderUserDropdown = () => {
    if (this.props.userList.length > 0) {
      return this.props.userList.map(this.renderUserList);
    }
  };

  renderUserList = (user) => {
    return (<option value={ user.id } key={ user.id }>{`${user.first_name} ${user.last_name}`}</option>);
  };

  render() {
    return (
      <section>
        <Modal visible={ this.props.displayModal } close={ this.props.closeModal }>
          <div className="my-3">
            <p className={ this.props.showUserListLoader === true ? 'text-center' : 'd-none' }>
              <i className="fa fa-refresh fa-spin"/> Fetching Automotive Claim Manager list
            </p>
            <div
              className={ this.props.showUserListLoader === true ? 'd-none' : 'form-group text-center clearfix' }>
              <label className="form-check-label col-form-label col-form-label-sm">
                Select Owner:&nbsp;
              </label>
              <select className="form-control ml-1 col-7 d-inline-block"
                value={ this.props.claimObject.owner_id }
                onChange={ this.props.handleOwnerChange }>
                <option value=''>Select Owner</option>
                { this.renderUserDropdown() }
              </select>
              <div className="text-center mt-2">
                <button className="btn btn-primary"
                  disabled={ !this.props.claimObject.owner_id }
                  onClick={ this.props.handleModalSubmit }>
                  <i className="fa fa-check"/>&nbsp;Submit
                </button>
              </div>
            </div>
          </div>
        </Modal>
      </section>
    );
  }
}