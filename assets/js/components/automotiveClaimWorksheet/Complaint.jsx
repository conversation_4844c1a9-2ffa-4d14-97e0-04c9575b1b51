import React from "react";
import If from "../reusable/If/If.jsx";
import InputBox from "../reusable/InputBox/InputBox.jsx";
import SelectBox from "../reusable/SelectBox/SelectBox.jsx";
import PartTable from "./PartTable.jsx";
import LaborTable from "./LaborTable.jsx";
import TowingTable from "./TowingTable.jsx";
import SubletTable from "./SubletTable.jsx";
import MiscTable from "./MiscTable.jsx";
import RentalTable from "./RentalTable.jsx";
import ConfirmationModal from "../reusable/ConfirmationModal/ConfirmationModal.jsx";
import moment from "moment";
import dateFormat from "../reusable/Utilities/dateFormat.js";
import PropTypes from "prop-types";
import { CONSTANTS } from "../reusable/Constants/constants.js";
import Select from "react-select";
import { json as ajax } from "./../../ajax.js";
import { URLS as apiUrls } from "./../reusable/Utilities/urls";
import Alert from "react-s-alert";
import { formatCurrency } from "../reusable/Utilities/format";
import { userHasRole } from "../reusable/Utilities/userHasRole";

const optionList = {
  goodWillOptions: [
    { name: '--Select status--', value: "" },
    { name: 'Process Exception', value: "Process Exception" },
    { name: 'Underwriting Period', value: "Underwriting Period" },
    { name: 'Non-Covered Item', value: "Non-Covered Item" },
  ],
  complaintStatus: [
    { name: '--Select status--', value: "" },
    { name: 'Open', value: "Open" },
    { name: 'Payable', value: "Payable" },
    { name: 'Denied', value: "Denied" },
  ],
  complaintDeniedReason: [
    { name: '--Select status--', value: "" },
    { name: CONSTANTS.AUTO_CLAIM_COMPLAINTS_DENIED_REASON.expiredByTime, 
      value: CONSTANTS.AUTO_CLAIM_COMPLAINTS_DENIED_REASON.expiredByTime },
    { name: CONSTANTS.AUTO_CLAIM_COMPLAINTS_DENIED_REASON.expiredByMileage, 
      value: CONSTANTS.AUTO_CLAIM_COMPLAINTS_DENIED_REASON.expiredByMileage },
    { name: CONSTANTS.AUTO_CLAIM_COMPLAINTS_DENIED_REASON.failedInspectionComponent, 
      value: CONSTANTS.AUTO_CLAIM_COMPLAINTS_DENIED_REASON.failedInspectionComponent },
    { name: CONSTANTS.AUTO_CLAIM_COMPLAINTS_DENIED_REASON.notCovered, 
      value: CONSTANTS.AUTO_CLAIM_COMPLAINTS_DENIED_REASON.notCovered },
    { name: CONSTANTS.AUTO_CLAIM_COMPLAINTS_DENIED_REASON.notPreAuthorized,
      value: CONSTANTS.AUTO_CLAIM_COMPLAINTS_DENIED_REASON.notPreAuthorized },
    { name: CONSTANTS.AUTO_CLAIM_COMPLAINTS_DENIED_REASON.stillInFactoryWarranty, 
      value: CONSTANTS.AUTO_CLAIM_COMPLAINTS_DENIED_REASON.stillInFactoryWarranty },
    { name: CONSTANTS.AUTO_CLAIM_COMPLAINTS_DENIED_REASON.underWritingPeriod30, 
      value: CONSTANTS.AUTO_CLAIM_COMPLAINTS_DENIED_REASON.underWritingPeriod30 },
    { name: CONSTANTS.AUTO_CLAIM_COMPLAINTS_DENIED_REASON.underWritingPeriod60, 
      value: CONSTANTS.AUTO_CLAIM_COMPLAINTS_DENIED_REASON.underWritingPeriod60 },
    { name: CONSTANTS.AUTO_CLAIM_COMPLAINTS_DENIED_REASON.underWritingPeriod90,
      value: CONSTANTS.AUTO_CLAIM_COMPLAINTS_DENIED_REASON.underWritingPeriod90 },
    { name: CONSTANTS.AUTO_CLAIM_DENIED_REASON.over60days,
      value: CONSTANTS.AUTO_CLAIM_DENIED_REASON.over60days },
    { name: CONSTANTS.AUTO_CLAIM_COMPLAINTS_DENIED_REASON.other, 
      value: CONSTANTS.AUTO_CLAIM_COMPLAINTS_DENIED_REASON.other },
  ]
};

export default class Complaint extends React.Component {

  static propTypes = {
    complaint: PropTypes.object,
    claimObject: PropTypes.object.isRequired,
    index: PropTypes.number.isRequired,
    handleComplaintUpdate: PropTypes.func.isRequired,
    handleComplaintDelete: PropTypes.func.isRequired,
    handlePartLaborUpdate: PropTypes.func.isRequired,
    addPartOrLabor: PropTypes.func.isRequired,
    removePartOrLabor: PropTypes.func.isRequired,
    productCode: PropTypes.string.isRequired,
    coverageList: PropTypes.array,
    isDisabled: PropTypes.bool,
    showRequiredNoteError: PropTypes.bool,
    coverageDetails: PropTypes.array,
    user: PropTypes.shape({
      id: PropTypes.number.isRequired,
      first_name: PropTypes.string.isRequired,
      last_name: PropTypes.string.isRequired,
      email: PropTypes.string.isRequired,
      stores: PropTypes.arrayOf(PropTypes.string.isRequired),
      roles: PropTypes.shape({
        Map: PropTypes.object,
      }).isRequired,
      approval_limit: PropTypes.string,
    })
  };

  constructor(props) {
    super(props);
    let coverageDetailsExists= false;
    if (props.coverageDetails && props.coverageDetails.length > 0) {
      coverageDetailsExists = true;
    }
    this.state = {
      isExpanded: true,
      coverageDetailsExists: coverageDetailsExists,
      displayDeleteConfirmationModal: false,
      queryString: '',
      searching: false,
      typingTimeOut: 0,
      repairCodes: [],
      showNonCoveredWarning: false,
      uncoveredComponent: false,
      goodWillFlagInitialValue: props.complaint.goodwill_flag,
      goodwillDescription: props.complaint.goodwill_description,
      goodwillAmount: props.complaint.goodwill_amount,
      goodwillUpdated: false,
    };
  }

  static getDerivedStateFromProps (nextProps, prevState) {
    let uncoveredComponent = false;
    if (!prevState.coverageDetailsExists && nextProps.coverageDetails && nextProps.coverageDetails.length > 0) {
      const coverageCode  = nextProps.coverageDetails.find(cmp => cmp.repair_code === nextProps.complaint.repair_code);
      if (coverageCode && coverageCode.repair_code_label.includes('-')) {
        const afterSplitValue = coverageCode.repair_code_label.split('-');
        if (afterSplitValue[0].includes('No')) {
          uncoveredComponent = true;
        }
      }
      return {
        ...prevState,
        uncoveredComponent: uncoveredComponent,
        coverageDetailsExists: true,
      };
    }
    return null;
  }

  calculatePartsLaborTotal = (type) => {
    let total = 0;
    if (this.props.complaint[ type ] && this.props.complaint[ type ].length > 0) {
      for (let index = 0; index < this.props.complaint[ type ].length; index++) {
        total += this.props.complaint[ type ][ index ].approved ? parseFloat(this.props.complaint[ type ][ index ].approved) : 0;
      }
      return formatCurrency(total);
    }
    return 0;
  };

  calculateTotal = () => {
    let total = 0;
    total += parseFloat(this.calculatePartsLaborTotal('parts')) +
      parseFloat(this.calculatePartsLaborTotal('labors')) +
      parseFloat(this.calculatePartsLaborTotal('towings')) +
      parseFloat(this.calculatePartsLaborTotal('rentals')) +
      parseFloat(this.calculatePartsLaborTotal('sublets')) +
      parseFloat(this.calculatePartsLaborTotal('miscs'));

    if ((parseFloat(this.props.complaint.goodwill_amount) === 0 || this.state.goodwillUpdated) && parseFloat(this.props.complaint.goodwill_amount) !== total && this.props.complaint.goodwill_flag){
      this.setState({goodwillUpdated: true}, () => this.props.handleComplaintUpdate(this.props.complaint.id, 'goodwill_amount', total));
    }
    return formatCurrency(total);
  };

  searchByRepairCode = () => {
    this.setState({ searching: true }, function () {
      ajax(`${apiUrls.repairCodes}?q=${encodeURIComponent(this.state.queryString)}`, {}, {}, (data, status) => {
        if (status === 200) {
          this.setState({ repairCodes: data.repair_codes, searching: false });
        } else {
          this.setState({ searching: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  getRepairCodeOptions = () => {
    if (this.props.coverageDetails && this.props.coverageDetails.length > 0) {
      return this.props.coverageDetails.map((element) => {
        return { label: `${element.repair_code_label || element.repair_code}`, value: element.repair_code};
      });
    } else if (this.props.complaint && this.props.complaint.repair_code) {
      let repairCodeArray = [];
      repairCodeArray.push({
        value: this.props.complaint.repair_code,
        label: `${this.props.complaint.repair_code}`
      });
      return repairCodeArray;
    }
  };

  handleGoodwillAmountChange = (v) => {
    this.setState({ goodwillUpdated: false }, this.props.handleComplaintUpdate(this.props.complaint.id, 'goodwill_amount', v));
    if (!this.state.uncoveredComponent && v === '0.00') {
      this.updateComplaintStatusToOpen();
    }
  };

  handleGoodwillDescriptionChange = (v) => {
    this.props.handleComplaintUpdate(this.props.complaint.id, 'goodwill_description', v);
    if (!this.state.uncoveredComponent && v === '') {
      this.updateComplaintStatusToOpen();
    }
  };

  handleGoodwillFlagChange = (v) => {
    this.props.handleComplaintUpdate(this.props.complaint.id, 'goodwill_flag', !this.props.complaint.goodwill_flag);
    if (!this.state.uncoveredComponent && v.target.checked) {
      this.updateComplaintStatusToOpen();
    }

    if (!this.state.uncoveredComponent && !v.target.checked) {
      this.props.handleComplaintUpdate(this.props.complaint.id, 'goodwill_description', this.state.goodwillDescription || '');
      this.props.handleComplaintUpdate(this.props.complaint.id, 'goodwill_amount', this.state.goodwillAmount || 0);
    }
  }

  updateComplaintStatusToOpen = () => {
    this.props.handleComplaintUpdate(this.props.complaint.id, 'status', CONSTANTS.AUTO_CLAIM_STATUS_MAP.open);
  };

  handleComplaintStatusChange = (value) => {
    const {
      uncoveredComponent
    } = this.state;

    if (value === CONSTANTS.AUTO_CLAIM_STATUS_MAP.payable && (uncoveredComponent || this.props.complaint.goodwill_flag)) {
      if (!this.props.complaint.goodwill_flag) {
        Alert.warning("Please select exception check.");
        return;
      } else if (this.props.complaint.goodwill_description === "") {
        Alert.warning("Please select exception status details.");
        return;
      } else if (parseFloat(this.props.complaint.goodwill_amount) === 0) {
        Alert.warning("Please insert valid exception amount.");
        return;
      }
    }

    if((userHasRole(this.props.user, CONSTANTS.USER_ROLES.autoClaims)
      && !userHasRole(this.props.user, CONSTANTS.USER_ROLES.autoClaimsManager))
      && value === CONSTANTS.AUTO_CLAIM_STATUS_MAP.payable && uncoveredComponent) {
      this.setState({showNonCoveredWarning: true});
      return;
    }

    if (value === CONSTANTS.AUTO_CLAIM_STATUS_MAP.denied && this.props.complaint.goodwill_flag) {
      this.props.handleComplaintUpdate(this.props.complaint.id, 'goodwill_flag', false);
      this.props.handleComplaintUpdate(this.props.complaint.id, 'goodwill_description', '');
      this.props.handleComplaintUpdate(this.props.complaint.id, 'goodwill_amount', 0);
    }

    this.props.handleComplaintUpdate(this.props.complaint.id, 'status', value);
  };

  handleComplaintStatusReasonChange = (value) => {
    if (this.props.complaint.status !== CONSTANTS.AUTO_CLAIM_STATUS_MAP.denied) {
      Alert.warning("Please enter valid status.");
      return;
    }

    this.props.handleComplaintUpdate(this.props.complaint.id, 'status_reason', value);
  };

  handleComplaintStatusOtherReasonChange = (value) => {
    if (this.props.complaint.status_reason !== CONSTANTS.AUTO_CLAIM_COMPLAINTS_DENIED_REASON.other) {
      Alert.warning("Please enter valid status reason.");
      return;
    }

    this.props.handleComplaintUpdate(this.props.complaint.id, 'status_other_reason', value);
  };

  closeCoveredWarningModal = () => this.setState({showNonCoveredWarning: false });

  renderNonCoveredWarning = () => {
    return (
      <ConfirmationModal confirmButtonText="Ok"
        type={"warning"}
        displayConfirmationModal={ this.state.showNonCoveredWarning }
        displayMessage="This claim includes Non-Covered Repair Items. Please assign it to Manager."
        onConfirm={ this.closeCoveredWarningModal }/>
    );
  }

  onRepairCodeSelection = (repairCodeObject) => {
    let uncoveredComponent = false;
    if (repairCodeObject.label.includes('-')) {
      const afterSplitValue = repairCodeObject.label.split('-');
      if (afterSplitValue[0].includes('No')) {
        uncoveredComponent = true;
      }
    }
    
    this.setState({uncoveredComponent});

    if((userHasRole(this.props.user, CONSTANTS.USER_ROLES.autoClaims) 
      && !userHasRole(this.props.user, CONSTANTS.USER_ROLES.autoClaimsManager))
      && this.props.complaint.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.payable && uncoveredComponent) {
      this.props.handleComplaintUpdate(this.props.complaint.id, 'status', CONSTANTS.AUTO_CLAIM_STATUS_MAP.open);
    }

    if (uncoveredComponent) {
      // If non covered componet set the value to true
      this.props.handleComplaintUpdate(this.props.complaint.id, 'goodwill_flag', true);
    } else {
      // If covered component with previous value as false then set the previous value
      this.props.handleComplaintUpdate(this.props.complaint.id, 'goodwill_flag', this.state.goodWillFlagInitialValue || false);
      this.props.handleComplaintUpdate(this.props.complaint.id, 'goodwill_description', this.state.goodwillDescription || '');
      this.props.handleComplaintUpdate(this.props.complaint.id, 'goodwill_amount', this.state.goodwillAmount || 0);
    }

    if (repairCodeObject) {
      this.props.handleComplaintUpdate(this.props.complaint.id, 'repair_code', repairCodeObject.value);
    } else {
      this.props.handleComplaintUpdate(this.props.complaint.id, 'repair_code', '');
    }
  };

  renderCalculation = () => {
    return (
      <div>
        <div className="row">
          <div className="form-group col-2">
            <div className="d-flex justify-content-around">
              <div className="pr-1">
                <label
                  className="col-form-label">
                  Parts
                </label>
              </div>
              <InputBox type="Currency"
                id={`parts-${this.props.index}-inputBox`}
                isDisabled={true}
                value={this.calculatePartsLaborTotal('parts')} />
            </div>
          </div>
          <div className="form-group col-2">
            <div className="d-flex justify-content-around">
              <div className="pr-1">
                <label
                  className="col-form-label">
                  Labor
                </label>
              </div>
              <InputBox type="Currency"
                id={`labor-${this.props.index}-inputBox`}
                isDisabled={true}
                value={this.calculatePartsLaborTotal('labors')} />
            </div>
          </div>
          <div className="form-group col-2">
            <div className="d-flex justify-content-around">
              <div className="pr-1">
                <label
                  className="col-form-label">
                  Towing
                </label>
              </div>
              <InputBox type="Currency"
                id={`towing-${this.props.index}-inputBox`}
                customClass=""
                hasDefaultValue={true}
                isDisabled={true}
                value={this.calculatePartsLaborTotal('towings')} />
            </div>
          </div>
          <div className="form-group col-2">
            <div className="d-flex justify-content-around">
              <div className="pr-1">
                <label
                  className="col-form-label">
                  Rental
                </label>
              </div>
              <InputBox type="Currency"
                id={`rental-${this.props.index}-inputBox`}
                customClass=""
                isDisabled={true}
                hasDefaultValue={true}
                value={this.calculatePartsLaborTotal('rentals')} />
            </div>
          </div>
          <div className="form-group col-2">
            <div className="d-flex justify-content-around">
              <div className="pr-1">
                <label
                  className="col-form-label">
                  Sublet
                </label>
              </div>
              <InputBox type="Currency"
                id={`sublet-${this.props.index}-inputBox`}
                hasDefaultValue={true}
                customClass=""
                isDisabled={true}
                value={this.calculatePartsLaborTotal('sublets')} />
            </div>
          </div>
          <div className="form-group col-2">
            <div className="d-flex justify-content-around">
              <div className="pr-1">
                <label
                  className="col-form-label">
                  Misc
                </label>
              </div>
              <InputBox type="Currency"
                id={`misc-${this.props.index}-inputBox`}
                hasDefaultValue={true}
                customClass=""
                isDisabled={true}
                value={this.calculatePartsLaborTotal('miscs')} />
            </div>
          </div>
        </div>
        <div className="row">
          <div className="form-group col-3 ml-auto">
            <div className="d-flex justify-content-around">
              <div className="pr-3">
                <label
                  className="col-form-label">
                Total
                </label>
              </div>
              <InputBox type="Currency"
                id={ `total-${this.props.index}-inputBox` }
                isDisabled={ true }
                value={ this.calculateTotal() }/>
            </div>
          </div>
        </div>
      </div>
    

    );
  };

  getComplaintByCoverage = () => {
    for (let i = 0; i < this.props.coverageList.length; i++) {
      if (this.props.coverageList[ i ].value) {
        if (this.props.coverageList[ i ].flag === CONSTANTS.AUTO_CLAIM_COVERAGE_FLAG_DISPLAY_NAME.Paint) {
          this.props.handleComplaintUpdate(this.props.complaint.id, 'complaint', CONSTANTS.AUTO_CLAIM_COMPLAINTS.Exterior);
          return CONSTANTS.AUTO_CLAIM_COMPLAINTS.Exterior;
        } else if (this.props.coverageList[ i ].flag === CONSTANTS.AUTO_CLAIM_COVERAGE_FLAG_DISPLAY_NAME.Fabric ||
          this.props.coverageList[ i ].flag === CONSTANTS.AUTO_CLAIM_COVERAGE_FLAG_DISPLAY_NAME.LeatherOrVinyl) {
          this.props.handleComplaintUpdate(this.props.complaint.id, 'complaint', CONSTANTS.AUTO_CLAIM_COMPLAINTS.Interior);
          return CONSTANTS.AUTO_CLAIM_COMPLAINTS.Interior;
        } else if (this.props.coverageList[ i ].flag === CONSTANTS.AUTO_CLAIM_COVERAGE_FLAG_DISPLAY_NAME.DentAndDing) {
          this.props.handleComplaintUpdate(this.props.complaint.id, 'complaint', CONSTANTS.AUTO_CLAIM_COMPLAINTS.DentAndDing);
          return CONSTANTS.AUTO_CLAIM_COMPLAINTS.DentAndDing;
        }
      }
    }
  };

  renderComplaint = () => {
    if (this.props.productCode === CONSTANTS.PRODUCT_CODE_NEW_MAP.century) {
      return (
        <div className="col-4">
          <strong>
            {this.props.complaint.complaint ? this.props.complaint.complaint : this.getComplaintByCoverage()}
          </strong>
        </div>
      );
    } else {
      return (
        <InputBox type="Text"
          id={ `complaint-${this.props.index}-inputBox` }
          isDisabled={ this.props.isDisabled }
          customClass="col-10"
          value={ this.props.complaint.complaint }
          onChange={ this.props.handleComplaintUpdate.bind(null, this.props.complaint.id, 'complaint') }/>
      );
    }
  };

  renderComplaintDetails = () => {
    return (
      <div>
        <div className="form-group row">
          <div className="col-2">
            <label
              className="col-form-label">
                Tech Id
            </label>
          </div>
          <InputBox
            type="Text"
            id={ `cause-${this.props.index}-inputBox-tech_id` }
            isDisabled={ this.props.isDisabled }
            customClass="col-10"
            value={ this.props.complaint.tech_id }
            onChange={ this.props.handleComplaintUpdate.bind(null, this.props.complaint.id, 'tech_id') }/>
        </div>
        <div className="form-group row">
          <div className="col-2">
            <label
              className="col-form-label">
              Complaint
            </label>
          </div>
          {this.renderComplaint()}
        </div>
        <div className="form-group row">
          <div className="col-2">
            <label
              className="col-form-label">
              Cause
            </label>
          </div>
          <InputBox type="Text"
            id={ `cause-${this.props.index}-inputBox` }
            isDisabled={ this.props.isDisabled }
            customClass="col-10"
            value={ this.props.complaint.cause }
            onChange={ this.props.handleComplaintUpdate.bind(null, this.props.complaint.id, 'cause') }/>
        </div>
        <div className="form-group row">
          <div className="col-2">
            <label
              className="col-form-label">
              Correction
            </label>
          </div>
          <InputBox type="Text"
            id={ `correction-${this.props.index}-inputBox` }
            isDisabled={ this.props.isDisabled }
            customClass="col-10"
            value={ this.props.complaint.correction }
            onChange={ this.props.handleComplaintUpdate.bind(null, this.props.complaint.id, 'correction') }/>
        </div>
        <div className="form-group row">
          <div className="col-2">
            <label
              className="col-form-label">
              Repair Code
            </label>
          </div>
          <div className="col-4">
            <Select value={ this.props.complaint.repair_code }
              id={ `repairCode-${this.props.index}-inputBox` }
              disabled={ this.props.isDisabled }
              isLoading={ this.state.searching }
              options={ this.getRepairCodeOptions() }
              onChange={ this.onRepairCodeSelection }
              menuContainerStyle={ { zIndex: 10 } }
              tabSelectsValue={ false }
              matchProp='label'/>
          </div>
          <label
            className="form-check form-check-label col-form-label col no-gutters">
            <input type="checkbox"
              className="form-check-input"
              id={ `addLine-${this.props.index}-checkBox` }
              disabled={ this.props.isDisabled }
              checked={ this.props.complaint.add_line_flag }
              onChange={ this.props.handleComplaintUpdate.bind(null, this.props.complaint.id, 'add_line_flag', !this.props.complaint.add_line_flag) }/>
            <span>Add Line</span>
          </label>
          <label
            className="form-check form-check-label col-form-label col">
            <input type="checkbox"
              className="form-check-input"
              id={ `goodwill-${this.props.index}-checkBox` }
              disabled={this.props.isDisabled || (this.props.complaint.status === "Denied")}
              checked={ this.props.complaint.goodwill_flag }
              onChange={(e) => this.handleGoodwillFlagChange(e) }/>
            <span>Exception</span>
          </label>
          <If condition={ this.props.complaint.goodwill_flag }>
            <div className="col-4 form-group row">
              <div className="col-7">
                <SelectBox
                  id={ `goodwill-${this.props.index}-dropdown` }
                  value={ this.props.complaint.goodwill_description }
                  disabled={ this.props.isDisabled }
                  onChange={(e) => this.handleGoodwillDescriptionChange(e) }
                  optionsList={ optionList.goodWillOptions }/>
              </div>
              <InputBox type="Currency"
                id={ `goodwill-${this.props.index}-inputBox` }
                customClass="col-5"
                hasDefaultValue={ true }
                isDisabled={ this.props.isDisabled }
                value={ formatCurrency(this.props.complaint.goodwill_amount) }
                onBlur={ (e) => this.handleGoodwillAmountChange(e) }/>
            </div>
          </If>
        </div>
      </div>
    );
  };

  handleLineItemUpdate = (complaintId, type, partId, field, value) => {
    const currentItem = this.getCurrentItem(type, partId);
    if (field === "approved" && currentItem) {
      if (type === "labors") {
        if (parseFloat(value) > parseFloat(currentItem.billed)) {
          this.updateComplaintStatusToOpen();
        }
      } else {
        if (parseFloat(value) > parseFloat(currentItem.requested)) {
          this.updateComplaintStatusToOpen();
        }
      }
    } else if ((field === "requested" || field === "billed") && currentItem) {
      if (parseFloat(value) < parseFloat(currentItem.approved)) {
        this.updateComplaintStatusToOpen();
      }
    }
    this.props.handlePartLaborUpdate(complaintId, type, partId, field, value);
  }

  getCurrentItem = (type, id) => {
    const itemList = this.props.complaint[type];
    if (itemList) {
      return itemList.find(obj => obj.id === id);
    }
    return null;
  }

  render() {
    let repairCodeDescription = '';

    if (this.state.repairCodes) {
      const selectedRepairCode = this.state.repairCodes.filter(ele => ele.repair_code === this.props.complaint.repair_code);

      if (selectedRepairCode && selectedRepairCode.length) {
        repairCodeDescription = `- ${selectedRepairCode[ 0 ].repair_code}`;
      }
    }

    if (!repairCodeDescription && this.props.complaint && this.props.complaint.repair_code &&
      this.props.complaint.repair_code_description) {
      repairCodeDescription = `- ${this.props.complaint.repair_code}`;
    }

    return (
      <div className="col mt-3">
        <div className="card">
          <div className="card-header d-flex cursor-pointer"
            id={ `complaint-${this.props.index}` }
            onClick={ () => {
              this.setState({ isExpanded: !this.state.isExpanded });
            } }>
            <div className="col-8">
              <span className="align-middle">
                {`Complaint ${parseInt(this.props.index) + 1} ${repairCodeDescription}`}
              </span>
            </div>
            <div className="col-4 ml-auto row justify-content-end">
              <div className="d-inline-block form-group mb-0">
                {this.props.complaint.status && <span>{`${this.props.complaint.status} - `}</span>}
              </div>
              <div className="d-inline-block ml-2">
                <span className="align-middle">
                  {moment.utc(this.props.complaint.complaint_date, dateFormat.backendDateFormat).format(dateFormat.displayDateFormat)}
                  <If condition={ !this.props.isDisabled }>
                    <i className="fa fa-window-close-o ml-2 cursor-pointer" title="Delete" onClick={ () => {
                      this.setState({ displayDeleteConfirmationModal: true });
                    } }/>
                  </If>
                </span>
              </div>
            </div>
          </div>
          <If condition={ this.state.isExpanded }>
            <div className="card-body">
              {this.renderComplaintDetails()}
              <div className="row mt-5">
                <div className="col-12">
                  <PartTable parts={ this.props.complaint.parts }
                    index={ this.props.index }
                    isDisabled={ this.props.isDisabled }
                    showRequiredNoteError={ this.props.showRequiredNoteError }
                    handlePartLaborUpdate={(type, partId, field, value) => this.handleLineItemUpdate(this.props.complaint.id, type, partId, field, value)}
                    addPartOrLabor={ this.props.addPartOrLabor.bind(null, this.props.complaint.id, 'parts') }
                    removePartOrLabor={ this.props.removePartOrLabor.bind(null, this.props.complaint.id, 'parts') }/>
                  <LaborTable labors={ this.props.complaint.labors }
                    claimObject={ this.props.claimObject }
                    index={ this.props.index }
                    isDisabled={ this.props.isDisabled }
                    showRequiredNoteError={ this.props.showRequiredNoteError }
                    handlePartLaborUpdate={(type, partId, field, value) => this.handleLineItemUpdate(this.props.complaint.id, type, partId, field, value)}
                    addPartOrLabor={ this.props.addPartOrLabor.bind(null, this.props.complaint.id, 'labors') }
                    removePartOrLabor={this.props.removePartOrLabor.bind(null, this.props.complaint.id, 'labors')} />
                  <TowingTable towings={this.props.complaint.towings}
                    claimObject={this.props.claimObject}
                    index={this.props.index}
                    isDisabled={this.props.isDisabled}
                    showRequiredNoteError={this.props.showRequiredNoteError}
                    handlePartLaborUpdate={(type, partId, field, value) => this.handleLineItemUpdate(this.props.complaint.id, type, partId, field, value) }
                    addPartOrLabor={this.props.addPartOrLabor.bind(null, this.props.complaint.id, 'towings')}
                    removePartOrLabor={this.props.removePartOrLabor.bind(null, this.props.complaint.id, 'towings')} />
                  <RentalTable rentals={this.props.complaint.rentals}
                    claimObject={this.props.claimObject}
                    index={this.props.index}
                    isDisabled={this.props.isDisabled}
                    showRequiredNoteError={this.props.showRequiredNoteError}
                    handlePartLaborUpdate={(type, partId, field, value) => this.handleLineItemUpdate(this.props.complaint.id, type, partId, field, value) }
                    addPartOrLabor={this.props.addPartOrLabor.bind(null, this.props.complaint.id, 'rentals')}
                    removePartOrLabor={this.props.removePartOrLabor.bind(null, this.props.complaint.id, 'rentals')} />
                  <SubletTable sublets={this.props.complaint.sublets}
                    claimObject={this.props.claimObject}
                    index={this.props.index}
                    isDisabled={this.props.isDisabled}
                    showRequiredNoteError={this.props.showRequiredNoteError}
                    handlePartLaborUpdate={(type, partId, field, value) => this.handleLineItemUpdate(this.props.complaint.id, type, partId, field, value) }
                    addPartOrLabor={this.props.addPartOrLabor.bind(null, this.props.complaint.id, 'sublets')}
                    removePartOrLabor={this.props.removePartOrLabor.bind(null, this.props.complaint.id, 'sublets')} />
                  <MiscTable miscs={this.props.complaint.miscs}
                    claimObject={this.props.claimObject}
                    index={this.props.index}
                    isDisabled={this.props.isDisabled}
                    showRequiredNoteError={this.props.showRequiredNoteError}
                    handlePartLaborUpdate={(type, miscId, field, value) => this.handleLineItemUpdate(this.props.complaint.id, type, miscId, field, value)}
                    addPartOrLabor={() => this.props.addPartOrLabor(this.props.complaint.id, 'miscs')}
                    removePartOrLabor={(id) => this.props.removePartOrLabor(this.props.complaint.id, 'miscs', id)} />
                </div>
              </div>
              {this.renderCalculation()}
              <div className="row justify-content-end">
                <div className="col-1">
                  Status
                </div>
                <div className="col-3">
                  <SelectBox
                    id={ `status-${this.props.index}-dropdown` }
                    disabled={ this.props.isDisabled }
                    customClassName="form-control-sm"
                    value={ this.props.complaint.status }
                    onChange={ this.handleComplaintStatusChange }
                    optionsList={ optionList.complaintStatus }/>
                </div>
              </div>
              &nbsp;
              {this.props.complaint.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.denied &&
              (<div>
                <div className="row justify-content-end">
                  <div className="col-1">
                  Reason
                  </div>
                  <div className="col-3">
                    <SelectBox
                      id={`status-reason${this.props.index}-dropdown`}
                      disabled={this.props.isDisabled}
                      customClassName="form-control-sm"
                      value={this.props.complaint.status_reason  || ''}
                      onChange={this.handleComplaintStatusReasonChange}
                      optionsList={optionList.complaintDeniedReason}/>
                  </div>
                </div>
                {!this.props.complaint.status_reason && (
                  <div className="row justify-content-end">
                    <div className="col-3 text-danger text-sm-left">Reason is required</div>
                  </div>)}
              </div>)}
              &nbsp;
              {this.props.complaint.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.denied && 
              this.props.complaint.status_reason === CONSTANTS.AUTO_CLAIM_COMPLAINTS_DENIED_REASON.other &&
              (<div>
                <div className="row justify-content-end">
                  <div className="col-2 text-right">
                  Denial Note
                  </div>
                  <div className="col-3">
                    <InputBox type="Text"
                      id={`status-other-reason${this.props.index}`}
                      className="form-control" 
                      disabled={this.props.isDisabled}
                      value={this.props.complaint.status_other_reason || ''}
                      onChange={this.handleComplaintStatusOtherReasonChange} />
                  </div>
                </div>
                {!this.props.complaint.status_other_reason && (
                  <div className="row justify-content-end">
                    <div className="col-3 text-danger text-sm-left">Denial note is required</div>
                  </div>)}
              </div>)}
            </div>
          </If>
          <ConfirmationModal
            displayConfirmationModal={ this.state.displayDeleteConfirmationModal }
            displayMessage="Are you sure you want to delete this complaint?"
            onConfirm={ () => {
              this.setState({ displayDeleteConfirmationModal: false }, () => {
                this.props.handleComplaintDelete(this.props.complaint.id);
              });
            } }
            onDecline={ () => {
              this.setState({ displayDeleteConfirmationModal: false });
            } }
            type="warning"
            confirmButtonText="Yes"
            declineButtonText="Cancel"/>
          {this.state.showNonCoveredWarning && this.renderNonCoveredWarning()}
        </div>
      </div>
    );
  }
}
