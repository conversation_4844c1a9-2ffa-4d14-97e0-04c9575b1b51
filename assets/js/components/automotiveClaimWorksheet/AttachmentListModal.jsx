import React from "react";
import Modal from "./../../Modal.jsx";
import DocumentLinkList from "../reusable/DocumentLinkList/DocumentLinkList.jsx";
import If from "../reusable/If/If.jsx";
import { CONSTANTS } from "../reusable/Constants/constants.js";
import PropTypes from 'prop-types';

export default class AttachmentListModal extends React.Component {

  static propTypes = {
    displayAttachmentListModal: PropTypes.bool.isRequired,
    closeModal: PropTypes.func.isRequired,
    attachments: PropTypes.array.isRequired,
    handleDeleteAttachment: PropTypes.func,
    isContractAttachment: PropTypes.bool,
    isInspectionAttachments: PropTypes.bool,
    contractId: PropTypes.number,
  };

  render() {
    let title = "Automotive Attachments";
    if (this.props.isContractAttachment) {
      title = "Contract Attachments";
    } else if (this.props.isInspectionAttachments) {
      title = "Inspection Attachments";
    }
    return (
      <section>
        <Modal visible={ this.props.displayAttachmentListModal } size="large" close={ this.props.closeModal }>
          <div>
            <p className="h5">{ title }</p>
            <If condition={ this.props.attachments && this.props.attachments.length > 0 }>
              <div>
                <DocumentLinkList
                  deleteAttachment={ this.props.handleDeleteAttachment }
                  fieldDocs={ this.props.attachments }
                  claimType={ CONSTANTS.CLAIM_TYPE.automotive }
                  isContractAttachment={ this.props.isContractAttachment }
                  contractId={ this.props.contractId }
                />
              </div>
            </If>
            <If condition={ !this.props.attachments || this.props.attachments.length === 0 }>
              <p className="text-center">No Attachments available</p>
            </If>
          </div>
        </Modal>
      </section>
    );
  }
}