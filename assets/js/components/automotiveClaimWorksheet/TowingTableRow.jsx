import React from "react";
import InputBox from "../reusable/InputBox/InputBox.jsx";
import If from "../reusable/If/If.jsx";
import PropTypes from "prop-types";
import Select from "react-select";
import { formatCurrency } from "../reusable/Utilities/format.js";

const TowingTableRow = (props) => {
  
  const ZINDEX = 10;
  
  const onBlur = (field, value) => {
    let approvedAmount = parseFloat(props.towing.approved),
      billedAmount = parseFloat(props.towing.billed);

    if (field === 'requested') {
      billedAmount = getRequestedAmount(props.claimObject, props.towing);
      props.handlePartLaborUpdate('towings', props.towing.id, 'billed', billedAmount.toFixed(2));
    } else if (field === 'approved') {
      approvedAmount = parseFloat(value);
      props.handlePartLaborUpdate('towings', props.towing.id, field, approvedAmount.toFixed(2));
    } else if (field === 'billed') {
      billedAmount = parseFloat(value);
      props.handlePartLaborUpdate('towings', props.towing.id, field, billedAmount.toFixed(2));
    }
  };

  const getRequestedAmount = ({ requested }) => {
    if (requested) {
      return parseFloat(requested);
    } else {
      return 0;
    }
  };

  const getOptions = () => {
    return [];
  };

  const onTowingNoteChange = (laborObject) => {
    if (laborObject) {
      props.handlePartLaborUpdate('towings', props.towing.id, 'notes', laborObject.value);
    } else {
      props.handlePartLaborUpdate('towings', props.towing.id, 'notes', '');
    }
  };

  const billed = parseFloat(props.towing.requested);
  const approved = parseFloat(props.towing.approved);
  let borderStyle = {};

  if (billed < approved) {
    borderStyle = {
      "border": "1px solid #dc3545",
      "borderRadius": "0.2rem"
    };
  }

  let selectedNoteOption;
  const note = props.towing.notes;

  if (note) {
    selectedNoteOption = {
      label: note,
      value: note
    };
  }

  return (
    <tbody className="border-top-0">
      <tr className="row col-12 pr-0">
        <td className="col-4 border-bottom-0">
          <InputBox type="Text"
            autoExpand={true}
            id={`complaint-${props.complaintIndex}-towing-description-inputBox-${props.index}`}
            isDisabled={props.isDisabled}
            value={props.towing.description}
            onChange={props.handlePartLaborUpdate.bind(null, 'towings', props.towing.id, 'description')} />
        </td>
        <td className="col border-bottom-0">
          <InputBox type="Currency"
            hasDefaultValue={true}
            id={`complaint-${props.complaintIndex}-requested-inputBox-${props.index}`}
            isDisabled={props.isDisabled}
            value={props.towing.requested}
            onChange={props.handlePartLaborUpdate.bind(null, 'towings', props.towing.id, 'requested')}
            onBlur={onBlur.bind(null, 'requested')} />
        </td>
        <td className="col border-bottom-0">
          <InputBox type="Currency"
            style={borderStyle}
            hasDefaultValue={true}
            id={`complaint-${props.complaintIndex}-approved-inputBox-${props.index}`}
            isDisabled={props.isDisabled}
            value={formatCurrency(props.towing.approved)}
            onBlur={onBlur.bind(null, 'approved')} />
        </td>
        <td className="h5">
          <i className="fa fa-times cursor-pointer"
            id={`complaint-${props.complaintIndex}-delete-towing-${props.index}`}
            onClick={!props.isDisabled ? props.removePartOrLabor.bind(null, props.towing.id) : () => { }} />
        </td>
      </tr>
      <If condition={billed > approved}>
        <tr className="row col-12">
          <td className="col border-top-0">
            <Select.Creatable value={selectedNoteOption}
              id={`complaint-${props.complaintIndex}-towing-note-inputBox-${props.index}`}
              disabled={props.isDisabled}
              options={getOptions()}
              onChange={onTowingNoteChange}
              menuContainerStyle={{ zIndex: ZINDEX }}
              placeholder="Type or select reason"
              tabSelectsValue={false}
              matchProp='label' />
            <If condition={props.showRequiredNoteError && !props.towing.notes}>
              <span className="text-danger">Required Note for approved amount less than requested amount.</span>
            </If>
          </td>
        </tr>
      </If>
      <If condition={billed < approved}>
        <tr className="row col-12">
          <td className="col border-top-0 text-right">
            <span className="text-danger">Approved amount is greater than requested amount.</span>
          </td>
        </tr>
      </If>
    </tbody>
  );
};

TowingTableRow.propTypes = {
  towing: PropTypes.object,
  claimObject: PropTypes.object.isRequired,
  index: PropTypes.number,
  complaintIndex: PropTypes.number,
  handlePartLaborUpdate: PropTypes.func.isRequired,
  removePartOrLabor: PropTypes.func.isRequired,
  isDisabled: PropTypes.bool,
  showRequiredNoteError: PropTypes.bool
};

export default TowingTableRow;