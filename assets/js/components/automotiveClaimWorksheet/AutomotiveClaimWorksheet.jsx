import React from "react";
import Alert from "react-s-alert";
import Loader from "react-loader-advanced";
import Header from "./Header.jsx";
import immstruct from "immstruct";
import Immutable from "immutable";
import { json as ajax, raw as ajaxRaw } from "./../../ajax.js";
import dateFormat from "./../reusable/Utilities/dateFormat.js";
import { URLS as apiUrls } from "./../reusable/Utilities/urls";
import moment from "moment";
import ConfirmationModal from "../reusable/ConfirmationModal/ConfirmationModal.jsx";
import PropTypes from "prop-types";
import { CONSTANTS } from "../reusable/Constants/constants.js";
import ClaimSummary from "./ClaimSummary.jsx";
import CustomerInformation from "./CustomerInformation.jsx";
import ContractInformation from "./ContractInformation.jsx";
import ComplaintsInformation from "./ComplaintsInformation.jsx";
import ROInformation from "./ROInformation.jsx";
import FacilityInformation from "./FacilityInformation.jsx";
import UserSelectionModal from "./UserSelectionModal.jsx";
import ROSelectionModal from "./ROSelectionModal.jsx";
import { userHasRole } from "../reusable/Utilities/userHasRole";
import ChargbackClaim from "./ChargeBackClaim.jsx";
import hstore from '../reusable/Utilities/hstore.js';

export default class AutomotiveClaimWorksheet extends React.Component {

  IMMS_KEY = 'automotive_claim';

  static contextTypes = {
    router: PropTypes.object.isRequired
  };

  static propTypes = {
    location: PropTypes.shape({
      query: PropTypes.shape({
        id: PropTypes.string.isRequired,
        chargeback: PropTypes.string,
      }).isRequired,
    }).isRequired,
    user: PropTypes.shape({
      id: PropTypes.number.isRequired,
      first_name: PropTypes.string.isRequired,
      last_name: PropTypes.string.isRequired,
      email: PropTypes.string.isRequired,
      stores: PropTypes.arrayOf(PropTypes.string.isRequired),
      roles: PropTypes.shape({
        Map: PropTypes.object,
      }).isRequired,
      banner_info: PropTypes.shape({
        header: PropTypes.string.isRequired,
        message: PropTypes.string.isRequired,
        enabled: PropTypes.bool.isRequired,
      }).isRequired,
    })
  };

  static TIMEFORAUTOSAVE = 2 * 60 * 1000;

  constructor(props) {
    super(props);
    this.automotive_claim = immstruct(
      this.IMMS_KEY,
      {
        id: '',
        vin: '',
        contract_number: '',
        status: '',
        customer_name: '',
        last_updated_by: '',
        last_updated_at: '',
        street_address: '',
        city: '',
        state: '',
        postal_code: '',
        phone_number: '',
        model: '',
        make: '',
        year: '',
        beginning_miles: '',
        ending_miles: '',
        effective_date: '',
        expiration_date: '',
        coverage: '',
        coverage_list: [],
        deductible: '',
        ro: '',
        ro_selected_date: '',
        ro_opened_date: moment(),
        ro_mileage: '',
        facility_id: undefined,
        complaints: [],
        documents: [],
        facility_name: '',
        facility_address: '',
        facility_postal_code: '',
        facility_city: '',
        facility_state_code: '',
        facility_country: '',
        facility_phone: '',
        facility_fax: '',
        labor_rate: '0',
        tax_labor: '0',
        tax_parts: '0',
        advisor: '',
        term: '',
        contract_status: '',
        pay_type: '',
        owner_id: '',
        estimate: '',
        pre_auth_amount: '',
        is_ro_auto: false,
        email_address: '',
        alternate_phone_number: '',
        best_contact_method: '',
        is_ro_customer_valid: '',
        is_ro_contract_vin_match: '',
        is_ro_contract_mileage_valid: '',
        total_tax: '0',
        requested_total: '0',
        deductible_collect: false,
        canceled_reason: '',
        canceled_other_reason: '',
        contract_store_id: '',
        claim_type: '',
        originating_dealership: '',
        is_transferred: '',
        charge_back_exists: false,
        warning_message_note: '',
        review_inspection: '',
        repairing_facility_labor_rate: '0',
      }
    );
    this.automotive_claim.on('swap', (newStructure, oldStructure, keyPath) => {
      this.setState({ automotive_claim: this.automotive_claim.cursor() });
    });
    this.state = {
      automotive_claim: this.automotive_claim.cursor(),
      contractStatus: '',
      showLoader: false,
      isWorksheetUpdated: false,
      displayBackConfirmationModal: false,
      displayNoVendorConfirmationModal: false,
      initialStatus: '',
      currentOwnerID: '',
      showFacilityLoader: false,
      facilityDetails: {},
      hasRO: false,
      updateValidationModal: false,
      updateWarningMessage: '',
      partLaborToBeRemoved: {
        complaintId: '',
        type: '',
        id: ''
      },
      removePartLaborModal: false,
      manuallyEnterRO: false,
      showFaxCCSheet: false,
      disableFaxCCSheet: false,
      showInvoiceSent: false,
      showRequiredNoteError: false,
      showStoreChangeWarning: false,
      manuallyEnterTax: false,
      displayUserSelectionModal: false,
      displayROSelectionModal: false,
      roList: [],
      showUserListLoader: false,
      userList: [],
      usedRos: [],
      showChargeBackClaim: false,
      chargeBackClaim: {},
      coverageDetails: [],
      attachments: [],
      reloadAttachments: false,
    };
    this.timerAutoSave = null;
    this.claimRO = null;
  }

  componentDidMount() {
    document.title = 'TCA Portal - Automotive Claims Worksheet';
    this.loadClaim();
  }

  componentWillUnmount() {
    this.automotive_claim.removeAllListeners();
    immstruct.remove(this.IMMS_KEY);
    if (this.timerAutoSave) {
      clearTimeout(this.timerAutoSave);
    }
  }

  loadCoverageComponents = () => {
    this.setState({ loading: true }, () => {
      const claimObject = this.state.automotive_claim.toJS();

      let code = claimObject && claimObject.contract_number;
      if (code === undefined) {
        return;
      }
      const url = apiUrls.repairCodes.replace('__code__', code);
      ajax(url, {}, {}, ({ repair_codes }, status) => {
        if (status !== 200) {
          Alert.error('Error while fetching the contract components');
        } else {
          if (repair_codes !== null) {
            this.setState({
              coverageDetails: repair_codes,
              loading: false,
            });
          } else {
            this.setState({
              loading: false,
              coverageDetails: [],
            });
          }
        }
      });
    });
  };

  loadClaim = (callback) => {
    this.setState({ showLoader: true }, () => {
      ajax(`${apiUrls.automotiveClaims}/${this.props.location.query.id}`, {}, {}, (data, status) => {
        if (status === 200) {
          const claimObject = this.state.automotive_claim.toJS();
          const warningMessageNote = claimObject.warning_message_note;
          this.state.automotive_claim.update(() => Immutable.fromJS(data.auto_claim));
          let payableComplaint = data.auto_claim.complaints.find((complaint) => complaint.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.payable);
          if (payableComplaint) {
            this.setState({
              manuallyEnterTax: true
            });
          }
          if (this.claimRO) {
            this.state.automotive_claim.cursor("ro").update(() => this.claimRO);
            this.claimRO = null;
          }

          if (warningMessageNote) {
            this.state.automotive_claim.cursor("warning_message_note").update(() => warningMessageNote);
          }

          this.setState({
            showLoader: false,
            isWorksheetUpdated: false,
            initialStatus: data.auto_claim.status,
            contractStatus: data.auto_claim.contract_status,
            currentOwnerID: data.auto_claim.owner_id,
            hasRO: (data.auto_claim.claim_type === "LCA") ? data.auto_claim.complaints.length !== 0 && !!data.auto_claim.ro : !!data.auto_claim.ro,
            incomingRO: !!data.auto_claim.ro,
            showFaxCCSheet: (data.auto_claim.pay_type === CONSTANTS.PAYMENT_TYPE_CODES.CREDIT_CARD && userHasRole(this.props.user, CONSTANTS.USER_ROLES.accountingClaimHandler) && data.auto_claim.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.authorizedCCClaim),
            showInvoiceSent: (data.auto_claim.pay_type === CONSTANTS.PAYMENT_TYPE_CODES.CREDIT_CARD && userHasRole(this.props.user, CONSTANTS.USER_ROLES.accountingClaimHandler) && data.auto_claim.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.authorizedCCClaim),
            usedRos: data.auto_claim.used_ro || [],
          }, () => this.loadCoverageComponents());
          if (data.auto_claim.facility_id) {
            this.loadFacilityDetails(data.auto_claim.facility_id, false);
          }
          if (callback && typeof callback === "function") {
            callback();
          }

          if (data.auto_claim.pay_type === CONSTANTS.PAYMENT_TYPE_CODES.CREDIT_CARD) {
            this.loadAttachments(data.auto_claim.id, false);
          }

          if (this.props.location.query.chargeback === "true") {
            this.showChargebackClaimModal();
          }
        } else {
          this.setState({ showLoader: false, isWorksheetUpdated: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  showStoreChangeWarningModal = (preStore, newStore) => {
    const claimObject = this.state.automotive_claim.toJS();
    if (preStore && preStore.Int64 && claimObject.contract_store_id === preStore.Int64 &&
      claimObject.contract_store_id !== newStore.Int64 &&
      claimObject.product_code === CONSTANTS.PRODUCT_CODE_NEW_MAP.service &&
      claimObject.coverage_list.find(this.isDisappearingDeductibleService)) {
      this.setState({ showStoreChangeWarning: true });
    }
  };

  loadFacilityDetails = (facilityID, isUpdated) => {
    this.setState({ showFacilityLoader: true, isWorksheetUpdated: !!isUpdated }, () => {
      ajax(`${apiUrls.facility}/${facilityID}`, {}, {}, (data, status) => {
        if (status === 200) {
          const prevFacilityStore = this.state.facilityDetails.store_id;
          this.setState({
            facilityDetails: data.facility,
            showFacilityLoader: false
          });
          if (isUpdated) {
            this.showStoreChangeWarningModal(prevFacilityStore, data.facility.store_id);
            this.updateClaimFacility(facilityID, data);
          } else {
            this.state.automotive_claim.cursor('facility_id').update(() => facilityID);
          }
        } else {
          this.setState({ showFacilityLoader: false });
          Alert.error("Failed to fetch facility details. If the error continues, contact your system administrator.");
        }
      });
    });
  };

  loadAttachments = (claimId, isUpdated, callback) => {
    const url = apiUrls.autoClaimCCDocumentIndex.replace('__claimId__', claimId);
    this.setState({ showFacilityLoader: true, isWorksheetUpdated: !!isUpdated }, () => {
      ajax(url, {}, { method: 'GET' }, (data, status) => {
        if (status === 200) {
          this.setState({ attachments: data.docs, disableFaxCCSheet: data.docs && data.docs.length > 0 });
          if (callback) {
            callback();
          }
        } else {
          Alert.error("Error getting claim documents. If the error continues, contact your system administrator.");
        }
        this.setState({ showFacilityLoader: false });
      });
    });
  };

  isDisappearingDeductibleService = (coverage) => {
    return (coverage.flag === "Disappearing Deductible" && coverage.value === true);
  };

  updateClaimFacility = (facilityID, facilityDetails) => {
    let laborRate = 0;
    let partsTax = 0;
    let laborTax = 0;
    if (facilityDetails.facility.labor_rate && facilityDetails.facility.labor_rate.length > 0) {
      const activeRate = facilityDetails.facility.labor_rate.find(obj => obj.active);
      if (activeRate) {
        laborRate = activeRate.effective_rate;
      }
    }
    if (facilityDetails.facility.labor_tax && facilityDetails.facility.labor_tax.length > 0) {
      const activeRate = facilityDetails.facility.labor_tax.find(obj => obj.active);
      if (activeRate) {
        laborTax = activeRate.effective_rate;
      }
    }
    if (facilityDetails.facility.parts_tax && facilityDetails.facility.parts_tax.length > 0) {
      const activeRate = facilityDetails.facility.parts_tax.find(obj => obj.active);
      if (activeRate) {
        partsTax = activeRate.effective_rate;
      }
    }
    this.state.automotive_claim.update(oldValue => oldValue.merge(Immutable.fromJS({
      facility_id: facilityID,
      facility_name: facilityDetails.facility.name,
      facility_address: facilityDetails.facility.address.String,
      facility_postal_code: facilityDetails.facility.postal_code,
      facility_city: facilityDetails.facility.city,
      facility_state_code: facilityDetails.facility.state_code,
      facility_country: facilityDetails.facility.country,
      facility_phone: facilityDetails.facility.phone.String,
      facility_fax: facilityDetails.facility.fax.String,
      labor_rate: laborRate,
      tax_labor: laborTax,
      tax_parts: partsTax,
    })));
  };

  handleBackButtonOnClick = () => {
    if (this.state.isWorksheetUpdated) {
      this.setState({ displayBackConfirmationModal: true });
    } else {
      this.context.router.goBack();
    }
  };

  redirectToPrevious = () => {
    this.setState({ displayBackConfirmationModal: false, isWorksheetUpdated: false }, () => {
      this.context.router.goBack();
    });
  };

  getClaimData = (config) => {
    const claimObject = this.state.automotive_claim.toJS();
    let claim_parts_total = 0;
    let claim_labor_total = 0;
    for (let complaintIndex = 0; complaintIndex < claimObject.complaints.length; complaintIndex++) {
      let complaint_parts_total = 0;
      let complaint_labor_total = 0;
      let complaint_towings_total = 0;
      let complaint_rentals_total = 0;
      let complaint_sublets_total = 0;
      let complaint_miscs_total = 0;
      for (let partIndex = 0; partIndex < claimObject.complaints[complaintIndex].parts.length; partIndex++) {
        let quantity = claimObject.complaints[complaintIndex].parts[partIndex].quantity || "0";
        claimObject.complaints[complaintIndex].parts[partIndex].quantity = parseInt(quantity);
        complaint_parts_total += parseFloat(claimObject.complaints[complaintIndex].parts[partIndex].approved);
      }
      for (let laborIndex = 0; laborIndex < claimObject.complaints[complaintIndex].labors.length; laborIndex++) {
        complaint_labor_total += parseFloat(claimObject.complaints[complaintIndex].labors[laborIndex].approved);
      }
      for (let towingIndex = 0; towingIndex < claimObject.complaints[complaintIndex].towings.length; towingIndex++) {
        complaint_towings_total += parseFloat(claimObject.complaints[complaintIndex].towings[towingIndex].approved);
      }
      for (let rentalIndex = 0; rentalIndex < claimObject.complaints[complaintIndex].rentals.length; rentalIndex++) {
        complaint_rentals_total += parseFloat(claimObject.complaints[complaintIndex].rentals[rentalIndex].approved);
      }
      for (let subletIndex = 0; subletIndex < claimObject.complaints[complaintIndex].sublets.length; subletIndex++) {
        complaint_sublets_total += parseFloat(claimObject.complaints[complaintIndex].sublets[subletIndex].approved);
      }
      for (let miscIndex = 0; miscIndex < claimObject.complaints[complaintIndex].miscs.length; miscIndex++) {
        complaint_miscs_total += parseFloat(claimObject.complaints[complaintIndex].miscs[miscIndex].approved);
      }
      claimObject.complaints[complaintIndex].parts_total = complaint_parts_total;
      claimObject.complaints[complaintIndex].labor_total = complaint_labor_total;
      claimObject.complaints[complaintIndex].towings_total = complaint_towings_total;
      claimObject.complaints[complaintIndex].rentals_total = complaint_rentals_total;
      claimObject.complaints[complaintIndex].sublets_total = complaint_sublets_total;
      claimObject.complaints[complaintIndex].miscs_total = complaint_miscs_total;
      if (claimObject.complaints[complaintIndex].status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.payable) {
        claim_parts_total += complaint_parts_total;
        claim_labor_total += complaint_labor_total;
      }
    }

    claimObject.labor_rate = claimObject.labor_rate || "0";
    claimObject.tax_labor = claimObject.tax_labor || "0";
    claimObject.tax_parts = claimObject.tax_parts || "0";
    claimObject.pre_auth_amount = claimObject.pre_auth_amount || "0";
    claimObject.repairing_facility_labor_rate = claimObject.repairing_facility_labor_rate || "0";
    // We dont want to store tax in requested_total in database
    claimObject.requested_total = this.claimSummary.calculateRequestedGrandTotal(0);
    claimObject.total_tax = claimObject.total_tax || "0";

    //Priority is given to total_tax in claimObject after that to calculated amount
    if (!this.state.manuallyEnterTax) {
      let totalTax = ((claim_parts_total * (parseFloat(claimObject.tax_parts) / 100)) +
        (claim_labor_total * (parseFloat(claimObject.tax_labor) / 100)));

      claimObject.total_tax = totalTax.toFixed(2);
    }

    claimObject.ro_mileage = claimObject.ro_mileage ? parseInt(claimObject.ro_mileage) : 0;
    claimObject.estimate = this.claimSummary.calculateGrandTotal(claimObject.total_tax);
    claimObject.actual_paid_amount = this.claimSummary.calculateGrandTotal(claimObject.total_tax);
    claimObject.owner_id = parseInt(claimObject.owner_id);
    claimObject.facility_id = parseInt(claimObject.facility_id);

    // If the update claim call came from the autosave then only default the status to open
    if ((config && typeof config.is_auto_save !== "undefined" && config.is_auto_save !== null && config.is_auto_save) &&
      (this.state.initialStatus === CONSTANTS.AUTO_CLAIM_STATUS_MAP.denied ||
        this.state.initialStatus === CONSTANTS.AUTO_CLAIM_STATUS_MAP.returned)) {
      claimObject.status = CONSTANTS.AUTO_CLAIM_STATUS_MAP.open;
    }

    if (config && config.is_ro_auto) {
      claimObject["is_ro_auto"] = config.is_ro_auto;
    }

    if (config && typeof config.is_auto_save !== "undefined" && config.is_auto_save !== null) {
      claimObject["is_auto_save"] = config.is_auto_save;
    }

    if (config && typeof config.is_ro_customer_valid !== "undefined" && config.is_ro_customer_valid !== null) {
      claimObject["is_ro_customer_valid"] = config.is_ro_customer_valid;
    }
    if (config && typeof config.is_ro_contract_vin_match !== "undefined" && config.is_ro_contract_vin_match !== null) {
      claimObject["is_ro_contract_vin_match"] = config.is_ro_contract_vin_match;
    }
    if (config && typeof config.is_ro_contract_mileage_valid !== "undefined" && config.is_ro_contract_mileage_valid !== null) {
      claimObject["is_ro_contract_mileage_valid"] = config.is_ro_contract_mileage_valid;
    }

    if (config && config.is_auto_save) {
      this.claimRO = claimObject["ro"];
    }

    if (config && config.ro_selected_date) {
      claimObject["ro_selected_date"] = config["ro_selected_date"];
    }

    if (!(this.state.incomingRO || this.state.manuallyEnterRO || (config && config.is_ro_auto))) {
      claimObject["ro"] = "";
    }
    return claimObject;
  };

  validateClaim = (claimObject) => {
    if (claimObject.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.payable ||
      claimObject.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.approved ||
      claimObject.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.invoiceSent) {
      if (!claimObject.facility_id) {
        return "Before claim can be updated, you need to provide a facility code.  Please select or add a facility code to continue.";
      }
      if (claimObject.advisor === "") {
        return "Before claim can be updated, please add an advisor name to continue";
      }
    } else if (claimObject.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.denied ||
      claimObject.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.returned) {
      const index = claimObject.complaints.findIndex(element => element.status !== CONSTANTS.AUTO_CLAIM_STATUS_MAP.denied);
      if (index !== -1) {
        return "One or more complaint has not been denied.";
      } else if (claimObject.canceled_reason === "") {
        return "Reason is required when claim is returned or denied";
      } else if (claimObject.canceled_reason === CONSTANTS.AUTO_CLAIM_COMPLAINTS_DENIED_REASON.other &&
        claimObject.canceled_other_reason === "") {
        return "Denial note is required when claim status reason is Other";
      } 
    } 
  };

  updateClaim = (claimObject, callback) => {
    ajax(`${apiUrls.automotiveClaims}/${this.props.location.query.id}`, claimObject, { method: 'PUT' }, (data, status) => {
      callback(data, status);
    });
  };

  handleAutoSave = (config) => {
    const claimObject = this.getClaimData(config);
    this.setState({ showLoader: true }, () => {
      this.updateClaim(claimObject, (data, status) => {
        if (status === 401 && data.message) {
          Alert.error(data.message);
        }
        this.setState({ showLoader: false });
        this.loadClaim();
      });
    });
  };

  printFaxCCSheet = () => {
    const claimObject = this.getClaimData();
    this.setState({ showLoader: true }, () => {
      let url = apiUrls.downloadAutoCCInvoice.replace("__claimId__", this.props.location.query.id);
      ajaxRaw(url, { responseType: 'blob' }, (data) => {
        if (data.status === 200) {
          this.updateReloader(true);
          this.setState({ showLoader: false, disableFaxCCSheet: true, showInvoiceSent: true });
          this.loadAttachments(this.props.location.query.id, false, () => this.setState({ showInvoiceSent: true }));
          window.URL = window.URL || window.webkitURL;
          const csvURL = window.URL.createObjectURL(data.response);
          window.open(csvURL, `${claimObject.contract_number} - ${claimObject.ro}.pdf`, `location=no,left=${window.outerWidth * 0.1},width=${window.outerWidth * 0.8},height=${window.outerHeight}`);
        } else {
          this.setState({ showLoader: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  updateReloader = (value) => {
    this.setState({ reloadAttachments: value });
  };

  updateCCClaim = () => {
    ajax(`${apiUrls.automotiveClaims}/${this.props.location.query.id}/cc-update`, {}, { method: 'PUT' }, (data, status) => {
      if (status === 200) {
        Alert.success("Update successful.");
        if (this.state.displayBackConfirmationModal) {
          this.setState({ displayBackConfirmationModal: false }, () => {
            this.context.router.goBack();
          });
        } else {
          this.loadClaim();
        }
      }
    });
  };

  reOpenClaim = () => {
    const url = apiUrls.automotiveClaimReOpen.replace('__claimId__', this.props.location.query.id);
    ajax(url, {}, { method: 'PUT' }, (data, status) => {
      if (status === 200) {
        Alert.success("Claim is successfully reopened.");
        this.loadClaim();
      } else {
        Alert.error("Reopen request failed due to : " + data.message);
      }
    });
  };

  validateClaimComplaint = (complaints) => {
    let errors = complaints.map((complaint, index) => {
      index += 1;
      if (complaint.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.denied &&  !complaint.status_reason) {
        return `Complaint` + index + `: Reason is required for denied status`;
      }
      if (complaint.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.denied &&
          complaint.status_reason === CONSTANTS.AUTO_CLAIM_COMPLAINTS_DENIED_REASON.other &&
          !complaint.status_other_reason) {
        return `Complaint` + index + `: Denial note is required for other reason`;
      }
    }).filter(notUndefined => notUndefined !== undefined);
    
    return errors.length > 0 && errors.join(' and ');
  };

  handleUpdate = (config) => {
    const claimObject = this.getClaimData(config);
    let errorMessage = this.validateClaim(claimObject);
    if (errorMessage) {
      if (this.state.initialStatus) {
        this.state.automotive_claim.cursor("status").update(() => this.state.initialStatus);
        this.state.automotive_claim.cursor("canceled_reason").update(() => '');
        this.state.automotive_claim.cursor("owner_id").update(() => this.state.currentOwnerID);
      }
      this.setState({ updateValidationModal: true, updateWarningMessage: errorMessage });
      return;
    }
    if (claimObject.complaints && claimObject.complaints.length > 0) {
      let errorClaimComplaints = this.validateClaimComplaint(claimObject.complaints);
      if (errorClaimComplaints) {
        this.setState({ updateValidationModal: true, updateWarningMessage: errorClaimComplaints });
        return;
      }
    }
    this.setState({ showLoader: true }, () => {
      this.updateClaim(claimObject, (data, status) => {
        if (status === 200) {
          Alert.success("Update successful.");
          this.state.automotive_claim.cursor("warning_message_note").update(() => '');
          if (this.state.displayBackConfirmationModal) {
            this.setState({ displayBackConfirmationModal: false }, () => {
              this.context.router.goBack();
            });
          } else {
            this.loadClaim();
          }
        } else if (status === 207) {
          this.state.automotive_claim.cursor("owner_id").update(() => this.state.currentOwnerID);
          this.loadClaim(() => {
            this.setState({
              showLoader: false,
              displayBackConfirmationModal: false
            }, () => {
              if (data.errors) {
                for (let key of Object.keys(data.errors)) {
                  Alert.warning(data.errors[key]);
                }
              } else {
                Alert.warning("Update failed due to : " + data.message);
              }
            });
          });
        } else if (status === 409) {
          this.setState({
            showLoader: false,
            displayROSelectionModal: true,
            roList: data.ros
          });
        } else if (status === 401 && data.message) {
          this.setState({
            showLoader: false,
          });
          const claimObject = this.state.automotive_claim.toJS();
          claimObject['status'] = this.state.initialStatus;
          this.state.automotive_claim.update(() => Immutable.fromJS(claimObject));
          Alert.error(data.message);
        }
        else if (status === 400) {
          Alert.error("Update failed: Invalid Data");
          this.setState({
            showLoader: false,
            displayBackConfirmationModal: false
          }, () => {
            if (data.errors) {
              if (data.errors.cause) {
                Alert.warning(data.errors.cause);
              }
              if (data.errors.complaint) {
                Alert.warning(data.errors.complaint);
              }
              if (data.errors.correction) {
                Alert.warning(data.errors.correction);
              }
              if (data.errors.facility) {
                Alert.warning(data.errors.facility);
              }
              if (data.errors.advisor) {
                Alert.warning(data.errors.advisor);
              }
              if (data.errors.repair_code) {
                Alert.warning(data.errors.repair_code);
              }
              if (data.errors.vendor) {
                Alert.warning(data.errors.vendor);
              }
              if (data.errors.bill_number) {
                Alert.warning(data.errors.bill_number);
              }
              if (data.errors.requested_total) {
                Alert.warning(data.errors.requested_total);
              }
              if (data.errors.cancelled_reason) {
                Alert.warning(data.errors.cancelled_reason);
              }
            } else {
              Alert.warning("Update failed due to : " + data.message);
            }
          });
        } else {
          this.setState({
            showLoader: false,
            isWorksheetUpdated: false,
            displayBackConfirmationModal: false
          }, () => {
            if (data.message) {
              return Alert.error(data.message);
            }
            Alert.error("Click the browser's Refresh button to reload the data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  handleCustomerUpdate = (updatedCustomer) => {
    this.setState({ showLoader: true }, () => {
      ajax(`${apiUrls.automotiveClaims}/${this.props.location.query.id}/customer`, updatedCustomer, { method: 'PUT' }, (data, status) => {
        if (status === 200) {
          this.handleAutoSave({
            "is_auto_save": true,
            "is_ro_customer_valid": "true"
          });
        }
        else {
          this.setState({
            showLoader: false
          }, () => {
            Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  handleAssignMe = () => {
    this.setState({ showLoader: true }, () => {
      ajax(`${apiUrls.automotiveClaimsReassignSelf}`, { "claim_id": parseInt(this.props.location.query.id, 10) }, { method: 'PUT' }, (data, status) => {
        if (status === 200) {
          this.loadClaim();
        } else {
          this.setState({
            showLoader: false
          }, () => {
            Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  handleAssignTo = () => {
    const claimObject = this.state.automotive_claim.toJS();
    let userRole = CONSTANTS.USER_ROLES.autoClaimsManager;
    if (claimObject.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.open ||
      claimObject.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.returned ||
      claimObject.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.preAuthorization ||
      claimObject.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.needRentalBill ||
      claimObject.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.needSubletBill ||
      claimObject.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.needSMToCall ||
      claimObject.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.needClosedAccountingRO ||
      claimObject.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.needProofOfDeductibleReimbursement ||
      claimObject.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.waitingOnVendor) {
      userRole = CONSTANTS.USER_ROLES.autoClaims;
    }
    this.setState({ displayUserSelectionModal: true }, () => {
      this.setState({ showUserListLoader: true }, function () {
        ajax(apiUrls.userList, {}, {}, (data, status) => {
          if (status === 200) {
            this.setState({ userList: data.users.filter(user => userHasRole(user, userRole)), showUserListLoader: false });
          } else {
            this.setState({ showUserListLoader: false });
            Alert.error("Click the browser's Refresh button to reload the user list. If the error continues, contact your system administrator.");
          }
        });
      });
    });
  };

  setWorksheetUpdated = () => {
    this.setState({
      isWorksheetUpdated: true
    }, () => {
      this.autoSave();
    });
  };

  setShowRequiredNoteError = (value) => {
    this.setState({
      showRequiredNoteError: value
    });
  };

  setManuallyEnterTax = (value) => {
    this.setState({
      manuallyEnterTax: value
    });
  };

  handleOnChange = (name, value) => {
    const claimObject = this.state.automotive_claim.toJS();
    claimObject[name] = value;
    if (name === 'status') {
      claimObject['canceled_reason'] = '';
      claimObject['canceled_other_reason'] = '';
    }
    this.state.automotive_claim.update(() => Immutable.fromJS(claimObject));
    this.setWorksheetUpdated();
  };

  handleMultipleChange = (updateObject) => {
    this.state.automotive_claim.update(oldValue => oldValue.merge(Immutable.fromJS(updateObject)));
    this.setWorksheetUpdated();
  };

  handleDateChange = (name, date) => {
    if (this.state.facilityDetails && this.state.facilityDetails.id) {
      let roOpenedDate = moment(new Date()).local(false);
      if (date) {
        roOpenedDate = moment(date, dateFormat.displayDateFormat).local(false);
      }

      const facilityDetails = this.getFacilityActiveRates(roOpenedDate);
      let laborRate = 0;
      let partsTax = 0;
      let laborTax = 0;
      if (facilityDetails.labor_rate && facilityDetails.labor_rate.length > 0) {
        const activeRate = facilityDetails.labor_rate.find(obj => obj.active);
        if (activeRate) {
          laborRate = activeRate.effective_rate;
        }
      }
      if (facilityDetails.labor_tax && facilityDetails.labor_tax.length > 0) {
        const activeRate = facilityDetails.labor_tax.find(obj => obj.active);
        if (activeRate) {
          laborTax = activeRate.effective_rate;
        }
      }
      if (facilityDetails.parts_tax && facilityDetails.parts_tax.length > 0) {
        const activeRate = facilityDetails.parts_tax.find(obj => obj.active);
        if (activeRate) {
          partsTax = activeRate.effective_rate;
        }
      }

      this.state.automotive_claim.update(oldValue => oldValue.merge(Immutable.fromJS({
        labor_rate: laborRate,
        tax_labor: laborTax,
        tax_parts: partsTax,
        [name]: roOpenedDate.format(dateFormat.backendDateFormat),
      })));
    } else {
      this.state.automotive_claim.cursor(name).update(() => {
        if (date) {
          date = date.format(dateFormat.displayDateFormat);
          return moment.utc(date, dateFormat.displayDateFormat).format(dateFormat.backendDateFormat);
        } else {
          return moment.utc(new Date()).format(dateFormat.backendDateFormat);
        }
      });
    }

    this.setWorksheetUpdated();
  };

  getFacilityActiveRates = (roOpenedDate) => {
    const {
      facilityDetails,
    } = this.state;

    const newLaborRates = this.getActiveRates(facilityDetails, roOpenedDate, 'labor_rate');
    const newLaborTax = this.getActiveRates(facilityDetails, roOpenedDate, 'labor_tax');
    const newPartsTax = this.getActiveRates(facilityDetails, roOpenedDate, 'parts_tax');
    return { ...facilityDetails, labor_rate: newLaborRates, labor_tax: newLaborTax, parts_tax: newPartsTax };
  };

  getActiveRates = (facilityDetails, roOpenedDate, key) => {
    let existingRates = facilityDetails[key];

    if (!existingRates) {
      return existingRates;
    }

    if (existingRates.length === 1) {
      return existingRates;
    }
    let newRates = [];
    let maxEffectiveDate = moment("2006-01-01", dateFormat.backendDateFormat).local(false);
    existingRates.forEach(element => {
      const parsedDate = moment(element.effective_date, dateFormat.backendDateFormat).locale(false);
      if (parsedDate.isAfter(roOpenedDate)) {
        newRates.push({ ...element, active: false });
        return;
      }

      if (parsedDate.isSameOrBefore(roOpenedDate) && parsedDate.isAfter(maxEffectiveDate)) {
        maxEffectiveDate = parsedDate;

        // We set all previous rates to active false
        newRates = newRates.map(obj => {
          return {
            ...obj,
            active: false,
          };
        });

        newRates.push({ ...element, active: true });
      } else {
        newRates.push({ ...element, active: false });
      }
    });
    const activeExists = newRates.find(obj => obj.active);
    return activeExists ? newRates : existingRates;
  }

  isWorksheetDisabled = () => {
    return ((parseInt(this.props.user.id) !== parseInt(this.state.currentOwnerID)) ||
      (this.state.initialStatus === CONSTANTS.AUTO_CLAIM_STATUS_MAP.approved) ||
      (this.state.initialStatus === CONSTANTS.AUTO_CLAIM_STATUS_MAP.invoiceSent) ||
      (this.state.initialStatus === CONSTANTS.AUTO_CLAIM_STATUS_MAP.authorizedCCClaim) ||
      (this.state.initialStatus === CONSTANTS.AUTO_CLAIM_STATUS_MAP.denied) ||
      (this.state.initialStatus === CONSTANTS.AUTO_CLAIM_STATUS_MAP.ccPaid) ||
      (this.state.initialStatus === CONSTANTS.AUTO_CLAIM_STATUS_MAP.waitingForCheck) ||
      (this.state.initialStatus === CONSTANTS.AUTO_CLAIM_STATUS_MAP.deactivated) ||
      (this.state.initialStatus === CONSTANTS.AUTO_CLAIM_STATUS_MAP.checkWritten) ||
      (this.state.initialStatus === CONSTANTS.AUTO_CLAIM_STATUS_MAP.reversed) ||
      (this.state.initialStatus === CONSTANTS.AUTO_CLAIM_STATUS_MAP.waitingForReversed) ||
      (this.state.initialStatus === CONSTANTS.AUTO_CLAIM_STATUS_MAP.adjusted) ||
      (this.state.initialStatus === CONSTANTS.AUTO_CLAIM_STATUS_MAP.dealerChargedBack)
    );
  };

  autoSave = () => {
    if (this.timerAutoSave) {
      clearTimeout(this.timerAutoSave);
    }
    this.timerAutoSave = setTimeout(() => {
      this.handleAutoSave({ "is_auto_save": true });
    }, AutomotiveClaimWorksheet.TIMEFORAUTOSAVE);
  };

  handleROSelect = (ro) => {
    this.setState({
      displayROSelectionModal: false,
      roList: []
    });

    const config = {
      "is_ro_auto": true,
      "ro_selected_date": ro.date
    };

    this.handleUpdate(config);
  };

  handleComplaintUpdate = (complaintId, field, value) => {
    const index = this.state.automotive_claim.cursor(['complaints']).deref().findIndex(c => c.toJS().id === complaintId);

    if (index !== -1) {
      const claimObject = this.state.automotive_claim.toJS();
      if (field === "status" && value !== CONSTANTS.AUTO_CLAIM_STATUS_MAP.open && !claimObject.review_inspection) {
        Alert.warning("Please select reviewed inspection option before changing status of complaint.");
        return;
      }
      if (field === "status" && value === CONSTANTS.AUTO_CLAIM_STATUS_MAP.payable) {
        let complaint = this.state.automotive_claim.cursor(["complaints", index]).toJS();

        for (let labor of complaint.labors) {
          if (parseFloat(labor.billed) < parseFloat(labor.approved)) {
            Alert.warning(`Approved amount is greater than billed amount, please correct it.`);
            return;
          }
        }

        for (let part of complaint.parts) {
          if (parseFloat(part.requested) < parseFloat(part.approved)) {
            Alert.warning(`Approved amount is greater than requested amount, please correct it.`);
            return;
          }
        }

        for (let sublet of complaint.sublets) {
          if (parseFloat(sublet.requested) < parseFloat(sublet.approved)) {
            Alert.warning(`Approved amount is greater than requested amount, please correct it.`);
            return;
          }
        }

        for (let misc of complaint.miscs) {
          if (parseFloat(misc.requested) < parseFloat(misc.approved)) {
            Alert.warning(`Approved amount is greater than requested amount, please correct it.`);
            return;
          }
        }

        for (let rental of complaint.rentals) {
          if (parseFloat(rental.requested) < parseFloat(rental.approved)) {
            Alert.warning(`Approved amount is greater than requested amount, please correct it.`);
            return;
          }
        }

        for (let towing of complaint.towings) {
          if (parseFloat(towing.requested) < parseFloat(towing.approved)) {
            Alert.warning(`Approved amount is greater than requested amount, please correct it.`);
            return;
          }
        }

        if (!complaint.repair_code) {
          Alert.warning(`Please enter repair code before changing complaint status`);
          return;
        }
      } 
      if (field === "status" && value !== CONSTANTS.AUTO_CLAIM_STATUS_MAP.denied) {
        this.state.automotive_claim.cursor(['complaints', index, "status_other_reason"]).update(() => "");
        this.state.automotive_claim.cursor(['complaints', index, "status_reason"]).update(() => "");
      }
      if (field === "status_reason" && value !== CONSTANTS.AUTO_CLAIM_COMPLAINTS_DENIED_REASON.other) {
        this.state.automotive_claim.cursor(['complaints', index, "status_other_reason"]).update(() => "");
      }
      this.state.automotive_claim.cursor(['complaints', index, field]).update(() => value);
    }
    this.setWorksheetUpdated();
  };

  handleComplaintDelete = (complaintId) => {
    const url = apiUrls.automotiveComplaintDelete.replace('__complaintId__', complaintId);
    this.setState({ showLoader: true }, () => {
      ajax(url, {}, { method: 'DELETE' }, (data, status) => {
        if (status === 200) {
          this.popComplaint(data.complaint_id);
        } else {
          this.setState({ showLoader: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  handlePartLaborUpdate = (complaintId, type, partId, field, value) => {
    let partIndex;
    const complaintIndex = this.state.automotive_claim.cursor(['complaints']).deref().findIndex(c => c.toJS().id === complaintId);
    if (complaintIndex !== -1) {
      partIndex = this.state.automotive_claim.cursor(['complaints', complaintIndex, type]).deref().findIndex(c => c.toJS().id === partId);
    }
    if (partIndex !== -1) {
      this.state.automotive_claim.cursor(['complaints', complaintIndex, type, partIndex, field]).update(() => value);
      if (field === 'msrp') {
        this.state.automotive_claim.cursor(['complaints', complaintIndex, type, partIndex, 'approved']).update(() => value);
      }
    }
    this.setWorksheetUpdated();
  };

  onUseCurrentFacility = (facilityDetails, facilityID) => {
    const prevFacilityStore = this.state.facilityDetails.store_id;
    this.setState({ facilityDetails: facilityDetails }, () => {
      this.showStoreChangeWarningModal(prevFacilityStore, facilityDetails.facility.store_id);
      this.updateClaimFacility(facilityID, facilityDetails);
      this.setWorksheetUpdated();
    });
  };

  createNewComplaint = (claimObject) => {
    const url = apiUrls.automotiveComplaint.replace('__claimId__', claimObject.id);
    this.setState({ showLoader: true }, () => {
      ajax(url, {}, { method: 'POST' }, (data, status) => {
        if (status === 200) {
          this.getComplaint(claimObject, data.complaint_id);
        } else {
          this.setState({ showLoader: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  getComplaint(claimObject, id) {
    const url = apiUrls.automotiveComplaint.replace('__claimId__', claimObject.id);
    ajax(`${url}/${id}`, {}, { method: 'GET' }, (data, status) => {
      if (status === 200) {
        this.pushComplaint(data.complaint);
      } else {
        this.setState({ showLoader: false }, () => {
          Alert.error("Click the browser's Refresh button to reload the data. If the error continues, contact your system administrator.");
        });
      }
    });
  }

  pushComplaint = (complaint) => {
    const claimObject = this.state.automotive_claim.toJS();
    claimObject.complaints.push(complaint);
    this.state.automotive_claim.update(oldValue => oldValue.merge(Immutable.fromJS({
      complaints: claimObject.complaints
    })));
    this.setState({ showLoader: false });
  };

  popComplaint = (complaintId) => {
    this.state.automotive_claim.cursor(['complaints']).update(complaints => complaints.filterNot(unit => unit.get('id') === complaintId));
    this.setState({ showLoader: false });
  };

  addPartOrLabor = (claimObject, complaintId, type) => {
    let url = this.getURL(type);
    url = url.replace('__claimId__', claimObject.id);
    url = url.replace('__complaintId__', complaintId);
    this.setState({ showLoader: true }, () => {
      ajax(url, {}, { method: 'POST' }, (data, status) => {
        if (status === 200) {
          if (type === 'parts') {
            this.getPartOrLabor(claimObject, complaintId, type, data.part_id);
          } else if (type === 'labors') {
            this.getPartOrLabor(claimObject, complaintId, type, data.labor_id);
          } else if (type === 'rentals') {
            this.getPartOrLabor(claimObject, complaintId, type, data.rental_id);
          } else if (type === 'towings') {
            this.getPartOrLabor(claimObject, complaintId, type, data.towing_id);
          } else if (type === 'sublets') {
            this.getPartOrLabor(claimObject, complaintId, type, data.sublet_id);
          } else if (type === 'miscs') {
            this.getPartOrLabor(claimObject, complaintId, type, data.misc_id);
          }
        } else {
          this.setState({ showLoader: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  getURL = (type) => {
    if (type === 'parts') {
      return apiUrls.automotiveParts;
    } else if (type === 'labors') {
      return apiUrls.automotiveLabors;
    } else if (type === 'towings') {
      return apiUrls.automotiveTowings;
    } else if (type === 'sublets') {
      return apiUrls.automotiveSublets;
    } else if (type === 'miscs') {
      return apiUrls.automotiveMiscs;
    } else {
      return apiUrls.automotiveRentals;
    }
  };

  getPartOrLabor = (claimObject, complaintId, type, id) => {
    let url = this.getURL(type);
    url = url.replace('__claimId__', claimObject.id);
    url = url.replace('__complaintId__', complaintId);
    ajax(`${url}/${id}`, {}, { method: 'GET' }, (data, status) => {
      if (status === 200) {
        if (type === 'parts') {
          this.pushPartOrLabor(type, data.part);
        } else if (type === 'labors') {
          this.pushPartOrLabor(type, data.labor);
        } else if (type === 'rentals') {
          this.pushPartOrLabor(type, data.rental);
        } else if (type === 'towings') {
          this.pushPartOrLabor(type, data.towing);
        } else if (type === 'sublets') {
          this.pushPartOrLabor(type, data.sublet);
        } else if (type === 'miscs') {
          this.pushPartOrLabor(type, data.misc);
        }
      } else {
        this.setState({ showLoader: false }, () => {
          Alert.error("Click the browser's Refresh button to reload the data. If the error continues, contact your system administrator.");
        });
      }
    });
  };

  pushPartOrLabor = (type, object) => {
    const claimObject = this.state.automotive_claim.toJS();
    for (let index = 0; index < claimObject.complaints.length; index++) {
      if (claimObject.complaints[index].id === object.automotive_claim_complaint_id) {
        claimObject.complaints[index][type].push(object);
      }
    }
    this.state.automotive_claim.update(oldValue => oldValue.merge(Immutable.fromJS({
      complaints: claimObject.complaints
    })));
    this.setState({ showLoader: false });
  };

  removePartOrLaborPrompt = (claimId, complaintId, type, id) => {
    this.setState({
      partLaborToBeRemoved: {
        complaintId,
        type,
        id
      },
      removePartLaborModal: true
    });
  };

  abortPartOrLaborRemoval = () => {
    this.setState({
      partLaborToBeRemoved: {
        complaintId: '',
        type: '',
        id: ''
      },
      removePartLaborModal: false
    });
  };

  removePartOrLabor = (claimId) => {
    let url = this.getURL(this.state.partLaborToBeRemoved.type);
    url = url.replace('__claimId__', claimId);
    url = url.replace('__complaintId__', this.state.partLaborToBeRemoved.complaintId);
    this.setState({ showLoader: true }, () => {
      ajax(`${url}/${this.state.partLaborToBeRemoved.id}`, {}, { method: 'DELETE' }, (data, status) => {
        if (status === 200) {
          this.popPartOrLabor(this.state.partLaborToBeRemoved.complaintId, this.state.partLaborToBeRemoved.type, data.id);
        } else {
          this.setState({
            showLoader: false,
            partLaborToBeRemoved: {
              complaintId: '',
              type: '',
              id: ''
            },
            removePartLaborModal: false
          }, () => {
            Alert.error("Click the browser's Refresh button to reload the data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  popPartOrLabor = (complaintId, type, id) => {
    const complaintIndex = this.state.automotive_claim.cursor(['complaints']).deref().findIndex(c => c.toJS().id === complaintId);
    this.state.automotive_claim.cursor(['complaints', complaintIndex, type]).update(partsOrLabor => partsOrLabor.filterNot(unit => unit.get('id') === id));
    this.setState({
      partLaborToBeRemoved: {
        complaintId: '',
        type: '',
        id: ''
      },
      removePartLaborModal: false,
      showLoader: false
    });
  };

  handleManuallyEnterRO = () => {
    this.state.automotive_claim.cursor('ro_opened_date').update(() => moment());
    this.setState({
      manuallyEnterRO: true
    });
  };

  showChargebackClaimModal = () => {
    const claimObject = this.state.automotive_claim.toJS();
    if (claimObject.charge_back_exists) {
      this.loadClaimData();
    } else {
      this.createClaim();
    }
  };

  loadClaimData = () => {
    const claimObject = this.state.automotive_claim.toJS();
    this.setState({ showLoader: true }, () => {
      ajax(`api/automotive-chargeback-claims/${claimObject.id}`, {}, { method: 'GET' }, (data, status) => {
        if (status === 200) {
          this.setState({ chargeBackClaim: data.claim, showChargeBackClaim: true });
        } else {
          if (data.message) {
            Alert.error(data.message);
          }
        }
        this.setState({ showLoader: false });
      });
    });
  };

  createClaim = () => {
    const claimObject = this.state.automotive_claim.toJS();
    this.setState({ showLoader: true }, () => {
      ajax(`api/automotive-chargeback-claims/${claimObject.id}`, {}, { method: 'POST' }, (data, status) => {
        if (status === 200) {
          this.setState({ chargeBackClaim: data.claim, showChargeBackClaim: true });
          this.state.automotive_claim.cursor("charge_back_exists").update(() => true);
          Alert.success("Claim created successfully.");
        } else {
          if (data.message) {
            Alert.error(data.message);
          }
        }
        this.setState({ showLoader: false });
      });
    });
  };

  closeChargebackClaimModel = () => this.setState({ showChargeBackClaim: false });

  renderChargeBack = () => {
    const claimObject = this.state.automotive_claim.toJS();


    if (userHasRole(this.props.user, CONSTANTS.USER_ROLES.autoClaimsManager)
      && (claimObject.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.checkWritten ||
        claimObject.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.ccPaid) || claimObject.charge_back_exists) {
      return (<button
        className={`btn btn-primary mr-2 cursor-pointer`}
        id={`chargeback_btn`}
        onClick={this.showChargebackClaimModal}
        disabled={this.getAssignmentStatus(claimObject)}
      >
        {claimObject.charge_back_exists ? `View Chargeback` : `Add Chargeback`}&nbsp;&nbsp;
        <i className={`fa fa-undo`} />
      </button>);
    }
  };

  getAssignmentStatus = (claimObject) => {
    return (this.props.user.id !== claimObject.owner_id && ((
      claimObject.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.open ||
      claimObject.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.returned ||
      claimObject.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.preAuthorization ||
      claimObject.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.needRentalBill ||
      claimObject.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.needSubletBill ||
      claimObject.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.needSMToCall ||
      claimObject.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.needClosedAccountingRO ||
      claimObject.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.needProofOfDeductibleReimbursement ||
      claimObject.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.waitingOnVendor
    ) ||
      (
        userHasRole(this.props.user, CONSTANTS.USER_ROLES.autoClaimsManager) &&
        (
          claimObject.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.payable ||
          claimObject.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.waitingForCheck ||
          claimObject.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.checkWritten ||
          claimObject.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.reversed ||
          claimObject.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.needRentalBill ||
          claimObject.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.needSubletBill ||
          claimObject.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.needSMToCall ||
          claimObject.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.needClosedAccountingRO ||
          claimObject.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.needProofOfDeductibleReimbursement ||
          claimObject.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.waitingOnVendor ||
          claimObject.status === CONSTANTS.AUTO_CLAIM_STATUS_MAP.dealerChargedBack
        )
      )));
  }

  renderChargebackModal = () => {
    return (
      <ChargbackClaim
        user={this.props.user}
        location={this.props.location}
        claimObject={this.state.chargeBackClaim}
        closeModal={this.closeChargebackClaimModel} />
    );
  }

  isViewOnlyRole = () => {
    let r = CONSTANTS.USER_ROLES;
    let u = this.props.user;
    const hasReadRole = hstore.hasAny(u.roles, [r.accounting, r.viewOnlyClaims]);
    const hasWriteRole = hstore.hasAny(u.roles, [r.autoClaims,r.autoClaimsManager]);
    return hasReadRole && !hasWriteRole;
  };

  render() {
    const claimObject = this.state.automotive_claim.toJS();
    // if claim is denied, anyone can reopen, if claim is approved, only manger can reopen
    const isButtonReopen = this.state.initialStatus === CONSTANTS.AUTO_CLAIM_STATUS_MAP.denied
      || ((this.state.initialStatus === CONSTANTS.AUTO_CLAIM_STATUS_MAP.approved
        || this.state.initialStatus === CONSTANTS.AUTO_CLAIM_STATUS_MAP.deactivated
        || this.state.initialStatus === CONSTANTS.AUTO_CLAIM_STATUS_MAP.authorizedCCClaim
        || this.state.initialStatus === CONSTANTS.AUTO_CLAIM_STATUS_MAP.invoiceSent
        || this.state.initialStatus === CONSTANTS.AUTO_CLAIM_STATUS_MAP.dealerChargedBack)
        && userHasRole(this.props.user, CONSTANTS.USER_ROLES.autoClaimsManager));
    let isDisabled = this.isWorksheetDisabled(claimObject);
    const isNextButtonDisabled = this.isViewOnlyRole();

    const isDifferentOwner = parseInt(this.props.user.id) !== parseInt(this.state.currentOwnerID);
    const isClaimHandler = userHasRole(this.props.user, CONSTANTS.USER_ROLES.accountingClaimHandler);
    let nextButtonId = "btn-next";
    let nextButtonText = "Update";
    let nextButtonHandler = this.handleUpdate;
    let nextButtonClassName = "fa-check";
    let nextButtonDisabled = isDisabled;
    let nextButtonNode = () => { };

    if (this.getAssignmentStatus(claimObject)) {
      nextButtonId = "btn-assign-to-me";
      nextButtonText = "Assign To Me";
      nextButtonHandler = this.handleAssignMe;
      nextButtonClassName = "fa-crosshairs";
      nextButtonDisabled = isNextButtonDisabled;
    } else if (isButtonReopen && claimObject.claim_type !== "SB") {
      nextButtonId = "btn-reopen";
      nextButtonText = "Re-open";
      nextButtonHandler = this.reOpenClaim;
      nextButtonDisabled = isNextButtonDisabled;
    } else {
      nextButtonNode = () => {
        if (!nextButtonDisabled) {
          return (
            <button
              className="btn btn-primary mr-2 cursor-pointer"
              id="btn-assign-to"
              onClick={this.handleAssignTo}>
              <i className="fa fa-crosshairs" />
              &nbsp;{"Assign To"}
            </button>
          );
        }
      };
    }

    const spinnerMessage = <p className='text-center'><i className="fa fa-refresh fa-spin" /> Loading...</p>;
    return (
      <Loader show={this.state.showLoader} message={spinnerMessage}>
        <section className="claim-worksheet">
          <Header
            pageTitle="Automotive Claim"
            showLastUpdated={true}
            claimObject={claimObject}
            nextButtonClassName={nextButtonClassName}
            nextButtonId={nextButtonId}
            nextButtonText={nextButtonText}
            nextButtonOnClick={nextButtonHandler}
            backButtonOnClick={this.handleBackButtonOnClick}
            nextButtonDisabled={nextButtonDisabled}
            extraButtonNodes={nextButtonNode}
            renderChargeBack={this.renderChargeBack}
            user={this.props.user}
            claimStatus={this.state.initialStatus} />
          <section className="mt-5 pt-5">
            <div className="row mt-1">
              <CustomerInformation claimObject={claimObject}
                isDisabled={isDisabled}
                isDifferentOwner={isDifferentOwner}
                onCustomerUpdate={this.handleCustomerUpdate}
                reloadAttachments={this.state.reloadAttachments}
                updateReloader={this.updateReloader}
                isClaimHandler={isClaimHandler}
                reviewInspection={claimObject.review_inspection}
                onChangeReviewInspection={(v) => {
                  claimObject['review_inspection'] = v.value || v;
                  this.state.automotive_claim.update(() => Immutable.fromJS(claimObject));
                }}
                showLoader={() => {
                  this.setState({ showLoader: true });
                }}
                hideLoader={() => {
                  this.setState({ showLoader: false });
                }} />
              <ContractInformation
                claimObject={claimObject}
                handleOnChange={this.handleOnChange}
                isDisabled={isDisabled}
              />
              <FacilityInformation showFacilityLoader={this.state.showFacilityLoader}
                onUseCurrentFacility={this.onUseCurrentFacility}
                claimObject={claimObject}
                facilityDetails={this.state.facilityDetails}
                isDisabled={isDisabled}
                loadFacilityDetails={this.loadFacilityDetails}
                user={this.props.user} />
              <ROInformation isDisabled={isDisabled}
                claimObject={claimObject}
                facilityDetails={this.state.facilityDetails}
                handleDateChange={this.handleDateChange}
                handleMultipleChange={this.handleMultipleChange}
                handleOnChange={this.handleOnChange}
                hasRO={this.state.hasRO}
                showFacilityLoader={this.state.showFacilityLoader}
                updateState={this.handleUpdate}
                manuallyEnterRO={this.state.manuallyEnterRO}
                handleManuallyEnterRO={this.handleManuallyEnterRO}
                isManager={userHasRole(this.props.user, CONSTANTS.USER_ROLES.autoClaimsManager) || false}
                usedRo={this.state.usedRos || []} />
            </div>
            <ComplaintsInformation claimObject={claimObject}
              addPartOrLabor={this.addPartOrLabor}
              createNewComplaint={this.createNewComplaint}
              handleComplaintUpdate={this.handleComplaintUpdate}
              handleComplaintDelete={this.handleComplaintDelete}
              handlePartLaborUpdate={this.handlePartLaborUpdate}
              removePartOrLaborPrompt={this.removePartOrLaborPrompt}
              showRequiredNoteError={this.state.showRequiredNoteError}
              coverageDetails={this.state.coverageDetails}
              isDisabled={isDisabled}
              hasRO={this.state.hasRO}
              manuallyEnterRO={this.state.manuallyEnterRO}
              user={this.props.user} />
            <ClaimSummary claimObject={claimObject}
              facilityDetails={this.state.facilityDetails}
              loadFacilityDetails={this.loadFacilityDetails}
              ref={(claimSummary) => {
                this.claimSummary = claimSummary;
              }}
              handleOnChange={this.handleOnChange}
              coverageDetails={this.state.coverageDetails}
              isDisabled={isDisabled}
              user={this.props.user}
              isDifferentOwner={isDifferentOwner}
              initialStatus={this.state.initialStatus}
              contractStatus={this.state.contractStatus}
              printFaxCCSheet={this.printFaxCCSheet}
              updateCCClaim={this.updateCCClaim}
              showFaxCCSheet={this.state.showFaxCCSheet}
              disableFaxCCSheet={this.state.disableFaxCCSheet}
              showInvoiceSent={this.state.showInvoiceSent}
              setShowRequiredNoteError={this.setShowRequiredNoteError}
              handleUpdate={this.handleUpdate}
              manuallyEnterTax={this.state.manuallyEnterTax}
              setManuallyEnterTax={this.setManuallyEnterTax}
              loadClaim={this.loadClaim}
              attachments={this.state.attachments}
              showLoader={() => {
                this.setState({ showLoader: true });
              }}
              hideLoader={() => {
                this.setState({ showLoader: false });
              }}
              usedRo={
                (this.state.usedRos && this.state.usedRos.length > 0 && this.state.usedRos.find(ro => claimObject.ro === ro))
              } />
          </section>
          <ConfirmationModal confirmButtonText="Yes"
            declineButtonText="No"
            displayConfirmationModal={this.state.displayBackConfirmationModal}
            displayMessage="You have unsaved work, do you want to save it before continuing?"
            onConfirm={this.handleUpdate}
            onDecline={this.redirectToPrevious} />
          <ConfirmationModal confirmButtonText="Yes"
            declineButtonText="No"
            displayConfirmationModal={this.state.removePartLaborModal}
            displayMessage="The Current line item will be deleted, do you wish to proceed?"
            onConfirm={this.removePartOrLabor.bind(this, claimObject.id)}
            onDecline={this.abortPartOrLaborRemoval} />
          <ConfirmationModal confirmButtonText="Ok"
            type="warning"
            displayConfirmationModal={this.state.updateValidationModal}
            displayMessage={this.state.updateWarningMessage}
            onConfirm={() => {
              this.setState({ updateValidationModal: false });
            }} />
          <ConfirmationModal confirmButtonText="Ok"
            type="warning"
            displayConfirmationModal={this.state.showStoreChangeWarning}
            displayMessage={"By changing the store, you will no longer have $0 deductible."}
            onConfirm={() => {
              this.setState({ showStoreChangeWarning: false });
            }} />
          <UserSelectionModal userType={CONSTANTS.USER_ROLES.autoClaimsManager}
            userList={this.state.userList}
            displayModal={this.state.displayUserSelectionModal}
            showUserListLoader={this.state.showUserListLoader}
            handleModalSubmit={() => {
              this.setState({
                displayUserSelectionModal: false,
                showUserListLoader: false
              }, () => {
                this.handleUpdate();
              });
            }}
            claimObject={claimObject}
            handleOwnerChange={(e) => {
              this.handleOnChange('owner_id', e.target.value);
            }}
            closeModal={() => {
              this.handleOnChange('owner_id', this.props.user.id);
              this.setState({ displayUserSelectionModal: false });
            }} />
          <ROSelectionModal roList={this.state.roList}
            displayModal={this.state.displayROSelectionModal}
            onROSelect={this.handleROSelect}
            closeModal={() => {
              this.setState({
                displayROSelectionModal: false,
                roList: []
              });
            }}
          />
          {this.state.showChargeBackClaim && this.renderChargebackModal()}
        </section>
      </Loader>
    );
  }
}
