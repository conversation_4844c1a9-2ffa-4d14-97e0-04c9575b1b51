import React from 'react';
import immstruct from "immstruct";
import Mo<PERSON> from "../../Modal.jsx";
import { URLS as apiUrls } from "./../reusable/Utilities/urls";
import { json as ajax } from "./../../ajax.js";
import Alert from "react-s-alert";
import NewFacility from "./NewFacility.jsx";
import Warning from "./WarningPresentational.jsx";
import Confirmation from "./ConfirmationPresentational.jsx";
import If from "../reusable/If/If.jsx";
import PropTypes from 'prop-types';

export default class FacilityCreateModal extends React.Component {

  IMMS_KEY = 'new_facility';

  static propTypes = {
    displayModal: PropTypes.bool.isRequired,
    onClose: PropTypes.func.isRequired,
    onFacilitySelect: PropTypes.func.isRequired,
    onUseCurrentFacility: PropTypes.func.isRequired,
    user: PropTypes.shape({
      id: PropTypes.number.isRequired,
      first_name: PropTypes.string.isRequired,
      last_name: PropTypes.string.isRequired,
      email: PropTypes.string.isRequired,
      stores: PropTypes.arrayOf(PropTypes.string.isRequired),
      roles: PropTypes.shape({
        Map: PropTypes.object,
      }).isRequired
    }),
    shouldLoadFacility: PropTypes.bool.isRequired
  };

  constructor(props) {
    super(props);
    this.new_facility = immstruct(
      this.IMMS_KEY,
      {
        name: "",
        phone: "",
        postal_code: "",
        state_code: "",
        contact: "",
        facility_code: "",
        payment_type: "",
        labor_rate: [],
        labor_tax: [],
        parts_tax: [],
        pre_auth: false,
        pre_auth_limit: "",
        address: "",
        city: "",
        country: "USA",
        fax: "",
        email: "",
        vendor_id: "",
        is_active: true,
        ein: "",
        tax_type: ""
      }
    );
    this.new_facility.on('swap', (newStructure, oldStructure, keyPath) => {
      this.setState({ new_facility: this.new_facility.cursor() });
    });
    this.state = {
      new_facility: this.new_facility.cursor(),
      showLoader: false,
      facilityDetails: {},
      displayConfirmationModal: false,
      displayWarningModal: false,
      modalSize: 'extra-large',
      facilityInfo: ''
    };
  }

  componentWillUnmount() {
    this.new_facility.removeAllListeners();
    immstruct.remove(this.IMMS_KEY);
  }

  onCursorToggle = (name) => {
    this.state.new_facility.cursor(name).update(oldValue => !oldValue);
  };

  onChange = (name, value) => {
    this.state.new_facility.cursor(name).update(oldValue => value);
  };

  checkFacilityAgainstCode = (facility) => {
    if ((facility.facility_code.length < 5) || (facility.facility_code.length > 7)) {
      this.setState({ displayWarningModal: true, modalSize: 'medium' });
      return;
    }
    this.setState({ showLoader: true }, function () {
      ajax(`${apiUrls.facility}/${facility.facility_code}`, {}, {}, (data, status) => {
        switch (status) {
        case 200:
          this.setState({
            facilityDetails: data.facility,
            showLoader: false,
            displayConfirmationModal: true,
            modalSize: 'medium',
            facilityInfo: 'againstCode'
          });
          break;
        case 404:
          this.checkFacilityAgainstDetails(facility);
          break;
        default:
          this.setState({ showLoader: false, facilityDetails: {} }, function () {
            this.props.onClose();
            Alert.error("Failed to fetch facility details. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  checkFacilityAgainstDetails = (facility) => {
    this.setState({ showLoader: true }, function () {
      const url = `${apiUrls.facilityList}?name=${facility.name}&zip=${facility.postal_code}&phone=${facility.phone}`;
      ajax(url, {}, {}, (data, status) => {
        switch (status) {
        case 200:
          if (!Array.isArray(data.facilities) || data.facilities.length === 0) {
            this.onCreateFacility(facility);
          } else if (data.facilities && Array.isArray(data.facilities) && data.facilities.length > 0) {
            this.setState({
              facilityDetails: data.facilities[0],
              showLoader: false,
              displayConfirmationModal: true,
              modalSize: 'medium',
              facilityInfo: 'againstDetails'
            });
          }
          break;
        case 404:
          this.onCreateFacility(facility);
          break;
        default:
          this.setState({ showLoader: false, facilityDetails: {} }, function () {
            this.props.onClose();
            Alert.error("Failed to fetch facility details. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  useCurrentFacility = (facility) => {
    if (this.state.facilityInfo === 'againstCode') {
      this.props.onUseCurrentFacility(this.state.facilityDetails, facility.facility_code);
    } else if (this.state.facilityInfo === 'againstDetails') {
      this.props.onClose();
      this.props.onFacilitySelect(this.state.facilityDetails.id);
    }
  };

  onCreateFacility = (facility) => {
    facility.pre_auth_limit = parseFloat(facility.pre_auth_limit) || 0;
    ajax(apiUrls.facilityList, facility, { method: 'POST' }, (data, status) => {
      if (status === 200) {
        this.setState({ showLoader: false, facilityDetails: {} }, function () {
          this.props.onClose();
          this.props.onFacilitySelect(data.facility_id);
          Alert.success("Facility added successfully.");
        });
      } else if (status === 400) {
        this.setState({ showLoader: false }, function () {
          if (data.errors) {
            for (let key in data.errors) {
              if (key) {
                Alert.error(data.errors[key]);
              }
            }
          }
          if (data.message) {
            Alert.error(data.message);
          }
        });
      } else {
        this.setState({ showLoader: false, facilityDetails: {} }, function () {
          this.props.onClose();
          if (data.message) {
            Alert.error(data.message);
          }
          Alert.error("Failed to create facility. If the error continues, contact your system administrator.");
        });
      }
    });
  };

  getFormStatus = (facility) => {
    return (facility.name &&
    facility.address &&
    facility.phone &&
    facility.contact &&
    facility.postal_code &&
    facility.state_code &&
    facility.facility_code &&
    facility.payment_type &&
    facility.labor_rate &&
    facility.city);
  };

  onActionCallBack = (facility) => {
    if(this.props.shouldLoadFacility){
      this.useCurrentFacility(facility);
    } else {
      this.setState({
        displayConfirmationModal: false,
        modalSize: 'extra-large'
      });
    }
  };

  onClose = () => {
    this.setState({
      modalSize: 'extra-large',
      displayConfirmationModal: false,
      displayWarningModal: false
    }, () => {
      this.props.onClose();
    });
  };

  render() {
    const facility = this.state.new_facility.toJS();
    const spinnerMessage = <p className='text-center'><i className="fa fa-refresh fa-spin"/> Loading...</p>;
    let confirmationMessage = "";
    const facilityInfo = this.state.facilityInfo;

    if(this.props.shouldLoadFacility) {
      if (facilityInfo === 'againstCode') {
        confirmationMessage = `The entered Facility Code already exists, If this is the correct facility, please click yes. If it
                    is not the correct facility, please click No and modify the facility code to try again.`;
      }
      else if (facilityInfo === 'againstDetails') {
        confirmationMessage = `Vendor Already exists. If this is the correct facility, please click yes. If it is not the correct
                    facility, please modify the facility details to try again.`;
      }
    } else {
      if (facilityInfo === 'againstCode') {
        confirmationMessage = `The facility code already exists for the following facility. Facility code must be unique.`;
      }
      else if (facilityInfo === 'againstDetails') {
        confirmationMessage = `A facility with that name and zip code already exists. Please review the facility information.`;
      }
    }

    return (
      <section>
        <Modal visible={ this.props.displayModal } close={ this.onClose } size={ this.state.modalSize }>
          <If condition={ !this.state.displayConfirmationModal && !this.state.displayWarningModal }>
            <NewFacility facility={ facility }
              onChange={ this.onChange }
              onActionCallBack={ this.checkFacilityAgainstCode.bind(this,facility) }
              onNoActionCallBack={ this.props.onClose }
              onCursorToggle={ this.onCursorToggle }
              showLoader={ this.state.showLoader }
              user={ this.props.user }
              isActionEnabled={ !!this.getFormStatus(facility) }
              loaderMessage={ spinnerMessage }
              shouldLoadFacility={ this.props.shouldLoadFacility }/>
          </If>
          <If condition={ this.state.displayWarningModal }>
            <Warning message={ "The Facility Code needs to be between five and seven characters long, please correct." }
              onActionCallBack={ () => {
                this.setState({ displayWarningModal: false, modalSize: 'extra-large' });
              } }/>
          </If>
          <If condition={ this.state.displayConfirmationModal }>
            <Confirmation message={ confirmationMessage }
              facilityDetails={ this.state.facilityDetails }
              shouldShowTwoButton={ this.props.shouldLoadFacility }
              onActionCallBack={ this.onActionCallBack.bind(this, facility) }
              onNoActionCallBack={ () => {
                this.setState({
                  displayConfirmationModal: false,
                  modalSize: 'extra-large'
                });
              } }/>
          </If>
        </Modal>
      </section>
    );
  }
}
