import React from "react";
import Modal from "./../../Modal.jsx";
import { URLS as apiUrls } from "./../reusable/Utilities/urls";
import Loader from "react-loader-advanced";
import { json as ajax } from "./../../ajax.js";
import Alert from "react-s-alert";
import moment from "moment";
import dateFormat from "./../reusable/Utilities/dateFormat.js";
import accounting from "accounting";
import PropTypes from 'prop-types';
import { getContractCode } from "../automotiveClaimsDashboard/ClaimsList.jsx";

export default class ContractHistoryModal extends React.Component {

  static propTypes = {
    showContractHistory: PropTypes.bool.isRequired,
    closeHistoryModal: PropTypes.func.isRequired,
    claimObject: PropTypes.object.isRequired
  };

  constructor(props) {
    super(props);
    this.state = {
      showLoader: false,
      contractDetails: [],
      sortByDate: true
    };
  }

  loadContractDetails = () => {
    let url = `${apiUrls.automotiveContractHistory}/${this.props.claimObject.contract_number}/${this.props.claimObject.product_code}`;
    if (this.state.sortByDate) {
      url += '?sort_by_date=asc';
    } else {
      url += '?sort_by_date=desc';
    }
    this.setState({ showLoader: true }, function () {
      ajax(url, {}, {}, (data, status) => {
        if (status === 200) {
          this.setState({
            showLoader: false,
            contractDetails: data.auto_claim_history || []
          });
        } else if (status === 404){
          this.setState({
            showLoader: false,
            contractDetails: []
          });
        }  else {
          this.setState({ showLoader: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the claim data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  renderTableHeader = () => {
    return (
      <tr>
        <th onClick={ this.handleSortByDate }>Date {this.renderSortIcon()} </th>
        <th>Miles</th>
        <th>Service</th>
        <th>Facility</th>
        <th>Advisor</th>
        <th>RO#</th>
        <th>Amount</th>
        <th>Status</th>
      </tr>
    );
  };

  renderSortIcon = () => {
    if (this.state.sortByDate) {
      return (<i className="fa fa-caret-up" aria-hidden="true"/>);
    } else {
      return (<i className="fa fa-caret-down" aria-hidden="true"/>);
    }
  };

  handleSortByDate = () => {
    this.setState({ sortByDate: !this.state.sortByDate }, function () {
      this.loadContractDetails();
    });
  };

  renderTableBody = () => {
    return this.state.contractDetails.map(this.renderTableBodyRow);
  };

  renderTableBodyRow = (claimData, index) => {
    const claimDate = claimData.date && claimData.date.Valid ? moment(claimData.date.Time).format(dateFormat.displayDateFormat) : '';
    return (
      <tr key={ index }>
        <td>
          {claimDate}
        </td>
        <td>
          {claimData['miles']}
        </td>
        <td>
          {claimData['service']}
        </td>
        <td>
          {claimData['facility_code']}
        </td>
        <td>
          {claimData['advisor']}
        </td>
        <td>
          <a href="#!"
            className="users">
            {claimData['ro']}
          </a>
        </td>
        <td>
          { accounting.formatMoney(claimData['amount'], '$', 2) }
        </td>
        <td>
          {claimData['status']}
        </td>
      </tr>
    );
  };

  render() {
    const spinnerMessage = <p className='text-center'><i className="fa fa-refresh fa-spin"/> Loading...</p>;
    return (
      <section>
        <Modal visible={ this.props.showContractHistory } close={ this.props.closeHistoryModal } size="large">
          <Loader show={ this.state.showLoader } message={ spinnerMessage }>
            <h2 className="lead mb-4">Claim History for {getContractCode(this.props.claimObject.contract_number)}</h2>
            {
              this.state.contractDetails ?
                <div className="table-responsive claim-list mb-4">
                  <table className="table table-striped">
                    <thead>
                      {this.renderTableHeader()}
                    </thead>
                    <tbody>
                      {this.renderTableBody()}
                    </tbody>
                  </table>
                </div>
                :
                <div className="text-center">
                  No record found
                </div>
            }
            <div>
              <button className="btn btn-primary ml-auto d-flex" onClick={ this.props.closeHistoryModal }>
                Close
              </button>
            </div>
          </Loader>
        </Modal>
      </section>
    );
  }
}