import React from 'react';
import Modal from "../../Modal.jsx";
import PropTypes from 'prop-types';
import SearchFacility from './SearchFacility.jsx';

export default class FacilityLookupModal extends React.Component {

  static propTypes = {
    search: PropTypes.string,
    displayModal: PropTypes.bool.isRequired,
    closeModal: PropTypes.func.isRequired,
    onFacilitySelect: PropTypes.func.isRequired,
    onNewFacilityClick: PropTypes.func.isRequired,
    onActionButtonClick: PropTypes.func.isRequired,
    onRowSelect: PropTypes.func.isRequired,
  };

  constructor(props) {
    super(props);
    this.state = {
      selectedFacilityID: void 0,
    };
  }

  onClose = () => {
    this.setState({
      selectedFacilityID: void 0
    }, () => {
      this.props.closeModal();
    });
  };

  onRowSelect = (id) => {
    if(id) {
      this.setState({
        selectedFacilityID: parseInt(id), 
      });
    }
    else{
      this.setState({
        selectedFacilityID: void 0, 
      });
    }
    this.props.onRowSelect(id || void 0);
  };

  onSelectFacilityClick = () => {
    this.props.onFacilitySelect(this.state.selectedFacilityID);
  };

  render() {
    return (
      <section>
        <Modal visible={ this.props.displayModal } close={ this.onClose } size="large">
          <SearchFacility search={ this.props.search }
            showActionButton={ false }
            onActionButtonClick={ this.props.onActionButtonClick }
            onAddNewFacilityClick={ this.props.onNewFacilityClick }
            selectedFacilityID={ this.state.selectedFacilityID }
            onRowSelect={ this.onRowSelect }
            viewButtonDisabled={this.state.selectedFacilityID && this.state.selectedFacilityID !== 0}
          />
          <div className="row float-right mt-2 mr-1">
            <button className="btn btn-secondary mr-2" onClick={ this.onClose }>
              Cancel
            </button>
            <button className="btn btn-primary"
              onClick={ this.onSelectFacilityClick }
              disabled={ !this.state.selectedFacilityID }>
              OK
            </button>
          </div>
        </Modal>
      </section>
    );
  }
}