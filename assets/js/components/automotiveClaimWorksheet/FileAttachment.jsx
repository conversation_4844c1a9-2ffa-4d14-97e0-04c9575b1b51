import React from "react";
import AttachmentModal from "../reusable/AttachmentModal/AttachmentModal.jsx";
import { URLS as apiUrls } from "../reusable/Utilities/urls";
import { json as ajax } from "./../../ajax.js";
import PropTypes from "prop-types";

export default class FileAttachment extends React.Component {

  static propTypes = {
    displayModal: PropTypes.bool.isRequired,
    closeModal: PropTypes.func.isRequired,
    isInspectionAttachment: PropTypes.bool,
    maxFileSizeInMBs: PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.number
    ]),
    isMultipleAllowed: PropTypes.bool,
    claimId: PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.number
    ])
  };

  constructor(props) {
    super(props);
    this.state = {
      isUploading: false
    };
  }

  handleAttachmentUpload = (fileData, fileName, fileExtension) => {
    const params = {
      automotive_claim_id: this.props.claimId,
      file_content: fileData,
      file_name: fileName,
      file_type: fileExtension,
      is_inspection_attachments: this.props.isInspectionAttachment
    };
    this.setState({ isUploading: true }, function () {
      ajax(apiUrls.automotiveClaimDocument, params, { method: 'POST' }, (data, status) => {
        if (status === 200) {
          this.onAddNoteSuccess();
        } else {
          this.setState({ isUploading: false }, function () {
            this.props.closeModal('error', this.props.isInspectionAttachment);
          });
        }
      });
    });
  };

  onAddNoteSuccess = () => {
    this.setState({ isUploading: false }, function () {
      this.props.closeModal('success', this.props.isInspectionAttachment);
    });
  };

  render() {
    return (
      <AttachmentModal displayModal={this.props.displayModal}
        handleUpload={this.handleAttachmentUpload}
        closeModal={this.props.closeModal}
        title={this.props.isInspectionAttachment ? 'Inspection Attachment' : 'Automotive'}
        isInspectionAttachment={this.props.isInspectionAttachment}
        isUploading={this.state.isUploading}/>
    );
  }
}