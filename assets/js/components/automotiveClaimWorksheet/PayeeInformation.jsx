import React from "react";
import PropTypes from "prop-types";
import Alert from "react-s-alert";
import { json as ajax } from "./../../ajax.js";
import { URLS as apiUrls } from "./../reusable/Utilities/urls";
import Modal from "../../Modal.jsx";
import immstruct from "immstruct";
import Immutable from "immutable";
import VendorSelectionModal from "./../claimCheckList/BankSelectionModal.jsx";

export default class PayeeInformation extends React.Component {

  IMMS_KEY = 'payee';

  static propTypes = {
    claimObject: PropTypes.object.isRequired,
    isDisabled: PropTypes.bool.isRequired,
    showLoader: PropTypes.func,
    hideLoader: PropTypes.func.isRequired
  };

  constructor(props) {
    super(props);
    this.payee = immstruct(
      this.IMMS_KEY,
      {
        id: '',
        name: '',
        address: '',
        city: '',
        state: '',
        postal_code: '',
        vendor_id: ''
      }
    );
    this.payee.on('swap', (newStructure, oldStructure, keyPath) => {
      this.setState({ payee: this.payee.cursor() });
    });
    this.state = {
      showPayeeModal: false,
      formEdited: false,
      payee: this.payee.cursor(),
      displayVendorSelectionModal: false,
    };
  }

  componentWillUnmount = () => {
    this.payee.removeAllListeners();
    immstruct.remove(this.IMMS_KEY);
  };

  componentDidMount = () => {
    this.loadPayee();
  };

  UNSAFE_componentWillReceiveProps = (nextProps) => {
    if (nextProps.claimObject.id !== this.props.claimObject.id) {
      this.loadPayee(nextProps);
    }
  };


  loadPayee = (props) => {
    if (!props) {
      props = this.props;
    }
    this.props.showLoader();
    const url = apiUrls.automotivePayee.replace('__claimId__', props.claimObject.id);
    ajax(url, {}, { method: 'GET' }, (data, status) => {
      this.props.hideLoader();
      if (status === 200) {
        this.state.payee.update(() => Immutable.fromJS(data.payee));
      } else if(status === 404) {
        const payee = {
          id: 0,
          name: this.props.claimObject.customer_name,
          address: this.props.claimObject.street_address,
          city: this.props.claimObject.city,
          state: this.props.claimObject.state,
          postal_code: this.props.claimObject.postal_code,
          vendor_id: ''
        };
        this.state.payee.update(() => Immutable.fromJS(payee));
      } else {
        Alert.error("Click the browser's Refresh button to reload the data. If the error continues, contact your system administrator.");
      }
    });
  };

  closePayeeModal = () => {
    this.setState({ showPayeeModal: false });
  };

  showPayeeModal = () => {
    this.setState({ showPayeeModal: true });
  };

  getVendor = () => {
    const payeeObject = this.state.payee.toJS();
    return payeeObject.vendor_id;
  };

  handleUpdate = () => {
    this.props.showLoader();
    this.closePayeeModal();
    const payeeObject = this.state.payee.toJS();
    const url = apiUrls.automotivePayee.replace('__claimId__', this.props.claimObject.id);
    ajax(url, payeeObject, { method: 'POST' }, (data, status) => {
      this.props.hideLoader();
      if (status === 200) {
        this.loadPayee();
        Alert.success(data.message);
      } else if (status === 404) {
        this.showPayeeModal();
        Alert.warning(data.message);
      } else if (status === 400) {
        this.showPayeeModal();
        data.errors.forEach((error) => {
          Alert.warning(error);
        });
      }else {
        Alert.error(data.message);
      }
    });
  };

  renderPayeeModal = () => {
    return (
      <Modal visible={ this.state.showPayeeModal }
        close={ this.closePayeeModal } size="medium">
        {this.renderPayeeEditForm()}
      </Modal>
    );
  };

  renderPayeeEditForm = () => {
    const payeeObject = this.state.payee.toJS();
    return (
      <div className="row justify-content-md-center">
        <h6 className="col-10">Payee Information</h6>
        <div className="col-8">
          <div className="row my-3">
            <div className="col-5">Name</div>
            <div className="col-7">
              { payeeObject.name }
            </div>
          </div>
          <div className="row my-3">
            <div className="col-5">Address</div>
            <div className="col-7">
              { payeeObject.address }
            </div>
          </div>
          <div className="row my-3">
            <div className="col-5">City</div>
            <div className="col-7">
              { payeeObject.city }
            </div>
          </div>
          <div className="row my-3">
            <div className="col-5">State</div>
            <div className="col-7">
              { payeeObject.state }
            </div>
          </div>
          <div className="row my-3">
            <div className="col-5">ZIP</div>
            <div className="col-7">
              { payeeObject.postal_code }
            </div>
          </div>
          <div className="row my-3">
            <div className="col-5">Vendor #</div>
            <div className="col-7">
              { this.addEditVendor(payeeObject.vendor_id)}
            </div>
          </div>
        </div>
        <div className="col-10 my-3 mx-1">
          <div className="row justify-content-end">
            <button className="btn btn-secondary mr-2"
              onClick={ () => {
                if (this.state.formEdited) {
                  this.loadPayee();
                }
                this.closePayeeModal();
              } }>
              Cancel
            </button>
            <button className="btn btn-primary"
              onClick={ this.handleUpdate }>
              Update
            </button>
          </div>
        </div>
      </div>
    );
  };

    addEditVendor = (vendorID) => {
      return (
        <div>
          {vendorID ? vendorID : ""}
          {' '}
          <button className="btn btn-primary btn-sm" onClick={ ()=>{this.setState({ displayVendorSelectionModal: true, showPayeeModal: false });} }>
            {vendorID ? "Edit Vendor" : "Add Vendor"}
          </button>
        </div>
      );
    };

    onVendorSelect = (data) => {
      const payeeObject = this.state.payee.toJS();
      payeeObject['vendor_id'] = data.vendor_id;
      payeeObject['name'] = data.name;
      payeeObject['address'] = data.address1;
      payeeObject['city'] = data.city;
      payeeObject['state'] = data.state;
      payeeObject['postal_code'] = data.zip;

      this.state.payee.update(() => Immutable.fromJS(payeeObject));

      this.setState({ formEdited: true, displayVendorSelectionModal: false, showPayeeModal: true });
    };

    render() {
      const payeeObject = this.state.payee.toJS();
      let icon = "";
      if(payeeObject.vendor_id === "" && !this.props.isDisabled){
        icon = <i className="fa fa-exclamation-triangle fa-lg text-warning ml-3 cursor-pointer mt-1 ml-auto" aria-hidden="true" onClick={ this.showPayeeModal }/>;
      }

      const longTextStyle = {
        "maxWidth": "180px",
        "overflow": "hidden",
        "textOverflow": "ellipsis",
        "whiteSpace": "nowrap"
      };

      return (
        <section className="col text-sm-left">
          <div className="d-flex">
            <span style={ longTextStyle } title={ payeeObject.name }>{payeeObject.name}</span>
            {!this.props.isDisabled && <span id="link-payee-customer-change" className="text-primary cursor-pointer ml-auto" onClick={ this.showPayeeModal }>Change</span> }
          </div>
          <p className="mb-0 col-form-label-sm">
            {payeeObject.address}
          </p>
          <p className="mb-0 col-form-label-sm">
            {`${payeeObject.city}, ${payeeObject.state} ${payeeObject.postal_code}`}
          </p>
          <div className="d-flex">
            {`Vendor # ${payeeObject.vendor_id}`}
            {icon}
          </div>
          {this.renderPayeeModal()}
          <VendorSelectionModal selectBankDetails={ this.onVendorSelect }
            closeBankSelectionModal={ () => {
              this.setState({ displayVendorSelectionModal: false });
            } }
            displayBankSelectionModal={ this.state.displayVendorSelectionModal }/>
        </section>
      );
    }
}
