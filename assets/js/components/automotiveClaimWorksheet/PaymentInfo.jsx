import React from "react";
import PropTypes from "prop-types";
import moment from "moment";
import dateFormat from "./../reusable/Utilities/dateFormat.js";
import accounting from "accounting";
import { CONSTANTS } from "../reusable/Constants/constants.js";
import { URLS as apiUrls } from "./../reusable/Utilities/urls";
import { json as ajax } from "./../../ajax.js";
import Alert from "react-s-alert";
import Loader from "react-loader-advanced";
import ReversalPaymentInfo from "./ReversalPaymentInfo.jsx";
export default class PaymentInfo extends React.Component {

  static propTypes = {
    claimObject: PropTypes.object.isRequired,
    initialStatus: PropTypes.string.isRequired,
    user: PropTypes.shape({
      id: PropTypes.number.isRequired,
      first_name: PropTypes.string.isRequired,
      last_name: PropTypes.string.isRequired,
      email: PropTypes.string.isRequired,
      stores: PropTypes.arrayOf(PropTypes.string.isRequired),
      roles: PropTypes.shape({
        Map: PropTypes.object,
      }).isRequired
    }),
  };

  constructor(props) {
    super(props);
    this.state = {
      paymentObject: [],
      reversePaymentInfo: [],
      showLoader: false,
    };
  }

  componentDidMount() {
    if (this.displayPaymentDetails(this.props)) {
      this.getPaymentDetails();
    }
  }

  componentDidUpdate(prevProps, prevState) {
    if (prevProps.initialStatus != this.props.initialStatus && this.displayPaymentDetails(this.props) 
        && (this.state.paymentObject && this.state.paymentObject.length === 0)) {
      this.getPaymentDetails();
    }
  }


  getPaymentDetails = () => {
    this.setState({ showLoader: true }, function () {
      ajax(`${apiUrls.automotivePayments.replace('__claimId__', this.props.claimObject.id)}`, {}, {}, (data, status) => {
        if (status === 200) {
          this.setState({
            showLoader: false,
            paymentObject: data.auto_claim_payment,
            reversePaymentInfo: data.negative_claim_payments
          });
        } else {
          this.setState({
            showLoader: false
          }, () => {
            Alert.error("Click the browser's Refresh button to reload the payment data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  };

  displayPaymentDetails = (props) => {
    return (props.initialStatus === CONSTANTS.AUTO_CLAIM_STATUS_MAP.waitingForCheck ||
      props.initialStatus === CONSTANTS.AUTO_CLAIM_STATUS_MAP.checkWritten ||
      props.initialStatus === CONSTANTS.AUTO_CLAIM_STATUS_MAP.ccPaid ||
      props.initialStatus === CONSTANTS.AUTO_CLAIM_STATUS_MAP.reversed ||
      props.initialStatus === CONSTANTS.AUTO_CLAIM_STATUS_MAP.waitingForReversed ||
      props.initialStatus === CONSTANTS.AUTO_CLAIM_STATUS_MAP.adjusted);
  };

  getReversePaymentDetail = () => {
    return this.state.reversePaymentInfo;
  };

  renderAuthorizationNumber = (paymentObject) => {
    if (this.displayPaymentDetails(this.props)) {
      return (
        <div className="form-group row">
          <div className="col-5">
            Authorization #
          </div>
          <div className="col-7">
            {paymentObject && paymentObject.authorization_number}
          </div>
        </div>
      );
    }
  };

  renderBillNumber = (paymentObject) => {
    if (this.displayPaymentDetails(this.props)) {
      return (
        <div className="form-group row">
          <div className="col-5">
            Bill #
          </div>
          <div className="col-7">
            {paymentObject && paymentObject.bill_number}
          </div>
        </div>
      );
    }
  };

  renderPaymentInfo = (paymentObject) => {
    const reason = paymentObject && paymentObject.bill_memo && paymentObject.bill_memo.replace(this.props.claimObject.contract_number + " " + this.props.claimObject.customer_name + " ", "");
    if (this.props.initialStatus === CONSTANTS.AUTO_CLAIM_STATUS_MAP.checkWritten ||
        this.props.initialStatus === CONSTANTS.AUTO_CLAIM_STATUS_MAP.reversed || 
        this.props.initialStatus === CONSTANTS.AUTO_CLAIM_STATUS_MAP.waitingForReversed ||
        this.props.initialStatus === CONSTANTS.AUTO_CLAIM_STATUS_MAP.adjusted ||
        this.props.initialStatus === CONSTANTS.AUTO_CLAIM_STATUS_MAP.waitingForCheck)  {
      return (
        <div>
          <div className="form-group row">
            <div className="col-5">
              Batch #
            </div>
            <div className="col-5">
              {paymentObject && (paymentObject.automotive_intacct_batch_id || "")}
            </div>
          </div>
          {reason && (<div className="form-group row">
            <div className="col-5">
              Reason
            </div>
            <div className="col-7">
              {reason}
            </div>
          </div>)}
          <div className="form-group row">
            <div className="col-5">
              Amount
            </div>
            <div className="col-5">
              {paymentObject && accounting.formatMoney(paymentObject.payment_amount.Valid ? paymentObject.payment_amount.Decimal : paymentObject.amount, '$', 2)}
            </div>
          </div>
          {
            paymentObject && paymentObject.check_details &&
            paymentObject.check_details.map((check_detail, index) => {
              return ([
                <div className="form-group row" key={ `check_number_${index}` }>
                  <div className="col-5">
                          Check #
                  </div>
                  <div className="col-5">
                    {check_detail.check_number}
                  </div>
                </div>,
                <div className="form-group row" key={ `amount_${index}` }>
                  <div className="col-5">
                    Check Amount
                  </div>
                  <div className="col-5">
                    {accounting.formatMoney(check_detail.amount, '$', 2)}
                  </div>
                </div>,
                <div className="form-group row" key={ `paid_date_${index}` }>
                  <div className="col-5">
                        Paid Date
                  </div>
                  <div className="col-5">
                    {moment.utc(check_detail.paid_date).format(dateFormat.displayDateFormat)}
                  </div>
                </div>
              ]);
            })
          }
        </div>
      );
    }
  };

  renderReversalPaymentInfo = () => {
    const {
      reversePaymentInfo,
    } = this.state;
    if(reversePaymentInfo && reversePaymentInfo.length > 0){
      return(
        <ReversalPaymentInfo paymentInfo={ this.state.reversePaymentInfo } claimObject={ this.props.claimObject }/>
      );
    }
  };

  renderPayments = () => {
    const { 
      paymentObject,
    } = this.state;
    if (!paymentObject || paymentObject.length < 1) {
      return (<div></div>);
    }

    const paymentDetails = paymentObject.map((p, i) => {
      return (
        <div key={p.bill_number}>
          {paymentObject.length > 1 && i !== 0 ? this.renderLine(i) : null}
          {this.renderAuthorizationNumber(p)}
          {this.renderBillNumber(p)}
          {this.renderPaymentInfo(p)}
        </div>
      );
    });
    return (
      <div>
        {paymentDetails}
        {this.renderReversalPaymentInfo()}
      </div>
    );
  }

  renderLine = (index) => {
    if (index === 1) {
      return (
        <h2 className='horizentalLineHeaderStyle' > <span className='horizentalLineSpanStyle'>Adjustments</span></h2 >
      );
    }
    return (
      <hr style={{borderTop: 2, backgroundColor: 'black', borderStyle: 'ridge'}}/>
    );
  };

  render() {
    const spinnerMessage = <p className='text-center'><i className="fa fa-refresh fa-spin"/> Loading...</p>;
    return (
      <div>
        <Loader show={ this.state.showLoader } message={ spinnerMessage }>
          {this.renderPayments()}
        </Loader>
      </div>
    );
  }
}