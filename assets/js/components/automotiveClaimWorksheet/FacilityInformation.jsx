import React from "react";
import PropTypes from "prop-types";
import FacilityLookupModal from "./FacilityLookupModal.jsx";
import Loader from "react-loader-advanced";
import NewFacilityModal from "./FacilityCreateModal.jsx";
import UpdateFacilityModal from "./FacilityUpdateModal.jsx";

export default class FacilityInformation extends React.Component {

  static propTypes = {
    claimObject: PropTypes.object.isRequired,
    isDisabled: PropTypes.bool.isRequired,
    loadFacilityDetails: PropTypes.func.isRequired,
    showFacilityLoader: PropTypes.bool.isRequired,
    facilityDetails: PropTypes.shape({
      id: PropTypes.number,
      name: PropTypes.string,
      vendor_id: PropTypes.shape({
        String: PropTypes.string
      }),
      email: PropTypes.shape({
        String: PropTypes.string
      }),
      address: PropTypes.shape({
        String: PropTypes.string
      }),
      phone: PropTypes.shape({
        String: PropTypes.string
      }),
      fax: PropTypes.shape({
        String: PropTypes.string
      }),
      city: PropTypes.string,
      state: PropTypes.string,
      zip: PropTypes.string
    }),
    onUseCurrentFacility: PropTypes.func.isRequired,
    user: PropTypes.shape({
      id: PropTypes.number.isRequired,
      first_name: PropTypes.string.isRequired,
      last_name: PropTypes.string.isRequired,
      email: PropTypes.string.isRequired,
      stores: PropTypes.arrayOf(PropTypes.string.isRequired),
      roles: PropTypes.shape({
        Map: PropTypes.object,
      }).isRequired
    })
  };

  constructor(props) {
    super(props);
    this.state = {
      facilityName: this.props.claimObject.facility_name ? this.props.claimObject.facility_name : '',
      displayFacilityLookupModal: false,
      showAddFacilityModal: false,
      showUpdateFacilityModal: false,
      displayVendorSelectionModal:false,
      selectedFacilityID: '',
      viewFacility: false
    };
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (nextProps.claimObject.facility_name && this.state.facilityName !== nextProps.claimObject.facility_name) {
      this.setState({ facilityName: nextProps.claimObject.facility_name });
    }
  }

  handleOnFacilityChange = (e) => {
    this.setState({ facilityName: e.target.value });
  };

  onFacilitySearchEnter = (e) => {
    if (e.keyCode === 13 || e.which === 13) {
      this.onFacilitySearch();
    }
  };

  onFacilitySearch = () => {
    this.setState({ displayFacilityLookupModal: true });
  };

  closeFacilityLookupModal = () => {
    this.setState({ displayFacilityLookupModal: false });
  };

  onFacilitySelect = (facilityID) => {
    this.setState({ displayFacilityLookupModal: false }, () => {
      this.setState({ selectedFacilityID : facilityID }, () => this.props.loadFacilityDetails(facilityID, true));
    });
  };

  onNewFacilityClick = () => {
    this.setState({ displayFacilityLookupModal: false, showAddFacilityModal: true });
  };

  onCloseNewFacilityModal = () => {
    this.setState({ showAddFacilityModal: false, showUpdateFacilityModal: false, viewFacility: false });
  };

  onUseCurrentFacility = (facility, facilityID) => {
    this.setState({
      facilityName: facility.name,
      showAddFacilityModal: false
    });
    this.props.onUseCurrentFacility(facility, facilityID);
  };

  onActionButtonClick = (viewFacility) => {
    this.setState({
      viewFacility: viewFacility,
      showUpdateFacilityModal: true,
    });
  }

  handleOnRowSelect = (facilityID) => {
    this.setState({
      selectedFacilityID: facilityID,
    });
  }

  render() {
    const {
      viewFacility,
    } = this.state;
    const spinnerMessage = <p className='text-center'><i className="fa fa-refresh fa-spin"/> Loading...</p>;
    return (
      <section className="col">
        <div className="form-group row">
          <div className="col-3">
            <label
              className="col-form-label-sm">
              Facility:
            </label>
          </div>
          <input type="text"
            className="form-control form-control-sm ml-2 col-7"
            id="facility_inputBox"
            disabled={ this.props.isDisabled }
            value={ this.state.facilityName ? this.state.facilityName : "" }
            onBlur={ this.handleOnFacilityChange }
            onChange={ this.handleOnFacilityChange }
            onKeyUp={ this.onFacilitySearchEnter }/>
          <div className="text-primary pl-2">
            <i className="fa fa-search"
              aria-hidden="true"
              id="search-facilities"
              onClick={ !this.props.isDisabled ? this.onFacilitySearch : void 0 }/>
          </div>
        </div>
        <Loader show={ this.props.showFacilityLoader } message={ spinnerMessage }>
          <div className="form-group row">
            <div className="col-3">
              <label className="col-form-label-sm">
                Address:
              </label>
            </div>
            <div className="col-9 col-form-label-sm">
              <div>{ this.props.claimObject.facility_address }</div>
              <div>{ this.props.claimObject.facility_city }</div>
              <div>{ this.props.claimObject.facility_state_code }</div>
              <div>{ this.props.claimObject.facility_postal_code }</div>
              <div>{ this.props.claimObject.facility_country }</div>
            </div>
          </div>
          <div className="form-group row">
            <div className="col-3">
              <label className="col-form-label-sm">
                Phone:
              </label>
            </div>
            <div className="col-9 col-form-label-sm">
              { this.props.claimObject.facility_phone }
            </div>
          </div>
          <div className="form-group row">
            <div className="col-3">
              <label className="col-form-label-sm">
                Fax:
              </label>
            </div>
            <div className="col-9 col-form-label-sm">
              { this.props.claimObject.facility_fax}
            </div>
          </div>
          <div className="form-group row">
            <div className="col-3">
              <label className="col-form-label-sm">
                Email:
              </label>
            </div>
            <div className="col-9 col-form-label-sm">
              { this.props.facilityDetails.email ? this.props.facilityDetails.email.String : ""}
            </div>
          </div>
        </Loader>
        <FacilityLookupModal search={ this.state.facilityName }
          displayModal={ this.state.displayFacilityLookupModal }
          closeModal={ this.closeFacilityLookupModal }
          onActionButtonClick= {this.onActionButtonClick}
          onFacilitySelect={ this.onFacilitySelect }
          onRowSelect={this.handleOnRowSelect}
          onNewFacilityClick={ this.onNewFacilityClick }/>
        <NewFacilityModal displayModal={ this.state.showAddFacilityModal }
          onClose={ this.onCloseNewFacilityModal }
          user={ this.props.user }
          shouldLoadFacility={ true }
          onFacilitySelect={ this.onFacilitySelect }
          onUseCurrentFacility={ this.onUseCurrentFacility }/>
        <UpdateFacilityModal 
          message={ `Labor and part rates for this facility need to be adjusted because they are from last quarter. New rates
                             will be applicable on all open claims.` }
          displayModal={ this.state.showUpdateFacilityModal }
          user={ this.props.user }
          viewFacility={viewFacility}
          onClose={ this.onCloseNewFacilityModal }
          onFacilitySelect={() => {} }
          facilityID= { parseInt(this.state.selectedFacilityID) }
        />
      </section>
    );
  }
}
