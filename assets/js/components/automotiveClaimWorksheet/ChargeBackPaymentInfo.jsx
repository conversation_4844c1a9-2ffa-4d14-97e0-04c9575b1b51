import React, { useEffect, useState } from "react";
import PropTypes from "prop-types";
import Alert from "react-s-alert";
import Loader from "react-loader-advanced";
import moment from "moment";
import accounting from "accounting";

import dateFormat from "./../reusable/Utilities/dateFormat.js";
import { CONSTANTS } from "../reusable/Constants/constants.js";
import { URLS as apiUrls } from "./../reusable/Utilities/urls";

import { json as ajax } from "./../../ajax.js";

const ChargeBackPaymentInfo = (props) => {
  const [paymentObject, setPaymentObject] = useState(undefined);
  const [showLoader, setShowLoader] = useState(false);
  const spinnerMessage = <p className='text-center'><i className="fa fa-refresh fa-spin"/> Loading...</p>;

  useEffect(() => {
    getPaymentDetails();
  }, [props.initialStatus]);
  
  const getPaymentDetails = () => {
    if (displayPaymentDetails()) {
      setShowLoader(true);
      ajax(`${apiUrls.automotivePayments.replace('__claimId__', props.claimObject.id)}`, {}, {}, (data, status) => {
        if (status === 200) {
          setPaymentObject(data.auto_claim_payment);
        } else {
          Alert.error(data.message || "Click the browser's Refresh button to reload the payment data. If the error continues, contact your system administrator.");
        }
        setShowLoader(false);
      });
    }
  };

  const displayPaymentDetails = () => {
    return (props.initialStatus === CONSTANTS.CHARGEBACK_CLAIM_STATUS_MAP.waitingForChargeback ||
      props.initialStatus === CONSTANTS.CHARGEBACK_CLAIM_STATUS_MAP.chargeback ||
      props.initialStatus === CONSTANTS.CHARGEBACK_CLAIM_STATUS_MAP.chargebackCollected);
  };

  const renderAuthorizationNumber = (payment) => {
    if (displayPaymentDetails()) {
      return (
        <div className="form-group row">
          <div className="col-3 col-form-label-sm pl-4">
            Authorization#
          </div>
          <div className="col-9">
            {paymentObject && payment.authorization_number}
          </div>
        </div>
      );
    }
  };

  const renderBillNumber = (payment) => {
    if (displayPaymentDetails()) {
      return (
        <div className="form-group row">
          <div className="col-3 col-form-label-sm pl-4">
            Bill #
          </div>
          <div className="col-9">
            {paymentObject && payment.bill_number}
          </div>
        </div>
      );
    }
  };

  const renderPaymentInfo = (payment) => {
    let reason = payment && payment.bill_memo && payment.bill_memo.replace(props.claimObject.contract_number + " " + props.claimObject.customer_name + " ", "");
    reason = reason === "" ? props.claimObject.notes : "";
    if (displayPaymentDetails())  {
      return (
        <div>
          <div className="form-group row">
            <div className="col-3 col-form-label-sm pl-4">
              Batch #
            </div>
            <div className="col-9">
              {payment && (payment.automotive_intacct_batch_id || "")}
            </div>
          </div>
          {reason && (<div className="form-group row">
            <div className="col-3 col-form-label-sm pl-4">
              Reason
            </div>
            <div className="col-9">
              {reason}
            </div>
          </div>)}
          {
            payment && payment.check_details &&
            payment.check_details.map((check_detail, index) => {
              return ([
                <div className="form-group row" key={ `check_number_${index}` }>
                  <div className="col-3 col-form-label-sm pl-4">
                          Check #
                  </div>
                  <div className="col-9">
                    {check_detail.check_number}
                  </div>
                </div>,
                <div className="form-group row" key={ `amount_${index}` }>
                  <div className="col-3 col-form-label-sm pl-4">
                    Amount
                  </div>
                  <div className="col-9">
                    {accounting.formatMoney(check_detail.amount, '$', 2)}
                  </div>
                </div>,
                <div className="form-group row" key={ `paid_date_${index}` }>
                  <div className="col-3 col-form-label-sm pl-4">
                        Paid Date
                  </div>
                  <div className="col-9">
                    {moment.utc(check_detail.paid_date).format(dateFormat.displayDateFormat)}
                  </div>
                </div>
              ]);
            })
          }
        </div>
      );
    }
  };

  const renderPayments = () => {
    if (!paymentObject || paymentObject.length < 1) {
      return (<div></div>);
    }
    const paymentDetails = paymentObject.map((p, i) => {
      return (
        <div className="col-12" key={p.authorization_number}>
          {renderAuthorizationNumber(p)}
          {renderBillNumber(p)}
          {renderPaymentInfo(p)}
        </div>
      );
    });
    return (
      <div>
        {paymentDetails}
      </div>
    );
  };

  return (
    <div>
      <Loader show={ showLoader } message={ spinnerMessage }>
        {renderPayments()}
      </Loader>
    </div>
  );
};

ChargeBackPaymentInfo.propTypes = {
  claimObject: PropTypes.object.isRequired,
  initialStatus: PropTypes.string.isRequired
};

export default ChargeBackPaymentInfo;