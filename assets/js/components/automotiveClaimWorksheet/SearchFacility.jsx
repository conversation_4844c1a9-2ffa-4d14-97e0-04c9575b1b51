import React from "react";
import PropTypes from "prop-types";
import SearchBox from '../reusable/SearchBox/Search.jsx';
import { URLS as apiUrls } from "./../reusable/Utilities/urls";
import { json as ajax } from "./../../ajax.js";
import <PERSON>ert from "react-s-alert";
import Pagination from './../../Pagination.jsx';
import Loader from 'react-loader-advanced';

export default class SearchFacility extends React.Component {
    static propTypes = {
      search: PropTypes.string,
      refresh: PropTypes.bool,
      onAddNewFacilityClick: PropTypes.func.isRequired,
      onActionButtonClick: PropTypes.func,
      actionButtonText: PropTypes.string,
      selectedFacilityID: PropTypes.number,
      onRowSelect: PropTypes.func.isRequired,
      isActionButtonDisable: PropTypes.bool,
      showActionButton: PropTypes.bool.isRequired,
      viewButtonDisabled: PropTypes.bool,
      user: PropTypes.shape({
        id: PropTypes.number.isRequired,
        first_name: PropTypes.string.isRequired,
        last_name: PropTypes.string.isRequired,
        email: PropTypes.string.isRequired,
        stores: PropTypes.arrayOf(PropTypes.string.isRequired),
        roles: PropTypes.shape({
          Map: PropTypes.object,
        }).isRequired
      })
    };

    constructor(props) {
      super(props);
      this.state = {
        searchQuery: "",
        facilityList: [],
        pageLimit: 20,
        numberOfFacilities: 0,
        page: 1,
        showLoader: false,
        facilitySortOrder: "asc"
      };
      [
        "searchFacility",
        "currentPage",
        "sortFacilityByName",
        "renderTableBodyRow",
        "setPage"
      ].forEach((func) => this[func] = this[func].bind(this));
    }

    componentDidMount(){
      if(this.props.search){
        this.searchFacility(this.props.search);
      }
    }

    UNSAFE_componentWillReceiveProps(nextProps){
      if(nextProps.search && !this.state.searchQuery){
        this.searchFacility(nextProps.search);
      }
      else if(nextProps.refresh && this.state.searchQuery){
        this.searchFacility(this.state.searchQuery);
      }
    }

    loadFacilities() {
      this.setState({ showLoader: true }, () => {
        ajax(`${apiUrls.facilityList}?search=${this.state.searchQuery}&page=${this.currentPage()}&sortByName=${this.state.facilitySortOrder}`, {}, {}, (data, status) => {
          if (status === 200) {
            this.props.onRowSelect(void 0);
            this.setState({
              facilityList: data.facilities,
              numberOfFacilities: data.count,
              showLoader: false
            });
          } else if (status === 404) {
            this.props.onRowSelect(void 0);
            this.setState({
              showLoader: false,
              facilityList: [],
              numberOfFacilities: 0
            });
          } else {
            this.setState({ showLoader: false });
            Alert.error("Failed to fetch facility list. If the error continues, contact your system administrator.");
          }
        });
      });
    }

    onRowSelect(event) {
      this.props.onRowSelect(event.currentTarget.id);
    }
  
    searchFacility(facility) {
      this.setState({
        searchQuery: facility,
        facilityList: [],
        numberOfFacilities: 0, 
        page: 1
      }, function () {
        this.loadFacilities();
      });
    }
  
    setPage(page) {
      this.setState({
        page
      }, function () {
        this.loadFacilities();
      });
    }
  
    currentPage() {
      return this.state.page || 1;
    }
  
    sortFacilityByName() {
      this.setState({
        facilitySortOrder: this.state.facilitySortOrder === "asc" ? "desc" : "asc"
      }, function () {
        this.loadFacilities();
      });
    }

    handleViewFacility = () => {
      this.props.onActionButtonClick(true);
    }
  
    renderFacilityList() {
      if (this.state.facilityList && this.state.facilityList.length > 0) {
        return this.renderFacilityListTable();
      } 
      else if (this.state.searchQuery) {
        return (
          <div className="text-center">
            <p>No Results available for this search query</p>
          </div>
        );
      }
    }
  
    renderFacilityListTable() {
      return (
        <div className="table-responsive claim-list">
          <table className="table table-hover">
            <thead>
              {this.renderTableHeader()}
            </thead>
            <tbody>
              {this.renderTableBody()}
            </tbody>
          </table>
        </div>
      );
    }
  
    renderTableHeader() {
      return (
        <tr>
          <th>Active</th>
          <th className="cursor-pointer" onClick={ this.sortFacilityByName }>
            Facility&nbsp;
            <i className={ this.state.facilitySortOrder === "asc" ? "fa fa-caret-up" : "fa fa-caret-down" }/>
          </th>
          <th>Facility Code</th>
          <th>City</th>
          <th>State</th>
          <th>Zip</th>
        </tr>
      );
    }
  
    renderTableBody() {
      return this.state.facilityList.map(this.renderTableBodyRow);
    }
  
    renderTableBodyRow(facilityData, index) {
      const rowClass = this.props.selectedFacilityID === facilityData.id ? "table-active" : "";
      return (
        <tr key={ index }
          id={ facilityData.id }
          onClick={ this.onRowSelect.bind(this) }
          className={ rowClass }>
          <td>
            <input type="checkbox" checked={ facilityData.is_active } readOnly={true}/>
          </td>
          <td>
            {facilityData.name}
          </td>
          <td>
            {facilityData.facility_code}
          </td>
          <td>
            {facilityData.city}
          </td>
          <td>
            {facilityData.state_code}
          </td>
          <td>
            {facilityData.postal_code}
          </td>
        </tr>
      );
    }
  
    renderPagination() {
      if (this.state.numberOfFacilities > this.state.pageLimit) {
        return (
          <div className="pagination-container clearfix col-12 px-0">
            <div className="pull-left my-1">
              <p>Showing {this.currentPage() * this.state.pageLimit - this.state.pageLimit + 1}&nbsp;
                to {this.currentPage() * this.state.pageLimit > this.state.numberOfFacilities ? this.state.numberOfFacilities : this.currentPage() * this.state.pageLimit}&nbsp;
                of {this.state.numberOfFacilities} items
              </p>
            </div>
            <div className="float-right">
              <Pagination page={ this.currentPage() } count={ this.state.numberOfFacilities }
                limit={ this.state.pageLimit } setPage={ this.setPage }/>
            </div>
          </div>
        );
      }
    }

    renderActionButton(){
      if(this.props.showActionButton){
        return(<button className="btn btn-primary ml-3"
          disabled={ this.props.isActionButtonDisable }
          onClick={ () => this.props.onActionButtonClick(false) }>
          {this.props.actionButtonText}
        </button>);
      }
    }

    render() {
      const headerPosition = { position: "relative", top: "-30px" };
      const spinnerMessage = <p className='text-center'><i className="fa fa-refresh fa-spin"/> Loading...</p>;
      return (
        <Loader show={ this.state.showLoader } message={ spinnerMessage }>
          <span style={ headerPosition }><b>Select Repair Facility</b></span>
          <div className="row">
            <div className="col-12">
              <div className="row">
                <div className="form-inline my-2 col-12">
                  <div className="pull-left col-6 px-0">
                    <SearchBox onSearch={ this.searchFacility }
                      placeholder="Search Facility Name, Code, State, ZIP, etc"
                      value={ this.state.searchQuery }/>
                  </div>
                  <div className="col-6 px-0 float-right">
                    <div className="form-group float-right">
                      <button className="btn btn-primary"
                        disabled= {this.props.viewButtonDisabled}
                        onClick={ this.handleViewFacility }>
                        View Facility
                      </button>
                      {this.renderActionButton()}
                      <button className="btn btn-primary ml-3"
                        onClick={ this.props.onAddNewFacilityClick }>
                        New Facility
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              {this.renderFacilityList()}
              {this.renderPagination()}
            </div>
          </div>
        </Loader>
      );
    }
}
