import React, { useEffect, useState } from 'react';
import { json as ajax } from './../../ajax.js';
import Alert from "react-s-alert";

function initialState() {
  return (
    {
      gap : 0,
      auto: 0,
      auto_maintenance: 0,
      lwt: 0,
      vta: 0,
    }
  );
}

export default function APReceive() {

  const [runningAPReceive, setRunningAPReceive] = useState(false);
  const [errors, setErrors] = useState([]);
  const [state, setState] = useState(()=>initialState());
  const [isRun, setIsRun] = useState(false);

  useEffect(() => {
    document.title = 'TCA Admin | APReceive';
  },[]);
  
  const renderButton = () => {
    return (
      <button type='button' className='btn btn-primary' disabled={runningAPReceive} onClick={runAPReceive}>
        <i className='fa fa-check' /> Run APReceive
      </button>);
  };

  const renderLoading = () =>  {
    return (
      <h1> <i className='fa fa-refresh fa-spin'></i> Running APReceive... </h1>
    );
  };

  const runAPReceive = () => {
    setIsRun(false);
    setRunningAPReceive(true);
    const url = '/api/apreceive';
    ajax(url, {}, { method: 'PUT' }, (data, status) => {
      setIsRun(true);
      if (status === 200) {
        setRunningAPReceive(false);
        setErrors(data.errors);
        setState({
          ...state,
          gap:data.gap,
          auto: data.auto,
          auto_maintenance: data.auto_maintenance,
          lwt: data.lwt,
          vta: data.vta,
        });
      } else {
        setRunningAPReceive(false);
        if (data.message) {
          Alert.error(data.message);
        } else {
          Alert.error("Error in APReceive");
        }
      }
    });
  };

  const renderErrors = () => {
    if (errors && errors.length > 0)
      return errors.map((error, index) => {
        return (<div key={index}><label key={index}>{error}</label></div>);
      });
  };

  const renderResults = () => {
    const {
      gap,
      auto,
      auto_maintenance,
      lwt,
      vta,
    } = state;

    return (
      <div className="row">
        <div className="claim-list col-12">
          <table className="table table-striped" id="batch-claim-list">
            <thead>
              <tr className="row">
                <th className="col-3">Type</th>
                <th className="col-3">Updated Records</th>
              </tr>
            </thead>
            <tbody>
              <tr className="row" key={ "gap" } id={ 1 }>
                <td className="col-3">GAP</td>
                <td className="col-3">{gap}</td>
              </tr>
              <tr className="row" key={ "auto" } id={ 2 }>
                <td className="col-3">Auto</td>
                <td className="col-3">{auto}</td>
              </tr>
              <tr className="row" key={ "auto_mnt" } id={ 3 }>
                <td className="col-3">AutoMaintenance</td>
                <td className="col-3">{auto_maintenance}</td>
              </tr>
              <tr className="row" key={ "lwt" } id={ 4 }>
                <td className="col-3">LWT</td>
                <td className="col-3">{lwt}</td>
              </tr>
              <tr className="row" key={ "vta" } id={ 5 }>
                <td className="col-3">VTA</td>
                <td className="col-3">{vta}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    );


  };
  
  return (
    <div>
      <div>
        {renderButton()}
        <p></p>
      </div>
      <div>
        {runningAPReceive && renderLoading()}
      </div>
      <div> 
        {isRun && <h4>Results</h4>}
        {isRun && renderResults()}
      </div>
      <div>
        {isRun && <h4>Errors</h4>}
        {isRun && renderErrors()}
      </div>
    </div>
  );
}