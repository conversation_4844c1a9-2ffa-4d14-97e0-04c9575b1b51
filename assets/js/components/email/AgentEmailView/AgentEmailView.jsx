import React from 'react';
import PageHeader from '../../pageHeader/PageHeader.jsx';
import GapDetailsHeader from '../../reusable/Gap/GapDetailsHeader.jsx';
import Editor from "../../reusable/WYSIWYGEditor/Editor.jsx";
import <PERSON><PERSON> from "react-s-alert";
import { json as ajax } from "./../../../ajax.js";
import { URLS as apiUrls } from "./../../reusable/Utilities/urls";
import { addRecordNote } from "./../../reusable/RecordNotes/addRecordNotes.js";
import { CONSTANTS } from "./../../reusable/Constants/constants.js";
import Loader from "react-loader-advanced";
import dateFormat from "./../../reusable/Utilities/dateFormat.js";
import moment from "moment";
import If from "./../../reusable/If/If.jsx";
import PropTypes from 'prop-types';

const editorStyle = { minHeight: 500, maxHeight: 350, overflowY: 'scroll' };

export default class AgentEmailView extends React.Component {

  static contextTypes = {
    router: PropTypes.object.isRequired
  };

  static propTypes = {
    location: PropTypes.shape({
      query: PropTypes.shape({
        id: PropTypes.string.isRequired,
        contractNumber: PropTypes.string.isRequired,
        vin: PropTypes.string.isRequired,
        customerName: PropTypes.string.isRequired,
        customerID: PropTypes.string.isRequired
      }).isRequired,
    }).isRequired,
    user: PropTypes.shape({
      id: PropTypes.number.isRequired,
      first_name: PropTypes.string.isRequired,
      last_name: PropTypes.string.isRequired,
      email: PropTypes.string.isRequired,
      stores: PropTypes.arrayOf(PropTypes.string.isRequired),
      roles: PropTypes.shape({
        Map: PropTypes.object,
      }).isRequired,
    }),
  };

  constructor(props) {
    super(props);
    this.state = {
      showLoader: false,
      templateList: [],
      letterType: "",
      letterMethod: "",
      letterLanguage: "",
      customerDetails: {},
      content: '',
      templateId: '',
      showLetterListLoader: false,
      letterList: []
    };
    this.loadCustomerDetails = this.loadCustomerDetails.bind(this);
    this.loadTemplateIndex = this.loadTemplateIndex.bind(this);
    this.loadLetterList = this.loadLetterList.bind(this);
    this.loadTemplate = this.loadTemplate.bind(this);
    this.getTemplateId = this.getTemplateId.bind(this);
    this.getTemplate = this.getTemplate.bind(this);
    this.handleSend = this.handleSend.bind(this);
    this.onLetterTypeChange = this.onLetterTypeChange.bind(this);
    this.getTemplateTypes = this.getTemplateTypes.bind(this);
    this.renderLetterTypeSelection = this.renderLetterTypeSelection.bind(this);
    this.renderLetterMethodSelection = this.renderLetterMethodSelection.bind(this);
    this.onLetterLanguageChange = this.onLetterLanguageChange.bind(this);
    this.renderLetterLanguageSelection = this.renderLetterLanguageSelection.bind(this);
    this.getAddressLine = this.getAddressLine.bind(this);
    this.renderEditor = this.renderEditor.bind(this);
    this.renderLetterList = this.renderLetterList.bind(this);
  }

  componentDidMount() {
    this.loadTemplateIndex();
    this.loadCustomerDetails();
    this.loadLetterList();
  }

  loadCustomerDetails() {
    this.setState({ showLoader: true }, () => {
      ajax(`${apiUrls.customers}/${this.props.location.query.contractNumber}`, {}, {}, (data, status) => {
        if (status === 200) {
          this.setState({
            showLoader: false,
            customerDetails: data.customer
          });
        } else if (status === 404) {
          this.setState({ showLoader: false });
        } else {
          this.setState({ showLoader: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  }

  loadTemplateIndex() {
    this.setState({ showLoader: true }, () => {
      ajax(`${apiUrls.templates}?template_type=GAP`, {}, {}, (data, status) => {
        if (status === 200) {
          this.setState({
            showLoader: false,
            templateList: data.email_templates
          });
        } else if (status === 404) {
          this.setState({ showLoader: false });
        } else {
          this.setState({ showLoader: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  }

  loadLetterList() {
    this.setState({ showLetterListLoader: true }, () => {
      let url = apiUrls.letters;
      url = url.replace("__claimId__", this.props.location.query.id);
      ajax(url, {}, {}, (data, status) => {
        if (status === 200) {
          this.setState({
            showLetterListLoader: false,
            letterList: data.letters
          });
        } else {
          this.setState({ showLetterListLoader: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  }

  loadTemplate() {
    if (this.state.letterType && this.state.letterLanguage) {
      this.setState({ templateId: this.getTemplateId() }, function () {
        if (this.state.templateId) {
          this.getTemplate();
        }
      });
    }
  }

  getTemplateId() {
    for (let index = 0; index < this.state.templateList.length; index++) {
      if (this.state.letterType === this.state.templateList[index]['name'] && this.state.letterLanguage === this.state.templateList[index]['language']) {
        return this.state.templateList[index]['id'];
      }
    }
  }

  getTemplate() {
    let url = apiUrls.emailTemplate;
    url = url.replace("__claimId__", this.props.location.query.id);
    url = url.replace("__templateId__", this.state.templateId);
    this.setState({ showLoader: true }, () => {
      ajax(url, {}, {}, (data, status) => {
        if (status === 200) {
          this.setState({
            showLoader: false,
            content: data.email_body
          });
        } else {
          this.setState({ showLoader: false }, () => {
            Alert.error("Click the browser's Refresh button to reload the data. If the error continues, contact your system administrator.");
          });
        }
      });
    });
  }

  handleSend() {
    let url = apiUrls.letter.replace("__claimId__", this.props.location.query.id);
    if (this.state.letterMethod === 'Email' || this.state.letterMethod === 'Both') {
      url += '?email=true';
    }
    const encodedText = btoa(unescape(encodeURIComponent(this.state.content)));
    const mailBody = { "email_body": encodedText, "letter_type": this.state.letterType };
    this.setState({ showLoader: true }, () => {
      ajax(url, mailBody, { method: "POST" }, (data, status) => {
        if (status === 200) {
          this.setState({
            showLoader: false
          });
          let noteText = "";
          if (this.state.letterMethod === 'Email' || this.state.letterMethod === 'Both') {
            Alert.success("E-mail sent successfully.");
            noteText = `${this.state.letterType} Emailed to Customer`;
            addRecordNote(this.props.location.query.id, noteText);
          }
          if (this.state.letterMethod === 'Print' || this.state.letterMethod === 'Both') {
            noteText = `${this.state.letterType} Mailed to Customer`;
            addRecordNote(this.props.location.query.id, noteText);
            this.editor.print();
          }
          this.loadLetterList();
        } else {
          if (data.message === 'Error in getting email address') {
            this.setState({ showLoader: false }, () => {
              Alert.error("Customer's email address is not available, Please add email address before proceeding.");
            });
          } else {
            this.setState({ showLoader: false }, () => {
              Alert.error("Click the browser's Refresh button to reload the data. If the error continues, contact your system administrator.");
            });
          }
        }
      });
    });
  }

  onLetterTypeChange(e) {
    this.setState({ letterType: e.target.value, letterLanguage: 'English', content: '' }, function () {
      /** TODO: English is added as default language here. This needs to be updated when we add preferred language in GAP checklist */
      this.loadTemplate();
    });
  }

  findTemplate(templateName, currentTemplate) {
    return currentTemplate.name === templateName;
  }

  /**
   * This method return list of unique template types.
   * */

  getTemplateTypes() {
    if (this.state.templateList) {
      const templateTypes = [];
      this.state.templateList.forEach(template => {
        if (templateTypes.length === 0) {
          templateTypes.push(template);
        } else {
          const foundTemplate = templateTypes.findIndex(this.findTemplate.bind(null, template.name));
          if (-1 === foundTemplate) {
            templateTypes.push(template);
          }
        }
      });
      return templateTypes;
    }
  }

  renderLetterTypeSelection() {
    return (
      <select className="form-control form-control-sm"
        onChange={this.onLetterTypeChange}
        value={this.state.letterType}>
        <option value="">Select Letter</option>
        {
          this.getTemplateTypes().map(template => <option key={template.id}
            value={template.name}>{template.name}</option>)
        }
      </select>
    );
  }

  renderLetterMethodSelection() {
    return (
      <select className="form-control form-control-sm"
        onChange={(e)=> {
          this.setState({ letterMethod: e.target.value });
        }}
        value={this.state.letterMethod}>
        <option value="">Select Method</option>
        {
          CONSTANTS.LETTER_METHOD.map((method, index) => <option key={index} value={method.type}>{method.type}</option>)
        }
      </select>
    );
  }

  onLetterLanguageChange(e) {
    this.setState({ letterLanguage: e.target.value, content: '' }, function () {
      this.loadTemplate();
    });
  }

  renderLetterLanguageSelection() {
    return (
      <select className="form-control form-control-sm"
        onChange={this.onLetterLanguageChange}
        value={this.state.letterLanguage}>
        <option value="">Select Language</option>
        {
          this.state.templateList.map(template => {
            if (template.name === this.state.letterType) {
              return <option key={template.id} value={template.language}>{template.language}</option>;
            }
          })
        }
      </select>
    );
  }

  getAddressLine() {
    const address = "";
    if (this.state.customerDetails.city && this.state.customerDetails.state && this.state.customerDetails.postal_code) {
      return address.concat(this.state.customerDetails.city, ", ", this.state.customerDetails.state, " ", this.state.customerDetails.postal_code);
    }
  }

  renderEditor() {
    if (this.state.content) {
      return (
        <Editor content={this.state.content}
          ref={(editor) => this.editor = editor}
          editorStyle={editorStyle}
          title={this.state.letterType}
          onChange={(event)=> {
            this.setState({ content: event.target.value });
          }}/>
      );
    } else if (!this.state.letterType) {
      return (
        <p className="text-center">Please select Letter from dropdown.</p>
      );
    }
  }

  renderLetterList() {
    if (this.state.letterList && this.state.letterList.length) {
      return this.state.letterList.map(element => <div key={element.document_id}>
        <div className="col-12 mb-2">
          <a target="_blank"
            rel="noopener noreferrer"
            className="d-inline-block"
            href={`${apiUrls.document}/${element.document_id}`}>{element.letter_type}</a>
          <span
            className="small">{` - ${moment.utc(element.sent_at, dateFormat.backendDateFormat).local().format(dateFormat.displayDateFormat)}`}</span>
        </div>
      </div>);
    }
  }

  render() {
    const spinnerMessage = <p className="text-center"><i className="fa fa-refresh fa-spin"/> Loading...</p>;
    return (
      <Loader show={this.state.showLoader} message={spinnerMessage}>
        <section className="row">
          <div className="col-12">
            <PageHeader pageTitle="GAP Customer Letter" user={this.props.user}/>
            <div className="col-9">
              <GapDetailsHeader
                contractNumber={ this.props.location.query.contractNumber }
                vehicleNumber={ this.props.location.query.vin }
                customerName={ this.props.location.query.customerName }
                nextButtonText='Send'
                nextButtonDisabled={ !this.state.content || !this.state.letterMethod }
                nextButtonClassName="fa-check"
                nextButtonOnClick={ this.handleSend }
                backButtonOnClick={ () => {
                  this.context.router.goBack();
                } }
              />
            </div>
            <div className="row">
              <div className="col-12 mt-2 row">
                <div className="col-9">
                  {this.renderEditor()}
                </div>
                <div className="col-3 pl-5">
                  <div className="form-group row">
                    <label className="col-4 col-form-label">Letter:</label>
                    <div className="col-8">
                      { this.renderLetterTypeSelection() }
                    </div>
                  </div>
                  <div className="form-group row">
                    <label className="col-4 col-form-label">Method:</label>
                    <div className="col-8">
                      { this.renderLetterMethodSelection() }
                    </div>
                  </div>
                  <div className="form-group row">
                    <label className="col-4 col-form-label">Language:</label>
                    <div className="col-8">
                      { this.renderLetterLanguageSelection() }
                    </div>
                  </div>
                  <br/>
                  <div className="form-group row">
                    <div className="col-12">
                      { this.props.location.query.customerName }
                    </div>
                  </div>
                  <div className="form-group row">
                    <div className="col-12">
                      <ul className="list-unstyled">
                        <li>{ this.state.customerDetails.street_address }</li>
                        <li>
                          { this.getAddressLine() }
                        </li>
                      </ul>
                    </div>
                  </div>
                  <div className="form-group row">
                    <div className="col-12">
                      { this.state.customerDetails.email_address }
                    </div>
                  </div>
                  <div className="form-group row">
                    <div className="col-12">
                      { this.state.customerDetails.phone_number }
                    </div>
                  </div>
                  <If condition={this.state.letterList.length}>
                    <div className="card">
                      <Loader show={this.state.showLetterListLoader} message={spinnerMessage}>
                        {this.renderLetterList()}
                      </Loader>
                    </div>
                  </If>
                </div>
              </div>
            </div>
          </div>
        </section>
      </Loader>
    );
  }
}