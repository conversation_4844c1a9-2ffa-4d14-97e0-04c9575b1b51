import React from 'react';
import PropTypes from 'prop-types';

export default class Flash extends React.Component {
  static propTypes =  {
    flashes: PropTypes.arrayOf(PropTypes.shape({
      Group: PropTypes.string.isRequired,
      Message: PropTypes.string.isRequired,
    }).isRequired).isRequired,
  };

  shouldComponentUpdate (nextProps, nextState) {
    if (nextProps.flashes.length !== this.props.flashes.length) {
      return true;
    }
    for (var i = 0; i < nextProps.flashes.length; i++) {
      var nextProp = nextProps.flashes[i];
      var thisProp = this.props.flashes[i];
      if (
        (nextProp.Group !== thisProp.Group) ||
        (nextProp.Message !== thisProp.Message)
      ) {
        return true;
      }
    }
    return false;
  }

  render() {
    return (
      <div id="flashes">
        { this.renderList() }
      </div>
    );
  }

  renderList() {
    var flashes = [];
    if (this.props.flashes && this.props.flashes.length > 0) {
      for (var i = 0; i < this.props.flashes.length; i++) {
        var flash = this.props.flashes[i];
        flashes.push(
          <div key={ i } className={ "callout " + flash.Group }>
            { flash.Message }
          </div>
        );
      }
    }
    return flashes;
  }
}

