import React, { Component } from "react";
import Modal from "./Modal.jsx";
import Support from "./Support.jsx";
import <PERSON><PERSON>he<PERSON> from "./VersionChecker.jsx";
import { json as ajax } from "./ajax.js";
import Alert from "react-s-alert";
import PropTypes from "prop-types";
import { userHasRole } from "./components/reusable/Utilities/userHasRole";
import { CONSTANTS } from "./components/reusable/Constants/constants";
import { getClasses } from "./components/reusable/Utilities/headerClasses";
import Loader from "react-loader-advanced";

export default class App extends Component {
  static contextTypes = {
    router: PropTypes.object.isRequired
  };

  static propTypes = {
    main: PropTypes.shape({}).isRequired,
    header: PropTypes.shape({}),
    location: PropTypes.shape({
      pathname: PropTypes.string.isRequired,
    }).isRequired,
  };

  constructor(props) {
    super(props);
    this.state = {
      sessionLoading: true,
      sessionLoaded: false,
      user: undefined,
      userLoggingOut: false,
      routeUnlisten: undefined,
      storeSwitching: false,
      hasNewVersion: false,
      showNewVersionModal: false,
    };
  }

  componentWillUnmount = () => {
    VersionChecker.stop();
    if (this.state.routeUnlisten) {
      this.state.routeUnlisten();
    }
  };

  componentDidMount = () => {
    VersionChecker.start(this.versionDiffed);
    this.loadUser();
  };

  versionDiffed = () => {
    this.setState({ hasNewVersion: true, showNewVersionModal: true });
  };

  openNewVersionModal = () => {
    this.setState({ showNewVersionModal: true });
  };

  closeNewVersionModal = () => {
    this.setState({ showNewVersionModal: false });
  };

  loadUser = () => {
    this.setState({ sessionLoading: true }, () => {
      ajax(
        '/session',
        {},
        {},
        (data, status, xhr) => {
          this.setState({ sessionLoading: false }, () => {
            if (status === 200) {
              this.setState({ sessionLoaded: true, user: data.User });
            } else {
              this.setState({ sessionLoaded: false }, () =>{
                this.context.router.push('/logged-out');
              });
            }
          });
        }
      );
    });
  };

  redirectUserBasedOnRole = () => {
    if ((userHasRole(this.state.user, CONSTANTS.USER_ROLES.autoClaims) || userHasRole(this.state.user, CONSTANTS.USER_ROLES.autoClaimsManager)) &&
      (userHasRole(this.state.user, CONSTANTS.USER_ROLES.gapClaims) || userHasRole(this.state.user, CONSTANTS.USER_ROLES.gapClaimsManager))) {
      this.context.router.push('/automotive-claims-list');
    } else if(userHasRole(this.state.user, CONSTANTS.USER_ROLES.gapClaims) || userHasRole(this.state.user, CONSTANTS.USER_ROLES.gapClaimsManager)) {
      this.context.router.push('/gap-claims-list');
    } else if(userHasRole(this.state.user, CONSTANTS.USER_ROLES.autoClaims) ||
      userHasRole(this.state.user, CONSTANTS.USER_ROLES.autoClaimsManager) ||
      userHasRole(this.state.user, CONSTANTS.USER_ROLES.viewOnlyClaims)) {
      this.context.router.push('/automotive-claims-list');
    } else if(userHasRole(this.state.user, CONSTANTS.USER_ROLES.accounting)) {
      this.context.router.push('/gap-finance-list');
    } else if (userHasRole(this.state.user, CONSTANTS.USER_ROLES.recoveryTeam)) {
      this.context.router.push('/gap-claims-recovery-list');
    }
  };

  logoutUser = () => {
    this.setState({ userLoggingOut: true }, () => {
      ajax(
        '/session',
        {},
        { method: "DELETE" },
        (data, status, xhr) => {
          this.setState({ userLoggingOut: false }, () => {
            if (status === 200) {
              this.setState({ sessionLoaded: false, user: undefined }, () => {
                this.context.router.push('/logged-out');
              });
            }
          });
        }
      );
    });
  };

  render() {
    if (!this.state.sessionLoaded) {
      const spinnerMessage = (<p className='text-center'><i className="fa fa-refresh fa-spin"/> Loading...</p>);
      const style = { minHeight: '500px' };
      return (
        <Loader show={this.state.sessionLoading} message={ spinnerMessage } style={ style }>
          <div></div>
        </Loader>
      );
    }

    const {
      user: {
        banner_info: bannerInfo,
      }
    } = this.state;
    const classes = getClasses(bannerInfo, true);
    return (
      <div className='container'>
        { this.renderNewVersion() }
        <Support user={ this.state.user } />
        { this.renderHeader() }
        <div className={classes}>
          <Alert stack={ { limit: 3 } } offset={ 30 } position={ 'top-right' } effect={ 'slide' } timeout={ 5000 } />
          { this.renderContent() }
        </div>
      </div>
    );
  }

  renderHeader = () => {
    if (this.props.header) {
      return React.cloneElement(this.props.header, {
        logoutUser: this.logoutUser,
        user: this.state.user
      });
    }
  };

  renderNewVersion = () => {
    let click = function(e){
      e.preventDefault();
      window.location.reload(true);
    };
    if (this.state.hasNewVersion) {
      return (
        <Modal visible={ this.state.showNewVersionModal } close={ this.closeNewVersionModal }>
          <h1><i className='fa fa-warning' /> New Version</h1>
          There is a new version of this TCA Portal app. Please <a onClick={ click }>refresh</a> the page.
        </Modal>
      );
    }
  };

  renderContent = () => {
    if (this.state.userLoggingOut) {
      return <div key='logging-out'>Logging out...</div>;
    } else if (this.state.sessionLoaded) {
      return React.cloneElement(this.props.main, { key: this.props.location.pathname, user: this.state.user });
    } else if (!this.state.sessionLoaded) {
      return <div key='logged-out'>Logged out</div>;
    } else if (this.state.sessionLoading) {
      return (
        <div key='loading-app'>
          <i className="fa fa-refresh fa-spin" /> Loading app...
        </div>
      );
    } else {
      return <div key='app-problem'>Problem loading app. Please try again.</div>;
    }
  };
}
