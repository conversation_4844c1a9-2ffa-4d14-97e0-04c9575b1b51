import React from 'react';
import PropTypes from 'prop-types';

import { userHasRole } from "../components/reusable/Utilities/userHasRole";
import { CONSTANTS } from "../components/reusable/Constants/constants";

export default class Home extends React.Component {

  static contextTypes = {
    router: PropTypes.object.isRequired,
  };

  static propTypes = {
    user: PropTypes.shape({
      id: PropTypes.number.isRequired,
      first_name: PropTypes.string.isRequired,
      last_name: PropTypes.string.isRequired,
      email: PropTypes.string.isRequired,
      stores: PropTypes.arrayOf(PropTypes.string.isRequired),
      roles: PropTypes.shape({
        Map: PropTypes.object,
      }).isRequired
    })
  };

  redirectUserBasedOnRole() {
    if ((userHasRole(this.props.user, CONSTANTS.USER_ROLES.autoClaims) || userHasRole(this.props.user, CONSTANTS.USER_ROLES.autoClaimsManager)) &&
      (userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaims) || userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaimsManager))
    || userHasRole(this.props.user, CONSTANTS.USER_ROLES.accounting)) {
      this.context.router.push('/automotive-claims-list');
    } else if (userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaims) || userHasRole(this.props.user, CONSTANTS.USER_ROLES.gapClaimsManager)) {
      this.context.router.push('/gap-claims-list');
    } else if (userHasRole(this.props.user, CONSTANTS.USER_ROLES.autoClaims) ||
    userHasRole(this.props.user, CONSTANTS.USER_ROLES.autoClaimsManager) ||
    userHasRole(this.props.user, CONSTANTS.USER_ROLES.viewOnlyClaims)
    ) {
      this.context.router.push('/automotive-claims-list');
    }
  }

  componentDidMount () {
    document.title = 'TCA Portal';
    this.redirectUserBasedOnRole();
  }

  render () {
    return <div>
      <h1>Home</h1>
    </div>;
  }
}

