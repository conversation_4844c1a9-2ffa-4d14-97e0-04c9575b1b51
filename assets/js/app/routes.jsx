import React from 'react';
import { Router, IndexRoute } from 'react-router';
import Header from './Header.jsx';
import NotFound from '../NotFound.jsx';
import Home from './Home.jsx';
import GapClaimsDashboard from './../components/gapClaimsDashboard/GapClaimsDashboard.jsx';
import AutomotiveClaimsDashboard from './../components/automotiveClaimsDashboard/AutomotiveClaimsDashboard.jsx';
import GapClaimWorksheet from './../components/claimCheckList/GapClaimWorksheet.jsx';
import AutomotiveClaimWorksheet from './../components/automotiveClaimWorksheet/AutomotiveClaimWorksheet.jsx';
import ContractsDashboard from '../components/contracts/ContractsDashboard.jsx';
import GapContract from '../components/contracts/Contract.jsx';
import TemplateDashboard from './../components/template/TemplateDashboard.jsx';
import AgentEmailView from "./../components/email/AgentEmailView/AgentEmailView.jsx";
import ManageZones from "./../components/manageAutomotiveZones/ManageZones.jsx";
import ReassignClaims from "./../components/automotiveClaimsDashboard/ReassignClaims.jsx";
import SubmitClaims from "./../components/automotiveClaimsDashboard/SubmitClaims.jsx";
import GapClaimsFinanceDashboard from "./../components/gapClaimsFinanceDashboard/GapClaimsFinanceDashboard.jsx";
import GapBatchClaimsDashboard from "./../components/gapBatchClaimsDashboard/GapBatchClaimsDashboard.jsx";
import GapBatchDetails from "../components/gapBatchClaimsDashboard/GapBatchDetails.jsx";
import GapBatchHistory from "../components/gapBatchClaimsDashboard/BatchHistory.jsx";
import AutomotivePreApprovedLimits from "./../components/users/AutomotiveUsersPreApprovedLimits.jsx";
import LwtGapUserPreApprovedLimits from "./../components/users/LwtGapUsersPreApprovedLimit.jsx";
import AutomotiveBatchHistory from "../components/automotiveBatchDashboard/BatchHistory.jsx";
import AutomotiveBatchDetails from "../components/automotiveBatchDashboard/BatchDetails.jsx";
import AutomotiveBatchDashboard from "../components/automotiveBatchDashboard/BatchDashboard.jsx";
import AutomotiveSearchFacility from "../components/automotiveBatchDashboard/SearchFacilityPage.jsx";
import FacilityAuditReport from "../components/automotiveBatchDashboard/FacilityAuditReport.jsx";
import GapClaimsRecoveryDashboard from "../components/gapClaimsRecoveryDashboard/GapClaimsRecoveryDashboard.jsx";
import GapClaimRecoveryWorksheet from "../components/gapClaimRecoveryCheckList/GapClaimRecoveryWorksheet.jsx";
import VTAClaimsDashboard from "../components/vtaClaimsDashboard/VTAClaimsDashboard.jsx";
import VTAClaimChecklist from "../components/vtaClaimCheckList/VTAClaimWorksheet.jsx";
import AutomotiveCCReconciliation from "../components/automotiveCCReconciliation";
import AutomotiveCCReconciliationHistory from "../components/automotiveCCReconciliationHistory/AutomotiveCCReconciliationHistory.jsx";
import CreditManage from "../components/creditManage/CreditManage.jsx";
import LoggedOut from "./LoggedOut.jsx";
import LWTClaimsDashboard from './../components/lwtClaimsDashbord/LWTClaimsDashboard.jsx';
import LWTEmailView from "./../components/email/AgentEmailView/LWTEmailView.jsx";
import LWTClaimChecklist from "../components/lwtClaimCheckList/LWTClaimWorksheet.jsx";
import APReceive from "./../components/apreceive/APReceive.jsx";

export default [
  <IndexRoute key="index" components={ { main: Home, header: Header } }/>,

  <Router key="gapClaimsDashboard"
    path="/gap-claims-list"
    components={ { main: GapClaimsDashboard, header: Header } }/>,

  <Router key="gapClaimsWorksheet"
    path="/gap-claims"
    components={ { main: GapClaimWorksheet, header: Header } }/>,

  <Router key="gapClaimsFinanceDashboard"
    path="/gap-finance-list"
    components={ { main: GapClaimsFinanceDashboard, header: Header } }/>,

  <Router key="gapBatchClaimsDashboard"
    path="/gap-batch-claims"
    components={ { main: GapBatchClaimsDashboard, header: Header } }/>,

  <Router key="gapBatchDetails"
    path="/gap-batch-claims/:id"
    components={ { main: GapBatchDetails, header: Header } }/>,

  <Router key="gapBatchHistory"
    path="/gap-batch-history"
    components={ { main: GapBatchHistory, header: Header } }/>,

  <Router key="gapClaimsRecovery"
    path="/gap-claims-recovery-list"
    components={ { main: GapClaimsRecoveryDashboard, header: Header } }/>,

  <Router key="gapClaimsRecoveryWorksheet"
    path="/gap-claims-recovery"
    components={ { main: GapClaimRecoveryWorksheet, header: Header } }/>,

  <Router key="vtaClaimsDashboard"
    path="/vta-claims-list"
    components={ { main: VTAClaimsDashboard, header: Header } }/>,
  
  <Router key="vtaClaimChecklist"
    path="/vta-claims"
    components={ { main: VTAClaimChecklist, header: Header } }/>,

  <Router key="automotiveClaimsDashboard"
    path="/automotive-claims-list"
    components={ { main: AutomotiveClaimsDashboard, header: Header } }/>,

  <Router key="lwtClaimsDashboard"
    path="/lwt-claims-list"
    components={ { main: LWTClaimsDashboard, header: Header } }/>,

  <Router key="automotiveClaimWorksheet"
    path="/automotive-claims"
    components={ { main: AutomotiveClaimWorksheet, header: Header } }/>,

  <Router key="automotiveManageZones"
    path="/automotive-manage-zones"
    components={ { main: ManageZones, header: Header } }/>,

  <Router key="automotiveCCReconciliation"
    path="/cc-reconciliation"
    components={ { main: AutomotiveCCReconciliation, header: Header } }/>,

  <Router key="automotiveCCReconciliationHistory"
    path="/cc-reconciliation-history"
    components={ { main: AutomotiveCCReconciliationHistory, header: Header } }/>,

  <Router key="gapClaimEmail"
    path="/gap-email"
    components={ { main: AgentEmailView, header: Header } }/>,

  <Router key="gapContracts"
    path="/contracts"
    components={ { main: ContractsDashboard, header: Header } }/>,

  <Router key="gapContract"
    path="/gap-contract/:id"
    components={ { main: GapContract, header: Header } }/>,

  <Router key="templates"
    path="/admin"
    components={ { main: TemplateDashboard, header: Header } }/>,

  <Router key="reassignClaims"
    path="/automotive-claims-list/reassign"
    components={ { main: ReassignClaims, header: Header } }/>,

  <Router key="submitClaims"
    path="/automotive-claims-list/submit"
    components={ { main: SubmitClaims, header: Header } }/>,

  <Router key="automotiveClaimPreApprovedLimits"
    path="/pre-approved-limits/automotive-claims"
    components={ { main: AutomotivePreApprovedLimits, header: Header } }/>,
    
  <Router key="lwtgapClaimPreApprovedLimits"
    path="/pre-approved-limits/lwt-gap-claims"
    components={ { main: LwtGapUserPreApprovedLimits, header: Header } }/>,

  <Router key="automotiveBatchHistory"
    path="/automotive-batch-history"
    components={ { main: AutomotiveBatchHistory, header: Header } }/>,

  <Router key="automotiveBatchDetails"
    path="/automotive-batch-claims/:id"
    components={ { main: AutomotiveBatchDetails, header: Header } }/>,

  <Router key="automotiveBatchDashboard"
    path="/automotive-batch-dashboard"
    components={ { main: AutomotiveBatchDashboard, header: Header } }/>,

  <Router key="automotiveSearchFacility"
    path="/automotive-search-facility"
    components={ { main: AutomotiveSearchFacility, header: Header } }/>,

  <Router key="automotiveFacilityReport"
    path="/automotive-facility-report"
    components={ { main: FacilityAuditReport, header: Header } }/>,

  <Router key="creditManage"
    path="/credit-manage"
    components={ { main: CreditManage, header:Header } }/>,

  <Router key="lwtClaimChecklist"
    path="/lwt-claims"
    components={ { main: LWTClaimChecklist, header: Header } }/>,

  <Router key="lwtClaimEmail"
    path="/lwt-email"
    components={ { main: LWTEmailView, header: Header } }/>,

  <Router key="apReceive"
    path="/apreceive"
    components={ { main: APReceive, header: Header } }/>,

  <Router key="logged-out"
    path="/logged-out"
    component={ LoggedOut } />,

  <Router key="notFound"
    path="*"
    components={ { main: NotFound } }/>
];
