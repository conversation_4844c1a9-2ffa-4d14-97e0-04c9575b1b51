import React from 'react';
import PropTypes from "prop-types";
import { Link } from 'react-router';
import { userHasRole } from '../components/reusable/Utilities/userHasRole';
import ConfirmationModal from '../components/reusable/ConfirmationModal/ConfirmationModal';
const USER_ROLES = require('../components/reusable/Constants/constants').CONSTANTS.USER_ROLES;

export default class Header extends React.Component{

  static contextTypes = {
    router: PropTypes.object.isRequired
  };

  static propTypes = {
    logoutUser: PropTypes.func,
    user: PropTypes.shape({
      id: PropTypes.number.isRequired,
      first_name: PropTypes.string.isRequired,
      last_name: PropTypes.string.isRequired,
      email: PropTypes.string.isRequired,
      stores: PropTypes.arrayOf(PropTypes.string.isRequired),
      roles: PropTypes.shape({
        Map: PropTypes.object,
      }).isRequired,
      banner_info: PropTypes.shape({
        header: PropTypes.string.isRequired,
        message: PropTypes.string.isRequired,
        enabled: PropTypes.bool.isRequired,
      }).isRequired,
      appenv: PropTypes.string.isRequired,
    }),
    hasUnsavedChanges: PropTypes.bool,
    onSaveAndNavigateHome: PropTypes.func,
    onNavigateHomeWithoutSaving: PropTypes.func
  };

  constructor (props){
    super(props);
    this.state = {
      displayHomeConfirmationModal: false
    };
  }

  handleLogoClick = (e) => {
    e.preventDefault();
    if (this.props.hasUnsavedChanges) {
      this.setState({ displayHomeConfirmationModal: true });
    } else {
      this.context.router.push('/');
    }
  };

  handleSaveAndNavigateHome = () => {
    this.setState({ displayHomeConfirmationModal: false });
    if (this.props.onSaveAndNavigateHome) {
      this.props.onSaveAndNavigateHome();
    }
  };

  handleNavigateHomeWithoutSaving = () => {
    this.setState({ displayHomeConfirmationModal: false });
    if (this.props.onNavigateHomeWithoutSaving) {
      this.props.onNavigateHomeWithoutSaving();
    } else {
      this.context.router.push('/');
    }
  };

  isClaimsActive = () => {
    return this.context.router.isActive("/automotive-claims-list") ||
      this.context.router.isActive("/automotive-claims") ||
      this.context.router.isActive("/gap-claims-list") ||
      this.context.router.isActive("/gap-claims") ||
      this.context.router.isActive("/gap-claims-recovery-list") ||
      this.context.router.isActive("/gap-claims-recovery") ||
      this.context.router.isActive("/vta-claims-list") ||
      this.context.router.isActive("/vta-claims");
  };

  isManageActive = () => {
    return this.context.router.isActive("/admin") ||
      this.context.router.isActive("/gap-batch-claims") ||
      this.context.router.isActive("/gap-batch-history") ||
      this.context.router.isActive("/automotive-batch-history") ||
      this.context.router.isActive("/automotive-batch-dashboard") ||
      this.context.router.isActive("/automotive-manage-zones") ||
      this.context.router.isActive("/cc-reconciliation") ||
      this.context.router.isActive("/cc-reconciliation-history") ||
      this.context.router.isActive("/pre-approved-limits");
  };

  renderLoggedInUserDetail = () => {
    if (this.props.user) {
      return (
        <li className='nav-item'>
          <a href="#" className='nav-link' id="link-nav">
            <i className='fa fa-user'/> {`${this.props.user.first_name} ${this.props.user.last_name}`}
          </a>
        </li>
      );
    }
  };

  renderGAPSelection = () => {
    if (userHasRole(this.props.user, USER_ROLES.gapClaims) ||
      userHasRole(this.props.user, USER_ROLES.gapClaimsManager) ||
      userHasRole(this.props.user, USER_ROLES.viewOnlyClaims)
    ){
      return (
        <div>
          <Link to='/gap-claims-list'
            id='link-gap-claims-list'
            className='dropdown-item'>
            GAP
          </Link>
          <Link to='/vta-claims-list'
            id='link-vta-claims-list'
            className='dropdown-item'>
            VTA
          </Link>
        </div>
      );
    } else if (userHasRole(this.props.user, USER_ROLES.accounting)) {
      return (
        <Link to='/gap-finance-list'
          id='link-gap-finance-list'
          className='dropdown-item'>
          GAP
        </Link>
      );
    }
  };

  renderRecoverySelection = () => {
    if (userHasRole(this.props.user, USER_ROLES.recoveryTeam)) {
      return (
        <Link to='/gap-claims-recovery-list'
          id='link-gap-claims-recovery-list'
          className='dropdown-item'>
          Recovery
        </Link>
      );
    }
  };

  renderAutomotiveSelection = () => {
    if (userHasRole(this.props.user, USER_ROLES.autoClaims)
      || userHasRole(this.props.user, USER_ROLES.autoClaimsManager)
      || userHasRole(this.props.user, USER_ROLES.accounting)
      || userHasRole(this.props.user, USER_ROLES.viewOnlyClaims)
    ) {
      return (
        <Link to='/automotive-claims-list'
          id='link-automotive-claims-list'
          className='dropdown-item'>
          Automotive
        </Link>
      );
    }
  };

  renderLWTSelection = () => {
    if (userHasRole(this.props.user, USER_ROLES.gapClaims)
      || userHasRole(this.props.user, USER_ROLES.gapClaimsManager)
      || userHasRole(this.props.user, USER_ROLES.accounting)
      || userHasRole(this.props.user, USER_ROLES.viewOnlyClaims)
    ) {
      return (
        <Link to='/lwt-claims-list'
          id='link-lwt-claims-list'
          className='dropdown-item'>
          LWT
        </Link>
      );
    }
  };

  renderGAPManageOptions = () => {
    if (userHasRole(this.props.user, USER_ROLES.gapClaimsManager)) {
      return (
        <div>
          <Link to='/gap-batch-claims'
            id="link-gap-claim-batch"
            className='dropdown-item'>
            GAP Claim Batch
          </Link>
          <Link to='/gap-batch-history'
            id="link-gap-batch-history"
            className='dropdown-item'>
            GAP Batch History
          </Link>
          <Link to='/pre-approved-limits/lwt-gap-claims'
            id='link-lwt-gap-pre-approved-limits'
            className='dropdown-item'>
            GAP/ LWT Authorization Limits
          </Link>
        </div>
      );
    }
  };

  renderAutomotiveManageOptions = () => {
    if (userHasRole(this.props.user, USER_ROLES.autoClaimsManager)) {
      return (
        <div>
          <Link to='/automotive-batch-dashboard'
            id='link-automotive-batch-dashboard'
            className='dropdown-item'>
            Automotive Claim Batch
          </Link>
          <Link to='/automotive-batch-history'
            id='link-automotive-batch-history'
            className='dropdown-item'>
            Automotive Batch History
          </Link>
          <Link to='/automotive-manage-zones'
            id='link-automotive-manage-zones'
            className='dropdown-item'>
            Automotive Facility Zones
          </Link>
          <Link to={ { pathname: '/cc-reconciliation', query: { sortBy: 'date_of_payment_received', sortOrder: 'asc' } } }
            id='link-cc-reconciliation'
            className='dropdown-item'>
            CC Reconciliation
          </Link>
          <Link to={ { pathname: '/cc-reconciliation-history', query: { sortBy: 'date_of_payment_received', sortOrder: 'asc' } } }
            id='link-cc-reconciliation-history'
            className='dropdown-item'>
                CC Reconciliation History
          </Link>
          <Link to='/pre-approved-limits/automotive-claims'
            id='link-pre-approved-limits'
            className='dropdown-item'>
            Automotive Pre Approved Limits
          </Link>
          <Link to='/automotive-search-facility'
            id='link-automotive-search-facility'
            className='dropdown-item'>
                Manage Facility
          </Link>
          <Link to='/credit-manage'
            id='link-automotive-credit-card'
            className='dropdown-item'>
            Manage Credit Card
          </Link>
        </div>
      );
    } else if (userHasRole(this.props.user, USER_ROLES.accounting)) {
      return (
        <div>
          <Link to='/automotive-batch-dashboard'
            id='link-automotive-batch-dashboard'
            className='dropdown-item'>
            Automotive Claim Batch
          </Link>
          <Link to='/automotive-batch-history'
            id='link-automotive-batch-history'
            className='dropdown-item'>
            Automotive Batch History
          </Link>
          <Link to={ { pathname: '/cc-reconciliation', query: { sortBy: 'date_of_payment_received', sortOrder: 'asc' } } }
            id='link-cc-reconciliation'
            className='dropdown-item'>
            CC Reconciliation
          </Link>
          <Link to={ { pathname: '/cc-reconciliation-history', query: { sortBy: 'date_of_payment_received', sortOrder: 'asc' } } }
            id='link-cc-reconciliation-history'
            className='dropdown-item'>
                CC Reconciliation History
          </Link>
          <Link to='/automotive-search-facility'
            id='link-automotive-search-facility'
            className='dropdown-item'>
            Manage Facility
          </Link>
        </div>
      );
    }else if (userHasRole(this.props.user, USER_ROLES.autoClaims)) {
      return (
        <div>
          <Link to={ { pathname: '/cc-reconciliation', query: { sortBy: 'date_of_payment_received', sortOrder: 'asc' } } }
            id='link-cc-reconciliation'
            className='dropdown-item'>
            CC Reconciliation
          </Link>
          <Link to={ { pathname: '/cc-reconciliation-history', query: { sortBy: 'date_of_payment_received', sortOrder: 'asc' } } }
            id='link-cc-reconciliation-history'
            className='dropdown-item'>
                CC Reconciliation History
          </Link>
          <Link to='/automotive-search-facility'
            id='link-automotive-search-facility'
            className='dropdown-item'>
                Manage Facility
          </Link>
        </div>
      );
    }
  };

  renderSeparator = () => {
    if (userHasRole(this.props.user, USER_ROLES.gapClaimsManager) && userHasRole(this.props.user, USER_ROLES.autoClaimsManager)) {
      return (
        <hr/>
      );
    }
  };

  renderGAPAutomotiveManageOptions = () => {
    if (userHasRole(this.props.user, USER_ROLES.gapClaimsManager) || userHasRole(this.props.user, USER_ROLES.autoClaimsManager)) {
      return (
        <div>
          <hr/>
          <Link to='/admin'
            id='link-admin'
            className='dropdown-item'>
            Email Templates
          </Link>
        </div>
      );
    }
  };

  renderAPReceive = () => {
    if ((this.props.user.appenv !== "production") &&
        (userHasRole(this.props.user, USER_ROLES.accounting) || userHasRole(this.props.user, USER_ROLES.productManager))) {
      return (
        <div>
          <hr/>
          <Link to='/apreceive'
            id='link-admin'
            className='dropdown-item'>
            Run Apreceive
          </Link>
        </div>
      );
    }
  };

  renderAuditReport = () => {
    if ((userHasRole(this.props.user, USER_ROLES.autoClaimsManager) || 
    userHasRole(this.props.user, USER_ROLES.productManager))) {
      return (
        <div>
          <Link to='/automotive-facility-report'
            id='link-automotive-facility-report'
            className='dropdown-item'>
                Facility Audit
          </Link>
        </div>
      );
    }
  };
  

  renderManageOptions = () => {
    if (userHasRole(this.props.user, USER_ROLES.gapClaimsManager) ||
      userHasRole(this.props.user, USER_ROLES.autoClaimsManager) ||
      userHasRole(this.props.user, USER_ROLES.autoClaims) ||
      userHasRole(this.props.user, USER_ROLES.accounting)) {
      return (
        <div className="nav-item dropdown">
          <button type="button" data-toggle="dropdown"
            id="btn-manage"
            className={ `btn btn-link nav-link dropdown-toggle${this.isManageActive() ? ' active' : ''}` }>
            Manage
          </button>
          <div className="dropdown-menu">
            {this.renderGAPManageOptions()}
            {this.renderSeparator()}
            {this.renderAutomotiveManageOptions()}
            {this.renderAuditReport()}
            {this.renderGAPAutomotiveManageOptions()}
            {this.renderAPReceive()}
          </div>
        </div>
      );
    }
  };

  render() {
    let bannerInfo = undefined;
    if(this.props.user) {
      bannerInfo = this.props.user.banner_info;
    }

    return <div className="fixed-top">
      <nav className="navbar navbar-expand-md navbar-light bg-light sticky-top d-print-none">
        <a href="#" className="navbar-brand" onClick={this.handleLogoClick}>
          <img src='/static/img/TCA-logo.svg' alt='logo' id='nav-logo'/>
        </a>
        <div className="navbar-nav mr-auto">
          <div className="nav-item dropdown">
            <button id="btn-claims" type="button" data-toggle="dropdown"
              className={ `btn btn-link nav-link dropdown-toggle${this.isClaimsActive() ? ' active' : ''}` }>
                Claims
            </button>
            <div className="dropdown-menu">
              {this.renderAutomotiveSelection()}
              {this.renderGAPSelection()}
              {this.renderRecoverySelection()}
              {this.renderLWTSelection()}
            </div>
          </div>
          {this.renderManageOptions()}
        </div>
        <div className="navbar-nav">
          <div className="nav-item align-self-end">
            {this.renderLoggedInUserDetail()}
          </div>
          <div className='nav-item'>
            <a href="#" className='nav-link' id="link-logout" onClick={ this.props.logoutUser }>
              <i className='fa fa-close'/> Logout
            </a>
          </div>
        </div>
      </nav>
      {(bannerInfo && bannerInfo.enabled) &&
        (
          <div className="alert alert-warning" role="alert">
            <h5 className="alert-heading">{bannerInfo.header}</h5>
            <p>
              <span dangerouslySetInnerHTML={{__html: bannerInfo.message}} />
            </p>
          </div>
        )}
      <ConfirmationModal
        confirmButtonText="Yes"
        declineButtonText="No"
        displayConfirmationModal={this.state.displayHomeConfirmationModal}
        displayMessage="You have unsaved work, do you want to save it before continuing?"
        onConfirm={this.handleSaveAndNavigateHome}
        onDecline={this.handleNavigateHomeWithoutSaving}
      />
    </div>;
  }
}
