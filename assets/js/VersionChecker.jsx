var ajax = require('./ajax.js').json;
var throttle = require('lodash/throttle');
var moment = require('moment');

var versionCheck;
var threshold = 60000;
var startVersion = moment().utc().format("YYYYMMDDHHmmss");

var start = function(callback) {
  versionCheck = throttle(function() {
    ajax("/app.json?" + moment().unix(), {}, {}, function(data, status, xhr) {
      if (status === 200) {
        if (startVersion && data.version > startVersion) {
          stop();
          callback(data.version);
        }
      }
    });
  }, threshold);
  window.addEventListener('mousemove', versionCheck);
  window.addEventListener('keypress', versionCheck);
};

var stop = function() {
  if (versionCheck) {
    window.removeEventListener('keypress', versionCheck);
    window.removeEventListener('mousemove', versionCheck);
    versionCheck.cancel();
    versionCheck = undefined;
  }
};

module.exports = {
  start: start,
  stop: stop,
};
