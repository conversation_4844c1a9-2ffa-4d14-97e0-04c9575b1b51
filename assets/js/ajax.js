var Alert = require('react-s-alert').default;

(function(global){

  var csrfToken = "";

  var rawAjax = function(url, options, callback) {
    var xmlhttp = new XMLHttpRequest();

    var aborter = function() { xmlhttp.abort(); };

    window.addEventListener('beforeunload', aborter);

    xmlhttp.onreadystatechange = function() {
      if (xmlhttp.readyState === 4) {
        var newCSRFToken = xmlhttp.getResponseHeader("X-CSRF-Token");
        if (newCSRFToken) {
          csrfToken = newCSRFToken;
        }
        if (!options.noAuth && xmlhttp.getResponseHeader("TCA-Authenticate") === "1") {
          module.exports.needAuth();
          return;
        }
        if (xmlhttp.getResponseHeader("X-CSRF-Invalid") === "1") {
          if (options.stopCSRFRetry) {
            Alert.error("CSRF problem: You can try to reload the page and/or clear cookies if problems persist.");
            callback(xmlhttp);
            return;
          }
          options.stopCSRFRetry = true;
          rawAjax(url, options, callback);
          return;
        }
        callback(xmlhttp);
        window.removeEventListener('beforeunload', aborter);
      }
    };

    var headers = options.headers || {};
    var method = options.method || "GET";
    if (options.responseType) {
      xmlhttp.responseType = options.responseType;
    }
    xmlhttp.open(method, url, true);
    Object.keys(headers).forEach(function(key){
      xmlhttp.setRequestHeader(key, headers[key]);
    }, this);
    if (method === "GET") { // GET methods don't send a body
      xmlhttp.send();
    } else {
      if (csrfToken !== "") {
        xmlhttp.setRequestHeader("X-CSRF-Token", csrfToken);
      }
      xmlhttp.send(options.data || '');
    }
    return xmlhttp;
  };

  var jsonAjax = function(url, data, options, callback) {
    options.data = JSON.stringify(data);
    if (!options.headers) {
      options.headers = {};
    }
    options.headers['Content-Type'] = 'application/json';
    return rawAjax(
      url,
      options,
      function(xhr) {
        var data = {};
        try {
          data = JSON.parse(xhr.responseText);
        } catch (exception) {
          /*eslint no-console: 0*/
          console.log(exception);
        }
        callback(data, xhr.status, xhr);
      }
    );
  };

  var jsonAjaxPromise = function(url, data, options) {
    options.data = JSON.stringify(data);
    if (!options.headers) {
      options.headers = {};
    }
    options.headers['Content-Type'] = 'application/json';

    return new Promise(function(resolve, reject){
      rawAjax(
        url,
        options,
        function(xhr) {
          var data = {};
          try {
            data = JSON.parse(xhr.responseText);
          } catch (exception) {
            reject(exception);
            return;
          }
          resolve({ data: data, status: xhr.status, xhr: xhr });
        }
      );
    });
  };

  module.exports = {
    raw: rawAjax,
    json: jsonAjax,
    jsonPromise: jsonAjaxPromise,
    needAuth: function() {
      if (window.location.pathname !== "/login") {
        window.location.replace("/login");
      }
    }
  };

})(this);