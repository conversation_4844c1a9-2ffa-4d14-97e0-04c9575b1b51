BEGIN;

DO $$
BEGIN
  INSERT INTO companies (created_at, updated_at, name, code) VALUES
  (NOW() AT TIME ZONE 'utc', NOW() AT TIME ZONE 'utc', 'Total Care Auto', 'TCA'),
  (NOW() AT TIME ZONE 'utc', NOW() AT TIME ZONE 'utc', 'Landcar', 'LCA');

  INSERT INTO users (id, created_at, updated_at, company_id, email, first_name, last_name, active, roles) VALUES
    (5103, NOW() AT TIME ZONE 'utc', NOW() AT TIME ZONE 'utc', 1, '<EMAIL>',
     'Gap', 'Claims', TRUE, '"gap_claims" => "1"'),
    (5086, NOW() AT TIME ZONE 'utc', NOW() AT TIME ZONE 'utc', 1, '<EMAIL>',
     'Gap', 'Claims Manager', TRUE, '"gap_claims_manager" => "1"');

  INSERT INTO customers (first_name, last_name, state) values
     ('<PERSON>', '<PERSON><PERSON><PERSON>', 'UT' ),
     ('<PERSON>','<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>' ),
     ('<PERSON>', '<PERSON>', 'UT' );

  INSERT INTO banks (bank_account_name, bank_address_street1, bank_address_street2, bank_address_city, bank_address_state, bank_address_zip) values
    ('BOA', 'street1', 'street2', 'city','UT','12345'),
    ('LB','street1', 'street2', 'city','UT','12345'),
    ('SBI', 'street1', 'street2', 'city','UT','12345');

  INSERT INTO gap_claims (
    vin,
    contract_number,
    status,
    status_change_description,
    date_of_loss,
    date_of_claim_received,
    date_of_last_in,
    date_of_last_out,
    waiting_for,
    last_message,
    customer_id,
    case_reserve,
    created_by_user_id,
    finance_manager,
    has_requested_book_out,
    book_out_requested_date,
    has_run_amortization_sheet,
    run_amortization_sheet_value,
    has_canceled_contracts,
    has_canceled_service_contract,
    canceled_service_contract_value,
    has_canceled_maintenance_contract,
    canceled_maintenance_contract_value,
    has_canceled_gap_refund_contract,
    gap_refund_contract_value,
    other_label1,
    other_value1,
    other_label2,
    other_value2,
    other_label3,
    other_value3,
    has_insurance_payment_check,
    insurance_payment_check_value,
    has_settlement_letter,
    settlement_letter_value,
    is_valuation_report_available,
    valuation_report_adjustments,
    is_valuation_report_matches_base_value,
    valuation_report_matches_base_value,
    valuation_report_vin_matches,
    has_valuation_report_prior_damage,
    valuation_report_prior_damage_value,
    has_valuation_report_misc_fee,
    valuation_report_misc_fee_value,
    valuation_report_mileage,
    valuation_report_type,
    has_options_match_book_out,
    options_match_book_out_mismatches,
    has_options_match_book_out_over_150_percent,
    options_match_book_out_over_150_percent_show_calculation,
    has_original_financing_contract,
    original_financing_contract_value,
    contract_number_matches,
    bank_history_matches,
    is_police_report_available,
    police_report_issues,
    police_report_is_insured_at_fault,
    has_insurance_policy_deductible,
    insurance_policy_deductible_value,
    has_bank_information,
    is_full_loan_history_available,
    payment_amount,
    interest_rate,
    first_payment_date,
    has_new_bank_information,
    bank_id,
    bank_account_number,
    last_action,
    owner_id) VALUES ('2XFT9877489XD',
    'GAPS978624',
    'P',
    'created',
    '2016-03-21',
    '2016-09-02',
    '2016-09-02',
    '2016-09-02',
    'Needs 3 or Statement of Loss.  Sent final request letter 3/23/16 **verify address** give 1 more week 5/3/16',
    'Final Request Letter',
     1,
    4004.04,
    5103,
    'Finance Manager',
    TRUE,
    '2016-03-21',
    TRUE,
    2400.2,
    TRUE,
    TRUE,
    2000.5,
    TRUE,
    1000.3,
    TRUE,
    208.2,
    'other_contract_1',
    101.20,
    'other_contract_2',
    202.5,
    'other_contract_3',
    303.2,
    TRUE,
    590.9,
    TRUE,
    489,
    TRUE,
    790,
    TRUE,
    890,
    TRUE,
    TRUE,
    897,
    TRUE,
    345,
    20,
    'Valuation Report Type Text',
    TRUE,
    'Mismatch Text',
    TRUE,
    '150 percent calculation',
    TRUE,
    1234,
    TRUE,
    TRUE,
    TRUE,
    'Police Report Issues',
    TRUE,
    TRUE,
    450,
    TRUE,
    TRUE,
    2309,
    6.85,
    '2016-03-09',
    TRUE,
    1,
    '986577880978',
    'Last Action',
    5103),
    ('3XGT982579XD',
      'GAPS978625',
      'P',
      'created',
      '2016-03-21',
      '2016-09-21',
      '2016-09-30',
      '2016-09-30',
      'Needs 3 or Statement of Loss.  Sent final request letter 3/23/16 **verify address** give 1 more week 5/3/16',
      'Final Request Letter',
  3,
      4004.04,
      5103,
      'Finance Manager',
      TRUE,
      '2016-03-21',
      TRUE,
      2400.2,
      TRUE,
      TRUE,
      2000.5,
      TRUE,
      1000.3,
      TRUE,
      208.2,
      'other_contract_1',
      101.20,
      'other_contract_2',
       202.5,
      'other_contract_3',
      303.2,
      TRUE,
      590.9,
      TRUE,
      489,
      TRUE,
      790,
      TRUE,
      890,
      TRUE,
      TRUE,
      897,
      TRUE,
      345,
      20,
      'Valuation Report Type Text',
      TRUE,
      'Mismatch Text',
      TRUE,
      '150 percent calculation',
      TRUE,
      1234,
      TRUE,
      TRUE,
      TRUE,
      'Police Report Issues',
      TRUE,
      TRUE,
      450,
      TRUE,
      TRUE,
      2309,
      6.85,
      '2016-03-09',
      TRUE,
     2,
     '986577880978',
     'Last Action',
     5103),
    ('2XT9872389XD',
      'GAPS978626',
      'P',
      'created',
      '2016-03-21',
      '2016-09-20',
      '2016-10-05',
      '2016-10-05',
      'Needs 3 or Statement of Loss.  Sent final request letter 3/23/16 **verify address** give 1 more week 5/3/16',
      'Final Request Letter',
  2,
      4004.04,
      5103,
      'Finance Manager',
      TRUE,
      '2016-03-21',
      TRUE,
      2400.2,
      TRUE,
      TRUE,
      2000.5,
      TRUE,
      1000.3,
      TRUE,
      208.2,
      'other_contract_1',
      101.20,
      'other_contract_2',
      202.5,
      'other_contract_3',
      303.2,
      TRUE,
      590.9,
      TRUE,
      489,
      TRUE,
      790,
      TRUE,
      890,
      TRUE,
      TRUE,
      897,
      TRUE,
      345,
      20,
      'Valuation Report Type Text',
      TRUE,
      'Mismatch Text',
      TRUE,
      '150 percent calculation',
      TRUE,
      1234,
      TRUE,
      TRUE,
      TRUE,
      'Police Report Issues',
      TRUE,
      TRUE,
      450,
      TRUE,
      TRUE,
      2309,
      6.85,
      '2016-03-09',
      TRUE,
      3,
     '986577880978',
     'Last Action',
     5103);

  INSERT INTO gap_claim_updates(gap_claim_id, updated_by_user_id, updated_at)
  VALUES
    (1, 5103, NOW() AT TIME ZONE 'utc'),
    (1, 5086, NOW() AT TIME ZONE 'utc'),
    (2, 5086, NOW() AT TIME ZONE 'utc'),
    (3, 5086, NOW() AT TIME ZONE 'utc');

  INSERT INTO record_notes (gap_claim_id, notes_text, created_by_user_id, created_at)
  VALUES
    (1, 'Some sample record notes go here', 5103, NOW() AT TIME ZONE 'utc'),
    (1, 'Some sample record notes go here', 5103, NOW() AT TIME ZONE 'utc'),
    (1, 'Some sample record notes go here', 5103, NOW() AT TIME ZONE 'utc'),
    (1, 'Some sample record notes go here', 5103, NOW() AT TIME ZONE 'utc'),
    (1, 'Some sample record notes go here', 5103, NOW() AT TIME ZONE 'utc'),
    (2, 'Some sample record notes go here', 5103, NOW() AT TIME ZONE 'utc'),
    (2, 'Some sample record notes go here', 5103, NOW() AT TIME ZONE 'utc'),
    (2, 'Some sample record notes go here', 5103, NOW() AT TIME ZONE 'utc'),
    (2, 'Some sample record notes go here', 5103, NOW() AT TIME ZONE 'utc'),
    (2, 'Some sample record notes go here', 5103, NOW() AT TIME ZONE 'utc'),
    (3, 'Some sample record notes go here', 5103, NOW() AT TIME ZONE 'utc'),
    (3, 'Some sample record notes go here', 5103, NOW() AT TIME ZONE 'utc'),
    (3, 'Some sample record notes go here', 5103, NOW() AT TIME ZONE 'utc'),
    (3, 'Some sample record notes go here', 5103, NOW() AT TIME ZONE 'utc'),
    (3, 'Some sample record notes go here', 5103, NOW() AT TIME ZONE 'utc');


  INSERT INTO email_templates(name, template_text)
  VALUES
    ('CSG GAP Claim', 'claim text will be placed here in html format'),
    ('GAP Ack', 'GAP Ack text will be placed here in html format'),
    ('GAP Cancellation', 'GAP Cancellation text will be placed here in html format');

  INSERT INTO email_template_updates(email_template_id, updated_by_user_id, updated_at)
  VALUES
    (1, 5103, NOW() AT TIME ZONE 'utc'),
    (2, 5103, NOW() AT TIME ZONE 'utc'),
    (3, 5103, NOW() AT TIME ZONE 'utc');

END$$;

COMMIT;