package sbmigrate

import (
	"bytes"
	"encoding/json"
	"fmt"
	"log"
	"strings"
	"time"

	"phizz/db"

	"github.com/jmoiron/sqlx"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
)

// Defines constants for TCA GAP products
const (
	GapClaimsTable         = "gap_claims"
	GapClaimContractColumn = "contract_number"
	GapClaimContractsTable = "gap_claim_contracts"
)

// TcaGap defines the structure of a TCA GAP contract that has been exported from SB
type TcaGap struct {
	Contract          string       `json:"Contract,omitempty"`   // contracts.code
	EffectiveDate     string       `json:"Eff date,omitempty"`   // contracts.effective_date
	Store             string       `json:"Store,omitempty"`      // stores.code
	Name              string       `json:"Cust Name,omitempty"`  // customers.last_name, customers.first_name
	Address           string       `json:"Address,omitempty"`    // customers.address
	City              string       `json:"City,omitempty"`       // customers.city
	State             string       `json:"St,omitempty"`         // customers.state_code
	Zip               string       `json:"Cust Zip,omitempty"`   // customers.postal_code
	HomePhone         string       `json:"Home phone,omitempty"` // customers.phone
	BusinessPhone     string       `json:"Work phone,omitempty"` // customers.phone if HomePhone not provided
	Email             string       `json:"Email,omitempty"`      // customers.email
	CobuyerName       string       `json:"Cobuyer Name,omitempty"`
	CobuyerAddress    string       `json:"Cobuyer Addr,omitempty"`
	CobuyerZip        string       `json:"Cobuyer Zip,omitempty"`
	CobuyerEmail      string       `json:"Cobuyer Email,omitempty"`
	CobuyerPhone      string       `json:"Cobuyer Phone,omitempty"`
	CobuyerAltPhone   string       `json:"Cobuyer Altphone,omitempty"`
	CobuyerContact    string       `json:"Cobuyer Contact,omitempty"`
	Term              string       `json:"Term,omitempty"`                // contracts.finance_term / 12
	Price             string       `json:"Price,omitempty"`               // contracts.price
	Cost              string       `json:"Cost,omitempty"`                // contracts.plan_cost
	Year              string       `json:"Year,omitempty"`                // vin_records.year
	Make              string       `json:"Make,omitempty"`                // vin_records.make
	Model             string       `json:"Model,omitempty"`               // vin_records.model
	Vin               string       `json:"Vin,omitempty"`                 // vin_records.vin
	BeginningMiles    string       `json:"Mileage,omitempty"`             // sales.odometer, contracts.effective_milage
	PurchaseLeaseFlag string       `json:"Purchase/Lease flag,omitempty"` // ??? sales.payment_type
	Lender            string       `json:"Lender,omitempty"`              // ??? lenders.name (This is not a unique column)
	BankAddress       string       `json:"Bank Addr,omitempty"`           // ???
	BankZip           string       `json:"Bank Zip,omitempty"`            // ???
	FinancedAmount    string       `json:"Financed Amount,omitempty"`     // sales.finance_amount
	DeductibleWaiver  string       `json:"Deductible Waiver,omitempty"`   // ???
	MonthlyPayment    string       `json:"Monthly Payment,omitempty"`     // ??? sales.financed_monthly_payment
	Status            string       `json:"Status,omitempty"`              // contracts.status (A=Active, C=Canceled, X=Expired, P=Pending, N=New) - verify possible statuses
	BookDate          string       `json:"Book date,omitempty"`           // New column needed
	ExpirationDate    string       `json:"Expiration date,omitempty"`     // contracts.expiration_date
	Events            EventWrapper `json:"EVENTS,omitempty"`              // ???
	Cancellation      struct {
		RefundDate      string `json:"Refund date,omitempty"`
		RefundAmount    string `json:"Refund Amount,omitempty"`
		RefundCheck     string `json:"Refund Check,omitempty"`
		CancelDate      string `json:"Cancel Date,omitempty"`
		StoreRefund     string `json:"Store Refund,omitempty"`
		ABCancelDt      string `json:"AB Cancel Dt,omitempty"`
		CancelNbr       string `json:"Cancel Nbr,omitempty"`
		CancelReason    string `json:"Cancel Reason,omitempty"`
		CancelCode      string `json:"Cancel Code,omitempty"`
		CancelCheckDate string `json:"Can Check Date,omitempty"`
		CancelFee       string `json:"Cancel Fee,omitempty"`
		CancelFactor    string `json:"Cancel Factor,omitempty"`
	} `json:"Cancellation,omitempty"` // skip for now
	InvoiceDate string `json:"Invoice Dt,omitempty"` // ???
	InvoiceNbr  string `json:"Inv Nbr,omitempty"`    // ???
	Provider    string `json:"Provider,omitempty"`   // ???
	Premium     string `json:"Premium,omitempty"`    // ???
	Agent       struct {
		AgentNbr  string `json:"Agent Nbr,omitempty"`
		AgentName string `json:"Agent Name,omitempty"`
	} `json:"Agent,omitempty"` // ???
	Comments  []string `json:"Comments,omitempty"` // ???
	CSCIFlags struct {
		NewTransferred    string `json:"New Transferred,omitempty"`
		CancelTransferred string `json:"Cancel Transferred,omitempty"`
	} `json:"CSCI Flags,omitempty"` // ???
	Special          string              `json:"Special"`                  // ???
	Weowe            string              `json:"Weowe,omitempty"`          // ???
	NetEquity        string              `json:"Net Equity,omitempty"`     // ???
	Commercial       string              `json:"Commercial,omitempty"`     // ??? contract_adjustments?
	MSRP             string              `json:"M.s.r.p,omitempty"`        // sales.msrp
	BankCode         string              `json:"Bank Code,omitempty"`      // ???
	FIDeal           string              `json:"Deal#,omitempty"`          // sales.dms_number
	UnearnedPremium  string              `json:"Unearned Premium"`         // ???
	Claim            TcaGapClaim         `json:"Claim,omitempty"`          // skip for now
	FiMgrNbr         string              `json:"Fi Mgr Nbr,omitempty"`     // ??? users.employee_number via sales.salesperson_id
	FiMgr            string              `json:"Fi Mgr,omitempty"`         // ??? users.last_name, users.first_name via sales.salesperson_id
	LCAPurchased     lcaPurchasedWrapper `json:"LCA Purchased,omitempty"`  // ???
	License          string              `json:"License,omitempty"`        // ???
	AdminOnly        string              `json:"Admin Only,omitempty"`     // ???
	LeaseFlag        string              `json:"Lease Flag,omitempty"`     // ??? sales.payment_type
	CrcFlag          string              `json:"Crc Flag,omitempty"`       // ??? sales.is_crc
	ProgSource       string              `json:"Prog Source,omitempty"`    // skip
	ContactMethod    string              `json:"CONTACT METHOD,omitempty"` // ???
	Broker           string              `json:"Broker,omitempty"`         // skip
	FormNbr          string              `json:"Form Nbr,omitempty"`       // ???
	CudlAmt          string              `json:"Cudl Amt,omitempty"`       // ???
	NUD              string              `json:"N U D,omitempty"`          // ??? sales.is_new
	CostClass        string              `json:"Cost Class,omitempty"`
	ValidationErrors []string            `json:"Validation Errors,omitempty"`
	OriginalCosts    struct {
		Cost      string `json:"Cost,omitempty"`
		Core      string `json:"Core,omitempty"`
		Admin     string `json:"Admin,omitempty"`
		Marketing string `json:"Marketing,omitempty"`
		Clip      string `json:"Clip,omitempty"`
		RSA       string `json:"RSA,omitempty"`
		Risk      string `json:"Risk,omitempty"`
		Other     string `json:"Other,omitempty"`
		Premium   string `json:"Premium,omitempty"`
		Escrow    string `json:"Escrow,omitempty"`
	}
}

// TcaGapClaim represents a claim on a GAP contract
type TcaGapClaim struct {
	ID                                        int                    `db:"id,omitempty" json:",string,omitempty"`
	Vin                                       string                 `db:"vin,omitempty" json:",omitempty"`
	ContractNumber                            string                 `db:"contract_number,omitempty" json:",omitempty"`
	Status                                    string                 `db:"status,omitempty" json:",omitempty"`
	StatusChangeDescription                   string                 `db:"status_change_description,omitempty" json:",omitempty"`
	DateOfLoss                                time.Time              `db:"date_of_loss,omitempty" json:",omitempty"`
	DateOfClaimRecieved                       time.Time              `db:"date_of_claim_received,omitempty" json:",omitempty"`
	DateOfLastIn                              time.Time              `db:"date_of_last_in,omitempty" json:",omitempty"`
	DateOfLastOut                             time.Time              `db:"date_of_last_out,omitempty" json:",omitempty"`
	WaitingFor                                string                 `db:"waiting_for,omitempty" json:",omitempty"`
	CustomerID                                int                    `db:"customer_id,omitempty" json:",string,omitempty"`
	CaseReserve                               decimal.Decimal        `db:"case_reserve,omitempty" json:",omitempty"`
	CreatedByUserID                           int                    `db:"created_by_user_id,omitempty" json:",string,omitempty"`
	HasRunAmortizationSheet                   bool                   `db:"has_run_amortization_sheet,omitempty" json:",string,omitempty"`
	RunAmortizationSheetValue                 NullableDecimal        `db:"run_amortization_sheet_value,omitempty" json:",omitempty"`
	HasCanceledContracts                      bool                   `db:"has_canceled_contracts,omitempty" json:",string,omitempty"`
	HasCanceldServiceContract                 bool                   `db:"has_canceled_service_contract,omitempty" json:",string,omitempty"`
	CanceleServiceContractValue               NullableDecimal        `db:"canceled_service_contract_value,omitempty" json:",omitempty"`
	HasCanceledMaintenanceContract            bool                   `db:"has_canceled_maintenance_contract,omitempty" json:",string,omitempty"`
	CanceldMaintenanceContractValue           NullableDecimal        `db:"canceled_maintenance_contract_value,omitempty" json:",omitempty"`
	HasCanceledGapRefundContract              bool                   `db:"has_canceled_gap_refund_contract,omitempty" json:",string,omitempty"`
	GapRefundContractValue                    NullableDecimal        `db:"gap_refund_contract_value,omitempty" json:",omitempty"`
	OtherLabel1                               NullString             `db:"other_label1,omitempty" json:",omitempty"`
	OtherValue                                NullableDecimal        `db:"other_value1,omitempty" json:",omitempty"`
	OtherLabel2                               NullString             `db:"other_label2,omitempty" json:",omitempty"`
	OtherValue2                               NullableDecimal        `db:"other_value2,omitempty" json:",omitempty"`
	OtherLabel3                               NullString             `db:"other_label3,omitempty" json:",omitempty"`
	OtherValue3                               NullableDecimal        `db:"other_value3,omitempty" json:",omitempty"`
	IsValuationReportAvailable                bool                   `db:"is_valuation_report_available,omitempty" json:",string,omitempty"`
	ValuationReportAdjustments                NullableDecimal        `db:"valuation_report_adjustments,omitempty" json:",omitempty"`
	IsValuationReportMatchesBaseValue         bool                   `db:"is_valuation_report_matches_base_value,omitempty" json:",string,omitempty"`
	ValuationReportMtchesBaseValue            decimal.Decimal        `db:"valuation_report_matches_base_value,omitempty" json:",omitempty"`
	ValuationReportVinMatches                 bool                   `db:"valuation_report_vin_matches,omitempty" json:",string,omitempty"`
	HasValuationReportPriorDamage             bool                   `db:"has_valuation_report_prior_damage,omitempty" json:",string,omitempty"`
	ValuationReportPriorDamageValue           NullableDecimal        `db:"valuation_report_prior_damage_value,omitempty" json:",omitempty"`
	HasValuationReportMiscFee                 bool                   `db:"has_valuation_report_misc_fee,omitempty" json:",string,omitempty"`
	ValuationReportMiscFeeValue               NullableDecimal        `db:"valuation_report_misc_fee_value,omitempty" json:",omitempty"`
	ValuationReportMileage                    int                    `db:"valuation_report_mileage,omitempty" json:",string,omitempty"`
	ValuationReportType                       NullString             `db:"valuation_report_type,omitempty" json:",omitempty"`
	HasOptionsMatchBookOutOver150Percent      bool                   `db:"has_options_match_book_out_over_150_percent,omitempty" json:",string,omitempty"`
	Over150Percent                            NullableDecimal        `db:"over_150_percent,omitempty" json:",omitempty"`
	HasOriginalFinancingContract              bool                   `db:"has_original_financing_contract,omitempty" json:",string,omitempty"`
	OriginalFinancingContractValue            decimal.Decimal        `db:"original_financing_contract_value,omitempty" json:",omitempty"`
	ContractNumberMatches                     bool                   `db:"contract_number_matches,omitempty" json:",string,omitempty"`
	BankHistoryMatches                        bool                   `db:"bank_history_matches,omitempty" json:",string,omitempty"`
	IsPoliceReportAvailable                   bool                   `db:"is_police_report_available,omitempty" json:",string,omitempty"`
	HasInsurancePolicyDeductible              bool                   `db:"has_insurance_policy_deductible,omitempty" json:",string,omitempty"`
	HasBankInformation                        bool                   `db:"has_bank_information,omitempty" json:",string,omitempty"`
	IsFullLoanHistoryAvailable                bool                   `db:"is_full_loan_history_available,omitempty" json:",string,omitempty"`
	PaymentAmount                             NullableDecimal        `db:"payment_amount,omitempty" json:",omitempty"`
	InterestRate                              NullableDecimal        `db:"interest_rate,omitempty" json:",omitempty"`
	FirstPaymentDate                          NullableTime           `db:"first_payment_date,omitempty" json:",omitempty"`
	HasNewBankInformation                     bool                   `db:"has_new_bank_information,omitempty" json:",string,omitempty"`
	BankID                                    int                    `db:"bank_id,omitempty" json:",string,omitempty"`
	BankAccountNumber                         string                 `db:"bank_account_number,omitempty" json:",omitempty"`
	LastAction                                string                 `db:"last_action,omitempty" json:",omitempty"`
	OwnerID                                   int                    `db:"owner_id,omitempty" json:",string,omitempty"`
	HasChildClaim                             bool                   `db:"has_child_claim,omitempty" json:",string,omitempty"`
	IsChild                                   bool                   `db:"is_child,omitempty" json:",string,omitempty"`
	ChildClaimReason                          NullString             `db:"child_claim_reason,omitempty" json:",omitempty"`
	InsurancePolicyDeductibleValueAddition    decimal.Decimal        `db:"insurance_policy_deductible_value_addition,omitempty" json:",omitempty"`
	InsurancePolicyDeductibleValueSubtraction decimal.Decimal        `db:"insurance_policy_deductible_value_subtraction,omitempty" json:",omitempty"`
	InsurancePolicyDeductible                 string                 `db:"insurance_policy_deductible_reason,omitempty" json:",omitempty"`
	HasSettlementAmount                       bool                   `db:"has_settlement_amount,omitempty" json:",string,omitempty"`
	SettlementAmount                          decimal.Decimal        `db:"settlement_amount,omitempty" json:",omitempty"`
	HasInsuranceCheckAmount                   bool                   `db:"has_insurance_check_amount,omitempty" json:",string,omitempty"`
	InsuranceCheckAmount                      decimal.Decimal        `db:"insurance_check_amount,omitempty" json:",omitempty"`
	RunAmortizationManagerFlag                bool                   `db:"run_amortization_manager_flag,omitempty" json:",string,omitempty"`
	OtherLable1ManagerFlag                    bool                   `db:"other_label1_manager_flag,omitempty" json:",string,omitempty"`
	OtherLabel2ManagerFlag                    bool                   `db:"other_label2_manager_flag,omitempty" json:",string,omitempty"`
	OtherLabel3ManagerFlag                    bool                   `db:"other_label3_manager_flag,omitempty" json:",string,omitempty"`
	InsurancePaymentCheckManagerFlag          bool                   `db:"insurance_payment_check_manager_flag,omitempty" json:",string,omitempty"`
	SettlementLetterManagerFlag               bool                   `db:"settlement_letter_manager_flag,omitempty" json:",string,omitempty"`
	ValuationReportManagerFlag                bool                   `db:"valuation_report_manager_flag,omitempty" json:",string,omitempty"`
	ValuationReportBaseValueManager           bool                   `db:"valuation_report_base_value_manager_flag,omitempty" json:",string,omitempty"`
	ValuationReportVinMatchesManagerFlag      bool                   `db:"valuation_report_vin_matches_manager_flag,omitempty" json:",string,omitempty"`
	ValuationReportPriorDamagerManagerFlag    bool                   `db:"valuation_report_prior_damage_manager_flag,omitempty" json:",string,omitempty"`
	ValuationReportMiscFeeManagerFlag         bool                   `db:"valuation_report_misc_fee_manager_flag,omitempty" json:",string,omitempty"`
	ValuationReportMileageManagerFlag         bool                   `db:"valuation_report_mileage_manager_flag,omitempty" json:",string,omitempty"`
	ValuationReportDolManagerFlag             bool                   `db:"valuation_report_dol_manager_flag,omitempty" json:",string,omitempty"`
	ValuationReportTypeManagerFlag            bool                   `db:"valuation_report_type_manager_flag,omitempty" json:",string,omitempty"`
	OptionsBookOutOver150PercentManagerFlag   bool                   `db:"options_book_out_over_150_percent_manager_flag,omitempty" json:",string,omitempty"`
	OriginalFinancingManager                  bool                   `db:"original_financing_manager_flag,omitempty" json:",string,omitempty"`
	ContractNumberMatchesManagerFlag          bool                   `db:"contract_number_matches_manager_flag,omitempty" json:",string,omitempty"`
	BankHistoryMatchesManagerFlag             bool                   `db:"bank_history_matches_manager_flag,omitempty" json:",string,omitempty"`
	PoliceReportManagerFlag                   bool                   `db:"police_report_manager_flag,omitempty" json:",string,omitempty"`
	InsuranceDeductibleAdditionManagerFlag    bool                   `db:"insurance_deductible_addition_manager_flag,omitempty" json:",string,omitempty"`
	InsuranceDeductibleSubtractionManagerFlag bool                   `db:"insurance_deductible_subtraction_manager_flag,omitempty" json:",string,omitempty"`
	BankInformationManagerFlag                bool                   `db:"bank_information_manager_flag,omitempty" json:",string,omitempty"`
	FullLoanHistoryManagerFlag                bool                   `db:"full_loan_history_manager_flag,omitempty" json:",string,omitempty"`
	LoanNumberManagerFlag                     bool                   `db:"loan_number_manager_flag,omitempty" json:",string,omitempty"`
	PaymentAmountManagerFlag                  bool                   `db:"payment_amount_manager_flag,omitempty" json:",string,omitempty"`
	InterestRateManager                       bool                   `db:"interest_rate_manager_flag,omitempty" json:",string,omitempty"`
	FirstPaymentManagerFlag                   bool                   `db:"first_payment_manager_flag,omitempty" json:",string,omitempty"`
	ContractDealDate                          NullableTime           `db:"contract_deal_date,omitempty" json:",omitempty"`
	ContractTermMonths                        NullInt                `db:"contract_term_months,omitempty" json:",omitempty"`
	ContractDealDateManagerFlag               NullBool               `db:"contract_deal_date_manager_flag,omitempty" json:",omitempty"`
	ContractTermMOnthsManagerFlag             NullBool               `db:"contract_term_months_manager_flag,omitempty" json:",omitempty"`
	CalculateAmortizationValue                bool                   `db:"calculate_amortization_value,omitempty" json:",string,omitempty"`
	HasMsrpValue                              bool                   `db:"has_msrp_value,omitempty" json:",string,omitempty"`
	MsrpValue                                 decimal.Decimal        `db:"msrp_value,omitempty" json:",omitempty"`
	MsrpValueManagerFlag                      bool                   `db:"msrp_value_manager_flag,omitempty" json:",string,omitempty"`
	CsCheckAmount                             decimal.Decimal        `db:"cs_check_amount,omitempty" json:",omitempty"`
	ParentClaimID                             NullInt                `db:"parent_claim_id,omitempty" json:",omitempty"`
	HasRecovery                               bool                   `db:"has_recovery,omitempty" json:",string,omitempty"`
	InsuranceCompany                          string                 `db:"insurance_company,omitempty" json:",omitempty"`
	HasInsuranceCompany                       bool                   `db:"has_insurance_company,omitempty" json:",string,omitempty"`
	PolicyNumber                              string                 `db:"policy_number,omitempty" json:",omitempty"`
	HasPolicyNumber                           bool                   `db:"has_policy_number,omitempty" json:",string,omitempty"`
	MileageDeduction                          decimal.Decimal        `db:"mileage_deduction,omitempty" json:",omitempty"`
	HasMileageDeduction                       bool                   `db:"has_mileage_deduction,omitempty" json:",string,omitempty"`
	NADA                                      decimal.Decimal        `db:"nada,omitempty" json:",omitempty"`
	HasNada                                   bool                   `db:"has_nada,omitempty" json:",string,omitempty"`
	HasValuationNadaDifference                bool                   `db:"has_valuation_nada_difference,omitempty" json:",string,omitempty"`
	MileageDeductionManagerFlag               bool                   `db:"mileage_deduction_manager_flag,omitempty" json:",string,omitempty"`
	HasEstimateWithPhotos                     bool                   `db:"has_estimate_with_photos,omitempty" json:",string,omitempty"`
	GapClosedAt                               NullableTime           `db:"gap_closed_at,omitempty" json:",omitempty"`
	NadaManagerFlag                           bool                   `db:"nada_manager_flag,omitempty" json:",string,omitempty"`
	IsCsClaim                                 bool                   `db:"is_cs_claim,omitempty" json:",string,omitempty"`
	NotPaidByCs                               bool                   `db:"not_paid_by_cs,omitempty" json:",string,omitempty"`
	VinRecordID                               NullInt                `db:"vin_record_id,omitempty" json:",omitempty"`
	HasNegativeEquityAmount                   bool                   `db:"has_negative_equity_amount,omitempty" json:",string,omitempty"`
	HasNegativeEquityAmountManagerFlag        bool                   `db:"has_negative_equity_amount_manager_flag,omitempty" json:",string,omitempty"`
	NegativeEquityAmount                      decimal.Decimal        `db:"negative_equity_amount,omitempty" json:",omitempty"`
	Payments                                  []*TcaGapClaimPay      `db:"-" json:",omitempty"`
	Contracts                                 []*TcaGapClaimContract `db:"-" json:"Contracts,omitempty"`
	IsInProgress                              bool                   `db:"is_in_progress,omitempty"`
	Source                                    string                 `db:"source,omitempty" json:",omitempty"`
}

// TcaGapClaimPay represents a payment on a TCA Gap Contract Claim
type TcaGapClaimPay struct {
	ID                  int             `db:"id,omitempty" json:",string,omitempty"`
	AuthorizationNumber int             `db:"authorization_number,omitempty" json:",string,omitempty"`
	GapClaimID          int             `db:"gap_claim_id,omitempty" json:",string,omitempty"`
	CheckNumber         int             `db:"check_number,omitempty" json:",string,omitempty"`
	Amount              NullableDecimal `db:"amount,omitempty" json:",omitempty"`
	PaidDate            NullableTime    `db:"paid_date,omitempty" json:",omitempty"`
	BatchKey            NullInt         `db:"batch_key,omitempty" json:",omitempty"`
	BillKey             int             `db:"bill_key,omitempty" json:",string,omitempty"`
	BillMemo            string          `db:"bill_memo,omitempty" json:",omitempty"`
	UpdatedAt           NullableTime    `db:"updated_at,omitempty" json:",omitempty"`
}

// TcaGapClaimContract represents a related contract with a matchin Vin
type TcaGapClaimContract struct {
	ID             int             `db:"id,omitempty" json:",string,omitempty"`
	GapClaimID     int             `db:"gap_claim_id,opmitempty" json:",string,omitempty"`
	ContractNumber string          `db:"contract_number,omitempty" json:",omitempty"`
	ContractCode   string          `db:"contract_code,omitempty" json:",omitempty"`
	ContractName   string          `db:"contract_name,omitempty" json:",omitempty"`
	ContractFlag   bool            `db:"contract_flag,omitempty" json:",string,omitempty"`
	ContractValue  decimal.Decimal `db:"contract_value,omitempty" json:",omitempty"`
	ManagerFlag    bool            `db:"manager_flag,omitempty" json:",string,omitempty"`
}

type lcaPurchased struct {
	LCAProdDesc string `json:"Lcaprod Desc,omitempty"`
	LCAProdAmt  string `json:"Lcaprod Amt,omitempty"`
}

type lcaPurchasedWrapper []struct {
	lcaPurchased
}

func (w *lcaPurchasedWrapper) UnmarshalJSON(data []byte) error {
	// Define a type with the same structure but without the custom unmarshaller
	type dataArray lcaPurchasedWrapper

	// Unmarshall as interface{} so we can check if we have a single item or an array of items
	var rawField interface{}
	err := json.Unmarshal(data, &rawField)
	if err != nil {
		return errors.Wrapf(err, "Failed to Unmarshal Management Fees JSON: %s", string(data))
	}
	dec := json.NewDecoder(bytes.NewReader(data))
	dec.DisallowUnknownFields()
	switch v := rawField.(type) {
	case map[string]interface{}:
		elements := make(dataArray, 1)
		*w = lcaPurchasedWrapper(elements)
		return dec.Decode(&elements[0])
	case []interface{}:
		elements := make(dataArray, len(v))
		*w = lcaPurchasedWrapper(elements)
		return dec.Decode(&elements)
	}
	return nil
}

// Extract will Extract the next Contract from the decoder
func (c *TcaGap) Extract(dec *json.Decoder) error {
	*c = TcaGap{} // Make sure old values are discarded
	return dec.Decode(c)
}

// ID gets the contract number
func (c *TcaGap) ID() string {
	return c.Contract
}

// ClaimsTable returns the name of the table that contains the claims and the name of the Contract number column
func (c *TcaGap) ClaimsTable() (string, string) {
	return GapClaimsTable, GapClaimContractColumn
}

// ProductCode returns the code of the product that is being migrated.
func (c *TcaGap) ProductCode() string {
	return db.ProductCodeGap
}

// func (c *tcaGapClaim) isEmpty() bool {
// 	empty := true

// 	if c.AuthAmt != "" {
// 		empty = false
// 	} else {
// 		value, err := decimalFromString(c.AuthAmt)
// 		if err == nil && value.GreaterThan(decimal.Zero) {
// 			empty = false
// 		}
// 	}

// 	if c.ChkNbr != "" {
// 		empty = false
// 	}

// 	if c.ClRefund != "" {
// 		empty = false
// 	}

// 	if len(c.ClaimPay) > 0 {
// 		empty = false
// 	} else {
// 		for _, p := range c.ClaimPay {
// 			if p.AuthAmt != "" {
// 				empty = false
// 				break
// 			} else {
// 				value, err := decimalFromString(p.AuthAmt)
// 				if err == nil && value.GreaterThan(decimal.Zero) {
// 					empty = false
// 					break
// 				}
// 			}

// 			if p.Authorization != "" {
// 				empty = false
// 				break
// 			}

// 			if p.ChkNbr != "" {
// 				empty = false
// 				break
// 			}

// 			if p.PaidAmt != "" {
// 				empty = false
// 				break
// 			} else {
// 				value, err := decimalFromString(p.PaidAmt)
// 				if err == nil && value.GreaterThan(decimal.Zero) {
// 					empty = false
// 					break
// 				}
// 			}

// 			if p.PaidDate != "" {
// 				empty = false
// 				break
// 			}
// 		}
// 	}

// 	if c.ClaimReceived != "" {
// 		empty = false
// 	}

// 	if c.ClaimStatus != "" {
// 		empty = false
// 	}

// 	if c.CurrentBalance != "" {
// 		empty = false
// 	} else {
// 		value, err := decimalFromString(c.AuthAmt)
// 		if err == nil && value.GreaterThan(decimal.Zero) {
// 			empty = false
// 		}
// 	}

// 	if c.InsDeduct != "" {
// 		empty = false
// 	} else {
// 		value, err := decimalFromString(c.AuthAmt)
// 		if err == nil && value.GreaterThan(decimal.Zero) {
// 			empty = false
// 		}
// 	}

// 	if c.InsPayoff != "" {
// 		empty = false
// 	} else {
// 		value, err := decimalFromString(c.AuthAmt)
// 		if err == nil && value.GreaterThan(decimal.Zero) {
// 			empty = false
// 		}
// 	}

// 	if c.LatePmts != "" {
// 		empty = false
// 	}

// 	if c.LoanNbr != "" {
// 		empty = false
// 	}

// 	if c.LoanValue != "" {
// 		empty = false
// 	} else {
// 		value, err := decimalFromString(c.AuthAmt)
// 		if err == nil && value.GreaterThan(decimal.Zero) {
// 			empty = false
// 		}
// 	}

// 	if c.LossDate != "" {
// 		empty = false
// 	}

// 	if c.LossType != "" {
// 		empty = false
// 	}

// 	if c.MarketValue != "" {
// 		empty = false
// 	} else {
// 		value, err := decimalFromString(c.AuthAmt)
// 		if err == nil && value.GreaterThan(decimal.Zero) {
// 			empty = false
// 		}
// 	}

// 	if len(c.MiscPayments) > 0 {
// 		empty = false
// 	}

// 	if c.OtherAmt != "" {
// 		empty = false
// 	} else {
// 		value, err := decimalFromString(c.AuthAmt)
// 		if err == nil && value.GreaterThan(decimal.Zero) {
// 			empty = false
// 		}
// 	}

// 	if c.PaidAmt != "" {
// 		empty = false
// 	} else {
// 		value, err := decimalFromString(c.AuthAmt)
// 		if err == nil && value.GreaterThan(decimal.Zero) {
// 			empty = false
// 		}
// 	}

// 	if c.PaidDate != "" {
// 		empty = false
// 	}

// 	if c.ScRefund != "" {
// 		empty = false
// 	}

// 	return empty
// }

// ShouldMigrate determines if the contract should be migrated.  If it should not be migrated, then a reason that it will not be migrated is provided
func (c *TcaGap) ShouldMigrate() (MigrationAllowed, error) {
	// If the contract number is blank, it's because there is no
	// claim on the contract so we need to skip it.
	if c.Claim.ContractNumber == "" {
		return MigrationAllowed{false, fmt.Sprintf("Contract doesn't have a claim, can't migrate.")}, nil
	}

	if c.EffectiveDate == "" {
		return MigrationAllowed{false, fmt.Sprintf("Contract '%s' does not have an effective date.", c.Contract)}, nil
	}

	effectiveDate, err := dateFromString(c.EffectiveDate)
	if err != nil {
		return MigrationAllowed{false, ""}, errors.Wrapf(err, "error parsing EffictiveDate [%s]", c.EffectiveDate)
	}

	// There's one contract for some wierd reason doesn't have a contract code/number.
	if c.Contract == "" {
		return MigrationAllowed{false, fmt.Sprintf("Contract doesn't have a contract number/code, can't migrate. Data: %+v", c)}, nil
	}

	contractFirstChar := c.Contract[0:1]

	// Any contract booked before 1/1/00 (which all start with the single letter 'A') are third party
	if effectiveDate.Before(time.Date(2000, 1, 1, 0, 0, 0, 0, MountainStandardTimezone)) && contractFirstChar == "A" {
		return MigrationAllowed{false, fmt.Sprintf("Contract '%s' is an old thrid-party contract.", c.Contract)}, nil
	}
	// all contracts sold before 3/1/04 that start with a number are also third party
	if effectiveDate.Before(time.Date(2004, 1, 4, 0, 0, 0, 0, MountainStandardTimezone)) && contractFirstChar > "0" && contractFirstChar < "9" {
		return MigrationAllowed{false, fmt.Sprintf("Contract '%s' is an old thrid-party contract.", c.Contract)}, nil
	}

	termMonths, err := intFromString(c.Term)
	if err != nil {
		return MigrationAllowed{}, errors.Wrapf(err, "error parsing term [%s]", c.Term)
	}

	// if c.Claim.isEmpty() {
	// 	return MigrationAllowed{false, fmt.Sprintf("Contract '%s' doesn't have claim data to be migrated.", c.Contract)}, nil
	// }

	return shouldMigrate(c.Contract, c.EffectiveDate, c.ExpirationDate, termMonths)
}

// Transform transforms the TCA Gap contract data into the appropriate Claim records.
func (c *TcaGap) Transform(migUser db.User, caches migrationCaches) error {
	var err error

	nameParts := strings.Split(c.Name, ",")
	firstName := nameParts[0]
	lastName := " "

	if len(nameParts) > 1 {
		lastName = nameParts[0]
		firstName = nameParts[1]
	}

	customer := customer{
		FirstName:      firstName,
		LastName:       lastName,
		EmailAddress:   c.Email,
		PhoneNumber:    c.HomePhone,
		AltPhoneNumber: c.BusinessPhone,
		StreetAddress:  c.Address,
		City:           c.City,
		State:          c.State,
		PostalCode:     c.Zip,
	}

	bank := bank{
		Name:           c.BankCode,
		StreetAddress1: c.BankAddress,
		Zip:            c.BankZip,
	}

	claim := c.Claim
	exists := claim.exists(caches.GapClaims)

	if exists {
		return nil
	}

	claim.Source = "SB"

	tx, err := db.Get().Beginx()
	if err != nil {
		return errors.Wrap(err, "error starting transaction to save claims")
	}

	customerID, err := getCustomerID(tx, caches.Customers, c.Contract, customer)
	if err != nil {
		tx.Rollback()
		return errors.Wrapf(err, "error getting customerID for contract [%s]", c.Contract)
	}
	claim.CustomerID = customerID

	bankID, err := getBankID(tx, caches.Banks, bank)
	if err != nil {
		tx.Rollback()
		return errors.Wrapf(err, "error getting bankID for contract [%s]", c.Contract)
	}
	claim.BankID = bankID

	if claim.CreatedByUserID == 0 {
		claim.CreatedByUserID = migUser.ID
	}

	if claim.OwnerID == 0 {
		claim.OwnerID = migUser.ID
	}

	switch claim.Status {
	case "P":
		claim.Status = db.GapClaimStatusCheckWritten
	case "C":
		fallthrough
	case "A":
		fallthrough
	case "X":
		claim.Status = db.GapClaimStatusClosedNoResponse
	}

	err = claim.saveTcaGapClaim(tx)
	if err != nil {
		tx.Rollback()
		return errors.Wrapf(err, "error trying to create claim for contract [%s]", c.Contract)
	}

	err = tx.Commit()
	if err != nil {
		return errors.Wrapf(err, "error commiting changes for contract [%s]", c.Contract)
	}
	return nil
}

type tcaGapClaimCache map[string]bool

type tcaGapClaimQueryRow struct {
	Contract string `db:"contract_number"`
	ID       int    `db:"id"`
}

func newTcaGapClaimCache() (tcaGapClaimCache, error) {
	var err error
	var contracts []string
	cache := make(tcaGapClaimCache)

	query := `select distinct contract_number from gap_claims`

	err = db.Get().Select(&contracts, query)
	if err != nil {
		return nil, errors.Wrap(err, "error loading claims cache")
	}

	log.Printf("Loaded [%d] contracts with claims\n", len(contracts))

	for _, c := range contracts {
		cache[c] = true
	}

	return cache, nil
}

func (claim *TcaGapClaim) exists(claims tcaGapClaimCache) bool {
	_, exists := claims[claim.ContractNumber]
	return exists
}

func (claim *TcaGapClaim) saveTcaGapClaim(tx *sqlx.Tx) error {
	query := `insert into gap_claims (
		vin, contract_number, status, status_change_description, date_of_loss, date_of_claim_received,
		date_of_last_in, date_of_last_out, waiting_for, customer_id, case_reserve, created_by_user_id,
		has_run_amortization_sheet, run_amortization_sheet_value, has_canceled_contracts,
		has_canceled_service_contract, canceled_service_contract_value, has_canceled_maintenance_contract,
		canceled_maintenance_contract_value, has_canceled_gap_refund_contract, gap_refund_contract_value,
		other_label1, other_value1, other_label2, other_value2, other_label3, other_value3,
		is_valuation_report_available, valuation_report_adjustments, is_valuation_report_matches_base_value,
		valuation_report_matches_base_value, valuation_report_vin_matches, has_valuation_report_prior_damage,
		valuation_report_prior_damage_value, has_valuation_report_misc_fee, valuation_report_misc_fee_value,
		valuation_report_mileage, valuation_report_type, has_options_match_book_out_over_150_percent,
		over_150_percent, has_original_financing_contract, original_financing_contract_value,
		contract_number_matches, bank_history_matches, is_police_report_available,
		has_insurance_policy_deductible, has_bank_information, is_full_loan_history_available,
		payment_amount, interest_rate, first_payment_date, has_new_bank_information, bank_id,
		bank_account_number, last_action, owner_id, has_child_claim, is_child, child_claim_reason,
		insurance_policy_deductible_value_addition, insurance_policy_deductible_value_subtraction,
		insurance_policy_deductible_reason, has_settlement_amount, settlement_amount,has_insurance_check_amount,
		insurance_check_amount, run_amortization_manager_flag, other_label1_manager_flag,
		other_label2_manager_flag, other_label3_manager_flag, insurance_payment_check_manager_flag,
		settlement_letter_manager_flag, valuation_report_manager_flag, valuation_report_base_value_manager_flag,
		valuation_report_vin_matches_manager_flag, valuation_report_prior_damage_manager_flag,
		valuation_report_misc_fee_manager_flag, valuation_report_mileage_manager_flag,
		valuation_report_dol_manager_flag, valuation_report_type_manager_flag,
		options_book_out_over_150_percent_manager_flag, original_financing_manager_flag,
		contract_number_matches_manager_flag, bank_history_matches_manager_flag, police_report_manager_flag,
		insurance_deductible_addition_manager_flag, insurance_deductible_subtraction_manager_flag,
		bank_information_manager_flag, full_loan_history_manager_flag, loan_number_manager_flag,
		payment_amount_manager_flag, interest_rate_manager_flag, first_payment_manager_flag,
		contract_deal_date, contract_term_months, contract_deal_date_manager_flag,
		contract_term_months_manager_flag, calculate_amortization_value, has_msrp_value, msrp_value,
		msrp_value_manager_flag, cs_check_amount, parent_claim_id, has_recovery, insurance_company,
		has_insurance_company, policy_number, has_policy_number, mileage_deduction, has_mileage_deduction,
		nada, has_nada, has_valuation_nada_difference, mileage_deduction_manager_flag, has_estimate_with_photos,
		gap_closed_at, nada_manager_flag, is_cs_claim, not_paid_by_cs, vin_record_id, has_negative_equity_amount,
		has_negative_equity_amount_manager_flag, negative_equity_amount, source
	) values (
		:vin, :contract_number, :status, :status_change_description, :date_of_loss, :date_of_claim_received,
		:date_of_last_in, :date_of_last_out, :waiting_for, :customer_id, :case_reserve, :created_by_user_id,
		:has_run_amortization_sheet, :run_amortization_sheet_value, :has_canceled_contracts,
		:has_canceled_service_contract, :canceled_service_contract_value, :has_canceled_maintenance_contract,
		:canceled_maintenance_contract_value, :has_canceled_gap_refund_contract, :gap_refund_contract_value,
		:other_label1, :other_value1, :other_label2, :other_value2, :other_label3, :other_value3,
		:is_valuation_report_available, :valuation_report_adjustments, :is_valuation_report_matches_base_value,
		:valuation_report_matches_base_value, :valuation_report_vin_matches, :has_valuation_report_prior_damage,
		:valuation_report_prior_damage_value, :has_valuation_report_misc_fee, :valuation_report_misc_fee_value,
		:valuation_report_mileage, :valuation_report_type, :has_options_match_book_out_over_150_percent,
		:over_150_percent, :has_original_financing_contract, :original_financing_contract_value,
		:contract_number_matches, :bank_history_matches, :is_police_report_available,
		:has_insurance_policy_deductible, :has_bank_information, :is_full_loan_history_available,
		:payment_amount, :interest_rate, :first_payment_date, :has_new_bank_information, :bank_id,
		:bank_account_number, :last_action, :owner_id, :has_child_claim, :is_child, :child_claim_reason,
		:insurance_policy_deductible_value_addition, :insurance_policy_deductible_value_subtraction,
		:insurance_policy_deductible_reason, :has_settlement_amount, :settlement_amount, :has_insurance_check_amount,
		:insurance_check_amount, :run_amortization_manager_flag, :other_label1_manager_flag,
		:other_label2_manager_flag, :other_label3_manager_flag, :insurance_payment_check_manager_flag,
		:settlement_letter_manager_flag, :valuation_report_manager_flag, :valuation_report_base_value_manager_flag,
		:valuation_report_vin_matches_manager_flag, :valuation_report_prior_damage_manager_flag,
		:valuation_report_misc_fee_manager_flag, :valuation_report_mileage_manager_flag,
		:valuation_report_dol_manager_flag, :valuation_report_type_manager_flag,
		:options_book_out_over_150_percent_manager_flag, :original_financing_manager_flag,
		:contract_number_matches_manager_flag, :bank_history_matches_manager_flag, :police_report_manager_flag,
		:insurance_deductible_addition_manager_flag, :insurance_deductible_subtraction_manager_flag,
		:bank_information_manager_flag, :full_loan_history_manager_flag, :loan_number_manager_flag,
		:payment_amount_manager_flag, :interest_rate_manager_flag, :first_payment_manager_flag,
		:contract_deal_date, :contract_term_months, :contract_deal_date_manager_flag,
		:contract_term_months_manager_flag, :calculate_amortization_value, :has_msrp_value, :msrp_value,
		:msrp_value_manager_flag, :cs_check_amount, :parent_claim_id, :has_recovery, :insurance_company,
		:has_insurance_company, :policy_number, :has_policy_number, :mileage_deduction, :has_mileage_deduction,
		:nada, :has_nada, :has_valuation_nada_difference, :mileage_deduction_manager_flag, :has_estimate_with_photos,
		:gap_closed_at, :nada_manager_flag, :is_cs_claim, :not_paid_by_cs, :vin_record_id, :has_negative_equity_amount,
		:has_negative_equity_amount_manager_flag, :negative_equity_amount, :source
	)
	returning *`

	stmt, err := tx.PrepareNamed(query)
	if err != nil {
		return errors.Wrap(err, "error preparing query to create claim")
	}
	defer Close(stmt)
	err = stmt.Get(claim, &claim)
	if err != nil {
		return errors.Wrapf(err, "error creating claim. Data %+v", claim)
	}

	for _, payment := range claim.Payments {
		payment.GapClaimID = claim.ID
		err = payment.saveTcaGapClaimPayment(tx)
		if err != nil {
			return errors.Wrap(err, "error creating claim payment")
		}
	}

	for _, contract := range claim.Contracts {
		contract.GapClaimID = claim.ID
		err = contract.saveTcaGapClaimContract(tx)
		if err != nil {
			return errors.Wrap(err, "error creating claim contract")
		}
	}

	return nil
}

func (payment *TcaGapClaimPay) saveTcaGapClaimPayment(tx *sqlx.Tx) error {
	query := `insert into gap_claim_payments (
		authorization_number, gap_claim_id, check_number, amount, paid_date,
		batch_key, bill_key, bill_memo, updated_at
	) values (
		:authorization_number, :gap_claim_id, :check_number, :amount, :paid_date,
		:batch_key, :bill_key, :bill_memo, :updated_at
	)
	returning *`

	stmt, err := tx.PrepareNamed(query)
	if err != nil {
		return errors.Wrap(err, "error preparity query to create claim payment")
	}
	defer Close(stmt)
	err = stmt.Get(payment, &payment)
	if err != nil {
		return errors.Wrapf(err, "error creating claim payment. Data %+v", payment)
	}

	return nil
}

func (contract *TcaGapClaimContract) saveTcaGapClaimContract(tx *sqlx.Tx) error {
	query := `insert into gap_claim_contracts (
		gap_claim_id, contract_number, contract_code, contract_name, contract_flag,
		contract_value, manager_flag
	) values (
		:gap_claim_id, :contract_number, :contract_code, :contract_name, :contract_flag,
		:contract_value, :manager_flag
	)
	returning *`

	stmt, err := tx.PrepareNamed(query)
	if err != nil {
		return errors.Wrap(err, "error preparing query to create gap claim contract")
	}
	defer Close(stmt)
	err = stmt.Get(contract, &contract)
	if err != nil {
		return errors.Wrapf(err, "error creating claim contract. Data %+v", contract)
	}

	return nil
}
