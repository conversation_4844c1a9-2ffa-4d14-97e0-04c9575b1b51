package sbmigrate

import (
	"bytes"
	"encoding/json"
	"fmt"
	"strings"

	"phizz/db"

	"github.com/pkg/errors"
)

// TcaMaintenance defines the structure of a TCA Maintenance contract that has been exported from SB
type TcaMaintenance struct {
	Contract       string `json:"Contract,omitempty"`       // contracts.code
	EffectiveDate  string `json:"Effective Date,omitempty"` // contracts.effective_date
	BeginningMiles string `json:"Odometer,omitempty"`       // contracts.effective_milage
	Miles          string `json:"Miles,omitempty"`          // contracts.plan_mileage
	Term           string `json:"Months,omitempty"`         // contracts.plan_duration
	Price          string `json:"Price,omitempty"`          // contracts.price
	Plan           string `json:"Plan,omitempty"`           // contracts.product_name, contracts.product_variant_name, contracts.product_variant_display_name
	Store          string `json:"Store,omitempty"`          // stores.code
	Status         string `json:"Status,omitempty"`         // contracts.status (A=Active, C=Canceled, X=Expired, P=Pending)
	PrintDate      string `json:"Print Date,omitempty"`     // skip
	LcaXref        string `json:"Lca Xref,omitempty"`       // ???
	Vin            string `json:"Vin number,omitempty"`     // vin_records.vin
	Created        string `json:"Created,omitempty"`        // contracts.created_date
	Customer       struct {
		Name           string `json:"Name,omitempty"`             // customers.last_name, customers.first_name
		Address        string `json:"Address,omitempty"`          // customers.address
		Phone          string `json:"Phone,omitempty"`            // customers.phone
		City           string `json:"City,omitempty"`             // customers.city
		State          string `json:"State,omitempty"`            // customers.state_code
		Zip            string `json:"Zip,omitempty"`              // customers.postal_code
		EntryDate      string `json:"Entry Date,omitempty"`       // customers.created_date
		FirstStoreCode string `json:"First Store Code,omitempty"` // ???
		Email          string `json:"Email Addr,omitempty"`       // customers.email
		MobilePhone    string `json:"Mobile Phone,omitempty"`     // customers.phone if Phone is empty
		Contact        string `json:"Contact,omitempty"`          // ???
		BiFlag         string `json:"Biflag,omitempty"`           // customers.is_business
	} `json:"Customer,omitempty"`
	BankCode struct {
		BankName string `json:"Bank Name,omitempty"`
		Address  string `json:"Address,omitempty"`
		City     string `json:"City"`
		State    string `json:"State"`
		Zip      string `json:"Zip"`
		Phone    string `json:"Phone"`
	} `json:"Bank Code,omitempty"`
	Cost struct {
		Cost       string `json:"Cost,omitempty"`       // Contract total cost
		BaseCost   string `json:"Base Cost,omitempty"`  // Base Cost (i.e. Plan Cost)
		CLP        string `json:"CLP,omitempty"`        // CLP Fee
		Admin      string `json:"Admin,omitempty"`      // Admin Fee
		Adjustment string `json:"Adjustment,omitempty"` // ???
		Rebate     string `json:"Rebate,omitempty"`     // ???
	} `json:"Cost,omitempty"` // contracts.plan_cost
	ExpiredMonths   string                   `json:"Expired months,omitempty"`   // ???
	UnearnedPremium string                   `json:"Unearned Premium,omitemtpy"` // ???
	Claims          []*AutomotiveClaim       `json:"Claims,omitempty"`           // Skip for now
	Cancellation    maintCancellationWrapper `json:"Cancellation,omitempty"`     // Skip for now
	Events          EventWrapper             `json:"EVENTS,omitempty"`           // ???
	FIDeal          string                   `json:"Fideal,omitempty"`           // ??? sales.dms_number if source is 'F&I'
	BillInvoice     string                   `json:"Bill Invoice,omitempty"`     // skip
	BillDate        string                   `json:"Bill Date,omitempty"`        // skip
	BookDate        string                   `json:"Book date,omitempty"`        // ???
	Reinstate       []string                 `json:"Reinstate,omitempty"`        // ???
	Transfer        struct {
		TransferTo string `json:"Transfer to,omitempty"`
	} `json:"Transfer,omitempty"` // ???
	CouponsPurchased string   `json:"Coupons Purchased,omitempty"` // contracts.maintenance_visits
	CouponValue      string   `json:"Coupon Value,omitempty"`      // contracts.mantenance_visit_value
	Year             string   `json:"Veh Yr,omitempty"`            // vin_records.year
	Make             string   `json:"Veh Make,omitempty"`          // vin_records.make
	Model            string   `json:"Veh Model,omitempty"`         // vin_records.model
	FIManager        struct { // ???
		FiMgrNbr string `json:"Fi Mgr Nbr,omitempty"`  // users.employee_number via sales.salesperson_id
		FiMgr    string `json:"Fi Mgr Name,omitempty"` // users.last_name, users.first_name via sales.salesperson_id
	} `json:"FI Manager,omitempty"`
	RO             string   `json:"RO,omitempty"`         // ??? sales.dms_number if source is 'S'
	ExpirationDate string   `json:"Expdate,omitempty"`    // contracts.expiration_date
	Fleet          string   `json:"Fleet"`                // ???
	Tega           string   `json:"Tega,omitempty"`       // skip
	SoldDate       string   `json:"Sold Date,omitempty"`  // ???
	SppFlag        string   `json:"Sppflag,omitempty"`    // ???
	ElectSign      string   `json:"Elect Sign,omitempty"` // ???
	SPPTransmit    struct { // ???
		SppCancelTransDate string `json:"Spp Cancel trans date,omitempty"`
		SppTransDate       string `json:"Spp Trans date,omitempty"`
	} `json:"SPP transmit,omitempty"`
	Special    string `json:"Special,omitempty"` // skip
	LastChange struct {
		ChangeDate string `json:"Chgdate,omitempty"` // ???
		ChgTime    string `json:"Chgtime,omitempty"` // ???
	} `json:"Last change,omitempty"`
	MinorCoupon           string               `json:"Minor Coupon,omitempty"`         // ??? Consider adding contracts.minor_maintenance_visit_value or a new table for all maintenance data
	MinorCouponsPurchased string               `json:"Minor Cpns Purchased,omitempty"` // ??? Consider adding contracts.minor_maintenance_visits or a new table for all maintenance data
	Source                string               `json:"Source,omitempty"`               // ???
	FrozenUpdates         []string             `json:"Frozen Updates,omitempty"`       // skip
	FrozenFlag            string               `json:"Frozen Flag (F/M/O),omitempty"`  // skip
	FrozenReason          string               `json:"Frozen Reason,omitempty"`        // skip
	FrozenExpired         string               `json:"Frozen Expired,omitempty"`       // skip
	PurchaseLeaseFlag     string               `json:"Lease Flag,omitempty"`           // ??? sales.payment_type
	CrcFlag               string               `json:"Crc Flag,omitempty"`             // ???
	ProgSource            string               `json:"Prog Source,omitempty"`          // skip
	MgtFees               maintMgmtFeesWrapper `json:"Mgt Fees,omitempty"`             // ??? Maybe contracts_adjustments, or rate_buckets
	Rebate                string               `json:"Rebate,omitempty"`               // skip
	RebateCancel          string               `json:"Rebate Cancel,omitempty"`        // skip
	Broker                string               `json:"Broker,omitempty"`               // skip
	TransferDate          string               `json:"Transfer date,omitempty"`        // ???
	FormNbr               string               `json:"Form Nbr,omitempty"`             // ???
	ReturnedSpiff         struct {
		ReturnedSpiffDate   string `json:"RSpiff Date,omitempty"`        // ???
		ReturnedSpiffAmount string `json:"Returned Spiff Amt,omitempty"` // ???
	} `json:"Returned Spiff,omitempty"`
	OwnerVehicleTransfer ownerVehicleTransferWrapper  `json:"Owner/Veh Transfer,omitempty"` // ???
	ManuallyAdded        contractManuallyAddedWrapper `json:"Manually added,omitempty"`
	ValidationErrors     []string                     `json:"Validation Errors,omitempty"`
	OriginalCosts        struct {
		Cost      string `json:"Cost,omitempty"`
		Core      string `json:"Core,omitempty"`
		Admin     string `json:"Admin,omitempty"`
		Marketing string `json:"Marketing,omitempty"`
		Clip      string `json:"Clip,omitempty"`
		RSA       string `json:"RSA,omitempty"`
		Risk      string `json:"Risk,omitempty"`
		Other     string `json:"Other,omitempty"`
		Premium   string `json:"Premium,omitempty"`
		Escrow    string `json:"Escrow,omitempty"`
	}
}

type maintMgmtFees struct {
	MgtFeeCodes      string `json:"Mfees Codes,omitempty"` // ???
	MgtFeeAmts       string `json:"Mfees Amts,omitempty"`  // adjustments.amount?
	MgtFeeRefundAmts string `json:"Mfees Ramts,omitempty"` //
}

type maintMgmtFeesWrapper []struct {
	maintMgmtFees
}

func (w *maintMgmtFeesWrapper) UnmarshalJSON(data []byte) error {
	// Define a type with the same structure but without the custom unmarshaller
	type dataArray maintMgmtFeesWrapper

	// Unmarshall as interface{} so we can check if we have a single item or an array of items
	var rawField interface{}
	err := json.Unmarshal(data, &rawField)
	if err != nil {
		return errors.Wrapf(err, "Failed to Unmarshal JSON: %s", string(data))
	}
	dec := json.NewDecoder(bytes.NewReader(data))
	dec.DisallowUnknownFields()
	switch v := rawField.(type) {
	case map[string]interface{}:
		elements := make(dataArray, 1)
		*w = maintMgmtFeesWrapper(elements)
		return dec.Decode(&elements[0])
	case []interface{}:
		elements := make(dataArray, len(v))
		*w = maintMgmtFeesWrapper(elements)
		return dec.Decode(&elements)
	}
	return nil
}

// MaintCancellation represents a Cancelleation on a Maintenance contract
type MaintCancellation struct {
	CancelDate         string `json:"Cancel Date,omitempty"`
	RefundAmount       string `json:"Refund Amt,omitempty"`
	CancelReason       string `json:"Cancel Reason,omitempty"`
	CancelCode         string `json:"Cancel Code,omitempty"`
	CancelFee          string `json:"Cancel Fee,omitempty"`
	CouponsReturned    string `json:"Coupons Returned,omitempty"`
	RefundAmountPaid   string `json:"Refund Amount Paid,omitempty"`
	RefundDate         string `json:"Refund date,omitempty"`
	RefundCheck        string `json:"Refund Check No.,omitempty"`
	MilesCancel        string `json:"Miles Cancel,omitempty"`
	RefamtCust         string `json:"Refamt Cust,omitempty"`
	CancelCheckDate    string `json:"Can Check Date,omitempty"`
	CancellationFactor string `json:"Cancel Factor,omitempty"`
	StoreAmount        string `json:"Store Amount,omitempty"`
}

type maintCancellationWrapper []struct {
	MaintCancellation
}

func (w *maintCancellationWrapper) UnmarshalJSON(data []byte) error {
	// Define a type with the same structure but without the custom unmarshaller
	type dataArray maintCancellationWrapper

	// Unmarshall as interface{} so we can check if we have a single item or an array of items
	var rawField interface{}
	err := json.Unmarshal(data, &rawField)
	if err != nil {
		return errors.Wrapf(err, "Failed to Unmarshal JSON: %s", string(data))
	}
	dec := json.NewDecoder(bytes.NewReader(data))
	dec.DisallowUnknownFields()
	switch v := rawField.(type) {
	case map[string]interface{}:
		elements := make(dataArray, 1)
		*w = maintCancellationWrapper(elements)
		return dec.Decode(&elements[0])
	case []interface{}:
		elements := make(dataArray, len(v))
		*w = maintCancellationWrapper(elements)
		return dec.Decode(&elements)
	}
	return nil
}

type ownerVehicleTransfer struct {
	TransferChange     string `json:"Transchange,omitempty"`
	TransferChangeDate string `json:"Transchgdate,omitempty"`
}

type ownerVehicleTransferWrapper []struct {
	ownerVehicleTransfer
}

func (w *ownerVehicleTransferWrapper) UnmarshalJSON(data []byte) error {
	// Define a type with the same structure but without the custom unmarshaller
	type dataArray ownerVehicleTransferWrapper

	// Unmarshall as interface{} so we can check if we have a single item or an array of items
	var rawField interface{}
	err := json.Unmarshal(data, &rawField)
	if err != nil {
		return errors.Wrapf(err, "Failed to Unmarshal JSON: %s", string(data))
	}
	dec := json.NewDecoder(bytes.NewReader(data))
	dec.DisallowUnknownFields()
	switch v := rawField.(type) {
	case map[string]interface{}:
		elements := make(dataArray, 1)
		*w = ownerVehicleTransferWrapper(elements)
		return dec.Decode(&elements[0])
	case []interface{}:
		elements := make(dataArray, len(v))
		*w = ownerVehicleTransferWrapper(elements)
		return dec.Decode(&elements)
	}
	return nil
}

// Extract will Extract the next Contract from the decoder
func (c *TcaMaintenance) Extract(dec *json.Decoder) error {
	*c = TcaMaintenance{} // Make sure old values are discarded
	return dec.Decode(c)
}

// ID returns the ID of the current contract
func (c *TcaMaintenance) ID() string {
	return c.Contract
}

// ShouldMigrate determines if the contract should be migrated.  If it should not be migrated, then a reason that it will not be migrated is provided
func (c *TcaMaintenance) ShouldMigrate() (MigrationAllowed, error) {
	if len(c.Claims) == 0 {
		return MigrationAllowed{allowed: false, reason: fmt.Sprintf("Contract [%s] has no claims to migrate", c.ID())}, nil
	}

	termMonths, err := intFromString(c.Term)
	if err != nil {
		return MigrationAllowed{}, errors.Wrapf(err, "error parsing term [%s]", c.Term)
	}

	// When there's no Term and Plan then the contract is an invalid contract and should not
	// be migrated.
	if termMonths == 0 && strings.TrimSpace(c.Plan) == "" {
		return MigrationAllowed{
			allowed: false,
			reason:  "No months and no plan associated with contract",
		}, nil
	}

	return shouldMigrate(c.Contract, c.EffectiveDate, c.ExpirationDate, termMonths)
}

// ClaimsTable returns the name of the table that contains the claims and the name of the Contract number column
func (c *TcaMaintenance) ClaimsTable() (string, string) {
	return AutomotiveClaimTable, AutomotiveClaimTableContractColumn
}

// ProductCode returns the code of the product that is being migrated.
func (c *TcaMaintenance) ProductCode() string {
	return db.ProductCodeMaintenance
}

// Transform transforms the TCA Maintenance contract data into the appropriate Claim records.
func (c *TcaMaintenance) Transform(migUser db.User, caches migrationCaches) error {
	tx, err := db.Get().Beginx()
	if err != nil {
		return errors.Wrap(err, "error starting transaction to save claims")
	}

	nameParts := strings.Split(c.Customer.Name, ",")
	firstName := nameParts[0]
	lastName := " "

	if len(nameParts) > 1 {
		lastName = nameParts[0]
		firstName = nameParts[1]
	}

	customer := customer{
		FirstName:      firstName,
		LastName:       lastName,
		EmailAddress:   c.Customer.Email,
		PhoneNumber:    c.Customer.Phone,
		AltPhoneNumber: "",
		StreetAddress:  c.Customer.Address,
		City:           c.Customer.City,
		State:          c.Customer.State,
		PostalCode:     c.Customer.Zip,
	}

	for _, claim := range c.Claims {
		exists := claim.exists(caches.AutomotiveClaims)

		if exists {
			continue
		}

		claim.Source = "SB"

		// If there's a paid date set the status the "Check Written" otherwise
		// skip the claim.
		if len(claim.Payments) > 0 && claim.Payments[0].PaidDate.Valid {
			claim.Status = db.AutoClaimStatusCheckWritten
		} else {
			continue
		}

		customerID, err := getCustomerID(tx, caches.Customers, c.Contract, customer)
		if err != nil {
			tx.Rollback()
			return errors.Wrapf(err, "error getting customerID for contract [%s]", c.Contract)
		}

		claim.CustomerID = customerID

		if claim.CreatedByUserID == 0 {
			claim.CreatedByUserID = migUser.ID
		}

		if claim.OwnerID == 0 {
			claim.OwnerID = migUser.ID
		}

		for _, n := range claim.Notes {
			if n.CreatedByUserID == 0 {
				n.CreatedByUserID = migUser.ID
			}
		}

		err = claim.saveClaim(tx)
		if err != nil {
			tx.Rollback()
			return errors.Wrapf(err, "error trying to create claim [%s] for contract [%s]", claim.SbID, c.ID())
		}
	}

	err = tx.Commit()
	if err != nil {
		return errors.Wrapf(err, "error commiting changes for claim [%s]", c.ID())
	}

	return nil
}
