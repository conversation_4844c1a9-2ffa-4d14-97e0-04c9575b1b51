package sbmigrate

import (
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
)

const (
	shortDateFormat = "01/02/06"
	longDateFormat  = "01/02/2006"
	longDateFormat2 = "01022006"

	mountainStandardTime = "America/Denver"
)

// The following contants define CPP Companies
const (
	SPPCompanyLandcar = "Landcar"
)

// The following are valid contract statuses
const (
	StatusActive   = "A"
	StatusCanceled = "C"
	StatusExpired  = "X"
	StatusPending  = "P"
	StatusNew      = "N" // TODO: Verify that N = New
)

var (
	regexInt = regexp.MustCompile(`.*?(\d+).*?`)
)

// MountainStandardTimezone represents the MST timezone location
var MountainStandardTimezone *time.Location

// Parses a string into a decimal.  If the string is empty then 0.0 will be returned
func decimalFromString(value string) (decimal.Decimal, error) {
	if value == "" {
		return decimal.Zero, nil
	}

	return decimal.NewFromString(value)
}

// Parses a string into a nullDecimal.  If the string is empty then 0.0 will be returned
func nullDecimalFromString(value string) (decimal.NullDecimal, error) {
	if value == "" {
		return decimal.NullDecimal{}, nil
	}
	var v decimal.NullDecimal
	err := v.Scan(value)
	return v, err
}

func floatFromString(value string) (float64, error) {
	if value == "" {
		return 0.00, nil
	}

	num, err := strconv.ParseFloat(value, 64)
	if err != nil {
		return 0.00, errors.Wrapf(err, "error parsing [%s] as float64", value)
	}

	return num, nil
}

func intFromString(value string) (int, error) {
	// Treat an empty string as 0
	if value == "" {
		return 0, nil
	}

	num, err := strconv.Atoi(value)
	if err != nil {
		// Only use a regex if we failed to convert a string to an int
		matches := regexInt.FindStringSubmatch(value)
		if len(matches) != 2 {
			return 0, err
		}
		return strconv.Atoi(matches[1])
	}

	return num, nil
}

func yearFromString(value string) (int, error) {
	year, err := intFromString(value)
	if err != nil {
		return 0, errors.Wrapf(err, "error converting string to year [%s]", value)
	}

	// Handle 2 digit years
	if year < 100 && year >= 30 {
		return year + 1900, nil
	}
	if year <= 30 && year != 0 { // do not treat 0 as a two digit year
		return year + 2000, nil
	}

	return year, nil
}

// GetMountainStandardTimezone gets the MST timezone
func GetMountainStandardTimezone() (*time.Location, error) {
	timezone, err := time.LoadLocation(mountainStandardTime)
	if err != nil {
		return nil, errors.Wrapf(err, "error getting time zone [%s]", mountainStandardTime)
	}

	return timezone, nil
}

func dateFromString(date string) (time.Time, error) {
	var parsedDate time.Time
	var err error
	if len(date) == len(shortDateFormat) && strings.Contains(date, "/") {
		parsedDate, err = time.ParseInLocation(shortDateFormat, date, MountainStandardTimezone)
		if err != nil {
			return time.Time{}, errors.Wrapf(err, "error parsing date as %s [%s] ", shortDateFormat, date)
		}
		return parsedDate, nil
	}

	if len(date) == len(longDateFormat) {
		parsedDate, err = time.ParseInLocation(longDateFormat, date, MountainStandardTimezone)
		if err != nil {
			return time.Time{}, errors.Wrapf(err, "error parsing date as %s [%s] ", longDateFormat, date)
		}
		return parsedDate, nil
	}

	if strings.Contains(date, "T") {
		parsedDate, err = time.ParseInLocation(time.RFC3339, date, MountainStandardTimezone)
		if err != nil {
			return time.Time{}, errors.Wrapf(err, "error parsing date as %s [%s] ", time.RFC3339, date)
		}
		return parsedDate, nil
	}

	parsedDate, err = time.ParseInLocation(longDateFormat2, date, MountainStandardTimezone)
	if err != nil {
		return time.Time{}, errors.Wrapf(err, "error parsing date as %s [%s] ", longDateFormat2, date)
	}
	return parsedDate, nil
}
