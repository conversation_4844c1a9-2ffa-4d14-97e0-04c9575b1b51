package sbmigrate

import (
	"encoding/json"
	"fmt"
	"time"

	"phizz/db"

	"github.com/pkg/errors"
)

// Transformer will convert a contract from JSON to a Connect contract
type Transformer interface {
	Extract(dec *json.Decoder) error
	Transform(migUser db.User, caches migrationCaches) error
	ID() string
	ClaimsTable() (string, string)
	ShouldMigrate() (MigrationAllowed, error)
	ProductCode() string
}

// MigrationAllowed indicates if a contract should be migrated, and if not a reason is provided.
type MigrationAllowed struct {
	allowed bool
	reason  string
}

func shouldMigrate(contract string, effectiveDate string, expirationDate string, termMonths int) (MigrationAllowed, error) {
	if effectiveDate == "" {
		return MigrationAllowed{false, fmt.Sprintf("contract [%s] does not have an effective date", contract)}, nil
	}

	var expires time.Time
	var err error

	expCutoff := time.Date(2017, 1, 1, 0, 0, 0, 0, MountainStandardTimezone)

	if expirationDate != "" {
		expires, err = dateFromString(expirationDate)
		if err != nil {
			return MigrationAllowed{}, errors.Wrapf(err, "error parsing ExpirationDate [%s] for contract [%s]", expirationDate, contract)
		}
	} else {
		effectiveDate, err := dateFromString(effectiveDate)
		if err != nil {
			return MigrationAllowed{}, errors.Wrapf(err, "error parsing EffectiveDate [%s] for contract [%s]", effectiveDate, contract)
		}
		expires = effectiveDate.AddDate(0, termMonths, 0)
	}

	if expires.Before(expCutoff) {
		return MigrationAllowed{false, fmt.Sprintf("contract [%s] expired at [%v]", contract, expires)}, nil
	}

	return MigrationAllowed{true, ""}, nil

}
