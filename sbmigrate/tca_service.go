package sbmigrate

import (
	"bytes"
	"encoding/json"
	"fmt"
	"strings"

	"phizz/db"

	"github.com/pkg/errors"
)

// TcaService defines the strudture of a TCA Service contract that has been exported from SB
type TcaService struct {
	Contract       string `json:"Contract,omitempty"`          // contracts.code
	EffectiveDate  string `json:"Effective Date,omitempty"`    // contracts.effective_date
	Name           string `json:"Cust Name,omitempty"`         // customers.last_name, customers.first_name
	Address        string `json:"Cust Addr,omitempty"`         // customers.address
	City           string `json:"Cust City,omitempty"`         // customers.city
	State          string `json:"Cust State,omitempty"`        // customers.state_code
	Zip            string `json:"Cust Zip,omitempty"`          // customers.postal_code
	BeginningMiles string `json:"Curr Miles,omitempty"`        // sales.odometer, contracts.effective_milage
	BookDate       string `json:"Book date,omitempty"`         // New column needed
	ExpireMiles    string `json:"Exp Miles,omitempty"`         // ???
	Price          string `json:"Contract Price,omitempty"`    // contracts.price
	ExpirationDate string `json:"Exp Date,omitempty"`          // contracts.expiration_date
	Store          string `json:"Store Code,omitempty"`        // stores.code
	Year           string `json:"Veh Year,omitempty"`          // vin_records.year
	Make           string `json:"Make,omitempty"`              // vin_records.make
	Model          string `json:"Model,omitempty"`             // vin_records.model
	Vin            string `json:"Vehicle Id Number,omitempty"` // vin_records.vin
	Terms          string `json:"Terms,omitempty"`             // ???
	NewUsed        string `json:"New/Used,omitempty"`          // ??? sales.is_new
	BankCode       struct {
		BankName string `json:"Bank Name,omitempty"`
		Address  string `json:"Address,omitempty"`
		City     string `json:"City"`
		State    string `json:"State"`
		Zip      string `json:"Zip"`
		Phone    string `json:"Phone"`
	} `json:"Bank Code,omitempty"`
	Plan         string   `json:"Plan,omitempty"`              // skip - 'N'ew or 'U'sed concatenated with the Years of the term, then miles in thousands of the term, then the plan/product code
	Cost         string   `json:"Cost,omitempty"`              // contracts.plan_cost
	Options      string   `json:"Options,omitempty"`           // contract_options
	Deductible   string   `json:"Deductible Amount,omitempty"` // ???
	VehicleType  string   `json:"Veh Type,omitempty"`          // ???
	Comments     []string `json:"Comments,omitempty"`          // ???
	Cancellation struct {
		CancelDate         string `json:"Cancel Date,omitempty"`
		RefundDate         string `json:"Refund date,omitempty"`
		RefundAmount       string `json:"Refund Amount,omitempty"`
		RefundCheck        string `json:"Refund Check,omitempty"`
		CancelFee          string `json:"Cancel Fee,omitempty"`
		CancelReason       string `json:"Cancel Reason,omitempty"`
		CancelCode         string `json:"Cancel Code,omitempty"`
		CustomerRefund     string `json:"Customer Refund,omitempty"`
		CancelOdometer     string `json:"Cancel Odo,omitempty"`
		CancelCheckDate    string `json:"Can Check Date,omitempty"`
		CancellationFactor string `json:"Cancel Factor,omitempty"`
	} `json:"Cancellation,omitempty"` // skip for now
	TransferToContract     string                                  `json:"Transfer To contract,omitempty"`     // ???
	TransferFromContract   string                                  `json:"Transfer From contract,omitempty"`   // ???
	ClaimReserve           string                                  `json:"Claim Reserve,omiteSurchargesmpty"`  // ???
	UnearnedPremium        string                                  `json:"Unearned,omitemtpy"`                 // ???
	Status                 string                                  `json:"STATUS,omitempty"`                   // contracts.status (A=Active, C=Canceled, X=Expired, P=Pending, N=New)
	FullyRefundable        string                                  `json:"Fully Refundable,omitempty"`         // ???
	Claims                 []*AutomotiveClaim                      `json:"Claims,omitempty"`                   // skip for now
	Events                 EventWrapper                            `json:"EVENTS,omitempty"`                   // ???
	FIDeal                 string                                  `json:"FI Deal#,omitempty"`                 // sales.dms_number
	Reinstate              string                                  `json:"Reinstate,omitempty"`                // ???
	Adjusted               string                                  `json:"Adjusted,omitempty"`                 // skip
	TrackingStatus         string                                  `json:"Tracking Status,omitempty"`          // ???
	FiMgr                  string                                  `json:"Fi Mgr,omitempty"`                   // ??? users.last_name, users.first_name via sales.salesperson_id
	MgtFees                tcaServiceMgmtFeesWrapper               `json:"Mgt Fees,omitempty"`                 // ???
	Rental                 string                                  `json:"Rental,omitempty"`                   // ???
	Email                  string                                  `json:"Email Addr,omitempty"`               // customers.email
	FiMgrNbr               string                                  `json:"Fi Mgr Nbr,omitempty"`               // ??? users.employee_number via sales.salesperson_id
	HomePhone              string                                  `json:"Phone,omitempty"`                    // customers.phone
	CellPhone              string                                  `json:"Cell,omitempty"`                     // customers.phone if HomePhone not provided
	ContactMethod          string                                  `json:"Contact Method,omitempty"`           // ???
	BusinessFlag           string                                  `json:"Biflag,omitempty"`                   // ???
	Reserve                string                                  `json:"Reserve,omitempty"`                  // ???
	SPPDownback            string                                  `json:"Spp Downback,omitempty"`             // ???
	SPPCancel              string                                  `json:"Spp Cancel,omitempty"`               // ???
	SPPTransFlag           string                                  `json:"Spp Transflag,omitempty"`            // ???
	SPPFlag                string                                  `json:"Sppflag,omitempty"`                  // ???
	Special                string                                  `json:"Special,omitempty"`                  // ???
	RptRenew               string                                  `json:"Rpt Renew,omitempty"`                // skip
	Premium                string                                  `json:"Premium,omitempty"`                  // ???
	UnearnedPremiumHistory tcaServiceUnearnedPremiumHistoryWrapper `json:"Unearned Premium History,omitempty"` // ???
	Source                 string                                  `jso:"Source,omitempty"`                    // ???
	PurchaseLeaseFlag      string                                  `json:"Lease Flag,omitempty"`               // ??? sales.payment_type
	DisappearingDeduct     string                                  `json:"Disappearing Deduct,omitempty"`      // ???
	CrcFlag                string                                  `json:"Crc Flag,omitempty"`                 // ??? sales.is_crc
	RSATransfer            struct {
		New    string `json:"New,omitempty"`
		Cancel string `json:"Cancel,omitempty"`
	} `json:"RSA Transfer,omitempty"` // ???
	ProgSource   string `json:"Prog Source,omitempty"` // skip
	FormNbr      string `json:"Form,omitempty"`        // ???
	CostBreakout struct {
		Deduct200             string `json:"$200 DEDUCT,omitempty"`
		Deduct50              string `json:"$50 DEDUCT,omitempty"`
		CommercialUseQuartTon string `json:"3/4T COMM USE,omitempty"`
		CommercialUseOneTon   string `json:"1T COMM USE,omitempty"`
		Admin                 string `json:"ADMIN,omitempty"`
		Core                  string `json:"BASE CORE,omitempty"`
		CalculatedCost        string `json:"CALCED COST,omitempty"`
		Canadian              string `json:"CANADIAN,omitempty"`
		Chassis250            string `json:"CHASSIS 250,omitempty"`
		Chassis350            string `json:"CHASSIS 350,omitempty"`
		Chassis35035000       string `json:"CHASSIS 35035000,omitempty"`
		ChevroletExpress      string `json:"CHEV EXPRESS,omitempty"`
		ChevroletExpressG     string `json:"CHEV EXPRESS G,omitempty"`
		CLP                   string `json:"CLP,omitempty"`
		Diesel                string `json:"DIESEL,omitempty"`
		DisappearDeduction    string `json:"DISAPPEAR DEDUCT,omitempty"`
		F450                  string `json:"F450,omitempty"`
		F550                  string `json:"F550,omitempty"`
		FactoryWarranty       string `json:"FACTORY WARR,omitempty"`
		FinalCost             string `json:"FINAL COST,omitempty"`
		HighTech              string `json:"HIGH TECH,omitempty"`
		InspectionFee         string `json:"INSPECTION FEE,omitempty"`
		InspectionRefund      string `json:"INSPECTION REFUND,omitempty"`
		LhmSpiff              string `json:"LHM SPIFF,omitempty"`
		ManualAdjustment      string `json:"MANUAL ADJUST,omitempty"`
		Marketing             string `json:"MARKETING,omitempty"`
		MbMetris              string `json:"MB METRIS,omitempty"`
		NissanNvG             string `json:"NISSAN NV G,omitempty"`
		Nissan1500P           string `json:"NISSAN 1500+,omitempty"`
		PtsDiesel             string `json:"P/T/S DIESEL,omitempty"`
		ProTranSprint         string `json:"PRO/TRAN/SPRINT,omitempty"`
		ProTransSprint        string `json:"PRO/TRANS/SPRINT,omitempty"`
		RentalUpgrade         string `json:"RENTAL UPGRADE,omitempty"`
		Risk                  string `json:"RISK,omitempty"`
		RSA                   string `json:"RSA,omitempty"`
		SealsAndGaskets       string `json:"SEALS & GASKETS,omitempty"`
		SmartTech             string `json:"SMART TECH,omitempty"`
		StdPPlus              string `json:"STD PPLUS,omitempty"`
		ZeroDeduction         string `json:"ZERO DEDUCT,omitempty"`
		Other                 string `json:"OTHER,omitempty"`
	} `json:"Cost Breakout,omitempty"`
	Canadian         string                            `json:"Canadian,omitempty"`    // ???
	Warranty         string                            `json:"Warranty,omitempty"`    // ???
	DriveTrain       string                            `json:"Drivetrain,omitempty"`  // ???
	WarningMessage   string                            `json:"Warning Msg,omitempty"` // skip
	CostAddReserve   string                            `json:"Cost Addrsv,omitempty"` // ???
	CustomerTransfer tcaServiceCustomerTransferWrapper `json:"Customer Transfer,omitempty"`
	Surcharges       tcaServiceSurchargesWrapper       `json:"Surcharges,omitempty"` // ???
	Frozen           struct {
		TypeFMO       string `json:"Type (F/M/O),omitempty"`
		FrozenReason  string `json:"Frozen Reason,omitempty"`
		FrozenExpired string `json:"Frozen Expired,omitempty"`
	} `json:"Frozen,omitempty"` // skip
	CostClass           string                       `json:"Cost Class,omitempty"` // ???
	Liability           string                       `json:"Liability,omitempty"`  // ???
	ManuallyAdded       contractManuallyAddedWrapper `json:"Manually added,omitempty"`
	CertifiedInspection string                       `json:"Cert Insp,omitempty"`
	ValidationErrors    []string                     `json:"Validation Errors,omitempty"`
	OriginalCosts       struct {
		Cost      string `json:"Cost,omitempty"`
		Core      string `json:"Core,omitempty"`
		Admin     string `json:"Admin,omitempty"`
		Marketing string `json:"Marketing,omitempty"`
		Clip      string `json:"Clip,omitempty"`
		RSA       string `json:"RSA,omitempty"`
		Risk      string `json:"Risk,omitempty"`
		Other     string `json:"Other,omitempty"`
		Premium   string `json:"Premium,omitempty"`
		Escrow    string `json:"Escrow,omitempty"`
	}
}

type tcaServiceUnearnedPremiumHistory struct {
	Date          string `json:"Date,omitempty"`
	AmountHistory string `json:"Amount Hist,omitempty"`
}

type tcaServiceUnearnedPremiumHistoryWrapper []struct {
	tcaServiceUnearnedPremiumHistory
}

func (w *tcaServiceUnearnedPremiumHistoryWrapper) UnmarshalJSON(data []byte) error {
	// Define a type with the same structure but without the custom unmarshaller
	type dataArray tcaServiceUnearnedPremiumHistoryWrapper

	// Unmarshall as interface{} so we can check if we have a single item or an array of items
	var rawField interface{}
	err := json.Unmarshal(data, &rawField)
	if err != nil {
		return errors.Wrapf(err, "Failed to Unmarshal Claims JSON: %s", string(data))
	}
	dec := json.NewDecoder(bytes.NewReader(data))
	dec.DisallowUnknownFields()
	switch v := rawField.(type) {
	case map[string]interface{}:
		elements := make(dataArray, 1)
		*w = tcaServiceUnearnedPremiumHistoryWrapper(elements)
		return dec.Decode(&elements[0])
	case []interface{}:
		elements := make(dataArray, len(v))
		*w = tcaServiceUnearnedPremiumHistoryWrapper(elements)
		return dec.Decode(&elements)
	}
	return nil
}

type tcaServiceMgmtFees struct {
	MgtFeeCodes      string `json:"Fee Codes,omitempty"`   // ???
	MgtFeeAmts       string `json:"Mfees Amts,omitempty"`  // adjustments.amount?
	MgtFeeRefundAmts string `json:"Mfees Ramts,omitempty"` //
}

type tcaServiceMgmtFeesWrapper []struct {
	tcaServiceMgmtFees
}

func (w *tcaServiceMgmtFeesWrapper) UnmarshalJSON(data []byte) error {
	// Define a type with the same structure but without the custom unmarshaller
	type dataArray tcaServiceMgmtFeesWrapper

	// Unmarshall as interface{} so we can check if we have a single item or an array of items
	var rawField interface{}
	err := json.Unmarshal(data, &rawField)
	if err != nil {
		return errors.Wrapf(err, "Failed to Unmarshal JSON: %s", string(data))
	}
	dec := json.NewDecoder(bytes.NewReader(data))
	dec.DisallowUnknownFields()
	switch v := rawField.(type) {
	case map[string]interface{}:
		elements := make(dataArray, 1)
		*w = tcaServiceMgmtFeesWrapper(elements)
		return dec.Decode(&elements[0])
	case []interface{}:
		elements := make(dataArray, len(v))
		*w = tcaServiceMgmtFeesWrapper(elements)
		return dec.Decode(&elements)
	}
	return nil
}

type tcaServiceParts struct {
	Part     string `json:"Part,omitempty"`
	Quantity string `json:"Qty,omitempty"`
	Cost     string `json:"Cost,omitempty"`
}

type tcsServicePartsWrapper []struct {
	tcaServiceParts
}

func (w *tcsServicePartsWrapper) UnmarshalJSON(data []byte) error {
	// Define a type with the same structure but without the custom unmarshaller
	type dataArray tcsServicePartsWrapper

	// Unmarshall as interface{} so we can check if we have a single item or an array of items
	var rawField interface{}
	err := json.Unmarshal(data, &rawField)
	if err != nil {
		return errors.Wrapf(err, "Failed to Unmarshal JSON: %s", string(data))
	}
	dec := json.NewDecoder(bytes.NewReader(data))
	dec.DisallowUnknownFields()
	switch v := rawField.(type) {
	case map[string]interface{}:
		elements := make(dataArray, 1)
		*w = tcsServicePartsWrapper(elements)
		return dec.Decode(&elements[0])
	case []interface{}:
		elements := make(dataArray, len(v))
		*w = tcsServicePartsWrapper(elements)
		return dec.Decode(&elements)
	}
	return nil
}

type tcaServiceLabor struct {
	Hours string `json:"Hours,omitempty"`
	Rate  string `json:"Rate,omitempty"`
}

type tcaServiceLaborWrapper []struct {
	tcaServiceLabor
}

func (w *tcaServiceLaborWrapper) UnmarshalJSON(data []byte) error {
	// Define a type with the same structure but without the custom unmarshaller
	type dataArray tcaServiceLaborWrapper

	// Unmarshall as interface{} so we can check if we have a single item or an array of items
	var rawField interface{}
	err := json.Unmarshal(data, &rawField)
	if err != nil {
		return errors.Wrapf(err, "Failed to Unmarshal JSON: %s", string(data))
	}
	dec := json.NewDecoder(bytes.NewReader(data))
	dec.DisallowUnknownFields()
	switch v := rawField.(type) {
	case map[string]interface{}:
		elements := make(dataArray, 1)
		*w = tcaServiceLaborWrapper(elements)
		return dec.Decode(&elements[0])
	case []interface{}:
		elements := make(dataArray, len(v))
		*w = tcaServiceLaborWrapper(elements)
		return dec.Decode(&elements)
	}
	return nil
}

type tcaServiceContactEvents struct {
	Contact     string `json:"Contact,omitempty"`
	ContactDate string `json:"Contact Date,omitempty"`
	ContactTime string `json:"Contact Time,omitempty"`
	Event       string `json:"Event,omitempty"`
}

type tcaServiceContactEventsWrapper []struct {
	tcaServiceContactEvents
}

func (w *tcaServiceContactEventsWrapper) UnmarshalJSON(data []byte) error {
	// Define a type with the same structure but without the custom unmarshaller
	type dataArray tcaServiceContactEventsWrapper

	// Unmarshall as interface{} so we can check if we have a single item or an array of items
	var rawField interface{}
	err := json.Unmarshal(data, &rawField)
	if err != nil {
		return errors.Wrapf(err, "Failed to Unmarshal JSON: %s", string(data))
	}
	dec := json.NewDecoder(bytes.NewReader(data))
	dec.DisallowUnknownFields()
	switch v := rawField.(type) {
	case map[string]interface{}:
		elements := make(dataArray, 1)
		*w = tcaServiceContactEventsWrapper(elements)
		return dec.Decode(&elements[0])
	case []interface{}:
		elements := make(dataArray, len(v))
		*w = tcaServiceContactEventsWrapper(elements)
		return dec.Decode(&elements)
	}
	return nil
}

type tcaServiceChanges struct {
	ChangedField string `json:"Changed Field,omitempty"`
	OldValue     string `json:"Old Value,omitempty"`
	ChangedWho   string `json:"Chg Who,omitempty"`
	ChangedDate  string `json:"Chg Date,omitempty"`
}

type tcaServiceChangesWrapper []struct {
	tcaServiceChanges
}

func (w *tcaServiceChangesWrapper) UnmarshalJSON(data []byte) error {
	// Define a type with the same structure but without the custom unmarshaller
	type dataArray tcaServiceChangesWrapper

	// Unmarshall as interface{} so we can check if we have a single item or an array of items
	var rawField interface{}
	err := json.Unmarshal(data, &rawField)
	if err != nil {
		return errors.Wrapf(err, "Failed to Unmarshal JSON: %s", string(data))
	}
	dec := json.NewDecoder(bytes.NewReader(data))
	dec.DisallowUnknownFields()
	switch v := rawField.(type) {
	case map[string]interface{}:
		elements := make(dataArray, 1)
		*w = tcaServiceChangesWrapper(elements)
		return dec.Decode(&elements[0])
	case []interface{}:
		elements := make(dataArray, len(v))
		*w = tcaServiceChangesWrapper(elements)
		return dec.Decode(&elements)
	}
	return nil
}

type tcaServiceSurcharges struct {
	Code   string `json:"Code,omitempty"`
	Amount string `json:"Amount,omitempty"`
}

type tcaServiceSurchargesWrapper []struct {
	tcaServiceSurcharges
}

func (w *tcaServiceSurchargesWrapper) UnmarshalJSON(data []byte) error {
	// Define a type with the same structure but without the custom unmarshaller
	type dataArray tcaServiceSurchargesWrapper

	// Unmarshall as interface{} so we can check if we have a single item or an array of items
	var rawField interface{}
	err := json.Unmarshal(data, &rawField)
	if err != nil {
		return errors.Wrapf(err, "Failed to Unmarshal JSON: %s", string(data))
	}
	dec := json.NewDecoder(bytes.NewReader(data))
	dec.DisallowUnknownFields()
	switch v := rawField.(type) {
	case map[string]interface{}:
		elements := make(dataArray, 1)
		*w = tcaServiceSurchargesWrapper(elements)
		return dec.Decode(&elements[0])
	case []interface{}:
		elements := make(dataArray, len(v))
		*w = tcaServiceSurchargesWrapper(elements)
		return dec.Decode(&elements)
	}
	return nil
}

// TcaServiceCustomerTransfer represents a instance of transfer of a contract between an old and new customer.
type TcaServiceCustomerTransfer struct {
	PreviousCustomer string `json:"Previous,omitempty"`
	TransferDate     string `json:"Date,omitempty"`
}

type tcaServiceCustomerTransferWrapper []struct {
	TcaServiceCustomerTransfer
}

func (w *tcaServiceCustomerTransferWrapper) UnmarshalJSON(data []byte) error {
	// Define a type with the same structure but without the custom unmarshaller
	type dataArray tcaServiceCustomerTransferWrapper

	// Unmarshall as interface{} so we can check if we have a single item or an array of items
	var rawField interface{}
	err := json.Unmarshal(data, &rawField)
	if err != nil {
		return errors.Wrapf(err, "Failed to Unmarshal Customer Transfer JSON: %s", string(data))
	}
	dec := json.NewDecoder(bytes.NewReader(data))
	dec.DisallowUnknownFields()
	switch v := rawField.(type) {
	case map[string]interface{}:
		elements := make(dataArray, 1)
		*w = tcaServiceCustomerTransferWrapper(elements)
		return dec.Decode(&elements[0])
	case []interface{}:
		elements := make(dataArray, len(v))
		*w = tcaServiceCustomerTransferWrapper(elements)
		return dec.Decode(&elements)
	}
	return nil
}

// Extract will Extract the next Contract from the decoder
func (c *TcaService) Extract(dec *json.Decoder) error {
	*c = TcaService{} // Make sure old values are discarded
	return dec.Decode(c)
}

// ID returns the ID of the current contract
func (c *TcaService) ID() string {
	return c.Contract
}

// product information is stored as a string of length 9 with each character representing a different feature of the product
const (
	contractType           = iota
	highTech               // "Y"
	antilockBrakes         // "-" Ignored now
	sealsAndGaskets        // "Y"
	disappearingDeductible // "Y"
	commercialUse          // "1" - Commercial Use 1 ton, "2" - Commercial Use 3/4 ton, "N" - Not commercial use (can also be blank)
	factoryWarranty        // "Y" (implies that inspection is not required)
	standardPlus           // "Y"
	smartTech              // "Y"
)

// ShouldMigrate determines if the contract should be migrated.  If it should not be migrated, then a reason that it will not be migrated is provided
func (c *TcaService) ShouldMigrate() (MigrationAllowed, error) {
	if len(c.Claims) == 0 {
		return MigrationAllowed{allowed: false, reason: fmt.Sprintf("Contract [%s] has no claims to migrate", c.ID())}, nil
	}

	if len(c.Options) != 9 {
		// Add extra spaces at the end if necessary
		c.Options = fmt.Sprintf("%-9v", c.Options)
	}

	if c.Options[contractType] == 'P' { // Powerstroke (Legacy)
		// Dava requested that we not migrate these because the last one expired in 2017 and she did not want to set up a product for it in Connect
		return MigrationAllowed{allowed: false, reason: fmt.Sprintf("contract [%s] is for the 'Powerstroke (Legacy)' contract type", c.Contract)}, nil
	}
	if c.Options[contractType] == 'X' { // Unknown
		// These only exist on really old contracts from SC.DELETES that will likely never be migrated
		return MigrationAllowed{allowed: false, reason: fmt.Sprintf("contract [%s] is for an unknown contract type", c.Contract)}, nil
	}

	terms := strings.Split(c.Terms, "/")
	if len(terms) == 0 {
		return MigrationAllowed{}, errors.Errorf("error parsing terms [%s]", c.Terms)
	}
	termMonths, err := intFromString(terms[0])
	if err != nil {
		return MigrationAllowed{}, errors.Wrapf(err, "error parsing term [%s]", terms[0])
	}

	return shouldMigrate(c.Contract, c.EffectiveDate, c.ExpirationDate, termMonths)
}

// ClaimsTable returns the name of the table that contains the claims and the name of the Contract number column
func (c *TcaService) ClaimsTable() (string, string) {
	return AutomotiveClaimTable, AutomotiveClaimTableContractColumn
}

// ProductCode returns the code of the product that is being migrated.
func (c *TcaService) ProductCode() string {
	return db.ProductCodeService
}

// Transform transforms the TCA Service contract data into the appropriate Claim records.
func (c *TcaService) Transform(migUser db.User, caches migrationCaches) error {
	tx, err := db.Get().Beginx()
	if err != nil {
		return errors.Wrap(err, "error starting transaction to save claims")
	}

	nameParts := strings.Split(c.Name, ",")
	firstName := nameParts[0]
	lastName := " "

	if len(nameParts) > 1 {
		lastName = nameParts[0]
		firstName = nameParts[1]
	}

	customer := customer{
		FirstName:      firstName,
		LastName:       lastName,
		EmailAddress:   c.Email,
		PhoneNumber:    c.HomePhone,
		AltPhoneNumber: "",
		StreetAddress:  c.Address,
		City:           c.City,
		State:          c.State,
		PostalCode:     c.Zip,
	}

	for _, claim := range c.Claims {
		exists := claim.exists(caches.AutomotiveClaims)

		if exists {
			continue
		}

		claim.Source = "SB"

		customerID, err := getCustomerID(tx, caches.Customers, c.Contract, customer)
		if err != nil {
			tx.Rollback()
			return errors.Wrapf(err, "error getting customerID for contract [%s]", c.Contract)
		}

		claim.CustomerID = customerID

		if claim.CreatedByUserID == 0 {
			claim.CreatedByUserID = migUser.ID
		}

		if claim.OwnerID == 0 {
			claim.OwnerID = migUser.ID
		}

		for _, n := range claim.Notes {
			if n.CreatedByUserID == 0 {
				n.CreatedByUserID = migUser.ID
			}
		}

		err = claim.saveClaim(tx)
		if err != nil {
			tx.Rollback()
			return errors.Wrapf(err, "error trying to create claim [%s] for contract [%s]", claim.SbID, c.ID())
		}
	}

	err = tx.Commit()
	if err != nil {
		return errors.Wrapf(err, "error commiting changes for claim [%s]", c.ID())
	}

	return nil
}
