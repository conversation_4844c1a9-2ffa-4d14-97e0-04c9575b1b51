package sbmigrate

import (
	"bytes"
	"encoding/json"
	"fmt"
	"strings"

	"phizz/db"

	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
)

// TcaKey defines the structure of a TCA Key contract that has been exported from SB
type Tca<PERSON><PERSON> struct {
	Contract       string       `json:"Contract,omitempty"`       // contracts.code
	EffectiveDate  string       `json:"Effdate,omitempty"`        // contracts.effective_date
	Name           string       `json:"Name,omitempty"`           // customers.last_name, customers.first_name
	Address        string       `json:"Address,omitempty"`        // customers.address
	City           string       `json:"City,omitempty"`           // customers.city
	State          string       `json:"St,omitempty"`             // customers.state_code
	Zip            string       `json:"Zip,omitempty"`            // customers.postal_code
	HomePhone      string       `json:"Hphone,omitempty"`         // customers.phone
	BusinessPhone  string       `json:"Bphone,omitempty"`         // customers.phone if HomePhone not provided
	Email          string       `json:"Email,omitempty"`          // customers.email
	Store          string       `json:"Store,omitempty"`          // stores.code
	FIDeal         string       `json:"Fideal,omitempty"`         // ??? sales.dms_number if source is 'F&I'
	Status         string       `json:"Status,omitempty"`         // contracts.status (A=Active, C=Canceled, X=Expired, P=Pending) - verify possible statuses
	Lender         string       `json:"Lender,omitempty"`         // ??? lenders.name (This is not a unique column)
	Events         EventWrapper `json:"Events,omitempty"`         // ???
	Term           string       `json:"Term,omitempty"`           // contracts.plan_duration
	Price          string       `json:"Price,omitempty"`          // contracts.price
	Cost           string       `json:"Cost,omitempty"`           // contracts.plan_cost - Does this include adjustments ?
	VehPrice       string       `json:"Veh Price,omitempty"`      // ??? sales.vehicle_price
	Plan           string       `json:"Plan,omitempty"`           // contracts.plan_code
	Year           string       `json:"Yr,omitempty"`             // vin_records.year
	Make           string       `json:"Make,omitempty"`           // vin_records.make
	Model          string       `json:"Model,omitempty"`          // vin_records.model
	Vin            string       `json:"Vin,omitempty"`            // vin_records.vin
	NUD            string       `json:"N U D,omitempty"`          // ??? sales.is_new
	BeginningMiles string       `json:"Begmiles,omitempty"`       // sales.odometer, contracts.effective_milage
	KeyCount       string       `json:"Keycnt,omitempty"`         // ??? sales.keys_remotes
	TransmitDate   string       `json:"Transmit Date,omitempty"`  // ???
	ExpirationDate string       `json:"Expdate,omitempty"`        // contracts.expiration_date
	BookDate       string       `json:"Bookdate,omitempty"`       // New column needed
	Comment        string       `json:"Comment,omitempty"`        // ???
	RefundDate     string       `json:"Refdate,omitempty"`        // ???
	Reinstatements []string     `json:"Reinstatements,omitempty"` // ???
	Cancellation   struct {
		CancelDate    string `json:"Cancel Date,omitempty"`
		CancelMileage string `json:"Cancel Mileage,omitempty"`
		CancelReason  string `json:"Cancel Reason,omitempty"`
		CancelCode    string `json:"Cancel Code,omitempty"`
		CancelFee     string `json:"Cancel Fee,omitempty"`
		CustAmt       string `json:"Cust Amt,omitempty"`
		StoreAmt      string `json:"Store Amt,omitempty"`
		CheckNbr      string `json:"Check Nbr,omitempty"`
		CancelEntry   string `json:"Cancel Entry,omitempty"`
		RefundAmount  string `json:"Refamt,omitempty"`
		ClaimsPaid    string `json:"Claims Paid,omitempty"`
	} `json:"Cancellation,omitempty"` // skip for now
	CostBreakout struct {
		Core      string `json:"Core,omitempty"`      // contracts.plan_cost
		Admin     string `json:"Admin,omitempty"`     // contract_adjustments rate_bucket_id = 2
		CLP       string `json:"CLP,omitempty"`       // contract_adjustments rate_bucket_id = 3
		RSA       string `json:"RSA,omitempty"`       // contract_adjustments rate_bucket_id = 4
		Marketing string `json:"Marketing,omitempty"` // contract_adjustments rate_bucket_id = 9
		MAO       string `json:"MAO Pack,omitempty"`  // contract_adjustments rate_bucket_id = 5
	} `json:"Cost Breakout,omitempty"`
	FiMgrNbr          string                       `json:"Fi Mgr Nbr,omitempty"`         // ??? users.employee_number via sales.salesperson_id
	FiMgr             string                       `json:"Fi Mgr,omitempty"`             // ??? users.last_name, users.first_name via sales.salesperson_id
	PurchaseLeaseFlag string                       `json:"Plflag,omitempty"`             // ??? sales.payment_type
	CancelCheckDate   string                       `json:"Can Check Date,omitempty"`     // ???
	Broker            string                       `json:"Broker,omitempty"`             // skip
	ProgSource        string                       `json:"Prog Source,omitempty"`        // skip
	Claims            []*AutomotiveClaim           `json:"Claims,omitempty"`             // skip for now
	ClaimsPaidAmount  []string                     `json:"Claims paid amount,omitempty"` // ???
	FormNbr           string                       `json:"Form Nbr,omitempty"`           // ???
	Transfer          tcaKeyTransferWrapper        `json:"Transfer,omitempty"`           // ???
	ManuallyAdded     contractManuallyAddedWrapper `json:"Manually added,omitempty"`
	ValidationErrors  []string                     `json:"Validation Errors,omitempty"`
	OriginalCosts     struct {
		Cost      string `json:"Cost,omitempty"`
		Core      string `json:"Core,omitempty"`
		Admin     string `json:"Admin,omitempty"`
		Marketing string `json:"Marketing,omitempty"`
		Clip      string `json:"Clip,omitempty"`
		RSA       string `json:"RSA,omitempty"`
		Risk      string `json:"Risk,omitempty"`
		Other     string `json:"Other,omitempty"`
		Premium   string `json:"Premium,omitempty"`
		Escrow    string `json:"Escrow,omitempty"`
	}
}

type tcaKeyChanges struct {
	ChangedField string `json:"Changed Field,omitempty"`
	OldValue     string `json:"Old Value,omitempty"`
	ChangedBy    string `json:"Chg Who,omitempty"`
	ChangeDate   string `json:"Chg Date,omitempty"`
}

type tcaKeyChangesWrapper []struct {
	tcaKeyChanges
}

func (w *tcaKeyChangesWrapper) UnmarshalJSON(data []byte) error {
	// Define a type with the same structure but without the custom unmarshaller
	type dataArray tcaKeyChangesWrapper

	// Unmarshall as interface{} so we can check if we have a single item or an array of items
	var rawField interface{}
	err := json.Unmarshal(data, &rawField)
	if err != nil {
		return errors.Wrapf(err, "Failed to Unmarshal Event JSON: %s", string(data))
	}
	dec := json.NewDecoder(bytes.NewReader(data))
	dec.DisallowUnknownFields()
	switch v := rawField.(type) {
	case map[string]interface{}:
		elements := make(dataArray, 1)
		*w = tcaKeyChangesWrapper(elements)
		return dec.Decode(&elements[0])
	case []interface{}:
		elements := make(dataArray, len(v))
		*w = tcaKeyChangesWrapper(elements)
		return dec.Decode(&elements)
	}
	return nil
}

type tcaContactEvents struct {
	Contact     string `json:"Contact,omitempty"`
	ContactDate string `json:"Contact Date,omitempty"`
	ContactTime string `json:"Contact Time,omitempty"`
	Event       string `json:"Event,omitempty"`
}

type tcaContactEventsWrapper []struct {
	tcaContactEvents
}

func (w *tcaContactEventsWrapper) UnmarshalJSON(data []byte) error {
	// Define a type with the same structure but without the custom unmarshaller
	type dataArray tcaContactEventsWrapper

	// Unmarshall as interface{} so we can check if we have a single item or an array of items
	var rawField interface{}
	err := json.Unmarshal(data, &rawField)
	if err != nil {
		return errors.Wrapf(err, "Failed to Unmarshal Event JSON: %s", string(data))
	}
	dec := json.NewDecoder(bytes.NewReader(data))
	dec.DisallowUnknownFields()
	switch v := rawField.(type) {
	case map[string]interface{}:
		elements := make(dataArray, 1)
		*w = tcaContactEventsWrapper(elements)
		return dec.Decode(&elements[0])
	case []interface{}:
		elements := make(dataArray, len(v))
		*w = tcaContactEventsWrapper(elements)
		return dec.Decode(&elements)
	}
	return nil
}

type tcaKeyLabor struct {
	Hours string `json:"Hours,omitempty"`
	Rate  string `json:"Rate,omitempty"`
}

type tcaKeyLaborWrapper []struct {
	tcaKeyLabor
}

func (w *tcaKeyLaborWrapper) UnmarshalJSON(data []byte) error {
	// Define a type with the same structure but without the custom unmarshaller
	type dataArray tcaKeyLaborWrapper

	// Unmarshall as interface{} so we can check if we have a single item or an array of items
	var rawField interface{}
	err := json.Unmarshal(data, &rawField)
	if err != nil {
		return errors.Wrapf(err, "Failed to Unmarshal Event JSON: %s", string(data))
	}
	dec := json.NewDecoder(bytes.NewReader(data))
	dec.DisallowUnknownFields()
	switch v := rawField.(type) {
	case map[string]interface{}:
		elements := make(dataArray, 1)
		*w = tcaKeyLaborWrapper(elements)
		return dec.Decode(&elements[0])
	case []interface{}:
		elements := make(dataArray, len(v))
		*w = tcaKeyLaborWrapper(elements)
		return dec.Decode(&elements)
	}
	return nil
}

type tcaKeyTransfer struct {
	TransferFromCustomer string `json:"Transfer from customer,omitempty"`
	TransferDate         string `json:"Transfer date,omitempty"`
}

type tcaKeyTransferWrapper []struct {
	tcaKeyTransfer
}

func (w *tcaKeyTransferWrapper) UnmarshalJSON(data []byte) error {
	// Define a type with the same structure but without the custom unmarshaller
	type dataArray tcaKeyTransferWrapper

	// Unmarshall as interface{} so we can check if we have a single item or an array of items
	var rawField interface{}
	err := json.Unmarshal(data, &rawField)
	if err != nil {
		return errors.Wrapf(err, "Failed to Unmarshal Event JSON: %s", string(data))
	}
	dec := json.NewDecoder(bytes.NewReader(data))
	dec.DisallowUnknownFields()
	switch v := rawField.(type) {
	case map[string]interface{}:
		elements := make(dataArray, 1)
		*w = tcaKeyTransferWrapper(elements)
		return dec.Decode(&elements[0])
	case []interface{}:
		elements := make(dataArray, len(v))
		*w = tcaKeyTransferWrapper(elements)
		return dec.Decode(&elements)
	}
	return nil
}

// Extract will Extract the next Contract from the decoder
func (c *TcaKey) Extract(dec *json.Decoder) error {
	*c = TcaKey{} // Make sure old values are discarded
	return dec.Decode(c)
}

// ID returns the ID of the current contract
func (c *TcaKey) ID() string {
	return c.Contract
}

// ShouldMigrate determines if the contract should be migrated.  If it should not be migrated, then a reason that it will not be migrated is provided
func (c *TcaKey) ShouldMigrate() (MigrationAllowed, error) {
	if len(c.Claims) == 0 {
		return MigrationAllowed{allowed: false, reason: fmt.Sprintf("Contract [%s] has no claims to migrate", c.ID())}, nil
	}

	termYears, err := intFromString(c.Term)
	if err != nil {
		return MigrationAllowed{}, errors.Wrapf(err, "error parsing term [%s]", c.Term)
	}

	return shouldMigrate(c.Contract, c.EffectiveDate, c.ExpirationDate, termYears*12)
}

// ClaimsTable returns the name of the table that contains the claims and the name of the Contract number column
func (c *TcaKey) ClaimsTable() (string, string) {
	return AutomotiveClaimTable, AutomotiveClaimTableContractColumn
}

// ProductCode returns the code of the product that is being migrated.
func (c *TcaKey) ProductCode() string {
	return db.ProductCodeKeyReplacement
}

// Transform transforms the TCA Maintenance contract data into the appropriate Claim records.
func (c *TcaKey) Transform(migUser db.User, caches migrationCaches) error {
	tx, err := db.Get().Beginx()
	if err != nil {
		return errors.Wrap(err, "error starting transaction to save claims")
	}

	nameParts := strings.Split(c.Name, ",")
	firstName := nameParts[0]
	lastName := " "

	if len(nameParts) > 1 {
		lastName = nameParts[0]
		firstName = nameParts[1]
	}

	customer := customer{
		FirstName:      firstName,
		LastName:       lastName,
		EmailAddress:   c.Email,
		PhoneNumber:    c.HomePhone,
		AltPhoneNumber: c.BusinessPhone,
		StreetAddress:  c.Address,
		City:           c.City,
		State:          c.State,
		PostalCode:     c.Zip,
	}

	for _, claim := range c.Claims {
		exists := claim.exists(caches.AutomotiveClaims)

		if exists {
			continue
		}

		claim.Source = "SB"

		customerID, err := getCustomerID(tx, caches.Customers, c.Contract, customer)
		if err != nil {
			tx.Rollback()
			return errors.Wrapf(err, "error getting customerID for contract [%s]", c.Contract)
		}

		claim.CustomerID = customerID

		if claim.CreatedByUserID == 0 {
			claim.CreatedByUserID = migUser.ID
		}

		if claim.OwnerID == 0 {
			claim.OwnerID = migUser.ID
		}

		for _, n := range claim.Notes {
			if n.CreatedByUserID == 0 {
				n.CreatedByUserID = migUser.ID
			}
		}

		// set claim status based on complaint status or claim.Status#1
		if claim.Status == "ERROR" {
			claim.Status = "Open"
			if len(claim.Complaints) > 0 {
				switch claim.Complaints[0].Status.String {
				case "C":
					claim.Status = "CheckWritten"
				case "R":
					claim.Status = "Returned"
				case "D":
					claim.Status = "Denied"
				case "O":
					claim.Status = "Open"
				}
			} else {
				if len(claim.Payments) > 0 {
					if claim.Payments[0].CheckNumber != "" {
						claim.Status = "CheckWritten"
					} else if claim.Payments[0].Amount.Decimal.GreaterThan(decimal.Zero) {
						claim.Status = "CCPaid"
						claim.Estimate = claim.Payments[0].Amount.Decimal
					} else if claim.StatusNum1 == "O" {
						claim.Status = "Open"
					} else if claim.StatusNum1 == "D" {
						claim.Status = "Denied"
					}
				}
			}
		}

		err = claim.saveClaim(tx)
		if err != nil {
			tx.Rollback()
			return errors.Wrapf(err, "error trying to create claim [%s] for contract [%s]", claim.SbID, c.ID())
		}
	}

	err = tx.Commit()
	if err != nil {
		return errors.Wrapf(err, "error commiting changes for claim [%s]", c.ID())
	}

	return nil
}
