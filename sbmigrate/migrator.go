package sbmigrate

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"time"

	"phizz/db"

	"github.com/pkg/errors"
)

// Migrator is used to migrate contracts from SB to Connect
type Migrator struct {
	dec      *json.Decoder
	contract Transformer
}

// NewMigrator initializes a new Migrator instance
func NewMigrator(stream io.Reader, contract Transformer) Migrator {
	dec := json.NewDecoder(stream)
	// dec.DisallowUnknownFields()
	return Migrator{
		dec:      dec,
		contract: contract,
	}
}

// Run migrates all migratable contracts from the source stream
func (m Migrator) Run(parseOnly bool) error {
	var err error

	created := 0
	skipped := 0
	prevContractID := ""
	start := time.Now()

	migUser, err := getMigrationUser()
	if err != nil {
		return errors.Wrap(err, "error getting migration user")
	}

	defer func() {
		elapsed := time.Since(start)
		log.Printf("Migrated %d new contracts in %v at %.2f contracts/second.\n", created, elapsed, float64(created)/(float64(elapsed)/float64(time.Second)))
		log.Printf("Skipped %d contracts\n", skipped)
	}()

	caches, err := newCache(m.contract.ProductCode())
	if err != nil {
		return errors.Wrap(err, "error loading caches")
	}

	// read open bracket
	_, err = m.dec.Token()
	if err != nil {
		return errors.Wrap(err, "error reading opening bracket")
	}

	for m.dec.More() {
		err = m.contract.Extract(m.dec)
		if err != nil {
			return errors.Wrapf(err, "error decoding contract #%d. Previous contract [%s]", (created + skipped + 1), prevContractID)
		}

		whizContractKey := whizContractCacheKey{
			Code:        m.contract.ID(),
			ProductCode: m.contract.ProductCode(),
		}
		if _, ok := caches.WhizContracts[whizContractKey]; !ok {
			fmt.Printf("Contract [%s] doesn't exist in whiz (i.e. hasn't been migrated probably). Skipping.\n", m.contract.ID())
			skipped++
			continue
		}

		migrate, err := m.contract.ShouldMigrate()
		if err != nil {
			return errors.Wrapf(err, "error determining if contract should be migrated [%s]", m.contract.ID())
		}
		if !migrate.allowed {
			fmt.Printf("Skipping. %s\n", migrate.reason)
			skipped++
			continue
		}

		err = m.contract.Transform(migUser, caches)
		if err != nil {
			return errors.Wrapf(err, "error transforming contract [%s]", m.contract.ID())
		}

		prevContractID = m.contract.ID()
		created++
	}

	return nil
}

func contractExists(table string, contractColumn string, contract string) (bool, error) {
	var exists bool
	err := db.Get().Get(&exists, fmt.Sprintf(`select 1 from %s where %s = $1`, table, contractColumn), contract)
	if err != nil {
		if err == sql.ErrNoRows {
			return false, nil
		}
		return false, errors.Wrap(err, "error checking if contract exists")
	}
	return true, nil
}

func getMigrationUser() (db.User, error) {
	migrateUserEmail := "<EMAIL>"
	userQuery := "select * from users where email = $1"
	migrateUser := db.User{}
	err := db.Get().Unsafe().Get(&migrateUser, userQuery, migrateUserEmail)
	if err != nil {
		return db.User{}, errors.Wrapf(err, "migration user not found [%s]", migrateUserEmail)
	}

	return migrateUser, nil
}
