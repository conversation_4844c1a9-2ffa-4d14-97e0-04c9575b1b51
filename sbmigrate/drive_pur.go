package sbmigrate

import (
	"encoding/json"
	"fmt"
	"strings"

	"phizz/db"

	"github.com/pkg/errors"
)

// DrivePur defines the structure of a DrivePur contract that has been exported from SB
type DrivePur struct {
	Contract       string       `json:"Contract,omitempty"`       // contracts.code
	EffectiveDate  string       `json:"Effective date,omitempty"` // contracts.effective_date
	Name           string       `json:"Name,omitempty"`           // customers.last_name, customers.first_name
	Address        string       `json:"Address,omitempty"`        // customers.address
	City           string       `json:"City,omitempty"`           // customers.city
	State          string       `json:"St,omitempty"`             // customers.state_code
	Zip            string       `json:"Zip,omitempty"`            // customers.postal_code
	HomePhone      string       `json:"Home phone,omitempty"`     // customers.phone
	BusinessPhone  string       `json:"Business phone,omitempty"` // customers.phone if HomePhone not provided
	Email          string       `json:"Email,omitempty"`          // customers.email
	Store          string       `json:"Store,omitempty"`          // stores.code
	FIDeal         string       `json:"FI deal,omitempty"`        // ??? sales.dms_number
	Status         string       `json:"Status,omitempty"`         // contracts.status (A=Active, C=Canceled, X=Expired, P=Pending, N=New) - verify possible statuses
	Lender         string       `json:"Lender,omitempty"`         // ??? lenders.name (This is not a unique column)
	Events         EventWrapper `json:"EVENTS,omitempty"`         // ???
	Term           string       `json:"Term,omitempty"`           // contracts.finance_term / 12
	Price          string       `json:"Price,omitempty"`          // contracts.price
	Cost           string       `json:"Cost,omitempty"`           // contracts.plan_cost - Does this include adjustments ?
	VehPrice       string       `json:"Veh Price,omitempty"`      // ??? sales.vehicle_price
	Plan           string       `json:"Plan,omitempty"`           // ???
	Year           string       `json:"Yr,omitempty"`             // vin_records.year
	Make           string       `json:"Make,omitempty"`           // vin_records.make
	Model          string       `json:"Model,omitempty"`          // vin_records.model
	Vin            string       `json:"Vin,omitempty"`            // vin_records.vin
	NewUsed        string       `json:"New/Used,omitempty"`       // ??? sales.is_new
	BeginningMiles string       `json:"Begining miles,omitempty"` // ??? sales.odometer, contracts.effective_milage
	NSDFlags       struct {
		XferNewToNSD  string `json:"Xfer new to NSD,omitempty"`
		XferCnclToNSD string `json:"Xfer CNCL to NSD,omitempty"`
		XferRinsToNSD string `json:"Xfer REINS to NSD,omitempty"`
	} `json:"NSD Flags,omitempty"` // ???
	ExpirationDate string `json:"Expiration date,omitempty"` // contracts.expiration_date
	BookDate       string `json:"Book date,omitempty"`       // New column needed
	Cancellation   struct {
		RefundDate         string `json:"Refund date,omitempty"`
		RefundAmount       string `json:"Refund amount,omitempty"`
		CancelCheckDate    string `json:"Cancel Check Date,omitempty"`
		CancelDate         string `json:"Cancel Date,omitempty"`
		CancelMileage      string `json:"Cancel Mileage,omitempty"`
		CancelReason       string `json:"Cancel Reason,omitempty"`
		CancelCode         string `json:"Cancel Code,omitempty"`
		CancelFee          string `json:"Cancel Fee,omitempty"`
		CustAmt            string `json:"Cust Amt,omitempty"`
		StoreAmt           string `json:"Store Amt,omitempty"`
		CheckNbr           string `json:"Check Nbr,omitempty"`
		CancelEntry        string `json:"Cancel Entry,omitempty"`
		NsdCnclAmt         string `json:"Nsd Cncl Amt,omitempty"`
		CancellationFactor string `json:"Cancel Factor,omitempty"`
	} `json:"Cancellation,omitempty"` // skip for now
	UnearnedPremium string             `json:"Unearned Premium,omitemtpy"` // ???
	FiMgrNbr        string             `json:"Fi Mgr Nbr,omitempty"`       // ??? users.employee_number via sales.salesperson_id
	FiMgr           string             `json:"Fi Mgr,omitempty"`           // ??? users.last_name, users.first_name via sales.salesperson_id
	ProgSource      string             `json:"Prog Source,omitempty"`      // skip
	InvoiceFlag     string             `json:"Invoice flag,omitempty"`     // skip
	Broker          string             `json:"Broker,omitempty"`           // skip
	EscrowAmount    string             `json:"Escrow Amount,omitempty"`    // ???
	Claims          []*AutomotiveClaim `json:"Claims,omitempty"`           // skip
	FormNbr         string             `json:"Form Nbr,omitempty"`         // ???
	DrivepurFees    struct {
		DrivepurFee       string `json:"Drivepur Fee,omitempty"`
		DrivepurCancelFee string `json:"Drivepur Cancel Fee,omitempty"`
	} `json:"Drivepur Fees,omitempty"` // ???
	AdminFees struct {
		AdminFee       string `json:"Admin Fee,omitempty"`
		AdminCancelFee string `json:"Admin Cancel Fee,omitempty"`
	} `json:"Admin Fees,omitempty"` // ???
	VeroFees struct {
		VeroFee        string `json:"Vero Fee,omitempty"`
		AdminCancelFee string `json:"Admin Cancel Fee,omitempty"`
	} `json:"Vero Fees,omitempty"` // ???
	CustomerTransfer customerTransferWrapper      `json:"Customer Transfer,omitempty"` // ???
	ContactMethod    string                       `json:"Contact Method,omitempty"`    // ???
	ManuallyAdded    contractManuallyAddedWrapper `json:"Manually added,omitempty"`
	ValidationErrors []string                     `json:"Validation Errors,omitempty"`
	OriginalCosts    struct {
		Cost      string `json:"Cost,omitempty"`
		Core      string `json:"Core,omitempty"`
		Admin     string `json:"Admin,omitempty"`
		Marketing string `json:"Marketing,omitempty"`
		Clip      string `json:"Clip,omitempty"`
		RSA       string `json:"RSA,omitempty"`
		Risk      string `json:"Risk,omitempty"`
		Other     string `json:"Other,omitempty"`
		Premium   string `json:"Premium,omitempty"`
		Escrow    string `json:"Escrow,omitempty"`
	}
}

// Extract will Extract the next Contract from the decoder
func (c *DrivePur) Extract(dec *json.Decoder) error {
	*c = DrivePur{} // Make sure old values are discarded
	return dec.Decode(c)
}

// ID returns the ID of the current contract
func (c *DrivePur) ID() string {
	return c.Contract
}

// ShouldMigrate determines if the contract should be migrated.  If it should not be migrated, then a reason that it will not be migrated is provided
func (c *DrivePur) ShouldMigrate() (MigrationAllowed, error) {
	if len(c.Claims) == 0 {
		return MigrationAllowed{allowed: false, reason: fmt.Sprintf("Contract [%s] has no claims to migrate", c.ID())}, nil
	}

	return shouldMigrate(c.Contract, c.EffectiveDate, c.ExpirationDate, TermYearsCentury*12)
}

// ClaimsTable returns the name of the table that contains the claims and the name of the Contract number column
func (c *DrivePur) ClaimsTable() (string, string) {
	return AutomotiveClaimTable, AutomotiveClaimTableContractColumn
}

// ProductCode returns the code of the product that is being migrated.
func (c *DrivePur) ProductCode() string {
	return db.ProductCodeDrivePur
}

// Transform transforms the Century Protection contract data into the appropriate Claim records.
func (c *DrivePur) Transform(migUser db.User, caches migrationCaches) error {
	tx, err := db.Get().Beginx()
	if err != nil {
		return errors.Wrap(err, "error starting transaction to save claims")
	}

	nameParts := strings.Split(c.Name, ",")
	firstName := nameParts[0]
	lastName := " "

	if len(nameParts) > 1 {
		lastName = nameParts[0]
		firstName = nameParts[1]
	}

	customer := customer{
		FirstName:      firstName,
		LastName:       lastName,
		EmailAddress:   c.Email,
		PhoneNumber:    c.HomePhone,
		AltPhoneNumber: c.BusinessPhone,
		StreetAddress:  c.Address,
		City:           c.City,
		State:          c.State,
		PostalCode:     c.Zip,
	}

	for _, claim := range c.Claims {
		exists := claim.exists(caches.AutomotiveClaims)

		if exists || claim.RepairOrderNumber == "DEFAULT" {
			continue
		}

		claim.Source = "SB"

		customerID, err := getCustomerID(tx, caches.Customers, c.Contract, customer)
		if err != nil {
			tx.Rollback()
			return errors.Wrapf(err, "error getting customerID for contract [%s]", c.Contract)
		}

		claim.CustomerID = customerID

		if claim.CreatedByUserID == 0 {
			claim.CreatedByUserID = migUser.ID
		}

		if claim.OwnerID == 0 {
			claim.OwnerID = migUser.ID
		}

		for _, n := range claim.Notes {
			if n.CreatedByUserID == 0 {
				n.CreatedByUserID = migUser.ID
			}
		}

		err = claim.saveClaim(tx)
		if err != nil {
			tx.Rollback()
			return errors.Wrapf(err, "error trying to create claim [%s] for contract [%s]", claim.SbID, c.ID())
		}
	}

	err = tx.Commit()
	if err != nil {
		return errors.Wrapf(err, "error commiting changes for claim [%s]", c.ID())
	}

	return nil
}
