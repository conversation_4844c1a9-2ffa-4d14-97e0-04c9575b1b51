package sbmigrate

import "github.com/pkg/errors"

type migrationCaches struct {
	AutomotiveClaims   automotiveClaimCache
	GapClaims          tcaGapClaimCache
	VehicleTheftClaims vtaClaimCache
	Customers          customerCache
	Banks              bankCache
	WhizContracts      whizContractCache
}

func newCache(productCode string) (migrationCaches, error) {
	var err error
	c := migrationCaches{}

	c.Customers, err = newCustomerCache(productCode)
	if err != nil {
		return migrationCaches{}, errors.Wrapf(err, "error loading customers cache for product code [%s]\n", productCode)
	}

	c.Banks, err = newBankCache()
	if err != nil {
		return migrationCaches{}, errors.Wrap(err, "error loading bank cache")
	}

	c.AutomotiveClaims, err = newAutomotiveClaimCache()
	if err != nil {
		return migrationCaches{}, errors.Wrap(err, "error loading automotive claims cache")
	}

	c.GapClaims, err = newTcaGapClaimCache()
	if err != nil {
		return migrationCaches{}, errors.Wrap(err, "error loading gap claims cache")
	}

	c.VehicleTheftClaims, err = newVtaClaimCache()
	if err != nil {
		return migrationCaches{}, errors.Wrap(err, "error loading vehicle theft claims cache")
	}

	c.WhizContracts, err = newWhizContractCache()
	if err != nil {
		return migrationCaches{}, errors.Wrap(err, "error loading whiz contracts cache")
	}

	return c, nil
}
