package sbmigrate

import (
	"fmt"
	"phizz/db"

	"github.com/pkg/errors"
)

type whizContract struct {
	Code            string `db:"code"`
	OriginalCode    string `db:"original_code"`
	ProductTypeCode string `db:"product_type_code"`
}

const (
	whizContractQuery = `
		select
			code,
			original_code,
			product_type_code
		from contracts
	`
)

func getWhizContracts() ([]whizContract, error) {
	var err error
	var contracts []whizContract

	fmt.Println("Loading Whiz contracts...")
	err = db.GetWhiz().Select(&contracts, whizContractQuery)
	if err != nil {
		return nil, errors.Wrap(err, "error loading whiz contracts")
	}

	fmt.Printf("loaded [%d] whiz contracts\n", len(contracts))

	return contracts, nil
}

type whizContractCacheKey struct {
	Code        string
	ProductCode string
}

type whizContractCache map[whizContractCacheKey]whizContract

func newWhizContractCache() (whizContractCache, error) {
	var err error
	cache := make(whizContractCache)

	contracts, err := getWhizContracts()
	if err != nil {
		return nil, errors.Wrap(err, "error getting whiz contracts")
	}

	for _, c := range contracts {
		key := whizContractCacheKey{
			Code:        c.Code,
			ProductCode: c.ProductTypeCode,
		}
		cache[key] = c

		if c.Code != c.OriginalCode {
			key := whizContractCacheKey{
				Code:        c.OriginalCode,
				ProductCode: c.ProductTypeCode,
			}
			cache[key] = c
		}
	}

	return cache, nil
}
