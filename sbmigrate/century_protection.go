package sbmigrate

import (
	"bytes"
	"encoding/json"
	"fmt"
	"strings"

	"phizz/db"

	"github.com/pkg/errors"
)

// CenturyProtection defines the structure of a Century Protection contract that has been exported from SB
type CenturyProtection struct {
	Contract       string       `json:"Contract,omitempty"`        // contracts.code
	EffectiveDate  string       `json:"Effective date,omitempty"`  // contracts.effective_date
	Name           string       `json:"Name,omitempty"`            // customers.last_name, customers.first_name
	Address        string       `json:"Address,omitempty"`         // customers.address
	City           string       `json:"City,omitempty"`            // customers.city
	State          string       `json:"St,omitempty"`              // customers.state_code
	Zip            string       `json:"Zip,omitempty"`             // customers.postal_code
	HomePhone      string       `json:"Home phone,omitempty"`      // customers.phone
	BusinessPhone  string       `json:"Business phone,omitempty"`  // customers.phone if HomePhone not provided
	Email          string       `json:"Email,omitempty"`           // customers.email
	Store          string       `json:"Store,omitempty"`           // stores.code
	FIDeal         string       `json:"FI deal,omitempty"`         // ??? sales.dms_number if source is 'F&I'
	Status         string       `json:"Status,omitempty"`          // contracts.status (A=Active, C=Canceled, X=Expired, P=Pending) - verify possible statuses
	Lender         string       `json:"Lender,omitempty"`          // ??? lenders.name (This is not a unique column)
	Events         EventWrapper `json:"EVENTS,omitempty"`          // ???
	Price          string       `json:"Price,omitempty"`           // contracts.price
	VehPrice       string       `json:"Veh Price,omitempty"`       // ??? sales.vehicle_price
	Year           string       `json:"Yr,omitempty"`              // vin_records.year
	Make           string       `json:"Make,omitempty"`            // vin_records.make
	Model          string       `json:"Model,omitempty"`           // vin_records.model
	Vin            string       `json:"Vin,omitempty"`             // vin_records.vin
	NewUsed        string       `json:"New/Used,omitempty"`        // ??? sales.is_new
	BeginningMiles string       `json:"Begining miles,omitempty"`  // sales.odometer, contracts.effective_milage
	TransferDate   string       `json:"Transfer Date,omitempty"`   // ???
	ExpirationDate string       `json:"Expiration Date,omitempty"` // contracts.expiration_date
	BookDate       string       `json:"Book date,omitempty"`       // ???
	Cancellation   struct {
		RefundDate         string `json:"Refund date,omitempty"`
		RefundAmount       string `json:"Refund Amount,omitempty"`
		RefComm            string `json:"Refcomm,omitempty"`
		CancelDate         string `json:"Cancel Date,omitempty"`
		CancelMileage      string `json:"Cancel Mileage,omitempty"`
		CancelReason       string `json:"Cancel Reason,omitempty"`
		CancelCode         string `json:"Cancel Code,omitempty"`
		CancelFee          string `json:"Cancel Fee,omitempty"`
		CustAmt            string `json:"Cust Amt,omitempty"`
		StoreAmt           string `json:"Store Amt,omitempty"`
		CheckNbr           string `json:"Check Nbr,omitempty"`
		CancelEntry        string `json:"Cancel Entry,omitempty"`
		CancelCheckDate    string `json:"Cancel Check Date,omitempty"`
		CancellationFactor string `json:"Cancellation Factor,omitempty"`
	} `json:"Cancellation,omitempty"` // Skip for now
	FiMgrNbr          string                 `json:"Fi Mgr Nbr,omitempty"`                  // ??? users.employee_number via sales.salesperson_id
	FiMgr             string                 `json:"Fi Mgr,omitempty"`                      // ??? users.last_name, users.first_name via sales.salesperson_id
	ProgSource        string                 `json:"Prog Source,omitempty"`                 // skip
	Broker            string                 `json:"Broker,omitempty"`                      // skip
	Claims            []*AutomotiveClaim     `json:"Claims,omitempty"`                      // Skip for now
	Forms             []string               `json:"Forms,omitempty"`                       // ???
	ClaimCount        string                 `json:"Claim Count"`                           // ???
	Products          centuryProductsWrapper `json:"Products,omitempty"`                    // (contracts.ProductName)
	CPNU              string                 `json:"CPNU,omitempty"`                        // skip
	Source            string                 `json:"Source,omitempty"`                      // ??? sales.sale_type
	PrintedFlag       string                 `json:"Printed Flag,omitempty"`                // skip
	PurchaseLeaseFlag string                 `json:"Purchase/Lease flag,omitempty"`         // ??? sales.payment_type
	CrcFlag           string                 `json:"Credit Resource Center Flag,omitempty"` // ??? sales.is_crc
	Unlabeled         string                 `json:"Unlabeled,omitempty"`                   // skip
	Comments          []string               `json:"Comments,omitempty"`                    // ???
	LastChange        struct {
		Date string `json:"Date,omitempty"` // ???
		Time string `json:"Time,omitempty"` // ???
	} `json:"Last Change,omitempty"`
	Invoiced     string `json:"Invoiced,omitempty"` // skip
	CostBreakout struct {
		Core  string `json:"Core,omitempty"`  // contracts.plan_cost
		Admin string `json:"Admin,omitempty"` // contract_adjustments rate_bucket_id = 2
		CLP   string `json:"CLIP,omitempty"`  // contract_adjustments rate_bucket_id = 3
		Spiff string `json:"Spiff,omitempty"` // contract_adjustments rate_bucket_id = 8
		MMC   string `json:"MMC,omitempty"`   // contract_adjustments rate_bucket_id = 7
		MAO   string `json:"MAO,omitempty"`   // contract_adjustments rate_bucket_id = 5
	} `json:"Cost Breakout,omitempty"`
	CustomerTransfer customerTransferWrapper      `json:"Customer Transfer,omitempty"` // ???
	ManuallyAdded    contractManuallyAddedWrapper `json:"Manually added,omitempty"`
	ValidationErrors []string                     `json:"Validation Errors,omitempty"`
	OriginalCosts    struct {
		Cost      string `json:"Cost,omitempty"`
		Core      string `json:"Core,omitempty"`
		Admin     string `json:"Admin,omitempty"`
		Marketing string `json:"Marketing,omitempty"`
		Clip      string `json:"Clip,omitempty"`
		RSA       string `json:"RSA,omitempty"`
		Risk      string `json:"Risk,omitempty"`
		Other     string `json:"Other,omitempty"`
		Premium   string `json:"Premium,omitempty"`
		Escrow    string `json:"Escrow,omitempty"`
	}
	Prods string `json:"Prods,omitempty"`
}

type centuryProduct struct {
	Term    string `json:"Term,omitempty"`
	Cost    string `json:"Cost,omitempty"`
	Product string `json:"Product,omitempty"`
}

type centuryProductsWrapper []struct {
	centuryProduct
}

func (w *centuryProductsWrapper) UnmarshalJSON(data []byte) error {
	// Define a type with the same structure but without the custom unmarshaller
	type dataArray centuryProductsWrapper

	// Unmarshall as interface{} so we can check if we have a single item or an array of items
	var rawField interface{}
	err := json.Unmarshal(data, &rawField)
	if err != nil {
		return errors.Wrapf(err, "Failed to Unmarshal JSON: %s", string(data))
	}
	dec := json.NewDecoder(bytes.NewReader(data))
	dec.DisallowUnknownFields()
	switch v := rawField.(type) {
	case map[string]interface{}:
		elements := make(dataArray, 1)
		*w = centuryProductsWrapper(elements)
		return dec.Decode(&elements[0])
	case []interface{}:
		elements := make(dataArray, len(v))
		*w = centuryProductsWrapper(elements)
		return dec.Decode(&elements)
	}
	return nil
}

// Extract will Extract the next Contract from the decoder
func (c *CenturyProtection) Extract(dec *json.Decoder) error {
	*c = CenturyProtection{} // Make sure old values are discarded
	return dec.Decode(c)
}

// ID returns the ID of the current contract
func (c *CenturyProtection) ID() string {
	return c.Contract
}

// Defines constants for Century products
const (
	TermYearsCentury = 5
)

// ShouldMigrate determines if the contract should be migrated.  If it should not be migrated, then a reason that it will not be migrated is provided
func (c *CenturyProtection) ShouldMigrate() (MigrationAllowed, error) {
	if len(c.Claims) == 0 {
		return MigrationAllowed{allowed: false, reason: fmt.Sprintf("Contract [%s] has no claims to migrate", c.ID())}, nil
	}

	return shouldMigrate(c.Contract, c.EffectiveDate, c.ExpirationDate, TermYearsCentury*12)
}

// ClaimsTable returns the name of the table that contains the claims and the name of the Contract number column
func (c *CenturyProtection) ClaimsTable() (string, string) {
	return AutomotiveClaimTable, AutomotiveClaimTableContractColumn
}

// ProductCode returns the code of the product that is being migrated.
func (c *CenturyProtection) ProductCode() string {
	if c.Contract == "" {
		return db.ProductCodeCentury
	}

	// If the contract starts with "PDR" then it is a Paintless Dent Repair product.  Everything else
	// is an appearance package
	productCode := db.ProductCodePaintlessDentRepair
	if c.Contract[0:3] != "PDR" {
		productCode = db.ProductCodeAppearanceProtection
	}

	return productCode
}

// Transform transforms the Century Protection contract data into the appropriate Claim records.
func (c *CenturyProtection) Transform(migUser db.User, caches migrationCaches) error {

	tx, err := db.Get().Beginx()
	if err != nil {
		return errors.Wrap(err, "error starting transaction to save claims")
	}

	nameParts := strings.Split(c.Name, ",")
	firstName := nameParts[0]
	lastName := " "

	if len(nameParts) > 1 {
		lastName = nameParts[0]
		firstName = nameParts[1]
	}

	customer := customer{
		FirstName:      firstName,
		LastName:       lastName,
		EmailAddress:   c.Email,
		PhoneNumber:    c.HomePhone,
		AltPhoneNumber: c.BusinessPhone,
		StreetAddress:  c.Address,
		City:           c.City,
		State:          c.State,
		PostalCode:     c.Zip,
	}

	for _, claim := range c.Claims {
		exists := claim.exists(caches.AutomotiveClaims)

		if exists {
			continue
		}

		claim.Source = "SB"

		customerID, err := getCustomerID(tx, caches.Customers, c.Contract, customer)
		if err != nil {
			tx.Rollback()
			return errors.Wrapf(err, "error getting customerID for contract [%s]", c.Contract)
		}

		claim.CustomerID = customerID

		if claim.CreatedByUserID == 0 {
			claim.CreatedByUserID = migUser.ID
		}

		if claim.OwnerID == 0 {
			claim.OwnerID = migUser.ID
		}

		for _, n := range claim.Notes {
			if n.CreatedByUserID == 0 {
				n.CreatedByUserID = migUser.ID
			}
		}

		err = claim.saveClaim(tx)
		if err != nil {
			tx.Rollback()
			return errors.Wrapf(err, "error trying to create claim [%s] for contract [%s]", claim.SbID, c.ID())
		}
	}

	err = tx.Commit()
	if err != nil {
		return errors.Wrapf(err, "error commiting changes for claim [%s]", c.ID())
	}

	return nil
}
