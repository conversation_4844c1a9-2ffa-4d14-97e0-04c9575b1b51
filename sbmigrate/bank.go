package sbmigrate

import (
	"fmt"

	"phizz/db"

	"github.com/jmoiron/sqlx"
	"github.com/pkg/errors"
)

type bank struct {
	ID             int        `db:"id"`
	Name           string     `db:"bank_account_name"`
	StreetAddress1 string     `db:"bank_address_street1"`
	StreetAddress2 string     `db:"bank_address_street2"`
	City           string     `db:"bank_address_city"`
	State          string     `db:"bank_address_state"`
	Zip            string     `db:"bank_address_zip"`
	VendorID       NullString `db:"bank_vendor_id,omitempty"`
}

type bankCache map[string]int

func newBankCache() (bankCache, error) {
	cache := make(bankCache)
	var banks []bank
	var err error

	fmt.Println("Loading Bank cache...")

	query := `select distinct * from banks`

	err = db.Get().Select(&banks, query)
	if err != nil {
		return nil, errors.Wrap(err, "error loading bank cache")
	}

	for _, b := range banks {
		cache[b.Name] = b.ID
	}

	return cache, nil
}

func getBankID(tx *sqlx.Tx, cache bankCache, b bank) (int, error) {
	var err error

	if value, exists := cache[b.Name]; exists {
		return value, nil
	}

	fmt.Printf("Bank not cound in cache, creating new bank [%s]\n", b.Name)

	err = b.saveBank(tx)
	if err != nil {
		return -1, errors.Wrap(err, "error creating new bank record")
	}

	cache[b.Name] = b.ID

	return b.ID, nil
}

func (b *bank) saveBank(tx *sqlx.Tx) error {
	query := `insert into banks (
		bank_account_name, bank_address_street1, bank_address_street2, bank_address_city, bank_address_state,
		bank_address_zip, bank_vendor_id
	) values (
		:bank_account_name, :bank_address_street1, :bank_address_street2, :bank_address_city, :bank_address_state,
		:bank_address_zip, :bank_vendor_id
	)
	returning *`

	stmt, err := tx.PrepareNamed(query)
	if err != nil {
		return errors.Wrap(err, "error preparing query to create bank")
	}
	defer Close(stmt)
	err = stmt.Get(b, &b)
	if err != nil {
		return errors.Wrapf(err, "error creating bank. Data %+v", b)
	}

	return nil
}
