package sbmigrate

import (
	"fmt"

	"phizz/db"

	"github.com/jmoiron/sqlx"
	"github.com/pkg/errors"
)

type customer struct {
	ID                int    `json:"id" db:"id"`
	FirstName         string `json:"first_name" db:"first_name"`
	LastName          string `json:"last_name" db:"last_name"`
	EmailAddress      string `json:"email_address" db:"email_address,omitempty"`
	PhoneNumber       string `json:"phone_number" db:"phone_number,omitempty"`
	StreetAddress     string `json:"street_address" db:"street_address,omitempty"`
	City              string `json:"city" db:"city,omitempty"`
	State             string `json:"state" db:"state,omitempty"`
	PostalCode        string `json:"postal_code" db:"postal_code,omitempty"`
	AltPhoneNumber    string `json:"alternate_phone_number" db:"alternate_phone_number,omitempty"`
	BestContactMethod string `json:"best_contact_method" db:"best_contact_method,omitempty"`
}

type customerCacheKey struct {
	FirstName      string
	LastName       string
	ContractNumber string
}

type customerCache map[customerCacheKey]int

type customerCacheQueryRecord struct {
	ID             int    `db:"id"`
	FirstName      string `db:"first_name"`
	LastName       string `db:"last_name"`
	ContractNumber string `db:"contract_number"`
}

func newCustomerCache(productCode string) (customerCache, error) {
	cache := make(customerCache)
	var customers []customerCacheQueryRecord
	var err error

	fmt.Println("Loading Customer cache...")

	query := `
		select distinct 
			customers.id, 
			customers.first_name, 
			customers.last_name, 
			claims.contract_number 
		from customers`

	switch productCode {
	case db.ProductCodeService, db.ProductCodeCentury, db.ProductCodeKeyReplacement, db.ProductCodeMaintenance, db.ProductCodeDrivePur:
		query := query + ` 
			join automotive_claims claims on claims.customer_id = customers.id 
			where product_code = $1`
		err = db.Get().Select(&customers, query, productCode)
	case db.ProductCodeGap:
		query := query + `
			join gap_claims claims on claims.customer_id = customers.id`
		err = db.Get().Select(&customers, query)
	case db.ProductCodeTheftRegistration:
		query := query + `
			join vta_claims claims on claims.customer_id = customers.id`
		err = db.Get().Select(&customers, query)
	default:
		return nil, errors.Errorf("error unknown product [%s]", productCode)
	}

	if err != nil {
		return nil, errors.Wrap(err, "error loading customer cache")
	}

	for _, c := range customers {
		key := customerCacheKey{
			FirstName:      c.FirstName,
			LastName:       c.LastName,
			ContractNumber: c.ContractNumber,
		}

		cache[key] = c.ID
	}

	return cache, nil
}

func getCustomerID(tx *sqlx.Tx, cache customerCache, contractNumber string, cust customer) (int, error) {
	var err error

	key := customerCacheKey{
		FirstName:      cust.FirstName,
		LastName:       cust.LastName,
		ContractNumber: contractNumber,
	}

	if value, exists := cache[key]; exists {
		return value, nil
	}

	fmt.Printf("Customer not found in cache, creating new customer [%s %s]\n", cust.FirstName, cust.LastName)

	err = cust.save(tx)
	if err != nil {
		return -1, errors.Wrap(err, "error creating new customer record")
	}

	cache[key] = cust.ID

	return cust.ID, nil
}

func (c *customer) save(tx *sqlx.Tx) error {
	query := `insert into customers (
		first_name, last_name, email_address, phone_number, street_address, city, state, postal_code,
		alternate_phone_number, best_contact_method
	) values (
		:first_name, :last_name, :email_address, :phone_number, :street_address, :city, :state, :postal_code,
		:alternate_phone_number, :best_contact_method
	)
	returning *`

	stmt, err := tx.PrepareNamed(query)
	if err != nil {
		return errors.Wrap(err, "error preparing query to create customer")
	}
	defer Close(stmt)
	err = stmt.Get(c, &c)
	if err != nil {
		return errors.Wrapf(err, "error creating customer. Data %+v", c)
	}

	return nil
}
