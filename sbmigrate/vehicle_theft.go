package sbmigrate

import (
	"encoding/json"
	"log"
	"strings"
	"time"

	"phizz/db"

	"github.com/jmoiron/sqlx"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
)

// Constants for Vehicle theft claims data
const (
	VehicleTheftClaimsTable               = "vta_claims"
	VehicleTheftClaimsTableContractColumn = "contract_number"
)

// VehicleTheft defines the structure of a Vehicle Theft Assistance contract that has been exported from SB
type VehicleTheft struct {
	Contract       string       `json:"Contract,omitempty"`       // contracts.code
	EffectiveDate  string       `json:"Effective date,omitempty"` // contracts.effective_date
	Name           string       `json:"Name,omitempty"`           // customers.last_name, customers.first_name
	Address        string       `json:"Address,omitempty"`        // customers.address
	City           string       `json:"City,omitempty"`           // customers.city
	State          string       `json:"St,omitempty"`             // customers.state_code
	Zip            string       `json:"Zip,omitempty"`            // customers.postal_code
	HomePhone      string       `json:"Home phone,omitempty"`     // customers.phone
	BusinessPhone  string       `json:"Business phone,omitempty"` // customers.phone if HomePhone not provided
	Email          string       `json:"Email,omitempty"`          // customers.email
	Store          string       `json:"Store,omitempty"`          // stores.code
	FIDeal         string       `json:"FI deal,omitempty"`        // ??? sales.dms_number if source is 'F&I'
	Status         string       `json:"Status,omitempty"`         // contracts.status (A=Active, C=Canceled, X=Expired, P=Pending) - verify possible statuses
	Lender         string       `json:"Lender,omitempty"`         // ??? lenders.name (This is not a unique column)
	Events         EventWrapper `json:"EVENTS,omitempty"`         // ???
	Term           string       `json:"Terms,omitempty"`          // contracts.plan_duration
	Price          string       `json:"Price,omitempty"`          // contracts.price
	Cost           string       `json:"Cost,omitempty"`           // contracts.plan_cost
	VehPrice       string       `json:"Veh Price,omitempty"`      // ??? sales.vehicle_price
	Plan           string       `json:"Plan,omitempty"`           // contracts.plan_code
	Year           string       `json:"Yr,omitempty"`             // vin_records.year
	Make           string       `json:"Make,omitempty"`           // vin_records.make
	Model          string       `json:"Model,omitempty"`          // vin_records.model
	Vin            string       `json:"Vin,omitempty"`            // vin_records.vin
	NewUsed        string       `json:"New/Used,omitempty"`       // ??? sales.is_new
	BeginningMiles string       `json:"Begining miles,omitempty"` // sales.odometer, contracts.effective_milage
	AFCUFlag       string       `json:"AFCU flag,omitempty"`      // ???
	TransferredTo  string       `json:"Transferred to,omitemtpy"` // ???
	BookDate       string       `json:"Book date,omitempty"`      // New column needed
	Cancellation   struct {
		RefundDate      string `json:"Refund date,omitempty"`
		RefundAmount    string `json:"Refund Amount,omitempty"`
		CancelDate      string `json:"Cancel Date,omitempty"`
		CancelReason    string `json:"Cancel Reason,omitempty"`
		CancelCode      string `json:"Cancel Code,omitempty"`
		CancelFee       string `json:"Cancel Fee,omitempty"`
		CustAmt         string `json:"Cust Amt,omitempty"`
		StoreAmt        string `json:"Store Amt,omitempty"`
		CheckNbr        string `json:"Check Nbr,omitempty"`
		CancelEntry     string `json:"Cancel Entry,omitempty"`
		CancelCheckDate string `json:"Cancel Check Date,omitempty"`
	} `json:"Cancellation,omitempty"` // skip for now
	LastChange struct {
		Date string `json:"Date,omitempty"` // ???
	} `json:"Last Change,omitempty"`
	Comments struct {
		Comment string `json:"Comment,omitempty"` // ???
	}
	UnearnedPremium   string                  `json:"Unearned Premium,omitemtpy"`            // ???
	Claims            []*VtaClaim             `json:"Claims,omitempty"`                      // skip for now
	FiMgrNbr          string                  `json:"Fi Mgr Nbr,omitempty"`                  // ??? users.employee_number via sales.salesperson_id
	FiMgr             string                  `json:"Fi Mgr,omitempty"`                      // ??? users.last_name, users.first_name via sales.salesperson_id
	NoteFlag          string                  `json:"Note Flag,omitempty"`                   // ???
	Broker            string                  `json:"Broker,omitempty"`                      // skip
	PurchaseLeaseFlag string                  `json:"Purchase/Lease flag,omitempty"`         // ??? sales.paytment_type
	CrcFlag           string                  `json:"Credit Resource Center Flag,omitempty"` // ??? sales.is_crc
	MgtFees           mgmtFeesWrapper         `json:"Mgt Fees,omitempty"`                    // ???
	ProgSource        string                  `json:"Prog Source,omitempty"`                 // skip
	FormNbr           string                  `json:"Form Nbr,omitempty"`                    // ???
	CustomerTransfer  customerTransferWrapper `json:"Transfer,omitempty"`                    // ???
	Benefit           string                  `json:"Benefit,omitempty"`                     // contracts.product_variant_name / contract_options
	ExpirationDate    string                  `json:"Expiration date,omitempty"`             // contracts.expiration_date
	CostBreakout      struct {
		Core       string `json:"Core,omitempty"`      // contracts.plan_cost
		Admin      string `json:"Admin,omitempty"`     // contract_adjustments rate_bucket_id = 2
		CLP        string `json:"CLP,omitempty"`       // contract_adjustments rate_bucket_id = 3
		Spiff      string `json:"Spiff,omitempty"`     // contract_adjustments rate_bucket_id = 8
		MAO        string `json:"MAO,omitempty"`       // contract_adjustments rate_bucket_id = 5
		MMC        string `json:"MMC,omitempty"`       // contract_adjustments rate_bucket_id = 7
		IDTheft    string `json:"ID Theft,omitempty"`  // ???: Is there a rate bucket for this?
		ThirdParty string `json:"3rd Party,omitempty"` // ???: Is there a rate bucket for this?
	} `json:"Cost Breakout,omitempty"`
	ManuallyAdded    contractManuallyAddedWrapper `json:"Manually added,omitempty"`
	ValidationErrors []string                     `json:"Validation Errors,omitempty"`
	OriginalCosts    struct {
		Cost      string `json:"Cost,omitempty"`
		Core      string `json:"Core,omitempty"`
		Admin     string `json:"Admin,omitempty"`
		Marketing string `json:"Marketing,omitempty"`
		Clip      string `json:"Clip,omitempty"`
		RSA       string `json:"RSA,omitempty"`
		Risk      string `json:"Risk,omitempty"`
		Other     string `json:"Other,omitempty"`
		Premium   string `json:"Premium,omitempty"`
		Escrow    string `json:"Escrow,omitempty"`
	}
}

// VtaClaim represents a claim on a Vehicle Theft Contract
type VtaClaim struct {
	ID                               int                `db:"id,omitempty" json:",string,omitempty"`
	VIN                              string             `db:"vin,omitempty" json:",omitempty"`
	ContractNumber                   string             `db:"contract_number,omitempty" json:",omitempty"`
	IsPoliceReportAvailable          bool               `db:"is_police_report_available,omitempty" json:",omitempty"`
	PoliceReportAvailableManagerFlag bool               `db:"police_report_available_manager_flag,omitempty" json:",omitempty"`
	HasSettlement                    bool               `db:"has_settlement_check,omitempty" json:",omitempty"`
	SettlementCheckValue             decimal.Decimal    `db:"settlement_check_value,omitempty" json:",omitempty"`
	SettlementCheckManagerFlag       bool               `db:"settlement_check_manager_flag,omitempty" json:",omitempty"`
	HasOriginalFinancing             bool               `db:"has_original_financing,omitempty" json:",omitempty"`
	OriginalFinancingValue           decimal.Decimal    `db:"original_financing_value" json:",omitempty"`
	OriginalFinancingManagerFlag     bool               `db:"original_financing_manager_flag,omitempty" json:",omitempty"`
	HasVtaContract                   bool               `db:"has_vta_contract,omitempty" json:",omitempty"`
	VtaContractManagerFlag           bool               `db:"vta_contract_manager_flag,omitempty" json:",omitempty"`
	HasInsuranceNotRecovered         bool               `db:"has_insurance_not_recovered,omitempty" json:",omitempty"`
	InsuranceNotRecoveredManagerFlag bool               `db:"insurance_not_recovered_manager_flag,omitempty" json:",omitempty"`
	Status                           string             `db:"status,omitempty" json:",omitempty"`
	DeniedReason                     string             `db:"denied_reason,omitempty" json:",omitempty"`
	CreatedByUserID                  int                `db:"created_by_user_id,omitempty" json:",omitempty"`
	DateOfClaimReceived              time.Time          `db:"date_of_claim_received,omitempty" json:",omitempty"`
	DateOfLastIn                     time.Time          `db:"date_of_last_in,omitempty" json:",omitempty"`
	OwnerID                          int                `db:"owner_id,omitempty" json:",omitempty"`
	CustomerID                       int                `db:"customer_id,omitempty" json:",omitempty"`
	VendorID                         NullString         `db:"vendor_id,omitempty" json:",omitempty"`
	HasVendorID                      bool               `db:"has_vendor_id,omitempty" json:",omitempty"`
	VendorIDManagerFlag              bool               `db:"vendor_id_manager_flag,omitempty" json:",omitempty"`
	HasCaseReserve                   bool               `db:"has_case_reserve,omitempty" json:",omitempty"`
	CaseReserveManagerFlag           bool               `db:"case_reserve_manager_flag,omitempty" json:",omitempty"`
	CaseReserve                      decimal.Decimal    `db:"case_reserve,omitempty" json:",omitempty"`
	Payments                         []*VtaClaimPayment `db:"-" json:",omitempty"`
	Notes                            []*VtaClaimNote    `db:"-" json:",omitempty"`
	IsInProgress                     bool               `db:"is_in_progress,omitempty"`
	Source                           string             `db:"source,omitempty" json:",omitempty"`
}

// VtaClaimPayment represents a payment on a claim on a Vehicle Theft Contract
type VtaClaimPayment struct {
	ID                  int             `db:"id" json:",string,omitempty"`
	AuthorizationNumber int             `db:"authorization_number,omitempty" json:",string,omitempty"`
	VtaClaimID          int             `db:"vta_claim_id,omitempty" json:",omitempty"`
	CheckNumber         int             `db:"check_number,omitempty" json:",omitempty"`
	Amount              NullableDecimal `db:"amount,omitempty" json:",omitempty"`
	PaidDate            NullableTime    `db:"paid_date,omitempty" json:",omitempty"`
	BatchKey            NullInt         `db:"batch_key,omitempty" json:",omitempty"`
	BillKey             NullInt         `db:"bill_key,omitempty" json:",omitempty"`
	PaymentKey          NullInt         `db:"payment_key,omitempty" json:",omitempty"`
	BillMemo            string          `db:"bill_memo,omitempty" json:",omitempty"`
	UpdatedAt           NullableTime    `db:"updated_at,omitempty" json:",omitempty"`
	BillNumber          NullString      `db:"bill_number,omitempty" json:",omitempty"`
}

// VtaClaimNote represents a note on a Vehicle Theft claim
type VtaClaimNote struct {
	ID              int       `db:"id,omitempty" json:",omitempty"`
	VtaClaimID      int       `db:"vta_claim_id,omitempty" json:",omitempty"`
	NotesText       string    `db:"notes_text,omitempty" json:",omitempty"`
	CreatedByUserID int       `db:"created_by_user_id" json:",omitempty"`
	CreatedAt       time.Time `db:"created_at,omitempty" json:",omitempty"`
}

// Extract will Extract the next Contract from the decoder
func (c *VehicleTheft) Extract(dec *json.Decoder) error {
	*c = VehicleTheft{} // Make sure old values are discarded
	return dec.Decode(c)
}

// ID returns the ID of the current contract
func (c *VehicleTheft) ID() string {
	return c.Contract
}

// ShouldMigrate determines if the contract should be migrated.  If it should not be migrated, then a reason that it will not be migrated is provided
func (c *VehicleTheft) ShouldMigrate() (MigrationAllowed, error) {
	termYears, err := intFromString(c.Term)
	if err != nil {
		return MigrationAllowed{}, errors.Wrapf(err, "error parsing term [%s]", c.Term)
	}

	return shouldMigrate(c.Contract, c.EffectiveDate, c.ExpirationDate, termYears*12)
}

// ClaimsTable returns the name of the table that contains the claims and the name of the Contract number column
func (c *VehicleTheft) ClaimsTable() (string, string) {
	return VehicleTheftClaimsTable, VehicleTheftClaimsTableContractColumn
}

// ProductCode returns the code of the product that is being migrated.
func (c *VehicleTheft) ProductCode() string {
	return db.ProductCodeTheftRegistration
}

// Transform transforms the Vehicle Theft contract data into the appropriate Claim records.
func (c *VehicleTheft) Transform(migUser db.User, caches migrationCaches) error {
	tx, err := db.Get().Beginx()
	if err != nil {
		return errors.Wrap(err, "error starting transaction to save claims")
	}

	nameParts := strings.Split(c.Name, ",")
	firstName := nameParts[0]
	lastName := " "

	if len(nameParts) > 1 {
		lastName = nameParts[0]
		firstName = nameParts[1]
	}

	customer := customer{
		FirstName:      firstName,
		LastName:       lastName,
		EmailAddress:   c.Email,
		PhoneNumber:    c.HomePhone,
		AltPhoneNumber: c.BusinessPhone,
		StreetAddress:  c.Address,
		City:           c.City,
		State:          c.State,
		PostalCode:     c.Zip,
	}

	for _, claim := range c.Claims {
		exists := claim.exists(caches.VehicleTheftClaims)

		if exists {
			continue
		}

		claim.Source = "SB"

		customerID, err := getCustomerID(tx, caches.Customers, c.Contract, customer)
		if err != nil {
			tx.Rollback()
			return errors.Wrapf(err, "error getting customerID for contract [%s]", c.Contract)
		}

		claim.CustomerID = customerID

		if claim.CreatedByUserID == 0 {
			claim.CreatedByUserID = migUser.ID
		}

		if claim.OwnerID == 0 {
			claim.OwnerID = migUser.ID
		}

		err = claim.saveClaim(tx)
		if err != nil {
			tx.Rollback()
			return errors.Wrapf(err, "error trying to create claim for contract [%s]", c.ID())
		}
	}

	err = tx.Commit()
	if err != nil {
		return errors.Wrapf(err, "error commiting changes for claim [%s]", c.ID())
	}

	return nil
}

type vtaClaimCacheKey struct {
	Contract string `db:"contract_number"`
	Vin      string `db:"vin"`
}

type vtaClaimCache map[vtaClaimCacheKey]bool

func newVtaClaimCache() (vtaClaimCache, error) {
	var err error
	var keys []vtaClaimCacheKey
	cache := make(vtaClaimCache)

	query := `select distinct contract_number, vin from vta_claims`

	err = db.Get().Select(&keys, query)
	if err != nil {
		return nil, errors.Wrap(err, "error loading claims cache")
	}

	log.Printf("Loaded [%d] claim cache keys\n", len(keys))

	for _, k := range keys {
		cache[k] = true
	}

	return cache, nil
}

func (claim *VtaClaim) exists(claims vtaClaimCache) bool {
	key := vtaClaimCacheKey{
		Contract: claim.ContractNumber,
		Vin:      claim.VIN,
	}
	_, ok := claims[key]

	return ok
}

func (claim *VtaClaim) saveClaim(tx *sqlx.Tx) error {
	query := `insert into vta_claims (
		vin, contract_number, is_police_report_available, police_report_available_manager_flag, 
		has_settlement_check, settlement_check_value, settlement_check_manager_flag, 
		has_original_financing, original_financing_value, original_financing_manager_flag, 
		has_vta_contract, vta_contract_manager_flag, has_insurance_not_recovered, 
		insurance_not_recovered_manager_flag, status, denied_reason, created_by_user_id, 
		date_of_claim_received, date_of_last_in, owner_id, customer_id, vendor_id, has_vendor_id, 
		vendor_id_manager_flag, has_case_reserve, case_reserve_manager_flag, case_reserve, source
	) values (
		:vin, :contract_number, :is_police_report_available, :police_report_available_manager_flag,
		:has_settlement_check, :settlement_check_value, :settlement_check_manager_flag,
		:has_original_financing, :original_financing_value, :original_financing_manager_flag,
		:has_vta_contract, :vta_contract_manager_flag, :has_insurance_not_recovered,
		:insurance_not_recovered_manager_flag, :status, :denied_reason, :created_by_user_id,
		:date_of_claim_received, :date_of_last_in, :owner_id, :customer_id, :vendor_id, :has_vendor_id,
		:vendor_id_manager_flag, :has_case_reserve, :case_reserve_manager_flag, :case_reserve, :source
	)
	returning *`

	stmt, err := tx.PrepareNamed(query)
	if err != nil {
		return errors.Wrap(err, "error preparing query to create claim")
	}
	defer Close(stmt)
	err = stmt.Get(claim, &claim)
	if err != nil {
		return errors.Wrapf(err, "error creating claim. Data %+v", claim)
	}

	for _, payment := range claim.Payments {
		payment.VtaClaimID = claim.ID
		err = payment.savePayment(tx)
		if err != nil {
			return errors.Wrap(err, "error creating claim payment")
		}
	}

	for _, note := range claim.Notes {
		note.VtaClaimID = claim.ID

		if note.CreatedByUserID == 0 {
			note.CreatedByUserID = claim.CreatedByUserID
			note.CreatedAt = time.Now()
		}

		err = note.saveNote(tx)
		if err != nil {
			return errors.Wrap(err, "error creating claim note")
		}
	}

	return nil
}

func (payment *VtaClaimPayment) savePayment(tx *sqlx.Tx) error {
	query := `insert into vta_claim_payments (
		authorization_number, vta_claim_id, check_number, amount, paid_date, batch_key, 
		bill_key, payment_key, bill_memo, updated_at, bill_number
	) values (
		:authorization_number, :vta_claim_id, :check_number, :amount, :paid_date, :batch_key,
		:bill_key, :payment_key, :bill_memo, :updated_at, :bill_number
	)
	returning *`

	stmt, err := tx.PrepareNamed(query)
	if err != nil {
		return errors.Wrap(err, "error preparing query to create claim payment")
	}
	defer Close(stmt)
	err = stmt.Get(payment, &payment)
	if err != nil {
		return errors.Wrapf(err, "error creating claim payment. Data %+v", payment)
	}

	return nil
}

func (note *VtaClaimNote) saveNote(tx *sqlx.Tx) error {
	query := `insert into vta_record_notes (
		vta_claim_id, notes_text, created_by_user_id, created_at
	) values (
		:vta_claim_id, :notes_text, :created_by_user_id, :created_at
	)
	returning *`

	stmt, err := tx.PrepareNamed(query)
	if err != nil {
		return errors.Wrap(err, "error preparing query to create claim note")
	}
	defer Close(stmt)
	err = stmt.Get(note, &note)
	if err != nil {
		return errors.Wrapf(err, "error creating claim note. Data %+v", note)
	}

	return nil
}
