package sbmigrate

import (
	"bytes"
	"database/sql"
	"encoding/json"
	"strconv"
	"strings"

	"github.com/lib/pq"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
)

// Event represents an event on a contract
type Event struct {
	EventDesc string `json:"Event Desc,omitempty"` // contracts.event or contract_logs.data.event
	EventWho  string `json:"Event Who,omitempty"`  // user_versions.first_name via contracts.updated_by_user_id
	EventDate string `json:"Event Date,omitempty"` // contracts.updated_at or contract_logs.data.updated_at
}

// EventWrapper is a collection of Events
type EventWrapper []struct {
	Event
}

// UnmarshalJSON is custom logic for unmarshalling the events data
func (w *EventWrapper) UnmarshalJSON(data []byte) error {
	// Define a type with the same structure but without the custom unmarshaller
	type dataArray EventWrapper

	// Unmarshall as interface{} so we can check if we have a single item or an array of items
	var rawField interface{}
	err := json.Unmarshal(data, &rawField)
	if err != nil {
		return errors.Wrapf(err, "Failed to Unmarshal Event JSON: %s", string(data))
	}
	dec := json.NewDecoder(bytes.NewReader(data))
	dec.DisallowUnknownFields()
	switch v := rawField.(type) {
	case map[string]interface{}:
		elements := make(dataArray, 1)
		*w = EventWrapper(elements)
		return dec.Decode(&elements[0])
	case []interface{}:
		elements := make(dataArray, len(v))
		*w = EventWrapper(elements)
		return dec.Decode(&elements)
	}
	return nil
}

// MgmtFees represents the Stucture for a Managment Fee on a contract.
type MgmtFees struct {
	MgtFeeCodes      string `json:"Mgt fee Codes,omitempty"`       // ???
	MgtFeeAmts       string `json:"Mgt fee Amts,omitempty"`        // adjustments.amount?
	MgtFeeRefundAmts string `json:"Mgt fee Refund amts,omitempty"` //
}

type mgmtFeesWrapper []struct {
	MgmtFees
}

func (w *mgmtFeesWrapper) UnmarshalJSON(data []byte) error {
	// Define a type with the same structure but without the custom unmarshaller
	type dataArray mgmtFeesWrapper

	// Unmarshall as interface{} so we can check if we have a single item or an array of items
	var rawField interface{}
	err := json.Unmarshal(data, &rawField)
	if err != nil {
		return errors.Wrapf(err, "Failed to Unmarshal JSON: %s", string(data))
	}
	dec := json.NewDecoder(bytes.NewReader(data))
	dec.DisallowUnknownFields()
	switch v := rawField.(type) {
	case map[string]interface{}:
		elements := make(dataArray, 1)
		*w = mgmtFeesWrapper(elements)
		return dec.Decode(&elements[0])
	case []interface{}:
		elements := make(dataArray, len(v))
		*w = mgmtFeesWrapper(elements)
		return dec.Decode(&elements)
	}
	return nil
}

type customerTransfer struct {
	PreviousCustomer string `json:"Previous Customer,omitempty"`
	TransferDate     string `json:"Transfer Date,omitempty"`
}

type customerTransferWrapper []struct {
	customerTransfer
}

func (w *customerTransferWrapper) UnmarshalJSON(data []byte) error {
	// Define a type with the same structure but without the custom unmarshaller
	type dataArray customerTransferWrapper

	// Unmarshall as interface{} so we can check if we have a single item or an array of items
	var rawField interface{}
	err := json.Unmarshal(data, &rawField)
	if err != nil {
		return errors.Wrapf(err, "Failed to Unmarshal Customer Transfer JSON: %s", string(data))
	}
	dec := json.NewDecoder(bytes.NewReader(data))
	dec.DisallowUnknownFields()
	switch v := rawField.(type) {
	case map[string]interface{}:
		elements := make(dataArray, 1)
		*w = customerTransferWrapper(elements)
		return dec.Decode(&elements[0])
	case []interface{}:
		elements := make(dataArray, len(v))
		*w = customerTransferWrapper(elements)
		return dec.Decode(&elements)
	}
	return nil
}

// ContractManuallyAddedStructItem describes a manually added adjustment
type ContractManuallyAddedStructItem struct {
	Code   string `json:"Code,omitempty"`
	Amount string `json:"Amount,omitempty"`
}

type contractManuallyAddedWrapper []struct {
	ContractManuallyAddedStructItem
}

func (w *contractManuallyAddedWrapper) UnmarshalJSON(data []byte) error {
	// Define a type with the same structure but without the custom unmarshaller
	type dataArray contractManuallyAddedWrapper

	// Unmarshall as interface{} so we can check if we have a single item or an array of items
	var rawField interface{}
	err := json.Unmarshal(data, &rawField)
	if err != nil {
		return errors.Wrapf(err, "Failed to Unmarshal Manually Added JSON: %s", string(data))
	}
	dec := json.NewDecoder(bytes.NewReader(data))
	dec.DisallowUnknownFields()
	switch v := rawField.(type) {
	case map[string]interface{}:
		elements := make(dataArray, 1)
		*w = contractManuallyAddedWrapper(elements)
		return dec.Decode(&elements[0])
	case []interface{}:
		elements := make(dataArray, len(v))
		*w = contractManuallyAddedWrapper(elements)
		return dec.Decode(&elements)
	}
	return nil
}

// NullString wrapper struct for the sql.NullString so can be used on structs that are being populated from both JSON and a database
type NullString struct {
	sql.NullString
}

// UnmarshalJSON logic for unmarshaling JSON data into a NullString (i.e. sql.NullString).
func (s *NullString) UnmarshalJSON(data []byte) error {
	s.String = strings.Trim(string(data), `"`)
	s.Valid = true
	return nil
}

// NullableTime wrapper struct for the pq.NullTime so can be used on structs that are being populated from both JSON and a Database
type NullableTime struct {
	pq.NullTime
}

// UnmarshalJSON logic for unmarshaling JSON data into a NullableTime (i.e. pq.NullTime)
func (t *NullableTime) UnmarshalJSON(data []byte) error {
	var err error
	t.Time, err = dateFromString(strings.Trim(string(data), `"`))
	if err != nil {
		return errors.Wrapf(err, "error reading in [%v] as a Nullable Time value", string(data))
	}
	t.Valid = true

	return nil
}

// NullInt wrapper struct for the sql.NullInt64 so can be used on structs that are being populated from JSON and a database
type NullInt struct {
	sql.NullInt64
}

// UnmarshalJSON logic for unmarshaling JSON data into a NullInt (i.e. sql.Nullint64)
func (i *NullInt) UnmarshalJSON(data []byte) error {
	var err error
	i.Int64, err = strconv.ParseInt(strings.Trim(strings.Trim(string(data), `"`), `.`), 10, 64)
	if err != nil {
		return errors.Wrapf(err, "error reading in [%v] as a NullInt value", string(data))
	}
	i.Valid = true

	return nil
}

// NullBool wrapper struct for the sql.NullBool so can be used on structs that are being populated from JSON and a database
type NullBool struct {
	sql.NullBool
}

// UnmarshalJSON logic for unmarshaling JSON data into a NullBool (i.e. sql.NullBool)
func (b *NullBool) UnmarshalJSON(data []byte) error {
	var err error

	if strings.Trim(string(data), `"`) == "N" {
		b.Bool = false
	} else if strings.Trim(string(data), `"`) == "Y" {
		b.Bool = true
	} else {
		b.Bool, err = strconv.ParseBool(strings.Trim(string(data), `"`))
		if err != nil {
			return errors.Wrapf(err, "error reading in [%v] as a NullBool value", string(data))
		}
	}

	b.Valid = true

	return nil
}

// NullableDecimal wrapper struct for the decimal.NullDecimal so can be used on structs that are being populated from JSON and a database
type NullableDecimal struct {
	decimal.NullDecimal
}

// UnmarshalJSON logic for unmarshaling JSON data into a NullableDecimal (i.e. decimal.NullDecimal)
func (d *NullableDecimal) UnmarshalJSON(data []byte) error {
	var err error

	if strings.Trim(string(data), `"`) == "" {
		d.Valid = false
	} else {
		d.Decimal, err = decimal.NewFromString(strings.Trim(string(data), `"`))
		if err != nil {
			return errors.Wrapf(err, "error reading in [%v] as a NullableDecimal", string(data))
		}
		d.Valid = true
	}

	return nil
}
