package sbmigrate

import (
	"log"
	"time"

	"phizz/db"

	"github.com/jmoiron/sqlx"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
)

// Constants for Automotive claims data
const (
	AutomotiveClaimTable               = "automotive_claims"
	AutomotiveClaimTableContractColumn = "contract_number"
)

// AutomotiveClaim represents a claim on an automotive contract
type AutomotiveClaim struct {
	ID                         int                         `db:"id,omitempty" json:",string,omitempty"`
	VIN                        string                      `db:"vin,omitempty" json:",omitempty"`
	ContractNumber             string                      `db:"contract_number,omitempty" json:",omitempty"`
	Status                     string                      `db:"status,omitempty" json:",omitempty"`
	CustomerID                 int                         `db:"customer_id,omitempty" json:",string,omitempty"`
	RecievedDate               time.Time                   `db:"date_of_claim_received,omitempty" json:",omitempty"`
	Estimate                   decimal.Decimal             `db:"estimate,omitempty" json:",omitempty"`
	CreatedByUserID            int                         `db:"created_by_user_id,omitempty" json:",string,omitempty"`
	OwnerID                    int                         `db:"owner_id,omitempty" json:",string,omitempty"`
	Make                       string                      `db:"make,omitempty" json:",omitempty"`
	Model                      string                      `db:"model,omitempty" json:",omitempty"`
	Year                       string                      `db:"year,omitempty" json:",omitempty"`
	EffectiveDate              NullableTime                `db:"effective_date,omitempty" json:",omitempty"`
	BeginningMiles             int                         `db:"beginning_miles,omitempty" json:",string,omitempty"`
	Deductible                 decimal.Decimal             `db:"deductible,omitempty" json:",omitempty"`
	Maintenance                NullString                  `db:"maintenance,omitempty" json:",omitempty"`
	RepairOrderNumber          string                      `db:"ro,omitempty" json:",omitempty"`
	Mileage                    NullString                  `db:"mileage,omitempty" json:",omitempty"`
	Advisor                    NullString                  `db:"advisor,omitempty" json:",omitempty"`
	LaborRate                  NullableDecimal             `db:"labor_rate,omitempty" json:",omitempty"`
	TaxParts                   NullableDecimal             `db:"tax_parts,omitempty" json:",omitempty"`
	TaxLabor                   NullableDecimal             `db:"tax_labor,omitempty" json:",omitempty"`
	RepairOrderOpenedDate      NullableTime                `db:"ro_opened_date,omitempty" json:",omitempty"`
	RepairOrderMileage         int                         `db:"ro_mileage,omitempty" json:",string,omitempty"`
	ExpirationDate             NullableTime                `db:"expiration_date,omitempty" json:",omitempty"`
	EndingMiles                NullInt                     `db:"ending_miles,omitempty" json:",omitempty"`
	Coverage                   NullString                  `db:"coverage,omitempty" json:",omitempty"`
	Term                       NullString                  `db:"term,omitempty" json:",omitempty"`
	ContractStatus             NullString                  `db:"contract_status,omitempty" json:",omitempty"`
	ReassignedOwnerID          NullInt                     `db:"reassigned_owner_id,omitempty" json:",omitempty"`
	ReassignmentStatus         NullString                  `db:"reassignment_status,omitempty" json:",omitempty"`
	ProductCode                NullString                  `db:"product_code,omitempty" json:",omitempty"`
	TaxAdjustment              decimal.Decimal             `db:"tax_adjustment,omitempty" json:",omitempty"`
	PayType                    NullString                  `db:"pay_type,omitempty" json:",omitempty"`
	FacilityID                 NullInt                     `db:"facility_id,omitempty" json:",omitempty"`
	AutoApproved               NullBool                    `db:"auto_approved,omitempty" json:",omitempty"`
	PreAuthAmount              decimal.Decimal             `db:"pre_auth_amount,omitempty" json:",omitempty"`
	IsReconciled               NullBool                    `db:"is_reconciled,omitempty" json:",omitempty"`
	FacilityName               NullString                  `db:"facility_name,omitempty" json:",omitempty"`
	FacilityAddress            NullString                  `db:"facility_address,omitempty" json:",omitempty"`
	FacilityPostalCode         NullString                  `db:"facility_postal_code,omitempty" json:",omitempty"`
	FacilityCity               NullString                  `db:"facility_city,omitempty" json:",omitempty"`
	FacilityStateCode          NullString                  `db:"facility_state_code,omitempty" json:",omitempty"`
	FacilityPhone              NullString                  `db:"facility_phone,omitempty" json:",omitempty"`
	FacilityFax                NullString                  `db:"facility_fax,omitempty" json:",omitempty"`
	IsRepairOrderCustomerValid string                      `db:"is_ro_customer_valid,omitempty" json:",omitempty"`
	UnidataClaimNumber         int                         `db:"unidata_claim_number,omitempty" json:",string,omitempty"`
	TotalTax                   NullableDecimal             `db:"total_tax,omitempty" json:",omitempty"`
	RequestedTotal             decimal.Decimal             `db:"requested_total,omitempty" json:",omitempty"`
	CanceledReason             string                      `db:"canceled_reason,omitempty" json:",omitempty"`
	SbRecordKey                string                      `db:"sb_record_key,omitempty" json:",omitempty"`
	FacilityCountry            NullString                  `db:"facility_country,omitempty" json:",omitempty"`
	ContractDeductible         decimal.Decimal             `db:"contract_deductible,omitempty" json:",omitempty"`
	ContractStoreID            NullInt                     `db:"contract_store_id,omitempty" json:",omitempty"`
	TotalLabor                 decimal.Decimal             `db:"total_labor,omitempty" json:",omitempty"`
	TotalParts                 decimal.Decimal             `db:"total_parts,omitempty" json:",omitempty"`
	ClaimType                  string                      `db:"claim_type,omitempty" json:",omitempty"`
	CustomerPayeeVendorID      string                      `db:"customer_payee_vendor_id,omitempty" json:",omitempty"`
	SbID                       string                      `db:"-" json:"SB ID,omitempty"`
	Source                     string                      `db:"source,omitempty" json:"omitempty"`
	Complaints                 []*AutomotiveClaimComplaint `db:"-" json:",omitempty"`
	Payee                      *AutomotiveClaimPayee       `db:"-" json:",omitempty"`
	Payments                   []*AutomotiveClaimPayment   `db:"-" json:",omitempty"`
	Notes                      []*AutomotiveClaimNote      `db:"-" json:",omitempty"`
	AutomotiveCoverage         *AutomotiveClaimCoverage    `db:"-" json:",omitempty"`
	StatusNum1                 string                      `db:"-" json:"Status#1,omitempty"`
}

// AutomotiveClaimComplaint represents a complaint on a claim on an automotive contract
type AutomotiveClaimComplaint struct {
	ID                  int                              `db:"id,omitempty" json:",string,omitempty"`
	ComplaintDate       NullableTime                     `db:"complaint_date,omitempty" json:",omitempty"`
	AutomotiveClaimID   int                              `db:"automotive_claim_id,omitempty" json:",omitempty"`
	Complaint           NullString                       `db:"complaint,omitempty" json:",omitempty"`
	Cause               NullString                       `db:"cause,omitempty" json:",omitempty"`
	Correction          NullString                       `db:"correction,omitempty" json:",omitempty"`
	AddLineFlag         NullBool                         `db:"add_line_flag,omitempty" json:",omitempty"`
	GoodwillFlag        NullBool                         `db:"goodwill_flag,omitempty" json:",omitempty"`
	Status              NullString                       `db:"status,omitempty" json:",omitempty"`
	PartsTotal          NullableDecimal                  `db:"parts_total,omitempty" json:",omitempty"`
	LaborTotal          NullableDecimal                  `db:"labor_total,omitempty" json:",omitempty"`
	Towing              NullableDecimal                  `db:"towing,omitempty" json:",omitempty"`
	Rental              NullableDecimal                  `db:"rental,omitempty" json:",omitempty"`
	Sublet              NullableDecimal                  `db:"sublet,omitempty" json:",omitempty"`
	GoodwillDescription NullString                       `db:"goodwill_description,omitempty" json:",omitempty"`
	GoodwillAmount      NullableDecimal                  `db:"goodwill_amount,omitempty" json:",omitempty"`
	RepairCodeID        NullInt                          `db:"repair_code_id,omitempty" json:",omitempty"`
	IsManual            NullBool                         `db:"is_manual,omitempty" json:",omitempty"`
	LineCode            NullString                       `db:"line_code,omitempty" json:",omitempty"`
	UnidataRepairNumber int                              `db:"unidata_repair_number,omitempty" json:",string,omitempty"`
	Technician          string                           `db:"-" json:",omitempty"`
	TechID              NullString                       `db:"tech_id,omitempty" json:",omitempty"`
	RepairCode          string                           `db:"repair_code,omitempty" json:",omitempty"`
	Parts               []*AutomotiveClaimComplaintPart  `db:"-" json:",omitempty"`
	Labor               []*AutomotiveClaimComplaintLabor `db:"-" json:",omitempty"`
}

// AutomotiveClaimComplaintPart represents a part on an automotive claim complaint
type AutomotiveClaimComplaintPart struct {
	ID                         int             `db:"id,omitempty" json:",string,omitempty"`
	AutomotiveClaimComplaintID int             `db:"automotive_claim_complaint_id,omitempty" json:",omitempty"`
	PartNumber                 NullString      `db:"part_number,omitempty" json:",omitempty"`
	Description                NullString      `db:"description,omitempty" json:",omitempty"`
	Quantity                   NullInt         `db:"quantity,omitempty" json:",omitempty"`
	Cost                       NullableDecimal `db:"cost,omitempty" json:",omitempty"`
	Requested                  NullableDecimal `db:"requested,omitempty" json:",omitempty"`
	Approved                   NullableDecimal `db:"approved,omitempty" json:",omitempty"`
	Notes                      NullString      `db:"notes,omitempty" json:",omitempty"`
	Msrp                       NullableDecimal `db:"msrp,omitempty" json:",omitempty"`
}

// AutomotiveClaimComplaintLabor represents labor on an automotive claim complaint
type AutomotiveClaimComplaintLabor struct {
	ID                         int             `db:"id,omitempty" json:",string,omitempty"`
	AutomotiveClaimComplaintID int             `db:"automotive_claim_complaint_id,omitempty" json:",omitempty"`
	LaborDescription           NullString      `db:"labor_description,omitempty" json:",omitempty"`
	Requested                  NullableDecimal `db:"requested,omitempty" json:",omitempty"`
	Hours                      NullableDecimal `db:"hours,omitempty" json:",omitempty"`
	Rate                       NullableDecimal `db:"rate,omitempty" json:",omitempty"`
	Billed                     NullableDecimal `db:"billed,omitempty" json:",omitempty"`
	Approved                   NullableDecimal `db:"approved,omitempty" json:",omitempty"`
	Notes                      NullString      `db:"notes,omitempty" json:",omitempty"`
}

// AutomotiveClaimPayee represents a payee on an automotive claim
type AutomotiveClaimPayee struct {
	ID                 int    `db:"id,omitempty" json:",string,omitempty"`
	AutomotiveClaimID  int    `db:"automotive_claim_id,omitempty" json:",string,omitempty"`
	Name               string `db:"name,omitempty" json:",omitempty"`
	Address            string `db:"address,omitempty" json:",omitempty"`
	City               string `db:"city,omitempty" json:",omitempty"`
	State              string `db:"state,omitempty" json:",omitempty"`
	PostalCode         string `db:"postal_code,omitempty" json:",omitempty"`
	VendorID           string `db:"vendor_id,omitempty" json:",omitempty"`
	UnidataPayeeNumber int    `db:"unidata_payee_number,omitempty" json:",string,omitempty"`
}

// AutomotiveClaimPayment represents a payment on an automotive claim
type AutomotiveClaimPayment struct {
	ID                  int                            `db:"id,omitempty" json:",string,omitempty"`
	AuthorizationNumber int                            `db:"authorization_number,omitempty" json:",string,omitempty"`
	AutomotiveClaimID   int                            `db:"automotive_claim_id,omitempty" json:",string,omitempty"`
	CheckNumber         string                         `db:"check_number,omitempty" json:",omitempty"`
	Amount              NullableDecimal                `db:"amount,omitempty" json:",omitempty"`
	PaidDate            NullableTime                   `db:"paid_date,omitempty" json:",omitempty"`
	BatchKey            NullInt                        `db:"batch_key,omitempty" json:",omitempty"`
	BillKey             NullInt                        `db:"bill_key,omitempty" json:",omitempty"`
	PaymentKey          NullInt                        `db:"payment_key,omitempty" json:",omitempty"`
	BillMemo            string                         `db:"bill_memo,omitempty" json:",omitempty"`
	UpdatedAt           NullableTime                   `db:"updated_at,omitempty" json:",omitempty"`
	IntacctBillNumber   NullString                     `db:"intacct_bill_number,omitempty" json:",omitempty"`
	UnidataBillNumber   NullString                     `db:"unidata_bill_number,omitempty" json:",omitempty"`
	IsComplete          bool                           `db:"is_complete,omitempty" json:",omitempty"`
	RefundClaimNumber   NullInt                        `db:"refund_claim_number,omitempty" json:",omitempty"`
	Checks              []*AutomotiveClaimPaymentCheck `db:"-" json:",omitempty"`
}

// AutomotiveClaimPaymentCheck represents a check on a payment
type AutomotiveClaimPaymentCheck struct {
	ID                        int             `db:"id,omitempty" json:",string,omitempty"`
	AutomotiveClaimPaymentsID int             `db:"automotive_claim_payments_id,omitempty" json:",string,omitempty"`
	CheckAmount               decimal.Decimal `db:"check_amount,omitempty" json:",omitempty"`
	CheckNumber               string          `db:"check_number,omitempty" json:",omitempty"`
	PaidDate                  time.Time       `db:"paid_date,omitempty" json:",omitempty"`
	UpdatedAt                 time.Time       `db:"updated_at,omitempty" json:",omitempty"`
}

// AutomotiveClaimNote represents a note on an Automotive claim
type AutomotiveClaimNote struct {
	ID                int       `db:"id,omitempty" json:",string,omitempty"`
	IsManual          bool      `db:"is_manual,omitempty" json:",omitempty"`
	AutomotiveClaimID int       `db:"automotive_claim_id,omitempty" json:",string,omitempty"`
	NotesText         string    `db:"notes_text,omitempty" json:",omitempty"`
	CreatedByUserID   int       `db:"created_by_user_id,omitempty" json:",string,omitempty"`
	CreatedAt         time.Time `db:"created_at,omitempty" json:",omitempty"`
}

// AutomotiveClaimCoverage represents the coverage for an Automotive Claim
type AutomotiveClaimCoverage struct {
	ID                           int        `db:"id,omitempty" json:",string,omitempty"`
	AutomotiveClaimID            int        `db:"automotive_claim_id,omitempty" json:",string,omitempty"`
	DisappearingDeductible       NullBool   `db:"disappearing_deductible,omitempty" json:",string,omitempty"`
	HighTech                     NullBool   `db:"high_tech,omitempty" json:",string,omitempty"`
	SealsAndGasket               NullBool   `db:"seals_and_gasket,omitempty" json:",string,omitempty"`
	RentalUpgrade                NullBool   `db:"rental_upgrade,omitempty" json:",string,omitempty"`
	CommericalUser               NullInt    `db:"commercial_use,omitempty" json:",string,omitempty"`
	StandardPowerTrainPlusOption NullBool   `db:"standard_powertrain_plus_option,omitempty" json:",string,omitempty"`
	SmartTechOption              NullBool   `db:"smart_tech_option,omitempty" json:",string,omitempty"`
	CanadianVehicle              NullBool   `db:"canadian_vehicle,omitempty" json:",string,omitempty"`
	Paint                        NullBool   `db:"paint,omitempty" json:",string,omitempty"`
	Fabric                       NullBool   `db:"fabric,omitempty" json:",string,omitempty"`
	LeatherOrVinyl               NullBool   `db:"leather_or_vinyl,omitempty" json:",string,omitempty"`
	DentAndDing                  NullBool   `db:"dent_and_ding,omitempty" json:",string,omitempty"`
	KeyCount                     NullInt    `db:"key_count,omitempty" json:",string,omitempty"`
	Plan                         NullString `db:"plan,omitempty" json:",omitempty"`
	Purchased                    NullInt    `db:"purchased,omitempty" json:",string,omitempty"`
	Remaining                    NullInt    `db:"remaining,omitempty" json:",string,omitempty"`
}

type automotiveClaimCacheKey struct {
	Contract    string `db:"contract_number"`
	RONumber    string `db:"ro"`
	ProductCode string `db:"product_code"`
}

type automotiveClaimCache map[automotiveClaimCacheKey]bool

type automotiveClaimCacheQueryRow struct {
	automotiveClaimCacheKey
	ID     int    `db:"id"`
	Source string `db:"source"`
}

func newAutomotiveClaimCache() (automotiveClaimCache, error) {
	var err error
	cache := make(automotiveClaimCache)
	var keys []automotiveClaimCacheKey

	query := `
		select distinct
			contract_number,
			ro,
			product_code
		from automotive_claims
	`
	err = db.Get().Select(&keys, query)
	if err != nil {
		return nil, errors.Wrap(err, "error loading automotive claims cache")
	}

	log.Printf("Loaded [%d] claim cache keys\n", len(keys))

	for _, k := range keys {
		cache[k] = true
	}

	return cache, nil

}
func (claim *AutomotiveClaim) exists(claims automotiveClaimCache) bool {
	key := automotiveClaimCacheKey{
		Contract:    claim.ContractNumber,
		RONumber:    claim.RepairOrderNumber,
		ProductCode: claim.ProductCode.String,
	}

	_, ok := claims[key]

	// If we didn't find an existing and the claim is a service
	// claim, then we want to double check to see if there is
	// a matching claim for a migrated service contract code that
	// would have the "VSC" appendted to the contract number.
	//
	// This scenario would happen if we run another claims migration
	// after we've ran the process to sync the contract codes with
	// the code in whiz.
	if claim.ProductCode.String == "VSC" && !ok {
		key.Contract = key.Contract + "VSC"

		_, ok = claims[key]
	}

	return ok
}

func (claim *AutomotiveClaim) saveClaim(tx *sqlx.Tx) error {
	// All claims being migrated would be ones created in SB and not LCA,
	// so setting the Claim Type to be 'SB' for all migrated automotive claims.
	claim.ClaimType = "SB"
	query := `insert into automotive_claims (
		vin, contract_number, status, customer_id, date_of_claim_received,
		estimate, created_by_user_id, owner_id, make, model, year, effective_date,
		beginning_miles, deductible, maintenance, ro, mileage, advisor, labor_rate,
		tax_parts, tax_labor, ro_opened_date, ro_mileage, expiration_date,
		ending_miles, coverage, term, contract_status, reassigned_owner_id,
		reassignment_status, product_code, tax_adjustment, pay_type, facility_id,
		auto_approved, pre_auth_amount, is_reconciled, facility_name, facility_address,
		facility_postal_code, facility_city, facility_state_code, facility_phone,
		facility_fax, is_ro_customer_valid, unidata_claim_number, total_tax, requested_total,
		canceled_reason, sb_record_key, facility_country, contract_deductible, contract_store_id,
		total_labor, total_parts, claim_type, customer_payee_vendor_id, source
	) values (
		:vin, :contract_number, :status, :customer_id, :date_of_claim_received,
		:estimate, :created_by_user_id, :owner_id, :make, :model, :year, :effective_date,
		:beginning_miles, :deductible, :maintenance, :ro, :mileage, :advisor, :labor_rate,
		:tax_parts, :tax_labor, :ro_opened_date, :ro_mileage, :expiration_date,
		:ending_miles, :coverage, :term, :contract_status, :reassigned_owner_id,
		:reassignment_status, :product_code, :tax_adjustment, :pay_type, :facility_id,
		:auto_approved, :pre_auth_amount, :is_reconciled, :facility_name, :facility_address,
		:facility_postal_code, :facility_city, :facility_state_code, :facility_phone,
		:facility_fax, :is_ro_customer_valid, :unidata_claim_number, :total_tax, :requested_total,
		:canceled_reason, :sb_record_key, :facility_country, :contract_deductible, :contract_store_id,
		:total_labor, :total_parts, :claim_type, :customer_payee_vendor_id, :source
	)
	returning *`

	stmt, err := tx.PrepareNamed(query)
	if err != nil {
		return errors.Wrap(err, "error preparing query to create claim")
	}
	defer Close(stmt)

	err = stmt.Get(claim, &claim)
	if err != nil {
		return errors.Wrapf(err, "error creating claim. Data %+v", claim)
	}

	for i := range claim.Complaints {
		claim.Complaints[i].AutomotiveClaimID = claim.ID
		err = claim.Complaints[i].saveComplaint(tx)
		if err != nil {
			return errors.Wrap(err, "error creating claim complaint")
		}
	}

	if claim.Payee != nil {
		claim.Payee.AutomotiveClaimID = claim.ID
		err = claim.Payee.savePayee(tx)
		if err != nil {
			return errors.Wrap(err, "error creating claim payee")
		}
	}

	for i := range claim.Payments {
		claim.Payments[i].AutomotiveClaimID = claim.ID
		err = claim.Payments[i].savePayment(tx)
		if err != nil {
			return errors.Wrap(err, "error creating claim payment")
		}
	}

	for i := range claim.Notes {
		claim.Notes[i].AutomotiveClaimID = claim.ID
		err = claim.Notes[i].saveNote(tx)
		if err != nil {
			return errors.Wrap(err, "error creating claim note")
		}
	}

	if claim.AutomotiveCoverage != nil {
		claim.AutomotiveCoverage.AutomotiveClaimID = claim.ID
		err = claim.AutomotiveCoverage.saveCoverage(tx)
		if err != nil {
			return errors.Wrap(err, "error creating coverage")
		}
	}

	return nil
}

func (complaint *AutomotiveClaimComplaint) saveComplaint(tx *sqlx.Tx) error {
	query := `insert into automotive_claim_complaints (
		complaint_date, automotive_claim_id, complaint, cause, correction,
		add_line_flag, goodwill_flag, status, parts_total,
		labor_total, towing, rental, sublet, goodwill_description,
		goodwill_amount, repair_code_id, is_manual, line_code, unidata_repair_number,
		tech_id, repair_code
	) values (
		:complaint_date, :automotive_claim_id, :complaint, :cause, :correction,
		:add_line_flag, :goodwill_flag, :status, :parts_total,
		:labor_total, :towing, :rental, :sublet, :goodwill_description,
		:goodwill_amount, :repair_code_id, :is_manual, :line_code, :unidata_repair_number,
		:tech_id, :repair_code
	)
	returning *`

	stmt, err := tx.PrepareNamed(query)
	if err != nil {
		return errors.Wrap(err, "error preparing query to create claim complaint")
	}
	defer Close(stmt)
	err = stmt.Get(complaint, &complaint)
	if err != nil {
		return errors.Wrapf(err, "error creating claim complaint. Data %+v", complaint)
	}

	for i, part := range complaint.Parts {
		complaint.Parts[i].AutomotiveClaimComplaintID = complaint.ID
		err = part.savePart(tx)
		if err != nil {
			return errors.Wrap(err, "error creating complaint part.")
		}
	}

	for i, labor := range complaint.Labor {
		complaint.Labor[i].AutomotiveClaimComplaintID = complaint.ID
		err = labor.saveLabor(tx)
		if err != nil {
			return errors.Wrap(err, "error creating complaint labor.")
		}
	}

	return nil
}

func (part *AutomotiveClaimComplaintPart) savePart(tx *sqlx.Tx) error {
	query := `insert into automotive_claim_complaint_parts (
		automotive_claim_complaint_id, part_number, description, quantity,
		cost, requested, approved, notes, msrp
	) values (
		:automotive_claim_complaint_id, :part_number, :description, :quantity,
		:cost, :requested, :approved, :notes, :msrp
	)
	returning *`

	stmt, err := tx.PrepareNamed(query)
	if err != nil {
		return errors.Wrap(err, "error preparing query to create complaint part")
	}
	defer Close(stmt)
	err = stmt.Get(part, &part)
	if err != nil {
		return errors.Wrapf(err, "error creating complaint part. Data %+v", part)
	}

	return nil
}

func (labor *AutomotiveClaimComplaintLabor) saveLabor(tx *sqlx.Tx) error {
	query := `insert into automotive_claim_complaint_labors (
		automotive_claim_complaint_id, labor_description, requested, hours,
		rate, billed, approved, notes
	) values (
		:automotive_claim_complaint_id, :labor_description, :requested, :hours,
		:rate, :billed, :approved, :notes
	)
	returning *`

	stmt, err := tx.PrepareNamed(query)
	if err != nil {
		return errors.Wrap(err, "error preparing query to create complaint labor")
	}
	defer Close(stmt)
	err = stmt.Get(labor, &labor)
	if err != nil {
		return errors.Wrapf(err, "error creating complaint labor. Data %+v", labor)
	}

	return nil
}

func (payee *AutomotiveClaimPayee) savePayee(tx *sqlx.Tx) error {
	query := `insert into automotive_claim_payees (
		automotive_claim_id, name, address, city, state, postal_code, vendor_id, unidata_payee_number
	) values (
		:automotive_claim_id, :name, :address, :city, :state, :postal_code, :vendor_id, :unidata_payee_number
	)
	returning *`

	stmt, err := tx.PrepareNamed(query)
	if err != nil {
		return errors.Wrap(err, "error preparing query to create complaint payee")
	}
	defer Close(stmt)
	err = stmt.Get(payee, &payee)
	if err != nil {
		return errors.Wrapf(err, "error creating complaint payee. Data %+v", payee)
	}

	return nil
}

func (payment *AutomotiveClaimPayment) savePayment(tx *sqlx.Tx) error {
	payment.IsComplete = true
	query := `insert into automotive_claim_payments (
		automotive_claim_id, authorization_number, check_number, amount, paid_date, batch_key, 
		bill_key, payment_key, bill_memo, updated_at, intacct_bill_number, unidata_bill_number,
		is_complete, refund_claim_number
	) values (
		:automotive_claim_id, :authorization_number, :check_number, :amount, :paid_date, :batch_key, 
		:bill_key, :payment_key, :bill_memo, :updated_at, :intacct_bill_number, :unidata_bill_number,
		:is_complete, :refund_claim_number
	)
	returning *`

	stmt, err := tx.PrepareNamed(query)
	if err != nil {
		return errors.Wrap(err, "error preparing query to create payment")
	}
	defer Close(stmt)
	err = stmt.Get(payment, &payment)
	if err != nil {
		return errors.Wrapf(err, "error creating payment. Data %+v", payment)
	}

	for i, check := range payment.Checks {
		payment.Checks[i].AutomotiveClaimPaymentsID = payment.ID
		err = check.saveCheck(tx)
		if err != nil {
			return errors.Wrap(err, "error creating payment check")
		}
	}

	return nil
}

func (check *AutomotiveClaimPaymentCheck) saveCheck(tx *sqlx.Tx) error {
	query := `insert into automotive_claim_payment_checks (
		automotive_claim_payments_id, check_amount, check_number, paid_date, updated_at
	) values (
		:automotive_claim_payments_id, :check_amount, :check_number, :paid_date, :updated_at
	)
	returning *`

	stmt, err := tx.PrepareNamed(query)
	if err != nil {
		return errors.Wrap(err, "error preparing query to create payment check")
	}
	defer Close(stmt)
	err = stmt.Get(check, &check)
	if err != nil {
		return errors.Wrapf(err, "error creating payment check. Data %+v", check)
	}

	return nil
}

func (note *AutomotiveClaimNote) saveNote(tx *sqlx.Tx) error {
	query := `insert into automotive_record_notes (
		automotive_claim_id, is_manual, notes_text, created_by_user_id, created_at
	) values (
		:automotive_claim_id, :is_manual, :notes_text, :created_by_user_id, :created_at
	)
	returning *`

	stmt, err := tx.PrepareNamed(query)
	if err != nil {
		return errors.Wrap(err, "error preparing query to create note")
	}
	defer Close(stmt)
	err = stmt.Get(note, &note)
	if err != nil {
		return errors.Wrapf(err, "error creating note. Data %+v", note)
	}

	return nil
}

func (coverage *AutomotiveClaimCoverage) saveCoverage(tx *sqlx.Tx) error {
	query := `insert into automotive_claim_coverage (
		automotive_claim_id, disappearing_deductible, high_tech, seals_and_gasket,
		rental_upgrade, commercial_use, standard_powertrain_plus_option,
		smart_tech_option, canadian_vehicle, paint, fabric, leather_or_vinyl,
		dent_and_ding, key_count, plan, purchased, remaining
	) values (
		:automotive_claim_id, :disappearing_deductible, :high_tech, :seals_and_gasket,
		:rental_upgrade, :commercial_use, :standard_powertrain_plus_option,
		:smart_tech_option, :canadian_vehicle, :paint, :fabric, :leather_or_vinyl,
		:dent_and_ding, :key_count, :plan, :purchased, :remaining
	)
	returning *`

	stmt, err := tx.PrepareNamed(query)
	if err != nil {
		return errors.Wrap(err, "error preparing query to create coverage")
	}
	defer Close(stmt)
	err = stmt.Get(coverage, &coverage)
	if err != nil {
		return errors.Wrapf(err, "error creating coverage. Data %+v", coverage)
	}

	return nil
}
