#!/usr/bin/env bash

# Common Functions Script.
#
# This script is to be included in other scripts and not ran alone.

sid=()

#####################################################################
#####################################################################
# terminateScript Function
#
# Ends the script killing any background processes still running.
function terminateScript()
{
    echo -e "${YELLOW_OUTPUT}Script being terminated.${NO_COLOR}"

    if [ ${#sid[@]} -gt 0 ]
    then
        # Kills any specified running background processes.
        kill -9 "${sid[@]}"
    fi

    endScript

    exit 1
}

#####################################################################
#####################################################################
# terminateSignalRecieved Function
#
# Called when Interrup or Kill Signal are received from the console.
function terminateSignalRecieved()
{
    echo ""
    echo -e "${YELLOW_OUTPUT}Signal to stop script recieved.${NO_COLOR}"
    terminateScript
}

#####################################################################
#####################################################################
# endScript Function
#
# Ends the script by outputing the length of time the script ran for
function endScript()
{
    duration=$SECONDS
    echo "Ran for $(($duration/3600)) hours $(($duration%3600/60)) minutes and $(($duration%60)) seconds."
    exit 0
}

#####################################################################
#####################################################################
# waitForBackgroundProcesses Function
#
# Loops over the background processes in the sid list and waits for
# them to complete. If there's one that completes unsuccessfully
# then the script will be terminated. (Any background processes still
# running will also be terminated.)
function waitForBackgroundProcesses()
{
    for p in "${sid[@]}"
    do
        if wait $p; then
            :
        else
            message "There was an error with one of the background processes."
            terminateScript
        fi
    done

    # Clear current list of background processes kicked off
    sid=()
}

#####################################################################
#####################################################################
# message Function
#
# Prints console message in Green output color.
function message()
{
    echo -e "${GREEN_OUTPUT}${1}${NO_COLOR}"
}

#####################################################################
#####################################################################
# messageNoNewLine Function
#
# Prints console message in Green output color without new line character.
function messageNoNewLine()
{
    echo -n -e "${GREEN_OUTPUT}${1}${NO_COLOR}"
}

