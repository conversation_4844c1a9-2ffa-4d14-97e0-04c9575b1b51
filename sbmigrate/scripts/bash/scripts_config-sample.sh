#!/usr/bin/env bash

SECONDS=0

GREEN_OUTPUT='\033[0;32m'
YELLOW_OUTPUT='\033[1;33m'
NO_COLOR='\033[0m'

START_DIR=$PWD
BIN_DIR=$GOPATH/src/bin
PHIZZ_DIR=$GOPATH/src/phizz

SCRIPTS_DIR=$PHIZZ_DIR/sbmigrate/scripts
SQL_DIR=$PHIZZ_DIR/sbmigrate/sql
EXPORTS_DIR=$PHIZZ_DIR/sbmigrate/exports
WHIZ_LOG_FILE=${PHIZZ_LOG_FILE:-~/log}

# Add this to be used with psql commands. When in a windows environment where the
# server is installed on Windows and the psql command is being executed in a LINUX
# shell, the psql command defaults to the UNIX socket to connect, but fails because
# the server isn't running under a LINUX environment. This connection string makes it
# so the neccessary connection parameters can be specified and not have to provide
# the password when running scripts that execute the psql command.
# replace the [user:password] with the username and password to be used to connect.
phizz_db_connection_string="postgresql://[user:password]@localhost:5432/phizz"
db_host='localhost:5432'
db_user='whiz'
db_name='whiz_production'
db_pass=''
shallow_dump_file_name="shallow_dump.sql"
contract_dump_file_name='contracts_dump.sql'
full_dump_file_name='full_dump.sql'
cancel_rules_dump_file_name='cancel_rules.sql'