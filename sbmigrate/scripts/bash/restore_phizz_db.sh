#!/usr/bin/env bash

. ./scripts_config.sh

. ./common.sh

function restoreFromFile()
{
    file=$1

    echo -e "${GREEN_OUTPUT}Restoring file ${YELLOW_OUTPUT}$file${GREEN_OUTPUT}...${NO_COLOR}"

    # if [ ${file: -4} == ".zip" ] | [ ${file: -3} == ".gz" ]; then
    #     gunzip -c $SQL_DIR/$file | psql $phizz_db_connection_string >> $PHIZZ_LOG_FILE &
    # else
        psql $phizz_db_connection_string < $SQL_DIR/$file >> $PHIZZ_LOG_FILE 2>&1
    # fi

    sid+=($!)
    waitForBackgroundProcesses
}

#####################################################################
#####################################################################
# MAIN
#
# 1/22/2019 - Had to update the psql commands to specify a connection string URL because
# had to install Windows instance of PostgreSQL and psql in Linux defaults to using the
# UNIX sockets when host is not specified instead of the TCP localhost. The connection
# string parameter is found in the scripts_config.sh.
function main()
{
    # Default to a full restore which includes restoring contracts
    restore_type=${1:-full}
    cd $PHIZZ_DIR

    echo -e "${GREEN_OUTPUT}Use ${YELLOW_OUTPUT}tail -f ${WHIZ_LOG_FILE}${GREEN_OUTPUT} to monitor results${NO_COLOR}"

    echo -e "${GREEN_OUTPUT}Dropping 'public' schema...${NO_COLOR}"
    psql $phizz_db_connection_string -c "drop schema if exists public cascade;" &> $PHIZZ_LOG_FILE 2>&1
    sid+=($!)
    waitForBackgroundProcesses

    echo -e "${GREEN_OUTPUT}Restoring Database...${NO_COLOR}"

    restoreFromFile $phizz_dump_file_name

    cd $START_DIR

    echo -e "${GREEN_OUTPUT}Completed restoring database!${NO_COLO}"
    endScript
}

# Setup to catch the Interrupt and Kill Signals from the console
# and call the specified function when one of those signals are detected.
trap "terminateSignalRecieved" INT
trap "terminateSignalRecieved" KILL

main "$@"
