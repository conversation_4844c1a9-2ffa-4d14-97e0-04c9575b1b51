#!/usr/bin/env bash

. ./scripts_config.sh

. ./common.sh

#####################################################################
#####################################################################
# runMigration Function
#
# Runs the migration based on selected options.
function runMigration()
{
    # Input parameters:
    # $1 - Migration Type
    # $2 - Input File
    # $3 - Whether or not to run as Parse Only mode.

    echo -e "${GREEN_OUTPUT}Running Migration with type: ${NO_COLOR}${1}${GREEN_OUTPUT} and input ${NO_COLOR}${2}"
    echo -e "${GREEN_OUTPUT}Use ${YELLOW_OUTPUT}tail -f ${WHIZ_LOG_FILE}${GREEN_OUTPUT} to monitor results${NO_COLOR}"

    if [ $RUN_COMPILED -eq 1 ]
    then
        if [ ! -f $BIN_DIR/config.toml ]; then
            message "Copying Config to Binary directory..."
            cp $PHIZZ_DIR/config.toml $BIN_DIR/config.toml
        fi
    fi

    cd $PHIZZ_DIR
    if [ "$3" = "Y" ] || [ "$3" = "y" ]
    then
        echo -e "${YELLOW_OUTPUT}ParseOnly Enabled!${NO_COLOR}"

        if [ $RUN_COMPILED -eq 1 ]
        then
            message "Running compiled binary"
            $BIN_DIR/sbmigrate -type=$1 -input=$EXPORTS_DIR/$2 -parseOnly > $PHIZZ_LOG_FILE &
        else
            go run cmd/sbmigrate/main.go -type=$1 -input=$EXPORTS_DIR/$2 -parseOnly > $PHIZZ_LOG_FILE &
        fi
    else
        if [ $RUN_COMPILED -eq 1 ]
        then
            message "Running compiled binary"
            $BIN_DIR/sbmigrate -type=$1 -input=$EXPORTS_DIR/$2 > $PHIZZ_LOG_FILE &
        else
            go run cmd/sbmigrate/main.go -type=$1 -input=$EXPORTS_DIR/$2 > $PHIZZ_LOG_FILE &
        fi
    fi

    sid+=($!)
    waitForBackgroundProcesses

    cd $START_DIR
    message "Completed running migration!"
}

#####################################################################
#####################################################################
# displayMigrationTypes Function
#
# Displays the available migrations that can be ran.
function displayMigrationTypes()
{
    echo "Migration Types:"
    echo "1. Century Protection"
    echo "2. DrivePur"
    echo "3. TCA GAP"
    echo "4. TCA Key"
    echo "5. TCA Maintenance"
    echo "6. TCA Service"
    echo "7. Vehicle Theft"
}

#####################################################################
#####################################################################
# main Function
#
function main()
{
    displayMigrationTypes
    messageNoNewLine "Enter which migartion you want to run: "
    read -n 1 migration
    echo ""

    messageNoNewLine "Do you want to run as Parse Only? (Y or N): "
    read -n 1 parseOnly
    message ""

    messageNoNewLine "Do you want to run compiled binary? (Y or N): "
    read -n 1 binary
    message ""

    RUN_COMPILED=0

    if [ "$binary" = "Y" ] || [ "$binary" = "y" ]
    then
        RUN_COMPILED=1
    fi

    type=""
    input=""
    case "$migration" in
        1)
            type="century_protection"
            input="CPCON.JSON"
            ;;
        2)
            type="drive_pur"
            input="DPCON.JSON"
            ;;
        3)
            type="tca_gap"
            input="ABGAP.JSON"
            ;;
        4)
            type="tca_key"
            input="KECON.JSON"
            ;;
        5)
            type="tca_maintenance"
            input="MAINTCON.JSON"
            ;;
        6)
            type="tca_service"
            input="SC-NAMES.JSON"
            ;;
        7)
            type="vehicle_theft"
            input="TRCON.JSON"
            ;;
    esac

    runMigration $type $input $parseOnly
    endScript
}

# Setup to catch the Interrupt and Kill Signals from the console
# and call the specified function when one of those signals are detected.
trap "terminateSignalRecieved" INT
trap "terminateSignalRecieved" KILL

main
