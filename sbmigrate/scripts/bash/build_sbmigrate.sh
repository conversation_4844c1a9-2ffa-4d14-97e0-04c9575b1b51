#!/usr/bin/env bash

. ./scripts_config.sh

. ./common.sh

#####################################################################
#####################################################################
# MAIN
#
function main()
{
    cd $PHIZZ_DIR

    message "Building sbmigrate-phizz..."
    rm -f $BIN_DIR/sbmigrate
    GOOS=linux go build -o $BIN_DIR/sbmigrate-phizz cmd/sbmigrate/main.go > $PHIZZ_LOG_FILE &
    sid+=($!)
    waitForBackgroundProcesses

    cd $START_DIR

    message "Completed building sbmigrate-phizz!"
    endScript
}

# Setup to catch the Interrupt and Kill Signals from the console
# and call the specified function when one of those signals are detected.
trap "terminateSignalRecieved" INT
trap "terminateSignalRecieved" KILL

main

