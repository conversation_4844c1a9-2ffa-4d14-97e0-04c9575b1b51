cookiestoreauthkey="43AAFYwYR2S50bBzYdXmrIWrahDf+9mtHCDSxjJmq00="
cookiestoreenckey="5RftjY8nLRN7BbECad4MuH964PXWJji7nt4FI+SXwDk="
httpsredirector=false
rollbartoken="9653d709b3224d758114ad8df2f6a488"
googleanalyticstrackingid="UA-76583603-2"
appenv="development"
s3bucket="tca-app-development"
appurl="http://localhost:3000/"
authsalt="b2415f485938829d5a56991d1ac09346951a1870"

[database]
connectionstring="user=phizz dbname=phizz sslmode=verify-full"
debug=true

[email]
uselog=true
host="email-smtp.us-west-2.amazonaws.com"
port=587
username="replace_with_user"
password="replace_with_pass"

[gapemail]
from="<EMAIL>"

[autoemail]
from="<EMAIL>"
facilitymanager=["<EMAIL>","<EMAIL>"]

[lwtemail]
from="<EMAIL>"

[csrf]
key="sopurtycraigis"
secure=false
maxage=43200

[unidata]
# host="https://stage-restuni.totalcareauto.com"
host="disabled" # for uat 2

[intacct]
log=true
host="https://api.intacct.com/ia/xml/xmlgw.phtml"
senderid = "replace_with_sender_id"
senderpassword = "replace_with_sender_password"
userid = "replace_with_user_id"
password=  "replace with_password"
companyid = "replace_with_company_id"

[intacctGap]
AccountLabel = "replace_with_account_id"
LocationID = 1234
projectid = "replace_with_project_id"
termname = "replace_with_term_name"
bankid="BankID"

[intacctService]
AccountLabel = "replace_with_account_id"
LocationID = 1234
projectid = "replace_with_project_id"
termname = "replace_with_term_name"
bankid="BankID"

[intacctTireWheel]
AccountLabel = "replace_with_account_id"
LocationID = 1234
projectid = "replace_with_project_id"
termname = "replace_with_term_name 30"
bankid="BankID"

[intacctMaintenance]
AccountLabel = "replace_with_account_id"
LocationID = 1234
projectid = "replace_with_project_id"
termname = "replace_with_term_name"
bankid="BankID"

[intacctAPP]
AccountLabel = "replace_with_account_id"
LocationID = 1234
projectid = "replace_with_project_id"
termname = "replace_with_term_name"
bankid="BankID"

[intacctPDR]
AccountLabel = "replace_with_account_id"
LocationID = 1234
projectid = "replace_with_project_id"
termname = "replace_with_term_name"
bankid="BankID"

[intacctKey]
AccountLabel = "replace_with_account_id"
LocationID = 1234
projectid = "replace_with_project_id"
termname = "replace_with_term_name"
bankid="BankID"

[intacctDrivePur]
AccountLabel = "replace_with_account_id"
LocationID = 1234
projectid = "replace_with_project_id"
termname = "replace_with_term_name"
bankid="BankID"

[intacctVta]
AccountLabel = "replace_with_account_id"
LocationID = 1234
projectid = "replace_with_project_id"
termname = "replace_with_term_name"
bankid="BankID"

[intacctLwt]
AccountLabel = "replace_with_account_id"
LocationID = 200
projectid = "replace_with_project_id"
termname = "replace_with_term_name"
bankid="BankID"

[aws]
maxsize = ********

[cdk]
log=true
username="replace_with_user"
password="replace_with_pwd"
host="replace_with_host"

[newrelic]
enabled=true
token="replace_with_license_key"

[ucs]
log=true
host="replace_with_host"
username="replace_with_user"
password="replace_with_pwd"

[autoclaims]
ccthreshold=50.0

[whiz]
log=true
baseurl="http://localhost:4000"
authsalt="abczyx9778xcdkddjd90j"

[s3reverseproxy]
enckey="..."
pathprefix="/api/files/download"
defaultlinktimeoutminutes=5