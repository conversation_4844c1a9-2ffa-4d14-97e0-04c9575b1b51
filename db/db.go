package db

import (
	"log"

	"phizz/conf"

	"github.com/jmoiron/sqlx"
	_ "github.com/lib/pq" // Load pq (postgres driver) only when this db package has been loaded
)

// DB used for database connection handle
var DB *sqlx.DB

// WhizDB used for whiz database connection handle
var WhizDB *sqlx.DB

// Get returns either an already loaded gorm database connection or sets one up
func Get() *sqlx.DB {
	var err error

	if DB == nil || DB.Ping() != nil {
		DB, err = sqlx.Connect("postgres", conf.Get().Database.ConnectionString)
		if err != nil {
			log.Fatal(err)
		}
	}

	return DB
}

// GetWhiz returns either an already loaded gorm whiz database connection or sets one up
func GetWhiz() *sqlx.DB {
	var err error

	if WhizDB == nil || WhizDB.Ping() != nil {
		WhizDB, err = sqlx.Connect("postgres", conf.Get().Whiz.ConnectionString)
		if err != nil {
			log.Fatal(err)
		}
	}

	return WhizDB
}
