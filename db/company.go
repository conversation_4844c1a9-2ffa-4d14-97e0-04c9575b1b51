package db

import (
	"gopkg.in/guregu/null.v3"
	"time"
)

// Company represents a company database record
type Company struct {
	ID               int       `db:"id" json:"id"`
	CreatedAt        time.Time `db:"created_at" json:"created_at"`
	UpdatedAt        time.Time `db:"updated_at" json:"updated_at"`
	Name             string    `db:"name" json:"name"`
	Code             string    `db:"code" json:"code"`
	CompanyGroupID   null.Int  `db:"company_group_id" json:"company_group_id"`
	CompanyGroupName string    `db:"-" json:"company_group_name"`
}
