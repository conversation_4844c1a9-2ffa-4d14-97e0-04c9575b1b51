package db

const (
	// ServiceSalesClosedExtPath Service Sales Extract Closed Header by RO number
	ServiceSalesClosedExtPath = "/pip-extract/servicesalesclosedext/extract"
	// ServiceSalesOpenExtPath Service Sales Extract Open Header
	ServiceSalesOpenExtPath = "/pip-extract/servicesalesopenext/extract"
	// ServiceSalesDetailsClosedExtPath Service Sales Extract Closed Details by RO number
	ServiceSalesDetailsClosedExtPath = "/pip-extract/servicesalesdetailsclosedext/extract"
	// ServiceSalesDetailsOpenExtPath Service Sales Extract Open Details by RO number
	ServiceSalesDetailsOpenExtPath = "/pip-extract/servicesalesdetailsopenext/extract"
	// ServiceSalesPartsClosedExtPath Service Sales Extract Closed Parts by RO number
	ServiceSalesPartsClosedExtPath = "/pip-extract/servicesalespartsclosedext/extract"
	// ServiceSalesPartsOpenExtPath Service Sales Extract Open Parts by RO number
	ServiceSalesPartsOpenExtPath = "/pip-extract/servicesalespartsopenext/extract"
	// CustomerExtractPath Customer Extract Path by customer number
	CustomerExtractPath = "/pip-extract/help-customer/extract"
	// WeOweExtractByDealNumberPath Extract we-owe by deal-number
	WeOweExtractByDealNumberPath = "/pip-extract/we-owe/extract"
	// FISalesOpenPath extract by deal-number
	FISalesOpenPath = "/pip-extract/fisales-open/extract"
	// ServiceSalesClosedStoryPath extract by ro number
	ServiceSalesClosedStoryPath = "/pip-extract/servicesalesstoryclosedext/extract"
)

const (
	// ServiceSalesClosedExtQueryByRO extract by RO
	ServiceSalesClosedExtQueryByRO = "SSC_H_ByRONumber"
	// ServiceSalesOpenExtQueryByRO extract by RO
	ServiceSalesOpenExtQueryByRO = "SSO_H_ByRONumber"
	// ServiceSalesDetailsClosedExtQueryByRO extract by RO
	ServiceSalesDetailsClosedExtQueryByRO = "SSC_Dtl_ByRONumber"
	// ServiceSalesDetailsOpenExtQueryByRO extract by RO
	ServiceSalesDetailsOpenExtQueryByRO = "SSO_Dtl_ByRONumber"
	// ServiceSalesPartsClosedExtQueryByRO extract by RO
	ServiceSalesPartsClosedExtQueryByRO = "SSC_Parts_ByRONumber"
	// ServiceSalesPartsOpenExtQueryByRO extract by RO
	ServiceSalesPartsOpenExtQueryByRO = "SSO_Parts_ByRONumber"
	// CustomerExtractQuery extract by custNO
	CustomerExtractQuery = "Cust_ByCustNo"
	// WeOweExtractByDealNumberQuery extract by deal-number
	WeOweExtractByDealNumberQuery = "WeOwe_DealNo"
	// FISalesOpenQuery extract by deal-number
	FISalesOpenQuery = "FISO_ByItem"
	// ServiceSalesClosedStoryQuery by RO
	ServiceSalesClosedStoryQuery = "SSC_Story_ByRO"
)
