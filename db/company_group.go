package db

import (
	"time"
)

// CompanyGroup represents a row in the company_groups table
type CompanyGroup struct {
	ID                   int       `db:"id" json:"id"`
	Name                 string    `db:"name" json:"name"`
	AutoDeactivationDays int       `db:"auto_deactivation_days" json:"auto_deactivation_days"`
	CreatedByUserID      int       `db:"created_by_user_id" json:"created_by_user_id"`
	UpdatedByUserID      int       `db:"updated_by_user_id" json:"updated_by_user_id"`
	CreatedAt            time.Time `db:"created_at" json:"created_at"`
	UpdatedAt            time.Time `db:"updated_at" json:"updated_at"`
}
