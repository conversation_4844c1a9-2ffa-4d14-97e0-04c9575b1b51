package db

const (
	// GapClaimRecoveryStatusRecoveryInquiry RI
	GapClaimRecoveryStatusRecoveryInquiry = "RI"
	// GapClaimRecoveryStatusInRecovery IR
	GapClaimRecoveryStatusInRecovery = "IR"
	// GapClaimRecoveryStatusRecovered R
	GapClaimRecoveryStatusRecovered = "R"
	// GapClaimRecoveryStatusNoRecovery NR
	GapClaimRecoveryStatusNoRecovery = "NR"
	// GapClaimRecoveryStatusPossibleRecovery PR
	GapClaimRecoveryStatusPossibleRecovery = "PR"
	// GapClaimRecoveryStatusWaitingRecovery WR
	GapClaimRecoveryStatusWaitingRecovery = "WR"
)

const (
	// GapClaimRecoveryNoteAdded GAP Recovery Added
	GapClaimRecoveryNoteAdded = "GAP Recovery Added"
	// GapClaimRecoveryNoteRemoved GAP Recovery Removed
	GapClaimRecoveryNoteRemoved = "GAP Recovery Removed"
)

// RecoveryStatusChange map for GAP Recovery status change
var RecoveryStatusChange = map[string]string{
	GapClaimRecoveryStatusRecoveryInquiry:  "Status Changed to RecoveryInquiry",
	GapClaimRecoveryStatusInRecovery:       "Status Changed to InRecovery",
	GapClaimRecoveryStatusRecovered:        "Status Changed to Recovered",
	GapClaimRecoveryStatusNoRecovery:       "Status Changed to NoRecovery",
	GapClaimRecoveryStatusPossibleRecovery: "Status Changed to PossibleRecovery",
	GapClaimRecoveryStatusWaitingRecovery:  "Status Changed to WaitingRecovery",
}
