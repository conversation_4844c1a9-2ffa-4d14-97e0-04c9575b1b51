package db

import (
	"context"
	"database/sql"
	"log"

	"github.com/jmoiron/sqlx"
	"github.com/pkg/errors"
)

// GetBatchKey return batchkey from intacct_bill_batches by batchTitle
func GetBatchKey(batchTitle string) (int, error) {
	batchKey := 0
	err := Get().Get(&batchKey, `select batch_key from intacct_bill_batches where batch_title = $1`, batchTitle)
	if err != nil {
		if err == sql.ErrNoRows {
			return batchKey, nil
		}
		return batchKey, errors.Wrap(err, "Error loading intacct batch data")
	}
	return batchKey, nil
}

// StoreBatchKey stores batchTitle and batchkey to intacct_bill_batches
func StoreBatchKey(tx *sqlx.Tx, batchTitle string, batchKey int) error {
	batchInsert := `insert into intacct_bill_batches(batch_title, batch_key) values($1,$2) returning id`
	id := 0
	err := tx.Get(&id, batchInsert, batchTitle, batchKey)
	if err != nil {
		return errors.Wrap(err, "Error inserting batchkey in database")
	}
	return nil
}

// TrimIntacctRequestResponseTable deletes old rows from the intacct_request_response_table
func TrimIntacctRequestResponseTable(ctx context.Context) error {
	const maxAgeDays = 90
	log.Println("Running trim-intacct-request-response-table task")

	// Find rows that are older than the maximum age
	log.Printf("Checking for rows that are more than %d days old...\n", maxAgeDays)
	query := `select irr.id
		from intacct_request_response irr
		where irr.logged_at < now() - interval '1 day' * $1
		order by irr.id`

	var ids []int64
	err := Get().SelectContext(ctx, &ids, query, maxAgeDays)
	// err := Get().SelectContext(ctx, &ids, query)
	if err != nil {
		err = errors.Wrap(err, "failed to get old entries from intacct_request_response table")
		return err
	}

	// Delete the older rows in smaller batches
	total := len(ids)
	var batch []int64
	if total > 0 {
		deleteQueryTemplate := `delete from intacct_request_response
			where id in (?)
		`
		const batchSize = 1000
		start := 0
		for start < total {
			// Prepare a small batch to delete all at once so we don't hold a lock
			// on the entire table for a long period of time.
			end := start + batchSize
			if end > total {
				end = total
			}
			batch = ids[start:end]

			log.Printf("Deleting intacct_request_response rows with IDs [%d..%d] (count = %d)\n", start, end, len(batch))
			deleteQuery, params, err := sqlx.In(deleteQueryTemplate, batch)
			if err != nil {
				err = errors.Wrap(err, "failed to define delete batch query")
				return err
			}
			deleteQuery = Get().Rebind(deleteQuery)

			tx, err := Get().BeginTxx(ctx, nil)
			if err != nil {
				err = errors.Wrap(err, "failed to start transaction")
				return err
			}

			_, err = tx.ExecContext(ctx, deleteQuery, params...)
			if err != nil {
				_ = tx.Rollback()
				err = errors.Wrap(err, "failed to delete batch")
				return err
			}

			err = tx.Commit()
			if err != nil {
				err = errors.Wrap(err, "failed to commit transaction")
				return err
			}

			// Advance starting index
			start += batchSize
		}
	}

	log.Printf("%d total rows deleted\n", total)

	return nil
}
