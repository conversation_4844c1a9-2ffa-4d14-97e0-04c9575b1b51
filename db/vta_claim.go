package db

const (
	// VtaClaimStatusInquiry Vta Claim status Inquiry
	VtaClaimStatusInquiry = "I"
	// VtaClaimStatusPending Vta Claim status Pending
	VtaClaimStatusPending = "P"
	// VtaClaimStatusPendingReopened Vta Claim status Pending/Reopened
	VtaClaimStatusPendingReopened = "RO-RP"
	// VtaClaimStatusReadyToProcess Vta Claim Status Ready to Process (paperwork Complete)
	VtaClaimStatusReadyToProcess = "RP"
	// VtaClaimStatusWaitingForAuthorization Vta Claim Status Waiting for Authorization
	VtaClaimStatusWaitingForAuthorization = "WA"
	// VtaClaimStatusWaitingForCheck Vta Claim Status Waiting for Check
	VtaClaimStatusWaitingForCheck = "WC"
	// VtaClaimStatusWaitingForPayment Vta Claim Status Waiting for Payment (CSCI)
	VtaClaimStatusWaitingForPayment = "WP"
	// VtaClaimStatusDeny Vta Claim Status Closed, Deny
	VtaClaimStatusDeny = "D"
	// VtaClaimStatusReturnedForCorrections Vta Claim Status Returned for Corrections
	VtaClaimStatusReturnedForCorrections = "RC"
	// VtaClaimStatusCheckWritten Vta Claim Status Check Written
	VtaClaimStatusCheckWritten = "C"
	// VtaClaimStatusPendingDenial Vta Claim Status Pending Denial
	VtaClaimStatusPendingDenial = "PD"
	// VtaClaimStatusCheckVoided Vta Claim Status Check Voided
	VtaClaimStatusCheckVoided = "CV"
	// VtaClaimStatusNew VtaClaimStatusNew for new claim
	VtaClaimStatusNew = "New"
	// VtaClaimStatusClosedNoResponse Vta Claim Status Closed No Response
	VtaClaimStatusClosedNoResponse = "C-NR"
)

const (
	// VTAFieldIDPoliceReport Field ID for police report
	VTAFieldIDPoliceReport = 1
	// VTAFieldIDSettlementCheck Field ID for settlement amount
	VTAFieldIDSettlementCheck = 2
	// VTAFieldIDOriginalFinancing Field ID for original financing
	VTAFieldIDOriginalFinancing = 3
	// VTAFieldIDVTAContract Field ID for VTA contract
	VTAFieldIDVTAContract = 4
	// VTAFieldIDInsuranceNotRecovered Field ID Insurance not recovered
	VTAFieldIDInsuranceNotRecovered = 5
	// VTAFieldIDStatus Field ID for status
	VTAFieldIDStatus = 6
	// VTAFieldIDDeniedReason Field ID for denied reason
	VTAFieldIDDeniedReason = 7
	// VTAFieldIDAuthorizationNumber Field ID for authorization number
	VTAFieldIDAuthorizationNumber = 8
	// VTAFieldIDCheckNumber Field ID for check number
	VTAFieldIDCheckNumber = 9
	// VTAFieldIDPaidAmount Field ID for paid amount
	VTAFieldIDPaidAmount = 10
	// VTAFieldIDPaidDate Field ID for paid date
	VTAFieldIDPaidDate = 11
	// VTAFieldIDVendorID Field ID for paid date
	VTAFieldIDVendorID = 12
	// VTAFieldIDCaseReserve Field ID for paid date
	VTAFieldIDCaseReserve = 13
)

// VTAFieldRecordNoteDescription map for record note desc
var VTAFieldRecordNoteDescription = map[int]string{
	VTAFieldIDPoliceReport:          "Police Report",
	VTAFieldIDSettlementCheck:       "Settlement Check",
	VTAFieldIDOriginalFinancing:     "Original Financing",
	VTAFieldIDVTAContract:           "VTA Contract",
	VTAFieldIDInsuranceNotRecovered: "Insured not Recovered",
	VTAFieldIDStatus:                "Status",
	VTAFieldIDDeniedReason:          "Denied Reason",
	VTAFieldIDAuthorizationNumber:   "Authorization Number",
	VTAFieldIDCheckNumber:           "Check Number",
	VTAFieldIDPaidAmount:            "Paid Amount",
	VTAFieldIDPaidDate:              "Paid Date",
	VTAFieldIDVendorID:              "Vendor ID",
	VTAFieldIDCaseReserve:           "Case Reserve",
}

// ClaimRequestType holds request type
type ClaimRequestType int

const (
	// ClaimValidateCreate validation check flag for claim request create
	ClaimValidateCreate ClaimRequestType = iota
	// ClaimValidateAuthorize claim authorize
	ClaimValidateAuthorize
	// ClaimValidateDeny claim deny
	ClaimValidateDeny
	// ClaimValidateReturn claim return
	ClaimValidateReturn
	// ClaimValidateSubmit claim submit
	ClaimValidateSubmit
	// ClaimNoValidation claim no validation
	ClaimNoValidation
)

// VTAClaimValidationTypes Map for claim validations on status change. nil if no validation required.
var VTAClaimValidationTypes = map[string]ClaimRequestType{
	VtaClaimStatusPendingDenial:           ClaimValidateSubmit,
	VtaClaimStatusWaitingForAuthorization: ClaimValidateSubmit,
	VtaClaimStatusReturnedForCorrections:  ClaimValidateReturn,
	VtaClaimStatusDeny:                    ClaimValidateDeny,
	VtaClaimStatusInquiry:                 ClaimNoValidation,
	VtaClaimStatusPending:                 ClaimNoValidation,
	VtaClaimStatusPendingReopened:         ClaimNoValidation,
	VtaClaimStatusReadyToProcess:          ClaimNoValidation,
	VtaClaimStatusWaitingForCheck:         ClaimNoValidation,
	VtaClaimStatusWaitingForPayment:       ClaimNoValidation,
	VtaClaimStatusCheckWritten:            ClaimNoValidation,
	VtaClaimStatusCheckVoided:             ClaimNoValidation,
	VtaClaimStatusClosedNoResponse:        ClaimNoValidation,
}

// VTARecordNoteDescription map for VTA record note desc
var VTARecordNoteDescription = map[string]string{
	VtaClaimStatusNew:                     "New claim started",
	VtaClaimStatusInquiry:                 "Status Changed to Inquiry",
	VtaClaimStatusPending:                 "Status Changed to Pending",
	VtaClaimStatusReadyToProcess:          "Status Changed to Ready to process",
	VtaClaimStatusWaitingForAuthorization: "Status Changed to Waiting for authorization",
	VtaClaimStatusPendingDenial:           "Status Changed to Pending denial",
	VtaClaimStatusWaitingForCheck:         "Status Changed to Authorization",
	VtaClaimStatusReturnedForCorrections:  "Status Changed to Returned",
	VtaClaimStatusCheckWritten:            "Status Changed to Check Written",
	VtaClaimStatusDeny:                    "Status Changed to Denied",
	VtaClaimStatusPendingReopened:         "Status Changed to Pending Reopened",
	VtaClaimStatusWaitingForPayment:       "Status Changed to Waiting for payment",
	VtaClaimStatusClosedNoResponse:        "Status Changed to Closed No-Response",
}
