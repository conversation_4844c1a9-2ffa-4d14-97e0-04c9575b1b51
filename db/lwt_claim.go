package db

const (
	// LwtClaimStatusInquiry LWT Claim status Inquiry
	LwtClaimStatusInquiry = "Inquiry"
	// LwtClaimStatusClaimInProcess LWT Claim status claim in process
	LwtClaimStatusClaimInProcess = "ClaimInProcess"
	// LwtClaimStatusPendingApproval LWT Claim status Pending approval
	LwtClaimStatusPendingApproval = "PendingApproval"
	// LwtClaimStatusPendingDenial LWT Claim Status pending denial
	LwtClaimStatusPendingDenial = "PendingDenial"
	// LwtClaimStatusApproved LWT Claim Status approved
	LwtClaimStatusApproved = "Approved"
	// LwtClaimStatusWaitingForCheck LWT Claim Status Waiting for Check
	LwtClaimStatusWaitingForCheck = "WaitingForCheck"
	// LwtClaimStatusCheckWritten LWT Claim Status Check Written
	LwtClaimStatusCheckWritten = "CheckWritten"
	// LwtClaimStatusClosedNoResponse LWT Claim Status Closed No Response
	LwtClaimStatusClosedNoResponse = "NoResponse"
	// LwtClaimStatusReturnedClaim LWT Claim Status returned claim
	LwtClaimStatusReturnedClaim = "ReturnedClaim"
	// LwtClaimStatusDenied LWT Claim Status Closed, Denied
	LwtClaimStatusDenied = "Denied"
	// LwtClaimStatusDeactivated LWT Claim Status Deactivated
	LwtClaimStatusDeactivated = "Deactivated"
	// LwtClaimStatusCheckVoided LWT claim status CheckVoided
	LwtClaimStatusCheckVoided = "CheckVoided"
	// LwtClaimStatusAdjusted LWT Claim Status Adjusted
	LwtClaimStatusAdjusted = "Adjusted"
)

// LWTRecordNoteDescription map for LWT record note desc
var LWTRecordNoteDescription = map[string]string{
	LwtClaimStatusInquiry:          "Status Changed to Inquiry",
	LwtClaimStatusClaimInProcess:   "Status Changed to Claim In Process",
	LwtClaimStatusPendingApproval:  "Status Changed to Pending Approval",
	LwtClaimStatusPendingDenial:    "Status Changed to Pending denial",
	LwtClaimStatusApproved:         "Status Changed to Approved",
	LwtClaimStatusCheckWritten:     "Status Changed to Check Written",
	LwtClaimStatusWaitingForCheck:  "Status Changed to Waiting For Check",
	LwtClaimStatusClosedNoResponse: "Status Changed to Closed No-Response",
	LwtClaimStatusReturnedClaim:    "Status Changed to Returned",
	LwtClaimStatusDenied:           "Status Changed to Denied",
	LwtClaimStatusDeactivated:      "Status Changed to Deactivated",
	LwtClaimStatusCheckVoided:      "Status Changed to Check Voided",
	LwtClaimStatusAdjusted:         "Status Changed to Adjusted",
}

const (
	// LWTClaimComplaintStatusOpen status open
	LWTClaimComplaintStatusOpen = "Open"
	// LWTClaimComplaintStatusApproved status Payable
	LWTClaimComplaintStatusApproved = "Approved"
	// LWTClaimComplaintStatusDenied status Denied
	LWTClaimComplaintStatusDenied = "Denied"
)
