package db

import (
	"database/sql"
	"fmt"
)

const (
	// ProductCodeService contract product code
	ProductCodeService = "VSC"
	// ProductCodeMaintenance contract product code
	ProductCodeMaintenance = "MNT"
	// ProductCodeGap contract product code
	ProductCodeGap = "GAP"
	// ProductCodeTheftRegistration contract product code
	ProductCodeTheftRegistration = "VTA"
	// ProductCodeCentury contract product code
	ProductCodeCentury = "CP"
	// ProductCodeAppearanceProtection contract product code
	ProductCodeAppearanceProtection = "AP"
	// ProductCodePaintlessDentRepair contract product code
	ProductCodePaintlessDentRepair = "PDR"
	// ProductCodeKeyReplacement contract product code
	ProductCodeKeyReplacement = "KEY"
	// ProductCodeTireWheel contract product code
	ProductCodeTireWheel = "TW"
	// ProductCodeLeaseWearTear contract product code
	ProductCodeLeaseWearTear = "LWT"
	// ProductCodeDrivePur contract product code
	ProductCodeDrivePur = "DP"
	// ProductCodeToyotaTireWheel contract product code
	ProductCodeToyotaTireWheel = "TT"
	// ProductCodeToyotaExcessiveWearUse contract product code
	ProductCodeToyotaExcessiveWearUse = "WU"
	// ProductCodeToyotaGap contract product code
	ProductCodeToyotaGap = "TG"
	// ProductCodeKey contract product code
	ProductCodeKey = "KE"
	// ProductCodeVeroGap contract product code
	ProductCodeVeroGap = "VG"
	// ProductCodeNSDTheft contract product code
	ProductCodeNSDTheft = "NT"
)

// RowExists function to check if row exists
func RowExists(query string, args ...interface{}) (bool, error) {
	var exists bool
	query = fmt.Sprintf("SELECT exists (%s)", query)
	err := Get().QueryRow(query, args...).Scan(&exists)
	if err != nil && err != sql.ErrNoRows {
		return false, err
	}
	return exists, nil
}
