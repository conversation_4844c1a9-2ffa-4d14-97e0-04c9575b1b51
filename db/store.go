package db

import (
	"fmt"
	"time"

	"github.com/lib/pq/hstore"
	"github.com/pkg/errors"
	"gopkg.in/guregu/null.v3"
)

// Store represents a store database record
type Store struct {
	ID                    int           `db:"id" json:"id"`
	CreatedAt             time.Time     `db:"created_at" json:"created_at"`
	UpdatedAt             time.Time     `db:"updated_at" json:"updated_at"`
	CompanyID             int           `db:"company_id" json:"company_id"`
	Name                  string        `db:"name" json:"name"`
	Code                  string        `db:"code" json:"code"`
	Address               string        `db:"address" json:"address"`
	City                  string        `db:"city" json:"city"`
	StateCode             string        `db:"state_code" json:"state_code"`
	PostalCode            string        `db:"postal_code" json:"postal_code"`
	Phone                 string        `db:"phone" json:"phone"`
	TimeZone              string        `db:"time_zone" json:"time_zone"`
	HasROIntegration      bool          `db:"has_ro_integration" json:"has_ro_integration"`
	HasDealIntegration    bool          `db:"has_deal_integration" json:"has_deal_integration"`
	AlphaDealerNumber     string        `db:"alpha_dealer_number" json:"alpha_dealer_number"`
	DMSProvider           string        `db:"dms_provider" json:"dms_provider"`
	DMSProviderParameters hstore.Hstore `db:"dms_provider_parameters" json:"dms_provider_parameters"`
	ClaimFacilityID       null.Int      `db:"claim_facility_id" json:"claim_facility_id,omitempty"`
}

// PDFFieldData receives a map and adds key/value pairs specific to a store to that map
func (s Store) PDFFieldData(payload map[string]string) {
	payload["store_name"] = s.Name
	payload["store_address"] = s.Address
	payload["store_city"] = s.City
	payload["store_state_code"] = s.StateCode
	payload["store_postal_code"] = s.PostalCode
	payload["store_city_state_postal"] = fmt.Sprintf("%s, %s %s", s.City, s.StateCode, s.PostalCode)
	payload["store_phone"] = s.Phone
}

const (
	// TimeZoneEastern is the time zone string used for Eastern in time.LoadLocation
	TimeZoneEastern = "US/Eastern"
	// TimeZoneCentral is the time zone string used for Central in time.LoadLocation
	TimeZoneCentral = "US/Central"
	// TimeZoneMountain is the time zone string used for Mountain in time.LoadLocation
	TimeZoneMountain = "US/Mountain"
	// TimeZoneArizona is the time zone string used for Arizona in time.LoadLocation
	TimeZoneArizona = "US/Arizona"
	// TimeZonePacific is the time zone string used for Pacific in time.LoadLocation
	TimeZonePacific = "US/Pacific"
	// TimeZoneAlaska is the time zone string used for mountain in time.LoadLocation. Representing' Jason!
	TimeZoneAlaska = "US/Alaska"
	// TimeZoneHawaii is the time zone string used for mountain in time.LoadLocation
	TimeZoneHawaii = "US/Hawaii"
	// DMSProviderCDK is the string representation of the DMS Provider 'CDK'
	DMSProviderCDK = "CDK"
	// DMSProviderStoneEagle is the string representation of the DMS Provider 'StoneEagle'
	DMSProviderStoneEagle = "StoneEagle"
	// DMSProviderUCS is the string representation of the DMS Provider 'UCS'
	DMSProviderUCS = "UCS"
	// DMSProviderTekion is the string representation of the DMS Provider 'Tekion'
	DMSProviderTekion = "TEKION"
)

// TimeZones is a standardized list of our accepted time zones
var TimeZones = []string{
	TimeZoneEastern,
	TimeZoneCentral,
	TimeZoneMountain,
	TimeZoneArizona,
	TimeZonePacific,
	TimeZoneAlaska,
	TimeZoneHawaii,
}

// States is a map of state codes to state names
var States = map[string]string{
	"AL": "Alabama",
	"AK": "Alaska",
	"AZ": "Arizona",
	"AR": "Arkansas",
	"CA": "California",
	"CO": "Colorado",
	"CT": "Connecticut",
	"DE": "Delaware",
	"FL": "Florida",
	"GA": "Georgia",
	"HI": "Hawaii",
	"ID": "Idaho",
	"IL": "Illinois",
	"IN": "Indiana",
	"IA": "Iowa",
	"KS": "Kansas",
	"KY": "Kentucky",
	"LA": "Louisiana",
	"ME": "Maine",
	"MD": "Maryland",
	"MA": "Massachusetts",
	"MI": "Michigan",
	"MN": "Minnesota",
	"MS": "Mississippi",
	"MO": "Missouri",
	"MT": "Montana",
	"NE": "Nebraska",
	"NV": "Nevada",
	"NH": "New Hampshire",
	"NJ": "New Jersey",
	"NM": "New Mexico",
	"NY": "New York",
	"NC": "North Carolina",
	"ND": "North Dakota",
	"OH": "Ohio",
	"OK": "Oklahoma",
	"OR": "Oregon",
	"PA": "Pennsylvania",
	"RI": "Rhode Island",
	"SC": "South Carolina",
	"SD": "South Dakota",
	"TN": "Tennessee",
	"TX": "Texas",
	"UT": "Utah",
	"VT": "Vermont",
	"VA": "Virginia",
	"WA": "Washington",
	"WV": "West Virginia",
	"WI": "Wisconsin",
	"WY": "Wyoming",
}

// DMSProviders is a list of supported DMS providers
var DMSProviders = []string{
	DMSProviderCDK,
	DMSProviderStoneEagle,
}

// InspectionSetIDForStoreID returns the inspection set ID for a given store by store ID
func InspectionSetIDForStoreID(storeID int) (int, error) {
	var isID int

	const query = `select c.inspection_set_id from companies c join stores s on s.company_id = c.id where s.id = $1`
	err := Get().Get(&isID, query, storeID)

	return isID, errors.Wrapf(err, "error getting inspection set ID for store ID %d", storeID)
}
