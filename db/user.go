package db

import (
	"time"

	"github.com/lib/pq"
	"github.com/lib/pq/hstore"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"

	"golang.org/x/crypto/bcrypt"
)

const (
	passwordSalt = "82d53ee2ac9e3bd4d316f20153b3774d83dc08af511f8db42092b673e90faa28391d71e98c80f6ea8421bf2fee00e6f32989ba6a481908106f5e3193c53a9a83"
	passwordCost = 14
	// RoleGapClaims is the `gap_claims` role
	RoleGapClaims = "gap_claims"
	// RoleGapClaimsManager is the `gap_claims_manager` role
	RoleGapClaimsManager = "gap_claims_manager"
	// RoleAutoClaims is the `auto_claims` role
	RoleAutoClaims = "auto_claims"
	// RoleAutoClaimsManager is the `auto_claims_manager` role
	RoleAutoClaimsManager = "auto_claims_manager"
	// RoleProductManager is the `product_manager` role
	RoleProductManager = "product_manager"
)

var (
	// RoleAdmin is the `admin` role
	RoleAdmin = "admin"
)

// User represents a user record
type User struct {
	ID                      int           `db:"id" json:"id"`
	CreatedAt               time.Time     `db:"created_at" json:"created_at"`
	UpdatedAt               time.Time     `db:"updated_at" json:"updated_at"`
	Email                   string        `db:"email" json:"email"`
	FirstName               string        `db:"first_name" json:"first_name"`
	LastName                string        `db:"last_name" json:"last_name"`
	PasswordDigest          string        `db:"password_digest" json:"-"`
	CompanyID               int           `db:"company_id" json:"company_id"`
	Active                  bool          `db:"active" json:"active"`
	Roles                   hstore.Hstore `db:"roles" json:"roles"`
	ConfirmedAt             pq.NullTime   `db:"confirmed_at" json:"confirmed_at"`
	ConfirmationTokenSetAt  pq.NullTime   `db:"confirmation_token_set_at" json:"confirmation_token_set_at"`
	ConfirmationToken       string        `db:"confirmation_token" json:"confirmation_token"`
	ResetPasswordToken      string        `db:"reset_password_token" json:"reset_password_token"`
	ResetPasswordTokenSetAt pq.NullTime   `db:"reset_password_token_set_at" json:"reset_password_token_set_at"`
	LoginToken              string        `db:"login_token" json:"login_token"`
	LoginTokenExpiresAt     time.Time     `db:"login_token_expires_at" json:"login_token_expires_at"`
	EmployeeNumber          string        `db:"employee_number" json:"employee_number"`
}

// FullName should return a combined version of the first name and last name
func (u User) FullName() string {
	return u.FirstName + " " + u.LastName
}

// PasswordHash will use bcrypt to generate a hash for the given password
func PasswordHash(pw string) (string, error) {
	b, err := bcrypt.GenerateFromPassword([]byte(pw+passwordSalt), passwordCost)
	if err != nil {
		return "", errors.Wrap(err, "bcrypt.GenerateFromPassword failed")
	}
	return string(b), nil
}

// PasswordMatch will use bcrypt to compare the given password with the password digest on the user
func (u *User) PasswordMatch(pw string) bool {
	return bcrypt.CompareHashAndPassword([]byte(u.PasswordDigest), []byte(pw+passwordSalt)) == nil
}

// SetPassword will set the password digest to the hashed version of the given password
func (u *User) SetPassword(pw string) error {
	h, err := PasswordHash(pw)
	if err != nil {
		return errors.Wrap(err, "User SetPassword failed")
	}
	u.PasswordDigest = h
	return nil
}

// HasRole will return true if the user has the given role
func (u *User) HasRole(role string) bool {
	return u.Roles.Map[role].Valid
}

// UserApprovedLimit represents user approved limit
type UserApprovedLimit struct {
	UserID           int             `json:"user_id" db:"user_id"`
	UpdatedAt        time.Time       `json:"updated_at" db:"updated_at"`
	UpdatedByUserID  int             `json:"updated_by_user_id" db:"updated_by_user_id"`
	ApprovedLimit    decimal.Decimal `json:"approved_limit" db:"approved_limit"`
	GAPApprovedLimit decimal.Decimal `json:"gap_approved_limit" db:"gap_approved_limit"`
	LWTApprovedLimit decimal.Decimal `json:"lwt_approved_limit" db:"lwt_approved_limit"`
}
