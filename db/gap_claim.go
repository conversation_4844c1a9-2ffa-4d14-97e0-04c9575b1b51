package db

const (
	// GapClaimStatusInquiry GAP Claim status Inquiry
	GapClaimStatusInquiry = "I"
	// GapClaimStatusPending GAP Claim status Pending
	GapClaimStatusPending = "P"
	// GapClaimStatusPendingReopened GAP Claim status Pending/Reopened
	GapClaimStatusPendingReopened = "RO-RP"
	// GapClaimStatusReadyToProcess GAP Claim Status Ready to Process (paperwork Complete)
	GapClaimStatusReadyToProcess = "RP"
	// GapClaimStatusWaitingForAuthorization GAP Claim Status Waiting for Authorization
	GapClaimStatusWaitingForAuthorization = "WA"
	// GapClaimStatusWaitingForCheck GAP Claim Status Waiting for Check
	GapClaimStatusWaitingForCheck = "WC"
	// GapClaimStatusWaitingForPayment GAP Claim Status Waiting for Payment (CSCI)
	GapClaimStatusWaitingForPayment = "WP"
	// GapClaimStatusDeny GAP Claim Status Closed, Deny
	GapClaimStatusDeny = "D"
	// GapClaimStatusReturnedForCorrections GAP Claim Status Returned for Corrections
	GapClaimStatusReturnedForCorrections = "RC"
	// GapClaimStatusClosedNoResponse GAP Claim Status Closed No Response
	GapClaimStatusClosedNoResponse = "C-NR"
	// GapClaimStatusNoGap GAP Claim Status No Gap
	GapClaimStatusNoGap = "NG"
	// GapClaimStatusCheckWritten GAP Claim Status Check Written
	GapClaimStatusCheckWritten = "C"
	// GapClaimStatusPendingDenial GAP Claim Status Pending Denial
	GapClaimStatusPendingDenial = "PD"
	// GapClaimStatusCheckVoided GAP Claim Status Check Voided
	GapClaimStatusCheckVoided = "CV"
)

// Constants for waiting for reasons
const (
	WaitingForInsuranceCheck            = "1a"
	WaitingForSettlementAmount          = "1b"
	WaitingForValuationReport           = "1c"
	WaitingForOriginalFinancingContract = "2a"
	WaitingForContractNumberMatches     = "2b"
	WaitingForPoliceReport              = "3"
	WaitingForInsurancePolicyDeductible = "4"
	WaitingForBankInformation           = "5a"
	WaitingForFullLoanHistory           = "5b"
	WaitingForCancelContractService     = "6a"
	WaitingForCancelContractMaintenance = "6b"
	WaitingForCancelContractKey         = "7a"
	WaitingForCancelContractTireWheel   = "7b"
)

// Field IDs
// These are referred in the gap_claim_documents table
const (
	FieldIDBookOutRequested                     = 1
	FieldIDRunAmortizationSheet                 = 2
	FieldIDCancelContract                       = 3
	FieldIDOtherInputOne                        = 4
	FieldIDOtherInputTwo                        = 5
	FieldIDOtherInputThree                      = 6
	FieldIDSettlementAmount                     = 7
	FieldIDInsuranceCheckAmount                 = 8
	FieldIDValuationReport                      = 9
	FieldIDMatchesBaseValue                     = 10
	FieldIDVinMatches                           = 11
	FieldIDPriorDamage                          = 12
	FieldIDMiscFee                              = 13
	FieldIDMileage                              = 14
	FieldIDDol                                  = 15
	FieldIDType                                 = 16
	FieldIDOptionsMatchBookOut                  = 17
	FieldIDOptionsMatchBookOutOver150Percentage = 18
	FieldIDOriginalFinancingContract            = 19
	FieldIDContractNumberMatches                = 20
	FieldIDBankHistoryMatches                   = 21
	FieldIDPoliceReport                         = 22
	FieldIDInsurancePolicyDeductiblePositive    = 23
	FieldIDInsurancePolicyDeductibleNegative    = 24
	FieldIDBankInformation                      = 25
	FieldIDFullLoanHistory                      = 26
	FieldIDLoanNumber                           = 27
	FieldIDPaymentAmount                        = 28
	FieldIDInterestRate                         = 29
	FieldIDFirstPaymentDate                     = 30
	FieldIDParentStatus                         = 31
	FieldIDParentReturnedNote                   = 32
	FieldIDParentDeniedReason                   = 33
	FieldIDParentAuthorizationNumber            = 34
	FieldIDParentCheckNumber                    = 35
	FieldIDParentPaidAmount                     = 36
	FieldIDParentPaidDate                       = 37
	FieldIDChildClaim                           = 38
	FieldIDChildClaimReason                     = 39
	FieldIDChildClaimAmount                     = 40
	FieldIDChildStatus                          = 41
	FieldIDChildReturnedNote                    = 42
	FieldIDChildDeniedReason                    = 43
	FieldIDChildAuthorizationNumber             = 44
	FieldIDChildCheckNumber                     = 45
	FieldIDChildPaidAmount                      = 46
	FieldIDChildPaidDate                        = 47
	FieldIDDealDate                             = 48
	FieldIDTermMonths                           = 49
	FieldCSCheck                                = 50
	FieldCSChildCheck                           = 51
	FieldIDMSRPValue                            = 52
	FieldIDInsuranceCompany                     = 53
	FieldIDPolicyNumber                         = 54
	FieldIDMileageDeduction                     = 55
	FieldIDNADA                                 = 56
	FieldIDValuationNADADifference              = 57
	FieldIDRecoveryReview                       = 58
	FieldIDRecoveryAdded                        = 59
	FieldIDRecoveryRemoved                      = 60
	FieldIDRecoveryStatus                       = 61
	FieldIDEstimateWithPhotos                   = 62
	FieldIDIsPaidByCS                           = 63
	FieldIDNegativeEquityAmount                 = 64
	// There will be IDs created dynamically based on the number of child claims
	// the value of those will be 138 for 2nd ChildClaim, 238 for 3rd ChildClaim etc.
	FieldIDGAPMAX = 1000
	// Below IDs for claim recovery fields, these will be referred in the same table
	FieldIDAlliedClaimNumber = 1001

	//TODO: FieldIDInsuranceCompany     = 1002, the same field is used on GAP worksheet and Recovery worksheet
	// hence reusing the FieldIDInsuranceCompany = 53, as defined in line 105
	// still blocking 1002 for now, if we plan to keep different notes for the field on two worksheet, we can use it

	FieldIDInsuranceClaimNumber = 1003
	// TODO: FieldIDCaseAttachments      = 1004, the same field is used on GAP worksheet and Recovery worksheet
	// hence reusing the FieldIDRecoveryReview = 58 for case attachment on on recovery worksheet
	// still blocking 1004 for now, if we plan to keep different notes and attachments for the field on two worksheet, we can use it
	FieldIDContractBalance = 1005
	FieldIDClaimType       = 1006
	FieldIDCaseComments    = 1007
	//TODO: FieldIDRecoveryStatus    = 1008
	// FieldIDRecoveryStatus on GAP RecoverySheet shares notes with FieldIDRecoveryReview on GAP Claim Worksheet, hence
	// using the same ID for both fields, however still blocking 1008 for now, if we decide to keep different notes for
	// each worksheet
	FieldIDCheckAmount = 1009
	FieldIDMAX         = 1010
)

// RecordNoteDescription map of claims status and record note
var RecordNoteDescription = map[string]string{
	GapClaimStatusInquiry:                 "Status Changed to Inquiry",
	GapClaimStatusPending:                 "Status Changed to Pending",
	GapClaimStatusReadyToProcess:          "Status Changed to Ready to process",
	GapClaimStatusWaitingForAuthorization: "Status Changed to Waiting for authorization",
	GapClaimStatusPendingDenial:           "Status Changed to Pending denial",
	GapClaimStatusWaitingForCheck:         "Status Changed to Authorization",
	GapClaimStatusReturnedForCorrections:  "Status Changed to Returned",
	GapClaimStatusCheckWritten:            "Status Changed to Check Written",
	GapClaimStatusDeny:                    "Status Changed to Denied",
	GapClaimStatusClosedNoResponse:        "Status Changed to Closed No-Response",
	GapClaimStatusPendingReopened:         "Status Changed to Pending Reopened",
	GapClaimStatusNoGap:                   "Status Changed to No GAP",
	GapClaimStatusWaitingForPayment:       "Status Changed to Waiting for payment",
}

// FieldRecordNoteDescription map of claims fields and record note
var FieldRecordNoteDescription = map[int]string{
	FieldIDBookOutRequested:                     "BookOut Requested ",
	FieldIDRunAmortizationSheet:                 "Run Amortization Sheet",
	FieldIDCancelContract:                       "Cancel Contract",
	FieldIDOtherInputOne:                        "Other Input One",
	FieldIDOtherInputTwo:                        "Other Input Two",
	FieldIDOtherInputThree:                      "Other Input Three",
	FieldIDSettlementAmount:                     "Settlement Amount",
	FieldIDInsuranceCheckAmount:                 "Insurance Check Amount",
	FieldIDValuationReport:                      "Valuation Report",
	FieldIDMatchesBaseValue:                     "Matches Base Value",
	FieldIDVinMatches:                           "Vin Matches",
	FieldIDPriorDamage:                          "Prior Damage",
	FieldIDMiscFee:                              "Misc Fee",
	FieldIDMileage:                              "Mileage",
	FieldIDDol:                                  "Dol",
	FieldIDType:                                 "Type",
	FieldIDOptionsMatchBookOut:                  "Options Match BookOut",
	FieldIDOptionsMatchBookOutOver150Percentage: "Options Match Book Out Over 150 Percentage",
	FieldIDOriginalFinancingContract:            "Original Financing Contract",
	FieldIDContractNumberMatches:                "Contract Number Matches ",
	FieldIDBankHistoryMatches:                   "Bank History Matches",
	FieldIDPoliceReport:                         "Police Report",
	FieldIDInsurancePolicyDeductiblePositive:    "Insurance Policy Deductible Positive",
	FieldIDInsurancePolicyDeductibleNegative:    "Insurance Policy Deductible Negative ",
	FieldIDBankInformation:                      "Bank Information",
	FieldIDFullLoanHistory:                      "Full Loan History",
	FieldIDLoanNumber:                           "Loan Number",
	FieldIDPaymentAmount:                        "Payment Amount",
	FieldIDInterestRate:                         "Interest Rate",
	FieldIDFirstPaymentDate:                     "First Payment Date",
	FieldIDParentStatus:                         "Parent Status",
	FieldIDParentReturnedNote:                   "Parent Returned Note",
	FieldIDParentDeniedReason:                   "Parent Denied Reason",
	FieldIDParentAuthorizationNumber:            "Parent Authorization Number",
	FieldIDParentCheckNumber:                    "Parent Check Number",
	FieldIDParentPaidAmount:                     "Parent Paid Amount",
	FieldIDParentPaidDate:                       "Parent Paid Date",
	FieldIDChildClaim:                           "Child Claim",
	FieldIDChildClaimReason:                     "Child Claim Reason",
	FieldIDChildClaimAmount:                     "Child Claim Amount",
	FieldIDChildStatus:                          "Child Status",
	FieldIDChildReturnedNote:                    "Child Returned Note",
	FieldIDChildDeniedReason:                    "Child Denied Reason",
	FieldIDChildAuthorizationNumber:             "Child Authorization Number",
	FieldIDChildCheckNumber:                     "Child Check Number",
	FieldIDChildPaidAmount:                      "Child Paid Amount",
	FieldIDChildPaidDate:                        "Child Paid Date",
	FieldIDDealDate:                             "Contract Deal Date",
	FieldIDTermMonths:                           "Contract Term Months",
	FieldCSCheck:                                "CS Check",
	FieldCSChildCheck:                           "CS Child Check",
	FieldIDMSRPValue:                            "MSRP Value",
	FieldIDInsuranceCompany:                     "Insurance Company",
	FieldIDPolicyNumber:                         "Policy Number",
	FieldIDMileageDeduction:                     "Mileage Deduction",
	FieldIDNADA:                                 "NADA",
	FieldIDValuationNADADifference:              "Valuation NADA Difference",
	FieldIDRecoveryReview:                       "Recovery review",
	FieldIDRecoveryAdded:                        "Recovery added",
	FieldIDRecoveryRemoved:                      "Recovery removed",
	FieldIDRecoveryStatus:                       "Recovery status",
	FieldIDEstimateWithPhotos:                   "Estimate with photos",
	FieldIDAlliedClaimNumber:                    "Allied claim number",
	FieldIDInsuranceClaimNumber:                 "Insurance claim number",
	FieldIDContractBalance:                      "Contract balance",
	FieldIDClaimType:                            "Claim Type",
	FieldIDCaseComments:                         "Case comments",
	FieldIDCheckAmount:                          "Recovery Check Amount",
	FieldIDIsPaidByCS:                           "Claim paid by CS",
	FieldIDNegativeEquityAmount:                 "Negative Equity Amount",
}
