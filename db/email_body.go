package db

// GapMissingDescription map for missing item description
var GapMissingDescription = map[string]string{
	WaitingForInsuranceCheck:            "Copy of Primary Insurance Settlement Check, Screenshot or proof of check on the account history.",
	WaitingForSettlementAmount:          "Copy of Primary Insurance Settlement Breakdown Letter",
	WaitingForValuationReport:           "Copy of Valuation Worksheet and Comparison Report from your Insurance Company.",
	WaitingForOriginalFinancingContract: "Copy of Original Financing/Retail Installment Contract (shows first payment due, payment amount & interest).",
	WaitingForContractNumberMatches:     "Copy of Original GAP Addendum.",
	WaitingForPoliceReport:              "Copy of full Police/Accident Report, not drivers exchange form or tow report.",
	WaitingForInsurancePolicyDeductible: "Copy of Insurance Declaration Page showing Deductible amount.",
	WaitingForBankInformation:           "Lenders Address & Account # (for proper credit to your account).",
	WaitingForFullLoanHistory:           "Copy of the Full Auto Loan Payment History if you would like us to review it for consideration.",
	WaitingForCancelContractService:     "Copy of Service Contract",
	WaitingForCancelContractMaintenance: "Copy of Maintenance Contract",
	WaitingForCancelContractKey:         "Copy of Key Contract",
	WaitingForCancelContractTireWheel:   "Copy of Tire and Wheel Contract",
}

// tokens will be part of email template used for gap letters
// e.g. token {{CustomerFullName}}
const (
	TokenDateToday               = "{{DateToday}}"
	TokenCustomerNameFull        = "{{CustomerNameFull}}"
	TokenCustomerNameFirst       = "{{CustomerNameFirst}}"
	TokenCustomerNameLast        = "{{CustomerNameLast}}"
	TokenCustomerAddress         = "{{CustomerAddress}}"
	TokenCustomerCity            = "{{CustomerCity}}"
	TokenCustomerState           = "{{CustomerState}}"
	TokenCustomerZip             = "{{CustomerZip}}"
	TokenCustomerAddressFull     = "{{CustomerAddressFull}}"
	TokenGapContractNumber       = "{{GapContractNumber}}"
	TokenGapMissing              = "{{GapMissing}}"
	TokenGapCalculateCaseReserve = "{{GapCalculateCaseReserve}}"
	TokenDealershipName          = "{{DealershipName}}"
	TokenPoliceReport            = "{{PoliceReport}}"
	TokenVINNumber               = "{{VINNumber}}"
	TokenDOLMileage              = "{{DOLMileage}}"
	TokenDOL                     = "{{DOL}}"
	TokenDealNumber              = "{{DealNumber}}"
	TokenVehicleMake             = "{{VehicleMake}}"
	TokenVehicleModel            = "{{VehicleModel}}"
	TokenVehicleYear             = "{{VehicleYear}}"
	TokenContractEffectiveDate   = "{{ContractEffectiveDate}}"
	TokenContractNumber          = "{{ContractNumber}}"
)
