package db

const (
	// AutoClaimStatusOpen status open
	AutoClaimStatusOpen = "Open"
	// AutoClaimStatusReturned status returned
	AutoClaimStatusReturned = "Returned"
	// AutoClaimStatusPayable status payable
	AutoClaimStatusPayable = "Payable"
	// AutoClaimStatusApproved status approved
	AutoClaimStatusApproved = "Approved"
	// AutoClaimStatusDenied status denied
	AutoClaimStatusDenied = "Denied"
	// AutoClaimStatusWaitingForCheck status WaitingForCheck
	AutoClaimStatusWaitingForCheck = "WaitingForCheck"
	// AutoClaimStatusWaitingForChargeback status WaitingForChargeback
	AutoClaimStatusWaitingForChargeback = "WaitingForChargeback"
	// AutoClaimStatusChargeback status Chargeback
	AutoClaimStatusChargeback = "Chargeback"
	// AutoClaimStatusChargebackCollected status Chargeback
	AutoClaimStatusChargebackCollected = "ChargebackCollected"
	// AutoClaimStatusCheckWritten status CheckWritten
	AutoClaimStatusCheckWritten = "CheckWritten"
	// AutoClaimStatusCCPaid for paid by credit card
	AutoClaimStatusCCPaid = "CCPaid"
	// AutoClaimStatusInvoiceSent for paid by credit card
	AutoClaimStatusInvoiceSent = "InvoiceSent"
	// AutoClaimStatusAuthorizedCCClaim for paid by credit card
	AutoClaimStatusAuthorizedCCClaim = "AuthorizedCCClaim"
	// AutoClaimStatusNew for new claims
	AutoClaimStatusNew = "New"
	// AutoClaimStatusPreAuth for PreAuth claims
	AutoClaimStatusPreAuth = "PreAuthorization"
	// AutoClaimStatusDeactivated for Deactivated claims
	AutoClaimStatusDeactivated = "Deactivated"
	// AutoClaimStatusNewSB temporary flag for sb claims
	AutoClaimStatusNewSB = "SBClaim"
	// AutoClaimStatusReversed flag for claim reversal
	AutoClaimStatusReversed = "Reversed"
	// AutoClaimStatusWaitingForReversed flag for claim reversal
	AutoClaimStatusWaitingForReversed = "WaitingForReversed"
	// AutoClaimStatusAdjusted flag for claim adjustment
	AutoClaimStatusAdjusted = "Adjusted"
	// AutoClaimStatusWaitingOnVendor flag for claim waiting on vendor #
	AutoClaimStatusWaitingOnVendor = "WaitingOnVendor"
	// AutoClaimStatusNeedRentalBill status NeedRentalBill
	AutoClaimStatusNeedRentalBill = "NeedRentalBill"
	// AutoClaimStatusNeedSubletBill status NeedSubletBill
	AutoClaimStatusNeedSubletBill = "NeedSubletBill"
	// AutoClaimStatusNeedSMToCall status NeedSMToCall
	AutoClaimStatusNeedSMToCall = "NeedSMToCall"
	// AutoClaimStatusNeedClosedAccountingRO status NeedClosedAccountingRO
	AutoClaimStatusNeedClosedAccountingRO = "NeedClosedAccountingRO"
	// AutoClaimStatusNeedProofOfDeductibleReimbursement status NeedProofOfDeductibleReimbursement
	AutoClaimStatusNeedProofOfDeductibleReimbursement = "NeedProofOfDeductibleReimbursement"
	// AutoClaimStatusNewLCAWithROLookup for new claims in LCA RO Lookup
	AutoClaimStatusNewLCAWithROLookup = "NewLCAROLookup"
	// AutoClaimStatusNewLCAWithManual for new claims in LCA Manual
	AutoClaimStatusNewLCAWithManual = "NewLCAManual"
	// AutoClaimStatusDealerChargedBack for dealer charged back
	AutoClaimStatusDealerChargedBack = "DealerChargedBack"
	// ClaimDocumentTypeInspectionDocument indicates document type for inspection attachments
	ClaimDocumentTypeInspectionDocument = "INSPECTIONDOCUMENT"
)

// DashClaimsStatuses list of automotive claim statuses to be used in claims dashboard
var DashClaimsStatuses = []string{
	AutoClaimStatusOpen,
	AutoClaimStatusPreAuth,
	AutoClaimStatusReturned,
	AutoClaimStatusCCPaid,
	AutoClaimStatusNeedRentalBill,
	AutoClaimStatusNeedSubletBill,
	AutoClaimStatusNeedSMToCall,
	AutoClaimStatusNeedClosedAccountingRO,
	AutoClaimStatusNeedProofOfDeductibleReimbursement,
	AutoClaimStatusPayable,
	AutoClaimStatusApproved,
	AutoClaimStatusAuthorizedCCClaim,
	AutoClaimStatusInvoiceSent,
	AutoClaimStatusDenied,
	AutoClaimStatusDeactivated,
	AutoClaimStatusWaitingForCheck,
	AutoClaimStatusWaitingForReversed,
	AutoClaimStatusWaitingForChargeback,
	AutoClaimStatusAdjusted,
	AutoClaimStatusCheckWritten,
	AutoClaimStatusChargeback,
	AutoClaimStatusChargebackCollected,
	AutoClaimStatusReversed,
	AutoClaimStatusWaitingOnVendor,
	AutoClaimStatusDealerChargedBack,
}

const (
	// AutoClaimComplaintStatusOpen status open
	AutoClaimComplaintStatusOpen = "Open"
	// AutoClaimComplaintStatusPayable status Payable
	AutoClaimComplaintStatusPayable = "Payable"
	// AutoClaimComplaintStatusDenied status Denied
	AutoClaimComplaintStatusDenied = "Denied"
)

const (
	// AutoClaimReassignmentStatusNew reassign status new
	AutoClaimReassignmentStatusNew = "New"
	// AutoClaimReassignmentStatusAccepted reassign status accepted
	AutoClaimReassignmentStatusAccepted = "Accepted"
	// AutoClaimReassignmentStatusPending reassign sttaus pending
	AutoClaimReassignmentStatusPending = "Pending"
	// AutoClaimReassignmentStatusRejected reassign status rejected
	AutoClaimReassignmentStatusRejected = "Rejected"
)

const (
	// CoverageDisappearingDeductible db coverage flag name
	CoverageDisappearingDeductible = "disappearing_deductible"
	// CoverageHighTech db coverage flag name
	CoverageHighTech = "high_tech"
	// CoverageSealsAndGasket db coverage flag name
	CoverageSealsAndGasket = "seals_and_gasket"
	// CoverageRentalUpgrade db coverage flag name
	CoverageRentalUpgrade = "rental_upgrade"
	// CoverageCommercialUse db coverage flag name
	CoverageCommercialUse = "commercial_use"
	// CoverageStandardPowertrainPlusOption db coverage flag name
	CoverageStandardPowertrainPlusOption = "standard_powertrain_plus_option"
	// CoverageSmartTechOption db coverage flag name
	CoverageSmartTechOption = "smart_tech_option"
	// CoverageCanadianVehicle db coverage flag name
	CoverageCanadianVehicle = "canadian_vehicle"
	// CoveragePaint db coverage flag name
	CoveragePaint = "paint"
	// CoverageFabric db coverage flag name
	CoverageFabric = "fabric"
	// CoverageLeatherOrVinyl db coverage flag name
	CoverageLeatherOrVinyl = "leather_or_vinyl"
	// CoverageDentAndDing db coverage flag name
	CoverageDentAndDing = "dent_and_ding"
)

const (
	// CreditCardBillPrefix Credit card bill prefix
	CreditCardBillPrefix = "CCARD_"
	// CustomerBillPrefix Customer payee bill prefix
	CustomerBillPrefix = "CUST_"
	// ReversedBillPrefix Reversed bill prefix
	ReversedBillPrefix = "REV_"
	// AdjustedBillPrefix Reversed bill prefix
	AdjustedBillPrefix = "ADJ_"
)

const (
	// PayTypeCreditCard Paytype Credit Card
	PayTypeCreditCard = "CC"
	// PayTypeCustomer Paytype Customer
	PayTypeCustomer = "CR"
	// PayTypeStore Paytype Facility
	PayTypeStore = "RF"
)

// PayTypes current supported paytypes
var PayTypes = []string{PayTypeCreditCard, PayTypeCustomer, PayTypeStore}

// AutomotiveRecordNoteDescription current auto record note desc
var AutomotiveRecordNoteDescription = map[string]string{
	AutoClaimStatusNew:                                "New claim started",
	AutoClaimStatusPreAuth:                            "Status changed to pre-authorization",
	AutoClaimStatusOpen:                               "Status changed to open",
	AutoClaimStatusReturned:                           "Status changed to returned",
	AutoClaimStatusReversed:                           "Status changed to reversed",
	AutoClaimStatusPayable:                            "Status changed to payable",
	AutoClaimStatusApproved:                           "Status changed to approved",
	AutoClaimStatusDenied:                             "Status changed to deny",
	AutoClaimStatusWaitingForCheck:                    "Status changed to waiting for check",
	AutoClaimStatusCheckWritten:                       "Status changed to check written",
	AutoClaimStatusCCPaid:                             "Status changed to cc paid",
	AutoClaimStatusDeactivated:                        "Status changed to deactivated",
	AutoClaimStatusNewSB:                              "New SB claim started",
	AutoClaimStatusWaitingOnVendor:                    "Status changed to waiting on vendor #",
	AutoClaimStatusNeedRentalBill:                     "Status changed to need rental bill",
	AutoClaimStatusNeedSubletBill:                     "Status changed to need sublet bill",
	AutoClaimStatusNeedSMToCall:                       "Status changed to need SM to call",
	AutoClaimStatusNeedClosedAccountingRO:             "Status changed to need closed accounting RO",
	AutoClaimStatusNeedProofOfDeductibleReimbursement: "Status changed to need proof of deductible reimbursement",
	AutoClaimStatusInvoiceSent:                        "Status changed to Invoice Sent",
	AutoClaimStatusAuthorizedCCClaim:                  "Status changed to Authorized CC claim",
	AutoClaimStatusNewLCAWithROLookup:                 "New LCA claim with RO lookup started",
	AutoClaimStatusNewLCAWithManual:                   "New LCA claim with Manual RO started",
	AutoClaimStatusDealerChargedBack:                  "Status changed to Dealer Charged Back",
}

// AutomotiveChargebackRecordNoteDescription current chargeback auto record note desc
var AutomotiveChargebackRecordNoteDescription = map[string]string{
	AutoClaimStatusNew:                  "Chargeback: New claim started",
	AutoClaimStatusOpen:                 "Chargeback: Status changed to open",
	AutoClaimStatusApproved:             "Chargeback: Status changed to approved",
	AutoClaimStatusWaitingForChargeback: "Chargeback: Status changed to waiting for chargeback",
	AutoClaimStatusChargeback:           "Chargeback: Status changed to chargeback",
	AutoClaimStatusDeactivated:          "Chargeback: Status changed to deactivated",
}

const (
	// CDKLaborTypeCLCA CLCA
	CDKLaborTypeCLCA = "CLCA"
	// CDKLaborTypeCLCM CLCM
	CDKLaborTypeCLCM = "CLCM"
	// CDKLaborTypeCLCE CLCE
	CDKLaborTypeCLCE = "CLCE"
	// CDKLaborTypeCLCX CLCX
	CDKLaborTypeCLCX = "CLCX"
	// CDKLaborTypeCLCAR CLCAR
	CDKLaborTypeCLCAR = "CLCAR"
	// CDKLaborTypeCLCER CLCER
	CDKLaborTypeCLCER = "CLCER"
	// CDKLaborTypeCELCA CELCA
	CDKLaborTypeCELCA = "CELCA"
	// CDKLaborTypeCEOMC CEOMC
	CDKLaborTypeCEOMC = "CEOMC"
	// CDKLaborTypeCEVSC CEVSC
	CDKLaborTypeCEVSC = "CEVSC"
	// CDKLaborTypeCELCXMaintOnly CELCX
	CDKLaborTypeCELCXMaintOnly = "CELCX"
	// CDKLaborTypeCLCL CLCL
	CDKLaborTypeCLCL = "CLCL"
	// CDKLaborTypeCLCAG CLCAG
	CDKLaborTypeCLCAG = "CLCAG"
	//CDKLaborTypeCLCXG CLCXG
	CDKLaborTypeCLCXG = "CLCXG"
	// CDKLaborTypeWLCA WLCA
	CDKLaborTypeWLCA = "WLCA"
	// CDKLaborTypeWLCXMaintOnly WLCX
	CDKLaborTypeWLCXMaintOnly = "WLCX"
)

// CDKLaborTypes CDK labor types
var CDKLaborTypes = []string{
	CDKLaborTypeCLCA,
	CDKLaborTypeCLCL,
	CDKLaborTypeCLCM,
	CDKLaborTypeCLCE,
	CDKLaborTypeCLCX,
	CDKLaborTypeCLCAR,
	CDKLaborTypeCLCER,
	CDKLaborTypeCELCA,
	CDKLaborTypeCEOMC,
	CDKLaborTypeCEVSC,
	CDKLaborTypeCLCAG,
	CDKLaborTypeCLCXG,
	CDKLaborTypeWLCA,
}

const (
	// FacilityUserName Facility user name
	FacilityUserName = "FACILITY"
	// SystemUserName system user name
	SystemUserName = "SYSTEM"
)

const (
	// ClaimTypeTCA claim type TCA
	ClaimTypeTCA = "TCA"
	// ClaimTypeSB claim type SB
	ClaimTypeSB = "SB"
	// ClaimTypeLCA claim type LCA
	ClaimTypeLCA = "LCA"
)

const (
	// ProductCodeWhizMaintenance product in whiz for maintenance contract
	ProductCodeWhizMaintenance = "MNT"
)

// InProcessStatuses compilation of all open status for claim.
var InProcessStatuses = []string{
	AutoClaimStatusOpen,
	AutoClaimStatusReturned,
	AutoClaimStatusPayable,
	AutoClaimStatusApproved,
	AutoClaimStatusWaitingForCheck,
	AutoClaimStatusCheckWritten,
	AutoClaimStatusCCPaid,
	AutoClaimStatusInvoiceSent,
	AutoClaimStatusAuthorizedCCClaim,
	AutoClaimStatusNew,
	AutoClaimStatusPreAuth,
	AutoClaimStatusNewSB,
	AutoClaimStatusWaitingOnVendor,
	AutoClaimStatusNeedRentalBill,
	AutoClaimStatusNeedSubletBill,
	AutoClaimStatusNeedSMToCall,
	AutoClaimStatusNeedClosedAccountingRO,
	AutoClaimStatusNeedProofOfDeductibleReimbursement,
}

const (
	// ContractStatusActive indicates the active status of the contract
	ContractStatusActive = "Active"
)

const (
	// AutoClaimPaymentType indicates the payment type of the claim amount
	AutoClaimPaymentType = "Claim"
	// AutoClaimAdjustPaymentType indicates the adjustment payment type of the claim amount
	AutoClaimAdjustPaymentType = "Adjustment"
	// AutoClaimNegativePaymentType indicates the negative payment type of the claim amount
	AutoClaimNegativePaymentType = "Negative"
)

const (
	// AutoClaimComplaintStatusReasonOther status reason Other
	AutoClaimComplaintStatusReasonOther = "Other"
)
