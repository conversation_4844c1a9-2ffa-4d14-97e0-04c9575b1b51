package main

import (
	"flag"
	"log"
	"os"
	"phizz/db"
	"time"

	"github.com/pkg/errors"
)

const (
	whizContractQuery = `
		select
			code,
			original_code,
			product_type_code
		from contracts
		where code != original_code
	`
	automotiveContractQuery = `
		select distinct
			contract_number as code,
			product_code as product_type_code
		from automotive_claims
	`
	gapContractQuery = `
		select distinct
			contract_number as code
		from gap_claims
	`
	gapClaimContractQuery = `
		select distinct
			contract_number as code,
			contract_code as product_type_code
		from gap_claim_contracts
	`
	updateAutomotiveClaim = `
		update automotive_claims
		set 
			contract_number = :code
		where 
			contract_number = :original_code
			and product_code = :product_type_code
	`
	updateGapClaimContract = `
		update gap_claim_contracts
		set
			contract_number = :code
		where
			contract_number = :original_code
			and contract_code = :product_type_code
	`
	updateGapContract = `
		update gap_claims
		set
			contract_number = :code
		where
			contract_number = :original_code
	`
)

type whizContract struct {
	Code            string `db:"code"`
	OriginalCode    string `db:"original_code"`
	ProductTypeCode string `db:"product_type_code"`
}

type automotiveContractCache map[automotiveContract]bool

type automotiveContract struct {
	Code            string `db:"code"`
	ProductTypeCode string `db:"product_type_code"`
}

type gapContractCache map[string]bool

type gapContract struct {
	Code string `db:"code"`
}

type gapClaimContractCache map[gapClaimContract]bool

type gapClaimContract struct {
	Code            string `db:"code"`
	ProductTypeCode string `db:"product_type_code"`
}

var (
	audit bool
)

func init() {
	flag.BoolVar(&audit, "audit", false, "will run doing audit not applying changes")
	flag.Parse()
}

func main() {
	var err error
	var contracts []whizContract

	logger := log.New(os.Stdout, "[Contract Code Sync] ", log.LstdFlags)

	if audit {
		logger.Println("Running in [Audit] mode")
	}

	synced := 0
	updatedAC := 0
	updatedGCC := 0
	updatedGC := 0
	start := time.Now()
	defer func() {
		elapsed := time.Since(start)
		logger.Printf("Synced %d Whiz Contract codes Updating %d automotive claims and %d gap claim contracts and %d gap claims in %v at %.2f claims|contracts/second.\n", synced, updatedAC, updatedGCC, updatedGC, elapsed, float64(updatedAC+updatedGCC)/(float64(elapsed)/float64(time.Second)))

		// Run an Analyze statement to refresh database/table stats as recommended by
		// the PostgreSQL documentation
		// https://www.postgresql.org/docs/current/populate.html#POPULATE-ANALYZE
		_, err = db.Get().Exec("ANALYZE")
		if err != nil {
			logger.Fatalf("error running analyze statement. Error: %+v", err)
		}
	}()

	logger.Println("Loading Whiz contracts...")
	err = db.GetWhiz().Select(&contracts, whizContractQuery)
	if err != nil {
		logger.Fatalf("error loading whiz contracts. %+v\n", err)
	}

	logger.Printf("loaded [%d] whiz contracts\n", len(contracts))

	automotiveContracts, err := loadAutomotiveContractCache()
	if err != nil {
		logger.Fatalf("error loading automotive contracts. %+v\n", err)
	}

	gapContracts, err := loadGapContractCache()
	if err != nil {
		logger.Fatalf("error loading gap contracts. %+v\n", err)
	}

	gapClaimContracts, err := loadGapClaimContractCache()
	if err != nil {
		logger.Fatalf("error loading gap claim contracts. %+v\n", err)
	}

	for _, c := range contracts {
		tx, err := db.Get().Beginx()
		if err != nil {
			logger.Fatalf("error getting database transaction. %+v\n", err)
		}

		ac := automotiveContract{
			Code:            c.OriginalCode,
			ProductTypeCode: c.ProductTypeCode,
		}
		if _, ok := automotiveContracts[ac]; ok {
			stmt, err := tx.PrepareNamed(updateAutomotiveClaim)
			if err != nil {
				tx.Rollback()
				logger.Fatalf("error preparing update automotive claims. %+v\n", err)
			}
			result, err := stmt.Exec(c)
			if err != nil {
				tx.Rollback()
				logger.Fatalf("error updating automotive claim for product [%s] contract code [%s] to [%s]. %+v\n", c.ProductTypeCode, c.OriginalCode, c.Code, err)
			}
			updatedRows, err := result.RowsAffected()
			if err != nil {
				updatedRows = 0
			}
			updatedAC = updatedAC + int(updatedRows)
			logger.Printf("Updated %d automotive claim rows from [%s] to [%s]\n", updatedRows, c.OriginalCode, c.Code)
		}

		if c.ProductTypeCode == "GAP" {
			if _, ok := gapContracts[c.OriginalCode]; ok {
				stmt, err := tx.PrepareNamed(updateGapContract)
				if err != nil {
					tx.Rollback()
					logger.Fatalf("error preparing update gap claims. %+v\n", err)
				}
				result, err := stmt.Exec(c)
				if err != nil {
					tx.Rollback()
					logger.Fatalf("error updating gap claims. %+v\n", err)
				}
				updatedRows, err := result.RowsAffected()
				if err != nil {
					updatedRows = 0
				}
				updatedGC = updatedGC + int(updatedRows)
				logger.Printf("Updated %d gap claim rows from [%s] to [%s]\n", updatedRows, c.OriginalCode, c.Code)
			}

			var ptcs []string
			switch c.ProductTypeCode {
			case "VSC":
				ptcs = append(ptcs, "SC")
			case "MNT":
				ptcs = append(ptcs, "MC")
			case "GAP":
				ptcs = append(ptcs, "GP")
				ptcs = append(ptcs, "TG")
			case "AP":
				fallthrough
			case "PDR":
				ptcs = append(ptcs, "CP")
			case "LWT":
				ptcs = append(ptcs, "WT")
				ptcs = append(ptcs, "WU")
			case "KEY":
				ptcs = append(ptcs, "KE")
				ptcs = append(ptcs, "KY")
			case "DP":
				ptcs = append(ptcs, "DP")
			case "TW":
				ptcs = append(ptcs, "TW")
				ptcs = append(ptcs, "TT")
			case "VTA":
				ptcs = append(ptcs, "TR")
				ptcs = append(ptcs, "NT")
			}

			for _, ptc := range ptcs {
				c.ProductTypeCode = ptc
				gcc := gapClaimContract{
					Code:            c.OriginalCode,
					ProductTypeCode: c.ProductTypeCode,
				}
				if _, ok := gapClaimContracts[gcc]; ok {
					stmt, err := tx.PrepareNamed(updateGapClaimContract)
					if err != nil {
						tx.Rollback()
						logger.Fatalf("error preparing update gap claim contracts. %v\n", err)
					}
					result, err := stmt.Exec(c)
					if err != nil {
						tx.Rollback()
						logger.Fatalf("error updatig gap claim contract for product [%s] contract code [%s] to [%s]. %+v\n", c.ProductTypeCode, c.OriginalCode, c.Code, err)
					}
					updatedRows, err := result.RowsAffected()
					if err != nil {
						updatedRows = 0
					}
					updatedGCC = updatedGCC + int(updatedRows)
					logger.Printf("Updated %d gap claim contracts rows from [%s] to [%s]\n", updatedRows, c.OriginalCode, c.Code)
				}
			}
		}

		if !audit {
			err = tx.Commit()
			if err != nil {
				logger.Fatalf("error commiting changes. %+v\n", err)
			}
			logger.Printf("Synced Product [%s] Contract Code [%s] to [%s]\n", c.ProductTypeCode, c.OriginalCode, c.Code)
		} else {
			// We want to rollback any changes if running in 'audit' mode
			tx.Rollback()
		}
		synced++
	}
}

func loadAutomotiveContractCache() (automotiveContractCache, error) {
	cache := make(automotiveContractCache)
	var contracts []automotiveContract

	err := db.Get().Select(&contracts, automotiveContractQuery)
	if err != nil {
		return nil, errors.Wrap(err, "error getting automotive contracts")
	}

	for _, c := range contracts {
		cache[c] = true
	}

	return cache, nil
}

func loadGapContractCache() (gapContractCache, error) {
	cache := make(gapContractCache)
	var contracts []gapContract

	err := db.Get().Select(&contracts, gapContractQuery)
	if err != nil {
		return nil, errors.Wrap(err, "error getting gap contracts")
	}

	for _, c := range contracts {
		cache[c.Code] = true
	}

	return cache, nil
}

func loadGapClaimContractCache() (gapClaimContractCache, error) {
	cache := make(gapClaimContractCache)
	var contracts []gapClaimContract

	err := db.Get().Select(&contracts, gapClaimContractQuery)
	if err != nil {
		return nil, errors.Wrap(err, "error getting gap claim contracts")
	}

	for _, c := range contracts {
		cache[c] = true
	}

	return cache, nil
}
