package main

import (
	"encoding/csv"
	"flag"
	"fmt"
	"log"
	"os"
	"phizz/db"
	"phizz/sbmigrate"

	"github.com/pkg/errors"
)

var (
	logger    *log.Logger
	auditOnly bool
)

func init() {
	flag.BoolVar(&auditOnly, "auditOnly", false, "will do processing, but not apply changes to the database while still generating reports")
	flag.Parse()
}

func main() {
	var err error

	logFile, err := os.Create("assign-vin.log")
	if err != nil {
		log.Fatalf("error trying to open log file [assign-vin.log] %+v\n", err)
	}

	logger = log.New(logFile, "[Main] ", log.LstdFlags)

	claims, err := loadPhizClaimsCache()
	if err != nil {
		logger.Println(err)
		log.Fatalf("Error getting Claims Cache.\n%v\n", err)
	}

	vins, err := loadWhizContractVinCache()
	if err != nil {
		logger.Println(err)
		log.Fatalf("Error getting Contract Vins Cache.\n%v\n", err)
	}

	var assignedChanges []changeRecord
	var missingVins []changeRecord

	for _, c := range claims {
		if v, ok := vins[c.ContractCode]; ok {
			assignedChanges = append(assignedChanges, changeRecord{
				ClaimID:      c.ID,
				ContractCode: c.ContractCode,
				RO:           c.RO,
				ProductCode:  c.ProductCode,
				Vin:          v,
			})
		} else {
			missingVins = append(missingVins, changeRecord{
				ClaimID:      c.ID,
				ContractCode: c.ContractCode,
				RO:           c.RO,
				ProductCode:  c.ProductCode,
			})
		}
	}

	if len(assignedChanges) > 0 {
		if !auditOnly {
			err = applyChanges(assignedChanges)
			if err != nil {
				logger.Println(err)
				log.Fatalf("Error applying changes\n%v\n", err)
			}
		}

		err = writeReport("AssignedVins", assignedChanges)
		if err != nil {
			logger.Println(err)
			log.Fatalf("Error writing assigned report\n%v\n", err)
		}
	}

	if len(missingVins) > 0 {
		err = writeReport("MissingVins", missingVins)
		if err != nil {
			logger.Println(err)
			log.Fatalf("Error writing missing report\n%v\n", err)
		}
	}

	fmt.Println("Finished Assigning VINs to Automotive Claims missing a Vin.")
}

type contractVin struct {
	ContractCode string `db:"code"`
	Vin          string `db:"vin"`
}

type contractVinCache map[string]string

func loadWhizContractVinCache() (contractVinCache, error) {
	cache := make(contractVinCache)
	var rows []contractVin

	logger.Println("Loading Whiz Contract/Vins...")

	q := `
		select
			c.code
			, vr.vin
		from contracts c
		join vin_records vr on vr.id = c.vin_record_id
	`

	err := db.GetWhiz().Unsafe().Select(&rows, q)
	if err != nil {
		return nil, errors.Wrap(err, "error loading contract vins from whiz db")
	}

	logger.Printf("Loaded [%d] Contract VINs\n", len(rows))

	for _, r := range rows {
		cache[r.ContractCode] = r.Vin
	}

	logger.Println("Loaded Contract Vins cache")

	return cache, nil
}

type claim struct {
	ID           int    `db:"id"`
	ContractCode string `db:"contract_number"`
	RO           string `db:"ro"`
	ProductCode  string `db:"product_code"`
}

type claimCache map[int]claim

func loadPhizClaimsCache() ([]claim, error) {
	var rows []claim

	logger.Println("Loading Phiz Automotive Claims...")

	q := `select id, contract_number, ro, product_code from automotive_claims where vin is null or vin = ''`

	err := db.Get().Unsafe().Select(&rows, q)
	if err != nil {
		return nil, errors.Wrap(err, "error loading claims from phizz db")
	}

	logger.Printf("Loaded [%d] Claims\n", len(rows))
	logger.Println("Loaded Claims cache")

	return rows, nil
}

type changeRecord struct {
	ClaimID      int `db:"claim_id"`
	ContractCode string
	RO           string
	ProductCode  string
	Vin          string `db:"vin"`
}

func (r *changeRecord) toStringArray() []string {
	var cells []string

	cells = append(cells, fmt.Sprintf("%d", r.ClaimID))
	cells = append(cells, r.ContractCode)
	cells = append(cells, r.RO)
	cells = append(cells, r.ProductCode)
	cells = append(cells, r.Vin)

	return cells
}

func getHeaderRow() []string {
	return []string{
		"Claim ID",
		"Contract Code",
		"RO",
		"Product Code",
		"VIN",
	}
}

func writeReport(reportName string, changes []changeRecord) error {
	logger.Printf("Writting CSV Report [%s]...\n", reportName)

	file, err := os.Create(reportName + ".csv")
	if err != nil {
		return errors.Wrapf(err, "there was an error opening csv file for reporting [%s] changes", reportName)
	}
	defer file.Close()

	csvHeaderRow := getHeaderRow()

	writer := csv.NewWriter(file)
	err = writer.Write(csvHeaderRow)
	if err != nil {
		return errors.Wrap(err, "there was an error writing csv header row")
	}
	writer.Flush()

	for _, c := range changes {
		record := c.toStringArray()
		err = writer.Write(record)
		if err != nil {
			return errors.Wrap(err, "there was an error writing record row to csv")
		}
		writer.Flush()
	}

	logger.Printf("Completed writing report [%s]\n", reportName)
	return nil
}

func applyChanges(changes []changeRecord) error {
	logger.Println("Applying Changes")

	tx, err := db.Get().Unsafe().Beginx()
	if err != nil {
		return errors.Wrap(err, "there was an error starting transaction for saving vin changes")
	}

	q := `
		update automotive_claims
		set
			vin = :vin
		where
			id = :claim_id
	`

	stmt, err := tx.PrepareNamed(q)
	if err != nil {
		tx.Rollback()
		return errors.Wrap(err, "error preparing contract vin update statement")
	}
	defer sbmigrate.Close(stmt)

	for _, c := range changes {
		_, err = stmt.Exec(c)
		if err != nil {
			tx.Rollback()
			return errors.Wrap(err, "error updating claim vin")
		}
	}

	err = tx.Commit()
	if err != nil {
		return errors.Wrap(err, "error committing changes for vins")
	}

	logger.Println("Completed Applying Changes")
	return nil
}
