package main

import (
	"flag"
	"fmt"
	"log"
	"os"
	"phizz/sbmigrate"

	"github.com/pkg/errors"
)

// Supported contract migration types
const (
	migrationTypeCentury                = "century_protection" // CPCON
	migrationTypeDrivePur               = "drive_pur"          // DPCON
	migrationTypeTCAGap                 = "tca_gap"            // ABGAP
	migrationTypeMaint                  = "tca_maintenance"    // MAINTCON
	migrationTypeTCAKey                 = "tca_key"            // KECON
	migrationTypeVehicleTheftAssistance = "vehicle_theft"      // TRCON
	migrationTypeTCAService             = "tca_service"        // SC-NAMES, SC.DELETES
)

var (
	input         string
	migrationType string
	parseOnly     bool
)

func init() {
	flag.StringVar(&input, "input", "", "input file to migrate")
	flag.StringVar(&migrationType, "type", "", "migration type (tire_and_wheel | toyota_tire_and_wheel | toyota_wear_and_use | drive_pur | century_protection | tca_gap | vero_gap | tca_maintenance | vehicle_theft | lease_wear_tear)")
	flag.BoolVar(&parseOnly, "parseOnly", false, "will only parse the input file and not put anything into the database")

	flag.Parse()
}

func main() {
	if input == "" {
		log.Fatal("Error: input is required")
	}

	f, err := os.Open(input)
	if err != nil {
		log.Fatalf("error opening input file [%s]\n%s\n", input, err)
	}
	defer sbmigrate.Close(f)

	var migrator sbmigrate.Migrator

	switch migrationType {
	case migrationTypeCentury:
		migrator = sbmigrate.NewMigrator(f, &sbmigrate.CenturyProtection{})
	case migrationTypeTCAGap:
		migrator = sbmigrate.NewMigrator(f, &sbmigrate.TcaGap{})
	case migrationTypeMaint:
		migrator = sbmigrate.NewMigrator(f, &sbmigrate.TcaMaintenance{})
	case migrationTypeTCAKey:
		migrator = sbmigrate.NewMigrator(f, &sbmigrate.TcaKey{})
	case migrationTypeVehicleTheftAssistance:
		migrator = sbmigrate.NewMigrator(f, &sbmigrate.VehicleTheft{})
	case migrationTypeTCAService:
		migrator = sbmigrate.NewMigrator(f, &sbmigrate.TcaService{})
	case migrationTypeDrivePur:
		migrator = sbmigrate.NewMigrator(f, &sbmigrate.DrivePur{})
	default:
		err := errors.Errorf("invalid contract type [%s]", migrationType)
		fmt.Printf("%+v", err)
		return
	}

	timezone, err := sbmigrate.GetMountainStandardTimezone()
	if err != nil {
		log.Fatalf("error getting MST Timezone\n%s\n", err)
	}
	sbmigrate.MountainStandardTimezone = timezone

	err = migrator.Run(parseOnly)
	if err != nil {
		log.Fatalf("error migrating contracts from %s: %+v\n", input, err)
	}
}
