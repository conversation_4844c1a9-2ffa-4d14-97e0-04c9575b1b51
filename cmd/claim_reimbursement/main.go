package main

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/csv"
	"encoding/xml"
	"flag"
	"io"
	"io/ioutil"
	"log"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"phizz/auto"
	"phizz/conf"
	"phizz/db"
	"phizz/dms"
	"phizz/dmsfactory"
	"phizz/handlers"
	"phizz/intacct"

	"github.com/jmoiron/sqlx"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
	"gopkg.in/guregu/null.v3"
)

type complaintPayload struct {
	PartsTotal decimal.Decimal `json:"parts_total" db:"parts_total"`
	LaborTotal decimal.Decimal `json:"labor_total" db:"labor_total"`
}

type claimDB struct {
	ID                    int             `db:"id"`
	ProductCode           string          `db:"product_code"`
	RO                    string          `db:"ro"`
	Status                string          `db:"status"`
	TaxLabor              decimal.Decimal `db:"tax_labor"`
	TaxParts              decimal.Decimal `db:"tax_parts"`
	Estimate              decimal.Decimal `db:"estimate"`
	TotalTax              decimal.Decimal `db:"total_tax"`
	RequestedTotal        decimal.Decimal `db:"requested_total"`
	ContractStoreID       int             `db:"contract_store_id"`
	CreatedByUserID       int             `db:"created_by_user_id"`
	FacilityID            int             `db:"facility_id"`
	PayType               string          `db:"pay_type"`
	CustomerPayeeVendorID null.String     `db:"customer_payee_vendor_id"`
	ActualEstimate        decimal.Decimal `db:"estimate"`
}

func main() {
	ctx := context.Background()
	// Check for alternate tasks which don't involve launching the service
	var jobName string
	createJobFlag(&jobName)
	flag.Parse()
	if jobName != "" {
		execute(ctx, jobName)
		return
	}
}

// execute invokes the specified task
func execute(ctx context.Context, jobName string) {
	if jobName == "reimburse-claim-payment" {
		if flag.NArg() == 0 {
			log.Println("error importing transactions from production")
			err := errors.New("please provide path of the CSV file to import")
			log.Fatalf("%+v\n", err)
		}

		path := flag.Args()
		err := reimburseClaimPayments(ctx, path[0], false)
		if err != nil {
			log.Println("error updating claim payments")
			log.Fatalf("%+v\n", err)
		}
	} else if jobName == "reimburse-claim-payment-preview" {
		if flag.NArg() == 0 {
			log.Println("error importing transactions from production")
			err := errors.New("please provide path of the CSV file to import")
			log.Fatalf("%+v\n", err)
		}

		path := flag.Args()
		err := reimburseClaimPayments(ctx, path[0], true)
		if err != nil {
			log.Println("error updating claim payments")
			log.Fatalf("%+v\n", err)
		}
	} else if jobName == "reimburse-claim-payment-apreceive" {
		if flag.NArg() == 0 {
			log.Println("error importing transactions from production")
			err := errors.New("please provide path of the CSV file to import")
			log.Fatalf("%+v\n", err)
		}

		path := flag.Args()
		err := processAPReceive(ctx, path[0])
		if err != nil {
			log.Println("error updating claim payments")
			log.Fatalf("%+v\n", err)
		}
	} else if jobName != "" {
		log.Fatalf("Unrecognized job: %s", jobName)
	}
}

// CreateJobFlag defines that flag that is used to indicate that
// a task should run instead of the main service.
func createJobFlag(dest *string) {
	flag.StringVar(dest, "job", "", `Run a helper job

Options:
	reimburse-claim-payment - Update claim payments from $0 to actual amount. Needs claims csv file path as parameter.
	reimburse-claim-payment-preview  - Gives csv as output as result without actually changing any data.
	reimburse-claim-payment-apreceive - Process ap-receive for claim payments processed by 'reimburse-claim-payment'. 
			Needs 'result.csv' files path as parameter which is generated by 'reimburse-claim-payment' job.
`)
}

func reimburseClaimPayments(ctx context.Context, p string, isPreview bool) error {
	files, err := getCSVFiles(p)
	if err != nil {
		return err
	}

	for _, file := range files {
		if strings.Contains(file.Name(), "result") {
			continue
		}

		r, err := os.Open(file.Name())
		if err != nil {
			return err
		}
		records, err := readCSVFile(r)
		if err != nil {
			return err
		}
		for i, r := range records {
			actualAmount, err := processPayments(ctx, r, isPreview)

			// Write payment amount and transaction success/failure status to file.
			if i == 0 {
				records[i] = append(records[i], "AdditionalPaidAmount")
			} else {
				records[i] = append(records[i], actualAmount.StringFixed(2))
			}
			if err != nil {
				if i == 0 {
					records[i] = append(records[i], "ProcessingStatus")
				} else {
					records[i] = append(records[i], err.Error())
				}
				log.Printf("%+v\n", err)
			} else {
				records[i] = append(records[i], "Success")
			}
		}
		writeCSVFile(records, isPreview)
	}
	return nil
}

func writeCSVFile(d [][]string, isPreview bool) error {
	// write the file
	fileName := "result.csv"
	if isPreview {
		fileName = "result-preview.csv"
	}
	f, err := os.Create(fileName)
	if err != nil {
		return err
	}
	w := csv.NewWriter(f)
	if err = w.WriteAll(d); err != nil {
		f.Close()
		return err
	}
	return f.Close()
}

func readCSVFile(r io.Reader) ([][]string, error) {
	csvr := csv.NewReader(r)
	records, err := csvr.ReadAll()
	if err != nil {
		return nil, errors.Wrap(err, "error reading file.")
	}
	return records, nil
}

func getCSVFiles(root string) ([]os.FileInfo, error) {
	var files []os.FileInfo
	fileInfo, err := ioutil.ReadDir(root)
	if err != nil {
		return files, err
	}

	for _, file := range fileInfo {
		if file.Mode().IsRegular() && strings.EqualFold(filepath.Ext(file.Name()), ".csv") {
			files = append(files, file)
		}
	}
	return files, nil
}

func processPayments(ctx context.Context, p []string, isPreview bool) (decimal.Decimal, error) {
	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		_ = tx.Rollback()
		return decimal.Zero, errors.Wrap(err, "Unable to create transaction")
	}

	// Get ro and contract from csv row
	roNumber := p[2]
	storeCode := p[0]
	contractNumber := p[3]
	couponLimit, _ := decimal.NewFromString(p[8])

	// Get claim details
	var claim claimDB
	query := `select id, estimate, contract_store_id, ro, customer_payee_vendor_id,
				tax_labor, tax_parts, product_code, created_by_user_id, facility_id, pay_type
				from automotive_claims where contract_number = $1 and ro = $2 `
	err = tx.GetContext(ctx, &claim, query, contractNumber, roNumber)
	if err != nil {
		_ = tx.Rollback()
		return decimal.Zero, err
	}

	query = `select id as contract_store_id from stores where code like $1`
	err = tx.GetContext(ctx, &claim.ContractStoreID, query, storeCode)
	if err != nil {
		_ = tx.Rollback()
		return decimal.Zero, err
	}

	if claim.FacilityID == 0 && claim.PayType != db.PayTypeCustomer {
		_ = tx.Rollback()
		return decimal.Zero, errors.New("Invalid claim details")
	}

	// Get RO Details to see how much actual amount needs to be paid
	var roDetails []dms.RODetail
	roDetails, err = getRO(ctx, claim.ID, claim.ContractStoreID, claim.RO)
	for err != nil {
		roDetails, err = getRO(ctx, claim.ID, claim.ContractStoreID, claim.RO)
		if err != nil && !strings.Contains(err.Error(), "RO Closed Lookup fetch error") {
			_ = tx.Rollback()
			return decimal.Zero, err
		}
	}

	var ro dms.RODetail
	if len(roDetails) > 0 {
		ro = roDetails[0]
	} else {
		_ = tx.Rollback()
		return decimal.Zero, errors.New("RO Not found")
	}

	// Fetch complaints from cdk only if store is available
	var complaints []complaintPayload
	complaints, err = createROComplaints(ro.ROLines, claim.ProductCode)
	if err != nil {
		_ = tx.Rollback()
		return decimal.Zero, errors.New("Failed to add complaints")
	}

	var requestedLaborTotal decimal.Decimal
	var requestedPartsTotal decimal.Decimal
	for _, v := range complaints {
		requestedLaborTotal = requestedLaborTotal.Add(v.LaborTotal)
		requestedPartsTotal = requestedPartsTotal.Add(v.PartsTotal)
	}

	amountToPay := calcMaintenanceValue(claim, requestedPartsTotal, requestedLaborTotal, couponLimit)

	// We dont need to do anything
	if amountToPay.LessThanOrEqual(claim.ActualEstimate) {
		_ = tx.Rollback()
		return decimal.Zero, nil
	}

	// If we already paid till full allowed coupon amount don't need to do anything
	if amountToPay.GreaterThan(claim.ActualEstimate) && claim.ActualEstimate.Equal(couponLimit) {
		_ = tx.Rollback()
		return decimal.Zero, nil
	}

	claim.Estimate = claim.ActualEstimate
	// Calculate what amount we need to post transaction for
	if amountToPay.GreaterThan(claim.Estimate) {
		claim.Estimate = amountToPay.Sub(claim.ActualEstimate)
		claim.ActualEstimate = amountToPay
	}

	if !isPreview {
		// We are only processing maintenance contracts so we dont have to worry about other types
		intacctAccountDetails := conf.Get().IntacctMaintenance

		batch := auto.BatchData{}
		batch.BatchID, err = auto.CreateIntacctBatch(ctx, tx)
		if err != nil {
			_ = tx.Rollback()
			return decimal.Zero, errors.Wrap(err, "Database error creating auto claim batch")
		}

		if claim.PayType == db.PayTypeCustomer {
			if !claim.CustomerPayeeVendorID.Valid || (claim.CustomerPayeeVendorID.Valid && claim.CustomerPayeeVendorID.String == "") {
				_ = tx.Rollback()
				return decimal.Zero, errors.New("Vendor is missing")
			}
			batch.VendorID = claim.CustomerPayeeVendorID.String
		} else {
			batch.VendorID, err = auto.FacilityVendorID(ctx, claim.FacilityID)
			if err != nil || batch.VendorID == "" {
				_ = tx.Rollback()
				return decimal.Zero, errors.Wrap(err, "error getting facilityVendorID for facility "+strconv.Itoa(claim.FacilityID))
			}
		}

		errorMessage, err := auto.UpdateBatchDetails(&batch, claim.FacilityID, claim.ProductCode, claim.PayType, intacctAccountDetails)
		if err != nil {
			_ = tx.Rollback()
			return decimal.Zero, errors.Wrap(err, errorMessage)
		}

		var batchClaims []*auto.ClaimPayload // claim list to be submitted to INTACCT
		// Validate claims and add to claims to batch
		c, err := auto.ClaimSelect(nil, nil, strconv.Itoa(claim.ID))
		if err != nil {
			_ = tx.Rollback()
			return decimal.Zero, errors.Wrap(err, "Error getting auto claim from database for ID:"+strconv.Itoa(claim.ID))
		}

		err = auto.AddClaimToBatch(ctx, tx, c.ID, batch.BatchID, claim.Estimate)
		if err != nil {
			_ = tx.Rollback()
			return decimal.Zero, errors.Wrap(err, "Error adding auto claim to batch:"+strconv.Itoa(claim.ID))
		}
		// We need to use the calculated amount for processing of payment
		c.Estimate = claim.Estimate

		batchClaims = append(batchClaims, c)

		err = tx.Commit()
		if err != nil {
			return decimal.Zero, errors.Wrap(err, "Database error committing transaction for auto claim authorization")
		}

		var authorizedClaimsIDs []int // claim list successfully submitted to INTACCT
		// Authorize maintenance claims as one bill
		if claim.ProductCode == db.ProductCodeMaintenance && claim.PayType == db.PayTypeStore && len(batchClaims) > 0 {
			authorizedClaimsIDs, _, err = claimMaintenanceBatchAuthorize(ctx, batchClaims, &batch, claim.CreatedByUserID, intacctAccountDetails)
			if err != nil {
				return decimal.Zero, errors.Wrap(err, "Failed to authorize maintenance batch with ID: "+strconv.Itoa(batch.BatchID))
			}
		} else {
			// Authorize individual claims, create bills
			for _, c := range batchClaims {
				batchTx, err := db.Get().BeginTxx(ctx, nil)
				if err != nil {
					_ = batchTx.Rollback()
					return decimal.Zero, errors.Wrap(err, "Database error beginning transaction for auto claim authorization")
				}

				err = auto.ClaimAuthorization(ctx, batchTx, c, &batch, claim.CreatedByUserID, intacctAccountDetails)
				if err != nil {
					_ = batchTx.Rollback()
					return decimal.Zero, errors.Wrap(err, "Failed to authorize claim for contract :"+c.ContractNumber)
				}

				_ = auto.ClaimSubmitSuccess(ctx, batchTx, c.ID, batch.BatchID)

				// add success note in claim
				recordNote := auto.RecordNotePayload{
					AutomotiveClaimID: c.ID,
					CreatedByUserID:   123,
					CreatedAt:         time.Now(),
					NotesText:         "INTACCT_BATCH: Submit to Intacct successful in batch:" + strconv.Itoa(batch.BatchID),
				}

				_, _ = auto.InsertRecordNote(ctx, &recordNote, batchTx)

				// Bill was created successfully
				authorizedClaimsIDs = append(authorizedClaimsIDs, c.ID)
				err = batchTx.Commit()
				if err != nil {
					return decimal.Zero, errors.Wrap(err, "Database error committing transaction for auto claim authorization")
				}
			}
		}
		updateClaims(ctx, claim.ActualEstimate, claim.ID)
	}

	_ = tx.Commit()
	return claim.Estimate, nil
}

func updateClaims(ctx context.Context, amount decimal.Decimal, claimID int) error {
	claim := struct {
		Estimate decimal.Decimal `db:"estimate"`
		Status   string          `db:"status"`
		ID       int             `db:"id"`
	}{
		Estimate: amount,
		Status:   db.AutoClaimStatusWaitingForCheck,
		ID:       claimID,
	}
	query := `update automotive_claims
			set estimate =:estimate,
				status =:status
			where id =:id`

	stmt, err := db.Get().PrepareNamed(query)
	if err != nil {
		return errors.Wrap(err, "Error updating Claim, PrepareNamed failed")
	}
	defer func() { _ = stmt.Close() }()
	_, err = stmt.ExecContext(ctx, claim)
	if err != nil {
		return errors.Wrap(err, "Error updating Claim, database error")
	}

	return nil
}

// claimMaintenanceBatchAuthorize methods generates single intacct bill request for maintenance claims batch if payment type is store
// All claims from single batch will have respective auth numbers, payment entries, notes and unidata bill numbers but it will generate single
// bill request to intacct with summation of estimated values of each claim.
func claimMaintenanceBatchAuthorize(ctx context.Context, claims []*auto.ClaimPayload, batch *auto.BatchData, userID int, intacctAccountDetails conf.IntacctAccountDetails) ([]int, []string, error) {
	claimIDs := []int{}
	batchErrors := []string{}
	tx, err := db.Get().Beginx()
	if err != nil {
		return claimIDs, batchErrors, errors.Wrap(err, "Database error beginning transaction for auto claim authorization")
	}

	var loc = handlers.LoadLocOrPanic(db.TimeZoneMountain)
	currentDate := time.Now().In(loc).Format("01-02-2006_15:04:05")
	batch.IntacctBillNumber = batch.FacilityCode + "_" + currentDate
	batch.Memo = batch.IntacctBillNumber

	paymentDetails := auto.ClaimPayload{}

	var authNumbers []int
	for _, claim := range claims {
		// Create bill number for unidata
		// TODO: Need to update createBillNumber function to create unique rev_based bill number
		unidataBillNumber, err := auto.CreateBillNumber(ctx, tx, claim, batch.FacilityCode, claim.ContractNumber)
		if err != nil {
			_ = tx.Rollback()
			return claimIDs, batchErrors, errors.Wrap(err, "Failed to create bill number.")
		}

		authNumber := 0
		authQuery := `insert into automotive_claim_payments(automotive_claim_id, unidata_bill_number) values($1, $2) returning authorization_number`
		row := tx.QueryRowContext(ctx, authQuery, claim.ID, unidataBillNumber)
		err = row.Scan(&authNumber)
		if err != nil {
			_ = tx.Rollback()
			return claimIDs, batchErrors, errors.Wrap(err, "Database error inserting new payment request")
		}
		authNumbers = append(authNumbers, authNumber)

		updateClaimStatus := `update automotive_claims set status = $1 where id = $2`
		_, err = tx.ExecContext(ctx, updateClaimStatus, db.AutoClaimStatusWaitingForCheck, claim.ID)
		if err != nil {
			_ = tx.Rollback()
			return claimIDs, batchErrors, errors.Wrap(err, "Error updating auto claim status")
		}

		// Add entry in update table for audit trail
		err = auto.ClaimUpdated(ctx, tx, claim.ID, userID)
		if err != nil {
			_ = tx.Rollback()
			return claimIDs, batchErrors, errors.Wrap(err, "Error inserting auto_claim_updates")
		}

		// update batch entry
		err = auto.ClaimSubmitSuccess(ctx, tx, claim.ID, batch.BatchID)
		if err != nil {
			_ = tx.Rollback()
			return claimIDs, batchErrors, errors.Wrap(err, "Error updating batch entry")
		}

		// add success note in claim
		recordNote := auto.RecordNotePayload{
			AutomotiveClaimID: claim.ID,
			CreatedByUserID:   userID,
			CreatedAt:         time.Now(),
			NotesText:         "INTACCT_BATCH: Submit to Intacct successful in batch:" + strconv.Itoa(batch.BatchID),
		}

		_, err = auto.InsertRecordNote(ctx, &recordNote, tx)
		if err != nil {
			_ = tx.Rollback()
			return claimIDs, batchErrors, errors.Wrap(err, "Error adding batch record note")
		}
		// Bill was created successfully
		claimIDs = append(claimIDs, claim.ID)
		paymentDetails.Estimate = paymentDetails.Estimate.Add(claim.Estimate)
	}

	claimPayment, err := auto.SubmitBillsToIntacct(ctx, tx, &paymentDetails, batch, intacctAccountDetails)
	if err != nil {
		_ = tx.Rollback()
		return claimIDs, batchErrors, err
	}

	err = tx.Commit()
	if err != nil {
		return claimIDs, batchErrors, errors.Wrap(err, "Database error committing transaction for auto claim authorization")
	}

	queryPaymentUpdate := `update automotive_claim_payments
			  set batch_key = ?,
				  bill_key = ?,
				  intacct_bill_number = ?,
				  bill_memo = ?
			  where authorization_number in (?)`
	queryPaymentUpdate, args, err := sqlx.In(queryPaymentUpdate, claimPayment.BatchKey, claimPayment.BillKey, claimPayment.BillNumber, claimPayment.BillMemo, authNumbers)
	if err != nil {
		batchErrors = append(batchErrors, err.Error())
	}
	queryPaymentUpdate = db.Get().Rebind(queryPaymentUpdate)
	_, err = db.Get().ExecContext(ctx, queryPaymentUpdate, args...)
	if err != nil {
		batchErrors = append(batchErrors, err.Error())
	}

	return claimIDs, batchErrors, nil
}

// get RO details from CDK
func getRO(ctx context.Context, claimID int, storeID int, roNumber string) ([]dms.RODetail, error) {
	err := db.Get().GetContext(ctx, &claimID, `select id from automotive_claims where id = $1`, claimID)
	if err != nil {
		return nil, errors.Wrap(err, "Claim not found")
	}

	var store db.Store
	err = db.Get().Unsafe().GetContext(
		ctx,
		&store,
		`select * from stores where id = $1`,
		storeID,
	)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.Wrap(err, "Store with DMS provider and RO integration not found")
		}
		return nil, errors.Wrap(err, "Database error getting store for DMS RO lookup")
	}
	if !store.HasROIntegration {
		return nil, errors.Wrap(err, "Store with DMS provider and has RO integration not found")
	}
	var roDetails []dms.RODetail
	switch store.DMSProvider {
	case db.DMSProviderCDK:
		roDetails, err = dmsfactory.RODetail(ctx, &store, roNumber)
		if err != nil {
			return nil, err
		}
	default:
		return nil, errors.Wrap(err, "Store with DMS provider and has RO integration not found")
	}
	return roDetails, nil
}

func createROComplaints(roLines []*dms.ROLine, productCode string) ([]complaintPayload, error) {
	var newComplaints []complaintPayload

	cdkLaborTypes := db.CDKLaborTypes
	if productCode == db.ProductCodeMaintenance {
		cdkLaborTypes = append(cdkLaborTypes, db.CDKLaborTypeCELCXMaintOnly)
	}

	for _, roLine := range roLines {
		var laborTotal decimal.Decimal
		var partTotal decimal.Decimal
		// The labor types that will apply to TCA are CLCA, CLCM, CLCE, CLCX, CLCAR, CLCER, CLCL
		if isRelevantCDKComplaint(cdkLaborTypes, roLine.LaborTypes) {
			complaint := complaintPayload{}
			for _, roLabor := range roLine.Labors {
				if contains(cdkLaborTypes, roLabor.LaborType) {
					laborTotal = laborTotal.Add(roLabor.LaborSale)
				}
			}

			for _, roPart := range roLine.Parts {
				partTotal = partTotal.Add(roPart.PartsSale)
			}
			complaint.LaborTotal = laborTotal
			complaint.PartsTotal = partTotal
			newComplaints = append(newComplaints, complaint)
		}
	}
	return newComplaints, nil
}

// check if any of labor type matches with any of valid labortype
func isRelevantCDKComplaint(cdkLaborTypes []string, laborTypes []string) bool {
	for _, laborType := range laborTypes {
		if contains(cdkLaborTypes, laborType) {
			return true
		}
	}
	return false
}

func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

func calcMaintenanceValue(c claimDB, partsAmount decimal.Decimal, laborAmount decimal.Decimal, couponLimit decimal.Decimal) decimal.Decimal {
	verifiedPartsAmount := decimal.Zero
	verifiedLaborAmount := decimal.Zero
	verifiedTotalPlusTax := decimal.Zero

	// 1. Calculate highest_tax_rate = greater of parts_tax_rate or labor_tax_rate.
	highestTaxRate := decimal.Zero
	if c.TaxParts.GreaterThan(decimal.Zero) || c.TaxLabor.GreaterThan(decimal.Zero) {
		highestTaxRate = c.TaxParts
		if c.TaxLabor.GreaterThan(highestTaxRate) {
			highestTaxRate = c.TaxLabor
		}
	}
	// 2.Calculate requestedTotal = PartsAmount + LaborAmount
	requestedTotal := partsAmount.Add(laborAmount)
	//3. Calculate parts_plus_tax = requested_parts_amount + (requested_parts_amount * parts_tax_rate).
	partsPlusTax := partsAmount.Add(partsAmount.Mul(c.TaxParts.Div(decimal.NewFromFloat(100))))
	//4. Calculate labor_plus_tax = requested_labor_amount + (requested_labor_amount * labor_tax_rate).
	laborPlusTax := laborAmount.Add(laborAmount.Mul(c.TaxLabor.Div(decimal.NewFromFloat(100))))
	//5. Calculate total_requested_plus_tax = parts_plus_tax + labor_plus_tax.
	requestedTotalPlusTax := partsPlusTax.Add(laborPlusTax)
	//6. Calculate parts_minus_tax = requested_parts_amount / (1 + parts_tax_rate).
	partsMinusTax := partsAmount.Div(decimal.NewFromFloat(1).Add(c.TaxParts.Div(decimal.NewFromFloat(100))))
	//7. Calculate labor_minus_tax = requested_labor_amount / (1 + labor_tax_rate).
	laborMinusTax := laborAmount.Div(decimal.NewFromFloat(1).Add(c.TaxLabor.Div(decimal.NewFromFloat(100))))
	//8. Calculate total_requested_minus_tax = parts_minus_tax + labor_minus_tax.
	requestedTotalMinusTax := partsMinusTax.Add(laborMinusTax)

	if requestedTotal.LessThanOrEqual(couponLimit) {
		return requestedTotalPlusTax
	} else if requestedTotalMinusTax.Equal(couponLimit) {
		return requestedTotal
	} else {
		// Verify that parts and labor amounts don't exceed coupon value.
		// Figure out highest tax category.
		if c.TaxParts.Div(decimal.NewFromFloat(100)).GreaterThanOrEqual(c.TaxLabor.Div(decimal.NewFromFloat(100))) {
			// Parts tax is highest.
			// Use parts amount up to coupon value with no labor amount.
			if partsAmount.LessThanOrEqual(couponLimit) {
				verifiedPartsAmount = partsAmount

				remaining := couponLimit.Sub(verifiedPartsAmount)

				// If there's a Labor amount, then use that amount up to the remaining left over.
				if laborAmount.GreaterThan(decimal.Zero) {
					if laborAmount.LessThanOrEqual(remaining) {
						verifiedLaborAmount = laborAmount
					} else {
						verifiedLaborAmount = remaining
					}
				}
			} else {
				verifiedPartsAmount = couponLimit
				verifiedLaborAmount = decimal.Zero
			}

			verifiedPartsPlusTax := verifiedPartsAmount.Add(verifiedPartsAmount.Mul(c.TaxParts.Div(decimal.NewFromFloat(100))))
			verifiedTotalPlusTax = verifiedPartsPlusTax.Add(verifiedLaborAmount)
		} else {
			// Labor tax is highest.
			// Use labor amount up to coupon value with no parts amount.
			if laborAmount.LessThanOrEqual(couponLimit) {
				verifiedLaborAmount = laborAmount
				remaining := couponLimit.Sub(verifiedLaborAmount)

				// If there's a Parts amount, then use that amount up to the remaining left over.
				if partsAmount.GreaterThan(decimal.Zero) {
					if partsAmount.LessThanOrEqual(remaining) {
						verifiedPartsAmount = partsAmount
					} else {
						verifiedPartsAmount = remaining
					}
				}
			} else {
				verifiedLaborAmount = couponLimit
				verifiedPartsAmount = decimal.Zero
			}
			verifiedLaborPlusTax := verifiedLaborAmount.Add(verifiedLaborAmount.Mul(c.TaxLabor.Div(decimal.NewFromFloat(100))))
			verifiedTotalPlusTax = verifiedLaborPlusTax.Add(verifiedPartsAmount)
		}
	}

	return verifiedTotalPlusTax
}

type paymentInfo struct {
	ID                  int             `db:"id"`
	AutomotiveClaimID   int             `db:"automotive_claim_id"`
	UnidataClaimNumber  int             `db:"unidata_claim_number"`
	ContractNumber      string          `db:"contract_number"`
	CheckNumber         string          `db:"check_number"`
	Amount              decimal.Decimal `db:"amount"`
	PaidDate            time.Time       `db:"paid_date"`
	ProductCode         string          `db:"product_code"`
	RO                  string          `db:"ro"`
	Estimate            decimal.Decimal `db:"estimate"`
	IntacctBillNumber   string          `db:"intacct_bill_number"`
	UnidataBillNumber   string          `db:"unidata_bill_number"`
	BillKey             int             `db:"bill_key"`
	UserID              string          `db:"owner_id"`
	PayType             string          `db:"pay_type"`
	AuthorizationNumber int             `db:"authorization_number"`
	ClaimType           string          `db:"claim_type"`
	SBRecordKey         string          `db:"sb_record_key"`
}

func processAPReceive(ctx context.Context, p string) error {
	files, err := getCSVFiles(p)
	if err != nil {
		return err
	}

	for _, file := range files {
		if file.Name() != "result.csv" {
			continue
		}
		r, err := os.Open(file.Name())
		if err != nil {
			return err
		}
		records, err := readCSVFile(r)
		if err != nil {
			return err
		}
		for i, r := range records {
			if i == 0 {
				continue
			}
			err := apReceive(ctx, r)
			if err != nil {
				log.Println("Error updating Automotive claim paid info", err)
			}
		}
	}
	return nil
}

func apReceive(ctx context.Context, r []string) error {
	var pendingBatchPayments []struct {
		BillKey       int             `db:"bill_key"`
		BatchEstimate decimal.Decimal `db:"batch_estimate"`
		BillMemo      string          `db:"bill_memo"`
		Amount        decimal.Decimal `db:"amount"`
	}

	contractNumber := r[3]
	query := `select
                distinct (automotive_claim_payments.bill_key) as bill_key,
                sum(automotive_claims.estimate) as batch_estimate,
                bill_memo,
				case when sum(automotive_claim_payments.amount) is null then 0.0 else sum(automotive_claim_payments.amount) end as amount
			 from automotive_claims join automotive_claim_payments on automotive_claims.id = automotive_claim_id
			where is_complete = false and pay_type != $1 and product_code = $2 and contract_number in ($3)
			 group by bill_key, bill_memo;`
	err := db.Get().SelectContext(ctx, &pendingBatchPayments, query, db.PayTypeCreditCard, db.ProductCodeMaintenance, contractNumber)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil
		}
		return errors.Wrap(err, "Error loading Automotive claim payment from database.")
	}
	for _, payment := range pendingBatchPayments {
		if payment.Amount.LessThan(decimal.Zero) {
			continue
		}
		// Note : batchPayments is a list of checks generated for particular batch
		// Note : batchCredits is a list of credits applied on particular batch. Tip : When claim is reversed, reversed amount gets reduced from total vendor's payable amount.
		batchPayments, _, err := intacctPaidInfo(ctx, payment.BillKey)
		if err != nil {
			log.Println("Error getting MC batch payment details for Bill Number:", payment.BillKey, err)
			continue
		}

		// Get total payment made to vendor in this bill
		totalPaidAmount := decimal.Zero
		var batchChecks []string
		var batchLastPaidDate time.Time
		for _, payment := range batchPayments {
			totalPaidAmount = totalPaidAmount.Add(payment.Amount)
			batchChecks = append(batchChecks, payment.CheckNumber)
			batchLastPaidDate = payment.PaidDate
		}
		// If batch payment is complete then update payment information to all claims from that batch
		paymentComplete := true
		var batchClaims []paymentInfo
		claimsQuery := `select
						automotive_claim_payments.id,
						automotive_claim_payments.automotive_claim_id,
						unidata_claim_number,
						automotive_claims.product_code,
						automotive_claims.owner_id,
						automotive_claims.contract_number,
						automotive_claims.ro,
						automotive_claims.estimate,
						automotive_claims.claim_type,
						case when automotive_claims.sb_record_key is null then '' else automotive_claims.sb_record_key end,
						automotive_claim_payments.intacct_bill_number,
						case when automotive_claim_payments.unidata_bill_number is null then '' else automotive_claim_payments.unidata_bill_number end,
						pay_type,
						case when authorization_number is null then 0 else authorization_number end
				from automotive_claims
					join automotive_claim_payments on automotive_claims.id = automotive_claim_id
				where bill_key = $1 and automotive_claims.contract_number = $2`
		err = db.Get().SelectContext(ctx, &batchClaims, claimsQuery, payment.BillKey, contractNumber)
		if err != nil {
			log.Println("Error getting claim details for Bill Number:", payment.BillKey, err)
			continue
		}
		checks := strings.Join(batchChecks, ", ")
		for _, claim := range batchClaims {
			claim.Amount = totalPaidAmount
			claim.CheckNumber = checks
			claim.PaidDate = batchLastPaidDate
			count := 0 // verify check number already updated
			err := db.Get().GetContext(ctx, &count, `select count(id) from automotive_claim_payment_checks where check_number = $1 and check_amount = $2 and automotive_claim_payments_id = $3`, checks, claim.Estimate, claim.ID)
			if err != nil {
				log.Println("Error verifying check details for contract: "+claim.ContractNumber, err)
				paymentComplete = false
				continue
			}
			if count == 0 {
				tx, err := db.Get().Beginx()
				if err != nil {
					log.Println("Database error beginning transaction for Automotive claim payment update for contract "+claim.CheckNumber, err)
					continue
				}
				_, err = tx.ExecContext(ctx, `insert into automotive_claim_payment_checks(automotive_claim_payments_id, check_amount, check_number, paid_date, updated_at) values ($1,$2,$3,$4,now() at time zone 'utc')`,
					claim.ID, claim.Amount, checks, claim.PaidDate)
				if err != nil {
					log.Println("Database error inserting check details for Automotive claim payment update for contract "+claim.CheckNumber, err)
					paymentComplete = false
					_ = tx.Rollback()
					continue
				}

				err = updateClaimPaid(ctx, strconv.Itoa(claim.AutomotiveClaimID))
				if err != nil {
					paymentComplete = false
					_ = tx.Rollback()
					log.Println("Error updating paid info in automotive_claim_payments for claim ", strconv.Itoa(claim.AutomotiveClaimID), err)
				}
				tx.Commit()
			}
		}
		if paymentComplete {
			acpQuery := `update automotive_claim_payments
						set is_complete = true, 
							updated_at = now() at time zone 'utc'
						where bill_key = $1`
			_, err = db.Get().ExecContext(ctx, acpQuery, payment.BillKey)
			if err != nil {
				log.Println("Error updating Automotive claim paid info", err)
			}
		}
	}
	return nil
}

type intacctPaymentDetails struct {
	CheckNumber string
	Amount      decimal.Decimal
	PaidDate    time.Time
	PaymentKey  int
}

type intacctReversedPaymentDetails struct {
	Amount           decimal.Decimal
	PaidDate         time.Time
	BillKey          int
	ParentPaymentKey int
}

func intacctPaidInfo(ctx context.Context, billKey int) ([]intacctPaymentDetails, []intacctReversedPaymentDetails, error) {
	paymentKeys, err := intacct.GetPaymentKeys(ctx, billKey)
	if err != nil {
		return nil, nil, errors.Wrap(err, "Could not get payment key")
	}

	claimPayments := []intacctPaymentDetails{}
	claimCredits := []intacctReversedPaymentDetails{}
	for _, paymentKeyItem := range paymentKeys {
		if paymentKeyItem.State != intacct.BillPaymentStateComplete {
			return nil, nil, errors.Wrapf(intacct.ErrPaymentNotComplete, "Payment not complete for paymentKey %d and bill key %d", paymentKeyItem.PaymentKey, billKey)
		}
		query := " RECORDNO = " + strconv.Itoa(paymentKeyItem.PaymentKey)
		fields := "STATE, DOCUMENTNUMBER"
		requestXML, err := intacct.GetReadByQueryXML(query, "appayment", fields)
		if err != nil {
			return nil, nil, errors.Wrap(err, "Creating XML request for getting payment details failed")
		}
		response, err := intacct.Request(ctx, requestXML)
		if err != nil {
			return nil, nil, errors.Wrap(err, "Intacct Server communication error")
		}

		type apPaymentType struct {
			State          string `xml:"STATE"`
			DocumentNumber string `xml:"DOCUMENTNUMBER"`
		}
		type data struct {
			Count     int             `xml:"count,attr"`
			ApPayment []apPaymentType `xml:"appayment"`
		}
		type result struct {
			Status string `xml:"status"`
			Data   data   `xml:"data"`
		}
		type operation struct {
			Result result `xml:"result"`
		}

		resApPayment := struct {
			Operation operation `xml:"operation"`
		}{}

		apResBuf := bytes.NewBuffer(response)
		err = xml.NewDecoder(apResBuf).Decode(&resApPayment)
		if err != nil {
			return nil, nil, errors.Wrap(err, "Failed to decode getPaymentDetailsFromIntacct response")
		}

		if resApPayment.Operation.Result.Status != intacct.ResultSuccess {
			return nil, nil, errors.New("Failed to get appayment from Intacct")
		}
		if resApPayment.Operation.Result.Data.Count == 1 {
			var apPayment apPaymentType
			apPayment = resApPayment.Operation.Result.Data.ApPayment[0]
			if apPayment.State != intacct.PaymentStateComplete {
				continue
			}
			claimPayment := intacctPaymentDetails{}
			claimPayment.CheckNumber = apPayment.DocumentNumber
			claimPayment.PaidDate = intacct.ToTime(paymentKeyItem.PaymentDate)
			claimPayment.Amount = paymentKeyItem.Amount
			claimPayment.PaymentKey = paymentKeyItem.PaymentKey
			claimPayments = append(claimPayments, claimPayment)
		} else {
			// payment details without check number
			claimCredit := intacctReversedPaymentDetails{}
			claimCredit.Amount = paymentKeyItem.Amount
			claimCredit.PaidDate = intacct.ToTime(paymentKeyItem.PaymentDate)
			claimCredit.BillKey = paymentKeyItem.PaymentKey // In case of negative claims, paymentkey is actual bill key of negative claim
			claimCredit.ParentPaymentKey = paymentKeyItem.ParentPaymentKey
			claimCredits = append(claimCredits, claimCredit)
		}
	}
	if len(claimPayments) == 0 && len(claimCredits) == 0 {
		return nil, nil, errors.Wrapf(intacct.ErrPaymentNotComplete, "Payment not complete for billKey"+strconv.Itoa(billKey))
	}

	return claimPayments, claimCredits, nil
}

func updateClaimPaid(ctx context.Context, autoClaimID string) error {
	tx, err := db.Get().Beginx()
	if err != nil {
		return errors.Wrap(err, "Database error beginning transaction for Automotive claim payment update")
	}

	laborQ := `select case when sum(approved) is null then 0.0 else sum(approved) end
		from automotive_claim_complaint_labors
		where automotive_claim_complaint_id in (select id from automotive_claim_complaints where automotive_claim_id = $1)`
	totalLabor := 0.0
	err = tx.GetContext(ctx, &totalLabor, laborQ, autoClaimID)
	if err != nil {
		_ = tx.Rollback()
		return errors.Wrap(err, "Error while getting total labor for claim id #"+autoClaimID)
	}

	partsQ := `select case when sum(approved) is null then 0.0 else sum(approved) end
		from automotive_claim_complaint_parts
		where automotive_claim_complaint_id in (select id from automotive_claim_complaints where automotive_claim_id = $1)`

	totalParts := 0.0
	err = tx.GetContext(ctx, &totalParts, partsQ, autoClaimID)
	if err != nil {
		_ = tx.Rollback()
		return errors.Wrap(err, "Error while getting total parts for claim id#"+autoClaimID)
	}

	acQuery := `update automotive_claims set status = $1, total_labor=$2, total_parts=$3 where id = $4`
	_, err = tx.ExecContext(ctx, acQuery, db.AutoClaimStatusCheckWritten, totalLabor, totalParts, autoClaimID)
	if err != nil {
		_ = tx.Rollback()
		return errors.Wrap(err, "Error updating Auotomotive claim paid info")
	}
	err = tx.Commit()
	return err
}
