package main

import (
	"context"
	"log"

	"phizz/auto"
	"phizz/gap"
	"phizz/lwt"
	"phizz/vta"
)

func main() {
	ctx := context.Background()
	_, err := gap.ReceiveFromIntacct(ctx)
	if err != nil {
		log.Printf("Error in executing ReceiveFromIntacct for GAP claims: %+v\n", err)
	}

	_, err = auto.ReceiveFromIntacct(ctx)
	if err != nil {
		log.Printf("Error in executing ReceiveFromIntacct for Auto claims: %+v\n", err)
	}

	_, err = auto.ReceiveMCBatchesFromIntacct(ctx)
	if err != nil {
		log.Printf("Error in executing ReceiveFromIntacct for maintenance claims: %+v\n", err)
	}

	_, err = lwt.ReceiveFromIntacct(ctx)
	if err != nil {
		log.Printf("Error in executing ReceiveFromIntacct for LWT claims: %+v\n", err)
	}

	_, err = vta.ReceiveFromIntacct(ctx)
	if err != nil {
		log.Printf("Error in executing ReceiveFromIntacct for VTA claims: %+v\n", err)
	}
}
