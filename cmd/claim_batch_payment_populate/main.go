package main

import (
	"context"
	"database/sql"
	"log"
	"phizz/db"
	"sync"

	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
)

type paymentPayload struct {
	ID                       int             `json:"id" db:"id"`
	AutomotiveClaimID        int             `json:"automotive_claim_id" db:"automotive_claim_id"`
	Amount                   decimal.Decimal `json:"amount" db:"amount"`
	AutomotiveIntacctBatchID int             `json:"automotive_intacct_batch_id" db:"automotive_intacct_batch_id"`
	ProductCode              string          `json:"product_code" db:"product_code"`
}

type result struct {
	TotalRows   int
	SuccessRows int
	ErrorRows   int
}

var claimPaymentsQuery = `select acp.id,
			   acp.automotive_claim_id,
			   case when ac.product_code = 'MNT' then coalesce(acp.amount, 0) else coalesce(ac.estimate, 0) end amount,
			   ac.product_code
		from automotive_claim_payments acp
			join automotive_claims ac
				on acp.automotive_claim_id = ac.id
			left join automotive_intacct_batch_details aibd
				on acp.automotive_claim_id = aibd.automotive_claim_id
		where (ac.chargeback or aibd.amount > 0)
		  and (ac.chargeback or acp.amount is null or acp.amount > 0)
		group by acp.id, ac.id
		order by acp.id desc`

var negativeClaimPaymentQuery = `select acp.id,
				acp.automotive_claim_id,
				coalesce(acp.amount, 0) amount,
				ac.product_code
			from automotive_claim_payments acp
				join automotive_claims ac
					on acp.automotive_claim_id = ac.id
				left join automotive_claim_payment_checks acpc
					on acp.id = acpc.automotive_claim_payments_id
				left join automotive_intacct_batch_details aibd
					on (acp.automotive_claim_id = aibd.automotive_claim_id
						and aibd.amount =  coalesce(acp.amount, acpc.check_amount))
				where acp.amount < 0
				order by acp.id desc`

func main() {
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Process the data
	process(ctx)
}

func process(ctx context.Context) {
	wg := new(sync.WaitGroup)

	wg.Add(1)
	// Start claim payments process if issues just print error and continue
	go func() {
		defer wg.Done()

		rows, err := getClaimPayments(ctx, claimPaymentsQuery)
		if err != nil {
			log.Println("error getting claims payments for batches")
			log.Println(err)
		}
		defer rows.Close()

		res := processClaimPayments(ctx, rows)

		log.Printf("Total Rows Processed for Claim Payment: %d", res.TotalRows)
		log.Printf("Sucessfull Rows Processed for Claim Payment: %d", res.SuccessRows)
		log.Printf("Error Rows for Claim Payment: %d", res.ErrorRows)
	}()

	wg.Add(1)
	go func() {
		defer wg.Done()

		rows, err := getClaimPayments(ctx, negativeClaimPaymentQuery)
		if err != nil {
			log.Println("error getting negative claims payments for batches")
			log.Println(err)
		}
		defer rows.Close()

		res := processClaimPayments(ctx, rows)

		log.Printf("Total Rows Processed for Negative Claim Payment: %d", res.TotalRows)
		log.Printf("Sucessfull Rows Processed for Negative Claim Payment: %d", res.SuccessRows)
		log.Printf("Error Rows for Negative Claim Payment: %d", res.ErrorRows)
	}()
	wg.Wait()
}

func getClaimPayments(ctx context.Context, query string) (*sql.Rows, error) {
	rows, err := db.Get().QueryContext(ctx, query)
	if err != nil {
		if err == sql.ErrNoRows {
			return rows, errors.New("The Automotive claim payment was not found")
		}
		return rows, errors.Wrap(err, "Error loading Automotive claim payments from database.")
	}
	return rows, nil
}

func processClaimPayments(ctx context.Context, rows *sql.Rows) result {
	res := result{
		TotalRows:   0,
		SuccessRows: 0,
		ErrorRows:   0,
	}
	// Since we have around couple million records to process its good idea to stream the data rather than selecting everything
	for rows.Next() {
		var paymentDetail paymentPayload
		err := rows.Scan(&paymentDetail.ID, &paymentDetail.AutomotiveClaimID, &paymentDetail.Amount, &paymentDetail.ProductCode)
		if err != nil {
			log.Println("Error scanning Automotive claim payments details from database.")
			log.Println(err)
			// We want to continue processing for remaining rows
		}

		// Get the payment amount from check if no check then we will use amount form payments table
		paymentDetail.Amount = getTotalPaymentAmount(ctx, paymentDetail)

		// Get batch id from amount paid
		paymentDetail.AutomotiveIntacctBatchID = getBatchID(ctx, paymentDetail)

		// Udpate batch details table with payment id
		err = processBatchUpdate(ctx, paymentDetail)
		if err != nil {
			log.Println("Error scanning Automotive claim payments details from database.")
			log.Println(err)
			res.ErrorRows++
			// We want to continue processing for remaining rows
		}
		res.SuccessRows++
		res.TotalRows++
	}
	return res
}

func getTotalPaymentAmount(ctx context.Context, paymentDetail paymentPayload) decimal.Decimal {
	var totalAmount decimal.Decimal
	if paymentDetail.ProductCode != "MNT" {
		totalAmount = paymentDetail.Amount
	} else {
		var checkAmount []decimal.Decimal
		err := db.Get().SelectContext(ctx, &checkAmount, `select check_amount from automotive_claim_payment_checks where automotive_claim_payments_id = $1`, paymentDetail.ID)
		if err != nil {
			log.Println("Error loading Automotive claim payments check details from database.")
			log.Fatal(err)
		}

		// We want to get correct batch details only if we have more than one payment
		if len(checkAmount) > 0 {
			for _, amt := range checkAmount {
				totalAmount = totalAmount.Add(amt)
			}
		} else {
			totalAmount = paymentDetail.Amount
		}
	}
	return totalAmount
}

func getBatchID(ctx context.Context, paymentDetail paymentPayload) int {
	var batchID int
	batchQuery := `select aibd.automotive_intacct_batch_id 
				from automotive_claim_payments acp
					left join automotive_claim_payment_checks acpc
						on acp.id = acpc.automotive_claim_payments_id
					join automotive_intacct_batch_details aibd
						on (acp.automotive_claim_id = aibd.automotive_claim_id 
							and aibd.amount =  coalesce(acp.amount, acpc.check_amount))
				where acp.automotive_claim_id = $1
					and aibd.amount = $2`
	err := db.Get().GetContext(ctx, &batchID, batchQuery, paymentDetail.AutomotiveClaimID, paymentDetail.Amount)
	if err != nil && err != sql.ErrNoRows {
		log.Println("Error loading Automotive claim payments batch details from database.")
		log.Fatal(err)
	}

	// If negative credit is applied on the batch then amounts are mismatch so we will run the join on the claim amount
	// We are avoiding the adding join with or condition on above query and make the both same query because there is and
	// chance that two payments/batches could have same amount in that case any random batch will get assigned so we will
	// do the query seprately
	if err == sql.ErrNoRows {
		batchQuery = `select automotive_intacct_batch_id 
				from automotive_claim_payments acp
					left join automotive_claim_payment_checks acpc
						on acp.id = acpc.automotive_claim_payments_id
					join automotive_intacct_batch_details aibd
						on (acp.automotive_claim_id = aibd.automotive_claim_id 
							and aibd.amount =  $2)
				where acp.automotive_claim_id = $1
					and aibd.amount = $2`

		err := db.Get().GetContext(ctx, &batchID, batchQuery, paymentDetail.AutomotiveClaimID, paymentDetail.Amount)
		if err != nil && err != sql.ErrNoRows {
			log.Println("Error loading Automotive claim payments batch details from database.")
			log.Fatal(err)
		}

		if err == sql.ErrNoRows {
			return 0
		}
	}
	return batchID
}

func processBatchUpdate(ctx context.Context, payment paymentPayload) error {
	updateQuery := `update automotive_intacct_batch_details set automotive_claim_payment_id = $1 
					where automotive_claim_id= $2 and automotive_intacct_batch_id = $3`
	_, err := db.Get().ExecContext(ctx, updateQuery, payment.ID, payment.AutomotiveClaimID, payment.AutomotiveIntacctBatchID)
	if err != nil {
		log.Printf("Failed to udpate for AutomotiveClaimID: %d, AuotmotiveIntacctBatchID: %d", payment.AutomotiveClaimID, payment.AutomotiveIntacctBatchID)
		return err
	}
	return nil
}
