package main

import (
	"database/sql"
	"log"
	"time"

	"phizz/db"
)

func main() {
	var contractNumbers []string
	count := 0

	query := `select distinct contract_number from gap_claims where parent_claim_id is not null and is_child = true`
	err := db.Get().Select(&contractNumbers, query)
	if err != nil && err != sql.ErrNoRows {
		log.Fatalf("error getting child claims")
	}

	start := time.Now()
	defer func() {
		elapsed := time.Since(start)
		log.Printf("Updated %d claims in %v.\n", count, elapsed)
		log.Printf("Skipped %d claims.", len(contractNumbers)-count)
	}()

	log.Printf("Updating %d child claims", len(contractNumbers))

	for _, contract := range contractNumbers {
		tx, err := db.Get().Beginx()
		if err != nil {
			log.Fatalf("error getting database transaction. %+v\n", err)
		}

		updateClaim := `update gap_claims set (date_of_loss, first_payment_date) = (
    				select date_of_loss, first_payment_date from gap_claims where is_child = false and contract_number = $1)
				where is_child = true and contract_number = $1`

		_, err = tx.Exec(updateClaim, contract)
		if err != nil {
			_ = tx.Rollback()
			log.Fatalf("error updating claim %s", contract)
		}

		err = tx.Commit()
		if err != nil {
			log.Fatalf("error commiting changes. %+v\n", err)
		}
		count++
		log.Printf("Successfully updated claim %s", contract)
	}
}
