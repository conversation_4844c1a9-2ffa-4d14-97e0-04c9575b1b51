package main

import (
	"context"
	"database/sql"
	"encoding/csv"
	"flag"
	"io"
	"io/ioutil"
	"log"
	"os"
	"path/filepath"
	"strings"
	"time"

	"phizz/db"
	"phizz/dms"
	"phizz/dmsfactory"

	"github.com/jmoiron/sqlx"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
)

const getClaimQuery = `select id, product_code, status, estimate,
			case when tax_parts is null then 0.0 else tax_parts end,
			case when tax_labor is null then 0.0 else tax_labor end,
			case when ro is null then '' else ro end,
			case when contract_store_id is null then 0 else contract_store_id end,
			created_by_user_id
		from automotive_claims
		where contract_number = $1 and status = $2 and estimate = $3`

type complaintPayload struct {
	ID                  int             `json:"id" db:"id"`
	ComplaintDate       time.Time       `json:"complaint_date" db:"complaint_date"`
	AutomotiveClaimID   int             `json:"automotive_claim_id" db:"automotive_claim_id"`
	Complaint           string          `json:"complaint" db:"complaint"`
	TechID              string          `json:"tech_id" db:"tech_id"`
	Cause               string          `json:"cause" db:"cause"`
	Correction          string          `json:"correction" db:"correction"`
	RepairCode          string          `json:"repair_code" db:"repair_code"`
	AddLineFlag         bool            `json:"add_line_flag" db:"add_line_flag"`
	GoodwillFlag        bool            `json:"goodwill_flag" db:"goodwill_flag"`
	GoodwillDescription string          `json:"goodwill_description" db:"goodwill_description"`
	GoodwillAmount      decimal.Decimal `json:"goodwill_amount" db:"goodwill_amount"`
	Status              string          `json:"status" db:"status"`
	PartsTotal          decimal.Decimal `json:"parts_total" db:"parts_total"`
	LaborTotal          decimal.Decimal `json:"labor_total" db:"labor_total"`
	Towing              decimal.Decimal `json:"towing" db:"towing"`
	Rental              decimal.Decimal `json:"rental" db:"rental"`
	Sublet              decimal.Decimal `json:"sublet" db:"sublet"`
	Parts               []partPayload   `json:"parts" db:"-"`
	Labor               []laborPayload  `json:"labors" db:"-"`
	IsManual            bool            `json:"is_manual" db:"is_manual"`
	LineCode            string          `json:"line_code" db:"line_code"`
	UnidataRepairNumber int             `json:"_" db:"unidata_repair_number"`
}

type partPayload struct {
	ID                         int             `json:"id" db:"id"`
	AutomotiveClaimComplaintID int             `json:"automotive_claim_complaint_id" db:"automotive_claim_complaint_id"`
	PartNumber                 string          `json:"part_number" db:"part_number"`
	Description                string          `json:"description" db:"description"`
	Quantity                   int             `json:"quantity" db:"quantity"`
	MSRP                       decimal.Decimal `json:"msrp" db:"msrp"`
	Cost                       decimal.Decimal `json:"cost" db:"cost"`
	Requested                  decimal.Decimal `json:"requested" db:"requested"`
	Approved                   decimal.Decimal `json:"approved" db:"approved"`
	Notes                      string          `json:"notes" db:"notes"`
}

type laborPayload struct {
	ID                         int             `json:"id" db:"id"`
	AutomotiveClaimComplaintID int             `json:"automotive_claim_complaint_id" db:"automotive_claim_complaint_id"`
	LaborDescription           string          `json:"labor_description" db:"labor_description"`
	Requested                  decimal.Decimal `json:"requested" db:"requested"`
	Hours                      decimal.Decimal `json:"hours" db:"hours"`
	Rate                       decimal.Decimal `json:"rate" db:"rate"`
	Billed                     decimal.Decimal `json:"billed" db:"billed"`
	Approved                   decimal.Decimal `json:"approved" db:"approved"`
	Notes                      string          `json:"notes" db:"notes"`
}

type claimDB struct {
	ID              int             `db:"id"`
	ProductCode     string          `db:"product_code"`
	RO              string          `db:"ro"`
	Status          string          `db:"status"`
	TaxLabor        decimal.Decimal `db:"tax_labor"`
	TaxParts        decimal.Decimal `db:"tax_parts"`
	Estimate        decimal.Decimal `db:"estimate"`
	TotalTax        decimal.Decimal `db:"total_tax"`
	RequestedTotal  decimal.Decimal `db:"requested_total"`
	ContractStoreID int             `db:"contract_store_id"`
	CreatedByUserID int             `db:"created_by_user_id"`
}

func main() {
	ctx := context.Background()
	// Check for alternate tasks which don't involve launching the service
	var jobName string
	createJobFlag(&jobName)
	flag.Parse()
	if jobName != "" {
		execute(ctx, jobName)
		return
	}
}

// execute invokes the specified task
func execute(ctx context.Context, jobName string) {
	if jobName == "recover-claim-payment" {
		if flag.NArg() == 0 {
			log.Println("error importing transactions from production")
			err := errors.New("please provide path of the CSV file to import")
			log.Fatalf("%+v\n", err)
		}

		path := flag.Args()
		err := recoverClaimPayments(ctx, path[0])
		if err != nil {
			log.Println("error updating claim payments")
			log.Fatalf("%+v\n", err)
		}
	} else if jobName != "" {
		log.Fatalf("Unrecognized job: %s", jobName)
	}
}

// CreateJobFlag defines that flag that is used to indicate that
// a task should run instead of the main service.
func createJobFlag(dest *string) {
	flag.StringVar(dest, "job", "", `Run a helper job

Options: 
	recover-claim-payment - Update claim payments from $0 to actual amount.
`)
}

func recoverClaimPayments(ctx context.Context, p string) error {
	files, err := getCSVFiles(p)
	if err != nil {
		return err
	}

	for _, file := range files {
		r, err := os.Open(file.Name())
		if err != nil {
			return err
		}
		records, err := readCSVFile(r)
		if err != nil {
			return err
		}
		for _, r := range records {
			processData(ctx, r)
			if err != nil {
				return err
			}
		}
	}
	return nil
}

func readCSVFile(r io.Reader) ([][]string, error) {
	csvr := csv.NewReader(r)
	records, err := csvr.ReadAll()
	if err != nil {
		return nil, errors.Wrap(err, "error reading file.")
	}
	return records, nil
}

func getCSVFiles(root string) ([]os.FileInfo, error) {
	var files []os.FileInfo
	fileInfo, err := ioutil.ReadDir(root)
	if err != nil {
		return files, err
	}

	for _, file := range fileInfo {
		if file.Mode().IsRegular() && strings.EqualFold(filepath.Ext(file.Name()), ".csv") {
			files = append(files, file)
		}
	}
	return files, nil
}

func processData(ctx context.Context, data []string) error {
	tx, err := db.Get().BeginTxx(ctx, nil)

	var claimData claimDB
	err = tx.GetContext(ctx, &claimData, getClaimQuery, data[3], db.AutoClaimStatusApproved, decimal.Zero)
	if err != nil {
		_ = tx.Rollback()
		return err
	}

	roDetails, err := getRO(ctx, claimData.ID, claimData.ContractStoreID, claimData.RO)
	if err != nil {
		_ = tx.Rollback()
		return err
	}

	var ro dms.RODetail
	if len(roDetails) > 0 {
		ro = roDetails[0]
	} else {
		return errors.New("RO Not found")
	}

	// Fetch complaints from cdk only if store is available
	var complaints []complaintPayload
	complaints, err = createROComplaints(ctx, tx, ro.ROLines, claimData.ID, claimData.ProductCode, ro.Date)
	if err != nil {
		_ = tx.Rollback()
		return errors.New("Failed to add complaints")
	}

	var grandTotal decimal.Decimal
	for _, v := range complaints {
		grandTotal = grandTotal.Add(v.LaborTotal).Add(v.PartsTotal)
		grandTotal = grandTotal.Add(v.Towing).Add(v.Rental).Add(v.Sublet)
	}

	var taxUsingLabor decimal.Decimal
	var totalTax decimal.Decimal
	decimal100 := decimal.NewFromFloat(100.0)
	taxUsingLabor = grandTotal.Mul(claimData.TaxLabor).Div(decimal100)
	totalTax = grandTotal.Mul(claimData.TaxParts).Div(decimal100)
	if taxUsingLabor.GreaterThan(totalTax) {
		totalTax = taxUsingLabor
	}

	claimData.TotalTax = totalTax
	claimData.RequestedTotal = grandTotal
	claimData.Estimate = grandTotal.Add(totalTax)
	// Set status as approved
	claimData.Status = db.AutoClaimStatusApproved

	err = updateClaim(ctx, tx, &claimData)
	if err != nil {
		_ = tx.Rollback()
		return errors.New("Database error updating claim")
	}

	err = tx.Commit()
	if err != nil {
		return errors.New("Database error committing transaction for Auto claim update")
	}
	return nil
}

// get RO details from CDK
func getRO(ctx context.Context, claimID int, storeID int, roNumber string) ([]dms.RODetail, error) {
	err := db.Get().GetContext(ctx, &claimID, `select id from automotive_claims where id = $1`, claimID)
	if err != nil {
		return nil, errors.Wrap(err, "Claim not found")
	}
	var store db.Store

	err = db.Get().Unsafe().GetContext(
		ctx,
		&store,
		`select * from stores where id = $1`,
		storeID,
	)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.Wrap(err, "Store with DMS provider and RO integration not found")
		}
		return nil, errors.Wrap(err, "Database error getting store for DMS RO lookup")
	}
	if !store.HasROIntegration {
		return nil, errors.Wrap(err, "Store with DMS provider and has RO integration not found")
	}
	var roDetails []dms.RODetail
	roDetails, err = dmsfactory.RODetail(ctx, &store, roNumber)
	if err != nil {
		return nil, err
	}
	return roDetails, nil
}

func createROComplaints(ctx context.Context, tx *sqlx.Tx, roLines []*dms.ROLine, claimID int, productCode string, roDate time.Time) ([]complaintPayload, error) {
	var newComplaints []complaintPayload

	cdkLaborTypes := db.CDKLaborTypes
	if productCode == db.ProductCodeMaintenance {
		cdkLaborTypes = append(cdkLaborTypes, db.CDKLaborTypeCELCXMaintOnly)
	}

	for _, roLine := range roLines {
		var laborTotal decimal.Decimal
		var partTotal decimal.Decimal
		// The labor types that will apply to TCA are CLCA, CLCM, CLCE, CLCX, CLCAR, CLCER, CLCL
		if isRelevantCDKComplaint(cdkLaborTypes, roLine.LaborTypes) {
			complaint := complaintPayload{}
			complaint.AutomotiveClaimID = claimID
			complaint.ComplaintDate = roDate
			complaint.LineCode = roLine.LineCode
			complaint.Correction = roLine.Correction
			complaint.Complaint = roLine.LineCode + " - " + roLine.ComplaintDescription
			if roLine.AddOnFlag == "Y" {
				complaint.AddLineFlag = true
			}
			complaint.IsManual = false
			complaint.Status = db.AutoClaimComplaintStatusPayable
			complaint.TechID = roLine.TechID

			complaintID, err := insertComplaint(ctx, tx, &complaint)
			if err != nil {
				return nil, errors.Wrap(err, "Error while creating CDK complaints")
			}

			for _, roLabor := range roLine.Labors {
				if contains(cdkLaborTypes, roLabor.LaborType) {
					labor := laborPayload{}
					labor.AutomotiveClaimComplaintID = complaintID
					labor.Requested = roLabor.SoldHours
					labor.Billed = roLabor.LaborSale
					labor.Approved = roLabor.LaborSale
					laborTotal = laborTotal.Add(roLabor.LaborSale)
					_, err = insertLabor(ctx, tx, &labor)
					if err != nil {
						return nil, errors.Wrap(err, "Error while creating CDK complaint labor")
					}
					complaint.Labor = append(complaint.Labor, labor)
				}
			}

			for _, roPart := range roLine.Parts {
				part := partPayload{}
				part.AutomotiveClaimComplaintID = complaintID
				part.Cost = roPart.Cost
				part.MSRP = roPart.List
				part.Description = roPart.Description
				part.PartNumber = roPart.PartNumber
				part.Quantity = roPart.Quantity
				part.Requested = roPart.PartsSale
				part.Approved = roPart.PartsSale
				partTotal = partTotal.Add(roPart.PartsSale)
				_, err = insertPart(ctx, tx, &part)
				if err != nil {
					return nil, errors.Wrap(err, "Error while creating CDK complaint parts")
				}
				complaint.Parts = append(complaint.Parts, part)
			}

			complaint.LaborTotal = laborTotal
			complaint.PartsTotal = partTotal
			newComplaints = append(newComplaints, complaint)
		}
	}
	return newComplaints, nil
}

// check if any of labor type matches with any of valid labortype
func isRelevantCDKComplaint(cdkLaborTypes []string, laborTypes []string) bool {
	for _, laborType := range laborTypes {
		if contains(cdkLaborTypes, laborType) {
			return true
		}
	}
	return false
}

func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

func insertComplaint(ctx context.Context, tx *sqlx.Tx, complaint *complaintPayload) (int, error) {
	insertQuery := `insert into automotive_claim_complaints(
		automotive_claim_id,
		complaint,
		tech_id,
		complaint_date,
		status,
		is_manual,
		add_line_flag,
		correction,
		line_code)
	 	values (
		:automotive_claim_id,
		:complaint,
		:tech_id,
		now() at time zone 'utc',
		:status,
		:is_manual,
		:add_line_flag,
		:correction,
		:line_code) returning id`
	id := 0

	stmt, err := tx.PrepareNamed(insertQuery)
	if err != nil {
		return id, errors.Wrap(err, "An error occurred in PrepareNamed function while adding Complaint.")
	}
	defer func() { _ = stmt.Close() }()

	err = stmt.GetContext(ctx, &id, complaint)
	if err != nil {
		return id, errors.Wrap(err, "An error occurred while trying to add the complaint to the database.")
	}

	return id, err
}

func insertPart(ctx context.Context, tx *sqlx.Tx, part *partPayload) (int, error) {
	insertQuery := `insert into automotive_claim_complaint_parts(automotive_claim_complaint_id, part_number, description,
	 quantity, cost, msrp, requested, approved, notes)
	 values (:automotive_claim_complaint_id,:part_number, :description, :quantity, :cost, :msrp, :requested, :approved, :notes) returning id`
	id := 0

	stmt, err := tx.PrepareNamed(insertQuery)
	if err != nil {
		return id, errors.Wrap(err, "An error occurred in PrepareNamed function while adding Part.")
	}
	defer func() { _ = stmt.Close() }()

	err = stmt.GetContext(ctx, &id, part)
	if err != nil {
		return id, errors.Wrap(err, "An error occurred while trying to add the Part to the database.")
	}

	return id, err
}

func insertLabor(ctx context.Context, tx *sqlx.Tx, labor *laborPayload) (int, error) {
	insertQuery := `insert into automotive_claim_complaint_labors(automotive_claim_complaint_id,hours,labor_description,approved,billed,requested)
	 values (:automotive_claim_complaint_id,:hours,:labor_description,:approved,:billed,:requested) returning id`
	id := 0

	stmt, err := tx.PrepareNamed(insertQuery)
	if err != nil {
		return id, errors.Wrap(err, "An error occurred in PrepareNamed function while adding Labor.")
	}
	defer func() { _ = stmt.Close() }()

	err = stmt.GetContext(ctx, &id, labor)
	if err != nil {
		return id, errors.Wrap(err, "An error occurred while trying to add the Labor to the database.")
	}

	return id, err
}

func updateClaim(ctx context.Context, tx *sqlx.Tx, claim *claimDB) error {
	claimForDB := claimDB{}
	claimForDB.ID = claim.ID
	claimForDB.Estimate = claim.Estimate
	claimForDB.Status = claim.Status
	claimForDB.TotalTax = claim.TotalTax
	claimForDB.RequestedTotal = claim.RequestedTotal

	query := `update automotive_claims
		set estimate =:estimate,
		status =:status,
		total_tax =:total_tax,
		requested_total =:requested_total
		where id =:id`

	stmt, err := tx.PrepareNamed(query)
	if err != nil {
		return errors.Wrap(err, "Error updating Claim, PrepareNamed failed")
	}
	defer func() { _ = stmt.Close() }()
	_, err = stmt.ExecContext(ctx, claimForDB)
	if err != nil {
		return errors.Wrap(err, "Error updating Claim, database error")
	}

	err = addClaimNote(ctx, tx, claim.ID, claim.CreatedByUserID, db.AutomotiveRecordNoteDescription[claim.Status])
	if err != nil {
		tx.Rollback()
	}

	err = claimUpdated(ctx, tx, claim.ID, claim.CreatedByUserID)
	if err != nil {
		return errors.Wrap(err, "Error inserting automotive_claim_updates")
	}

	return nil
}

type recordNotePayload struct {
	AutomotiveClaimID int       `json:"automotive_claim_id" db:"automotive_claim_id"`
	IsManual          bool      `json:"is_manual" db:"is_manual"`
	NotesText         string    `json:"notes_text" db:"notes_text"`
	CreatedAt         time.Time `json:"created_at" db:"created_at"`
	CreatedByUserID   int       `json:"created_by_user_id" db:"created_by_user_id"`
}

func addClaimNote(ctx context.Context, tx *sqlx.Tx, claimID int, userID int, noteText string) error {
	note := recordNotePayload{}
	note.AutomotiveClaimID = claimID
	note.IsManual = false
	note.CreatedByUserID = userID
	note.NotesText = noteText
	_, err := insertRecordNote(ctx, tx, &note)
	return err
}

func insertRecordNote(ctx context.Context, tx *sqlx.Tx, recordNote *recordNotePayload) (int, error) {
	insertQuery := `insert into automotive_record_notes (is_manual,created_at,automotive_claim_id,notes_text,created_by_user_id)
	 values (:is_manual,now() at time zone 'utc',:automotive_claim_id,:notes_text,:created_by_user_id) returning id`
	id := 0

	var stmt *sqlx.NamedStmt
	var err error
	if tx != nil {
		stmt, err = tx.PrepareNamed(insertQuery)
	} else {
		stmt, err = db.Get().PrepareNamed(insertQuery)
	}
	if err != nil {
		return id, errors.Wrap(err, "An error occurred in PrepareNamed function while adding RecordNote.")
	}
	defer func() { _ = stmt.Close() }()

	err = stmt.GetContext(ctx, &id, recordNote)
	if err != nil {
		return id, errors.Wrap(err, "An error occurred while trying to add the RecordNote to the database.")
	}

	return id, err
}

func claimUpdated(ctx context.Context, tx *sqlx.Tx, claimID, updatedByUserID int) error {
	updateInsert := `insert into automotive_claim_updates(automotive_claim_id,updated_by_user_id,updated_at) values($1,$2,now() at time zone 'utc')`
	_, err := tx.ExecContext(ctx, updateInsert, claimID, updatedByUserID)
	return err
}
