package handlers

import (
	"encoding/json"
	"net/http"
	"phizz/db"

	"github.com/pkg/errors"
)

// UpdateStore udpates the store with the latest changes from whiz
func UpdateStore(_ http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	ctx := req.Context()
	var payload db.Store

	dec := json.NewDecoder(req.Body)
	err := dec.Decode(&payload)
	if err != nil {
		ReportError(req, err)
		return http.StatusBadRequest, ErrorMessage(err, "Bad request", nil)
	}

	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		ReportError(req, errors.Wrap(err, "failed to create transaction"))
		return http.StatusBadRequest, ErrorMessage(err, "Error updating store", nil)
	}

	query := `update stores set updated_at = now() at time zone 'utc',
		name = :name, code = :code, address = :address, city = :city, state_code = :state_code,
		postal_code = :postal_code, phone = :phone, time_zone = :time_zone, has_ro_integration = :has_ro_integration,
		has_deal_integration = :has_deal_integration, alpha_dealer_number = :alpha_dealer_number,
		dms_provider = :dms_provider, dms_provider_parameters = :dms_provider_parameters, company_id = :company_id,
		claim_facility_id = :claim_facility_id
		where id = :id`
	autoStmt, err := tx.PrepareNamedContext(ctx, query)
	if err != nil {
		_ = tx.Rollback()
		ReportError(req, errors.Wrap(err, "failed to prepare statement to update automotive_claims"))
		return http.StatusInternalServerError, ErrorMessage(err, "Error updating store", nil)
	}
	defer autoStmt.Close()

	_, err = autoStmt.ExecContext(ctx, payload)
	if err != nil {
		_ = tx.Rollback()
		ReportError(req, errors.Wrap(err, "failed to update automotive_claims"))
		return http.StatusInternalServerError, ErrorMessage(err, "Error updating store", nil)
	}

	// We can not have same store attached to multiple facilities so remove any previous association
	updateQuery := `update automotive_facilities set store_id = null where store_id = $1`
	_, err = tx.ExecContext(ctx, updateQuery, payload.ID)
	if err != nil {
		_ = tx.Rollback()
		ReportError(req, errors.Wrap(err, "failed to update automotive_facilities"))
		return http.StatusInternalServerError, ErrorMessage(err, "Error updating store", nil)
	}

	// If we have valid facility then only udpate respective facility with udpated store
	if payload.ClaimFacilityID.Valid {
		query = `update automotive_facilities 
			set store_id = $1
			where id = $2`
		_, err = tx.ExecContext(ctx, query, payload.ID, payload.ClaimFacilityID)
		if err != nil {
			_ = tx.Rollback()
			ReportError(req, errors.Wrap(err, "failed to update automotive_facilities"))
			return http.StatusInternalServerError, ErrorMessage(err, "Error updating store", nil)
		}
	}

	err = tx.Commit()
	if err != nil {
		ReportError(req, errors.Wrap(err, "failed to commit transaction"))
		return http.StatusInternalServerError, ErrorMessage(err, "Error updating store", nil)
	}

	return http.StatusOK, map[string]interface{}{"store_id": payload.ID, "facility_id": payload.ClaimFacilityID}
}

// CreateStore saves store to phizz for respective whiz store creation
func CreateStore(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	ctx := req.Context()
	var storePayload db.Store

	dec := json.NewDecoder(req.Body)
	err := dec.Decode(&storePayload)
	if err != nil {
		return http.StatusBadRequest, ErrorMessage(err, "Malformed Store data for create.", nil)
	}

	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		ReportError(req, errors.Wrap(err, "error beginning transaction in store create"))
		return http.StatusInternalServerError, ErrorMessage(err, "Database error inserting new store.", nil)
	}

	query := `insert into stores (id, created_at, updated_at, company_id, name, code, address, city, state_code,
	postal_code, phone, time_zone, has_ro_integration, has_deal_integration, alpha_dealer_number,
	dms_provider, dms_provider_parameters, claim_facility_id)
	values (:id, now() at time zone 'utc', now() at time zone 'utc', :company_id, :name, :code, :address, :city,
	:state_code, :postal_code, :phone, :time_zone, :has_ro_integration, :has_deal_integration,
	:alpha_dealer_number, :dms_provider, :dms_provider_parameters, :claim_facility_id) returning id`

	stmt, err := tx.PrepareNamedContext(ctx, query)
	if err != nil {
		_ = tx.Rollback()
		ReportError(req, errors.Wrap(err, "error creating named statement to create a store record"))
		return http.StatusInternalServerError, ErrorMessage(err, "Error creating new store record", nil)
	}
	defer func() { _ = stmt.Close() }()
	var storeID int
	err = stmt.GetContext(ctx, &storeID, storePayload)
	if err != nil {
		_ = tx.Rollback()
		ReportError(req, errors.Wrap(err, "database error inserting new store"))
		return http.StatusInternalServerError, ErrorMessage(err, "Error creating new store record", nil)
	}

	// If we have valid facility then only udpate respective facility with udpated store
	if storePayload.ClaimFacilityID.Valid {
		// We can not have same store attached to multiple facilities so remove any previous association
		updateQuery := `update automotive_facilities set store_id = null where store_id = $1`
		_, err = tx.ExecContext(ctx, updateQuery, storeID)
		if err != nil {
			_ = tx.Rollback()
			ReportError(req, errors.Wrap(err, "failed to update automotive_facilities"))
			return http.StatusInternalServerError, ErrorMessage(err, "Error updating store", nil)
		}

		query = `update automotive_facilities 
			set store_id = $1
			where id = $2`
		_, err = tx.ExecContext(ctx, query, storeID, storePayload.ClaimFacilityID)
		if err != nil {
			_ = tx.Rollback()
			ReportError(req, errors.Wrap(err, "failed to update automotive_facilities"))
			return http.StatusInternalServerError, ErrorMessage(err, "Error updating store", nil)
		}
	}

	err = tx.Commit()
	if err != nil {
		ReportError(req, errors.Wrap(err, "failed to commit transaction"))
		return http.StatusInternalServerError, ErrorMessage(err, "Error updating store", nil)
	}

	return http.StatusOK, map[string]interface{}{"store_id": storeID}
}
