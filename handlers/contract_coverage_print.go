package handlers

import (
	"strconv"
	"time"

	"github.com/jung-kurt/gofpdf"
	"github.com/shopspring/decimal"
	"gopkg.in/guregu/null.v3"
)

type coverageDetails struct {
	Code              int         `json:"code"`
	Covered           string      `json:"covered"`
	ProductTypeID     int         `json:"product_type_id"`
	GroupName         string      `json:"group_name"`
	Description       string      `json:"description"`
	ProductName       string      `json:"product_name"`
	OptionCode        null.String `json:"option_code"`
	UpdatedOptionCode null.String `json:"updated_option_code"`
}

type vehicleCoverageRequest struct {
	CoverageDetails []coverageDetails `json:"selectedItems"`
	Contract        contractInfo      `json:"contract"`
	CurrentMileage  string            `json:"currentMileage"`
}

type warranty struct {
	BasicWarrantyMiles       int `json:"basic_warranty_miles"`
	BasicWarrantyMonths      int `json:"basic_warranty_months"`
	DrivetrainWarrantyMiles  int `json:"drivetrain_warranty_miles"`
	DrivetrainWarrantyMonths int `json:"drivetrain_warranty_months"`
}

type contractOption struct {
	ID             int             `db:"-" json:"id"`
	CreatedAt      time.Time       `db:"-" json:"created_at"`
	ContractID     int             `db:"-" json:"contract_id"`
	QuoteOptionID  null.Int        `db:"-" json:"quote_option_id"`
	Name           null.String     `db:"-" json:"name"`
	Code           null.String     `db:"-" json:"code"`
	Cost           decimal.Decimal `db:"-" json:"cost"`
	OptionGroup    null.String     `db:"-" json:"option_group"`
	OptionID       null.Int        `db:"-" json:"option_id"`
	RateBucketID   int             `db:"-" json:"rate_bucket_id"`
	RateBucketName string          `db:"-" json:"rate_bucket_name"`
}

type contractInfo struct {
	ProductCode        string           `json:"Product_code"`
	Code               string           `json:"code"`
	EffectiveDate      time.Time        `json:"effective_date"`
	ExpirationMileage  int              `json:"expiration_mileage"`
	PlanName           string           `json:"plan_name"`
	CustomerName       string           `json:"customer_name"`
	Vin                string           `json:"vin"`
	VehicleYear        string           `json:"vehicle_year"`
	VehicleMake        string           `json:"vehicle_make"`
	VehicleModel       string           `json:"vehicle_model"`
	ProductName        string           `json:"product_name"`
	ProductVariantName string           `json:"product_variant_name"`
	OriginalCode       string           `json:"original_code"`
	Options            []contractOption `json:"options"`
	Warranty           warranty         `json:"warranty"`
}

const longDateFormat = "01/02/2006"

// coverageComponentsPDF generates the pdf for covered components
func coverageComponentsPDF(fpdf *gofpdf.Fpdf, payload vehicleCoverageRequest) {
	fpdf.AddPage()
	fpdf.SetFont("Helvetica", "", 8)
	fpdf.Cell(0.2, 0.01, time.Now().UTC().Format(longDateFormat))
	fpdf.Ln(0.2)

	fpdf.SetFont("Helvetica", "B", 12)
	fpdf.Cell(2.5, 0.0, "")
	fpdf.Cell(2.5, 0.2, "**Coverage Lookup Items**")
	fpdf.Ln(0.5)

	fpdf.SetFont("Helvetica", "", 10)
	fpdf.Cell(4.5, 0.2, "Customer Name: "+payload.Contract.CustomerName)
	fpdf.SetFont("Helvetica", "", 10)
	fpdf.Cell(3.5, 0.2, "VIN: "+payload.Contract.Vin)
	fpdf.Ln(-1)

	fpdf.SetFont("Helvetica", "", 10)
	fpdf.Cell(4.5, 0.2, "Contract Number: "+payload.Contract.OriginalCode)
	fpdf.SetFont("Helvetica", "", 10)
	fpdf.Cell(3.5, 0.2, "Make: "+payload.Contract.VehicleMake)
	fpdf.Ln(-1)

	fpdf.SetFont("Helvetica", "", 10)
	fpdf.Cell(4.5, 0.2, "Product Name: "+payload.Contract.ProductName)
	fpdf.SetFont("Helvetica", "", 10)
	fpdf.Cell(3.5, 0.2, "Model: "+payload.Contract.VehicleModel)
	fpdf.Ln(-1)

	fpdf.SetFont("Helvetica", "", 10)
	fpdf.Cell(4.5, 0.2, "Plan/ Term: "+payload.Contract.PlanName)
	fpdf.SetFont("Helvetica", "", 10)
	fpdf.Cell(3.5, 0.2, "Year: "+payload.Contract.VehicleYear)
	fpdf.Ln(-1)

	fpdf.SetFont("Helvetica", "", 10)
	fpdf.Cell(4.5, 0.2, "Expiration Mileage: "+strconv.Itoa(payload.Contract.ExpirationMileage))
	fpdf.SetFont("Helvetica", "", 10)
	fpdf.Cell(3.5, 0.2, "Current Mileage: "+payload.CurrentMileage)
	fpdf.Ln(-1)

	if len(payload.Contract.Options) > 0 {
		fpdf.SetFont("Helvetica", "", 10)
		fpdf.Cell(4.5, 0.2, "Included options: ")
		fpdf.Ln(-1)
		for index, opt := range payload.Contract.Options {
			if opt.Name.Valid {
				fpdf.SetFont("Helvetica", "", 10)
				fpdf.Cell(4.5, 0.2, strconv.Itoa(index+1)+") "+opt.Name.String)
			}
			fpdf.Ln(-1)
		}
	}
	fpdf.Ln(0.2)

	fpdf.SetTextColor(255, 0, 0)
	fpdf.SetFont("Helvetica", "", 10)
	fpdf.Cell(4.5, 0.2, "*These specific components represent the causal parts for a mechanical failure.")
	fpdf.Ln(-1)
	fpdf.SetFont("Helvetica", "", 10)
	fpdf.Cell(4.5, 0.2, " The terms and conditions per the contact still apply and coverage is NOT guaranteed.")
	fpdf.Ln(-1)
	fpdf.SetFont("Helvetica", "", 10)
	fpdf.Cell(4.5, 0.2, "*If a specific component is not listed, please contact TCA for verification.")
	fpdf.Ln(0.5)
	fpdf.SetTextColor(0, 0, 0)

	fpdf.SetFont("Helvetica", "", 10)
	fpdf.CellFormat(0.8, 0.5, "Covered", "1", 0, "", false, 0, "")
	fpdf.CellFormat(0.8, 0.5, "Code", "1", 0, "", false, 0, "")
	fpdf.CellFormat(0.8, 0.5, "Product", "1", 0, "", false, 0, "")
	fpdf.CellFormat(1.8, 0.5, "Group", "1", 0, "", false, 0, "")
	fpdf.CellFormat(3.5, 0.5, "Description", "1", 0, "", false, 0, "")
	fpdf.Ln(-1)

	for _, v := range payload.CoverageDetails {
		covered := ""
		if v.Covered == "Y" {
			covered = "Yes"
		} else if v.Covered == "N" {
			covered = "No"
		} else if v.Covered == "C" {
			covered = "Call TCA"
		} else if v.Covered == "O" {
			for _, opt := range payload.Contract.Options {
				if opt.Code == v.OptionCode || opt.Code == v.UpdatedOptionCode {
					covered = "Yes"
					break
				} else {
					covered = "No"
				}
			}
		}

		h := float64(len(fpdf.SplitLines([]byte(v.Description), 3.5))) / 2

		fpdf.SetFont("Helvetica", "", 10)
		fpdf.CellFormat(0.8, h, covered, "1", 0, "", false, 0, "")
		fpdf.CellFormat(0.8, h, strconv.Itoa(v.Code), "1", 0, "", false, 0, "")
		fpdf.CellFormat(0.8, h, v.ProductName, "1", 0, "", false, 0, "")
		fpdf.CellFormat(1.8, h, v.GroupName, "1", 0, "", false, 0, "")
		lines := fpdf.SplitLines([]byte(v.Description), 3.5)
		if len(lines) == 1 {
			fpdf.CellFormat(3.5, h, v.Description, "1", 0, "", false, 0, "")
			fpdf.Ln(0.5)
		} else {
			for index, line := range lines {
				fpdf.SetFont("Helvetica", "", 10)
				if index == 0 {
					fpdf.CellFormat(3.5, 0.5, string(line), "TR", 2, "", false, 0, "")
				} else if index == len(lines)-1 {
					fpdf.CellFormat(3.5, 0.5, string(line), "BR", 2, "", false, 0, "")
				} else {
					fpdf.CellFormat(3.5, 0.5, string(line), "R", 2, "", false, 0, "")
				}
			}
			fpdf.Ln(0)
		}
	}
	fpdf.Ln(0.5)

	fpdf.SetTextColor(255, 0, 0)
	fpdf.SetFont("Helvetica", "BU", 10)
	fpdf.Cell(0, 0.2, "PARTS AND LABOR DISCLAIMERS")
	fpdf.Ln(-1)

	fpdf.SetFont("Helvetica", "B", 10)
	fpdf.Cell(0.95, 0.2, "*LABOR TIME")
	disclaimer := " is solely based on data listed by ALLDATA. Labor times billed from other labor time guides will be adjusted and paid short and are not eligible for appeal."
	fpdf.SetFont("Helvetica", "", 10)
	lines := fpdf.SplitLines([]byte(disclaimer), 6.8)
	for _, line := range lines {
		fpdf.CellFormat(6.8, 0.2, string(line), "", 1, "", false, 0, "")
	}
	fpdf.Ln(0)

	fpdf.SetFont("Helvetica", "B", 10)
	fpdf.Cell(1.15, 0.2, "*PARTS PRICING")
	disclaimer = " Non-OEM or after-market parts should be charged at cost plus 60%, not to exceed manufacturer's suggested list price. The maximum markup for parts, whether OEM or Non-OEM, is $250."
	fpdf.SetFont("Helvetica", "", 10)
	lines = fpdf.SplitLines([]byte(disclaimer), 6.8)
	for _, line := range lines {
		fpdf.CellFormat(6.8, 0.2, string(line), "", 1, "", false, 0, "")
	}
}
