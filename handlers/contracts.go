package handlers

import (
	"bytes"
	"crypto/sha512"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"phizz/conf"
	"phizz/db"
	"phizz/nr"
	"phizz/types"

	"github.com/go-chi/chi"
	"github.com/jung-kurt/gofpdf"
	newrelic "github.com/newrelic/go-agent"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
	"gopkg.in/guregu/null.v3"
)

// ContractIndex returns the contract list, uses whiz data
func ContractIndex(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {

	search := req.FormValue("search")

	if search == "" {
		return http.StatusBadRequest, ErrorMessage(nil, "Invalid Parameters", nil)
	}

	t := &url.URL{Path: search}
	search = t.String()

	params := url.Values{
		"search":    {search},
		"page":      {strconv.Itoa(GetPage(req))},
		"page_size": {strconv.Itoa(GetPageSize(req, 20))},
	}
	url := fmt.Sprintf("%s/ext/contracts?%s", conf.Get().Whiz.BaseURL, params.Encode())

	if conf.Get().Whiz.Log {
		log.Println("[Whiz-contracts] request URL:", url)
	}

	whizReq, err := http.NewRequest("GET", url, nil)
	if err != nil {
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage(err, "could not create Whiz-contracts request", nil)
	}

	whizReq.Header.Set("Content-Type", "application/json")
	whizReq.Header.Set("Accept", "application/json")
	salt := []byte(conf.Get().Whiz.AuthSalt)
	checksum := sha512.Sum512(salt)
	whizReq.Header.Set("Whiz-Checksum", hex.EncodeToString(checksum[:]))

	txn := w.(newrelic.Transaction)

	client := http.Client{Timeout: time.Second * 120}
	resp, err := nr.External(txn, client, whizReq)
	if err != nil {
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage(err, "Invalid response for Whiz-contracts", nil)
	}
	defer resp.Body.Close()

	bodyBytes := make([]byte, 1024000) // 1000KB buffer for response
	n, err := io.ReadFull(resp.Body, bodyBytes)
	if err != nil && err != io.EOF && err != io.ErrUnexpectedEOF {
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage(err, "Invalid response for Whiz-contracts", nil)
	}
	bodyBytes = bodyBytes[:n]
	if conf.Get().Whiz.Log {
		log.Println("[Whiz-contracts] response status:", resp.Status, "body:", string(bodyBytes))
	}

	if resp.StatusCode != http.StatusOK {
		err = errors.New(fmt.Sprintf("invalid response for Whiz-contracts: %d", resp.StatusCode))
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage(err, "Invalid response for Whiz-contracts", nil)
	}

	type contractOption struct {
		ID             int             `db:"-" json:"id"`
		CreatedAt      time.Time       `db:"-" json:"created_at"`
		ContractID     int             `db:"-" json:"contract_id"`
		QuoteOptionID  null.Int        `db:"-" json:"quote_option_id"`
		Name           null.String     `db:"-" json:"name"`
		Code           null.String     `db:"-" json:"code"`
		Cost           decimal.Decimal `db:"-" json:"cost"`
		OptionGroup    null.String     `db:"-" json:"option_group"`
		OptionID       null.Int        `db:"-" json:"option_id"`
		RateBucketID   int             `db:"-" json:"rate_bucket_id"`
		RateBucketName string          `db:"-" json:"rate_bucket_name"`
	}

	type warrantyInfo struct {
		BasicWarrantyMiles       int `json:"basic_warranty_miles"`
		BasicWarrantyMonths      int `json:"basic_warranty_months"`
		DrivetrainWarrantyMiles  int `json:"drivetrain_warranty_miles"`
		DrivetrainWarrantyMonths int `json:"drivetrain_warranty_months"`
	}

	type contract struct {
		ID                int              `json:"id"`
		ProductCode       string           `json:"product_code"`
		Code              string           `json:"code"`
		Status            string           `json:"status"`
		EffectiveDate     null.Time        `json:"effective_date"`
		ExpirationMileage null.Int         `json:"expiration_mileage"`
		PlanName          string           `json:"plan_name"`
		CustomerName      string           `json:"customer_name"`
		VIN               string           `json:"vin"`
		DealNumber        string           `json:"deal_number"`
		VehicleYear       string           `json:"vehicle_year"`
		VehicleMake       string           `json:"vehicle_make"`
		VehicleModel      string           `json:"vehicle_model"`
		StoreCode         string           `json:"store_code"`
		StoreNumber       string           `json:"store_number"`
		Source            string           `json:"source"`
		EditWithRole      []string         `json:"edit_with_roles"`
		ProductName       string           `json:"product_name"`
		OriginalCode      string           `json:"original_code"`
		Options           []contractOption `json:"options"`
		Warranty          warrantyInfo     `json:"warranty"`
	}

	contractData := struct {
		Contracts         []contract `json:"contracts"`
		Count             int        `json:"count"`
		VoidedOrGenerated bool       `json:"voided_or_generated"`
	}{}

	err = json.Unmarshal(bodyBytes, &contractData)
	if err != nil {
		err = errors.Wrap(err, "Invalid response for Whiz-contracts")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage(err, "Invalid response for Whiz-contracts", nil)
	}

	return http.StatusOK, map[string]interface{}{
		"contracts":           contractData.Contracts,
		"count":               contractData.Count,
		"voided_or_generated": contractData.VoidedOrGenerated,
	}
}

// ContractShow to show individual contract
func ContractShow(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {

	id := chi.URLParam(req, "id")
	if id == "" {
		return http.StatusBadRequest, ErrorMessage(errors.New("Invalid request"), "Get contract request failed.", nil)
	}
	txn := w.(newrelic.Transaction)
	contract, err := GetContractByID(txn, req, id)
	if err != nil {
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage(err, "Invalid response for Whiz-contract-detail", nil)
	}
	return http.StatusOK, map[string]interface{}{"contract": contract}
}

// GetContractByID fetch contract from whiz by contract_id
func GetContractByID(txn newrelic.Transaction, req *http.Request, contractID string) (ContractData, error) {
	url := conf.Get().Whiz.BaseURL + "/ext/contracts/" + contractID

	if conf.Get().Whiz.Log {
		log.Println("[Whiz-contract-detail] request URL:", url)
	}

	whizReq, err := http.NewRequest("GET", url, nil)
	if err != nil {
		err = errors.Wrap(err, "could not create Whiz-contract-detail request")
		ReportError(req, err)
		return ContractData{}, err
	}

	whizReq.Header.Set("Content-Type", "application/json")
	whizReq.Header.Set("Accept", "application/json")
	salt := []byte(conf.Get().Whiz.AuthSalt)
	checksum := sha512.Sum512(salt)
	whizReq.Header.Set("Whiz-Checksum", hex.EncodeToString(checksum[:]))

	client := http.Client{Timeout: time.Second * 30}
	resp, err := nr.External(txn, client, whizReq)
	if err != nil {
		err = errors.Wrap(err, "invalid response for Whiz-contract-detail")
		ReportError(req, err)
		return ContractData{}, err
	}
	defer resp.Body.Close()

	bodyBytes := make([]byte, 1024000) // 1000KB buffer for response
	n, err := io.ReadFull(resp.Body, bodyBytes)
	if err != nil && err != io.EOF && err != io.ErrUnexpectedEOF {
		err = errors.Wrap(err, "invalid response for Whiz-contract-detail")
		ReportError(req, err)
		return ContractData{}, err
	}

	bodyBytes = bodyBytes[:n]
	if conf.Get().Whiz.Log {
		log.Println("[Whiz-contract-detail] response status:", resp.Status, "body:", string(bodyBytes))
	}

	if resp.StatusCode != http.StatusOK {
		err = errors.New(fmt.Sprintf("invalid response for Whiz-contract-detail: %d", resp.StatusCode))
		ReportError(req, err)
		return ContractData{}, err
	}

	contractData := struct {
		Data ContractData `json:"data"`
	}{}
	err = json.Unmarshal(bodyBytes, &contractData)
	if err != nil {
		err = errors.Wrap(err, "invalid response for Whiz-contracts")
		ReportError(req, err)
		return ContractData{}, err
	}

	if contractData.Data.Contract.ProductTypeCode == db.ProductCodeWhizMaintenance {
		query := `select count(*) from automotive_claims ac
        where contract_number = $1 and product_code = $2 and status in ($3,$4)`
		var count int64
		err := db.Get().Get(&count, query, contractData.Data.Contract.Code, db.ProductCodeWhizMaintenance, db.AutoClaimStatusCheckWritten, db.AutoClaimStatusCCPaid)
		if err != nil {
			err = errors.Wrap(err, "database error while getting claim count for maintenance claims")
			ReportError(req, err)
			return ContractData{}, err
		}
		contractData.Data.Contract.RemainingVisits = int(contractData.Data.Contract.MaintenanceVisits.Int64 - count)
	}

	return contractData.Data, nil
}

// UpdateContractStatus fetch contract from whiz by contract_id
func UpdateContractStatus(txn newrelic.Transaction, req *http.Request, contractID string, usedCoupons int) error {
	url := conf.Get().Whiz.BaseURL + "/ext/contracts/status"

	if conf.Get().Whiz.Log {
		log.Println("[Whiz-contract-status] request URL:", url)
	}

	payload := struct {
		ContractCode string `json:"contract_code"`
		UsedCoupons  int    `json:"used_coupons"`
	}{
		ContractCode: contractID,
		UsedCoupons:  usedCoupons,
	}

	buf := bytes.NewBuffer([]byte{})
	enc := json.NewEncoder(buf)
	err := enc.Encode(payload)
	if err != nil {
		err = errors.Wrap(err, "could not encode JSON for whiz-contracts-status update request")
		ReportError(req, err)
		return err
	}

	whizReq, err := http.NewRequest("PUT", url, buf)
	if err != nil {
		err = errors.Wrap(err, "could not create Whiz-contract-status request")
		ReportError(req, err)
		return err
	}

	whizReq.Header.Set("Content-Type", "application/json")
	whizReq.Header.Set("Accept", "application/json")
	salt := []byte(conf.Get().Whiz.AuthSalt)
	bodyAndSalt := append(buf.Bytes(), salt...)
	calcChecksum := sha512.Sum512(bodyAndSalt)
	whizReq.Header.Set("Whiz-Checksum", hex.EncodeToString(calcChecksum[:]))

	client := http.Client{Timeout: time.Second * 30}
	resp, err := nr.External(txn, client, whizReq)
	if err != nil {
		err = errors.Wrap(err, "invalid response for Whiz-contract-status")
		ReportError(req, err)
		return err
	}
	defer resp.Body.Close()

	bodyBytes := make([]byte, 102400) // 100KB buffer for response
	n, err := io.ReadFull(resp.Body, bodyBytes)
	if err != nil && err != io.EOF && err != io.ErrUnexpectedEOF {
		err = errors.Wrap(err, "invalid response for Whiz-contract-status")
		ReportError(req, err)
		return err
	}

	bodyBytes = bodyBytes[:n]
	if conf.Get().Whiz.Log {
		log.Println("[Whiz-contract-detail] response status:", resp.Status, "body:", string(bodyBytes))
	}

	if resp.StatusCode != http.StatusOK {
		err = errors.New(fmt.Sprintf("invalid response for Whiz-contract-status: %d", resp.StatusCode))
		ReportError(req, err)
		return err
	}

	var responseData struct {
		Status string `json:"status"`
	}
	err = json.Unmarshal(bodyBytes, &responseData)
	if err != nil {
		err = errors.Wrap(err, "invalid response for Whiz-contract-status")
		ReportError(req, err)
		return err
	}

	if responseData.Status != "Success" {
		return errors.New("error updating contract status")
	}
	return nil
}

// ContractData payload for ContractData
type ContractData struct {
	Contract struct {
		ID                        int                `json:"id"`
		Code                      string             `json:"code"`
		ProductVariantDisplayName string             `json:"product_variant_display_name"`
		EffectiveDate             types.JSPQNullDate `json:"effective_date"`
		ExpirationDate            types.JSPQNullDate `json:"expiration_date"`
		EffectiveMileage          int                `json:"effective_mileage"`
		ExpirationMileage         null.Int           `json:"expiration_mileage"`
		PlanName                  string             `json:"plan_name" `
		IssuingDealer             string             `json:"issuing_dealer"`
		IssuingDealerNumber       string             `json:"issuing_dealer_number"`
		BookDate                  types.JSPQNullDate `json:"book_date" `
		Price                     decimal.Decimal    `json:"price" `
		PlanCost                  decimal.Decimal    `json:"plan_cost"` // plan cost
		Cost                      decimal.Decimal    `json:"cost"`
		InvoicedAt                null.Time          `json:"invoiced_at"`
		InvoiceNumber             string             `json:"invoice_number"`
		OriginalCode              string             `json:"original_code"`
		Options                   []struct {
			ID            int             `json:"id"`
			CreatedAt     time.Time       `json:"created_at"`
			ContractID    int             `json:"contract_id"`
			QuoteOptionID null.Int        `json:"quote_option_id"`
			Name          null.String     `json:"name"`
			Code          null.String     `json:"code"`
			Cost          decimal.Decimal `json:"cost"`
			OptionGroup   null.String     `json:"option_group"`
			OptionID      null.Int        `json:"option_id"`
		} `json:"options"`
		Surcharges []struct {
			ID               int             `json:"id"`
			CreatedAt        time.Time       `json:"created_at"`
			ContractID       int             `json:"contract_id"`
			QuoteSurchargeID null.Int        `json:"quote_surcharge_id"`
			Name             null.String     `json:"name"`
			Code             null.String     `json:"code"`
			Cost             decimal.Decimal `json:"cost"`
			SurchargeID      null.Int        `json:"surcharge_id"`
		} `json:"surcharges"`
		MaintenanceVisits     null.Int    `json:"maintenance_visits"`
		MinorCouponsPurchased null.Int    `json:"minor_coupons_purchased"`
		Status                string      `json:"status"`
		Source                string      `json:"source"`
		Fabric                null.Bool   `json:"fabric"`
		LeatherOrVinyl        null.Bool   `json:"leather_or_vinyl"`
		Paint                 null.Bool   `json:"paint"`
		DentAndDing           null.Bool   `json:"dent_and_ding"`
		ProductTypeCode       string      `json:"product_type_code"`
		RemainingVisits       int         `json:"remaining_visits"`
		DealNumber            string      `json:"deal_number"`
		UpdatedAt             time.Time   `json:"updated_at"`
		RemitAmount           string      `json:"remit_amount"`
		VTANumber             null.String `json:"vehicle_theft_number"`
	} `json:"contract"`
	CustomerDetails struct {
		ID                int    `json:"id"`
		FirstName         string `json:"first_name"`
		LastName          string `json:"last_name"`
		IsBusiness        bool   `json:"is_business"`
		BusinessName      string `json:"business_name"`
		Address           string `json:"address"`
		City              string `json:"city"`
		StateCode         string `json:"state_code"`
		PostalCode        string `json:"postal_code"`
		Phone             string `json:"phone"`
		AlternatePhone    string `json:"alternate_phone"`
		Email             string `json:"email"`
		BestContactMethod string `json:"best_contact_method"`
	} `json:"customer_details"`
	VehicleDetails struct {
		VIN      string `json:"vin"`
		Year     string `json:"year"`
		Make     string `json:"make"`
		Model    string `json:"model"`
		IsNew    bool   `json:"is_new"`
		Odometer string `json:"odometer"`
	} `json:"vehicle_details"`
	FinancingDetails struct {
		PaymentType           string              `json:"payment_type"`
		LenderName            string              `json:"lender_name"`
		LenderAddress         string              `json:"lender_address"`
		LenderCity            string              `json:"lender_city"`
		LenderState           string              `json:"lender_state"`
		LenderZip             string              `json:"lender_zip"`
		SaleType              string              `json:"sale_type"`
		VehiclePrice          types.JSNullDecimal `json:"vehicle_price"`
		FinanceAmount         types.JSNullDecimal `json:"finance_amount"`
		FinanceApr            types.JSNullDecimal `json:"finance_apr"`
		FinanceMonthlyPayment types.JSNullDecimal `json:"finance_monthly_payment"`
		FirstPaymentDate      null.Time           `json:"first_payment_date"`
		ContractDate          types.JSPQNullDate  `json:"contract_date"`
		MSRP                  types.JSNullDecimal `json:"msrp"`
		SalesMan              string              `json:"sales_man"`
		Term                  int                 `json:"term"`
		KeysRemotes           int                 `json:"keys_remotes"`
	} `json:"financing_details"`
	CoBuyer struct {
		ID         int    `json:"id"`
		FirstName  string `json:"first_name"`
		LastName   string `json:"last_name"`
		Address    string `json:"address"`
		City       string `json:"city"`
		StateCode  string `json:"state_code"`
		PostalCode string `json:"postal_code"`
		HomePhone  string `json:"home_phone"`
		AltPhone   string `json:"alt_phone"`
		Email      string `json:"email"`
	} `json:"cobuyer"`
	ContractEvents []struct {
		ID            int       `json:"id"`
		CreatedAt     time.Time `json:"created_at"`
		CreatedByName string    `json:"created_by_name"`
		Description   string    `json:"description"`
	} `json:"contract_events"`
	Cancellation struct {
		ID                     int                `json:"id"`
		CancelDate             time.Time          `json:"cancel_date"`
		CancelMileage          int                `json:"cancel_mileage"`
		CancelReason           string             `json:"cancel_reason"`
		CustomerRefund         decimal.Decimal    `json:"customer_refund"`
		SalesTax               decimal.Decimal    `json:"sales_tax"`
		SalesTaxRate           decimal.Decimal    `json:"sales_tax_rate"`
		StoreRefund            decimal.Decimal    `json:"store_refund"`
		CancelFee              decimal.Decimal    `json:"cancel_fee"`
		CancelFactor           decimal.Decimal    `json:"cancel_factor"`
		InvoiceID              int                `json:"invoice_id"`
		ReviewedAt             types.JSPQNullDate `json:"reviewed_at"`
		ReviewedByUserID       int                `json:"reviewed_by_user_id"`
		ClaimsDeducted         bool               `json:"claims_deducted"`
		ClaimsPaidAmount       decimal.Decimal    `json:"claims_paid_amount"`
		ClaimsDeductedAmount   decimal.Decimal    `json:"claims_deducted_amount"`
		CancelFactorUsed       string             `json:"cancel_factor_used"`
		InvoicedAt             null.String        `json:"invoiced_at"`
		InvoiceNumber          string             `json:"invoice_number"`
		ThirdPartyRemitAmount  decimal.Decimal    `json:"third_party_remit"`
		SPPAmountPaid          decimal.Decimal    `json:"spp_customer_paid"`
		SPPBalance             decimal.Decimal    `json:"spp_balance"`
		NSDClaims              decimal.Decimal    `json:"nsd_claims"`
		AdjustedCustomerRefund decimal.Decimal    `json:"adjusted_customer_refund"`
		StoreChargeback        decimal.Decimal    `json:"store_chargeback"`
		SPPRefund              decimal.Decimal    `json:"spp_refund"`
		RSARefund              decimal.Decimal    `json:"rsa_refund"`
		TransactionID          null.Int           `json:"-"`
		PayeeType              string             `json:"payee_type"`
		PayeeName              string             `json:"payee_name"`
		PayeeAttentionTo       string             `json:"payee_attention_to"`
		PayeeAddress           string             `json:"payee_address"`
		PayeeCity              string             `json:"payee_city"`
		PayeeState             string             `json:"payee_state"`
		PayeePostalCode        string             `json:"payee_postal_code"`
		CancelStatus           string             `json:"cancel_status"`
		CheckNumber            string             `json:"check_number"`
		PaidDate               null.String        `json:"paid_date"`
		BillNumber             string             `json:"bill_number"`
		CheckAmount            string             `json:"check_amount"`
		ManualUpdateNotes      string             `json:"manual_update_notes"`
		IsElectronicCheck      bool               `json:"is_electronic_check"`
		Email                  string             `json:"email"`
		DaysUsed               decimal.Decimal    `json:"days_used"`
		MilesUsed              decimal.Decimal    `json:"miles_used"`
	} `json:"cancellation"`

	Transfers []struct {
		ID                       int         `db:"id" json:"id"`
		TransferredAt            time.Time   `db:"transferred_at" json:"transferred_at"`
		TransferredBy            int         `db:"transferred_by_user_id" json:"transferred_by_user_id"`
		TransferType             string      `db:"transfer_type" json:"transfer_type"`
		PreviousVin              null.String `db:"previous_vin" json:"previous_vin"`
		PreviousEffectiveMileage null.String `db:"previous_effective_mileage" json:"previous_effective_mileage"`
		PreviousCustomerID       null.String `db:"previous_customer_id" json:"previous_customer_id"`
		FeeReceived              bool        `db:"fee_received" json:"fee_received"`
	} `json:"transfers"`
	Reinstate struct {
		ID              int       `db:"id" json:"id"`
		ContractID      int       `db:"contract_id" json:"contract_id"`
		CreatedAt       time.Time `db:"created_at" json:"created_at"`
		ReinstateDate   null.Time `db:"reinstate_date" json:"reinstate_date"`
		CreatedByUserID int       `db:"created_by_user_id" json:"created_by_user_id"`
		InvoiceDate     null.Time `db:"invoice_date" json:"invoice_date"`
		InvoiceNumber   string    `db:"invoice_number" json:"invoice_number"`
		TransactionID   int       `db:"transaction_id" json:"transaction_id"`
		IsVoid          bool      `db:"is_void" json:"is_void"`
	} `json:"reinstate"`
}

type customer struct {
	UpdatedByUserID int    `json:"updated_by_user_id"`
	Address         string `json:"address"`
	City            string `json:"city"`
	StateCode       string `json:"state_code"`
	PostalCode      string `json:"postal_code"`
	Email           string `json:"email"`
	Phone           string `json:"phone"`
	Notes           string `json:"notes"`
}

type contractEditExtPayload struct {
	Customer customer `json:"customer"`
	Cobuyer  struct {
		ID              int    `json:"-"`
		CreatedByUserID int    `json:"created_by_user_id"`
		UpdatedByUserID int    `json:"updated_by_user_id"`
		FirstName       string `json:"first_name"`
		LastName        string `json:"last_name"`
		Address         string `json:"address"`
		City            string `json:"city"`
		StateCode       string `json:"state_code"`
		PostalCode      string `json:"postal_code"`
		Email           string `json:"email"`
		HomePhone       string `json:"home_phone"`
		AltPhone        string `json:"alt_phone"`
	}
	UpdateByUserEmail string `db:"-" json:"updated_by_user_email"`
}

func (p contractEditExtPayload) validate() (map[string]string, error) {
	validationErrors := map[string]string{}

	if p.Customer.Address == "" {
		validationErrors["address"] = "Customer Address is required."
	}
	if p.Customer.City == "" {
		validationErrors["city"] = "Customer City is required."
	}
	if p.Customer.StateCode == "" {
		validationErrors["state_code"] = "Customer State is required."
	}
	if p.Customer.PostalCode == "" {
		validationErrors["postal_code"] = "Customer Postal code is required."
	}
	if p.Customer.Phone == "" {
		validationErrors["phone"] = "Customer Phone is required."
	}

	if p.Cobuyer.FirstName != "" {
		if p.Cobuyer.LastName == "" {
			validationErrors["last_name"] = "Cobuyer LastName is required."
		}
	}
	return validationErrors, nil
}

func (p *contractEditExtPayload) clean() {
	p.Customer.Address = strings.TrimSpace(p.Customer.Address)
	p.Customer.City = strings.TrimSpace(p.Customer.City)
	p.Customer.StateCode = strings.TrimSpace(p.Customer.StateCode)
	p.Customer.PostalCode = strings.TrimSpace(p.Customer.PostalCode)
	p.Customer.Email = strings.TrimSpace(p.Customer.Email)
	p.Customer.Address = strings.TrimSpace(p.Customer.Address)
	p.Customer.Phone = strings.TrimSpace(p.Customer.Phone)
	p.Cobuyer.FirstName = strings.TrimSpace(p.Cobuyer.FirstName)
	p.Cobuyer.LastName = strings.TrimSpace(p.Cobuyer.LastName)
	p.Cobuyer.Address = strings.TrimSpace(p.Cobuyer.Address)
	p.Cobuyer.City = strings.TrimSpace(p.Cobuyer.City)
	p.Cobuyer.StateCode = strings.TrimSpace(p.Cobuyer.StateCode)
	p.Cobuyer.PostalCode = strings.TrimSpace(p.Cobuyer.PostalCode)
	p.Cobuyer.Email = strings.TrimSpace(p.Cobuyer.Email)
	p.Cobuyer.Address = strings.TrimSpace(p.Cobuyer.Address)
	p.Cobuyer.HomePhone = strings.TrimSpace(p.Cobuyer.HomePhone)
	p.Cobuyer.AltPhone = strings.TrimSpace(p.Cobuyer.AltPhone)
}

func getContractEditExtPayload(req *http.Request) (contractEditExtPayload, error) {
	decoder := json.NewDecoder(req.Body)

	payload := contractEditExtPayload{}
	err := decoder.Decode(&payload)
	if err != nil {
		err = errors.Wrap(err, "error decoding contract_edit_ext payload")
		return payload, err
	}

	return payload, nil
}

// ContractUpdate updates contract customer details and/or cobuyer in whiz
func ContractUpdate(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {

	payload, err := getContractEditExtPayload(req)
	if err != nil {
		return http.StatusBadRequest, ErrorMessage(err, "Bad request", nil)
	}

	payload.Customer.UpdatedByUserID = user.ID
	payload.Cobuyer.UpdatedByUserID = user.ID
	payload.Cobuyer.CreatedByUserID = user.ID
	payload.UpdateByUserEmail = user.Email

	id := chi.URLParam(req, "id")
	if id == "" {
		return http.StatusBadRequest, ErrorMessage(errors.New("Invalid request"), "Get contract request failed.", nil)
	}

	return contractUpdateExt(w, req, payload, id)
}

func contractUpdateExt(w http.ResponseWriter, req *http.Request, payload contractEditExtPayload, contractNumber string) (int, map[string]interface{}) {
	payload.clean()
	validationErrors, err := payload.validate()
	if err != nil {
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage(err, "Error during validation of contract details", nil)
	}
	if len(validationErrors) > 0 {
		return http.StatusBadRequest, ErrorMessage(err, "Validation error(s)", map[string]interface{}{"validation_errors": validationErrors})
	}

	buf := bytes.NewBuffer([]byte{})
	enc := json.NewEncoder(buf)
	err = enc.Encode(payload)
	if err != nil {
		err = errors.Wrap(err, "could not encode JSON for whiz-contracts-edit request")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage(err, "could not marshal JSON for whiz-contracts-edit request", nil)
	}

	url := conf.Get().Whiz.BaseURL + "/ext/contracts/" + contractNumber

	if conf.Get().Whiz.Log {
		log.Println("[whiz-contracts-edit] request URL:", url, "Payload:", buf.String())
	}

	whizReq, err := http.NewRequest("PUT", url, buf)
	if err != nil {
		err = errors.Wrap(err, "could not create Whiz-contracts-edit request")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage(err, "could not create whiz-contracts-edit request", nil)
	}

	whizReq.Header.Set("Content-Type", "application/json")
	whizReq.Header.Set("Accept", "application/json")

	salt := []byte(conf.Get().Whiz.AuthSalt)
	bodyAndSalt := append(buf.Bytes(), salt...)
	calcChecksum := sha512.Sum512(bodyAndSalt)
	whizReq.Header.Set("Whiz-Checksum", hex.EncodeToString(calcChecksum[:]))

	txn := w.(newrelic.Transaction)

	client := http.Client{Timeout: time.Second * 30}
	resp, err := nr.External(txn, client, whizReq)
	if err != nil {
		err = errors.Wrap(err, "Invalid response for Whiz-contracts-edit")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage(err, "Invalid response for Whiz-contracts-edit", nil)
	}
	defer resp.Body.Close()

	bodyBytes := make([]byte, 102400) // 100KB buffer for response
	n, err := io.ReadFull(resp.Body, bodyBytes)
	if err != nil && err != io.EOF && err != io.ErrUnexpectedEOF {
		err = errors.Wrap(err, "Invalid response for Whiz-contracts-edit")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage(err, "Invalid response for Whiz-contracts-edit", nil)
	}
	bodyBytes = bodyBytes[:n]
	if conf.Get().Whiz.Log {
		log.Println("[Whiz-contracts-edit] response status:", resp.Status, "body:", string(bodyBytes))
	}

	if resp.StatusCode != http.StatusOK {
		errorMessage := struct {
			Message string `json:"message" db:"-"`
		}{}
		err = json.Unmarshal(bodyBytes, &errorMessage)
		if err != nil {
			err = errors.Wrap(err, "Invalid response for Whiz-contracts-edit")
			ReportError(req, err)
			return http.StatusInternalServerError, ErrorMessage(err, "Invalid response for Whiz-contracts-edit", nil)
		}
		// forwarding the status code and message from whiz as is
		return resp.StatusCode, ErrorMessage(err, errorMessage.Message, nil)
	}

	resID := struct {
		ID int `json:"id"`
	}{}

	err = json.Unmarshal(bodyBytes, &resID)
	if err != nil {
		err = errors.Wrap(err, "Invalid response for Whiz-contracts-edit")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage(err, "Invalid response for Whiz-contracts-edit", nil)
	}

	return http.StatusOK, map[string]interface{}{
		"id": resID.ID,
	}
}

// CoverageDetails gets the list coverage items for given contract from whiz
func CoverageDetails(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	code := chi.URLParam(req, "code")

	if code == "" {
		return http.StatusBadRequest, ErrorMessage(nil, "Contract code is missing", nil)
	}

	url := conf.Get().Whiz.BaseURL + "/ext/contracts/coverage/" + code

	if conf.Get().Whiz.Log {
		log.Println("[Whiz-contract-coverage] request URL:", url)
	}

	whizReq, err := http.NewRequest("GET", url, nil)
	if err != nil {
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage(err, "could not create Whiz-contract-coverage request", nil)
	}

	whizReq.Header.Set("Content-Type", "application/json")
	whizReq.Header.Set("Accept", "application/json")
	salt := []byte(conf.Get().Whiz.AuthSalt)
	checksum := sha512.Sum512(salt)
	whizReq.Header.Set("Whiz-Checksum", hex.EncodeToString(checksum[:]))

	txn := w.(newrelic.Transaction)

	client := http.Client{Timeout: time.Second * 30}
	resp, err := nr.External(txn, client, whizReq)
	if err != nil {
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage(err, "Invalid response for Whiz-contract-coverage", nil)
	}
	defer resp.Body.Close()

	bodyBytes := make([]byte, 1024000) // 1000KB buffer for response
	n, err := io.ReadFull(resp.Body, bodyBytes)
	if err != nil && err != io.EOF && err != io.ErrUnexpectedEOF {
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage(err, "Invalid response for Whiz-contract-coverage", nil)
	}
	bodyBytes = bodyBytes[:n]
	if conf.Get().Whiz.Log {
		log.Println("[Whiz-contract-coverage] response status", resp.Status, "body", string(bodyBytes))
	}

	if resp.StatusCode != http.StatusOK {
		err = errors.New(fmt.Sprintf("invalid response for Whiz-contract-coverage: %d", resp.StatusCode))
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage(err, "Invalid response for Whiz-contract-coverage", nil)
	}

	type coverageDetails struct {
		Code              int         `db:"-" json:"code"`
		Covered           string      `db:"-" json:"covered"`
		ProductTypeID     int         `db:"-" json:"product_type_id"`
		GroupName         string      `db:"-" json:"group_name"`
		Description       string      `db:"-" json:"description"`
		ProductName       string      `db:"_" json:"product_name"`
		OptionCode        null.String `db:"_" json:"option_code"`
		UpdatedOptionCode null.String `db:"_" json:"updated_option_code"`
	}

	data := struct {
		ContractFormID  int               `json:"contractFormID"`
		CoverageDetails []coverageDetails `json:"coverageDetails"`
	}{}

	err = json.Unmarshal(bodyBytes, &data)
	if err != nil {
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage(err, "Invalid response for Whiz-contract-coverage", nil)
	}

	return http.StatusOK, map[string]interface{}{
		"data": data,
	}
}

// ContractCoverageAsPdf generates the pdf for covered components
func ContractCoverageAsPdf(w http.ResponseWriter, req *http.Request, user db.User) {
	fpdf := gofpdf.New("Portrait", "in", "Letter", "")

	reqPayloadData := vehicleCoverageRequest{}
	dec := json.NewDecoder(req.Body)
	err := dec.Decode(&reqPayloadData)
	if err != nil {
		_ = r.Text(w, http.StatusBadRequest, "Bad request"+err.Error())
	}

	if len(reqPayloadData.CoverageDetails) == 0 {
		_ = r.Text(w, http.StatusBadRequest, "Coverage list of contract is empty")
		return
	}

	coverageComponentsPDF(fpdf, reqPayloadData)

	w.Header().Set("Content-Type", "application/pdf")
	w.Header().Set("Content-Disposition", fmt.Sprintf("inline; filename=\"Contract_coverage.pdf\""))
	w.WriteHeader(http.StatusOK)
	err = fpdf.Output(w)
	if err != nil {
		err = errors.Wrap(err, "error writing contract coverage components")
		_ = r.Text(w, http.StatusBadRequest, "error writing contract coverage components")
		ReportError(req, err)
		return
	}
}

// ContractNoteIndex returns the contract list, uses whiz data
func ContractNoteIndex(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {

	code := chi.URLParam(req, "code")

	url := conf.Get().Whiz.BaseURL + "/ext/contracts/" + code + "/notes"

	if conf.Get().Whiz.Log {
		log.Println("[Whiz-contract-notes-index] request URL:", url)
	}

	whizReq, err := http.NewRequest("GET", url, nil)
	if err != nil {
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage(err, "could not create Whiz-contract-notes-index request", nil)
	}

	whizReq.Header.Set("Content-Type", "application/json")
	whizReq.Header.Set("Accept", "application/json")
	salt := []byte(conf.Get().Whiz.AuthSalt)
	checksum := sha512.Sum512(salt)
	whizReq.Header.Set("Whiz-Checksum", hex.EncodeToString(checksum[:]))

	txn := w.(newrelic.Transaction)

	client := http.Client{Timeout: time.Second * 30}
	resp, err := nr.External(txn, client, whizReq)
	if err != nil {
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage(err, "Invalid response for Whiz-contract-notes-index", nil)
	}
	defer resp.Body.Close()

	bodyBytes := make([]byte, 1024000) // 1000KB buffer for response
	n, err := io.ReadFull(resp.Body, bodyBytes)
	if err != nil && err != io.EOF && err != io.ErrUnexpectedEOF {
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage(err, "Invalid response for Whiz-contract-notes-index", nil)
	}
	bodyBytes = bodyBytes[:n]
	if conf.Get().Whiz.Log {
		log.Println("[Whiz-contract-notes] response status:", resp.Status, "body:", string(bodyBytes))
	}

	if resp.StatusCode != http.StatusOK {
		err = errors.New(fmt.Sprintf("invalid response for Whiz-contract-notes-index: %d", resp.StatusCode))
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage(err, "Invalid response for Whiz-contract-notes-index", nil)
	}

	type note struct {
		ID        int       `json:"id" db:"id"`
		NotesText string    `json:"notes_text" db:"notes_text"`
		CreatedAt time.Time `json:"created_at" db:"created_at"`
		UserName  string    `json:"user_name" db:"user_name"`
	}

	data := struct {
		Notes []note `json:"notes"`
	}{}

	err = json.Unmarshal(bodyBytes, &data)
	if err != nil {
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage(err, "Invalid response for Whiz-contract-notes-index", nil)
	}

	return http.StatusOK, map[string]interface{}{
		"notes": data.Notes,
	}
}

type notePayload struct {
	CreatedByUserID int    `json:"created_by_user_id"`
	NotesText       string `json:"notes_text"`
	CreatedByEmail  string `json:"created_by_email"`
}

func noteFromReq(req *http.Request) (*notePayload, error) {
	note := notePayload{}

	dec := json.NewDecoder(req.Body)
	err := dec.Decode(&note)
	if err != nil {
		return nil, errors.Wrap(err, "decoding Contract note request failed")
	}
	return &note, nil
}

// validate validates note
func (note notePayload) validate() map[string]string {
	formErrors := map[string]string{}
	if note.NotesText == "" {
		formErrors["notes_text"] = "Notes text is required"
	}
	if note.CreatedByUserID == 0 {
		formErrors["created_by_user_id"] = "Created by userID is required"
	}
	return formErrors
}

// ContractNoteCreate returns the contract list, uses whiz data
func ContractNoteCreate(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	note, err := noteFromReq(req)
	if err != nil {
		return http.StatusBadRequest, ErrorMessage(err, "Malformed contract note data for create.", nil)
	}

	note.NotesText = strings.TrimSpace(note.NotesText)
	note.CreatedByUserID = user.ID
	note.CreatedByEmail = user.Email

	formErrors := note.validate()
	if len(formErrors) > 0 {
		return http.StatusBadRequest, ErrorMessage(errors.New("Form validations errors"), "Form validations errors.",
			map[string]interface{}{"errors": formErrors})
	}

	buf := bytes.NewBuffer([]byte{})
	enc := json.NewEncoder(buf)
	err = enc.Encode(note)
	if err != nil {
		err = errors.Wrap(err, "could not encode JSON for whiz note create")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage(err, "could not marshal JSON for Whiz-contract-notes-create", nil)
	}

	code := chi.URLParam(req, "code")

	url := conf.Get().Whiz.BaseURL + "/ext/contracts/" + code + "/notes"

	if conf.Get().Whiz.Log {
		log.Println("[Whiz-contract-note-create] request URL:", url, "Payload:", buf.String())
	}

	whizReq, err := http.NewRequest("POST", url, buf)
	if err != nil {
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage(err, "could not create Whiz-contract-notes-create request", nil)
	}

	whizReq.Header.Set("Content-Type", "application/json")
	whizReq.Header.Set("Accept", "application/json")
	salt := []byte(conf.Get().Whiz.AuthSalt)
	bodyAndSalt := append(buf.Bytes(), salt...)
	checksum := sha512.Sum512(bodyAndSalt)

	whizReq.Header.Set("Whiz-Checksum", hex.EncodeToString(checksum[:]))

	txn := w.(newrelic.Transaction)

	client := http.Client{Timeout: time.Second * 30}
	resp, err := nr.External(txn, client, whizReq)
	if err != nil {
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage(err, "Invalid response for Whiz-contract-notes-create", nil)
	}
	defer resp.Body.Close()

	bodyBytes := make([]byte, 102400) // 100KB buffer for response
	n, err := io.ReadFull(resp.Body, bodyBytes)
	if err != nil && err != io.EOF && err != io.ErrUnexpectedEOF {
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage(err, "Invalid response for Whiz-contract-notes-create", nil)
	}
	bodyBytes = bodyBytes[:n]
	if conf.Get().Whiz.Log {
		log.Println("[Whiz-contract-notes-create] response status:", resp.Status, "body:", string(bodyBytes))
	}

	if resp.StatusCode != http.StatusOK {
		err = errors.New(fmt.Sprintf("invalid response for Whiz-contract-notes-create: %d", resp.StatusCode))
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage(err, "Invalid response for Whiz-contract-notes-create", nil)
	}

	data := struct {
		ID int `json:"id"`
	}{}

	err = json.Unmarshal(bodyBytes, &data)
	if err != nil {
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage(err, "Invalid response for Whiz-contract-notes-create", nil)
	}

	return http.StatusOK, map[string]interface{}{"id": data.ID}
}

// ClaimCustomerUpdate will update customer information for RO customer
func ClaimCustomerUpdate(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	id := chi.URLParam(req, "id")
	if id == "" {
		return http.StatusBadRequest, ErrorMessage(errors.New("Invalid Input"), "Invalid ClaimID", nil)
	}
	payload := struct {
		CustomerID        int    `json:"customer_id"`
		EmailAddress      string `json:"email_address"`
		PhoneNumber       string `json:"phone_number"`
		StreetAddress     string `json:"street_address"`
		City              string `json:"city"`
		State             string `json:"state"`
		PostalCode        string `json:"postal_code"`
		AltPhoneNumber    string `json:"alternate_phone_number"`
		BestContactMethod string `json:"best_contact_method"`
		ProductCode       string `json:"product_code"`
		ContractNumber    string `json:"contract_number"`
		Notes             string `json:"notes"`
	}{}
	dec := json.NewDecoder(req.Body)
	err := dec.Decode(&payload)
	if err != nil {
		return http.StatusBadRequest, ErrorMessage(err, "decoding claim request failed", nil)
	}

	contractPayload := contractEditExtPayload{
		Customer: customer{
			UpdatedByUserID: user.ID,
			Address:         payload.StreetAddress,
			City:            payload.City,
			StateCode:       payload.State,
			PostalCode:      payload.PostalCode,
			Email:           payload.EmailAddress,
			Phone:           payload.PhoneNumber,
			Notes:           payload.Notes,
		},
		UpdateByUserEmail: user.Email,
	}

	return contractUpdateExt(w, req, contractPayload, payload.ContractNumber)
}

// ContractExpire expires a contract
func ContractExpire(contractNumber, note string) error {

	payload := struct {
		EventNotes        string `json:"event_notes"`
		UpdateByUserEmail string `json:"updated_by_user_email"`
	}{
		EventNotes:        note,
		UpdateByUserEmail: "SYSTEM",
	}

	buf := bytes.NewBuffer([]byte{})
	enc := json.NewEncoder(buf)
	err := enc.Encode(payload)
	if err != nil {
		err = errors.Wrap(err, "could not encode JSON for whiz contract expire")
		return err
	}

	url := conf.Get().Whiz.BaseURL + "/ext/contracts/" + contractNumber + "/expire"

	if conf.Get().Whiz.Log {
		log.Println("[Whiz-contract-expire] request URL:", url, "Payload:", buf.String())
	}

	whizReq, err := http.NewRequest("PUT", url, buf)
	if err != nil {
		return errors.Wrap(err, "could not create Whiz-contract-expire request")
	}

	whizReq.Header.Set("Content-Type", "application/json")
	whizReq.Header.Set("Accept", "application/json")
	salt := []byte(conf.Get().Whiz.AuthSalt)
	bodyAndSalt := append(buf.Bytes(), salt...)
	checksum := sha512.Sum512(bodyAndSalt)

	whizReq.Header.Set("Whiz-Checksum", hex.EncodeToString(checksum[:]))

	client := http.Client{Timeout: time.Second * 30}

	resp, err := nr.External(nil, client, whizReq)
	if err != nil {
		return errors.Wrap(err, "invalid response for Whiz-contract-expire")
	}
	defer resp.Body.Close()

	bodyBytes := make([]byte, 102400) // 100KB buffer for response
	n, err := io.ReadFull(resp.Body, bodyBytes)
	if err != nil && err != io.EOF && err != io.ErrUnexpectedEOF {
		return errors.Wrap(err, "invalid response for Whiz-contract-expire")
	}
	bodyBytes = bodyBytes[:n]
	if conf.Get().Whiz.Log {
		log.Println("[Whiz-contract-expire] response status:", resp.Status, "body:", string(bodyBytes))
	}

	if resp.StatusCode != http.StatusOK {
		return errors.New(fmt.Sprintf("invalid response for Whiz-contracts: %d", resp.StatusCode))
	}

	data := struct {
		ID int `json:"id"`
	}{}

	err = json.Unmarshal(bodyBytes, &data)
	if err != nil {
		return errors.Wrap(err, "invalid response for Whiz-contract-expire")
	}

	return nil
}

// ContractAttachmentIndex returns the contract list, uses whiz data
func ContractAttachmentIndex(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {

	code := chi.URLParam(req, "code")

	url := conf.Get().Whiz.BaseURL + "/ext/contracts/" + code + "/attachments"

	if conf.Get().Whiz.Log {
		log.Println("[Whiz-contract-attachment-index] request URL:", url)
	}

	whizReq, err := http.NewRequest("GET", url, nil)
	if err != nil {
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage(err, "could not create Whiz-contract-attachment-index request", nil)
	}

	whizReq.Header.Set("Content-Type", "application/json")
	whizReq.Header.Set("Accept", "application/json")
	salt := []byte(conf.Get().Whiz.AuthSalt)
	checksum := sha512.Sum512(salt)
	whizReq.Header.Set("Whiz-Checksum", hex.EncodeToString(checksum[:]))

	txn := w.(newrelic.Transaction)

	client := http.Client{Timeout: time.Second * 30}
	resp, err := nr.External(txn, client, whizReq)
	if err != nil {
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage(err, "Invalid response for Whiz-contract-attachment-index", nil)
	}
	defer resp.Body.Close()

	bodyBytes := make([]byte, 102400) // 100KB buffer for response
	n, err := io.ReadFull(resp.Body, bodyBytes)
	if err != nil && err != io.EOF && err != io.ErrUnexpectedEOF {
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage(err, "Invalid response for Whiz-contract-attachment-index", nil)
	}
	bodyBytes = bodyBytes[:n]
	if conf.Get().Whiz.Log {
		log.Println("[Whiz-contract-attachment-index] response status:", resp.Status, "body:", string(bodyBytes))
	}

	if resp.StatusCode != http.StatusOK {
		err = errors.New(fmt.Sprintf("invalid response for Whiz-contract-attachment-index: %d", resp.StatusCode))
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage(err, "Invalid response for Whiz-contract-attachment-index", nil)
	}

	// contractAttachment represents the structure of the contract_attachments table
	type contractAttachment struct {
		ID               int       `json:"id"`
		CreatedAt        time.Time `json:"created_at"`
		CreatedByUserID  int       `json:"created_by_user_id"`
		ContractID       int       `json:"contract_id"`
		StoreID          int       `json:"store_id"`
		S3Bucket         string    `json:"s3_bucket"`
		S3FileName       string    `json:"s3_file_name"`
		FileName         string    `json:"file_name"`
		ContentType      string    `json:"content_type"`
		Description      string    `json:"description"`
		AttachmentTypeID null.Int  `json:"attachment_type_id"`
	}

	data := struct {
		Attachments []contractAttachment `json:"attachments"`
	}{}

	err = json.Unmarshal(bodyBytes, &data)
	if err != nil {
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage(err, "Invalid response for Whiz-contract-attachment-index", nil)
	}

	return http.StatusOK, map[string]interface{}{
		"attachments": data.Attachments,
	}
}

// ContractInspectionAttachments returns the inspection attachments
func ContractInspectionAttachments(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {

	code := chi.URLParam(req, "code")
	ctx := req.Context()
	type contractAttachment struct {
		ID              int       `json:"id" db:"id"`
		CreatedAt       time.Time `json:"created_at" db:"created_at"`
		CreatedByUserID int       `json:"created_by_user_id" db:"created_by_user_id"`
		S3Bucket        string    `json:"s3_bucket" db:"s3_bucket"`
		FileName        string    `json:"file_name" db:"file_name"`
	}

	var documentList []contractAttachment

	query := `select acd.id, acd.s3_bucket, acd.created_at, acd.created_by_user_id, acd.file_name
              from automotive_claim_documents as acd join automotive_claims as ac on ac.id = acd.automotive_claim_id 
              where acd.deleted_at is null and acd.document_type = $1 and ac.contract_number = $2 
              order by acd.created_at desc`

	err := db.Get().SelectContext(ctx, &documentList, query, db.ClaimDocumentTypeInspectionDocument, code)
	if err != nil {
		err = errors.Wrap(err, "error in getting contract inspection attachments")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage(err, "Error in getting attachments", nil)
	}

	return http.StatusOK, map[string]interface{}{
		"attachments": documentList,
	}
}

// ContractAttachmentDownload returns the contract list, uses whiz data
func ContractAttachmentDownload(w http.ResponseWriter, req *http.Request) {
	contractID := chi.URLParam(req, "contract_id")
	attachmentID := chi.URLParam(req, "id")

	url := conf.Get().Whiz.BaseURL + "/api/contracts/" + contractID + "/attachments/" + attachmentID + "/download"

	http.Redirect(w, req, url, http.StatusTemporaryRedirect)
}

// ContractFormDownload redirects the user to a contract's PDF in S3
func ContractFormDownload(w http.ResponseWriter, req *http.Request) {
	contractID := chi.URLParam(req, "id")

	url := conf.Get().Whiz.BaseURL + "/api/contracts/" + contractID + "/download"
	if conf.Get().Whiz.Log {
		log.Println("[Whiz-contract-detail] request URL:", url)
	}
	http.Redirect(w, req, url, http.StatusTemporaryRedirect)
}
