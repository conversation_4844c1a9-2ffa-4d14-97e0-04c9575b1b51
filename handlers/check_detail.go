package handlers

import (
	"database/sql"
	"fmt"
	"net/http"
	"sort"
	"strconv"
	"sync"
	"time"

	"phizz/db"
	"phizz/types"

	"github.com/go-chi/chi"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
)

type checkDetail struct {
	ID                int             `json:"id" db:"id"`
	ProductCode       string          `json:"product_code" db:"product_code"`
	RO                string          `json:"ro" db:"ro"`
	RepairDate        time.Time       `json:"repair_date" db:"repair_date"`
	VIN               string          `json:"vin" db:"vin"`
	RequestedTotal    decimal.Decimal `json:"requested_total" db:"requested_total"`
	Labor             decimal.Decimal `json:"labor" db:"labor"`
	Parts             decimal.Decimal `json:"parts" db:"parts"`
	TotalTax          decimal.Decimal `json:"total_tax" db:"total_tax"`
	Deductible        decimal.Decimal `json:"deductible" db:"deductible"`
	Paid              decimal.Decimal `json:"paid" db:"paid"`
	PaidDate          time.Time       `json:"paid_date" db:"paid_date"`
	ContractNumber    string          `json:"contract_number" db:"contract_number"`
	AdjustmentReasons []string        `json:"adjustment_reasons" db:"adjustment_reasons"`
}

type checkDetailList []checkDetail

type sortByFuncCDL func(c1, c2 *checkDetail) bool

type checkDetailListSorter struct {
	cdl    checkDetailList
	sortBy sortByFuncCDL
}

func (sbf sortByFuncCDL) sort(cdl checkDetailList, orderBy string) {
	cdls := &checkDetailListSorter{
		cdl:    cdl,
		sortBy: sbf,
	}
	if orderBy == "desc" {
		sort.Sort(sort.Reverse(cdls))
	} else {
		sort.Sort(cdls)
	}
}

// Len is part of sort.Interface.
func (c *checkDetailListSorter) Len() int {
	return len(c.cdl)
}

// Swap is part of sort.Interface.
func (c *checkDetailListSorter) Swap(i, j int) {
	c.cdl[i], c.cdl[j] = c.cdl[j], c.cdl[i]
}

// Less is part of sort.Interface. It is implemented by calling the "sortBy" closure in the sorter.
func (c *checkDetailListSorter) Less(i, j int) bool {
	return c.sortBy(&c.cdl[i], &c.cdl[j])
}

// CheckDetailList returns list of checkdetails, the api is written to be used by whiz service.
// It's doing authentication based on phizz-checksum header
// It's assumed the whiz api will do the role-based auth and then call this API
func CheckDetailList(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	if req.Header.Get("Content-Type") != "application/json" {
		return http.StatusBadRequest, ErrorMessage(errors.New("Invalid content type"), "Invalid content type", nil)
	}

	storeID := req.FormValue("store_id")
	if storeID == "" {
		return http.StatusBadRequest, ErrorMessage(errors.New("store_id is missing"), "store_id is missing", nil)
	}
	storeCode := ""
	err := db.Get().Get(&storeCode, "select code from stores where id = $1", storeID)
	if err != nil {
		return http.StatusInternalServerError, ErrorMessage(err, "error in getting store code", nil)
	}

	orderBy := ""
	sortBy := req.FormValue("sort_by")
	if sortBy != "" {
		orderBy = mapCheckDetailSort(sortBy)
	}

	sortOrder := req.FormValue("sort_order")
	if orderBy != "" && (sortOrder == "asc" || sortOrder == "desc") {
		orderBy = orderBy + " " + sortOrder
	}

	query := `select ac.id, product_code, ro, date_of_claim_received as repair_date, vin, requested_total, total_tax,
	deductible, acpc.check_amount as paid, acpc.paid_date, contract_number,
	total_labor as labor, total_parts as parts`
	fromClause := ` from automotive_claims ac join automotive_claim_payments acp on ac.id = acp.automotive_claim_id
	join automotive_claim_payment_checks acpc on acpc.automotive_claim_payments_id=acp.id
	where acpc.check_number=$1 and ac.facility_id in (select id from automotive_facilities where store_id = $2)`

	query = query + fromClause

	if orderBy != "" {
		query = query + " order by " + orderBy
	}

	isAdjReason := false // to track if we need to fetch adjustment reasons
	p := req.FormValue("page")
	// if page is not provided, all data is returned, this is required for csv and pdf
	if p != "" {
		query = query + fmt.Sprintf(" limit %d offset %d ", PerPageEntries, (GetPage(req)-1)*PerPageEntries)
		isAdjReason = true
	}

	checkDetails := []checkDetail{}

	err = db.Get().Select(&checkDetails, query, chi.URLParam(req, "check_number"), storeID)
	if err != nil {
		return http.StatusInternalServerError, ErrorMessage(err, "Error while loading check details", nil)
	}

	partsAdjustmentQuery := `select notes from automotive_claim_complaint_parts
								join automotive_claim_complaints on automotive_claim_complaints.id = automotive_claim_complaint_parts.automotive_claim_complaint_id
							 where automotive_claim_complaints.automotive_claim_id = $1 and automotive_claim_complaint_parts.notes != ''`

	laborAdjustmentQuery := `select notes from automotive_claim_complaint_labors
								join automotive_claim_complaints on automotive_claim_complaints.id = automotive_claim_complaint_labors.automotive_claim_complaint_id
							 where automotive_claim_complaints.automotive_claim_id = $1 and automotive_claim_complaint_labors.notes != ''`

	// for each claim, get total labor, total parts and manual notes / adjustment reasons
	if isAdjReason {
		var wg sync.WaitGroup
		var queryError string
		for index := range checkDetails {
			wg.Add(1)
			go func(claimIndex, claimID int) {
				adjustmentReasons := []string{}
				partsAdjustmentReasons := []string{}
				err = db.Get().Select(&partsAdjustmentReasons, partsAdjustmentQuery, claimID)
				if err != nil && err != sql.ErrNoRows {
					wg.Done()
					queryError = "Error while getting parts adjustment reasons for claim " + strconv.Itoa(claimID)
					return
				}
				adjustmentReasons = append(adjustmentReasons, partsAdjustmentReasons...)
				laborAdjustmentReasons := []string{}
				err = db.Get().Select(&laborAdjustmentReasons, laborAdjustmentQuery, claimID)
				if err != nil && err != sql.ErrNoRows {
					wg.Done()
					queryError = "Error while getting labors adjustment reasons for claim " + strconv.Itoa(claimID)
					return
				}
				adjustmentReasons = append(adjustmentReasons, laborAdjustmentReasons...)
				checkDetails[claimIndex].AdjustmentReasons = adjustmentReasons
				wg.Done()
			}(index, checkDetails[index].ID)
		}
		wg.Wait()
		if queryError != "" {
			return http.StatusInternalServerError, ErrorMessage(err, queryError, nil)
		}
	}

	countQuery := "select count(*) " + fromClause
	count := 0
	err = db.Get().Get(&count, countQuery, chi.URLParam(req, "check_number"), storeID)
	if err != nil {
		return http.StatusInternalServerError, ErrorMessage(err, "Error while getting count of check details", nil)
	}
	return http.StatusOK, map[string]interface{}{"check_details": checkDetails, "count": count}
}

// this would be used in sql sort
func mapCheckDetailSort(input string) string {
	switch input {
	case "ro":
		return "ro"
	case "repair_date":
		return "repair_date"
	case "vin":
		return "vin"
	case "requested_total":
		return "requested_total"
	case "total_tax":
		return "total_tax"
	case "deductible":
		return "deductible"
	case "paid":
		return "paid"
	case "paid_date":
		return "paid_date"
	case "contract_number":
		return "contract_number"
	case "labor":
		return "total_labor"
	case "parts":
		return "total_parts"
	}
	return ""
}

// this would be used in sql sort
func mapCheckSort(input string) string {
	switch input {
	case "payment_date":
		return "payment_date"
	case "amount":
		return "amount"
	case "check_number":
		return "check_number"
	}
	return ""
}

// ChecksByDateRange returns list of checks for a given date range and store_id
func ChecksByDateRange(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	ctx := req.Context()
	if req.Header.Get("Content-Type") != "application/json" {
		return http.StatusBadRequest, ErrorMessage(errors.New("Invalid content type"), "Invalid content type", nil)
	}

	storeID := req.FormValue("store_id")
	if storeID == "" {
		return http.StatusBadRequest, ErrorMessage(errors.New("Store_ID is missing"), "Store_ID is missing", nil)
	}
	storeCode := ""
	err := db.Get().GetContext(ctx, &storeCode, "select code from stores where id = $1", storeID)
	if err != nil {
		ReportError(req, errors.Wrap(errors.New("error in getting store code"), "error in getting store code"))
		return http.StatusInternalServerError, ErrorMessage(err, "Error in getting store code", nil)
	}

	beginDate := req.FormValue("begin_date")
	if beginDate == "" {
		ReportError(req, errors.Wrap(errors.New("begin Date is missing in request"), "begin Date is missing in request"))
		return http.StatusBadRequest, ErrorMessage(errors.New("Begin Date is missing"), "Begin Date is missing", nil)
	}

	beginTime, err := time.Parse(time.RFC3339, beginDate)
	if err != nil {
		ReportError(req, errors.Wrap(errors.New("beginDate is not in the proper date format 'YYYY-MM-DD'"), "beginDate is not in the proper date format 'YYYY-MM-DD'"))
		return http.StatusBadRequest, ErrorMessage(errors.New("BeginDate is not in the proper date format 'YYYY-MM-DD'"), "BeginDate is not in the proper date format 'YYYY-MM-DD'", nil)
	}

	endDate := req.FormValue("end_date")
	if endDate == "" {
		ReportError(req, errors.Wrap(errors.New("end Date is missing in request"), "end Date is missing in request"))
		return http.StatusBadRequest, ErrorMessage(errors.New("End Date is missing"), "End Date is missing", nil)
	}
	endTime, err := time.Parse(time.RFC3339, endDate)
	if err != nil {
		ReportError(req, errors.Wrap(errors.New("end Date is not in the proper date format 'YYYY-MM-DD'"), "end Date is not in the proper date format 'YYYY-MM-DD'"))
		return http.StatusBadRequest, ErrorMessage(errors.New("End Date is missing"), "End Date is not in the proper date format 'YYYY-MM-DD'", nil)
	}
	endTime = endTime.Add(time.Hour * time.Duration(24))

	orderBy := ""
	sortBy := req.FormValue("sort_by")
	if sortBy != "" {
		orderBy = mapCheckSort(sortBy)
	}

	sortOrder := req.FormValue("sort_order")
	if orderBy != "" && (sortOrder == "asc" || sortOrder == "desc") {
		orderBy = orderBy + " " + sortOrder
	}

	query := `select acpc.check_number,acpc.paid_date payment_date ,sum(acpc.check_amount) amount `
	fromClause := ` from automotive_claims ac join automotive_claim_payments acp on ac.id = acp.automotive_claim_id
	  join automotive_claim_payment_checks acpc on acpc.automotive_claim_payments_id=acp.id
	  where ac.facility_id in (select id from automotive_facilities where store_id = $1) and
	  acpc.paid_date > $2 and acpc.paid_date < $3 `
	groupBy := ` group by acpc.check_number,acpc.paid_date`

	query = query + fromClause + groupBy
	if orderBy == "" {
		orderBy = `acpc.paid_date asc`
	}

	if orderBy != "" {
		query = query + " order by " + orderBy
	}

	p := req.FormValue("page")
	if p != "" {
		query = query + fmt.Sprintf(" limit %d offset %d ", PerPageEntries, (GetPage(req)-1)*PerPageEntries)
	}

	checks := []struct {
		PaymentDate types.JSPQDate  `json:"payment_date" db:"payment_date"`
		Amount      decimal.Decimal `json:"amount" db:"amount"`
		CheckNumber string          `json:"check_number" db:"check_number"`
	}{}

	err = db.Get().SelectContext(ctx, &checks, query, storeID, beginTime, endTime)
	if err != nil {
		ReportError(req, errors.Wrap(errors.New("error in getting checks for given range"), "error in getting checks for given range"))
		return http.StatusInternalServerError, ErrorMessage(err, "Error while loading check details", nil)
	}

	countQuery := "select count (distinct(acpc.check_number))" + fromClause
	count := 0
	err = db.Get().GetContext(ctx, &count, countQuery, storeID, beginTime, endTime)
	if err != nil && err != sql.ErrNoRows {
		ReportError(req, errors.Wrap(errors.New("error in getting checks count for given range"), "error in getting checks for given range"))
		return http.StatusInternalServerError, ErrorMessage(err, "Error while getting count of check details", nil)
	}
	return http.StatusOK, map[string]interface{}{"checks": checks, "count": count}
}
