package handlers

import (
	"database/sql"
	"net/http"

	"phizz/db"
	"phizz/dms"
	"phizz/dmsfactory"

	"github.com/pkg/errors"
)

// ROCustomer will return ro customer information for closed complaint from CDK
func ROCustomer(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {

	var store db.Store
	facilityID := req.FormValue("facility_id")
	if facilityID == "" {
		return http.StatusBadRequest, ErrorMessage(errors.New("Invalid Input"), "Invalid facility id", nil)
	}
	query := `select st.* 
		from stores st join automotive_facilities on st.id = store_id
		where automotive_facilities.id = $1`
	err := db.Get().Unsafe().Get(&store, query, facilityID)

	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, ErrorMessage(err, "Store with DMS provider and has RO integration not found", nil)
		}
		err = errors.Wrap(err, "Database error getting store for DMS RO lookup")
		return http.StatusInternalServerError, ErrorMessage(err, "Error looking up RO Number", nil)
	}

	if !store.HasROIntegration {
		return http.StatusNotFound, ErrorMessage(errors.New("RO integration issue"), "Store with DMS provider and has RO integration not found", nil)
	}

	var ro *dms.RO
	roNumber := req.FormValue("ro_number")
	if roNumber == "" {
		return http.StatusBadRequest, ErrorMessage(errors.New("Invalid Input"), "Invalid RO number", nil)
	}

	ctx := req.Context()
	ro, err = dmsfactory.ROCustomer(ctx, &store, roNumber)
	if err != nil {
		return http.StatusInternalServerError, ErrorMessage(err, "Error in getting Customer information from CDK", nil)
	}
	if ro == nil {
		return http.StatusNotFound, ErrorMessage(err, "Customer information not found in CDK", nil)
	}

	return http.StatusOK, map[string]interface{}{"ro": ro}
}
