package handlers

import (
	"net/http"

	"github.com/go-chi/chi"

	"phizz/db"
	"phizz/dms"
	"phizz/dmsfactory"

	"database/sql"

	"github.com/pkg/errors"
)

type notFoundError interface {
	error
	IsNotFound() bool
}

// DMSRO will look for an RO given the store and the deal number.
func DMSRO(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {

	storeID := chi.URLParam(req, "storeID")
	if storeID == "" {
		return http.StatusBadRequest, ErrorMessage(nil, "Invalid store id", nil)
	}

	var store db.Store
	err := db.Get().Unsafe().Get(
		&store,
		`select * from stores where id = $1`,
		storeID,
	)
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, ErrorMessage(nil, "Store with DMS provider and has RO integration not found", nil)
		}
		err = errors.Wrap(err, "Database error getting store for DMS RO lookup")
		return http.StatusInternalServerError, ErrorMessage(err, "Error looking up RO Number", nil)
	}

	if !store.HasROIntegration {
		return http.StatusNotFound, ErrorMessage(nil, "Store with DMS provider and has RO integration not found", nil)
	}

	var roDetails []dms.RODetail
	ctx := req.Context()
	roDetails, err = dmsfactory.RODetail(ctx, &store, chi.URLParam(req, "roNumber"))
	if err != nil {
		return http.StatusInternalServerError, ErrorMessage(err, "Error in getting RO information", nil)
	}
	return http.StatusOK, map[string]interface{}{"ro": roDetails}
}
