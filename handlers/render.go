package handlers

import (
	"log"

	"gopkg.in/unrolled/render.v1"
	"io"
	"net/http"
)

const (
	// ContentCSV content csv
	ContentCSV = "text/csv"
)

var r *render.Render

func init() {
	r = render.New(render.Options{
		Layout:          "layout",
		RequirePartials: true,
	})
}

// Renderer will return the instance of render.New to be used outside of this package
func Renderer() *render.Render {
	return r
}

// ErrorMessage is used to standardize the JSON error messages sent back to the requester
func ErrorMessage(err error, message string, data map[string]interface{}) map[string]interface{} {
	if data == nil {
		data = make(map[string]interface{})
	}
	if err != nil {
		log.Printf("ERROR: %#v -- %s\n", err, message)
	}

	data["message"] = message
	return data
}

// FilePayload file payload
type FilePayload struct {
	Content  string
	FileName string
}

// CSV custom renderer.
type CSV struct {
	render.Head
}

// Render a text response.
func (t CSV) Render(w io.Writer, v interface{}) error {
	if hw, ok := w.(http.ResponseWriter); ok {
		c := hw.Header().Get(render.ContentType)
		if c != "" {
			t.Head.ContentType = c
		}
		t.Head.Write(hw)
	}

	w.Write([]byte(v.(string)))
	return nil
}

// CustomRender Custom Render
type CustomRender struct {
	*render.Render
}

// CSV Text writes out a string as plain text.
func (r CustomRender) CSV(w io.Writer, status int, v string) error {
	head := render.Head{
		ContentType: ContentCSV,
		Status:      status,
	}

	t := CSV{
		Head: head,
	}

	return r.Render.Render(w, t, v)
}

// GetCustomRender returns custom render
func GetCustomRender(r *render.Render) CustomRender {
	return CustomRender{r}
}
