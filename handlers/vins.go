package handlers

import (
	"context"
	"database/sql"
	"encoding/json"
	"net/http"

	"phizz/db"

	"github.com/jmoiron/sqlx"
	"github.com/pkg/errors"
)

type whizVINChange struct {
	OldVIN               string `db:"old_vin" json:"old_vin"`
	NewVIN               string `db:"new_vin" json:"new_vin"`
	ContractCode         string `db:"contract_code" json:"contract_code"`
	OriginalContractCode string `db:"original_contract_code" json:"original_contract_code"`
	Make                 string `db:"make" json:"make"`
	Model                string `db:"model" json:"model"`
	Year                 int    `db:"year" json:"year"`
}

// EditVINs Updates VINs to a new value
func EditVINs(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	ctx := req.Context()
	var vinChanges []whizVINChange

	dec := json.NewDecoder(req.Body)
	err := dec.Decode(&vinChanges)
	if err != nil {
		return http.StatusBadRequest, ErrorMessage(err, "Bad request", nil)
	}

	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		err = errors.Wrap(err, "unable to start DB transaction")
		ReportError(req, err)
		return http.StatusInternalServerError, nil
	}

	for _, vinChange := range vinChanges {
		err = editVIN(ctx, tx, vinChange)
		if err != nil {
			tx.Rollback()
			err = errors.Wrapf(err, "unable to update vin for %s", vinChange.OldVIN)
			ReportError(req, err)
			return http.StatusInternalServerError, nil

		}
	}

	err = tx.Commit()
	if err != nil {
		err = errors.Wrap(err, "failed to commit changes")
		ReportError(req, err)
		return http.StatusInternalServerError, nil
	}

	return http.StatusOK, map[string]interface{}{}
}

func editVIN(ctx context.Context, tx *sqlx.Tx, vinChange whizVINChange) error {
	// 1. Update automotive_claims
	err := updateAutomotiveClaims(ctx, tx, vinChange)
	if err != nil {
		return errors.Wrap(err, "failed to update automotive_claims")
	}

	// 2. Update vta_claims
	err = updateVTAClaims(ctx, tx, vinChange)
	if err != nil {
		return errors.Wrap(err, "failed to update vta_claims")
	}

	// 3. Update gap_claims
	err = updateGAPClaims(ctx, tx, vinChange)
	if err != nil {
		return errors.Wrap(err, "failed to update gap_claims")
	}

	return nil
}

func updateAutomotiveClaims(ctx context.Context, tx *sqlx.Tx, vinChange whizVINChange) error {
	query := `update automotive_claims
		set vin = :new_vin,
		make = :make,
		model = :model,
		year = :year
		where contract_number = :original_contract_code
			and vin = :old_vin
	`
	autoStmt, err := tx.PrepareNamedContext(ctx, query)
	if err != nil {
		return errors.Wrap(err, "failed to prepare statement to update automotive_claims")
	}
	defer autoStmt.Close()

	_, err = autoStmt.ExecContext(ctx, vinChange)
	if err != nil {
		return errors.Wrap(err, "failed to update automotive_claims")
	}

	return nil
}

func updateVTAClaims(ctx context.Context, tx *sqlx.Tx, vinChange whizVINChange) error {
	query := `update vta_claims
		set vin = :new_vin
		where contract_number = :original_contract_code
			and vin = :old_vin
	`
	vtaStmt, err := tx.PrepareNamedContext(ctx, query)
	if err != nil {
		return errors.Wrap(err, "failed to prepare statement to update vta_claims")
	}
	defer vtaStmt.Close()

	_, err = vtaStmt.ExecContext(ctx, vinChange)
	if err != nil {
		return errors.Wrap(err, "failed to update vta_claims")
	}
	return nil
}

// updateGAPClaims will update any gap_claims rows that need to be updated, including inserting a new entry
// into vin_records
func updateGAPClaims(ctx context.Context, tx *sqlx.Tx, vinChange whizVINChange) error {
	type gapClaimVINInfo struct {
		ID          int    `db:"id"`
		VIN         string `db:"vin"`
		CustomerID  int    `db:"customer_id"`
		VINRecordID int    `db:"vin_record_id"`
	}

	// Check if any gap_claims will be updated
	// and if there is already a vin_record for it
	var gapClaims []gapClaimVINInfo
	query := `select 
			gc.id id
			, gc.vin vin
			, gc.vin_record_id vin_record_id
			, gc.customer_id customer_id
		from gap_claims gc 
		where gc.contract_number = $1
		and gc.vin = $2`
	err := tx.SelectContext(ctx, &gapClaims, query, vinChange.OriginalContractCode, vinChange.OldVIN)
	if err != nil {
		return errors.Wrap(err, "failed to determine if any gap_claims need to be updated")
	}

	for _, gapClaim := range gapClaims {
		vinRecordID, err := getOrCreateVINRecordID(ctx, tx,
			vinChange.NewVIN, vinChange.Make, vinChange.Model, vinChange.Year, gapClaim.CustomerID)
		if err != nil {
			return errors.Wrap(err, "unable to get or create new vin_records")
		}

		updGapClaims := `update gap_claims
			set vin = $1,
				vin_record_id = $2
				where contract_number = $3
				and vin = $4`
		_, err = tx.ExecContext(ctx, updGapClaims,
			vinChange.NewVIN, vinRecordID, vinChange.OriginalContractCode, vinChange.OldVIN,
		)
		if err != nil {
			return errors.Wrap(err, "failed to update gap_claims")
		}
	}

	return nil

}

// getOrCreateVINRecordID Finds the ID of vin_records with the specified VIN
// or creates one if it does not exist
func getOrCreateVINRecordID(
	ctx context.Context,
	tx *sqlx.Tx,
	vin,
	make,
	model string,
	year,
	customerID int,
) (int, error) {
	var vinRecordID int

	query := `select id from vin_records where vin = $1`
	err := tx.GetContext(ctx, &vinRecordID, query, vin)
	if err == nil {
		return vinRecordID, nil
	}
	if err != nil && err != sql.ErrNoRows {
		return 0, errors.Wrap(err, "unable to lookup vin_records")
	}

	query = `insert into vin_records (
		created_at,
		vin, year, make, model, customer_id
	) values (
		now() at time zone 'utc',
		$1, $2, $3, $4, $5
	) returning id`
	err = tx.GetContext(ctx, &vinRecordID, query,
		vin, year, make, model, customerID,
	)
	if err != nil {
		return 0, errors.Wrap(err, "unable to create new vin_records")
	}

	return vinRecordID, nil
}
