package handlers

import (
	"context"
	"database/sql"
	"encoding/json"
	"net/http"

	"phizz/db"
	"phizz/session"

	"github.com/go-chi/chi"
	"github.com/lib/pq/hstore"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
)

// UsersIndex returns a list of claims
func UsersIndex(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	users := []struct {
		ID        int           `db:"id" json:"id"`
		Email     string        `db:"email" json:"email"`
		FirstName string        `db:"first_name" json:"first_name"`
		LastName  string        `db:"last_name" json:"last_name"`
		Roles     hstore.Hstore `db:"roles" json:"roles"`
	}{}

	_, err := session.GetUserID(req)
	if err != nil {
		return http.StatusBadRequest, ErrorMessage(nil, "Could not attribute action to user."+
			"Has the session expired?", nil)
	}

	query := `select id, email, first_name, last_name, roles from users where active = true order by first_name, last_name`
	args := []interface{}{}
	err = db.Get().Select(&users, query, args...)
	if err != nil {
		errors.Wrap(err, "Database error getting users lists")
		return http.StatusInternalServerError, ErrorMessage(err, "Error getting users lists data", nil)
	}

	count := 0
	countQuery := `select count(*) from users;`
	err = db.Get().Get(&count, countQuery, args...)
	if err != nil {
		errors.Wrap(err, "Database error getting users lists count")
		return http.StatusInternalServerError, ErrorMessage(err, "Error getting users lists data", nil)
	}
	return http.StatusOK, map[string]interface{}{"users": users, "count": count}
}

// UserLimitIndex returns a list of users with their pre approved limit
func UserLimitIndex(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	ctx := req.Context()
	var usersLimit []struct {
		db.UserApprovedLimit
		Name string `json:"name" db:"name"`
	}

	userRole := req.FormValue("role")
	if userRole == "" || (userRole != db.RoleAutoClaims && userRole != db.RoleGapClaims) {
		return http.StatusBadRequest, ErrorMessage(nil, "Invalid role", nil)
	}

	// show active users by default
	isActive := true
	if req.FormValue("show_inactive") == "true" {
		isActive = false
	}

	query := `select u.id user_id,
       				u.last_name || ' ' || u.first_name as name,
       				case when ual.approved_limit is null then 0.0 
       				    else ual.approved_limit end as approved_limit,
       				case when ual.gap_approved_limit is null then 0.0 
       				    else ual.gap_approved_limit end as gap_approved_limit,
       				case when ual.lwt_approved_limit is null then 0.0 
       				    else ual.lwt_approved_limit end as lwt_approved_limit
		  from users u
		    left join user_approved_limits ual
		        on u.id = ual.user_id
		  where u.roles -> $1 = '1' and
		  (u.active = true or u.active = $2) and
			ual.deleted_at is null
			order by name;`

	err := db.Get().SelectContext(ctx, &usersLimit, query, userRole, isActive)
	if err != nil {
		err = errors.Wrap(err, "database error getting users lists")
		return http.StatusInternalServerError, ErrorMessage(err, "Error getting users lists data", nil)
	}

	return http.StatusOK, map[string]interface{}{"users": usersLimit}
}

func validate(ctx context.Context, user db.User, newLimits []db.UserApprovedLimit) (int, error) {

	if !user.HasRole(db.RoleAutoClaimsManager) &&
		!user.HasRole(db.RoleGapClaimsManager) &&
		!user.HasRole(db.RoleProductManager) {
		return http.StatusUnauthorized, errors.New("Role does not have permission to edit user limit")
	}
	var err error
	type userLimitRoles struct {
		db.UserApprovedLimit
		Roles hstore.Hstore `db:"roles"`
	}

	var curUserLimit userLimitRoles

	hasRole := func(ulr userLimitRoles, role string) bool {
		return ulr.Roles.Map[role].Valid
	}

	query := `select u.roles, u.id as user_id,
			case when ual.approved_limit is null then 0.0
				else ual.approved_limit end as approved_limit,
			case when ual.gap_approved_limit is null then 0.0
				else ual.gap_approved_limit end as gap_approved_limit,
			case when ual.lwt_approved_limit is null then 0.0
				else ual.lwt_approved_limit end as lwt_approved_limit
			from users u
			left join user_approved_limits ual
			on u.id = ual.user_id
			where u.id=$1 and deleted_at is null;`

	for i, newUserLimit := range newLimits {
		err = db.Get().GetContext(ctx, &curUserLimit, query, newLimits[i].UserID)
		if err != nil {
			return http.StatusInternalServerError, errors.Wrap(err, "db error in getting user limit data")
		}
		// if limit is getting modified for the user, need to check role
		if !newUserLimit.ApprovedLimit.Equal(curUserLimit.ApprovedLimit) ||
			!newUserLimit.GAPApprovedLimit.Equal(curUserLimit.GAPApprovedLimit) ||
			!newUserLimit.LWTApprovedLimit.Equal(curUserLimit.LWTApprovedLimit) {
			if hasRole(curUserLimit, db.RoleAutoClaimsManager) ||
				hasRole(curUserLimit, db.RoleGapClaimsManager) {
				if !user.HasRole(db.RoleProductManager) {
					return http.StatusUnauthorized, errors.New("User does not have permission to edit manager limits")
				}
			}
		}

	}

	return http.StatusOK, nil
}

// UserLimitUpdate  updates pre approved limits for users
func UserLimitUpdate(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {

	var limits []db.UserApprovedLimit

	dec := json.NewDecoder(req.Body)
	err := dec.Decode(&limits)
	if err != nil {
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage(err, "Error while getting updated user limits", nil)
	}

	ctx := req.Context()
	status, err := validate(ctx, user, limits)
	if err != nil {
		ReportError(req, err)
		if status == http.StatusUnauthorized {
			return status, ErrorMessage(err, err.Error(), nil)
		}
		return status, ErrorMessage(err, "Error in validating user limit update", nil)
	}

	tx, err := db.Get().Beginx()
	if err != nil {
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage(err, "Database error beginning transaction for update limit", nil)
	}
	for _, userLimit := range limits {
		userData := struct {
			UserID           int             `db:"user_id"`
			ApprovedLimit    decimal.Decimal `db:"approved_limit"`
			GAPApprovedLimit decimal.Decimal `db:"gap_approved_limit"`
			LWTApprovedLimit decimal.Decimal `db:"lwt_approved_limit"`
		}{}

		err = db.Get().GetContext(ctx, &userData, `select user_id, approved_limit, gap_approved_limit, lwt_approved_limit
			from user_approved_limits where user_id = $1`, userLimit.UserID)
		if err != nil && err != sql.ErrNoRows {
			_ = tx.Rollback()
			ReportError(req, err)
			return http.StatusInternalServerError, ErrorMessage(err, "Error while loading user", nil)
		}
		if err == sql.ErrNoRows ||
			userData.ApprovedLimit.Cmp(userLimit.ApprovedLimit) != 0 ||
			userData.GAPApprovedLimit.Cmp(userLimit.GAPApprovedLimit) != 0 ||
			userData.LWTApprovedLimit.Cmp(userLimit.LWTApprovedLimit) != 0 {

			if err != sql.ErrNoRows {
				updateQ := `update user_approved_limits set deleted_at = now() at time zone 'utc' where user_id = $1`
				_, err = tx.ExecContext(ctx, updateQ, userLimit.UserID)
				if err != nil {
					_ = tx.Rollback()
					ReportError(req, err)
					return http.StatusInternalServerError, ErrorMessage(err, "Failed to update users pre approved limits", nil)
				}
			}
			// Insert limit for user
			insertQuery := `insert into user_approved_limits 
    							(
    							 user_id,
    							 approved_limit,
    							 lwt_approved_limit,
    							 gap_approved_limit,
    							 updated_at,
    							 updated_by_user_id
    							) 
							values ($1,$2, $3, $4, now() at time zone 'utc', $5)`
			_, err = tx.ExecContext(ctx, insertQuery, userLimit.UserID, userLimit.ApprovedLimit, userLimit.LWTApprovedLimit, userLimit.GAPApprovedLimit, user.ID)
			if err != nil {
				_ = tx.Rollback()
				ReportError(req, err)
				return http.StatusInternalServerError, ErrorMessage(err, "Failed to update users pre approved limits", nil)
			}
		}
	}
	err = tx.Commit()
	if err != nil {
		_ = tx.Rollback()
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage(err, "Failed to update users pre approved limits, update transaction commit failed", nil)
	}

	return http.StatusOK, map[string]interface{}{}
}

// UserDeactivate deactivates user by email id
func UserDeactivate(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	ctx := req.Context()
	email := chi.URLParam(req, "email")

	updateQuery := `update users set active = false, updated_at = now() at time zone 'utc' where email = $1 returning id`
	id := 0
	err := db.Get().GetContext(ctx, &id, updateQuery, email)
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, ErrorMessage(err, "User email not found", nil)
		}
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage(err, "Failed to deactivate user", nil)
	}

	return http.StatusOK, map[string]interface{}{}
}

// UserActivate activates user by email id
func UserActivate(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	ctx := req.Context()
	email := chi.URLParam(req, "email")

	updateQuery := `update users set active = true, updated_at = now() at time zone 'utc' where email = $1 returning id`
	id := 0
	err := db.Get().GetContext(ctx, &id, updateQuery, email)
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, ErrorMessage(err, "User email not found", nil)
		}
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage(err, "Failed to activate user", nil)
	}

	return http.StatusOK, map[string]interface{}{}
}

// ClaimOwnersGAP returns a list of gap claim owners
func ClaimOwnersGAP(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	ctx := req.Context()
	claimOwners := []struct {
		ID        int    `db:"id" json:"id"`
		FirstName string `db:"first_name" json:"first_name"`
		LastName  string `db:"last_name" json:"last_name"`
	}{}

	_, err := session.GetUserID(req)
	if err != nil {
		return http.StatusBadRequest, ErrorMessage(nil, "Could not attribute action to user."+
			"Has the session expired?", nil)
	}

	query := `select id, first_name, last_name
		from users where id in (select distinct(owner_id) from gap_claims)
		order by first_name, last_name`
	err = db.Get().SelectContext(ctx, &claimOwners, query)
	if err != nil {
		errors.Wrap(err, "database error getting gap claim owners")
		return http.StatusInternalServerError, ErrorMessage(err, "Error getting GAP claim owners data", nil)
	}

	return http.StatusOK, map[string]interface{}{"claim_owners": claimOwners}
}

// ClaimOwnersAuto returns a list of auto claim owners
func ClaimOwnersAuto(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	ctx := req.Context()
	claimOwners := []struct {
		ID        int    `db:"id" json:"id"`
		FirstName string `db:"first_name" json:"first_name"`
		LastName  string `db:"last_name" json:"last_name"`
	}{}

	_, err := session.GetUserID(req)
	if err != nil {
		return http.StatusBadRequest, ErrorMessage(nil, "Could not attribute action to user."+
			"Has the session expired?", nil)
	}

	query := `select id, first_name, last_name
		from users where id in (select distinct(owner_id) from automotive_claims)
		order by first_name, last_name`
	err = db.Get().SelectContext(ctx, &claimOwners, query)
	if err != nil {
		errors.Wrap(err, "database error getting auto claim owners")
		return http.StatusInternalServerError, ErrorMessage(err, "Error getting Auto claim owners data", nil)
	}

	return http.StatusOK, map[string]interface{}{"claim_owners": claimOwners}
}

// ClaimOwnersVTA returns a list of vta claim owners
func ClaimOwnersVTA(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	ctx := req.Context()
	claimOwners := []struct {
		ID        int    `db:"id" json:"id"`
		FirstName string `db:"first_name" json:"first_name"`
		LastName  string `db:"last_name" json:"last_name"`
	}{}

	_, err := session.GetUserID(req)
	if err != nil {
		return http.StatusBadRequest, ErrorMessage(nil, "Could not attribute action to user."+
			"Has the session expired?", nil)
	}

	query := `select id, first_name, last_name
		from users where id in (select distinct(owner_id) from vta_claims)
		order by first_name, last_name`
	err = db.Get().SelectContext(ctx, &claimOwners, query)
	if err != nil {
		errors.Wrap(err, "database error getting vta claim owners")
		return http.StatusInternalServerError, ErrorMessage(err, "Error getting VTA claim owners data", nil)
	}

	return http.StatusOK, map[string]interface{}{"claim_owners": claimOwners}
}

// ClaimOwnersLWT returns a list of lwt claim owners
func ClaimOwnersLWT(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	ctx := req.Context()
	claimOwners := []struct {
		ID        int    `db:"id" json:"id"`
		FirstName string `db:"first_name" json:"first_name"`
		LastName  string `db:"last_name" json:"last_name"`
	}{}

	_, err := session.GetUserID(req)
	if err != nil {
		return http.StatusBadRequest, ErrorMessage(nil, "Could not attribute action to user."+
			"Has the session expired?", nil)
	}

	query := `select id, first_name, last_name
		from users where id in (select distinct(owner_id) from lwt_claims)
		order by first_name, last_name`
	err = db.Get().SelectContext(ctx, &claimOwners, query)
	if err != nil {
		errors.Wrap(err, "database error getting lwt claim owners")
		return http.StatusInternalServerError, ErrorMessage(err, "Error getting LWT claim owners data", nil)
	}

	return http.StatusOK, map[string]interface{}{"claim_owners": claimOwners}
}
