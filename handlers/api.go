package handlers

import (
	"encoding/json"
	"net/http"
	"reflect"
	"time"

	"phizz/db"
	"phizz/session"

	"github.com/gorilla/csrf"
	"github.com/pkg/errors"
)

// JSONDate wraps time.Time to give a standard date format for unmarshalling JSON string to Time.
type JSONDate struct {
	time.Time
}

// UnmarshalJSON is needed to implement the json.Unmarshaler interface
func (d *JSONDate) UnmarshalJSON(b []byte) error {
	parse, err := time.Parse(`"2006-01-02"`, string(b))
	if err != nil {
		return errors.Wrap(err, "time.Parse failed")
	}
	*d = JSONDate{parse}
	return nil
}

func jsonTimeAfter(before time.Time) func(reflect.Value) bool {
	return func(value reflect.Value) bool {
		p, ok := value.Interface().(JSONDate)
		return ok && p.Time.After(before)
	}
}

// ErrEmptyRequestBody is to standardize an empty request body error.
var ErrEmptyRequestBody = errors.New("Empty request body")

func jsonReqData(req *http.Request, val interface{}) error {
	if req.Body == nil {
		return ErrEmptyRequestBody
	}
	defer func() { _ = req.Body.Close() }()
	err := json.NewDecoder(req.Body).Decode(val)
	if err != nil {
		return errors.Wrap(err, "json.NewDecoder failed")
	}
	return nil
}

// APIHandlerFunc is our special func for JSON requests
type APIHandlerFunc func(http.ResponseWriter, *http.Request) (int, map[string]interface{})

// APIHandler converts the standard HTTP handler func with our custom apiHandler.
// This will render the map[string]interface{} data to JSON.
func APIHandler(handler APIHandlerFunc) func(http.ResponseWriter, *http.Request) {
	return func(w http.ResponseWriter, req *http.Request) {
		w.Header().Set("X-CSRF-Token", csrf.Token(req))
		status, data := handler(w, req)
		_ = r.JSON(w, status, data)
	}
}

// APIFileHandler converts the standard HTTP handler func with our custom apiHandler.
// This will render the map[string]interface{} data to JSON or FilePayload data to download
func APIFileHandler(handler APIHandlerFunc) func(http.ResponseWriter, *http.Request) {
	return func(w http.ResponseWriter, req *http.Request) {
		w.Header().Set("X-CSRF-Token", csrf.Token(req))
		status, data := handler(w, req)
		if fileData, ok := data["file"]; ok {
			filePayload, ok := fileData.(FilePayload)
			if ok && status == http.StatusOK && filePayload.Content != "" {
				w.Header().Set("Content-Disposition", "attachment; filename=\""+filePayload.FileName+"\"")
				customRender := GetCustomRender(r)
				customRender.CSV(w, status, filePayload.Content)
				return
			}
		}

		_ = r.JSON(w, status, data)
	}
}

// AuthenticatedAPIHandlerFunc is our special func for authenticated JSON requests
type AuthenticatedAPIHandlerFunc func(http.ResponseWriter, *http.Request, db.User) (int, map[string]interface{})

// AuthenticatedAPIHandler converts the standard HTTP handler func with our custom AuthenticatedAPIHandler only for authenticated users.
// This will render the map[string]interface{} data to JSON.
func AuthenticatedAPIHandler(handler AuthenticatedAPIHandlerFunc, roles []string) func(http.ResponseWriter, *http.Request) {
	return func(w http.ResponseWriter, req *http.Request) {
		w.Header().Set("X-CSRF-Token", csrf.Token(req))
		user, err := session.FindCurrentUser(req)
		if err != nil {
			ReportError(req, err)
			_ = r.JSON(w, http.StatusForbidden, ErrorMessage(err, "Session error", nil))
			return
		}
		if !Permitted(req, roles) {
			_ = r.JSON(w, http.StatusForbidden, ErrorMessage(nil, "Not authorized", nil))
			return
		}
		status, data := handler(w, req, *user)
		_ = r.JSON(w, status, data)
	}
}

// Permitted tests if the current session user can access given roles.
// Always true when no roles are given.
// Always false when current session user cannot be loaded
func Permitted(req *http.Request, roles []string) bool {
	// assume nil or empty slice means anyone is permitted
	if len(roles) == 0 {
		return true
	}
	user, err := session.FindCurrentUser(req)
	// If could not get user successfully, just assume not permitted.
	// TODO: Should we log error though?
	if err != nil {
		return false
	}
	for _, role := range roles {
		if user.HasRole(role) {
			return true
		}
	}
	return false
}

// Session will return the current user and store for the session
func Session(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	ctx := req.Context()
	now := time.Now()
	var shouldFetch bool

	storeID, err := session.GetStoreID(req)
	if err != nil {
		PrepareReauthenticate(w, req)
		return http.StatusNotFound, ErrorMessage(errors.Wrap(err, "PrepareReauthenticate failed"), "Need to login", nil)
	}

	// Get last fetch time stamp
	lastBannerFetch, err := session.GetLastBannerFetch(req)
	if err != nil {
		err = errors.Wrap(err, "get banner fetch timestamp failed")
		return http.StatusNotFound, ErrorMessage(err, "Error getting session data", nil)
	}

	// If its first time or its been older than 1 minute then
	// update the timestamp and set the fetch flag
	if lastBannerFetch == nil || lastBannerFetch.Add(1*time.Minute).Before(now) {
		err = session.SetLastBannerFetch(req, &now)
		if err != nil {
			err = errors.Wrap(err, "set banner fetch timestamp failed")
			return http.StatusNotFound, ErrorMessage(err, "Error getting session data", nil)
		}
		shouldFetch = true
	}

	data, err := sessionData(ctx, user.ID, storeID, shouldFetch)
	if err != nil {
		err = errors.Wrap(err, "error getting session data")
		return http.StatusInternalServerError, ErrorMessage(err, "Error getting session data", nil)
	}
	return http.StatusOK, data
}

// Health just returns the current time
func Health(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	return http.StatusOK, map[string]interface{}{"Time": time.Now()}
}

// NotFoundHandler just returns not found page
func NotFoundHandler(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	return http.StatusNotFound, map[string]interface{}{"error": "API not found"}
}

// AuthenticatedHandlerFunc is our special func for authenticated requests
type AuthenticatedHandlerFunc func(http.ResponseWriter, *http.Request, db.User)

// AuthenticatedHandler converts the standard HTTP handler func with our custom AuthenticatedHandlerFunc only for authenticated users.
func AuthenticatedHandler(handler AuthenticatedHandlerFunc, roles []string) func(http.ResponseWriter, *http.Request) {
	return func(w http.ResponseWriter, req *http.Request) {
		user, err := session.FindCurrentUser(req)
		if err != nil {
			ReportError(req, err)
			_ = r.Text(w, http.StatusForbidden, "Session error")
			return
		}
		if !Permitted(req, roles) {
			_ = r.Text(w, http.StatusForbidden, "Not authorized")
			return
		}
		handler(w, req, *user)
	}
}
