package handlers

import (
	"bytes"
	"database/sql"
	"encoding/base64"
	"fmt"
	"net/http"
	"os/exec"
	"regexp"
	"strconv"
	"strings"
	"time"

	"phizz/db"
	"phizz/session"
	"phizz/util"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/go-chi/chi/middleware"
	"github.com/lib/pq/hstore"
	"github.com/pkg/errors"
	"github.com/stvp/rollbar"
)

// PerPageEntries per page entries to be used for pagination
const PerPageEntries = 20

// ListQueries returns a query for an admin "list" w/ pagination and a count query to get a total
// count of items in the admin "list"
func ListQueries(selectClause, fromClause, whereClause, orderClause string, perpage, page int) (string, string) {
	listQuery := fmt.Sprintf(
		"select %s from %s %s %s limit %d offset %d",
		selectClause,
		fromClause,
		where<PERSON>lause,
		orderClause,
		perpage,
		(page-1)*perpage,
	)
	countQuery := "select count(*) from " + fromClause + " " + whereClause

	return listQuery, countQuery
}

// GetPage returns the page number for use in pagination
func GetPage(req *http.Request) int {
	p := req.FormValue("page")
	page := 1
	if n, err := strconv.Atoi(p); err == nil && n > 0 {
		page = n
	}
	return page
}

// GetPageSize returns the number of items per page for use in pagination
func GetPageSize(req *http.Request, defaultSize int) int {
	p := req.FormValue("page_size")
	pageSize := defaultSize
	if n, err := strconv.Atoi(p); err == nil && n > 0 {
		pageSize = n
	}
	return pageSize
}

// UserIDQuery support function unit test
func UserIDQuery(mock sqlmock.Sqlmock) {
	q := regexp.QuoteMeta
	mock.ExpectQuery(q(`select "id", "created_at", "updated_at", "email", "first_name", "last_name", "company_id", "active", "roles", "confirmed_at", "confirmation_token_set_at", "confirmation_token", "reset_password_token", "reset_password_token_set_at" from users where id = $1`)).
		WithArgs(5103).
		WillReturnRows(sqlmock.NewRows([]string{"id", "created_at", "updated_at", "email", "first_name", "last_name", "company_id", "active", "roles", "confirmed_at", "confirmation_token_set_at", "confirmation_token", "reset_password_token", "reset_password_token_set_at"}).
			AddRow(5103, time.Time{}, time.Time{}, "<EMAIL>", "auto", "claims", 2, true, []uint8{}, time.Time{}, time.Time{}, "", "", time.Time{}))
}

// UserIDQueryWithRole support function unit test
func UserIDQueryWithRole(mock sqlmock.Sqlmock, role string) {
	hsRoles := hstore.Hstore{}
	hsRoles.Map = make(map[string]sql.NullString)
	hsRoles.Map[role] = sql.NullString{Valid: true, String: "1"}
	roles, _ := hsRoles.Value()
	q := regexp.QuoteMeta
	mock.ExpectQuery(q(`select "id", "created_at", "updated_at", "email", "first_name", "last_name", "company_id", "active", "roles", "confirmed_at", "confirmation_token_set_at", "confirmation_token", "reset_password_token", "reset_password_token_set_at" from users where id = $1`)).
		WithArgs(5103).
		WillReturnRows(sqlmock.NewRows([]string{"id", "created_at", "updated_at", "email", "first_name", "last_name", "company_id", "active", "roles", "confirmed_at", "confirmation_token_set_at", "confirmation_token", "reset_password_token", "reset_password_token_set_at"}).
			AddRow(5103, time.Time{}, time.Time{}, "<EMAIL>", "auto", "claims", 2, true, roles, time.Time{}, time.Time{}, "", "", time.Time{}))

	mock.ExpectQuery(q(`select "id", "created_at", "updated_at", "email", "first_name", "last_name", "company_id", "active", "roles", "confirmed_at", "confirmation_token_set_at", "confirmation_token", "reset_password_token", "reset_password_token_set_at" from users where id = $1`)).
		WithArgs(5103).
		WillReturnRows(sqlmock.NewRows([]string{"id", "created_at", "updated_at", "email", "first_name", "last_name", "company_id", "active", "roles", "confirmed_at", "confirmation_token_set_at", "confirmation_token", "reset_password_token", "reset_password_token_set_at"}).
			AddRow(5103, time.Time{}, time.Time{}, "<EMAIL>", "auto", "claims", 2, true, roles, time.Time{}, time.Time{}, "", "", time.Time{}))

}

// ReportError reports errors (with an HTTP context) to rollbar
func ReportError(req *http.Request, err error) {
	ctx := req.Context()
	util.LogError(ctx, err)

	reqID := middleware.GetReqID(ctx)
	reqField := rollbar.Field{Name: "request_id", Data: reqID}
	personField := rollbar.Field{Name: "person", Data: map[string]string{"id": "unknown"}}
	if val := req.Context().Value(session.ContextKeyCurrentUser); val != nil {
		if user := val.(*db.User); user != nil {
			personField.Data = map[string]string{
				"id":    strconv.Itoa(user.ID),
				"email": user.Email,
			}
		}
	}
	rollbar.RequestErrorWithStackSkip(rollbar.ERR, req, err, 1, &reqField, &personField)
}

// IsValidFileType valid file extension list for upload
func IsValidFileType(fileType string) bool {
	// TODO: need to find out which all types we want to support uploading with claim
	switch fileType {
	case ".doc":
		return true
	case ".docx":
		return true
	case ".txt":
		return true
	case ".pdf":
		return true
	case ".jpg":
		return true
	case ".jpeg":
		return true
	case ".png":
		return true
	default:
		return false
	}
	return false
}

// LoadLocOrPanic will provide locale time otherwise it will throw error
// TODO -- need to move into common.go file or common/util.go file
func LoadLocOrPanic(l string) *time.Location {
	loc, err := time.LoadLocation(l)
	if err != nil {
		panic(err)
	}
	return loc
}

// HTMLToPdf converts HTML to PDF
func HTMLToPdf(htmlText string) (string, error) {
	cmd := exec.Command("wkhtmltopdf", "-", "-")
	cmd.Stdin = strings.NewReader(htmlText)
	var out bytes.Buffer
	var stdErr bytes.Buffer
	cmd.Stdout = &out
	cmd.Stderr = &stdErr

	err := cmd.Run()
	if err != nil {
		return "", errors.Wrapf(err, "Error in HTML to PDF conversion: %s", stdErr.String())
	}
	return base64.StdEncoding.EncodeToString(out.Bytes()), nil
}
