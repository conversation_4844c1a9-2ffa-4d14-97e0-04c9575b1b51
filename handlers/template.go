package handlers

import (
	"net/http"
	"phizz/conf"
	"time"
)

// TemplateHandler with use r.HTML with the given template name.
func TemplateHandler(name string) func(http.ResponseWriter, *http.Request) {
	config := conf.Get()
	var data map[string]string
	if config.AppEnv == "development" {
		data = map[string]string{
			"js":        config.WebPackDev.BaseURL + "/js/app.js",
			"vendor":    config.WebPackDev.BaseURL + "/js/vendor.js",
			"css":       config.WebPackDev.BaseURL + "/css/app.css",
			"vendorcss": config.WebPackDev.BaseURL + "/css/vendor.css",
		}
	} else {
		data = map[string]string{
			"js":        "/static/js/app.js",
			"vendor":    "/static/js/vendor.js",
			"css":       "/static/css/app.css",
			"vendorcss": "/static/css/vendor.css",
		}
	}
	return func(w http.ResponseWriter, req *http.Request) {
		templateData := map[string]interface{}{"URLs": data, "t": time.Now().Format("20060102150405")}
		r.HTML(w, http.StatusOK, name, templateData)
	}
}
