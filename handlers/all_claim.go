package handlers

import (
	"database/sql"
	"net/http"

	"phizz/db"
	"phizz/types"

	"github.com/go-chi/chi"
	"github.com/jmoiron/sqlx"
	"github.com/pkg/errors"
	"gopkg.in/guregu/null.v3"
)

// ClaimData returns claim count and claim total for given contract number and product code
// product code is needed since there are some contract numbers used across different products ( e.g. SC and MC )
func ClaimData(res http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {

	contractArgs := struct {
		ContractNumber          string `db:"contract_number"`
		ProductCode             string `db:"product_code"`
		StatusCheckWritten      string `db:"status_check_written"`
		StatusCCPaid            string `db:"status_cc_paid"`
		StatusChargeback        string `db:"status_chargeback"`
		StatusC                 string `db:"status_c"`
		StatusDealerChargedBack string `db:"status_dealer_charged_back"`
	}{
		StatusCheckWritten:      db.AutoClaimStatusCheckWritten,
		StatusCCPaid:            db.AutoClaimStatusCCPaid,
		StatusC:                 db.GapClaimStatusCheckWritten,
		StatusChargeback:        db.AutoClaimStatusChargeback,
		StatusDealerChargedBack: db.AutoClaimStatusDealerChargedBack,
	}

	autoInProcessStatus := []string{
		db.AutoClaimStatusOpen,
		db.AutoClaimStatusReturned,
		db.AutoClaimStatusPayable,
		db.AutoClaimStatusApproved,
		db.AutoClaimStatusWaitingForCheck,
		db.AutoClaimStatusInvoiceSent,
		db.AutoClaimStatusNew,
		db.AutoClaimStatusPreAuth,
		db.AutoClaimStatusNewSB,
		db.AutoClaimStatusReversed,
		db.AutoClaimStatusWaitingForReversed,
		db.AutoClaimStatusAdjusted,
		db.AutoClaimStatusWaitingOnVendor,
		db.AutoClaimStatusNeedRentalBill,
		db.AutoClaimStatusNeedSubletBill,
		db.AutoClaimStatusNeedSMToCall,
		db.AutoClaimStatusNeedClosedAccountingRO,
		db.AutoClaimStatusNeedProofOfDeductibleReimbursement,
		db.AutoClaimStatusWaitingForChargeback,
		db.AutoClaimStatusAuthorizedCCClaim,
	}
	var query, query2, negClaimQ string
	var err error
	var args []interface{}

	contractArgs.ContractNumber = chi.URLParam(req, "contract_number")
	contractArgs.ProductCode = chi.URLParam(req, "product_code")

	switch contractArgs.ProductCode {

	case db.ProductCodeGap:
		query = `
			select 
				count(*) claim_count, 
				sum(case_reserve) claim_total 
			from gap_claims gc
			where 
				gc.contract_number = :contract_number 
				and gc.status = :status_c`

		query2 = `
			select 
				count(*) 
			from gap_claims gc
			where 
				gc.contract_number = $1 
				and gc.is_in_progress = true`
		args = append(args, contractArgs.ContractNumber)

	case db.ProductCodeTheftRegistration:
		query = `
			select 
				count(*) claim_count, 
				sum(case_reserve) claim_total 
			from vta_claims vc
			where 
				vc.contract_number = :contract_number 
				and vc.status = :status_c`

		query2 = `
			select 
				count(*) 
			from vta_claims vc
			where 
				vc.contract_number = $1 
				and vc.is_in_progress = true`
		args = append(args, contractArgs.ContractNumber)

	case db.ProductCodeService, db.ProductCodeMaintenance, db.ProductCodeCentury, db.ProductCodeDrivePur, db.ProductCodeKey,
		db.ProductCodeKeyReplacement, db.ProductCodeTireWheel, db.ProductCodeToyotaTireWheel:
		query = `
			select 
				count(*) filter (where status != :status_chargeback)  claim_count, 
				sum(estimate) claim_total 
			from automotive_claims ac
			where 
				contract_number = :contract_number 
				and product_code = :product_code 
				and status in (:status_check_written, :status_cc_paid, :status_chargeback, :status_dealer_charged_back)`

		query2 = `
			select 
				count(*) 
			from automotive_claims ac
			where 
				ac.contract_number = ? 
				and ac.status in (?)`

		// we nee to keep intacct_bill_numbers like check for older bill numbers with reverse payment
		negClaimQ = `
			select 
				sum(amount) 
			from automotive_claim_payments 
			where 
				automotive_claim_id in (
					select 
						id 
					from automotive_claims 
					where 
						contract_number = :contract_number
				) 
				and (intacct_bill_number like 'REV%' or amount < 0)`

		query2, args, _ = sqlx.In(query2, contractArgs.ContractNumber, autoInProcessStatus)
		query2 = db.Get().Rebind(query2)

	case db.ProductCodeLeaseWearTear:
		query = `
			select 
				count(*) claim_count, 
				sum(approved_amount) + sum(coalesce(lcj.amount, 0)) claim_total 
			from lwt_claims lc
			left join lwt_claim_adjustments lcj on lc.id = lcj.lwt_claim_id
			where 
				lc.contract_number = :contract_number 
				and lc.status = :status_check_written`

		query2 = `
			select 
				count(*) 
			from lwt_claims lc
			where 
				lc.contract_number = $1 
				and lc.in_progress = true`
		args = append(args, contractArgs.ContractNumber)
	}

	stmt, err := db.Get().PrepareNamed(query)
	if err != nil {
		err = errors.Wrap(err, "error preparing get for claim data")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage(err, "Error getting claim data", nil)
	}
	defer func() { _ = stmt.Close() }()

	data := struct {
		ClaimCount int        `json:"claim_count" db:"claim_count"`
		ClaimTotal null.Float `json:"claim_total" db:"claim_total"`
	}{}
	err = stmt.Get(&data, contractArgs)
	if err != nil && err != sql.ErrNoRows {
		err = errors.Wrap(err, "database error getting claim count and claim total")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage(err, "Error getting claim data", nil)
	}

	// Check if there are in process claims
	inProcessCount := 0
	hasInProcessClaims := false

	err = db.Get().Get(&inProcessCount, query2, args...)
	if err != nil && err != sql.ErrNoRows {
		err = errors.Wrap(err, "database error getting in process claims count")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage(err, "Error getting claim data", nil)
	}
	if inProcessCount != 0 {
		hasInProcessClaims = true
	}

	if negClaimQ != "" {
		stmt2, err := db.Get().PrepareNamed(negClaimQ)
		if err != nil {
			err = errors.Wrap(err, "error preparing get for claim negative total")
			ReportError(req, err)
			return http.StatusInternalServerError, ErrorMessage(err, "Error getting claim data", nil)
		}
		defer func() { _ = stmt2.Close() }()

		var negTotal null.Float
		err = stmt2.Get(&negTotal, contractArgs)
		if err != nil && err != sql.ErrNoRows {
			err = errors.Wrap(err, "database error getting negative total")
			ReportError(req, err)
			return http.StatusInternalServerError, ErrorMessage(err, "Error getting claim data", nil)
		}
		if negTotal.Valid {
			data.ClaimTotal.Float64 = data.ClaimTotal.Float64 + negTotal.Float64
		}
	}

	// if claim_count is 0, data.ClaimTotal.Valid is false and data.ClaimTotal.Float64 is 0.0, hence not checking valid
	return http.StatusOK, map[string]interface{}{"claim_count": data.ClaimCount, "claim_total": data.ClaimTotal.Float64, "has_claim_in_process": hasInProcessClaims}
}

// ClaimsByRO returns claim list for given ro
func ClaimsByRO(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	ctx := req.Context()

	ro := chi.URLParam(req, "ro")
	args := []interface{}{ro}
	selectClause := ` select ac.contract_number, ac.ro_opened_date claim_date, ac.product_code, af.store_id`
	fromClause := ` from automotive_claims ac left join automotive_facilities af on ac.facility_id = af.id
	where ro = $1 and status not in ($2,$3,$4, $5)`

	args = append(args, db.AutoClaimStatusDeactivated, db.AutoClaimStatusReversed, db.AutoClaimStatusWaitingForReversed, db.AutoClaimStatusAdjusted)

	listQuery := selectClause + fromClause
	countQuery := `select count(*)` + fromClause

	var claims []struct {
		ContractNumber string             `json:"contract_number" db:"contract_number"`
		ClaimDate      types.JSPQNullDate `json:"claim_date" db:"claim_date"`
		ProductCode    string             `json:"product_code" db:"product_code"`
		StoreID        null.Int           `json:"store_id" db:"store_id"`
	}

	err := db.Get().SelectContext(ctx, &claims, listQuery, args...)
	if err != nil {
		err = errors.Wrap(err, "Database error getting claims for contract")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage(err, "Error getting claims", nil)
	}

	count := 0
	err = db.Get().GetContext(ctx, &count, countQuery, args...)
	if err != nil && err != sql.ErrNoRows {
		err = errors.Wrap(err, "Database error getting count of claims for contract")
		ReportError(req, err)
		return http.StatusInternalServerError, ErrorMessage(err, "Error getting claims count", nil)
	}

	return http.StatusOK, map[string]interface{}{"count": count, "automotive_claims": claims}

}
