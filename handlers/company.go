package handlers

import (
	"context"
	"database/sql"
	"encoding/json"
	"net/http"
	"phizz/db"

	"github.com/jmoiron/sqlx"
	"github.com/pkg/errors"
)

// UpdateCompany udpates the company with the latest changes from whiz
func UpdateCompany(_ http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	ctx := req.Context()
	var err error
	var payload db.Company
	var autoStmt *sqlx.NamedStmt

	if err = json.NewDecoder(req.Body).Decode(&payload); err != nil {
		return http.StatusBadRequest, ErrorMessage(err, "Bad request", nil)
	}

	if payload.CompanyGroupID.Valid {
		err = createNewCompanyGroup(ctx, payload)
		if err != nil {
			ReportError(req, errors.WithMessage(err, "error creating new company group"))
			return http.StatusInternalServerError, ErrorMessage(err, "Error creating new company group", nil)
		}
	}

	query := `update companies 
			set updated_at = now() at time zone 'utc',
				name = :name,
				code = :code,
				company_group_id = :company_group_id
			where id = :id`

	if autoStmt, err = db.Get().PrepareNamedContext(ctx, query); err != nil {
		ReportError(req, errors.Wrap(err, "failed to prepare statement to update company"))
		return http.StatusInternalServerError, ErrorMessage(err, "Error updating company", nil)
	}
	defer autoStmt.Close()

	if _, err = autoStmt.ExecContext(ctx, payload); err != nil {
		ReportError(req, errors.Wrap(err, "database error to update company"))
		return http.StatusInternalServerError, ErrorMessage(err, "Error updating company", nil)
	}

	return http.StatusOK, map[string]interface{}{"comapny_id": payload.ID}
}

// CreateCompany saves store to phizz for respective whiz company creation
func CreateCompany(_ http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	ctx := req.Context()
	var companyPayload db.Company
	var err error
	var stmt *sqlx.NamedStmt
	var companyID int

	if err = json.NewDecoder(req.Body).Decode(&companyPayload); err != nil {
		return http.StatusBadRequest, ErrorMessage(err, "Malformed company data for create.", nil)
	}

	if companyPayload.CompanyGroupID.Valid {
		err = createNewCompanyGroup(ctx, companyPayload)
		if err != nil {
			ReportError(req, errors.WithMessage(err, "error creating new company group"))
			return http.StatusInternalServerError, ErrorMessage(err, "Error creating new company group", nil)
		}
	}

	query := `insert into companies (id, created_at, updated_at, name, code, company_group_id)
	values (:id, now() at time zone 'utc', now() at time zone 'utc', :name, :code, :company_group_id) returning id`

	if stmt, err = db.Get().PrepareNamedContext(ctx, query); err != nil {
		ReportError(req, errors.Wrap(err, "error creating named statement to create a company record"))
		return http.StatusInternalServerError, ErrorMessage(err, "Error creating new company record", nil)
	}

	defer func() { _ = stmt.Close() }()

	if err = stmt.GetContext(ctx, &companyID, companyPayload); err != nil {
		ReportError(req, errors.Wrap(err, "database error inserting new company"))
		return http.StatusInternalServerError, ErrorMessage(err, "Error creating new company record", nil)
	}

	return http.StatusOK, map[string]interface{}{"company_id": companyID}
}

func createNewCompanyGroup(ctx context.Context, companyPayload db.Company) error {
	var companyGroup db.CompanyGroup
	query := `select * from company_groups where id = $1`
	if err := db.Get().GetContext(ctx, &companyGroup, query, companyPayload.CompanyGroupID.Int64); err != nil {
		if err != sql.ErrNoRows {
			return errors.Wrap(err, "error fetching company group")
		}
		if err == sql.ErrNoRows && companyPayload.CompanyGroupName != "" {
			insertQuery := `insert into company_groups (id, created_at, updated_at, name)
				values ($1, now() at time zone 'utc', now() at time zone 'utc', $2) returning id`
			_, err = db.Get().ExecContext(ctx,
				insertQuery, companyPayload.CompanyGroupID.Int64,
				companyPayload.CompanyGroupName)
			if err != nil {
				return errors.Wrap(err, "error fetching company group")
			}
		}
	}

	return nil
}
