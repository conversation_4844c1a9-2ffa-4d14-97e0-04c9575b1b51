package handlers

import (
	"fmt"
	"net/http"
	"sort"
	"time"

	"phizz/db"

	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
)

type deniedClaim struct {
	ID             int             `json:"id" db:"id"`
	RO             string          `json:"ro" db:"ro"`
	RepairDate     time.Time       `json:"repair_date" db:"repair_date"`
	VIN            string          `json:"vin" db:"vin"`
	RequestedTotal decimal.Decimal `json:"requested_total" db:"requested_total"`
	Labor          decimal.Decimal `json:"labor" db:"labor"`
	Parts          decimal.Decimal `json:"parts" db:"parts"`
	TotalTax       decimal.Decimal `json:"total_tax" db:"total_tax"`
	Deductible     decimal.Decimal `json:"deductible" db:"deductible"`
	ContractNumber string          `json:"contract_number" db:"contract_number"`
	Max            time.Time       `json:"-" db:"max"`
	DeniedReason   string          `json:"denied_reason" db:"denied_reason"`
}

type deniedClaimList []deniedClaim

type sortByFuncDCL func(d1, d2 *deniedClaim) bool

type deniedClaimListSorter struct {
	dcl    deniedClaimList
	sortBy sortByFuncDCL
}

func (sbf sortByFuncDCL) sort(dcl deniedClaimList, orderBy string) {
	dcls := &deniedClaimListSorter{
		dcl:    dcl,
		sortBy: sbf,
	}
	if orderBy == "desc" {
		sort.Sort(sort.Reverse(dcls))
	} else {
		sort.Sort(dcls)
	}
}

// Len is part of sort.Interface.
func (d *deniedClaimListSorter) Len() int {
	return len(d.dcl)
}

// Swap is part of sort.Interface.
func (d *deniedClaimListSorter) Swap(i, j int) {
	d.dcl[i], d.dcl[j] = d.dcl[j], d.dcl[i]
}

// Less is part of sort.Interface. It is implemented by calling the "sortBy" closure in the sorter.
func (d *deniedClaimListSorter) Less(i, j int) bool {
	return d.sortBy(&d.dcl[i], &d.dcl[j])
}

// DeniedClaimList returns list of deniedClaims, the api is written to be used by whiz service.
// It's assumed the whiz api will do the role-based auth and then call this API
func DeniedClaimList(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	if req.Header.Get("Content-Type") != "application/json" {
		return http.StatusBadRequest, ErrorMessage(errors.New("Invalid content type"), "Invalid content type", nil)
	}

	storeID := req.FormValue("store_id")
	if storeID == "" {
		return http.StatusBadRequest, ErrorMessage(errors.New("store_id is missing"), "store_id is missing", nil)
	}
	storeCode := ""
	err := db.Get().Get(&storeCode, "select code from stores where id = $1", storeID)
	if err != nil {
		return http.StatusInternalServerError, ErrorMessage(err, "error in getting store code", nil)
	}

	productCode := req.FormValue("product_code")
	if productCode == "" {
		return http.StatusBadRequest, ErrorMessage(errors.New("product_code is missing"), "product_code is missing", nil)
	}

	orderBy := ""
	sortBy := req.FormValue("sort_by")
	if sortBy != "" {
		orderBy = mapDeniedClaimSort(sortBy)
	}

	sortOrder := req.FormValue("sort_order")
	if orderBy != "" && (sortOrder == "asc" || sortOrder == "desc") {
		orderBy = orderBy + " " + sortOrder
	}

	query := `select ac.id, ro, date_of_claim_received as repair_date, vin, requested_total, total_tax,
	deductible, contract_number, canceled_reason as denied_reason, max(updated_at)
	from automotive_claims ac join automotive_claim_updates acu on acu.automotive_claim_id = ac.id
	where status=$1 and product_code=$2 and ac.facility_id in (select id from automotive_facilities where store_id = $3)
	group by ac.id,ro,date_of_claim_received,vin,requested_total,total_tax,deductible,contract_number,denied_reason
	having max(updated_at) > (current_date - 120)`

	if orderBy != "" {
		query = query + " order by " + orderBy
	}

	p := req.FormValue("page")
	// if page is not provided, all data is returned, this is required for csv and pdf
	if p != "" {
		query = query + fmt.Sprintf(" limit %d offset %d ", PerPageEntries, (GetPage(req)-1)*PerPageEntries)
	}

	deniedClaims := []deniedClaim{}

	err = db.Get().Select(&deniedClaims, query, db.AutoClaimComplaintStatusDenied, productCode, storeID)
	if err != nil {
		return http.StatusInternalServerError, ErrorMessage(err, "Error while loading denied details", nil)
	}

	laborQuery := `select case when sum(approved) is null then 0.0 else sum(approved) end as labor
		from automotive_claim_complaint_labors
		where automotive_claim_complaint_id in
		(select id from automotive_claim_complaints where automotive_claim_id = $1)`

	partsQuery := `select case when sum(approved) is null then 0.0 else sum(approved) end as parts
		from automotive_claim_complaint_parts
		where automotive_claim_complaint_id in
		(select id from automotive_claim_complaints where automotive_claim_id = $1)`

	// for each claim, get total labor, total parts and manual notes / adjustment reasons
	for i := range deniedClaims {
		err = db.Get().Get(&deniedClaims[i].Labor, laborQuery, deniedClaims[i].ID)
		if err != nil {
			return http.StatusInternalServerError, ErrorMessage(err, "Error while getting sum(approved) labor", nil)
		}

		err = db.Get().Get(&deniedClaims[i].Parts, partsQuery, deniedClaims[i].ID)
		if err != nil {
			return http.StatusInternalServerError, ErrorMessage(err, "Error while getting sum(approved) parts", nil)
		}
	}

	if sortBy == "labor" || sortBy == "parts" {
		switch sortBy {
		case "labor":
			sortByFuncDCL(func(d1, d2 *deniedClaim) bool {
				return d1.Labor.LessThan(d2.Labor)
			}).sort(deniedClaims, sortOrder)
		case "parts":
			sortByFuncDCL(func(d1, d2 *deniedClaim) bool {
				return d1.Parts.LessThan(d2.Parts)
			}).sort(deniedClaims, sortOrder)
		}
	}

	countQuery := `select count(*) as count from
		(select 1 from automotive_claims ac join automotive_claim_updates acu on acu.automotive_claim_id = ac.id
		where status=$1 and product_code=$2 and ac.facility_id in (select id from automotive_facilities where store_id = $3)
		group by ac.id,ro,date_of_claim_received,vin,requested_total,total_tax,deductible,contract_number,canceled_reason
		having max(updated_at) > (current_date - 120)) x`
	count := 0
	err = db.Get().Get(&count, countQuery, db.AutoClaimComplaintStatusDenied, productCode, storeID)
	if err != nil {
		return http.StatusInternalServerError, ErrorMessage(err, "Error while getting count of denied claims", nil)
	}

	return http.StatusOK, map[string]interface{}{"denied_claims": deniedClaims, "count": count}
}

// this would be used in sql sort
func mapDeniedClaimSort(input string) string {
	switch input {
	case "ro":
		return "ro"
	case "repair_date":
		return "repair_date"
	case "vin":
		return "vin"
	case "requested_total":
		return "requested_total"
	case "total_tax":
		return "total_tax"
	case "deductible":
		return "deductible"
	case "contract_number":
		return "contract_number"
	}
	return ""
}
