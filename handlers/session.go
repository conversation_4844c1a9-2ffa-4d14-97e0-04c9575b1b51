package handlers

import (
	"bytes"
	"context"
	"crypto/rand"
	"crypto/sha512"
	"database/sql"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/url"
	"time"

	"phizz/conf"
	"phizz/db"
	"phizz/nr"
	"phizz/session"

	"github.com/lib/pq/hstore"
	newrelic "github.com/newrelic/go-agent"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
)

const (
	loginTokenSize           = 32
	authTimestampDuration    = time.Minute
	loginTokenExpireDuration = time.Minute
)

func sessionData(ctx context.Context, userID, currentStoreID int, shouldFetch bool) (map[string]interface{}, error) {
	const (
		currentStoreKey = "CurrentStore"
	)
	user := session.User{
		Stores: []session.Store{},
	}

	err := db.Get().Unsafe().GetContext(ctx, &user, `select * from users where active and id = $1`, userID)
	if err != nil {
		return nil, errors.Wrap(err, "error getting user for session data")
	}

	var approvalLimits struct {
		ApprovedLimit    decimal.Decimal `json:"approved_limit" db:"approved_limit"`
		GAPApprovedLimit decimal.Decimal `json:"gap_approved_limit" db:"gap_approved_limit"`
		LWTApprovedLimit decimal.Decimal `json:"lwt_approved_limit" db:"lwt_approved_limit"`
	}

	query := `select approved_limit, gap_approved_limit, lwt_approved_limit
	from user_approved_limits
	where user_id = $1 and deleted_at is null`
	err = db.Get().GetContext(ctx, &approvalLimits, query, user.ID)
	if err != nil && err != sql.ErrNoRows {
		return nil, errors.Wrap(err, "error while fetching pre approved limit for user")
	}

	user.ApprovalLimit = approvalLimits.ApprovedLimit
	user.LWTApprovalLimit = approvalLimits.LWTApprovedLimit
	user.GAPApprovalLimit = approvalLimits.GAPApprovedLimit
	user.AppEnv = conf.Get().AppEnv

	// Depending on the flag decide if we should fetch
	// or pull data from the local db
	if shouldFetch {
		// Fetch the banner data from whiz
		user.BannerInfo, err = fetchBannerInfo(ctx)
		if err != nil {
			return nil, errors.Wrap(err, "error while fetching banner info for user")
		}

		//Update local database
		err = updateBannerInfo(ctx, user.BannerInfo)
		if err != nil {
			return nil, errors.Wrap(err, "error while fetching banner info for user")
		}
	} else {
		// If not fetching then get it form local db
		user.BannerInfo, err = getBannerInfo(ctx)
		if err != nil {
			return nil, errors.Wrap(err, "error while fetching banner info for user")
		}
	}

	data := map[string]interface{}{
		"User": user,
	}
	// TODO the phizz project is using just the user for now, stores_users is not implemented in phizz yet
	/*
		err = db.Get().Unsafe().Select(&user.Stores, `select s.* from stores s join stores_users su on su.store_id = s.id where su.user_id = $1 order by s.code asc`, userID)
		if err != nil {
			errors.Wrap(err, "error getting user store list for session data")
			return nil, err
		}



		// attempt to find the current store via currentStoreID if that fails fall back to
		// the first store to which the user belongs otherwise the current store is nil
		var store session.Store
		err = db.Get().Unsafe().Get(&store, `select s.*, c.inspection_set_id from stores s join stores_users su on su.store_id = s.id join companies c on c.id = s.company_id where s.id = $1 and su.user_id = $2`, currentStoreID, userID)
		if err != nil {
			if err == sql.ErrNoRows {
				err = db.Get().Unsafe().Get(&store, `select s.*, c.inspection_set_id from stores s join stores_users su on su.store_id = s.id join companies c on c.id = s.company_id where su.user_id = $1 limit 1`, userID)
				if err != nil {
					if err == sql.ErrNoRows {
						data[currentStoreKey] = nil
					} else {
						return nil, err
					}
				} else {
					data[currentStoreKey] = store
				}
			} else {
				err = errors.Wrap(err, "db error getting user data for session data")
				return nil, err
			}
		} else {
			data[currentStoreKey] = store
		}
	*/
	return data, nil
}

func getBannerInfo(ctx context.Context) (session.BannerInfo, error) {
	var bannerInfo session.BannerInfo
	query := `select header, message, enabled from banner_info where enabled = true`
	err := db.Get().GetContext(ctx, &bannerInfo, query)
	if err != nil && err != sql.ErrNoRows {
		err = errors.Wrap(err, "db error getting banner data for session data")
		return bannerInfo, err
	}
	return bannerInfo, nil
}

func fetchBannerInfo(ctx context.Context) (session.BannerInfo, error) {
	txn := newrelic.FromContext(ctx)
	var bannerData struct {
		BannerInfo session.BannerInfo `db:"-" json:"banner_info"`
	}

	url := conf.Get().Whiz.BaseURL + "/ext/banner-info"

	if conf.Get().Whiz.Log {
		log.Println("[Whiz-banner-info] request URL:", url)
	}

	whizReq, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return bannerData.BannerInfo, errors.Wrap(err, "could not create Whiz-banner-info request")
	}

	whizReq.Header.Set("Content-Type", "application/json")
	whizReq.Header.Set("Accept", "application/json")
	salt := []byte(conf.Get().Whiz.AuthSalt)
	checksum := sha512.Sum512(salt)
	whizReq.Header.Set("Whiz-Checksum", hex.EncodeToString(checksum[:]))

	client := http.Client{Timeout: time.Second * 30}
	resp, err := nr.External(txn, client, whizReq)
	if err != nil {
		return bannerData.BannerInfo, errors.Wrap(err, "invalid response for Whiz-banner-info")
	}
	defer resp.Body.Close()

	bodyBytes := make([]byte, 1024000) // 1000KB buffer for response
	n, err := io.ReadFull(resp.Body, bodyBytes)
	if err != nil && err != io.EOF && err != io.ErrUnexpectedEOF {
		return bannerData.BannerInfo, errors.Wrap(err, "invalid response for Whiz-banner-info")
	}

	bodyBytes = bodyBytes[:n]
	if conf.Get().Whiz.Log {
		log.Println("[Whiz-banner-info] response status:", resp.Status, "body:", string(bodyBytes))
	}

	if resp.StatusCode != http.StatusOK {
		return bannerData.BannerInfo, errors.New(fmt.Sprintf("invalid response for Whiz-banner-info: %d", resp.StatusCode))
	}

	err = json.Unmarshal(bodyBytes, &bannerData)
	if err != nil {
		return bannerData.BannerInfo, errors.New(fmt.Sprintf("invalid response for Whiz-banner-info: %d", resp.StatusCode))
	}

	return bannerData.BannerInfo, nil
}

func updateBannerInfo(ctx context.Context, b session.BannerInfo) error {
	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		return errors.Wrap(err, "error creating transaction")
	}

	var existingInfo session.BannerInfo
	query := `select * from banner_info`
	err = tx.Unsafe().GetContext(ctx, &existingInfo, query)
	if err != nil {
		// This will only happen for the first time
		// and if we manually delete the data from table
		if err == sql.ErrNoRows {
			query = `insert into banner_info
			(header, message, enabled, created_at)
			values(:header, :message, :enabled, now() at time zone 'utc')`

			stmt, err := tx.PrepareNamedContext(ctx, query)
			if err != nil {
				_ = tx.Rollback()
				return errors.Wrap(err, "error creating prepared statement")
			}

			defer func() { _ = stmt.Close() }()

			_, err = stmt.ExecContext(ctx, b)
			if err != nil {
				_ = tx.Rollback()
				return errors.Wrap(err, "error inserting banner information")
			}

			err = tx.Commit()
			if err != nil {
				return errors.Wrap(err, "error committing banner information")
			}
			return nil
		}

		_ = tx.Rollback()
		return errors.Wrap(err, "error getting banner info")
	}

	updateQuery := `update banner_info 
					set header = :header,
						message = :message,
						enabled = :enabled,
						created_at = now() at time zone 'utc'`
	updateStmt, err := tx.PrepareNamedContext(ctx, updateQuery)
	if err != nil {
		_ = tx.Rollback()
		return errors.Wrap(err, "error creating prepared statement")
	}

	defer func() { _ = updateStmt.Close() }()
	_, err = updateStmt.ExecContext(ctx, b)
	if err != nil {
		_ = tx.Rollback()
		return errors.Wrap(err, "error updating banner info")
	}

	err = tx.Commit()
	if err != nil {
		return errors.Wrap(err, "error committing banner information")
	}
	return nil
}

type loginParams struct {
	Email    string
	Password string
}

type userInfoPayload struct {
	UserID    int      `json:"user_id"`
	Email     string   `json:"email"`
	FirstName string   `json:"first_name"`
	LastName  string   `json:"last_name"`
	Roles     []string `json:"roles"`
	Company   struct {
		ID   int    `json:"id"`
		Code string `json:"code"`
		Name string `json:"name"`
	}
	Stores []struct {
		ID    int    `json:"id"`
		Name  string `json:"name"`
		Code  string `json:"code"`
		State string `json:"state"`
	}
	Message string `json:"message"`
}

// Sample Response
/*
resp := `{"user_id":5085,"email":"<EMAIL>",
			"first_name":"Gap",
			"last_name":"Claims",
			"employee_number":null,
			"job_title":null,
			"super_admin":false,
			"roles":["gap_claims"], / other possible role is gap_claims_manager
			"company":{"code":"TCA","name":"Total Care Auto [TEST]"},
			"stores":[{"id":*********,"name":"Total Care Auto","code":"TCA","state":"UT"}],
			"message":"Valid email and password"}`
*/

// Auth authenticates and returns token to use to login
func Auth(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	ctx := req.Context()

	if req.Header.Get("Content-Type") != "application/json" {
		return http.StatusBadRequest, ErrorMessage(errors.New("Invalid content type"), "Invalid content type", nil)
	}

	givenChecksum := req.Header.Get("Phizz-Checksum")
	var buf bytes.Buffer
	tr := io.TeeReader(req.Body, &buf)      // TeeReader so that we can read it again during json decode.
	body := make([]byte, req.ContentLength) // TODO: Validate certain size or let the server configuration worry about that?
	_, err := io.ReadFull(tr, body)
	if err != nil {
		return http.StatusBadRequest, ErrorMessage(err, "Invalid data", nil)
	}
	salt := []byte(conf.Get().AuthSalt)
	bodyAndSalt := append(body, salt...)
	calcChecksum := sha512.Sum512(bodyAndSalt)
	if givenChecksum != hex.EncodeToString(calcChecksum[:]) {
		return http.StatusBadRequest, ErrorMessage(errors.New("Invalid data (bad checksum)"), "Invalid data (bad checksum)", nil)
	}

	payload := struct {
		CompanyID      int       `json:"company_id"`
		Timestamp      time.Time `json:"timestamp"`
		Email          string    `json:"email"`
		FirstName      string    `json:"first_name"`
		LastName       string    `json:"last_name"`
		Roles          []string  `json:"roles"`
		EmployeeNumber string    `json:"employee_number"`
	}{}
	dec := json.NewDecoder(&buf)
	err = dec.Decode(&payload)
	if err != nil {
		return http.StatusBadRequest, ErrorMessage(err, "Invalid data", nil)
	}

	if payload.Email == "" {
		return http.StatusBadRequest, ErrorMessage(errors.New("Invalid email"), "Invalid email", nil)
	}

	if payload.FirstName == "" {
		return http.StatusBadRequest, ErrorMessage(errors.New("Invalid first_name"), "Invalid first_name", nil)
	}

	if payload.LastName == "" {
		return http.StatusBadRequest, ErrorMessage(errors.New("Invalid last_name"), "Invalid last_name", nil)
	}

	if len(payload.Roles) < 1 {
		return http.StatusBadRequest, ErrorMessage(errors.New("Invalid roles"), "Invalid roles", nil)
	}

	t := payload.Timestamp.UTC()
	now := time.Now().UTC()
	if t.Before(now.Add(-authTimestampDuration)) || t.After(now.Add(authTimestampDuration)) {
		return http.StatusBadRequest, ErrorMessage(errors.New("Invalid timestamp"), "Invalid timestamp", nil)
	}

	roles := hstore.Hstore{
		Map: map[string]sql.NullString{},
	}
	for _, role := range payload.Roles {
		roles.Map[role] = sql.NullString{
			Valid:  true,
			String: "1",
		}
	}

	token, err := generateLoginToken()
	if err != nil {
		ReportError(req, errors.Wrap(err, "error generating login token"))
		return http.StatusInternalServerError, ErrorMessage(err, "Error authenticating", nil)
	}

	user := db.User{
		CompanyID:           payload.CompanyID,
		Email:               payload.Email,
		FirstName:           payload.FirstName,
		LastName:            payload.LastName,
		Roles:               roles,
		LoginToken:          token,
		LoginTokenExpiresAt: time.Now().UTC().Add(loginTokenExpireDuration),
		EmployeeNumber:      payload.EmployeeNumber,
	}
	query := `insert into users (
		created_at, updated_at, company_id, email, first_name, last_name, roles,
		login_token, login_token_expires_at, employee_number
	) values (
		now() at time zone 'utc', now() at time zone 'utc', :company_id, :email, :first_name, :last_name, :roles,
		:login_token, :login_token_expires_at, :employee_number
	)
	on conflict (email) do update set
	updated_at = now() at time zone 'utc', first_name = :first_name, last_name = :last_name,
	roles = :roles, login_token = :login_token, login_token_expires_at = :login_token_expires_at,
	employee_number = :employee_number
	returning id, created_at, updated_at`
	stmt, err := db.Get().PrepareNamedContext(ctx, query)
	if err != nil {
		ReportError(req, errors.Wrap(err, "error preparing upsert user"))
		return http.StatusInternalServerError, ErrorMessage(err, "Error authenticating", nil)
	}
	defer func() { _ = stmt.Close() }()

	err = stmt.GetContext(ctx, &user, user)
	if err != nil {
		ReportError(req, errors.Wrap(err, "error executing upsert user"))
		return http.StatusInternalServerError, ErrorMessage(err, "Error authenticating", nil)
	}
	/**/
	loginURL := conf.Get().AppURL + "/login-with-token?email=" + url.QueryEscape(payload.Email) + "&token=" + url.QueryEscape(token)
	return http.StatusOK, map[string]interface{}{"login_url": loginURL}
}

func generateLoginToken() (string, error) {
	b := make([]byte, loginTokenSize)
	_, err := rand.Read(b)
	if err != nil {
		return "", err
	}
	return base64.URLEncoding.EncodeToString(b), nil
}

// LoginWithToken Login checks token and sets up a session for the user
func LoginWithToken(w http.ResponseWriter, req *http.Request) {
	if req.FormValue("email") == "" {
		_ = r.Text(w, http.StatusBadRequest, "Invalid email")
		return
	}
	if req.FormValue("token") == "" {
		_ = r.Text(w, http.StatusBadRequest, "Invalid token")
		return
	}

	user := struct {
		ID         int    `db:"id" json:"id"`
		LoginToken string `db:"login_token" json:"login_token"`
	}{}
	query := `select id, login_token from users where email = $1 and login_token = $2 and login_token_expires_at > $3`
	err := db.Get().Get(&user, query, req.FormValue("email"), req.FormValue("token"), time.Now().UTC())
	if err != nil {
		if err == sql.ErrNoRows {
			_ = r.Text(w, http.StatusBadRequest, "Login invalid")
			return
		}
		_ = r.Text(w, http.StatusInternalServerError, "Error logging in")
		err = errors.Wrap(err, "database error on users select")
		ReportError(req, err)
		return
	}

	err = session.SetUserID(req, user.ID)
	if err != nil {
		_ = r.Text(w, http.StatusInternalServerError, "Error logging in")
		err = errors.Wrap(err, "error setting user ID for session")
		ReportError(req, err)
		return
	}
	err = session.SetLastActive(req, time.Now()) // TODO check error here?
	if err != nil {
		_ = r.Text(w, http.StatusInternalServerError, "Error logging in")
		err = errors.Wrap(err, "error setting last active for session")
		ReportError(req, err)
		return
	}

	http.Redirect(w, req, "/", http.StatusTemporaryRedirect)
}

// Logout will clear out sensitive session data
func Logout(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	PrepareReauthenticate(w, req)
	return http.StatusOK, map[string]interface{}{}
}

// PrepareReauthenticate clears out the sensitive session data and sets standard headers
func PrepareReauthenticate(w http.ResponseWriter, req *http.Request) {
	session.Logout(req)
	w.Header().Set("TCA-Authenticate", "1")
}
