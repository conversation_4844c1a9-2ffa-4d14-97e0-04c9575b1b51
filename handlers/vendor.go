package handlers

import (
	"context"
	"net/http"

	"phizz/intacct"

	"github.com/pkg/errors"
)

// Vendor payload for intacct vendor
type Vendor struct {
	VendorID string `xml:"VENDORID" json:"vendor_id"`
	Name     string `xml:"DISPLAYCONTACT.COMPANYNAME" json:"name"`
	Address1 string `xml:"DISPLAYCONTACT.MAILADDRESS.ADDRESS1" json:"address1"`
	Address2 string `xml:"DISPLAYCONTACT.MAILADDRESS.ADDRESS2" json:"address2"`
	City     string `xml:"DISPLAYCONTACT.MAILADDRESS.CITY" json:"city"`
	State    string `xml:"DISPLAYCONTACT.MAILADDRESS.STATE" json:"state"`
	Zip      string `xml:"DISPLAYCONTACT.MAILADDRESS.ZIP" json:"zip"`
	Country  string `xml:"DISPLAYCONTACT.MAILADDRESS.COUNTRY" json:"country"`
}

// GetVendors returns list of vendors either by name or by vendor id
func GetVendors(_ http.ResponseWriter, r *http.Request) (int, map[string]interface{}) {
	ctx := r.Context()
	name := r.FormValue("name")
	if name == "" {
		return http.StatusInternalServerError, ErrorMessage(nil, "Search Criteria for vendor list is needed", nil)
	}

	// get vendor query payload
	vendorQuery := intacct.VendorQueryPayload{}.
		Name(name).
		VendorID(name)

	// get vendors from intacct
	vendors, err := intacct.GetVendors(ctx, vendorQuery)
	if err != nil {
		return http.StatusInternalServerError, ErrorMessage(err, "Error in getting vendors from intacct", nil)
	}
	if len(vendors) == 0 {
		return http.StatusNotFound, map[string]interface{}{}
	}
	zip := r.FormValue("zip")
	if zip != "" {
		for _, vendor := range vendors {
			if vendor.Zip == zip {
				return http.StatusOK, map[string]interface{}{"vendors": []intacct.Vendor{vendor}, "count": 1}
			}
		}
	}

	return http.StatusOK, map[string]interface{}{"vendors": vendors, "count": len(vendors)}
}

// GetVendorID returns vendor id if vendor details like bankname, bankzip and bankaddress1 matches with vendors list
func GetVendorID(ctx context.Context, bankName, bankZip, bankAddress1 string) (string, error) {
	// get vendor query payload
	vendorQuery := intacct.VendorQueryPayload{}.
		Name(bankName)

	// get vendors from intacct
	vendors, err := intacct.GetVendors(ctx, vendorQuery)
	if err != nil {
		return "", err
	}

	vendorID := ""
	for _, vendor := range vendors {
		if vendor.Zip == bankZip && vendor.Address1 == bankAddress1 {
			vendorID = vendor.VendorID
			return vendorID, nil
		}
	}
	return vendorID, errors.New("Vendor not found")
}
