package ucs

import (
	"bytes"
	"context"
	"encoding/xml"
	"fmt"
	"io"
	"net/http"
	"phizz/conf"
	"phizz/dms"
	"phizz/nr"
	"phizz/util"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
	newrelic "github.com/newrelic/go-agent"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
)

const (
	// BusinessFlag is the IBFlag value for a Business record
	BusinessFlag = "B"
	// PhoneTypeHome is the PhoneType value for a Home number
	PhoneTypeHome = "H"
	// PhoneTypeBusiness is the PhoneType for a Business or Work number
	PhoneTypeBusiness = "B"
	// PhoneTypeCellular is the PhoneType for a Cellular number
	PhoneTypeCellular = "C"
	// IsCommercialFlag is the value for if the vehicle is a commercial vehicle
	IsCommercialFlag = "Y"
	// DealDateFormat is the Date format string for the Deal Date
	DealDateFormat = "01/02/2006"
	// RODateFormat is the Date format string for the RO Date
	RODateFormat = "01/02/2006"
	urlTemplate  = "{{host}}/Sync/RCI/Asbury/Receive.ashx"
	userAgent    = "TCAClient"
	contentZero  = "content0"
)

var (
	removePasswordXMLRegex = regexp.MustCompile(`\<wsse:Password\>(.*)\</wsse:Password\>`)
)

type notFoundError struct {
	error
}

func (e notFoundError) IsNotFound() bool {
	return true
}

type userError struct {
	error
	userErrorMessage string
}

func newUserError(err error, msg string) error {
	return &userError{
		error:            err,
		userErrorMessage: msg,
	}
}

func (e userError) UserErrorMessage() string {
	return e.userErrorMessage
}

func createNewROQueryRequest(dealerNumber string, storeNumber string, areaNumber string, roNumber string) *roRequestSoapEnvelope {
	ro := &RepairOrderTypeRequest{
		RoNoAttr: roNumber,
	}

	applicationArea := createApplicationArea(dealerNumber, storeNumber, areaNumber, taskROQuery)

	request := &ReyAsburyRepairOrderReq{
		Namespace:       queryNamespace,
		ApplicationArea: applicationArea,
		RepairOrders:    []*RepairOrderTypeRequest{},
	}
	request.RepairOrders = append(request.RepairOrders, ro)

	content := &ROQueryRequestContentType{
		ID: contentZero,
	}
	content.Request = request

	payload := &ServiceRORequestPayloadType{
		Namespace:             processMessageNamespace,
		SoapEnvelopeNamespace: soapEnvelopeNamespace,
		XSINamespace:          xsiNamespace,
		XSDNamespace:          xsdNamespace,
		WSANamespace:          wsaNamespace,
		WSSENamespace:         wsseNamespace,
		WSUNamespace:          wsuNamespace,
		Content:               content,
	}

	processMessage := &ServiceROQueryRequestProcessMessage{
		Namespace: processMessageNamespace,
		Payload:   payload,
	}

	return createROSoapRequest(processMessage)
}

func createApplicationArea(dealerNumber string, storeNumber string, areaNumber string, task string) *ApplicationAreaType {
	// Create Destination element
	destination := &DestinationType{
		DestinationNameCode: destinationNameCode,
		DealerNumber:        dealerNumber,
		StoreNumber:         storeNumber,
		AreaNumber:          areaNumber,
	}

	// Create Sender element
	sender := &SenderType{
		Component:   component,
		Task:        task,
		ReferenceID: referenceID,
	}

	// Create ApplicationArea element
	applicationArea := &ApplicationAreaType{
		BODId:            uuid.New().String(),
		CreationDateTime: time.Now().Format(time.RFC3339),
		Sender:           sender,
		Destination:      destination,
	}

	return applicationArea
}

func retrieveRO(ctx context.Context, dealerNumber string, storeNumber string, areaNumber string, roNumber string) (*ROQueryProcessMessageResponse, error) {
	req := createNewROQueryRequest(dealerNumber, storeNumber, areaNumber, roNumber)

	responseBytes, err := request(ctx, req)
	if err != nil {
		return nil, errors.WithMessage(err, "[UCS] error getting ro")
	}

	var response roResponseSoapEnvelope
	err = xml.Unmarshal(responseBytes, &response)
	if err != nil {
		return nil, errors.Wrap(err, "[UCS] error un-marshalling ucs ro response")
	}

	return response.Body.ProcessMessageResponse, nil
}

func request(ctx context.Context, envelope interface{}) ([]byte, error) {
	txn := newrelic.FromContext(ctx)

	ucsConf := conf.Get().UCS

	url := strings.Replace(urlTemplate, "{{host}}", ucsConf.Host, 1)

	envelopeXML, err := xml.MarshalIndent(envelope, "", "\t")
	if err != nil {
		err = errors.WithMessage(err, "[UCS] error building request XML")
		return nil, err
	}
	requestXML := fmt.Sprintf("%s%s", xml.Header, envelopeXML)

	cleanXMLData := removePassword(requestXML)

	// Log the Reqwest based on the Log Level
	if ucsConf.LogLevel != conf.LogLevelNone {
		logMessage := fmt.Sprintf("[UCS] URL: %s Data: %+v", url, cleanXMLData)
		util.LogMessage(ctx, logMessage)
	}
	body := bytes.NewReader([]byte(requestXML))

	req, err := http.NewRequest("POST", url, body)
	if err != nil {
		return nil, err
	}
	req.Header.Set("Content-Type", `text/xml; charset="utf-8"`)
	req.Header.Set("User-Agent", userAgent)

	client := http.Client{Timeout: time.Second * 60}

	resp, err := nr.External(txn, client, req)
	if err != nil {
		status := "nil"
		if resp != nil {
			status = resp.Status
		}
		return nil, errors.Wrap(err, fmt.Sprintf("[UCS] URL: %s, Data: %v, Response Status: %s", url, cleanXMLData, status))
	}
	defer func() { _ = resp.Body.Close() }()
	responseBytes, n, err := util.ReadResponse(resp.Body)
	if err != nil && err != io.EOF && err != io.ErrUnexpectedEOF {
		return nil, errors.Wrap(err, fmt.Sprintf("[UCS] URL: %s DATA: %s Response Status: %s", url, cleanXMLData, resp.Status))
	}

	// Remove the XML DocType element
	responseBytes = []byte(strings.Replace(string(responseBytes), xml.Header, "", 1))
	if ucsConf.LogLevel != conf.LogLevelNone {
		if resp.StatusCode != 200 {
			logFullResponse(ctx, url, resp.Status, int(n), responseBytes)
		} else {
			logResponse(ctx, url, resp.Status, int(n), responseBytes)
		}
	}

	if resp.StatusCode != 200 {
		var sf soapFaultResponseEnvelope
		err = xml.Unmarshal(responseBytes, &sf)
		if err != nil {
			return nil, fmt.Errorf("[UCS] bad response http status: %s", resp.Status)
		}

		return nil, fmt.Errorf("[UCS] bad response http status: %s fault code: %s message: %s", resp.Status, sf.Body.Fault.FaultCode, sf.Body.Fault.FaultString)
	}

	return responseBytes, nil
}

// logResponse logs a UCS response if logging is enabled.
// The full response body may be logged if full CDK logging is enabled or if the CDK Response payload included a CDK error
// Otherwise, only the response status and the length of the response body is logged.
func logResponse(ctx context.Context, url string, respStatus string, length int, respBody []byte) {
	ucsConf := conf.Get().UCS
	if ucsConf.LogLevel == conf.LogLevelNone {
		return
	}

	// Determine if we need to log the full response body
	logFullBody := false
	if ucsConf.LogLevel == conf.LogLevelFull {
		logFullBody = true
	}

	if logFullBody {
		logFullResponse(ctx, url, respStatus, length, respBody)
	} else {
		logSummaryResponse(ctx, url, respStatus, length)
	}
}

func logSummaryResponse(ctx context.Context, url string, respStatus string, length int) {
	util.LogMessagef(ctx, "[UCS] URL: %s, Response Status: %s, Response Body Length: %d", url, respStatus, length)
}

func removePassword(str string) string {
	return removePasswordXMLRegex.ReplaceAllString(str, "<wsse:Password>[Removed]</wsse:Password>")
}

func formatErrorMessage(message string, url string, dealerID string, dmsNumber string, status string) string {
	return fmt.Sprintf("[UCS]Message: %s, URL: %s, DealerID: %s,"+
		"DMSNumber: %s, Response Status: %s", message, url, dealerID, dmsNumber, status)
}

func logRequest(ctx context.Context, requestStr string, url string, dealerID string, dmsNumber string) {
	util.LogMessagef(ctx, "[UCS]URL: %s, DealerID: %s, DMS Number: %s, RequestBody: %s", url, dealerID, dmsNumber, requestStr)
}

func logFullResponse(ctx context.Context, url string, respStatus string, length int, respBody []byte) {
	util.LogMessagef(ctx, "[UCS]URL: %s, Response Status: %s, Response Body Length: %d, Response XML Formatted: %s", url, respStatus, length, string(respBody))
}

// RODetail fetches the repair order details from UCS
func RODetail(ctx context.Context, dealerNumber string, storeNumber string, areaNumber string, roNumber string) ([]dms.RODetail, error) {
	processMessageResponse, err := retrieveRO(ctx, dealerNumber, storeNumber, areaNumber, roNumber)
	if err != nil {
		return nil, errors.WithMessagef(err, "[UCS] error getting ro %s", roNumber)
	}

	ros := processMessageResponse.Payload.Content.Response.RepairOrders

	if len(ros) == 0 {
		err := notFoundError{
			error: fmt.Errorf("[UCS] ro %s was not found", roNumber),
		}
		return nil, err
	}

	dmsROs := mapToDMSRO(processMessageResponse.Payload.Content.Response)
	if err != nil {
		return nil, errors.WithMessage(err, "[UCS] error mapping ucs ro to dms ro")
	}

	return dmsROs, nil
}

func mapCustomer(r *RepairOrderType) dms.Customer {
	//Customer
	var c dms.Customer
	c.FirstName = r.CustRecord.ContactInfo.FirstNameAttr
	c.LastName = r.CustRecord.ContactInfo.LastNameAttr
	if len(r.CustRecord.ContactInfo.Address) == 0 {
		c.Address = r.CustRecord.ContactInfo.Address[0].Addr1Attr
		c.City = r.CustRecord.ContactInfo.Address[0].CityAttr
		c.State = r.CustRecord.ContactInfo.Address[0].StateAttr
		c.PostalCode = r.CustRecord.ContactInfo.Address[0].ZipAttr
	}
	if r.CustRecord.ContactInfo.Email != nil {
		c.Email = r.CustRecord.ContactInfo.Email.MailToAttr
	}
	if len(r.CustRecord.ContactInfo.Phone) > 0 {
		c.Phone = r.CustRecord.ContactInfo.Phone[0].NumAttr
	}
	return c
}
func mapToDMSRO(roResp *ReyAsburyRepairOrderResp) []dms.RODetail {
	var ros []dms.RODetail
	if roResp.GenTransStatus.StatusAttr != "Success" {
		return ros
	}
	for _, r := range roResp.RepairOrders {
		var roDetail dms.RODetail

		//Vehicle
		roDetail.Vehicle.Odometer, _ = strconv.Atoi(r.RoRecord[0].Rogen.MileageInAttr)
		roDetail.Vehicle.VIN = r.RoRecord[0].Rogen.VinAttr

		//Customer
		roDetail.Customer = mapCustomer(r)

		for _, rr := range r.RoRecord {
			var roLine dms.ROLine

			// Labors
			for _, rrOpCodeLI := range rr.Rolabor.OpCodeLaborInfo {

				var roLabor dms.ROLabor

				roLine.LineCode = rrOpCodeLI.OpCodeAttr
				if len(rrOpCodeLI.TechInfo) > 0 {
					roLine.TechID = rrOpCodeLI.TechInfo[0].TechNoAttr
					roLabor.SoldHours, _ = decimal.NewFromString(rrOpCodeLI.TechInfo[0].TechHrsAttr)
				}
				if len(rrOpCodeLI.CCCStmts) > 0 {
					roLine.ComplaintDescription = rrOpCodeLI.CCCStmts[0].ComplaintAttr
					roLine.Correction = rrOpCodeLI.CCCStmts[0].CorrectionAttr
				}
				if len(rrOpCodeLI.RoAmts) > 0 {
					roLabor.LaborSale, _ = decimal.NewFromString(rrOpCodeLI.RoAmts[0].TotalAmtAttr)
				}
				roLine.Labors = append(roLine.Labors, roLabor)

				roDetail.Date, _ = time.Parse("01/02/2006", rr.Rogen.RoCreateDateAttr)
			}

			// Parts
			for _, rrPart := range rr.Ropart.PartInfoByJob {
				for _, rrPartDetail := range rrPart.PartDetail {
					var roPart dms.ROPart
					roPart.PartNumber = rrPartDetail.PartNoAttr
					roPart.Description = rrPartDetail.PartNoDescAttr
					if len(rrPartDetail.RoAmts) > 0 {
						roPart.Cost, _ = decimal.NewFromString(rrPartDetail.RoAmts[0].CustPriceAttr)
						roPart.PartsSale, _ = decimal.NewFromString(rrPartDetail.RoAmts[0].TxblAmtAttr)
						roPart.Quantity, _ = strconv.Atoi(rrPartDetail.CustQtyShipAttr)
					}
					roLine.Parts = append(roLine.Parts, roPart)
				}
			}
			roDetail.ROLines = append(roDetail.ROLines, &roLine)
		}
		ros = append(ros, roDetail)
	}
	return ros
}

// ROCustomer fetches Service Sales Closed RO details from UCS and maps to dms.RO
func ROCustomer(ctx context.Context, dealerNumber string, storeNumber string, areaNumber string, roNumber string) (*dms.RO, error) {
	processMessageResponse, err := retrieveRO(ctx, dealerNumber, storeNumber, areaNumber, roNumber)
	if err != nil {
		return nil, errors.WithMessagef(err, "[UCS] error getting ro %s", roNumber)
	}

	ros := processMessageResponse.Payload.Content.Response.RepairOrders

	if len(ros) == 0 {
		err := notFoundError{
			error: fmt.Errorf("[UCS] ro %s was not found", roNumber),
		}
		return nil, err
	}
	return &dms.RO{
		Customer: mapCustomer(ros[0]),
	}, nil
}
