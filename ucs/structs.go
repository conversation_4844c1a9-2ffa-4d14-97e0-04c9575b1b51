package ucs

import (
	"encoding/xml"
)

const (
	queryNamespace          = "http://www.starstandards.org/STAR"
	processMessageNamespace = "http://www.starstandards.org/webservices/2005/10/transport"

	component           = "Asbury"
	taskDealQuery       = "DS"
	taskROQuery         = "SRO"
	referenceID         = "Query"
	destinationNameCode = "RR"
	testDealerNumber    = "QUS003"
	testStoreNumber     = "QUS003"
	testAreaNumber      = "QUS003"
)

// ServiceROQueryRequestProcessMessage represents a query for a Service RO
type ServiceROQueryRequestProcessMessage struct {
	XMLName   xml.Name                     `xml:"ProcessMessage"`
	Namespace string                       `xml:"xmlns,attr"`
	Payload   *ServiceRORequestPayloadType `xml:"payload"`
}

// ServiceRORequestPayloadType ...
type ServiceRORequestPayloadType struct {
	Namespace             string                     `xml:"xmlns,attr"`
	SoapEnvelopeNamespace string                     `xml:"xmlns:soap,attr"`
	XSINamespace          string                     `xml:"xmlns:xsi,attr"`
	XSDNamespace          string                     `xml:"xmlns:xsd,attr"`
	WSANamespace          string                     `xml:"xmlns:wsa,attr"`
	WSSENamespace         string                     `xml:"xmlns:wsse,attr"`
	WSUNamespace          string                     `xml:"xmlns:wsu,attr"`
	Content               *ROQueryRequestContentType `xml:"content"`
}

// ROQueryRequestContentType ...
type ROQueryRequestContentType struct {
	ID      string `xml:"id,attr,omitempty"`
	Request *ReyAsburyRepairOrderReq
}

// ApplicationAreaType is Unique Identifier for the transaction generated by Sender (ie: GUID) ef097f3a-01b2-1eca-b12a-80048cbb74f3 The BODId is a key tracking identifier in RIH.
type ApplicationAreaType struct {
	Sender           *SenderType      `xml:"Sender"`
	CreationDateTime string           `xml:"CreationDateTime"`
	BODId            string           `xml:"BODId"`
	Destination      *DestinationType `xml:"Destination"`
}

// ResponseApplicationAreaType is Unique Identifier for the transaction generated by Sender (ie: GUID) ef097f3a-01b2-1eca-b12a-80048cbb74f3 The BODId is a key tracking identifier in RIH.
type ResponseApplicationAreaType struct {
	Sender           *ResponseSenderType      `xml:"Sender"`
	CreationDateTime string                   `xml:"CreationDateTime"`
	BODId            string                   `xml:"BODId"`
	Destination      *ResponseDestinationType `xml:"Destination"`
}

// DestinationType is The branch of the store.
type DestinationType struct {
	DestinationNameCode string `xml:"DestinationNameCode"`
	DealerNumber        string `xml:"DealerNumber"`
	StoreNumber         string `xml:"StoreNumber"`
	AreaNumber          string `xml:"AreaNumber"`
}

// ResponseDestinationType is The branch of the store.
type ResponseDestinationType struct {
	DestinationNameCode string `xml:"DestinationNameCode"`
}

// SenderType ...
type SenderType struct {
	Component   string `xml:"Component"`
	Task        string `xml:"Task"`
	ReferenceID string `xml:"ReferenceId"`
}

// ResponseSenderType ...
type ResponseSenderType struct {
	Component    string `xml:"Component"`
	Task         string `xml:"Task"`
	DealerNumber string `xml:"DealerNumber"`
	StoreNumber  string `xml:"StoreNumber"`
	AreaNumber   string `xml:"AreaNumber"`
	ReferenceID  string `xml:"ReferenceId"`
}

// PhoneType ...
type PhoneType struct {
	TypeAttr string `xml:"Type,attr,omitempty"`
	NumAttr  string `xml:"Num,attr,omitempty"`
	ExtAttr  string `xml:"Ext,attr,omitempty"`
}

// EmailType ...
type EmailType struct {
	MailToAttr string `xml:"MailTo,attr,omitempty"`
}

// ROQueryProcessMessageResponse response the respone from querying a Service RO
type ROQueryProcessMessageResponse struct {
	XMLName xml.Name                    `xml:"ProcessMessageResponse"`
	Payload *ROQueryResponsePayloadType `xml:"payload"`
}

// ROQueryResponsePayloadType ...
type ROQueryResponsePayloadType struct {
	Content *ROQueryResponseContentType `xml:"content"`
}

// ROQueryResponseContentType ...
type ROQueryResponseContentType struct {
	ID       string `xml:"id,attr,omitempty"`
	Response *ReyAsburyRepairOrderResp
}

// ReyAsburyRepairOrderResp ...
type ReyAsburyRepairOrderResp struct {
	XMLName         xml.Name                     `xml:"rey_AsburyRepairOrder"`
	ApplicationArea *ResponseApplicationAreaType `xml:"ApplicationArea"`
	GenTransStatus  *GenTransStatusType          `xml:"GenTransStatus"`
	RepairOrders    []*RepairOrderType           `xml:"RepairOrder"`
}

// GenTransStatusType ...
type GenTransStatusType struct {
	StatusAttr     string `xml:"Status,attr,omitempty"`
	StatusCodeAttr string `xml:"StatusCode,attr,omitempty"`
}

// AllGogTotalAmtsType ...
type AllGogTotalAmtsType struct {
	AllTotAmtTypeAttr string         `xml:"AllTotAmtType,attr,omitempty"`
	RoAmts            []*RoAmtsType5 `xml:"RoAmts"`
}

// AllSubTotalAmtsType ...
type AllSubTotalAmtsType struct {
	AllTotAmtTypeAttr string         `xml:"AllTotAmtType,attr,omitempty"`
	RoAmts            []*RoAmtsType6 `xml:"RoAmts"`
}

// BillTimeRateHrsType ...
type BillTimeRateHrsType struct {
	BillRateAttr string `xml:"BillRate,attr,omitempty"`
}

// CCCStmtsType ...
type CCCStmtsType struct {
	CorrectionAttr string `xml:"Correction,attr,omitempty"`
	CauseAttr      string `xml:"Cause,attr,omitempty"`
	ComplaintAttr  string `xml:"Complaint,attr,omitempty"`
}

// CustRecordType ...
type CustRecordType struct {
	ContactInfo *ContactInfoType1Response `xml:"ContactInfo"`
}

// OpCodeLaborInfoType ...
type OpCodeLaborInfoType struct {
	JobStatusAttr   string                 `xml:"JobStatus,attr,omitempty"`
	UpSellFlagAttr  string                 `xml:"UpSellFlag,attr,omitempty"`
	TechNoteAttr    string                 `xml:"TechNote,attr,omitempty"`
	JobNoAttr       string                 `xml:"JobNo,attr,omitempty"`
	OpCodeAttr      string                 `xml:"OpCode,attr,omitempty"`
	OpCodeDescAttr  string                 `xml:"OpCodeDesc,attr,omitempty"`
	BillTimeRateHrs []*BillTimeRateHrsType `xml:"BillTimeRateHrs"`
	TechInfo        []*TechInfoType        `xml:"TechInfo"`
	CCCStmts        []*CCCStmtsType        `xml:"CCCStmts"`
	RoAmts          []*RoAmtsType2         `xml:"RoAmts"`
}

// PartDetailType ...
type PartDetailType struct {
	CustQtyShipAttr string         `xml:"CustQtyShip,attr,omitempty"`
	SeqNoAttr       string         `xml:"SeqNo,attr,omitempty"`
	PartNoDescAttr  string         `xml:"PartNoDesc,attr,omitempty"`
	PartNoAttr      string         `xml:"PartNo,attr,omitempty"`
	RoAmts          []*RoAmtsType3 `xml:"RoAmts"`
}

// PartInfoByJobType ...
type PartInfoByJobType struct {
	JobNoAttr  string            `xml:"JobNo,attr,omitempty"`
	OpCodeAttr string            `xml:"OpCode,attr,omitempty"`
	PartDetail []*PartDetailType `xml:"PartDetail"`
}

// RecommendedServcType ...
type RecommendedServcType struct {
	RecSvcPerformFlagAttr string `xml:"RecSvcPerformFlag,attr,omitempty"`
	RecSvcOpCdDescAttr    string `xml:"RecSvcOpCdDesc,attr,omitempty"`
	RecSvcOpCodeAttr      string `xml:"RecSvcOpCode,attr,omitempty"`
}

// RoCommentInfoType ...
type RoCommentInfoType struct {
	RoCommentAttr string `xml:"RoComment,attr,omitempty"`
}

// RoRecordType ...
type RoRecordType struct {
	Rogen   *RogenType   `xml:"Rogen"`
	Rolabor *RolaborType `xml:"Rolabor"`
	Ropart  *RopartType  `xml:"Ropart"`
	Romisc  *RomiscType  `xml:"Romisc"`
	Rogog   *RogogType   `xml:"Rogog"`
	Rosub   *RosubType   `xml:"Rosub"`
}

// RogenType ...
type RogenType struct {
	IntrRoTotalAmtAttr  string                  `xml:"IntrRoTotalAmt,attr,omitempty"`
	WarrRoTotalAmtAttr  string                  `xml:"WarrRoTotalAmt,attr,omitempty"`
	CustRoTotalAmtAttr  string                  `xml:"CustRoTotalAmt,attr,omitempty"`
	FinalPostDateAttr   string                  `xml:"FinalPostDate,attr,omitempty"`
	IntrPostDateAttr    string                  `xml:"IntrPostDate,attr,omitempty"`
	WarrPostDateAttr    string                  `xml:"WarrPostDate,attr,omitempty"`
	CustInvoiceDateAttr string                  `xml:"CustInvoiceDate,attr,omitempty"`
	VoidDateAttr        string                  `xml:"VoidDate,attr,omitempty"`
	PromiseTimeAttr     string                  `xml:"PromiseTime,attr,omitempty"`
	PromiseDateAttr     string                  `xml:"PromiseDate,attr,omitempty"`
	RoCreateTimeAttr    string                  `xml:"RoCreateTime,attr,omitempty"`
	RoCreateDateAttr    string                  `xml:"RoCreateDate,attr,omitempty"`
	MileageOutAttr      string                  `xml:"MileageOut,attr,omitempty"`
	MileageInAttr       string                  `xml:"MileageIn,attr,omitempty"`
	CarlineDescAttr     string                  `xml:"CarlineDesc,attr,omitempty"`
	VinAttr             string                  `xml:"Vin,attr,omitempty"`
	VehicleMakePfxAttr  string                  `xml:"VehicleMakePfx,attr,omitempty"`
	RoStatusAttr        string                  `xml:"RoStatus,attr,omitempty"`
	VehiclePriorityAttr string                  `xml:"VehiclePriority,attr,omitempty"`
	DeptTypeAttr        string                  `xml:"DeptType,attr,omitempty"`
	ApptNoAttr          string                  `xml:"ApptNo,attr,omitempty"`
	TagNoAttr           string                  `xml:"TagNo,attr,omitempty"`
	DispNoAttr          string                  `xml:"DispNo,attr,omitempty"`
	AdvNameAttr         string                  `xml:"AdvName,attr,omitempty"`
	AdvNoAttr           string                  `xml:"AdvNo,attr,omitempty"`
	CustNameAttr        string                  `xml:"CustName,attr,omitempty"`
	CustNoAttr          string                  `xml:"CustNo,attr,omitempty"`
	RoNoAttr            string                  `xml:"RoNo,attr,omitempty"`
	RoCommentInfo       []*RoCommentInfoType    `xml:"RoCommentInfo"`
	TechRecommends      []*TechRecommendsType   `xml:"TechRecommends"`
	RecommendedServc    []*RecommendedServcType `xml:"RecommendedServc"`
}

// RogogType ...
type RogogType struct {
	AllGogTotalAmts []*AllGogTotalAmtsType `xml:"AllGogTotalAmts"`
}

// RolaborType ...
type RolaborType struct {
	RoAmts          []*RoAmtsType1         `xml:"RoAmts"`
	OpCodeLaborInfo []*OpCodeLaborInfoType `xml:"OpCodeLaborInfo"`
}

// RomiscType ...
type RomiscType struct {
	IntrLbrTotAttr   string         `xml:"IntrLbrTot,attr,omitempty"`
	WarrLbrTotAttr   string         `xml:"WarrLbrTot,attr,omitempty"`
	CustLbrTotAttr   string         `xml:"CustLbrTot,attr,omitempty"`
	IntrPartsTotAttr string         `xml:"IntrPartsTot,attr,omitempty"`
	WarrPartsTotAttr string         `xml:"WarrPartsTot,attr,omitempty"`
	CustPartsTotAttr string         `xml:"CustPartsTot,attr,omitempty"`
	RoAmts           []*RoAmtsType4 `xml:"RoAmts"`
}

// RopartType ...
type RopartType struct {
	PartInfoByJob []*PartInfoByJobType `xml:"PartInfoByJob"`
}

// RosubType ...
type RosubType struct {
	AllSubTotalAmts []*AllSubTotalAmtsType `xml:"AllSubTotalAmts"`
	SubInfoByJob    []*SubInfoByJobType    `xml:"SubInfoByJob"`
}

// RepairOrderType ...
type RepairOrderType struct {
	RoRecord    []*RoRecordType  `xml:"RoRecord"`
	ServVehicle *ServVehicleType `xml:"ServVehicle"`
	CustRecord  *CustRecordType  `xml:"CustRecord"`
}

// ServVehicleType ...
type ServVehicleType struct {
	Vehicle         *VehicleTypeResponse         `xml:"Vehicle"`
	VehicleServInfo *VehicleServInfoTypeResponse `xml:"VehicleServInfo"`
}

// SubDetailType ...
type SubDetailType struct {
	SubletPoNoAttr string `xml:"SubletPoNo,attr,omitempty"`
	SubletDescAttr string `xml:"SubletDesc,attr,omitempty"`
}

// SubInfoByJobType ...
type SubInfoByJobType struct {
	JobNoAttr  string           `xml:"JobNo,attr,omitempty"`
	OpCodeAttr string           `xml:"OpCode,attr,omitempty"`
	SubDetail  []*SubDetailType `xml:"SubDetail"`
}

// TechInfoType ...
type TechInfoType struct {
	IntrTechRateAttr string `xml:"IntrTechRate,attr,omitempty"`
	WarrTechRateAttr string `xml:"WarrTechRate,attr,omitempty"`
	CustTechRateAttr string `xml:"CustTechRate,attr,omitempty"`
	TechHrsAttr      string `xml:"TechHrs,attr,omitempty"`
	TechNameAttr     string `xml:"TechName,attr,omitempty"`
	TechNoAttr       string `xml:"TechNo,attr,omitempty"`
}

// TechRecommendsType ...
type TechRecommendsType struct {
	TechRecommendAttr string `xml:"TechRecommend,attr,omitempty"`
}

// VehicleTypeResponse ...
type VehicleTypeResponse struct {
	IntClrDescAttr  string             `xml:"IntClrDesc,attr,omitempty"`
	ExtClrDescAttr  string             `xml:"ExtClrDesc,attr,omitempty"`
	CarlineAttr     string             `xml:"Carline,attr,omitempty"`
	ModelDescAttr   string             `xml:"ModelDesc,attr,omitempty"`
	VehicleYrAttr   string             `xml:"VehicleYr,attr,omitempty"`
	VehicleMakeAttr string             `xml:"VehicleMake,attr,omitempty"`
	MakeNameAttr    string             `xml:"MakeName,attr,omitempty"`
	MakePfxAttr     string             `xml:"MakePfx,attr,omitempty"`
	VehicleDetail   *VehicleDetailType `xml:"VehicleDetail"`
}

// VehicleDetailType ...
type VehicleDetailType struct {
	LicNoAttr string `xml:"LicNo,attr,omitempty"`
}

// VehicleServInfoTypeResponse ...
type VehicleServInfoTypeResponse struct {
	NoOfVisitsAttr       string `xml:"NoOfVisits,attr,omitempty"`
	LastRODateAttr       string `xml:"LastRODate,attr,omitempty"`
	LastRONoAttr         string `xml:"LastRONo,attr,omitempty"`
	FirstROAttr          string `xml:"FirstRO,attr,omitempty"`
	LastActivityDateAttr string `xml:"LastActivityDate,attr,omitempty"`
	ServiceDaysAttr      string `xml:"ServiceDays,attr,omitempty"`
	DemoOdomAttr         string `xml:"DemoOdom,attr,omitempty"`
	DeliveryDateAttr     string `xml:"DeliveryDate,attr,omitempty"`
	InServiceDateAttr    string `xml:"InServiceDate,attr,omitempty"`
	ModelMaintCodeAttr   string `xml:"ModelMaintCode,attr,omitempty"`
	SalesmanNoAttr       string `xml:"SalesmanNo,attr,omitempty"`
	StockIDAttr          string `xml:"StockID,attr,omitempty"`
}

// RoAmtsType1 ...
type RoAmtsType1 struct {
	DlrCostAttr  string `xml:"DlrCost,attr,omitempty"`
	NTxblAmtAttr string `xml:"NTxblAmt,attr,omitempty"`
	TxblAmtAttr  string `xml:"TxblAmt,attr,omitempty"`
	PayTypeAttr  string `xml:"PayType,attr,omitempty"`
	AmtTypeAttr  string `xml:"AmtType,attr,omitempty"`
}

// RoAmtsType2 ...
type RoAmtsType2 struct {
	DlrCostAttr  string `xml:"DlrCost,attr,omitempty"`
	PayTypeAttr  string `xml:"PayType,attr,omitempty"`
	AmtTypeAttr  string `xml:"AmtType,attr,omitempty"`
	TotalAmtAttr string `xml:"TotalAmt,attr,omitempty"`
}

// RoAmtsType3 ...
type RoAmtsType3 struct {
	CustPriceAttr string `xml:"CustPrice,attr,omitempty"`
	DlrCostAttr   string `xml:"DlrCost,attr,omitempty"`
	NTxblAmtAttr  string `xml:"NTxblAmt,attr,omitempty"`
	TxblAmtAttr   string `xml:"TxblAmt,attr,omitempty"`
	PayTypeAttr   string `xml:"PayType,attr,omitempty"`
	AmtTypeAttr   string `xml:"AmtType,attr,omitempty"`
}

// RoAmtsType4 ...
type RoAmtsType4 struct {
	NTxblAmtAttr string `xml:"NTxblAmt,attr,omitempty"`
	TxblAmtAttr  string `xml:"TxblAmt,attr,omitempty"`
	PayTypeAttr  string `xml:"PayType,attr,omitempty"`
	AmtTypeAttr  string `xml:"AmtType,attr,omitempty"`
	TotalAmtAttr string `xml:"TotalAmt,attr,omitempty"`
}

// RoAmtsType5 ...
type RoAmtsType5 struct {
	DlrCostAttr   string `xml:"DlrCost,attr,omitempty"`
	NTxblAmtAttr  string `xml:"NTxblAmt,attr,omitempty"`
	TxblAmtAttr   string `xml:"TxblAmt,attr,omitempty"`
	PayTypeAttr   string `xml:"PayType,attr,omitempty"`
	AmtTypeAttr   string `xml:"AmtType,attr,omitempty"`
	CustPriceAttr string `xml:"CustPrice,attr,omitempty"`
}

// RoAmtsType6 ...
type RoAmtsType6 struct {
	DlrCostAttr  string `xml:"DlrCost,attr,omitempty"`
	NTxblAmtAttr string `xml:"NTxblAmt,attr,omitempty"`
	TxblAmtAttr  string `xml:"TxblAmt,attr,omitempty"`
	PayTypeAttr  string `xml:"PayType,attr,omitempty"`
	AmtTypeAttr  string `xml:"AmtType,attr,omitempty"`
}

// ContactInfoType1Response ...
type ContactInfoType1Response struct {
	SuffixAttr    string                  `xml:"Suffix,attr,omitempty"`
	SalutAttr     string                  `xml:"Salut,attr,omitempty"`
	MidNameAttr   string                  `xml:"MidName,attr,omitempty"`
	FirstNameAttr string                  `xml:"FirstName,attr,omitempty"`
	LastNameAttr  string                  `xml:"LastName,attr,omitempty"`
	NameRecIDAttr string                  `xml:"NameRecId,attr,omitempty"`
	IBFlagAttr    string                  `xml:"IBFlag,attr,omitempty"`
	Address       []*AddressType1Response `xml:"Address"`
	Phone         []*PhoneType            `xml:"Phone"`
	Email         *EmailType              `xml:"Email"`
}

// AddressType1Response ...
type AddressType1Response struct {
	ZipAttr   string `xml:"Zip,attr,omitempty"`
	StateAttr string `xml:"State,attr,omitempty"`
	CityAttr  string `xml:"City,attr,omitempty"`
	Addr2Attr string `xml:"Addr2,attr,omitempty"`
	Addr1Attr string `xml:"Addr1,attr,omitempty"`
	TypeAttr  string `xml:"Type,attr,omitempty"`
}
