package ucs

import (
	"encoding/xml"
)

// ReyAsburyRepairOrderReq ...
type ReyAsburyRepairOrderReq struct {
	XMLName         xml.Name                  `xml:"rey_AsburyRepairOrderReq"`
	Namespace       string                    `xml:"xmlns,attr"`
	ApplicationArea *ApplicationAreaType      `xml:"ApplicationArea"`
	RepairOrders    []*RepairOrderTypeRequest `xml:"RepairOrder"`
}

// RepairOrderTypeRequest ...
type RepairOrderTypeRequest struct {
	RoNoAttr string `xml:"RoNo,attr,omitempty"`
}
