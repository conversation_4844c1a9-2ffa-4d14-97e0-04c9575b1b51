package ucs

import (
	"encoding/xml"
	"phizz/conf"
)

const (
	xmlDeclaration        = `<?xml version="1.0" encoding="utf-8"?>`
	soapEncodingNamespace = "http://schemas.xmlsoap.org/soap/encoding/"
	soapEnvelopeNamespace = "http://schemas.xmlsoap.org/soap/envelope/"
	xsdNamespace          = "http://www.w3.org/2001/XMLSchema"
	xsiNamespace          = "http://www.w3.org/2001/XMLSchema-instance"
	wsaNamespace          = "http://schemas.xmlsoap.org/ws/2004/03/addressing"
	wsuNamespace          = "http://docs.oasis- open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd"
	mustUnderstand        = "1"
	wsseNamespace         = "http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd"
	amountTypeTotal       = "Total"
)

type soapEnvelope struct {
	XMLName               xml.Name `xml:"soapenv:Envelope"`
	SoapEncodingNamespace string   `xml:"xmlns:soapenc,attr"`
	SoapEnvelopeNamespace string   `xml:"xmlns:soapenv,attr"`
	XSDNamespace          string   `xml:"xmlns:xsd,attr"`
	XSINamespace          string   `xml:"xmlns:xsi,attr"`
}

type requestSoapHeader struct {
	XMLName  xml.Name `xml:"soapenv:Header"`
	Security *soapSecurity
}

type soapSecurity struct {
	XMLName        xml.Name `xml:"wsse:Security"`
	MustUnderstand string   `xml:"soapenv:mustUnderstand,attr"`
	XSDNamespace   string   `xml:"xmlns:xsd,attr"`
	WsseNamespace  string   `xml:"xmlns:wsse,attr"`
	UsernameToken  *securityUsernameToken
}

type securityUsernameToken struct {
	XMLName  xml.Name `xml:"wsse:UsernameToken"`
	Username string   `xml:"wsse:Username"`
	Password string   `xml:"wsse:Password"`
}

type roRequestSoapEnvelope struct {
	soapEnvelope
	Header *requestSoapHeader
	Body   *roRequestSoapBody
}

type roRequestSoapBody struct {
	XMLName        xml.Name `xml:"soapenv:Body"`
	ProcessMessage *ServiceROQueryRequestProcessMessage
}

// ---------------------- Response Structures -----------------------------
//
// Notes: The XML Decoder doesn't handle having element prefixes, so
// when there's an XML structure that's used in both Request and Response
// a separate response version is defined where there's no prefix in the
// XML element name unlike with the request version.
//
// Also, the various namespace attributes have been omitted from the struct
// definitions as they're not needed.

type responseHeader struct {
	XMLName         xml.Name `xml:"Header"`
	PayloadManifest *payloadManifest
}

type payloadManifest struct {
	XMLName      xml.Name `xml:"payloadManifest"`
	XMLNamespace string   `xml:"xmlns,attr"`
	Manifest     *manifest
}

type manifest struct {
	XMLName      xml.Name `xml:"manifest"`
	ContentID    string   `xml:"contentID,attr"`
	NamespaceURI string   `xml:"namespaceURI,attr"`
	Element      string   `xml:"element,attr"`
}

type roResponseSoapEnvelope struct {
	XMLName xml.Name `xml:"Envelope"`
	Header  *responseHeader
	Body    *roResponseBody
}

type roResponseBody struct {
	XMLName                xml.Name `xml:"Body"`
	ProcessMessageResponse *ROQueryProcessMessageResponse
}

type soapFaultResponseEnvelope struct {
	XMLName xml.Name `xml:"Envelope"`
	Header  *soapFaultHeader
	Body    *soapFaultBody
}

type soapFaultHeader struct {
	XMLName xml.Name `xml:"Header"`
}

type soapFaultBody struct {
	XMLName xml.Name `xml:"Body"`
	Fault   *soapFault
}

type soapFault struct {
	XMLName     xml.Name `xml:"Fault"`
	FaultCode   string   `xml:"faultcode"`
	FaultString string   `xml:"faultstring"`
	FaultActor  string   `xml:"faultactor"`
	Detail      string   `xml:"detail"`
}

func createROSoapRequest(bodyContent *ServiceROQueryRequestProcessMessage) *roRequestSoapEnvelope {
	envelope := &roRequestSoapEnvelope{
		soapEnvelope: soapEnvelope{
			SoapEncodingNamespace: soapEncodingNamespace,
			SoapEnvelopeNamespace: soapEnvelopeNamespace,
			XSDNamespace:          xsdNamespace,
			XSINamespace:          xsiNamespace,
		},
	}

	envelope.Header = createSoapHeader()

	body := &roRequestSoapBody{
		ProcessMessage: bodyContent,
	}
	envelope.Body = body

	return envelope
}

func createSoapHeader() *requestSoapHeader {
	conf := conf.Get().UCS

	usernameToken := &securityUsernameToken{
		Username: conf.Username,
		Password: conf.Password,
	}

	security := &soapSecurity{
		MustUnderstand: mustUnderstand,
		XSDNamespace:   xsdNamespace,
		WsseNamespace:  wsseNamespace,
		UsernameToken:  usernameToken,
	}

	header := &requestSoapHeader{
		Security: security,
	}

	return header
}
