#!/usr/bin/env bash

. ./deploy_config.sh

if [ $golang_path ]
then
  PATH=$golang_path:$PATH
fi

if [ $nodejs_path ]
then
  PATH=$nodejs_path:$PATH
fi

start_time=`date "+%Y%m%d%H%M%S"`
GOOS=linux
GOARCH=amd64
ssh_user=landcar
shared_path=/var/www/phizz/shared
current_path=/var/www/phizz/current
release_path=/var/www/phizz/releases/$start_time
releases_path=/var/www/phizz/releases
environment=$1
rollbar_token="9653d709b3224d758114ad8df2f6a488"
local_username=`whoami`
revision=`git log -n 1 --pretty=format:"%H"`
binary_name=phizz

if [ "$1" = "production" ]; then
	hosts=("*************" "*************")
	migrate_host="*************"
	NODE_ENV=production
elif [ "$1" = "stage" ]; then
	hosts=("*************" "*************")
	migrate_host="*************"
	NODE_ENV=production
elif [ "$1" = "uat" ]; then
	hosts=("**************")
	migrate_host="**************"
	NODE_ENV=production
elif [ "$1" = "testa" ]; then
	hosts=("*************")
	migrate_host="*************"
	NODE_ENV=production
elif [ "$1" = "testb" ]; then
	hosts=("**************")
	migrate_host="**************"
	NODE_ENV=production
else
	echo "Invalid environment.  Specify environment {uat,uat2,uat3,testa,testb,stage,production}."
	exit 1
fi

set -e # exit on any non-zero returning command
set -x # echo on

############################ build binaries
rm -rf deploy
mkdir -p deploy

GOOS=$GOOS GOARCH=$GOARCH go mod download

GOOS=$GOOS GOARCH=$GOARCH go build -o $binary_name
mv $binary_name deploy

GOOS=$GOOS GOARCH=$GOARCH go build -o dpmigrate cmd/dpmigrate/main.go
mv dpmigrate deploy

GOOS=$GOOS GOARCH=$GOARCH go build -o apreceive cmd/apreceive/main.go
mv apreceive deploy

GOOS=$GOOS GOARCH=$GOARCH go build -o claimreport cmd/claimreport/main.go
mv claimreport deploy

GOOS=$GOOS GOARCH=$GOARCH go build -o submitsbclaims cmd/submitsbclaims/main.go
mv submitsbclaims deploy

GOOS=$GOOS GOARCH=$GOARCH go build -o deactivateclaims cmd/deactivateclaims/main.go
mv deactivateclaims deploy

############################ build CSS & Javascript"
yarn install
# NODE_ENV=$NODE_ENV npx webpack build --env min
# For now, test `--env prod` when deploying to UAT
npx webpack build --env prod --env min

############################ copy items to deploy dir
cp -r public deploy/public
cp -r migrations deploy/migrations
cp -r templates deploy/templates
############################ copying files dir"
cp -r files deploy/files

############################ begin ssh control session
for host in "${hosts[@]}"
do
	ssh -o "ControlMaster=auto" -o "ControlPath=/tmp/%r@%h:%p" -o "ControlPersist=10m" $ssh_user@$host "exit"
done

############################ copy build
for host in "${hosts[@]}"
do
	scp -o "ControlPath=/tmp/%r@%h:%p" -r deploy $ssh_user@$host:$release_path

	ssh -o "ControlPath=/tmp/%r@%h:%p" $ssh_user@$host ln -s $shared_path/log $release_path/log
	ssh -o "ControlPath=/tmp/%r@%h:%p" $ssh_user@$host ln -s $shared_path/config.toml $release_path/config.toml
done

############################ finalize / reboot
for host in "${hosts[@]}"
do
	ssh -o "ControlPath=/tmp/%r@%h:%p" $ssh_user@$host rm -f $current_path
	ssh -o "ControlPath=/tmp/%r@%h:%p" $ssh_user@$host ln -f -s $release_path $current_path

	ssh -o "ControlPath=/tmp/%r@%h:%p" $ssh_user@$host sudo systemctl restart phizz
	sleep 4
done

############################ run migrations
ssh -o "ControlPath=/tmp/%r@%h:%p" $ssh_user@$migrate_host "cd $release_path && ./dpmigrate up"

############################ removing old releases
for host in "${hosts[@]}"
do
  ssh -o "ControlPath=/tmp/%r@%h:%p" $ssh_user@$host "cd $releases_path && ls -1tr | head -n -5 | xargs -d '\n' rm -rf --"
done

############################ end ssh control session
for host in "${hosts[@]}"
do
	ssh -O stop -o "ControlPath=/tmp/%r@%h:%p" $ssh_user@$host
done

############################ cleaning up
rm -rf deploy

############################ notify rollbar
curl "https://api.rollbar.com/api/1/deploy/" -F access_token=$rollbar_token -F environment=$environment -F revision=$revision -F local_username=$local_username
