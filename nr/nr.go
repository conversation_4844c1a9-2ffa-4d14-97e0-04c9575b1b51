package nr

import (
	"log"
	"net/http"
	"os"

	"phizz/conf"

	newrelic "github.com/newrelic/go-agent"
)

var (
	app newrelic.Application
)

// GetApp returns a `newrelic.Application` setup with config's token.
func GetApp() newrelic.Application {
	if app == nil {
		var err error
		appConf := conf.Get()
		nrConf := newrelic.NewConfig("phizz", appConf.NewRelic.Token)
		nrConf.Enabled = appConf.NewRelic.Enabled
		nrConf.Logger = newrelic.NewLogger(os.Stdout) // newrelic.NewDebugLogger(os.Stdout)
		nrConf.ErrorCollector.IgnoreStatusCodes = []int{
			http.StatusNotFound,
			http.StatusBadRequest,
		}
		app, err = newrelic.NewApplication(nrConf)
		if err != nil {
			log.Fatal(err)
		}
	}
	return app
}

// External monitors an external segment and will return the response
func External(txn newrelic.Transaction, client http.Client, req *http.Request) (*http.Response, error) {
	sgmt := newrelic.StartExternalSegment(txn, req)
	resp, err := client.Do(req)
	sgmt.Response = resp
	_ = sgmt.End()
	return resp, err
}
