package customer

import (
	"net/http"

	"phizz/handlers"

	"github.com/go-chi/chi"
	newrelic "github.com/newrelic/go-agent"
)

type customerPayload struct {
	ID                int    `json:"id" db:"id"`
	FirstName         string `json:"first_name" db:"first_name"`
	LastName          string `json:"last_name" db:"last_name"`
	EmailAddress      string `json:"email_address" db:"email_address"`
	PhoneNumber       string `json:"phone_number" db:"phone_number"`
	StreetAddress     string `json:"street_address" db:"street_address"`
	City              string `json:"city" db:"city"`
	State             string `json:"state" db:"state"`
	PostalCode        string `json:"postal_code" db:"postal_code"`
	AltPhoneNumber    string `json:"alternate_phone_number" db:"alternate_phone_number"`
	BestContactMethod string `json:"best_contact_method" db:"best_contact_method"`
}

// Show returns a customer information
func Show(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	contractCode := chi.URLParam(req, "code")
	txn := w.(newrelic.Transaction)
	contract, err := handlers.GetContractByID(txn, req, contractCode)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Server error in getting contract data", nil)
	}

	customer := customerPayload{}
	customer.ID = contract.CustomerDetails.ID
	customer.FirstName = contract.CustomerDetails.FirstName
	customer.LastName = contract.CustomerDetails.LastName
	customer.EmailAddress = contract.CustomerDetails.Email
	customer.PhoneNumber = contract.CustomerDetails.Phone
	customer.StreetAddress = contract.CustomerDetails.Address
	customer.City = contract.CustomerDetails.City
	customer.State = contract.CustomerDetails.StateCode
	customer.PostalCode = contract.CustomerDetails.PostalCode
	return http.StatusOK, map[string]interface{}{"customer": customer}

}
