set -e

workdir=.cover
profile="$workdir/cover.out"
mode=count

PKGS=$(go list ./... | grep -v /vendor/)

rm -rf "$workdir"
mkdir "$workdir"

for pkg in $PKGS; do
    f="$workdir/$(echo $pkg | tr / -).cover"
        go test -covermode="$mode" -coverprofile="$f" "$pkg"
done

echo "mode: $mode" >"$profile"
grep -h -v "^mode:" "$workdir"/*.cover >>"$profile"

go tool cover -func="$profile"

go tool cover -html="$profile" -o coverage.html