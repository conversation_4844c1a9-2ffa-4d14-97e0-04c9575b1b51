package s3util

import (
	"context"
	"errors"
	"fmt"
	"io"
	"strings"
	"time"

	"phizz/conf"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/aws/smithy-go"
	newrelic "github.com/newrelic/go-agent"
	errs "github.com/pkg/errors"
)

const (
	// DefaultRegion is the default region to use for AWS S3
	DefaultRegion = "us-west-2"
)

// Put puts an object into s3
func Put(txn newrelic.Transaction, r io.ReadSeeker, region, bucket, key string) error {
	ctx := context.TODO()
	key = normalizeKey(key)
	c, err := client(region)
	if err != nil {
		var apiErr smithy.APIError
		var code string
		if errors.As(err, &apiErr) {
			code = apiErr.ErrorCode()
		}
		return errs.WithMessage(err, formatErrorMessage("error getting s3 client", fmt.Sprintf("s3://%s/%s", bucket, key), code))
	}
	if _, err = r.Seek(0, 0); err != nil {
		return errs.Wrap(err, "failed to seek to beginning of src")
	}
	defer newrelic.StartSegment(txn, "S3 Put").End()
	_, err = c.PutObject(ctx, &s3.PutObjectInput{
		Body:   r,
		Bucket: &bucket,
		Key:    &key,
	})
	if err != nil {
		var apiErr smithy.APIError
		var code string
		if errors.As(err, &apiErr) {
			code = apiErr.ErrorCode()
		}
		return errs.Wrapf(err, formatErrorMessage(fmt.Sprintf("failed to put object for bucket: %s and key: %s", bucket, key), fmt.Sprintf("s3://%s/%s", bucket, key), code))
	}
	return nil
}

// PresignedURL creates an expiring pre-signed URL for the s3 object
func PresignedURL(region, bucket, key, filename, contentType string) (string, error) {
	ctx := context.TODO()
	key = normalizeKey(key)
	client, err := client(region)
	if err != nil {
		var apiErr smithy.APIError
		var code string
		if errors.As(err, &apiErr) {
			code = apiErr.ErrorCode()
		}
		return "", errs.WithMessage(err, formatErrorMessage("error getting s3 client", fmt.Sprintf("s3://%s/%s", bucket, key), code))
	}
	presignClient := s3.NewPresignClient(client, func(po *s3.PresignOptions) {
		po.Expires = 5 * time.Minute
	})
	presignResult, err := presignClient.PresignGetObject(ctx, &s3.GetObjectInput{
		Bucket:                     &bucket,
		Key:                        &key,
		ResponseContentDisposition: aws.String("inline;filename=" + filename),
		ResponseContentType:        &contentType,
	})
	if err != nil {
		var apiErr smithy.APIError
		var code string
		if errors.As(err, &apiErr) {
			code = apiErr.ErrorCode()
		}
		return "", errs.Wrapf(err, formatErrorMessage(fmt.Sprintf("failed to generate pre-signed URL for bucket: %s and key: %s", bucket, key), fmt.Sprintf("s3://%s/%s", bucket, key), code))
	}
	return presignResult.URL, nil
}

// Bucket returns the S3 Bucket for the app
func Bucket() string {
	return conf.Get().S3Bucket
}

func client(region string) (*s3.Client, error) {
	if region == "" {
		region = DefaultRegion
	}

	ctx := context.TODO()
	cfg, err := config.LoadDefaultConfig(ctx)
	if err != nil {
		return nil, errs.Wrap(err, "could not load SDK config")
	}

	client := s3.NewFromConfig(cfg, func(o *s3.Options) {
		o.Region = region
	})
	return client, nil
}

// GetFileURL returns the S3 bucket file url
func GetFileURL(fileName, s3Bucket string) (string, error) {
	names := strings.Split(fileName, "/")
	url, err := PresignedURL(DefaultRegion, s3Bucket, fileName, names[len(names)-1], "")
	return url, err
}

func formatErrorMessage(message string, url string, status string) string {
	return fmt.Sprintf("[AWS S3]Message: %s, URL: %s, "+
		"Response Status: %s", message, url, status)
}

func normalizeKey(key string) string {
	// If the key starts with '/' then remove it
	if key[:1] == "/" {
		return key[1:]
	}

	return key
}
