package s3util

import (
	"errors"
	"fmt"
	"net/http"
	"net/http/httputil"
	"net/url"
	"strings"
	"time"

	"phizz/conf"
	"phizz/db"
	"phizz/util"
	"phizz/util/urlsigner"

	errs "github.com/pkg/errors"
	"gopkg.in/unrolled/render.v1"
)

// ErrInvalidURL is an error that is returned when a URL is provided that does not contain some of the required information
var ErrInvalidURL = fmt.Errorf("invalid URL")

// GetS3ReverseProxy is a factory function for creating an S3ReverseProxy with default values
func GetS3ReverseProxy() *S3ReverseProxy {
	rProxyConfig := conf.Get().S3ReverseProxy
	return NewS3ReverseProxy([]byte(rProxyConfig.EncKey), conf.Get().AppURL, rProxyConfig.PathPrefix)
}

// S3ReverseProxy acts as a reverse proxy for AWS S3.  It uses secure URLs so that
// the links provided to users can only be used by the original user and so the
// links can't be modified to point to other resources
type S3ReverseProxy struct {
	signer     *urlsigner.URLSigner
	server     string
	pathPrefix string
	r          *render.Render
}

// NewS3ReverseProxy creates a new instance of S3ReverseProxy
func NewS3ReverseProxy(secret []byte, server, pathPrefix string) *S3ReverseProxy {
	return &S3ReverseProxy{
		signer:     urlsigner.New(secret),
		pathPrefix: pathPrefix,
		server:     server,
		r: render.New(render.Options{
			Layout:          "layout",
			RequirePartials: true,
		}),
	}
}

// GetSecureURL returns a signed URL for the S3ReverseProxy of the file at the given AWS path
// The user's ID is used as a part of the URL signature.
// Returns an error if the URL cannot be signed.
func (rp S3ReverseProxy) GetSecureURL(region, bucket, key, filename, contentType string, user db.User, lifespan time.Duration) (string, error) {
	if key == "" {
		return "", errs.New("key must not be empty")
	}
	if filename == "" {
		return "", errs.New("filename must not be empty")
	}
	if bucket == "" {
		return "", errs.New("bucket must not be empty")
	}
	if region == "" {
		return "", errs.New("region must not be empty")
	}
	if user.ID <= 0 {
		return "", errs.New("invalid user id")
	}
	if lifespan == time.Duration(0) {
		return "", errs.New("lifespan must be greater than 0")
	}

	downloadURL := rp.buildDownloadURL(key, filename, bucket, region, contentType)

	signed, err := rp.signer.Sign(downloadURL, user.ID, lifespan)
	if err != nil {
		return "", errs.WithMessagef(err, "could not get secure URL for '%s'", key)
	}

	return signed, nil
}

// Serve sets up the reverse proxy to AWS s3 for the requested S3 object and serves it.
func (rp S3ReverseProxy) Serve(w http.ResponseWriter, req *http.Request, user db.User) {
	ctx := req.Context()
	// Get the URL from the request
	requestURL := req.URL.String()

	// Verify that the URL is for the correct user, hasn't been altered,
	// and hasn't expired
	if err := rp.signer.Verify(requestURL, user.ID); err != nil {
		status, output := rp.errorToHTTPResponse(err)
		_ = rp.r.Text(w, status, output)
		return
	}

	// Get the targetFile of the S3 file that has been requested
	targetFile, err := rp.parseDownloadURL(requestURL)
	if err != nil {
		err = errs.Wrapf(err, "unable to download URL (%s)", requestURL)
		util.ReportError(req, err)
		_ = rp.r.Text(w, http.StatusInternalServerError, "Download failed")
		return
	}

	// Generate a pre-signed URL for the S3 file
	targetFileURL, err := PresignedURL(targetFile.region, targetFile.bucket, targetFile.key, targetFile.filename, targetFile.contentType)
	if err != nil {
		err = errs.WithMessagef(err, "unable to create pre-signed URL for (%s)", requestURL)
		util.ReportError(req, err)
		_ = rp.r.Text(w, http.StatusInternalServerError, "Download failed")
		return
	}

	// Convert the pre-signed URL string to *url.URL
	proxyTargetURL, err := url.Parse(targetFileURL)
	if err != nil {
		err = errs.Wrapf(err, "could not parse url (%s)", targetFileURL)
		util.ReportError(req, err)
		_ = rp.r.Text(w, http.StatusInternalServerError, "Download failed")
		return
	}

	// Reverse proxy implementation based on https://hackernoon.com/writing-a-reverse-proxy-in-just-one-line-with-go-c1edfa78c84b

	// Create a reverse proxy
	// We need to clear the Path, but keep the URL.RawQuery in order for the S3 validation
	// to succeed
	proxyTargetHost := *proxyTargetURL
	proxyTargetHost.Path = ""
	proxy := httputil.NewSingleHostReverseProxy(&proxyTargetHost)
	defer func() {
		// If the user cancels the file download before it is complete, then the proxy will
		// panic with http.ErrAbortHandler
		if r := recover(); r != nil {
			if err, ok := r.(error); ok {
				if err == http.ErrAbortHandler {
					// Handle the expected error from the panic
					_ = rp.r.Text(w, http.StatusBadRequest, "Request Canceled")
					err = errs.Wrapf(err, "proxy request possibly canceled for '%s'", requestURL)
					util.LogWarning(ctx, err)
					return
				}
				// Handle the unexpected error from the panic
				_ = rp.r.Text(w, http.StatusInternalServerError, "Request Failed")
				err = errs.Wrapf(err, "proxy request failed for '%s'", requestURL)
				util.ReportError(req, err)
				return
			}
			// if recover() returned something other than an error.  Do our best to log it
			_ = rp.r.Text(w, http.StatusInternalServerError, "Request Failed")
			err = errs.Errorf("%v", r)
			err = errs.WithMessagef(err, "proxy request failed for '%s'", requestURL)
			util.ReportError(req, err)
		}
	}()

	// Update the headers to allow for SSL redirection
	req.URL.Host = proxyTargetURL.Host
	req.URL.Scheme = proxyTargetURL.Scheme
	req.Header.Set("X-Forwarded-Host", req.Header.Get("Host"))
	req.Host = proxyTargetURL.Host

	// Update URL to allow pre-signed URL validation to succeed.
	req.URL.RawQuery = "" // Remove TCA Connect query parameters
	req.URL.Path = proxyTargetURL.Path

	// Note that ServeHttp is non blocking and uses a go routine under the hood
	proxy.ServeHTTP(w, req)
}

func (rp S3ReverseProxy) buildDownloadURL(key, filename, bucket, region, contentType string) string {
	key = rp.ensureAbsolutePath(key)
	secureURL := fmt.Sprintf("%s%s%s?filename=%s&bucket=%s&region=%s&content-type=%s",
		rp.server, rp.pathPrefix, key, filename, bucket, region, url.PathEscape(contentType))
	return secureURL
}

// ensureAbsolutePath ensures that the path has a leading `/`
func (rp S3ReverseProxy) ensureAbsolutePath(path string) string {
	if path[0] != '/' {
		path = "/" + path
	}
	return path
}

// errorToHTTPResponse converts an error to an HTTP status code and error or success message
func (rp S3ReverseProxy) errorToHTTPResponse(err error) (int, string) {
	if err == nil {
		return http.StatusOK, "Valid"
	}

	if errors.Is(err, urlsigner.ErrExpired) {
		return http.StatusUnauthorized, "Link has expired"
	} else if errors.Is(err, urlsigner.ErrInvalidUser) {
		return http.StatusUnauthorized, "User not authorized"
	} else if errors.Is(err, urlsigner.ErrLinkModified) {
		return http.StatusUnauthorized, "Link does not match signature"
	} else if errors.Is(err, urlsigner.ErrParseURL) {
		return http.StatusBadRequest, "Invalid link"
	}

	return http.StatusInternalServerError, "Unhandled error type"
}

type s3TargetFile struct {
	region      string
	bucket      string
	key         string
	filename    string
	contentType string
}

func (rp S3ReverseProxy) parseDownloadURL(secureURL string) (s3TargetFile, error) {
	var targetFile s3TargetFile

	target, err := url.Parse(secureURL)
	if err != nil {
		return s3TargetFile{}, errs.Wrapf(err, "could not parse URL (%s)", secureURL)
	}
	query := target.Query()
	targetFile.region = query.Get("region")
	targetFile.bucket = query.Get("bucket")

	targetFile.key = strings.Replace(target.Path, rp.pathPrefix, "", 1)
	// Remove the leading '/' from the key if it has one
	if len(targetFile.key) > 0 && targetFile.key[0] == '/' {
		targetFile.key = targetFile.key[1:]
	}

	targetFile.filename = query.Get("filename")
	targetFile.contentType = query.Get("content-type")

	// Ensure that all the required information was provided
	if targetFile.bucket == "" || targetFile.region == "" ||
		targetFile.filename == "" {
		return s3TargetFile{}, ErrInvalidURL
	}

	return targetFile, nil
}
