package s3util

import (
	"errors"
	"net/url"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestS3ReverseProxy_parseDownloadURL(t *testing.T) {
	tests := []struct {
		name        string
		secureURL   string
		expected    s3TargetFile
		expectedErr error
	}{
		// cSpell: disable
		{
			name: "Relative URL",
			secureURL: "/api/files/download/contract-forms-stamped/TCA-B9193992.pdf" +
				"?filename=TCA-B9193992.pdf" +
				"&bucket=tca-app-development&content-type=application%2Fpdf&expires=1677277839" +
				"&region=us-west-2&signature=Lx0ts-yMl_JvZ1kCxx0qS6GQ_iabtGzFLVJjsnr8b_g%3D" +
				"&user_id=6945",
			expected: s3TargetFile{
				region:      "us-west-2",
				bucket:      "tca-app-development",
				key:         "contract-forms-stamped/TCA-B9193992.pdf",
				filename:    "TCA-B9193992.pdf",
				contentType: "application/pdf",
			},
			expectedErr: nil,
		},
		{
			name: "Full URL",
			secureURL: "http://localhost:4000/api/files/download/contract-forms-stamped/TCA-B9193992.pdf" +
				"?filename=TCA-B9193992.pdf" +
				"&bucket=tca-app-development&content-type=application%2Fpdf&expires=1677277839" +
				"&region=us-west-2&signature=Lx0ts-yMl_JvZ1kCxx0qS6GQ_iabtGzFLVJjsnr8b_g%3D" +
				"&user_id=6945",
			expected: s3TargetFile{
				region:      "us-west-2",
				bucket:      "tca-app-development",
				filename:    "TCA-B9193992.pdf",
				key:         "contract-forms-stamped/TCA-B9193992.pdf",
				contentType: "application/pdf",
			},
			expectedErr: nil,
		},
		{
			name: "Alternate file name",
			secureURL: "http://localhost:4000/api/files/download/contract-forms-stamped/8ASDF83KSDF09" +
				"?filename=TCA-B9193992.pdf" +
				"&bucket=tca-app-development&content-type=application%2Fpdf&expires=1677277839" +
				"&region=us-west-2&signature=Lx0ts-yMl_JvZ1kCxx0qS6GQ_iabtGzFLVJjsnr8b_g%3D" +
				"&user_id=6945",
			expected: s3TargetFile{
				region:      "us-west-2",
				bucket:      "tca-app-development",
				filename:    "TCA-B9193992.pdf",
				key:         "contract-forms-stamped/8ASDF83KSDF09",
				contentType: "application/pdf",
			},
			expectedErr: nil,
		},
		{
			name: "Missing bucket",
			secureURL: "http://localhost:4000/api/files/download/contract-forms-stamped/TCA-B9193992.pdf" +
				"?filename=TCA-B9193992.pdf" +
				"&content-type=application%2Fpdf&expires=1677277839" +
				"&region=us-west-2&signature=Lx0ts-yMl_JvZ1kCxx0qS6GQ_iabtGzFLVJjsnr8b_g%3D",
			expected:    s3TargetFile{},
			expectedErr: ErrInvalidURL,
		},
		{
			name: "Missing region",
			secureURL: "http://localhost:4000/api/files/download/contract-forms-stamped/TCA-B9193992.pdf" +
				"?filename=TCA-B9193992.pdf" +
				"&bucket=tca-app-development&content-type=application%2Fpdf&expires=1677277839" +
				"&signature=Lx0ts-yMl_JvZ1kCxx0qS6GQ_iabtGzFLVJjsnr8b_g%3D" +
				"&user_id=6945",
			expected:    s3TargetFile{},
			expectedErr: ErrInvalidURL,
		},
		{
			name: "Missing filename",
			secureURL: "http://localhost:4000/api/files/download" +
				"?bucket=tca-app-development&content-type=application%2Fpdf&expires=1677277839" +
				"&region=us-west-2&signature=Lx0ts-yMl_JvZ1kCxx0qS6GQ_iabtGzFLVJjsnr8b_g%3D" +
				"&user_id=6945",
			expected:    s3TargetFile{},
			expectedErr: ErrInvalidURL,
		},
		// cSpell: enable
	}

	for _, tt := range tests {
		rProxy := NewS3ReverseProxy([]byte("just testing"), "http://localhost:4000", "/api/files/download")
		t.Run(tt.name, func(t *testing.T) {
			actual, actualErr := rProxy.parseDownloadURL(tt.secureURL)
			assert.Equal(t, tt.expected, actual, "unexpected file details")

			assert.True(t, errors.Is(actualErr, tt.expectedErr), "unexpected error")
		})
	}
}

func TestS3ReverseProxy_buildDownloadURL(t *testing.T) {
	secret := []byte("just testing")
	server := "http://localhost:4000"
	pathPrefix := "/api/files/download"

	rp := NewS3ReverseProxy(secret, server, pathPrefix)

	key := "contract-forms-stamped/"
	filename := "TCA-B9193992.pdf"
	bucket := "tca-app-development"
	region := "us-west-2"
	contentType := "application/pdf"

	wantURL := &url.URL{
		Scheme: "http",
		Host:   "localhost:4000",
		Path:   "/api/files/download/contract-forms-stamped/",
		RawQuery: url.Values{
			"filename":     []string{filename},
			"bucket":       []string{bucket},
			"region":       []string{region},
			"content-type": []string{contentType},
		}.Encode(),
	}

	got := rp.buildDownloadURL(key, filename, bucket, region, contentType)

	parsedGot, err := url.Parse(got)
	assert.NoError(t, err)
	assert.Equal(t, wantURL.Scheme, parsedGot.Scheme)
	assert.Equal(t, wantURL.Host, parsedGot.Host)
	assert.Equal(t, wantURL.Path, parsedGot.Path)
	wantQuery := wantURL.Query()
	gotQuery := parsedGot.Query()
	assert.Equal(t, wantQuery.Get("filename"), gotQuery.Get("filename"))
	assert.Equal(t, wantQuery.Get("bucket"), gotQuery.Get("bucket"))
	assert.Equal(t, wantQuery.Get("region"), gotQuery.Get("region"))
	assert.Equal(t, wantQuery.Get("content-type"), gotQuery.Get("content-type"))
}
