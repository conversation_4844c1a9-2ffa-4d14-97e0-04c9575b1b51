package gap

import (
	"net/http"
	"time"

	"phizz/db"
	"phizz/handlers"

	"database/sql"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
	"strconv"
)

// ClaimFinanceIndex returns a list of claims
func ClaimFinanceIndex(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {

	wh := " where is_child = false " // child claims not to be shown in worklog
	args := []interface{}{}

	nameContractVin := req.FormValue("q")
	if nameContractVin != "" {
		wh = "and (last_name ilike $1 or first_name ilike $1 or contract_number ilike $1 or vin ilike $1)"
		args = append(args, "%"+nameContractVin+"%")
	}

	userID := req.FormValue("user_id")
	// if userID is not provided in query, don't restrict by userID, show all claims
	var err error
	if userID != "" {
		_, err := strconv.Atoi(userID) // converting to number ensures, it's a valid number
		if err == nil {
			wh = wh + " and owner_id = " + userID
		}
	}

	status := req.FormValue("status")
	if status != "" {
		statusQuery := mapGapStatus(status)
		if statusQuery != "" {
			wh = wh + " and " + statusQuery
		}
	}

	orderByName := req.FormValue("order_by_name")

	if orderByName == "" {
		orderByName = "asc"
	}

	claims := []struct {
		ID                 int             `json:"id" db:"id"`
		InsuredName        string          `json:"insured_name" db:"insured_name"`
		ContractNumber     string          `json:"contract_number" db:"contract_number"`
		Status             string          `json:"status" db:"status"`
		Opened             time.Time       `json:"opened" db:"opened"`
		Amount             decimal.Decimal `json:"amount" db:"amount"`
		DateOfLoss         time.Time       `json:"date_of_loss" db:"date_of_loss"`
		Bank               string          `json:"bank" db:"bank"`
		BankVendorID       string          `json:"bank_vendor_id" db:"bank_vendor_id"`
		BankAddressStreet1 string          `json:"bank_address_street1" db:"bank_address_street1"`
		BankAddressStreet2 string          `json:"bank_address_street2" db:"bank_address_street2"`
		BankAddressCity    string          `json:"bank_address_city" db:"bank_address_city"`
		BankAddressState   string          `json:"bank_address_state" db:"bank_address_state"`
		BankAddressZip     string          `json:"bank_address_zip" db:"bank_address_zip"`
		LoanNumber         string          `json:"loan_number" db:"loan_number"`
	}{}

	fromClause := `gap_claims join banks on bank_id = banks.id join customers on customer_id = customers.id`

	listQuery, countQuery := handlers.ListQueries(
		`gap_claims.id, last_name || ',' || first_name as insured_name, contract_number, status, date_of_claim_received as opened,
		 case_reserve as amount, date_of_loss, bank_account_name as bank, bank_vendor_id, bank_address_street1,
		 bank_address_street2, bank_address_city, bank_address_state, bank_address_zip, bank_account_number as loan_number`,
		fromClause,
		wh,
		`order by insured_name `+orderByName,
		handlers.PerPageEntries,
		handlers.GetPage(req),
	)

	sumTotalEstimateQuery := "select sum(case_reserve) from " + fromClause + " " + wh

	err = db.Get().Select(&claims, listQuery, args...)
	if err != nil {
		errors.Wrap(err, "Database error getting gap claims")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting claims data", nil)
	}

	count := 0
	err = db.Get().Get(&count, countQuery, args...)
	if err != nil {
		errors.Wrap(err, "Database error getting gap claims count")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting claims data", nil)
	}

	var totalEstimate sql.NullFloat64
	err = db.Get().Get(&totalEstimate, sumTotalEstimateQuery, args...)
	if err != nil {
		err = errors.Wrap(err, "Database error getting GAP claims total estimate sum")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting GAP claims lists data - total estimate sum", nil)
	}

	return http.StatusOK, map[string]interface{}{"claims": claims, "count": count, "totalEstimate": totalEstimate.Float64}
}
