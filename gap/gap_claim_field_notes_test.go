package gap

import (
	"net/http"
	"net/http/httptest"
	"regexp"
	"testing"

	"phizz/db"
	"phizz/handlers"

	sqlmock "github.com/DATA-DOG/go-sqlmock"
	"github.com/go-chi/chi"
	"github.com/jmoiron/sqlx"
	"time"
)

func TestGapFieldNoteIndex(t *testing.T) {
	mdb, mock, err := sqlmock.New()
	if err != nil {
		t.Fatal(err)
	}
	dbx := sqlx.NewDb(mdb, "sqlmock")
	db.DB = dbx
	q := regexp.QuoteMeta

	mock.ExpectQuery(q(`select gap_claim_id, field_id, notes_text, created_by_user_id, gap_claim_field_notes.created_at, first_name, last_name from gap_claim_field_notes join users on created_by_user_id = users.id where gap_claim_id = $1 order by field_id, gap_claim_field_notes.created_at desc`)).
		WithArgs("1").
		WillReturnRows(sqlmock.NewRows([]string{"gap_claim_id", "field_id", "notes_text", "created_by_user_id", "created_at", "first_name", "last_name"}).
			AddRow(1, 1, "notes_text", 5103, time.Time{}, "name", "last_name"))

	mock.ExpectQuery(q(`select contract_number, contract_code, contract_name, contract_flag, contract_value, manager_flag from gap_claim_contracts where gap_claim_id = $1`)).
		WithArgs("1").
		WillReturnRows(sqlmock.NewRows([]string{"contract_number", "contract_code", "contract_name", "contract_flag", "contract_value", "manager_flag"}).
			AddRow("SC1234", "SC", "Service", true, 100.0, false))

	mock.ExpectQuery(q(`select gap_claim_id, contract_code, contract_number, notes_text, created_by_user_id, gap_claim_contract_field_notes.created_at, first_name, last_name from gap_claim_contract_field_notes join users on created_by_user_id = users.id where gap_claim_id = $1 and contract_number = $2 and contract_code = $3 order by gap_claim_contract_field_notes.created_at desc`)).
		WithArgs("1", "SC1234", "SC").
		WillReturnRows(sqlmock.NewRows([]string{"gap_claim_id", "contract_code", "contract_number", "notes_text", "created_by_user_id", "created_at", "first_name", "last_name"}).
			AddRow("1", "SC", "SC1234", "notes_text", 5103, time.Time{}, "first_name", "last_name"))

	req, err := http.NewRequest("GET", "/api/gapclaims/1/field-notes", nil)
	if err != nil {
		t.Fatal(err)
	}

	rr := httptest.NewRecorder()
	handler := http.HandlerFunc(handlers.APIHandler(FieldNoteIndex))

	r := chi.NewRouter()
	r.HandleFunc("/api/gapclaims/{id:[0-9]+}/field-notes", handler)
	r.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v",
			status, http.StatusOK)
	}

	expected := `{"contract_notes":[{"contract_number":"SC1234","contract_code":"SC","contract_name":"Service","contract_flag":true,"contract_value":"100","manager_flag":false,"contract_field_notes":[{"gap_claim_id":1,"contract_number":"SC1234","contract_code":"SC","notes_text":"notes_text","created_at":"0001-01-01T00:00:00Z","created_by_user_id":5103,"first_name":"first_name","last_name":"last_name"}]}],"field_notes":[{"field_id":1,"count":1,"notes":[{"field_id":1,"gap_claim_id":1,"notes_text":"notes_text","created_at":"0001-01-01T00:00:00Z","created_by_user_id":5103,"first_name":"name","last_name":"last_name"}]}]}`
	if rr.Body.String() != expected {
		t.Errorf("handler returned unexpected body: got %v want %v",
			rr.Body.String(), expected)
	}

	if err := mock.ExpectationsWereMet(); err != nil {
		t.Errorf("there were unfulfilled expectations: %s", err)
	}
}
