package gap

import (
	"encoding/json"
	"net/http"
	"strings"
	"time"

	"phizz/db"
	"phizz/handlers"

	"database/sql"
	"github.com/go-chi/chi"
	"github.com/jmoiron/sqlx"
	"github.com/pkg/errors"
)

type fieldNotePayload struct {
	FieldID         int       `json:"field_id" db:"field_id"`
	GapClaimID      int       `json:"gap_claim_id" db:"gap_claim_id"`
	NotesText       string    `json:"notes_text" db:"notes_text"`
	CreatedAt       time.Time `json:"created_at" db:"created_at"`
	CreatedByUserID int       `json:"created_by_user_id" db:"created_by_user_id"`
	FirstName       string    `json:"first_name" db:"first_name"`
	LastName        string    `json:"last_name" db:"last_name"`
}

// FieldNoteCreate creates an item note for GAP Claim's specific field e.g. Book out request
func FieldNoteCreate(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	fieldNote, err := fieldNoteFromReq(req)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(nil, "Malformed Field Note data for create.", nil)
	}
	fieldNote.CreatedByUserID = user.ID

	cleanFieldNote(fieldNote)
	formErrors, err := validateFieldNote(fieldNote)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(errors.Wrap(err, "Error validating Record Note"),
			"An error occurred validating the form values.", nil)
	}

	if len(formErrors) > 0 {
		return http.StatusBadRequest, handlers.ErrorMessage(nil, "Form validations errors.",
			map[string]interface{}{"errors": formErrors})
	}

	id, err := createFieldNote(fieldNote, nil)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(errors.Wrap(err, "Database error in adding note"),
			"Database error in adding note", nil)
	}

	return http.StatusOK, map[string]interface{}{"field_note_id": id}
}

func fieldNoteFromReq(req *http.Request) (*fieldNotePayload, error) {
	fieldNote := fieldNotePayload{}

	dec := json.NewDecoder(req.Body)
	err := dec.Decode(&fieldNote)

	return &fieldNote, errors.Wrap(err, "decoding Field Note request failed")
}

// cleanRecordNote cleans up leading and trailing white-space etc...
func cleanFieldNote(fieldNote *fieldNotePayload) {
	fieldNote.NotesText = strings.TrimSpace(fieldNote.NotesText)
}

// validateRecordNote validates a NewClaim record for correctness
func validateFieldNote(fieldNote *fieldNotePayload) (map[string]string, error) {
	formErrors := map[string]string{}

	if fieldNote.GapClaimID == 0 {
		formErrors["gap_claim_id"] = "GAP claim id is required"
	}
	if fieldNote.FieldID == 0 {
		formErrors["field_id"] = "Field id is required"
	}
	if fieldNote.NotesText == "" {
		formErrors["notes_text"] = "Notes text is required"
	}
	if fieldNote.CreatedByUserID == 0 {
		formErrors["user_id"] = "User ID is required"
	}
	return formErrors, nil
}

type contractFieldNotePayload struct {
	GapClaimID      int       `json:"gap_claim_id" db:"gap_claim_id"`
	ContractNumber  string    `json:"contract_number" db:"contract_number"`
	ContractCode    string    `json:"contract_code" db:"contract_code"`
	NotesText       string    `json:"notes_text" db:"notes_text"`
	CreatedAt       time.Time `json:"created_at" db:"created_at"`
	CreatedByUserID int       `json:"created_by_user_id" db:"created_by_user_id"`
	FirstName       string    `json:"first_name" db:"first_name"`
	LastName        string    `json:"last_name" db:"last_name"`
}

// ContractFieldNoteCreate creates an item note for GAP Claim's dynamic contracts field e.g. Service contract
func ContractFieldNoteCreate(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	contractFieldNote, err := contractFieldNoteFromReq(req)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Malformed Contract Field Note data for create.", nil)
	}

	cleanContractFieldNote(contractFieldNote)
	formErrors, err := validateContractFieldNote(contractFieldNote)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(errors.Wrap(err, "Error validating Record Note"),
			"An error occurred validating the form values.", nil)
	}

	if len(formErrors) > 0 {
		return http.StatusBadRequest, handlers.ErrorMessage(errors.New("Form validations errors"), "Form validations errors",
			map[string]interface{}{"errors": formErrors})
	}

	insertQuery := `insert into gap_claim_contract_field_notes (created_at,gap_claim_id, contract_number, contract_code, notes_text,created_by_user_id)
		values (now() at time zone 'utc',:gap_claim_id,:contract_number,:contract_code,:notes_text,:created_by_user_id) returning id`

	contractFieldNote.CreatedByUserID = user.ID

	stmt, err := db.Get().PrepareNamed(insertQuery)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(errors.Wrap(err, "Error inserting Field Note"),
			"An error occurred in PrepareNamed function while adding Field Note.", nil)
	}
	defer func() { _ = stmt.Close() }()

	id := 0
	err = stmt.Get(&id, contractFieldNote)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(errors.Wrap(err, "Error inserting Field Note"),
			"An error occurred while trying to add the Field Note to the database.", nil)
	}

	return http.StatusOK, map[string]interface{}{"contract_field_note_id": id}
}

func contractFieldNoteFromReq(req *http.Request) (*contractFieldNotePayload, error) {
	contractFieldNote := contractFieldNotePayload{}

	dec := json.NewDecoder(req.Body)
	err := dec.Decode(&contractFieldNote)

	return &contractFieldNote, errors.Wrap(err, "decoding Contract Field Note request failed")
}

// cleanContractFieldNote cleans up leading and trailing white-space etc...
func cleanContractFieldNote(contractFieldNote *contractFieldNotePayload) {
	contractFieldNote.ContractCode = strings.TrimSpace(contractFieldNote.ContractCode)
	contractFieldNote.ContractNumber = strings.TrimSpace(contractFieldNote.ContractNumber)
	contractFieldNote.NotesText = strings.TrimSpace(contractFieldNote.NotesText)
}

// validateContractFieldNote validates a contractFieldNote record for correctness
func validateContractFieldNote(contractFieldNote *contractFieldNotePayload) (map[string]string, error) {
	formErrors := map[string]string{}

	if contractFieldNote.GapClaimID == 0 {
		formErrors["gap_claim_id"] = "GAP claim id is required"
	}
	if contractFieldNote.ContractCode == "" {
		formErrors["contract_code"] = "Contract code is required"
	}
	if contractFieldNote.ContractNumber == "" {
		formErrors["contract_number"] = "Contract number is required"
	}
	if contractFieldNote.NotesText == "" {
		formErrors["notes_text"] = "Notes text is required"
	}
	return formErrors, nil
}

func getFieldNotesByFieldID(gapClaimID, fieldID int) ([]string, error) {
	query := "select notes_text from gap_claim_field_notes where gap_claim_id=$1 and field_id=$2"
	var notes []string
	err := db.Get().Select(&notes, query, gapClaimID, fieldID)
	if err != nil {
		if err == sql.ErrNoRows {
			return notes, errors.Wrap(err, "The fieldNotes was not found")
		}
		return notes, errors.Wrap(err, "Error loading fieldnotes from database.")
	}
	return notes, nil
}

// FieldNoteIndex field notes and contract field notes for given claim
func FieldNoteIndex(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	id := chi.URLParam(req, "id")
	// Get field notes
	getFieldNotesQuery := `select gap_claim_id, field_id, notes_text, created_by_user_id, gap_claim_field_notes.created_at, first_name, last_name
	 from gap_claim_field_notes join users on created_by_user_id = users.id
	 where gap_claim_id = $1 order by field_id, gap_claim_field_notes.created_at desc`

	fieldNotes := []fieldNotePayload{}

	err := db.Get().Select(&fieldNotes, getFieldNotesQuery, id)
	if err != nil && err != sql.ErrNoRows {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error loading GAP claim field notes from database.", nil)
	}

	fieldNotesWithCount := []fieldNote{}
	currentFieldID := 0
	numberOfFieldID := 0
	for _, currentResultObject := range fieldNotes {
		if currentResultObject.FieldID != currentFieldID {
			currentFieldID = currentResultObject.FieldID
			numberOfFieldID = numberOfFieldID + 1
			fieldNotesWithCount = append(fieldNotesWithCount, fieldNote{})
		}
		fieldNotesWithCount[numberOfFieldID-1].FieldID = currentFieldID
		fieldNotesWithCount[numberOfFieldID-1].Notes = append(fieldNotesWithCount[numberOfFieldID-1].Notes, currentResultObject)
		fieldNotesWithCount[numberOfFieldID-1].Count = fieldNotesWithCount[numberOfFieldID-1].Count + 1
	}

	contracts := []gapClaimContractPayload{}

	getContractsQuery := `select contract_number, contract_code, contract_name, contract_flag, contract_value, manager_flag
	from gap_claim_contracts where gap_claim_id = $1`

	err = db.Get().Select(&contracts, getContractsQuery, id)
	if err != nil && err != sql.ErrNoRows {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error loading GAP claim contracts from database.", nil)
	}

	getContractFieldNotesQuery := `select gap_claim_id, contract_code, contract_number, notes_text, created_by_user_id,
	 gap_claim_contract_field_notes.created_at, first_name, last_name
	 from gap_claim_contract_field_notes join users on created_by_user_id = users.id
	 where gap_claim_id = $1 and contract_number = $2 and contract_code = $3
	 order by gap_claim_contract_field_notes.created_at desc`

	for i, contract := range contracts {
		err = db.Get().Select(&contracts[i].ContractFieldNotes, getContractFieldNotesQuery, id, contract.ContractNumber, contract.ContractCode)
		if err != nil && err != sql.ErrNoRows {
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error loading GAP claim contract field notes from database.", nil)
		}
	}

	return http.StatusOK, map[string]interface{}{"field_notes": fieldNotesWithCount, "contract_notes": contracts}
}

func createFieldNote(fieldNote *fieldNotePayload, tx *sqlx.Tx) (int, error) {

	insertQuery := `insert into gap_claim_field_notes (created_at,gap_claim_id, field_id, notes_text,created_by_user_id)
		values (now() at time zone 'utc',:gap_claim_id,:field_id,:notes_text,:created_by_user_id) returning id`

	var stmt *sqlx.NamedStmt
	var err error
	if tx != nil {
		stmt, err = tx.PrepareNamed(insertQuery)
	} else {
		stmt, err = db.Get().PrepareNamed(insertQuery)
	}

	if err != nil {
		return 0, errors.Wrap(err, "Error inserting Field Note, error occurred in PrepareNamed function while adding Field Note.")
	}
	defer func() { _ = stmt.Close() }()

	id := 0
	err = stmt.Get(&id, fieldNote)
	if err != nil {
		return id, errors.Wrap(err, "Error inserting Field Note, error occurred while trying to add the Field Note to the database.")
	}

	return id, nil
}
