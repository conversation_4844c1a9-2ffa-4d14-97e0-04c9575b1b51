package gap

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"phizz/db"
	"phizz/handlers"

	"github.com/go-chi/chi"
	"github.com/jmoiron/sqlx"
	newrelic "github.com/newrelic/go-agent"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
)

// ClaimRecoveryIndex returns a list of claims
func ClaimRecoveryIndex(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {

	wh := ""
	recovery := req.FormValue("recovery") // Need to return all recovery case claims (R, IR, NR)
	if recovery == "true" {
		recoveryStatus := req.FormValue("recovery_status")
		if isValidRecoveryStatus(recoveryStatus) {
			wh = " where gap_claim_recoveries.status='" + recoveryStatus + "'"
		} else {
			wh = " where gap_claim_recoveries.status in ('IR','R','PR','WR')"
		}
	} else { // Need to return recovery candidates RI only
		wh = " where gap_claim_recoveries.status = 'RI' "
	}

	args := []interface{}{}

	contractIns := req.FormValue("q")
	if contractIns != "" {
		wh = wh + "and (contract_number ilike $1 or insurance_company ilike $1)"
		args = append(args, "%"+contractIns+"%")
	}

	orderBy := ""

	sortBy := req.FormValue("sort_by")
	if sortBy != "" {
		sortByColumn := mapRWSortByColumn(sortBy)
		if sortByColumn != "" {
			orderBy = sortByColumn
		}
	}

	sortOrder := req.FormValue("sort_order")
	if sortOrder == "asc" || sortOrder == "desc" {
		orderBy = orderBy + " " + sortOrder
	}

	if orderBy != "" {
		orderBy = " order by " + orderBy
	} else {
		// default order should be status and DOL desc
		orderBy = "order by start_date desc"
	}

	selectClause := `gap_claims.id, contract_number, gap_claims.status, gap_claim_recoveries.status as recovery_status, start_date as flagged, gap_closed_at as gap_closed,
	valuation_report_adjustments as ins_value, (nada - valuation_report_adjustments) as difference,
	case when c.is_business and (c.first_name!='' or c.last_name!='') then c.last_name || ',' || c.first_name || '/' || c.business_name
            when c.is_business and c.first_name='' and c.last_name='' then c.business_name
            else c.last_name || ',' || c.first_name end customer_name,
	allied_claim_number, updated_at as updated`

	if recovery == "true" {
		selectClause = selectClause + ",submitted_at as submitted"
	}

	fromClause := `gap_claims join gap_claim_recoveries on gap_claims.id = gap_claim_id
	join customers c on gap_claims.customer_id = c.id`

	countQuery := "select count(*) from " + fromClause + " " + wh

	totalDiffQuery := "select sum(nada - valuation_report_adjustments) from " + fromClause + " " + wh

	// handle pagination
	p := req.FormValue("page")
	var listQuery string
	if n, err := strconv.Atoi(p); err == nil && n > 0 {
		listQuery = fmt.Sprintf("select %s from %s %s %s limit %d offset %d", selectClause, fromClause, wh, orderBy, handlers.PerPageEntries, (n-1)*handlers.PerPageEntries)
	} else { // if page number is not provided, return all values
		listQuery = fmt.Sprintf("select %s from %s %s %s", selectClause, fromClause, wh, orderBy)
	}

	var claims []struct {
		ID                int             `json:"id" db:"id"`
		ContractNumber    string          `json:"contract_number" db:"contract_number"`
		Status            string          `json:"status" db:"status"`
		RecoveryStatus    string          `json:"recovery_status" db:"recovery_status"`
		Flagged           time.Time       `json:"flagged" db:"flagged"`
		GapClosed         time.Time       `json:"gap_closed" db:"gap_closed"`
		InsValue          decimal.Decimal `json:"ins_value" db:"ins_value"`
		Difference        decimal.Decimal `json:"difference" db:"difference"`
		CustomerName      string          `json:"customer_name" db:"customer_name"`
		AlliedClaimNumber string          `json:"allied_claim_number" db:"allied_claim_number"`
		Submitted         time.Time       `json:"submitted" db:"submitted"`
		Updated           time.Time       `json:"updated" db:"updated"`
	}

	err := db.Get().Select(&claims, listQuery, args...)
	if err != nil {
		err = errors.Wrap(err, "Database error getting GAP recovery lists")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting GAP claims lists data", nil)
	}

	count := 0
	err = db.Get().Get(&count, countQuery, args...)
	if err != nil {
		err = errors.Wrap(err, "Database error getting GAP recovery lists count")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting GAP claims lists data", nil)
	}

	var totalDiff sql.NullFloat64
	err = db.Get().Get(&totalDiff, totalDiffQuery, args...)
	if err != nil {
		err = errors.Wrap(err, "Database error getting GAP recovery total difference")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting GAP claims lists total difference", nil)
	}

	return http.StatusOK, map[string]interface{}{"count": count, "gap_claims": claims, "totalDifference": totalDiff.Float64}
}

func isValidRecoveryStatus(status string) bool {
	switch status {
	case db.GapClaimRecoveryStatusRecoveryInquiry:
		return true
	case db.GapClaimRecoveryStatusInRecovery:
		return true
	case db.GapClaimRecoveryStatusRecovered:
		return true
	case db.GapClaimRecoveryStatusNoRecovery:
		return true
	case db.GapClaimRecoveryStatusPossibleRecovery:
		return true
	case db.GapClaimRecoveryStatusWaitingRecovery:
		return true
	}
	return false
}

// map recovery worklog sort by column
func mapRWSortByColumn(input string) string {
	switch input {
	case "contract_number":
		return "contract_number"
	case "flagged":
		return "flagged"
	case "recovery_status":
		return "recovery_status"
	case "ins_value":
		return "ins_value"
	case "nada":
		return "nada"
	case "difference":
		return "difference"
	case "customer_name":
		return "customer_name"
	case "status":
		return "status"
	case "gap_closed":
		return "gap_closed"
	case "submitted":
		return "submitted"
	case "updated":
		return "updated"
	case "allied_claim_number":
		return "allied_claim_number"
	}
	return ""
}

// RecoveryUpdate updates recovery status, checkbox and recovery fund of the claim
func RecoveryUpdate(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	claimDB := struct {
		ID          int       `json:"id" db:"id"`
		Status      string    `json:"status" db:"status"`
		StartDate   time.Time `json:"start_date" db:"start_date"`
		HasRecovery bool      `json:"has_recovery" db:"has_recovery"`
	}{}

	err := db.Get().Get(&claimDB, `select id, has_recovery from gap_claims where id=$1`, chi.URLParam(req, "id"))
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage(err, "Claim not found", nil)
		}
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting GAP recovery from database", nil)
	}

	// validation
	claimReq := struct {
		ID             int       `json:"id" db:"id"`
		Status         string    `json:"status" db:"status"`
		HasRecovery    bool      `json:"has_recovery" db:"has_recovery"`
		RecoveryReason string    `json:"recovery_reason" db:"recovery_reason"`
		StartDate      time.Time `json:"start_date" db:"start_date"`
	}{}

	dec := json.NewDecoder(req.Body)
	err = dec.Decode(&claimReq)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Malformed GAP Claim data for recovery update", nil)
	}

	if !isValidRecoveryStatus(claimReq.Status) {
		return http.StatusBadRequest, handlers.ErrorMessage(errors.New("Invalid recovery status"), "Invalid recovery status", nil)
	}

	err = db.Get().Get(&claimDB, `select status, start_date from gap_claim_recoveries where gap_claim_id=$1`, chi.URLParam(req, "id"))
	if err != nil && err != sql.ErrNoRows { // No recovery is a valid case, since this is the same API used to add recovery
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting GAP recovery from database", nil)
	}

	// Once recovery status is moved ahead from RI to IR, has recovery should not be false
	if claimDB.Status != db.GapClaimRecoveryStatusRecoveryInquiry && claimReq.HasRecovery == false {
		return http.StatusBadRequest, handlers.ErrorMessage(
			errors.New("can't change recovery to no recovery if recovery status is not RI"),
			"Changing to no recovery not permitted", nil)
	}
	claimReq.ID = claimDB.ID

	tx, err := db.Get().Beginx()
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error creating transaction of GAP recovery", nil)
	}

	// Add recovery
	if claimReq.HasRecovery == true && claimDB.Status == "" && claimReq.Status == db.GapClaimRecoveryStatusRecoveryInquiry {
		err = insertRecovery(claimDB.ID, user.ID, tx)
		if err != nil {
			_ = tx.Rollback()
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error adding recovery", nil)
		}
	}

	// Update recovery
	updateSubmittedAt := ""
	if claimReq.Status == db.GapClaimRecoveryStatusInRecovery || claimReq.Status == db.GapClaimRecoveryStatusNoRecovery {
		updateSubmittedAt = ", submitted_at = now() at time zone 'utc' "
	}

	query := `update gap_claim_recoveries
		set status = :status,
			updated_at = now() at time zone 'utc'` + updateSubmittedAt + ` where gap_claim_id = :id`

	stmt, err := tx.PrepareNamed(query)
	if err != nil {
		_ = tx.Rollback()
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error updating GAP recovery, PrepareNamed failed", nil)
	}
	defer func() { _ = stmt.Close() }()

	_, err = stmt.Exec(claimReq)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error updating GAP recovery, database error", nil)
	}

	// Update has recovery
	tx.Exec(`update gap_claims set has_recovery = $1 where id = $2`, claimReq.HasRecovery, claimDB.ID)
	// Add entry in update table for audit trail
	updateInsert := `insert into gap_claim_updates(gap_claim_id, updated_by_user_id, updated_at) values ($1, $2, now() at time zone 'utc')`
	_, err = tx.Exec(updateInsert, claimReq.ID, user.ID)
	if err != nil {
		_ = tx.Rollback()
		return http.StatusInternalServerError, handlers.ErrorMessage(errors.Wrap(err, "Error inserting gap_claim_updates"),
			"Error inserting gap_claim_updates", nil)
	}

	// Add note if claim status is changed
	if claimDB.Status != claimReq.Status {
		recordNote := recordNotePayload{}
		recordNote.ID = claimDB.ID
		recordNote.CreatedByUserID = user.ID
		recordNote.CreatedAt = time.Now()
		recordNote.NotesText = db.RecoveryStatusChange[claimReq.Status]
		_, err := insertRecordNote(&recordNote, tx)
		if err != nil {
			_ = tx.Rollback()
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error adding notes for gap recovery", nil)
		}
	}

	// Add recovery reason as a field not in Recovery Review
	fieldNote := fieldNotePayload{}
	fieldNote.GapClaimID = claimReq.ID
	fieldNote.FieldID = db.FieldIDRecoveryReview
	fieldNote.CreatedByUserID = user.ID

	if claimReq.HasRecovery == true {
		fieldNote.NotesText = db.GapClaimRecoveryNoteAdded
	} else {
		fieldNote.NotesText = db.GapClaimRecoveryNoteRemoved
	}
	fieldNote.NotesText = fieldNote.NotesText + ": " + claimReq.RecoveryReason

	_, err = createFieldNote(&fieldNote, tx)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error adding reason of recovery", nil)
	}
	err = tx.Commit()
	if err != nil {
		err = errors.Wrap(err, "Database error committing transaction for GAP recovery update")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error updating GAP recovery", nil)
	}
	return http.StatusOK, map[string]interface{}{"gap_claim_id": claimDB.ID}
}

// RecoveryShow returns recovery status, checkbox and recovery fund of the claim
func RecoveryShow(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {

	claimDB := struct {
		ID              int             `json:"id" db:"id"`
		Status          string          `json:"status" db:"status"`
		HasRecovery     bool            `json:"has_recovery" db:"has_recovery"`
		RecoveryAdded   time.Time       `json:"recovery_added" db:"recovery_added"`
		RecoveryRemoved time.Time       `json:"recovery_removed" db:"recovery_removed"`
		CheckAmount     decimal.Decimal `json:"check_amount" db:"check_amount"`
	}{}

	err := db.Get().Get(&claimDB, `select id, has_recovery from gap_claims where id=$1`, chi.URLParam(req, "id"))
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage(err, "Recovery not found", nil)
		}
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting GAP claim from database", nil)
	}

	err = db.Get().Get(&claimDB, `select status, check_amount from gap_claim_recoveries where gap_claim_id=$1`, chi.URLParam(req, "id"))
	if err != nil && err != sql.ErrNoRows { // even if there is no entry in claim recovery, this is not error, and need to fetch notes for No recovery
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting GAP claim from database", nil)
	}

	// fetch recovery added/ removed timestamp

	note := struct {
		NotesText string    `json:"notes_text" db:"notes_text"`
		CreatedAt time.Time `json:"created_at" db:"created_at"`
	}{}

	err = db.Get().Get(&note, `select created_at, notes_text from gap_claim_field_notes where gap_claim_id = $1
	and field_id = $2 order by created_at desc limit 1`, chi.URLParam(req, "id"), db.FieldIDRecoveryReview)
	if err != nil && err != sql.ErrNoRows {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting GAP claim from database", nil)
	}

	if strings.HasPrefix(note.NotesText, db.GapClaimRecoveryNoteAdded) {
		claimDB.RecoveryAdded = note.CreatedAt
	} else {
		claimDB.RecoveryRemoved = note.CreatedAt
	}

	return http.StatusOK, map[string]interface{}{"gap_claim": claimDB}
}

func insertRecovery(claimID, userID int, tx *sqlx.Tx) error {
	query := `insert into gap_claim_recoveries(gap_claim_id,status,start_date,updated_at,updated_by_user_id)
			values($1,$2,now() at time zone 'utc', now() at time zone 'utc',$3) returning id`
	recoveryID := 0
	err := tx.Get(&recoveryID, query, claimID, db.GapClaimRecoveryStatusRecoveryInquiry, userID)
	if err != nil {
		return errors.Wrap(err, "Error in inserting recovery")
	}
	return nil
}

// RecoveryDetailsShow returns recovery status, checkbox and recovery fund of the claim
func RecoveryDetailsShow(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	recovery := struct {
		ID                int             `json:"id" db:"id"`
		ContractNumber    string          `json:"contract_number" db:"contract_number"`
		Difference        decimal.Decimal `json:"difference" db:"difference"`
		LastUpdatedBy     string          `json:"last_updated_by" db:"last_updated_by"`
		LastUpdatedAt     time.Time       `json:"last_updated_at" db:"last_updated_at"`
		AlliedClaimNumber string          `json:"allied_claim_number" db:"allied_claim_number"`
		InsuranceCompany  string          `json:"insurance_company" db:"insurance_company"`
		LastName          string          `json:"last_name" db:"last_name"`
		FirstName         string          `json:"first_name" db:"first_name"`
		MiddleInitial     string          `json:"middle_initial" db:"middle_initial"`
		Address           string          `json:"address" db:"address"`
		City              string          `json:"city" db:"city"`
		State             string          `json:"state" db:"state"`
		Zip               string          `json:"zip" db:"zip"`
		HomePhone         sql.NullString  `json:"home_phone" db:"home_phone"`
		AltPhoneNumber    sql.NullString  `json:"alternate_phone_number" db:"alternate_phone_number"`
		EmailAddress      string          `json:"email_address" db:"email_address"`
		LoanNumber        string          `json:"loan_number" db:"loan_number"`
		ContractBalance   decimal.Decimal `json:"contract_balance" db:"contract_balance"`
		PolicyNumber      string          `json:"policy_number" db:"policy_number"`
		ClaimType         string          `json:"claim_type" db:"claim_type"`
		DateOfLoss        time.Time       `json:"date_of_loss" db:"date_of_loss"`
		Year              sql.NullString  `json:"year" db:"year"`
		Make              sql.NullString  `json:"make" db:"make"`
		Model             sql.NullString  `json:"model" db:"model"`
		VIN               sql.NullString  `json:"vin" db:"vin"`
		Status            string          `json:"status" db:"status"`
		CheckAmount       decimal.Decimal `json:"check_amount" db:"check_amount"`
	}{}

	query := `select gap_claims.id, contract_number, (nada - valuation_report_adjustments) as difference, users.first_name || ' ' || users.last_name as last_updated_by,
	gap_claim_recoveries.updated_at as last_updated_at, allied_claim_number, insurance_company, customers.last_name,
	customers.first_name, street_address as address, city, state, postal_code as zip, phone_number as home_phone,
	alternate_phone_number as alternate_phone_number, email_address, bank_account_number as loan_number, contract_balance, policy_number, claim_type, date_of_loss,
	vin_records.vin, make, model, year, gap_claim_recoveries.status, check_amount
	from gap_claims join gap_claim_recoveries on gap_claims.id = gap_claim_recoveries.gap_claim_id
	join customers on gap_claims.customer_id = customers.id
	join users on gap_claim_recoveries.updated_by_user_id = users.id
	left outer join vin_records on gap_claims.vin_record_id = vin_records.id
	where gap_claim_id = $1`

	err := db.Get().Get(&recovery, query, chi.URLParam(req, "id"))
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage(err, "Recovery Checklist data not found", nil)
		}
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting Recovery checklist data from database", nil)
	}

	strArr := strings.Split(recovery.FirstName, " ")
	if len(strArr) > 1 {
		recovery.MiddleInitial = strArr[1]
		recovery.FirstName = strArr[0]
	}

	if recovery.VIN.String == "" { // for this VIN the data is still not migrated to vin_records, get from unidata
		txn := w.(newrelic.Transaction)
		contract, err := handlers.GetContractByID(txn, req, recovery.ContractNumber)
		if err != nil {
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Unidata server error in getting contract data", nil)
		}
		recovery.VIN.String = contract.VehicleDetails.VIN
		recovery.VIN.Valid = true

		recovery.Make.String = contract.VehicleDetails.Make
		recovery.Make.Valid = true

		recovery.Model.String = contract.VehicleDetails.Model
		recovery.Model.Valid = true

		recovery.Year.String = contract.VehicleDetails.Year
		recovery.Year.Valid = true
	}
	return http.StatusOK, map[string]interface{}{"claim_recovery": recovery}
}

// RecoveryDetailsUpdate updates a gap Claim
func RecoveryDetailsUpdate(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	id := 0
	err := db.Get().Get(&id, `select gap_claim_id from gap_claim_recoveries where gap_claim_id=$1`, chi.URLParam(req, "id"))
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage(err, "Recovery not found", nil)
		}
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error in getting Recovery", nil)
	}

	recovery := struct {
		GapClaimID        int             `json:"-" db:"gap_claim_id"`
		LastUpdatedBy     string          `json:"last_updated_by" db:"last_updated_by"`
		UpdatedByUserID   int             `json:"updated_by_user_id" db:"updated_by_user_id"`
		AlliedClaimNumber string          `json:"allied_claim_number" db:"allied_claim_number"`
		InsuranceCompany  string          `json:"insurance_company" db:"insurance_company"`
		PolicyNumber      string          `json:"policy_number" db:"policy_number"`
		Address           string          `json:"address" db:"address"`
		ContractBalance   decimal.Decimal `json:"contract_balance" db:"contract_balance"`
		ClaimType         string          `json:"claim_type" db:"claim_type"`
		Status            string          `json:"status" db:"status"`
		CheckAmount       decimal.Decimal `json:"check_amount" db:"check_amount"`
	}{}

	dec := json.NewDecoder(req.Body)
	err = dec.Decode(&recovery)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "decoding GAP Recovery request failed", nil)
	}

	if !isValidRecoveryStatus(recovery.Status) {
		return http.StatusBadRequest, handlers.ErrorMessage(errors.New("Invalid recovery status"), "Invalid recovery status", nil)
	}

	recovery.GapClaimID = id
	recovery.UpdatedByUserID = user.ID

	query := `update gap_claim_recoveries
		set allied_claim_number = :allied_claim_number,
			contract_balance = :contract_balance,
			claim_type =:claim_type,
			status = :status,
			check_amount = :check_amount,
			updated_by_user_id = :updated_by_user_id,
			updated_at = now() at time zone 'utc'
			where gap_claim_id = :gap_claim_id`

	tx, err := db.Get().Beginx()
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error creating transaction of GAP recovery", nil)
	}
	stmt, err := tx.PrepareNamed(query)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error updating GAP Recovery, PrepareNamed failed", nil)
	}
	defer func() { _ = stmt.Close() }()

	_, err = stmt.Exec(recovery)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error updating GAP claim, database error", nil)
	}
	updateClaimQuery := `update gap_claims set insurance_company = $1, policy_number = $2 where id = $3`
	_, err = tx.Exec(updateClaimQuery, recovery.InsuranceCompany, recovery.PolicyNumber, id)
	if err != nil {
		_ = tx.Rollback()
		return http.StatusInternalServerError, handlers.ErrorMessage(errors.Wrap(err, "Error updating gap_claims"),
			"Error upating gap_claims", nil)
	}
	// Add entry in update table for audit trail
	updateInsert := `insert into gap_claim_updates(gap_claim_id, updated_by_user_id, updated_at) values ($1, $2, now() at time zone 'utc')`
	_, err = tx.Exec(updateInsert, recovery.GapClaimID, recovery.UpdatedByUserID)
	if err != nil {
		_ = tx.Rollback()
		return http.StatusInternalServerError, handlers.ErrorMessage(errors.Wrap(err, "Error inserting gap_claim_updates"),
			"Error inserting gap_claim_updates", nil)
	}

	err = tx.Commit()
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error committing recovery update transaction", nil)
	}

	return http.StatusOK, map[string]interface{}{"recovery_id": id}
}

// RecoveryCommentIndex returns a list of record notes for given recovery id
func RecoveryCommentIndex(_ http.ResponseWriter, req *http.Request, _ db.User) (int, map[string]interface{}) {
	contractNumber := chi.URLParam(req, "id")
	caseComments := []struct {
		GapClaimID      int       `json:"gap_claim_id" db:"gap_claim_id"`
		NotesText       string    `json:"notes_text" db:"notes_text"`
		CreatedAt       time.Time `json:"created_at" db:"created_at"`
		CreatedByUserID int       `json:"created_by_user_id" db:"created_by_user_id"`
		FirstName       string    `json:"first_name" db:"first_name"`
		LastName        string    `json:"last_name" db:"last_name"`
	}{}

	args := []interface{}{}
	args = append(args, contractNumber)

	listQuery, countQuery := handlers.ListQueries(
		`gap_claim_id, notes_text, gap_recovery_comments.created_at, created_by_user_id, first_name, last_name`,
		`gap_recovery_comments join gap_claim_recoveries on gap_recovery_comments.gap_recovery_id = gap_claim_recoveries.id
			        join users on gap_recovery_comments.created_by_user_id = users.id `,
		`where gap_claim_id = $1`,
		`order by created_at desc`,
		handlers.PerPageEntries,
		handlers.GetPage(req),
	)

	err := db.Get().Select(&caseComments, listQuery, args...)
	if err != nil {
		err = errors.Wrap(err, "Database error getting case comments lists")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting case comments lists data", nil)
	}

	count := 0
	err = db.Get().Get(&count, countQuery, args...)
	if err != nil {
		err = errors.Wrap(err, "Database error getting case comments lists count")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting case comments lists data", nil)
	}
	// returning record_notes so that the same react component can be used as used in gap record_notes
	return http.StatusOK, map[string]interface{}{"record_notes": caseComments, "count": count}
}

// RecoveryCommentCreate creates a new Gap Claim
func RecoveryCommentCreate(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	caseComment := struct {
		ID        int    `json:"id" db:"gap_claim_id"`
		NotesText string `json:"notes_text" db:"notes_text"`
	}{}

	dec := json.NewDecoder(req.Body)
	err := dec.Decode(&caseComment)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(nil, "Malformed Case comment data for create.", nil)
	}

	caseComment.NotesText = strings.TrimSpace(caseComment.NotesText)

	if caseComment.ID == 0 {
		return http.StatusBadRequest, handlers.ErrorMessage(nil, "Form validations errors.",
			map[string]interface{}{"errors": map[string]string{
				"gap_claim_id": "GAP claim id is required",
			}})
	}

	recoveryID := 0
	err = db.Get().Get(&recoveryID, `select id from gap_claim_recoveries where gap_claim_id=$1`, caseComment.ID)
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage(err, "Recovery not found", nil)
		}
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error in getting Recovery", nil)
	}

	insertQuery := `insert into gap_recovery_comments (created_at,gap_recovery_id,notes_text,created_by_user_id)
	values (now() at time zone 'utc',$1,$2,$3) returning id`

	id := 0
	err = db.Get().Get(&id, insertQuery, recoveryID, caseComment.NotesText, user.ID)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "An error occurred while trying to add the recovery comments to the database.", nil)
	}
	return http.StatusOK, map[string]interface{}{"case_comment_id": id}
}

type recoveryAddedRemoved struct {
	ID              int             `json:"id" db:"id"`
	Status          string          `json:"status" db:"status"`
	HasRecovery     bool            `json:"has_recovery" db:"has_recovery"`
	RecoveryAdded   time.Time       `json:"recovery_added" db:"recovery_added"`
	RecoveryRemoved time.Time       `json:"recovery_removed" db:"recovery_removed"`
	CheckAmount     decimal.Decimal `json:"check_amount" db:"check_amount"`
}

func recoveryAddedRemovedInfo(claimID int) (recoveryAddedRemoved, error) {

	var recoAR recoveryAddedRemoved
	err := db.Get().Get(&recoAR, `select id, has_recovery from gap_claims where id=$1`, claimID)
	if err != nil {
		if err == sql.ErrNoRows {
			return recoAR, errors.Wrap(err, "Recovery not found")
		}
		return recoAR, errors.Wrap(err, "Error getting GAP claim from database")
	}

	err = db.Get().Get(&recoAR, `select status, check_amount from gap_claim_recoveries where gap_claim_id=$1`, claimID)
	if err != nil && err != sql.ErrNoRows { // even if there is no entry in claim recovery, this is not error, and need to fetch notes for No recovery
		return recoAR, errors.Wrap(err, "Error getting GAP claim from database")
	}

	// fetch recovery added/ removed timestamp

	note := struct {
		NotesText string    `json:"notes_text" db:"notes_text"`
		CreatedAt time.Time `json:"created_at" db:"created_at"`
	}{}

	err = db.Get().Get(&note, `select created_at, notes_text from gap_claim_field_notes where gap_claim_id = $1
and field_id = $2 order by created_at desc limit 1`, claimID, db.FieldIDRecoveryReview)
	if err != nil && err != sql.ErrNoRows {
		return recoAR, errors.Wrap(err, "Error getting GAP claim from database")
	}

	if strings.HasPrefix(note.NotesText, db.GapClaimRecoveryNoteAdded) {
		recoAR.RecoveryAdded = note.CreatedAt
	} else {
		recoAR.RecoveryRemoved = note.CreatedAt
	}
	return recoAR, nil
}
