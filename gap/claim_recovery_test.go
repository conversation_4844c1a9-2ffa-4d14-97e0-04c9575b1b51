package gap

import (
	"net/http"
	"net/http/httptest"
	"regexp"
	"testing"
	"time"

	"phizz/db"
	"phizz/handlers"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/go-chi/chi"
	"github.com/jmoiron/sqlx"
)

func DISABLEDTestClaimRecoveryIndex(t *testing.T) {
	mdb, mock, err := sqlmock.New()
	if err != nil {
		t.Fatal(err)
	}
	dbx := sqlx.NewDb(mdb, "sqlmock")
	db.DB = dbx
	q := regexp.QuoteMeta

	mock.ExpectQuery(q(`select gap_claims.id, contract_number, gap_claims.status, gap_claim_recoveries.status as recovery_status, start_date as flagged, gap_closed_at as gap_closed,
	valuation_report_adjustments as ins_value, (nada - valuation_report_adjustments) as difference,
	customers.last_name ||', '|| customers.first_name as customer_name,
	allied_claim_number, updated_at as updated from gap_claims join gap_claim_recoveries on gap_claims.id = gap_claim_id
	join customers on gap_claims.customer_id = customers.id  where gap_claim_recoveries.status = 'RI' order by start_date desc`)).
		WillReturnRows(sqlmock.NewRows([]string{"id", "contract_number", "status", "recovery_status", "flagged", "gap_closed", "ins_value", "difference", "customer_name", "allied_claim_number", "updated"}).
			AddRow(1, "*********", "C", "R", time.Time{}, time.Time{}, "1112.35", "333", "Test", "*********", time.Time{}))

	mock.ExpectQuery(q(`select count(*) from gap_claims join gap_claim_recoveries on gap_claims.id = gap_claim_id
	join customers on gap_claims.customer_id = customers.id  where gap_claim_recoveries.status = 'RI'`)).
		WillReturnRows(sqlmock.NewRows([]string{"count"}).
			AddRow(1))

	mock.ExpectQuery(q(`select sum(nada - valuation_report_adjustments) from gap_claims join gap_claim_recoveries on gap_claims.id = gap_claim_id
	join customers on gap_claims.customer_id = customers.id  where gap_claim_recoveries.status = 'RI'`)).
		WillReturnRows(sqlmock.NewRows([]string{"totalEstimate"}).
			AddRow(1112.99))

	req, err := http.NewRequest("GET", "/api/gapclaims/recovery", nil)
	if err != nil {
		t.Fatal(err)
	}

	rr := httptest.NewRecorder()
	handler := http.HandlerFunc(handlers.APIHandler(ClaimRecoveryIndex))

	r := chi.NewRouter()
	r.HandleFunc("/api/gapclaims/recovery", handler)
	r.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v",
			status, http.StatusOK)
	}

	expected := `{"count":1,"gap_claims":[{"id":1,"contract_number":"*********","status":"C","recovery_status":"R","flagged":"0001-01-01T00:00:00Z","gap_closed":"0001-01-01T00:00:00Z","ins_value":"1112.35","difference":"333","customer_name":"Test","allied_claim_number":"*********","submitted":"0001-01-01T00:00:00Z","updated":"0001-01-01T00:00:00Z"}],"totalDifference":1112.99}`
	if rr.Body.String() != expected {
		t.Errorf("handler returned unexpected body: got %v want %v",
			rr.Body.String(), expected)
	}

	if err := mock.ExpectationsWereMet(); err != nil {
		t.Errorf("there were unfulfilled expections: %s", err)
	}
}

func DISABLEDTestClaimRecoveryIndexOrderBy(t *testing.T) {
	mdb, mock, err := sqlmock.New()
	if err != nil {
		t.Fatal(err)
	}
	dbx := sqlx.NewDb(mdb, "sqlmock")
	db.DB = dbx
	q := regexp.QuoteMeta

	mock.ExpectQuery(q(`select gap_claims.id, contract_number, gap_claims.status, gap_claim_recoveries.status as recovery_status, start_date as flagged, gap_closed_at as gap_closed,
	valuation_report_adjustments as ins_value, (nada - valuation_report_adjustments) as difference,
	customers.last_name ||', '|| customers.first_name as customer_name,
	allied_claim_number, updated_at as updated from gap_claims join gap_claim_recoveries on gap_claims.id = gap_claim_id
	join customers on gap_claims.customer_id = customers.id  where gap_claim_recoveries.status = 'RI' order by difference desc`)).
		WillReturnRows(sqlmock.NewRows([]string{"id", "contract_number", "status", "recovery_status", "flagged", "gap_closed", "ins_value", "difference", "customer_name", "allied_claim_number", "updated"}).
			AddRow(1, "*********", "C", "R", time.Time{}, time.Time{}, "1112.35", "333", "Test", "*********", time.Time{}))

	mock.ExpectQuery(q(`select count(*) from gap_claims join gap_claim_recoveries on gap_claims.id = gap_claim_id
	join customers on gap_claims.customer_id = customers.id  where gap_claim_recoveries.status = 'RI'`)).
		WillReturnRows(sqlmock.NewRows([]string{"count"}).
			AddRow(1))

	mock.ExpectQuery(q(`select sum(nada - valuation_report_adjustments) from gap_claims join gap_claim_recoveries on gap_claims.id = gap_claim_id
	join customers on gap_claims.customer_id = customers.id  where gap_claim_recoveries.status = 'RI'`)).
		WillReturnRows(sqlmock.NewRows([]string{"totalEstimate"}).
			AddRow(1112.99))

	req, err := http.NewRequest("GET", "/api/gapclaims/recovery?sort_by=difference&sort_order=desc", nil)
	if err != nil {
		t.Fatal(err)
	}

	rr := httptest.NewRecorder()
	handler := http.HandlerFunc(handlers.APIHandler(ClaimRecoveryIndex))

	r := chi.NewRouter()
	r.HandleFunc("/api/gapclaims/recovery", handler)
	r.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v",
			status, http.StatusOK)
	}

	expected := `{"count":1,"gap_claims":[{"id":1,"contract_number":"*********","status":"C","recovery_status":"R","flagged":"0001-01-01T00:00:00Z","gap_closed":"0001-01-01T00:00:00Z","ins_value":"1112.35","difference":"333","customer_name":"Test","allied_claim_number":"*********","submitted":"0001-01-01T00:00:00Z","updated":"0001-01-01T00:00:00Z"}],"totalDifference":1112.99}`
	if rr.Body.String() != expected {
		t.Errorf("handler returned unexpected body: got %v want %v",
			rr.Body.String(), expected)
	}

	if err := mock.ExpectationsWereMet(); err != nil {
		t.Errorf("there were unfulfilled expections: %s", err)
	}
}

func DISABLEDTestClaimRecoveryIndexSearchByContractNumber(t *testing.T) {
	mdb, mock, err := sqlmock.New()
	if err != nil {
		t.Fatal(err)
	}
	dbx := sqlx.NewDb(mdb, "sqlmock")
	db.DB = dbx
	q := regexp.QuoteMeta

	mock.ExpectQuery(q(`select gap_claims.id, contract_number, gap_claims.status, gap_claim_recoveries.status as recovery_status, start_date as flagged, gap_closed_at as gap_closed,
	valuation_report_adjustments as ins_value, (nada - valuation_report_adjustments) as difference,
	customers.last_name ||', '|| customers.first_name as customer_name,
	allied_claim_number, updated_at as updated from gap_claims join gap_claim_recoveries on gap_claims.id = gap_claim_id
	join customers on gap_claims.customer_id = customers.id  where gap_claim_recoveries.status = 'RI' and (contract_number ilike $1 or insurance_company ilike $1) order by start_date desc`)).
		WillReturnRows(sqlmock.NewRows([]string{"id", "contract_number", "status", "recovery_status", "flagged", "gap_closed", "ins_value", "difference", "customer_name", "allied_claim_number", "updated"}).
			AddRow(1, "*********", "C", "R", time.Time{}, time.Time{}, "1112.35", "333", "Test", "*********", time.Time{}))

	mock.ExpectQuery(q(`select count(*) from gap_claims join gap_claim_recoveries on gap_claims.id = gap_claim_id
	join customers on gap_claims.customer_id = customers.id  where gap_claim_recoveries.status = 'RI'`)).
		WillReturnRows(sqlmock.NewRows([]string{"count"}).
			AddRow(1))

	mock.ExpectQuery(q(`select sum(nada - valuation_report_adjustments) from gap_claims join gap_claim_recoveries on gap_claims.id = gap_claim_id
	join customers on gap_claims.customer_id = customers.id  where gap_claim_recoveries.status = 'RI'`)).
		WillReturnRows(sqlmock.NewRows([]string{"totalEstimate"}).
			AddRow(1112.99))

	req, err := http.NewRequest("GET", "/api/gapclaims/recovery?q=*********", nil)
	if err != nil {
		t.Fatal(err)
	}

	rr := httptest.NewRecorder()
	handler := http.HandlerFunc(handlers.APIHandler(ClaimRecoveryIndex))

	r := chi.NewRouter()
	r.HandleFunc("/api/gapclaims/recovery", handler)
	r.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v",
			status, http.StatusOK)
	}

	expected := `{"count":1,"gap_claims":[{"id":1,"contract_number":"*********","status":"C","recovery_status":"R","flagged":"0001-01-01T00:00:00Z","gap_closed":"0001-01-01T00:00:00Z","ins_value":"1112.35","difference":"333","customer_name":"Test","allied_claim_number":"*********","submitted":"0001-01-01T00:00:00Z","updated":"0001-01-01T00:00:00Z"}],"totalDifference":1112.99}`
	if rr.Body.String() != expected {
		t.Errorf("handler returned unexpected body: got %v want %v",
			rr.Body.String(), expected)
	}

	if err := mock.ExpectationsWereMet(); err != nil {
		t.Errorf("there were unfulfilled expections: %s", err)
	}
}
