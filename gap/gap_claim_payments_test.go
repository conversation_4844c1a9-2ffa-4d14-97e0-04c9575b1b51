package gap

import (
	"regexp"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/jmoiron/sqlx"

	"phizz/db"

	"phizz/intacct"

	"github.com/shopspring/decimal"
)

func Test_convertIntacctTime(t *testing.T) {
	converted := intacct.ToTime("11/14/2016")
	year, month, day := converted.Date()

	if year != 2016 || month != 11 || day != 14 {
		t.Error("Conversion of Intacct Date to time.Time failed")
	}
}
func Test_updateGapClaimPaidInformation(t *testing.T) {
	mdb, mock, err := sqlmock.New()
	if err != nil {
		t.Fatal(err)
	}
	dbx := sqlx.NewDb(mdb, "sqlmock")
	db.DB = dbx
	q := regexp.QuoteMeta

	mock.ExpectBegin()
	mock.ExpectExec(q(`update gap_claim_payments set check_number = $1, amount = $2, paid_date = $3, updated_at = now() at time zone 'utc' where id = $4`)).
		WithArgs(12350, decimal.NewFromFloat(4530.00), time.Time{}, "1").WillReturnResult(sqlmock.NewResult(1, 1))

	mock.ExpectExec(q(`update gap_claims set status = $1 where id = $2`)).
		WithArgs("C", "10").WillReturnResult(sqlmock.NewResult(1, 1))

	mock.ExpectExec(q(`update gap_claims set gap_closed_at=now() at time zone 'utc' where id =$1`)).
		WithArgs("10").WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()

	gapClaimPayment := gapClaimPaymentPayload{}
	gapClaimPayment.CheckNumber = 12350
	gapClaimPayment.Amount.Decimal = decimal.NewFromFloat(4530.00)
	gapClaimPayment.Amount.Valid = true
	gapClaimPayment.PaidDate.Time = time.Time{}
	gapClaimPayment.PaidDate.Valid = true

	err = updateGapClaimPaidInformation("1", "10", &gapClaimPayment)
	if err != nil {
		t.Errorf("Error executing updateGapClaimPaidInformation %s", err)
	}

	if err := mock.ExpectationsWereMet(); err != nil {
		t.Errorf("there were unfulfilled expectations: %s", err)
	}
}

func Test_getBatchKey(t *testing.T) {
	mdb, mock, err := sqlmock.New()
	if err != nil {
		t.Fatal(err)
	}
	dbx := sqlx.NewDb(mdb, "sqlmock")
	db.DB = dbx
	q := regexp.QuoteMeta

	mock.ExpectQuery(q(`select batch_key from intacct_bill_batches where batch_title = $1`)).WithArgs("11/14/2016").WillReturnRows(sqlmock.NewRows([]string{"batch_key"}).AddRow(6496))

	_, err = getBatchKey("11/14/2016")
	if err != nil {
		t.Errorf("getBatchKey function returned error %s", err)
	}

	if err := mock.ExpectationsWereMet(); err != nil {
		t.Errorf("there were unfulfilled expectations: %s", err)
	}
}

func Test_storeBatchKey(t *testing.T) {
	mdb, mock, err := sqlmock.New()
	if err != nil {
		t.Fatal(err)
	}
	dbx := sqlx.NewDb(mdb, "sqlmock")
	db.DB = dbx
	q := regexp.QuoteMeta

	mock.ExpectBegin()
	tx, err := dbx.Beginx()
	if err != nil {
		t.Errorf("failed to create transaciton: %s", err)
	}
	defer func() { _ = tx.Rollback() }()

	mock.ExpectQuery(q(`insert into intacct_bill_batches(batch_title, batch_key) values($1,$2)returning id`)).
		WithArgs("11/14/2016", 6496).WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

	err = storeBatchKey(tx, "11/14/2016", 6496)
	if err != nil {
		t.Errorf("storeBatchKey function returned error %s", err)
	}

	if err := mock.ExpectationsWereMet(); err != nil {
		t.Errorf("there were unfulfilled expectations: %s", err)
	}
}
