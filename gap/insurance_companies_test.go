package gap

import (
	"github.com/DATA-DOG/go-sqlmock"
	"github.com/go-chi/chi"
	"github.com/jmoiron/sqlx"
	"net/http"
	"net/http/httptest"
	"phizz/db"
	"phizz/handlers"
	"regexp"
	"testing"
)

func TestInsuranceCompanyShow(t *testing.T) {
	mdb, mock, err := sqlmock.New()
	if err != nil {
		t.Fatal(err)
	}
	dbx := sqlx.NewDb(mdb, "sqlmock")
	db.DB = dbx

	q := regexp.QuoteMeta

	mock.ExpectQuery(q(`SELECT id, name FROM gap_insurance_companies WHERE id = $1 LIMIT 1`)).
		WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
			AddRow(62, "Test123"))

	req, err := http.NewRequest("GET", "/api/insurance-companies/62", nil)
	if err != nil {
		t.<PERSON><PERSON>(err)
	}

	rr := httptest.NewRecorder()
	handler := http.HandlerFunc(handlers.APIHandler(InsuranceCompanyShow))

	r := chi.NewRouter()
	r.HandleFunc("/api/insurance-companies/{id:[0-9]+}", handler)
	r.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v",
			status, http.StatusOK)
	}

	expected := `{"insurance_company":{"id":62,"name":"Test123"}}`
	if rr.Body.String() != expected {
		t.Errorf("handler returned unexpected body: got %v want %v",
			rr.Body.String(), expected)
	}

	if err := mock.ExpectationsWereMet(); err != nil {
		t.Errorf("there were unfulfilled expections: %s", err)
	}
}

func TestInsuranceCompanyIndex(t *testing.T) {
	mdb, mock, err := sqlmock.New()
	if err != nil {
		t.Fatal(err)
	}
	dbx := sqlx.NewDb(mdb, "sqlmock")
	db.DB = dbx

	q := regexp.QuoteMeta

	mock.ExpectQuery(q(`select id, name from gap_insurance_companies where name ilike $1`)).
		WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
			AddRow(62, "Test123"))

	req, err := http.NewRequest("GET", "/api/insurance-companies?q=test", nil)
	if err != nil {
		t.Fatal(err)
	}

	rr := httptest.NewRecorder()
	handler := http.HandlerFunc(handlers.APIHandler(InsuranceCompanyIndex))

	r := chi.NewRouter()
	r.HandleFunc("/api/insurance-companies", handler)
	r.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v",
			status, http.StatusOK)
	}

	expected := `{"insurance_companies":[{"id":62,"name":"Test123"}]}`
	if rr.Body.String() != expected {
		t.Errorf("handler returned unexpected body: got %v want %v",
			rr.Body.String(), expected)
	}

	if err := mock.ExpectationsWereMet(); err != nil {
		t.Errorf("there were unfulfilled expections: %s", err)
	}
}
