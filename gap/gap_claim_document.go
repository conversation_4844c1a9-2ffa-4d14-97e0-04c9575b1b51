package gap

import (
	"bytes"
	"database/sql"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"phizz/conf"
	"phizz/db"
	"phizz/handlers"
	"phizz/s3util"
	"phizz/util"

	"github.com/go-chi/chi"
	newrelic "github.com/newrelic/go-agent"
	"github.com/pkg/errors"
)

type documentPayload struct {
	ID              int    `json:"-" db:"id"`
	GapClaimID      int    `json:"gap_claim_id" db:"gap_claim_id"`
	FieldID         int    `json:"field_id" db:"field_id"`
	ContractNumber  string `json:"contract_number" db:"contract_number"`
	ContractCode    string `json:"contract_code" db:"contract_code"`
	FileContent     string `json:"file_content" db:"-"`
	CreatedByUserID int    `json:"-" db:"created_by_user_id"`
	FileName        string `json:"file_name" db:"file_name"`
	FileType        string `json:"file_type" db:"-"`
	S3Bucket        string `json:"-" db:"s3_bucket"`
	LetterType      string `json:"letter_type" db:"letter_type"`
}

func (p documentPayload) validate() (map[string]string, error) {

	v := map[string]string{}
	if p.CreatedByUserID == 0 {
		v["created_by_user_id"] = "CreatedByUserID is required"
	}
	if p.GapClaimID == 0 {
		v["gap_claim_id"] = "GAP claim ID is required."
	}
	if len(p.FileName) < 1 {
		v["file_name"] = "File name is required."
	}
	if len(p.FileType) < 1 {
		v["file_type"] = "File type is required."
	} else if !handlers.IsValidFileType(p.FileType) {
		v["file_type"] = "File type is not valid"
	}
	if len(p.FileContent) < 1 {
		v["file_content"] = "File content is required"
	} else if len(p.FileContent) > conf.Get().AWS.MaxSize {
		v["file_content"] = "File content size is more than " + strconv.Itoa(conf.Get().AWS.MaxSize)
	}
	if p.FieldID > 0 && (p.FieldID < db.FieldIDBookOutRequested || p.FieldID > db.FieldIDMAX) {
		v["field_id"] = "Invalid Field ID"
	}
	if (len(p.ContractNumber) > 0 && len(p.ContractCode) == 0) || (len(p.ContractCode) > 0 && len(p.ContractNumber) == 0) {
		v["contract_code_and_number"] = "Contract code is required with contract number"
	}
	if len(p.ContractNumber) > 0 && len(p.ContractCode) > 0 {
		exist, err := cancelContractExists(p.GapClaimID, p.ContractNumber, p.ContractCode)
		if err != nil {
			return v, errors.Wrap(err, "Error validating cancel contract")
		}
		if !exist {
			v["contract_code_and_number"] = "Invalid contract details"
		}

	}
	return v, nil
}

// SaveDocument saves document to s3 and returns documentID from the gap_claim_documents table
func SaveDocument(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	p, err := getDocumentFromPayload(req.Body)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Bad request", nil)
	}

	p.clean()
	p.CreatedByUserID = user.ID
	formErrs, err := p.validate()
	if err != nil {
		err = errors.Wrap(err, "error validating document")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error during validation", nil)
	}
	if len(formErrs) > 0 {
		return http.StatusBadRequest, map[string]interface{}{
			"validation_errors": formErrs,
		}
	}
	txn := w.(newrelic.Transaction)
	documentID, err := saveDocument(&p, txn)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error in saving document", nil)
	}
	return http.StatusOK, map[string]interface{}{"gap_claim_document": documentID}

}

func saveDocument(p *documentPayload, txn newrelic.Transaction) (int, error) {
	const errTxt = "Error saving Document"

	contractNumber := ""
	documentID := 0
	err := db.Get().Get(&contractNumber, "select contract_number from gap_claims where id = $1", p.GapClaimID)
	if err != nil {
		if err == sql.ErrNoRows {
			return documentID, errors.Wrap(err, "The GAP claim was not found")
		}
		return documentID, errors.Wrap(err, "Error loading GAP claim from database.")
	}

	tx, err := db.Get().Beginx()
	if err != nil {
		err = errors.Wrap(err, "error starting document upload transaction")
		return documentID, errors.Wrap(err, errTxt)
	}

	insertQuery := `insert into gap_claim_documents (gap_claim_id, field_id, contract_number, contract_code, s3_bucket, file_name, letter_type, created_by_user_id, created_at) values
	 (:gap_claim_id, :field_id, :contract_number, :contract_code, :s3_bucket, :file_name, :letter_type, :created_by_user_id, now() at time zone 'utc') returning id`

	if len(p.FileContent) > 0 {
		bucket := s3util.Bucket()
		p.S3Bucket = bucket
		name := "gap-claims/" + contractNumber + "/" + p.FileName + p.FileType
		p.FileName = name
		data, err := base64.StdEncoding.DecodeString(p.FileContent)
		if err != nil {
			_ = tx.Rollback()
			err = errors.Wrap(err, "error base64 decoding file data")
			return documentID, errors.Wrap(err, errTxt)
		}
		err = s3util.Put(txn, bytes.NewReader(data), s3util.DefaultRegion, p.S3Bucket, p.FileName)
		if err != nil {
			_ = tx.Rollback()
			err = errors.Wrap(err, "error uploading document file to s3")
			return documentID, errors.Wrap(err, errTxt)
		}
	}

	stmt, err := tx.PrepareNamed(insertQuery)
	if err != nil {
		_ = tx.Rollback()
		err = errors.Wrap(err, "error creating document creating statement")
		return documentID, errors.Wrap(err, errTxt)
	}
	defer func() { _ = stmt.Close() }()

	err = stmt.Get(&documentID, p)
	if err != nil {
		_ = tx.Rollback()
		err = errors.Wrap(err, "error inserting document form")
		return documentID, errors.Wrap(err, errTxt)
	}
	err = tx.Commit()
	if err != nil {
		err = errors.Wrap(err, "error committing document transaction")
		return documentID, errors.Wrap(err, errTxt)
	}

	return documentID, nil
}

func (p *documentPayload) clean() {
	s := strings.TrimSpace

	p.FileName = s(p.FileName)
	p.FileType = s(p.FileType)
	p.ContractNumber = s(p.ContractNumber)
	p.ContractCode = s(p.ContractCode)
}

func getDocumentFromPayload(reader io.Reader) (documentPayload, error) {
	var p documentPayload

	dec := json.NewDecoder(reader)
	err := dec.Decode(&p)
	if err != nil {
		err = errors.Wrap(err, "error decoding document payload")
		return p, err
	}

	return p, nil
}

// DocumentDownload returns a pre-signed S3 URL for a the document
func DocumentDownload(w http.ResponseWriter, req *http.Request, user db.User) {
	id, err := strconv.Atoi(chi.URLParam(req, "id"))
	if err != nil {
		fmt.Fprint(w, "Bad request. Could not read document ID.")
	}
	url, err := documentDownload(id, user)
	if err != nil {
		fmt.Fprint(w, "Error in downloading document")
	}

	http.Redirect(w, req, url, http.StatusTemporaryRedirect)
}

// documentDownload returns a pre-signed S3 URL for a the document
func documentDownload(documentID int, user db.User) (string, error) {
	cf := struct {
		S3Bucket string `db:"s3_bucket"`
		S3Key    string `db:"s3_key"`
	}{}
	err := db.Get().Get(&cf, `select s3_bucket, file_name s3_key from gap_claim_documents where deleted_at is null and id = $1`, documentID)
	if err != nil {
		if err == sql.ErrNoRows {
			return "", errors.Wrap(err, "The document not found")
		}
		return "", errors.Wrap(err, "error loading document data for download")
	}
	_, fileName := filepath.Split(cf.S3Key)
	extn := filepath.Ext(cf.S3Key)
	contentType := util.ContentTypeByExtension(extn)
	reverseProxy := s3util.GetS3ReverseProxy()
	signedURL, err := reverseProxy.GetSecureURL(
		s3util.DefaultRegion, cf.S3Bucket, url.PathEscape(cf.S3Key),
		url.PathEscape(fileName), contentType, user,
		time.Minute*conf.Get().S3ReverseProxy.DefaultLinkTimeoutMinutes)
	if err != nil {
		return "", errors.Wrap(err, "error making document presigned download URL")

	}
	return signedURL, nil
}

// DocumentDelete deletes S3 object
func DocumentDelete(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	id, err := strconv.Atoi(chi.URLParam(req, "id"))
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Bad request", nil)
	}

	err = documentDelete(id, user.ID)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Could not delete document", nil)
	}
	return http.StatusOK, map[string]interface{}{"gap_claim_document": id}
}

// documentDelete
func documentDelete(documentID, userID int) error {
	const errTxt = "Error deleting Document"

	documentData := struct {
		GapClaimID int    `db:"gap_claim_id"`
		FileName   string `db:"file_name"`
		FieldID    int    `db:"field_id"`
	}{}
	err := db.Get().Get(&documentData, `select gap_claim_id, file_name, field_id from gap_claim_documents where deleted_at is null and id = $1`, documentID)
	if err != nil {
		if err == sql.ErrNoRows {
			return errors.Wrap(err, "The document not found")
		}
		return errors.Wrap(err, "error loading document data for delete")
	}

	tx, err := db.Get().Beginx()
	if err != nil {
		err = errors.Wrap(err, "error starting document delete transaction")
		return errors.Wrap(err, errTxt)
	}

	// delete document
	query := `update gap_claim_documents set deleted_at=now() at time zone 'utc', deleted_by_user_id=$1 where id = $2`
	_, err = tx.Exec(query, userID, documentID)
	if err != nil {
		_ = tx.Rollback()
		return errors.Wrap(err, "Error deleting document")
	}

	// add record note
	recordNote := recordNotePayload{}
	recordNote.ID = documentData.GapClaimID
	recordNote.CreatedByUserID = userID
	recordNote.CreatedAt = time.Now()
	recordNote.NotesText = "Deleted the attached file " + documentData.FileName + " to the " + db.FieldRecordNoteDescription[documentData.FieldID]
	_, err = insertRecordNote(&recordNote, nil)
	if err != nil {
		_ = tx.Rollback()
		return errors.Wrap(err, "Error adding notes for delete document")
	}

	err = tx.Commit()
	if err != nil {
		err = errors.Wrap(err, "error committing document transaction")
		return errors.Wrap(err, errTxt)
	}

	return nil
}

type fieldDocument struct {
	FieldID   int `json:"field_id" db:"-"`
	Count     int `json:"count" db:"-"`
	Documents []struct {
		ID        int       `db:"id" json:"id"`
		FieldID   int       `db:"field_id" json:"field_id"`
		FileName  string    `db:"file_name" json:"file_name"`
		CreatedAt time.Time `db:"created_at" json:"created_at"`
	} `json:"documents" db:"-"`
}

func fieldDocuments(gapClaimID int) ([]fieldDocument, error) {
	exists, err := gapClaimExists(gapClaimID)
	if err != nil {
		return nil, errors.Wrap(err, "Error getting GAP claim from database")
	}
	if !exists {
		return nil, errors.Wrap(err, "GAP claim does not exist")
	}

	fieldDocumentList := []struct {
		ID        int       `db:"id" json:"id"`
		FieldID   int       `db:"field_id" json:"field_id"`
		FileName  string    `db:"file_name" json:"file_name"`
		CreatedAt time.Time `db:"created_at" json:"created_at"`
	}{}
	err = db.Get().Select(&fieldDocumentList, `select id, field_id, file_name, created_at
	from gap_claim_documents where deleted_at is null and gap_claim_id = $1 and field_id > 0 order by field_id asc`, gapClaimID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.Wrap(err, "No field documents found for claim ")
		}
		return nil, errors.Wrap(err, "error loading field document list ")
	}

	fieldDocuments := []fieldDocument{}

	currentFieldID := 0
	numberOfFieldID := 0
	for _, currentResultObject := range fieldDocumentList {
		if currentResultObject.FieldID != currentFieldID {
			currentFieldID = currentResultObject.FieldID
			numberOfFieldID = numberOfFieldID + 1
			fieldDocuments = append(fieldDocuments, fieldDocument{})
		}
		fieldDocuments[numberOfFieldID-1].FieldID = currentFieldID
		fieldDocuments[numberOfFieldID-1].Documents = append(fieldDocuments[numberOfFieldID-1].Documents, currentResultObject)
		fieldDocuments[numberOfFieldID-1].Count = fieldDocuments[numberOfFieldID-1].Count + 1
	}

	return fieldDocuments, nil
}

type contractFieldDocument struct {
	ContractNumber string `json:"contract_number" db:"-"`
	ContractCode   string `json:"contract_code" db:"-"`
	Count          int    `json:"count" db:"-"`
	Documents      []struct {
		ID             int       `db:"id" json:"id"`
		ContractNumber string    `db:"contract_number" json:"contract_number"`
		ContractCode   string    `db:"contract_code" json:"contract_code"`
		FileName       string    `db:"file_name" json:"file_name"`
		CreatedAt      time.Time `db:"created_at" json:"created_at"`
	} `json:"documents" db:"-"`
}

// DocumentIndex returns all documents for given claim
func DocumentIndex(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	id, err := strconv.Atoi(chi.URLParam(req, "id"))
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Bad request", nil)
	}

	docs := struct {
		FieldDocuments         []fieldDocument         `json:"field_documents"`
		ContractFieldDocuments []contractFieldDocument `json:"contract_field_documents"`
	}{}
	docs.FieldDocuments, err = fieldDocuments(id)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting field document list", nil)
	}

	docs.ContractFieldDocuments, err = contractFieldDocuments(id)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting contract document list", nil)
	}

	return http.StatusOK, map[string]interface{}{"docs": docs}
}

func contractFieldDocuments(gapClaimID int) ([]contractFieldDocument, error) {
	exists, err := gapClaimExists(gapClaimID)
	if err != nil {
		return nil, errors.Wrap(err, "Error getting GAP claim from database")
	}
	if !exists {
		return nil, errors.Wrap(err, "GAP claim does not exist")
	}

	contractDocumentList := []struct {
		ID             int       `db:"id" json:"id"`
		ContractNumber string    `db:"contract_number" json:"contract_number"`
		ContractCode   string    `db:"contract_code" json:"contract_code"`
		FileName       string    `db:"file_name" json:"file_name"`
		CreatedAt      time.Time `db:"created_at" json:"created_at"`
	}{}
	err = db.Get().Select(&contractDocumentList, `select id, contract_number, contract_code, file_name, created_at
	from gap_claim_documents where char_length(contract_number) > 0 and deleted_at is null and gap_claim_id = $1`, gapClaimID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.Wrap(err, "No contract field documents found for claim ")
		}
		return nil, errors.Wrap(err, "error loading contract field document list ")
	}

	cfds := []contractFieldDocument{}

	currentContractNumber := ""
	currentContractCode := ""
	countOfFields := 0
	for _, currentResultObject := range contractDocumentList {
		if currentResultObject.ContractNumber != currentContractNumber || currentResultObject.ContractCode != currentContractCode {
			currentContractNumber = currentResultObject.ContractNumber
			currentContractCode = currentResultObject.ContractCode
			countOfFields = countOfFields + 1
			cfds = append(cfds, contractFieldDocument{})
		}
		cfds[countOfFields-1].ContractCode = currentContractCode
		cfds[countOfFields-1].ContractNumber = currentContractNumber
		cfds[countOfFields-1].Documents = append(cfds[countOfFields-1].Documents, currentResultObject)
		cfds[countOfFields-1].Count = cfds[countOfFields-1].Count + 1
	}

	return cfds, nil
}
