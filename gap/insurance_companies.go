package gap

import (
	"database/sql"
	"encoding/json"
	"github.com/go-chi/chi"
	"github.com/pkg/errors"
	"net/http"
	"phizz/db"
	"phizz/handlers"
	"strings"
)

// CompanyPayload includes name of insurance company
type CompanyPayload struct {
	ID   int    `json:"id" db:"id"`
	Name string `json:"name" db:"name"`
}

// InsuranceCompanyIndex returns code and description for matching
func InsuranceCompanyIndex(_ http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	search := req.FormValue("q")
	if len(search) < 3 {
		return http.StatusBadRequest, handlers.ErrorMessage(errors.New("search string too small"), "search string too small", nil)
	}
	search = "%" + search + "%"

	var companies []CompanyPayload

	err := db.Get().Select(&companies, `select id, name from gap_insurance_companies where name ilike $1`, search)
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, nil
		}
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "error loading insurance_companies ", nil)
	}
	return http.StatusOK, map[string]interface{}{"insurance_companies": companies}
}

// InsuranceCompanyShow return company name by matching id
func InsuranceCompanyShow(_ http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	id := chi.URLParam(req, "id")

	query := "SELECT id, name FROM gap_insurance_companies WHERE id = $1 LIMIT 1"

	company := CompanyPayload{}
	err := db.Get().Get(&company, query, id)
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage(err, "The Insurance company name was not found ", nil)
		}
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error loading Insurance company from database.", nil)
	}
	return http.StatusOK, map[string]interface{}{"insurance_company": company}
}

// InsuranceCompanyCreate create new insurance company name in database
// This function also checks for duplicate records in company name
func InsuranceCompanyCreate(_ http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	var company CompanyPayload
	err := insuranceCompanyPayloadFromReq(&company, req)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Malformed Insurance Company name for create.", nil)
	}

	// clean insurance company name
	company.Name = strings.TrimSpace(company.Name)
	if company.Name == "" {
		return http.StatusBadRequest, handlers.ErrorMessage(errors.New("Invalid inout"), "Insurance company name is required", nil)
	}

	exists, err := db.RowExists("SELECT id, name FROM gap_insurance_companies WHERE name = $1 LIMIT 1", company.Name)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting insurance company name", nil)
	}

	if exists {
		return http.StatusConflict, handlers.ErrorMessage(err, "Insurance Company Already exists.", nil)
	}

	insertCommand := `INSERT INTO gap_insurance_companies (name) VALUES (:name) RETURNING id`

	stmt, err := db.Get().PrepareNamed(insertCommand)
	defer func() { _ = stmt.Close() }()
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "PrepareNamed failed", nil)
	}
	err = stmt.Get(&company.ID, company)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error creating insurance company", nil)
	}
	return http.StatusOK, map[string]interface{}{"id": &company.ID}
}

func insuranceCompanyPayloadFromReq(companyPayload *CompanyPayload, req *http.Request) error {
	dec := json.NewDecoder(req.Body)
	err := dec.Decode(&companyPayload)
	return errors.Wrap(err, "decoding insurance company name request failed")
}
