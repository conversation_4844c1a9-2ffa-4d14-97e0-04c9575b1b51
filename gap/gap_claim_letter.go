package gap

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/base64"
	"encoding/json"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"

	"phizz/conf"
	"phizz/db"
	"phizz/email"
	"phizz/handlers"
	"phizz/randstr"

	"github.com/go-chi/chi"
	newrelic "github.com/newrelic/go-agent"
	"github.com/pkg/errors"
)

type emailBodyPayload struct {
	EmailBody  string `json:"email_body" db:"-"`
	LetterType string `json:"letter_type" db:"-"`
}

func (p emailBodyPayload) validate() (map[string]string, error) {
	v := map[string]string{}
	if len(p.EmailBody) == 0 {
		v["email_body"] = "email_body is required"
	}
	if len(p.LetterType) == 0 {
		v["letter_type"] = "letter_type is required"
	}
	return v, nil
}

// ClaimLetter saves file to s3 and/or sends email to customer
func ClaimLetter(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	ctx := req.Context()
	const errTxt = "Error sending email"
	id := chi.URLParam(req, "id")
	document := documentPayload{}
	document.GapClaimID, _ = strconv.Atoi(id)

	document.CreatedByUserID = user.ID

	emailBody, err := getEmailBodyFromPayload(req.Body)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Bad request", nil)
	}

	decodedString, err := base64.StdEncoding.DecodeString(emailBody.EmailBody)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error in decoding email body"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error in decoding email body", nil)
	}
	emailBody.EmailBody = string(decodedString)

	formErrs, err := emailBody.validate()
	if err != nil {
		err = errors.Wrap(err, "error validating email content")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error during validation of email", nil)
	}
	if len(formErrs) > 0 {
		return http.StatusBadRequest, map[string]interface{}{
			"validation_errors": formErrs,
		}
	}

	emailBodyPdf, err := handlers.HTMLToPdf(emailBody.EmailBody)
	if err != nil {
		err = errors.WithMessage(err, "error in HTML to PDF conversion")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, errTxt, nil)
	}
	document.FileContent = emailBodyPdf

	filePrefix := "LETTER_"
	if req.FormValue("email") == "true" {
		filePrefix = "EMAIL_"
	}

	// send email
	if req.FormValue("email") == "true" {
		// Get email address for claim
		emailAddress, err := getEmailAddress(ctx, req, id)
		if err != nil {
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error in getting email address", nil)
		}

		if emailAddress == "" {
			return http.StatusNotFound, handlers.ErrorMessage(err, "Unable to find email address.", nil)
		}

		contractNumber, err := getContractNumber(id)
		if err != nil {
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error in getting contract number", nil)
		}
		err = email.SendHTMLSync(conf.Get().GapEmail.From, []string{emailAddress}, "Contract Number# "+contractNumber, []byte(emailBody.EmailBody))
		if err != nil {
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error in sending email", nil)
		}
	}

	// send pdf to s3 for storing
	document.FileName = filePrefix + id + "_" + randstr.StringN(20)
	document.FileType = ".pdf"
	document.LetterType = emailBody.LetterType

	txn := w.(newrelic.Transaction)
	documentID, err := saveDocument(&document, txn)
	if err != nil {
		err = errors.Wrap(err, "error in saving pdf to s3")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, errTxt, nil)
	}

	url, err := documentDownload(documentID, user)
	if err != nil {
		err = errors.Wrap(err, "error in getting download url")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Email is sent, failure in getting the s3 download url", nil)
	}

	return http.StatusOK, map[string]interface{}{"s3_document_url": url}
}

func getEmailBodyFromPayload(reader io.Reader) (emailBodyPayload, error) {
	var p emailBodyPayload

	dec := json.NewDecoder(reader)
	err := dec.Decode(&p)
	if err != nil {
		err = errors.Wrap(err, "Error decoding emailbody payload")
		return p, err
	}

	return p, nil
}

func getEmailAddress(ctx context.Context, req *http.Request, id string) (string, error) {
	query := "select contract_number from gap_claims where id=$1"
	var contractCode string
	err := db.Get().GetContext(ctx, &contractCode, query, id)
	if err != nil {
		if err == sql.ErrNoRows {
			return "", errors.Wrap(err, "The GAP claim was not found")
		}
		// We will report error other than no rows found as it might be genuine malfunction
		handlers.ReportError(req, err)
		return "", errors.Wrap(err, "Error loading email address from database.")
	}

	txn := newrelic.FromContext(ctx)
	contract, err := handlers.GetContractByID(txn, req, contractCode)
	if err != nil {
		return "", errors.Wrap(err, "Error loading email address from database.")
	}

	return contract.CustomerDetails.Email, nil
}

func getContractNumber(id string) (string, error) {
	query := "select contract_number from gap_claims where id=$1"
	contractNumber := ""
	err := db.Get().Get(&contractNumber, query, id)
	if err != nil {
		if err == sql.ErrNoRows {
			return "", errors.Wrap(err, "The GAP claim was not found")
		}
		return "", errors.Wrap(err, "Error loading GAP claim from database.")
	}
	return contractNumber, nil
}

// EmailSubmittedGapClaims emails the submitted claims report each day, this function is invoked by scheduled job
func EmailSubmittedGapClaims() error {

	// We are using UTC timezone to save claims and submitted claims list will be generated at mountain time
	// If we use UTC, there is difference of 6 hours during MDT and 7 hours during MST,
	// Hence at 00:00 hrs - 05/18/17 MT, the UTC time will be 06:00 Hrs 05/18/17

	var loc = handlers.LoadLocOrPanic("MST7MDT")
	currentTime := time.Now().In(loc)
	startTime := time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), 0, 0, 0, 0, loc).UTC()
	endTime := time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day()+1, 23, 59, 59, 0, loc).UTC()

	claims, err := getSubmittedClaimsByDates(startTime, endTime)
	if err != nil {
		return errors.Wrap(err, "Error while fetching submitted claims")
	}

	emailTo := "<EMAIL>"
	subjectText := "GAP Claims Submitted to Accounting for " + currentTime.Format("2006-01-02")
	emailBody := new(bytes.Buffer)
	emailBody.WriteString("<p>Dear Accounting Team</p>")
	emailBody.WriteString("<p>Date: " + currentTime.Format("2006-01-02") + "</p>")
	if len(claims) == 0 {
		emailBody.WriteString("<b>No Claims submitted today.</b>")
	} else {
		emailBody.WriteString("<p>Following is a list of all claims submitted for payment today</p>")
		emailBody.WriteString("<table>")
		emailBody.WriteString("<tr><th>GAP Contract#</th><th>Customer Name</th><th>Amount</th></tr>")
		for _, claim := range claims {
			emailBody.WriteString(`<tr>
		<td>` + claim.ContractNumber + `</td>
		<td>` + claim.LastName + ` ` + claim.FirstName + `</td>
		<td>` + claim.PaymentAmount.String() + `</td>
		</tr>`)
		}
		emailBody.WriteString("</table>")
	}
	err = email.SendHTMLSync(conf.Get().GapEmail.From, []string{emailTo}, subjectText, []byte(emailBody.String()))
	if err != nil {
		return errors.Wrap(err, "Error while sending email")
	}

	return err
}

// ClaimLetterIndex returns a list of record notes for given contract number
func ClaimLetterIndex(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	id := chi.URLParam(req, "id")
	letters := []struct {
		DocumentID string    `json:"document_id" db:"document_id"`
		LetterType string    `json:"letter_type" db:"letter_type"`
		SentAt     time.Time `json:"sent_at" db:"sent_at"`
		FileName   string    `json:"-" db:"file_name"`
	}{}

	args := []interface{}{}
	args = append(args, id)

	query := `select id as document_id, letter_type, created_at as sent_at, file_name from gap_claim_documents where gap_claim_id = $1
		  and (file_name like '%EMAIL%' or file_name like '%LETTER%') and field_id =0 order by sent_at asc`
	err := db.Get().Select(&letters, query, args...)
	if err != nil {
		err = errors.Wrap(err, "Database error getting email letters")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting email letters data", nil)
	}
	for i, letter := range letters {
		if strings.Contains(letter.FileName, "EMAIL_") {
			letters[i].LetterType = "Email " + letters[i].LetterType
		} else {
			letters[i].LetterType = "Letter " + letters[i].LetterType
		}
	}
	return http.StatusOK, map[string]interface{}{"letters": letters, "count": len(letters)}
}
