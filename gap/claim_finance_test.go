package gap

import (
	"net/http"
	"net/http/httptest"
	"regexp"
	"testing"
	"time"

	"phizz/db"
	"phizz/handlers"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/go-chi/chi"
	"github.com/jmoiron/sqlx"
)

func TestClaimFinanceIndex(t *testing.T) {
	mdb, mock, err := sqlmock.New()
	if err != nil {
		t.Fatal(err)
	}
	dbx := sqlx.NewDb(mdb, "sqlmock")
	db.DB = dbx
	q := regexp.QuoteMeta

	mock.ExpectQuery(q(`select gap_claims.id, last_name || ',' || first_name as insured_name, contract_number, status, date_of_claim_received as opened, case_reserve as amount, date_of_loss, bank_account_name as bank, bank_vendor_id, bank_address_street1, bank_address_street2, bank_address_city, bank_address_state, bank_address_zip, bank_account_number as loan_number from gap_claims join banks on bank_id = banks.id join customers on customer_id = customers.id where is_child = false order by insured_name asc limit 20 offset 0`)).
		WillReturnRows(sqlmock.NewRows([]string{"id", "insured_name", "contract_number", "status", "opened", "amount", "date_of_loss",
			"bank", "bank_vendor_id", "bank_address_street1", "bank_address_street2", "bank_address_city", "bank_address_state", "bank_address_zip", "loan_number"}).
			AddRow(1, "YOUNG,JOHN E", "*********", "WC", time.Time{}, "1112.99", time.Time{}, "HSBC", "VEN_000825", "PO BOX 60179", "", "CITY OF INDUSTRY", "CA", "91716", "23"))

	mock.ExpectQuery(q(`select count(*) from gap_claims join banks on bank_id = banks.id join customers on customer_id = customers.id where is_child = false`)).
		WillReturnRows(sqlmock.NewRows([]string{"count"}).
			AddRow(1))

	mock.ExpectQuery(q(`select sum(case_reserve) from gap_claims join banks on bank_id = banks.id join customers on customer_id = customers.id where is_child = false`)).
		WillReturnRows(sqlmock.NewRows([]string{"totalEstimate"}).
			AddRow(1112.99))

	req, err := http.NewRequest("GET", "/api/gapclaims/finance", nil)
	if err != nil {
		t.Fatal(err)
	}

	rr := httptest.NewRecorder()
	handler := http.HandlerFunc(handlers.APIHandler(ClaimFinanceIndex))

	r := chi.NewRouter()
	r.HandleFunc("/api/gapclaims/finance", handler)
	r.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v",
			status, http.StatusOK)
	}

	expected := `{"claims":[{"id":1,"insured_name":"YOUNG,JOHN E","contract_number":"*********","status":"WC","opened":"0001-01-01T00:00:00Z","amount":"1112.99","date_of_loss":"0001-01-01T00:00:00Z","bank":"HSBC","bank_vendor_id":"VEN_000825","bank_address_street1":"PO BOX 60179","bank_address_street2":"","bank_address_city":"CITY OF INDUSTRY","bank_address_state":"CA","bank_address_zip":"91716","loan_number":"23"}],"count":1,"totalEstimate":1112.99}`
	if rr.Body.String() != expected {
		t.Errorf("handler returned unexpected body: got %v want %v",
			rr.Body.String(), expected)
	}

	if err := mock.ExpectationsWereMet(); err != nil {
		t.Errorf("there were unfulfilled expections: %s", err)
	}
}

func TestClaimFinanceIndexOrderByName(t *testing.T) {
	mdb, mock, err := sqlmock.New()
	if err != nil {
		t.Fatal(err)
	}
	dbx := sqlx.NewDb(mdb, "sqlmock")
	db.DB = dbx
	q := regexp.QuoteMeta

	mock.ExpectQuery(q(`select gap_claims.id, last_name || ',' || first_name as insured_name, contract_number, status, date_of_claim_received as opened, case_reserve as amount, date_of_loss, bank_account_name as bank, bank_vendor_id, bank_address_street1, bank_address_street2, bank_address_city, bank_address_state, bank_address_zip, bank_account_number as loan_number from gap_claims join banks on bank_id = banks.id join customers on customer_id = customers.id where is_child = false order by insured_name desc limit 20 offset 0`)).
		WillReturnRows(sqlmock.NewRows([]string{"id", "insured_name", "contract_number", "status", "opened", "amount", "date_of_loss",
			"bank", "bank_vendor_id", "bank_address_street1", "bank_address_street2", "bank_address_city", "bank_address_state", "bank_address_zip", "loan_number"}).
			AddRow(1, "YOUNG,JOHN E", "*********", "WC", time.Time{}, "1112.99", time.Time{}, "HSBC", "VEN_000825", "PO BOX 60179", "", "CITY OF INDUSTRY", "CA", "91716", "23"))

	mock.ExpectQuery(q(`select count(*) from gap_claims join banks on bank_id = banks.id join customers on customer_id = customers.id where is_child = false`)).
		WillReturnRows(sqlmock.NewRows([]string{"count"}).
			AddRow(1))

	mock.ExpectQuery(q(`select sum(case_reserve) from gap_claims join banks on bank_id = banks.id join customers on customer_id = customers.id where is_child = false`)).
		WillReturnRows(sqlmock.NewRows([]string{"totalEstimate"}).
			AddRow(1112.99))

	req, err := http.NewRequest("GET", "/api/gapclaims/finance?order_by_name=desc", nil)
	if err != nil {
		t.Fatal(err)
	}

	rr := httptest.NewRecorder()
	handler := http.HandlerFunc(handlers.APIHandler(ClaimFinanceIndex))

	r := chi.NewRouter()
	r.HandleFunc("/api/gapclaims/finance", handler)
	r.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v",
			status, http.StatusOK)
	}

	expected := `{"claims":[{"id":1,"insured_name":"YOUNG,JOHN E","contract_number":"*********","status":"WC","opened":"0001-01-01T00:00:00Z","amount":"1112.99","date_of_loss":"0001-01-01T00:00:00Z","bank":"HSBC","bank_vendor_id":"VEN_000825","bank_address_street1":"PO BOX 60179","bank_address_street2":"","bank_address_city":"CITY OF INDUSTRY","bank_address_state":"CA","bank_address_zip":"91716","loan_number":"23"}],"count":1,"totalEstimate":1112.99}`
	if rr.Body.String() != expected {
		t.Errorf("handler returned unexpected body: got %v want %v",
			rr.Body.String(), expected)
	}

	if err := mock.ExpectationsWereMet(); err != nil {
		t.Errorf("there were unfulfilled expections: %s", err)
	}
}
