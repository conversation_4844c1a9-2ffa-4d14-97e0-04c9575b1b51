package gap

import (
	"net/http"
	"strconv"

	"phizz/db"
	"phizz/handlers"

	"github.com/go-chi/chi"
	"github.com/jung-kurt/gofpdf"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
)

// ClaimPdf generates the pdf for gap claim
func ClaimPdf(w http.ResponseWriter, req *http.Request, user db.User) {
	id := chi.URLParam(req, "id")

	gapClaim, err := gapClaimSelect(id)
	if err != nil {
		_ = handlers.Renderer().Text(w, http.StatusInternalServerError, "Error getting GAP claim from database")
		handlers.ReportError(req, errors.Wrap(err, "error getting GAP claim from database"))
		return
	}
	fpdf := gofpdf.New("Portrait", "in", "Letter", "")

	gapClaimPayment, err := getClaimPayment(gapClaim)
	if err != nil {
		_ = handlers.Renderer().Text(w, http.StatusInternalServerError, "Error getting GAP claim payment from database")
		handlers.ReportError(req, errors.Wrap(err, "error getting GAP claim payment from database"))
		return
	}
	claimToPdf(fpdf, gapClaim, gapClaimPayment)

	w.Header().Set("Content-Type", "application/pdf")
	w.WriteHeader(http.StatusOK)
	err = fpdf.Output(w)
	if err != nil {
		err = errors.Wrap(err, "error writing gap claim pdf")
		_ = handlers.Renderer().Text(w, http.StatusBadRequest, "error writing gap claim pdf")
		handlers.ReportError(req, err)
		return
	}
}

// claimToPdf generates the pdf for gap claim
func claimToPdf(fpdf *gofpdf.Fpdf, gapClaim *gapClaimPayload, gapClaimPayment *gapClaimPaymentPayload) {
	const dateFormat = "2006/01/02"
	const yesFlag = "[Y]"
	const noFlag = "[N]"

	gapStatusMap := map[string]string{
		db.GapClaimStatusInquiry:                 "Inquiry",
		db.GapClaimStatusPending:                 "Pending",
		db.GapClaimStatusReadyToProcess:          "Ready to process",
		db.GapClaimStatusWaitingForAuthorization: "Waiting for authorization",
		db.GapClaimStatusPendingDenial:           "Pending denial",
		db.GapClaimStatusWaitingForCheck:         "Authorization",
		db.GapClaimStatusReturnedForCorrections:  "Returned",
		db.GapClaimStatusCheckWritten:            "Check Written",
		db.GapClaimStatusCheckVoided:             "Check Voided",
		db.GapClaimStatusDeny:                    "Denied",
		db.GapClaimStatusClosedNoResponse:        "Closed No-Response",
		db.GapClaimStatusPendingReopened:         "Pending Reopened",
		db.GapClaimStatusNoGap:                   "No GAP",
	}

	recoveryStatusMap := map[string]string{
		db.GapClaimRecoveryStatusRecoveryInquiry:  "Recovery Inquiry",
		db.GapClaimRecoveryStatusInRecovery:       "In Recovery",
		db.GapClaimRecoveryStatusRecovered:        "Recovered",
		db.GapClaimRecoveryStatusNoRecovery:       "No Recovery",
		db.GapClaimRecoveryStatusPossibleRecovery: "Possible Recovery",
		db.GapClaimRecoveryStatusWaitingRecovery:  "Waiting Recovery",
	}

	printFlag := func(flag bool) {
		flagStr := noFlag
		if flag == true {
			flagStr = yesFlag
		}
		fpdf.CellFormat(0.15, 0.15, flagStr, "", 0, "CM", true, 0, "")
	}

	printCurrency := func(value decimal.Decimal) {
		fpdf.SetX(2.5)
		fpdf.CellFormat(1, 0.2, "$ "+value.StringFixed(2), "", 0, "R", false, 0, "")
	}

	fpdf.SetFillColor(211, 211, 211)
	fpdf.AddPage()
	fpdf.SetFont("Helvetica", "B", 16)
	fpdf.CellFormat(7, 0.2, gapClaim.ContractNumber+" "+gapClaim.Status, "", 0, "C", false, 0, "")
	fpdf.Ln(0.3)

	fpdf.CellFormat(7, 0.2, getCaseReserveLabel(gapClaim)+": $ "+gapClaim.CaseReserve.StringFixed(2), "", 0, "C", false, 0, "")
	fpdf.Ln(0.3)
	fpdf.SetFont("Helvetica", "", 8)

	printFlag(gapClaim.IsInProgress)

	fpdf.Cell(1, 0.2, "In Progress")
	fpdf.Ln(-1)
	fpdf.Cell(1, 0.2, "Run Amortization")
	printCurrency(gapClaim.RunAmortizationSheetValue)
	fpdf.Ln(-1)

	printFlag(gapClaim.HasCanceledContracts)
	fpdf.Cell(1, 0.2, "Cancel Contracts")
	fpdf.Ln(-1)
	for _, contract := range gapClaim.Contracts {
		fpdf.SetX(1.0)
		printFlag(contract.ContractFlag)
		fpdf.Cell(1, 0.2, contract.ContractName)
		printCurrency(contract.ContractValue)
		fpdf.Ln(-1)
	}
	if gapClaim.OtherLabel1 != "Other" {
		fpdf.SetX(1.0)
		fpdf.Cell(1, 0.2, gapClaim.OtherLabel1)
		printCurrency(gapClaim.OtherValue1)
		fpdf.Ln(-1)
	}
	if gapClaim.OtherLabel2 != "Other" {
		fpdf.SetX(1.0)
		fpdf.Cell(1, 0.2, gapClaim.OtherLabel2)
		printCurrency(gapClaim.OtherValue2)
		fpdf.Ln(-1)
	}
	if gapClaim.OtherLabel3 != "Other" {
		fpdf.SetX(1.0)
		fpdf.Cell(1, 0.2, gapClaim.OtherLabel3)
		printCurrency(gapClaim.OtherValue3)
		fpdf.Ln(-1)
	}

	printFlag(gapClaim.HasInsuranceCompany)
	fpdf.Cell(1, 0.2, "Insurance Company:")
	fpdf.SetX(2.5)
	fpdf.Cell(1, 0.2, gapClaim.InsuranceCompany)
	fpdf.Ln(-1)
	fpdf.SetX(1.0)
	fpdf.Cell(1, 0.2, "Claim Number:")
	fpdf.SetX(2.5)
	fpdf.Cell(1, 0.2, gapClaim.PolicyNumber)
	fpdf.Ln(-1)

	printFlag(gapClaim.HasNumberOfDelinquentPayments)
	fpdf.Cell(1, 0.2, "Number of Delinquent Payments")
	fpdf.SetX(2.5)
	fpdf.Cell(1, 0.2, strconv.Itoa(gapClaim.NumberOfDelinquentPayments))
	fpdf.Ln(-1)

	fpdf.Cell(1, 0.2, "Calculated Delinquent Payments")
	fpdf.SetX(2.5)
	printCurrency(gapClaim.PaymentAmount.Mul(decimal.NewFromFloat(float64(gapClaim.NumberOfDelinquentPayments))))
	fpdf.Ln(-1)

	printFlag(gapClaim.HasTotalDelinquentPaymentsCovered)
	fpdf.Cell(1, 0.2, "Total Delinquent Payments Covered")
	fpdf.SetX(2.5)
	printCurrency(gapClaim.TotalDelinquentPaymentsCovered)
	fpdf.Ln(-1)

	printFlag(gapClaim.HasNumberOfExtensions)
	fpdf.Cell(1, 0.2, "Number of Extensions")
	fpdf.SetX(2.5)
	fpdf.Cell(1, 0.2, strconv.Itoa(gapClaim.NumberOfExtensions))
	fpdf.Ln(-1)

	fpdf.Cell(1, 0.2, "Calculated Extensions")
	fpdf.SetX(2.5)
	printCurrency(gapClaim.PaymentAmount.Mul(decimal.NewFromFloat(float64(gapClaim.NumberOfExtensions))))
	fpdf.Ln(-1)

	printFlag(gapClaim.HasTotalExtensionsCovered)
	fpdf.Cell(1, 0.2, "Total Extensions Covered")
	fpdf.SetX(2.5)
	printCurrency(gapClaim.TotalExtensionsCovered)
	fpdf.Ln(-1)

	printFlag(gapClaim.HasNegativeEquityAmount)
	fpdf.Cell(1, 0.2, "Negative equity amount:")
	printCurrency(gapClaim.NegativeEquityAmount)
	fpdf.Ln(-1)

	printFlag(gapClaim.HasSettlementAmount)
	fpdf.Cell(1, 0.2, "Settlement amount:")
	printCurrency(gapClaim.SettlementAmount)
	fpdf.Ln(-1)

	printFlag(gapClaim.HasInsuranceCompany)
	fpdf.Cell(1, 0.2, "Insurance check amount:")
	printCurrency(gapClaim.InsuranceCheckAmount)
	fpdf.Ln(-1)

	printFlag(gapClaim.IsValuationReportAvailable)
	fpdf.Cell(1, 0.2, "Valuation report:")
	printCurrency(gapClaim.ValuationReportAdjustments)
	fpdf.Ln(-1)

	printFlag(gapClaim.HasNADA)
	fpdf.Cell(1, 0.2, "NADA Value:")
	printCurrency(gapClaim.NADA)
	fpdf.Ln(-1)

	fpdf.SetX(1.0)
	printFlag(gapClaim.HasValuationNadaDifference)
	fpdf.Cell(1, 0.2, "Difference:")
	printCurrency(gapClaim.NADA.Sub(gapClaim.ValuationReportAdjustments))
	fpdf.Ln(-1)

	fpdf.SetX(1.0)
	printFlag(gapClaim.IsValuationReportMatchesBaseValue)
	fpdf.Cell(1, 0.2, "Matches base value:")
	printCurrency(gapClaim.ValuationReportMatchesBaseValue)
	fpdf.Ln(-1)

	fpdf.SetX(1.0)
	printFlag(gapClaim.ValuationReportVinMatches)
	fpdf.Cell(1, 0.2, "VIN matches:")
	fpdf.SetX(2.5)
	fpdf.Cell(1, 0.2, gapClaim.VIN)
	fpdf.Ln(-1)

	fpdf.SetX(1.0)
	printFlag(gapClaim.HasValuationReportPriorDamage)
	fpdf.Cell(1, 0.2, "Prior damage:")
	printCurrency(gapClaim.ValuationReportPriorDamageValue)
	fpdf.Ln(-1)

	fpdf.SetX(1.0)
	printFlag(gapClaim.HasValuationReportMiscFee)
	fpdf.Cell(1, 0.2, "Misc fee:")
	printCurrency(gapClaim.ValuationReportMiscFeeValue)
	fpdf.Ln(-1)

	fpdf.SetX(1.0)
	printFlag(gapClaim.HasMileageDeduction)
	fpdf.Cell(1, 0.2, "Mileage Deduction:")
	printCurrency(gapClaim.MileageDeduction)
	fpdf.Ln(-1)

	fpdf.SetX(1.0)
	fpdf.Cell(1, 0.2, "Mileage:")

	fpdf.Cell(1, 0.2, strconv.Itoa(gapClaim.ValuationReportMileage))
	fpdf.Ln(-1)

	fpdf.SetX(1.0)
	fpdf.Cell(1, 0.2, "DOL:")
	fpdf.Cell(1, 0.2, gapClaim.DateOfLoss.Format(dateFormat))
	fpdf.Ln(-1)

	fpdf.SetX(1.0)
	fpdf.Cell(1, 0.2, "Type:")
	fpdf.Cell(1, 0.2, gapClaim.ValuationReportType)
	fpdf.Ln(-1)

	printFlag(gapClaim.HasEstimateWithPhotos)
	fpdf.Cell(1, 0.2, "Estimate with photos:")
	fpdf.Ln(-1)

	printFlag(gapClaim.HasMSRPValue)
	fpdf.Cell(1, 0.2, "MSRP:")
	printCurrency(gapClaim.MSRPValue)
	fpdf.Ln(-1)

	printFlag(gapClaim.HasOriginalFinancingContract)
	fpdf.Cell(1, 0.2, "Original financing contract:")
	printCurrency(gapClaim.OriginalFinancingContractValue)
	fpdf.Ln(-1)

	if gapClaim.Over150Percent.LessThan(decimal.Zero) {
		fpdf.Cell(1, 0.2, "Over 150%:")
		printCurrency(gapClaim.Over150Percent)
		fpdf.Ln(-1)
	}

	fpdf.SetX(1.0)
	printFlag(gapClaim.ContractNumberMatches)
	fpdf.Cell(1, 0.2, "Contract# matches")
	fpdf.Ln(-1)

	fpdf.SetX(1.0)
	printFlag(gapClaim.BankHistoryMatches)
	fpdf.Cell(1, 0.2, "Bank history matches")
	fpdf.Ln(-1)

	printFlag(gapClaim.IsPoliceReportAvailable)
	fpdf.Cell(1, 0.2, "Police report:")
	fpdf.Ln(-1)

	printFlag(gapClaim.HasInsurancePolicyDeductible)
	fpdf.Cell(1, 0.2, "Insurance policy deductible:")
	fpdf.SetX(2.5)
	fpdf.CellFormat(1, 0.2, "$ + "+gapClaim.InsurancePolicyDeductibleValueAddition.StringFixed(2), "", 0, "R", false, 0, "")
	fpdf.Ln(-1)

	reason := "Add reason"
	if gapClaim.InsurancePolicyDeductibleReason != "" {
		reason = gapClaim.InsurancePolicyDeductibleReason
	}
	fpdf.Cell(1, 0.2, reason)
	fpdf.SetX(2.5)
	fpdf.CellFormat(1, 0.2, "$ - "+gapClaim.InsurancePolicyDeductibleValueSubtraction.StringFixed(2), "", 0, "R", false, 0, "")
	fpdf.Ln(-1)

	printFlag(gapClaim.HasBankInformation)
	fpdf.Cell(1, 0.2, "Bank information:")
	fpdf.SetX(2.5)
	fpdf.Cell(1, 0.2, gapClaim.BankAccountName)
	fpdf.Ln(-1)
	fpdf.SetX(2.5)
	fpdf.Cell(1, 0.2, gapClaim.BankAddressStreet1)
	fpdf.Ln(-1)
	if gapClaim.BankAddressStreet2 != "" {
		fpdf.SetX(2.5)
		fpdf.Cell(1, 0.2, gapClaim.BankAddressStreet2)
		fpdf.Ln(-1)
	}
	fpdf.SetX(2.5)
	fpdf.Cell(1, 0.2, gapClaim.BankAddressCity)
	fpdf.Ln(-1)
	fpdf.SetX(2.5)
	fpdf.Cell(1, 0.2, gapClaim.BankAddressState+" "+gapClaim.BankAddressZip)
	fpdf.Ln(-1)

	fpdf.SetX(1)
	fpdf.Cell(1, 0.2, "Vendor number:")
	fpdf.SetX(2.5)
	fpdf.Cell(1, 0.2, gapClaim.BankVendorID)
	fpdf.Ln(-1)

	fpdf.SetX(1)
	printFlag(gapClaim.BankHistoryMatches)
	fpdf.Cell(1, 0.2, "Full loan history")
	fpdf.SetX(2.5)
	fpdf.Ln(-1)

	fpdf.SetX(1)
	fpdf.Cell(1, 0.2, "Loan number:")
	fpdf.SetX(2.5)
	fpdf.Cell(1, 0.2, gapClaim.BankAccountNumber)
	fpdf.Ln(-1)

	fpdf.SetX(1)
	fpdf.Cell(1, 0.2, "Deal date:")
	fpdf.SetX(2.5)
	fpdf.Cell(1, 0.2, gapClaim.ContractDealDate.Format(dateFormat))
	fpdf.Ln(-1)

	fpdf.SetX(1)
	fpdf.Cell(1, 0.2, "Term:")
	fpdf.SetX(2.5)
	fpdf.Cell(1, 0.2, strconv.Itoa(gapClaim.ContractTermMonths))
	fpdf.Ln(-1)

	fpdf.SetX(1)
	fpdf.Cell(1, 0.2, "Payment amount:")
	fpdf.SetX(2.5)
	fpdf.Cell(1, 0.2, "$ "+gapClaim.PaymentAmount.StringFixed(2))
	fpdf.Ln(-1)

	fpdf.SetX(1)
	fpdf.Cell(1, 0.2, "Interest rate:")
	fpdf.SetX(2.5)
	fpdf.Cell(1, 0.2, gapClaim.InterestRate.StringFixed(2))
	fpdf.Ln(-1)

	fpdf.SetX(1)
	fpdf.Cell(1, 0.2, "First payment date:")
	fpdf.SetX(2.5)
	fpdf.Cell(1, 0.2, gapClaim.FirstPaymentDate.Format(dateFormat))
	fpdf.Ln(-1)

	fpdf.Cell(1, 0.2, "Status:")
	fpdf.SetX(2.5)
	fpdf.Cell(1, 0.2, gapStatusMap[gapClaim.Status])
	fpdf.Ln(-1)

	printFlag(gapClaim.HasRecovery)
	fpdf.Cell(1, 0.2, "Recovery Review:")
	fpdf.Ln(-1)

	reco, _ := recoveryAddedRemovedInfo(gapClaim.ID)

	if !reco.RecoveryAdded.IsZero() || reco.RecoveryRemoved.IsZero() {
		if !reco.RecoveryAdded.IsZero() {
			fpdf.Cell(1, 0.2, "Added:")
			fpdf.SetX(2.5)
			fpdf.Cell(1, 0.2, reco.RecoveryAdded.Format(dateFormat))
			fpdf.Ln(-1)
		} else if !reco.RecoveryRemoved.IsZero() {
			fpdf.Cell(1, 0.2, "Removed:")
			fpdf.SetX(2.5)
			fpdf.Cell(1, 0.2, reco.RecoveryRemoved.Format(dateFormat))
			fpdf.Ln(-1)
		}
		fpdf.Cell(1, 0.2, "Recovery Status:")
		fpdf.SetX(2.5)
		fpdf.Cell(1, 0.2, recoveryStatusMap[reco.Status])
		fpdf.Ln(-1)
	}
	if reco.Status == db.GapClaimRecoveryStatusRecovered {
		fpdf.Cell(1, 0.2, "Recovery Check:")
		fpdf.SetX(2.5)
		fpdf.Cell(1, 0.2, "$ "+reco.CheckAmount.StringFixed(2))
		fpdf.Ln(-1)
	}

	if gapClaimPayment != nil {
		fpdf.SetX(1)
		fpdf.Cell(1, 0.2, "Authorization #:")
		fpdf.SetX(2.5)
		fpdf.Cell(1, 0.2, strconv.Itoa(gapClaimPayment.AuthorizationNumber))
		fpdf.Ln(-1)
		if gapClaimPayment.CheckNumber > 0 {
			fpdf.SetX(1)
			fpdf.Cell(1, 0.2, "Check #:")
			fpdf.SetX(2.5)
			fpdf.Cell(1, 0.2, strconv.Itoa(gapClaimPayment.CheckNumber))
			fpdf.Ln(-1)
			fpdf.SetX(1)
			fpdf.Cell(1, 0.2, "Amount:")
			fpdf.SetX(2.5)
			var amount string
			if gapClaimPayment.Amount.Valid {
				amount = gapClaimPayment.Amount.Decimal.StringFixed(2)
			}
			fpdf.Cell(1, 0.2, "$ "+amount)
			fpdf.Ln(-1)

			fpdf.SetX(1)
			fpdf.Cell(1, 0.2, "Paid date:")
			fpdf.SetX(2.5)
			var paidDate string
			if gapClaimPayment.PaidDate.Valid {
				paidDate = gapClaimPayment.PaidDate.Time.Format(dateFormat)
			}
			fpdf.Cell(1, 0.2, paidDate)
			fpdf.Ln(-1)
		}
	}

	for _, child := range gapClaim.ChildClaims {
		fpdf.Cell(1, 0.2, "Child claim")
		fpdf.Ln(-1)
		fpdf.SetX(2.5)
		fpdf.SetX(1)
		fpdf.Cell(1, 0.2, "Status")
		fpdf.SetX(2.5)
		fpdf.Cell(1, 0.2, gapStatusMap[child.Status])
		fpdf.Ln(-1)
		gapChildClaim := &gapClaimPayload{
			ID:     child.ID,
			Status: child.Status,
		}
		childPay, _ := getClaimPayment(gapChildClaim)
		if childPay != nil {
			fpdf.SetX(1)
			fpdf.Cell(1, 0.2, "Authorization #:")
			fpdf.SetX(2.5)
			fpdf.Cell(1, 0.2, strconv.Itoa(childPay.AuthorizationNumber))
			fpdf.Ln(-1)

			fpdf.SetX(1)
			fpdf.Cell(1, 0.2, "Check#:")
			fpdf.SetX(2.5)
			fpdf.Cell(1, 0.2, strconv.Itoa(childPay.CheckNumber))
			fpdf.Ln(-1)

			fpdf.SetX(1)
			fpdf.Cell(1, 0.2, "Amount")
			fpdf.SetX(2.5)
			var amount string
			if childPay.Amount.Valid {
				amount = childPay.Amount.Decimal.StringFixed(2)
			}
			fpdf.Cell(1, 0.2, "$ "+amount)
			fpdf.Ln(-1)

			fpdf.SetX(1)
			fpdf.Cell(1, 0.2, "Paid Date")
			fpdf.SetX(2.5)
			var paidDate string
			if childPay.PaidDate.Valid {
				paidDate = childPay.PaidDate.Time.Format(dateFormat)
			}
			fpdf.Cell(1, 0.2, paidDate)

			fpdf.Ln(-1)

		} else {
			fpdf.SetX(1)
			fpdf.Cell(1, 0.2, "Amount")
			fpdf.SetX(2.5)
			fpdf.Cell(1, 0.2, "$ "+child.CaseReserve.StringFixed(2))
			fpdf.Ln(-1)
		}
		fpdf.SetX(1)
		fpdf.Cell(1, 0.2, "Reason")
		fpdf.SetX(2.5)
		fpdf.Cell(1, 0.2, child.ChildClaimReason)
		fpdf.Ln(-1)
		childPay = nil
	}
}

func getCaseReserveLabel(claim *gapClaimPayload) string {
	docList, _ := fieldDocuments(claim.ID) // ignoring the error as pdf should still be generated even if doclist not fetched
	if claim.Status == db.GapClaimStatusInquiry || len(docList) == 0 || claim.IsCSClaim == true {
		return "Reserve"
	} else if claim.SettlementAmount.Equal(decimal.Zero) || claim.RunAmortizationSheetValue.Equal(decimal.Zero) {
		return "Average"
	} else if claim.SettlementAmount.GreaterThan(decimal.Zero) && claim.RunAmortizationSheetValue.GreaterThan(decimal.Zero) && len(docList) > 0 {
		return "Reserve"
	}
	return "Average"
}
