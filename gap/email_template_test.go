package gap

import (
	"net/http"
	"net/http/httptest"
	"regexp"
	"strings"
	"testing"
	"time"

	"phizz/db"
	"phizz/handlers"

	sqlmock "github.com/DATA-DOG/go-sqlmock"
	"github.com/go-chi/chi"
	"github.com/jmoiron/sqlx"
)

func Test_emailTemplatePayloadFromReq(t *testing.T) {
	body := `{"name":"GAP Denial","template_text":"gap denial will be placed here in html format"}`
	req := httptest.NewRequest("POST", "/foo", strings.NewReader(body))

	emailTemplate := EmailTemplatePayload{}

	err := emailTemplateFromReq(&emailTemplate, req)
	if err != nil {
		t.Error("emailTemplateFromReq returned an error", err)
	}
	if emailTemplate.Name != "GAP Denial" {
		t.Error("didn't parse name from email-template payload")
	}
	if emailTemplate.TemplateText != "gap denial will be placed here in html format" {
		t.Error("didn't parse waiting_for from email-template payload")
	}
}

func TestEmailTemplateIndex(t *testing.T) {
	mdb, mock, err := sqlmock.New()
	if err != nil {
		t.Fatal(err)
	}
	dbx := sqlx.NewDb(mdb, "sqlmock")
	db.DB = dbx
	q := regexp.QuoteMeta

	mock.ExpectQuery(`^select id, name, template_type, language from email_templates where deleted_at is null$`).
		WillReturnRows(sqlmock.NewRows([]string{"id", "name", "template_type", "language"}).
			AddRow(1, "GAP Ack", "GAP", "English"))

	mock.ExpectQuery(q(`select email_template_updates.updated_at, updated_by_user_id, first_name, last_name from email_template_updates, users where email_template_id = $1 and updated_by_user_id= users.id order by updated_at desc limit 1`)).
		WithArgs(1).
		WillReturnRows(sqlmock.NewRows([]string{"updated_at", "updated_by_user_id", "first_name", "last_name"}).AddRow(time.Time{}, 5103, "James", "Bond"))

	mock.ExpectQuery(q(`select count(*) from email_templates where deleted_at is null`)).WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))

	req, err := http.NewRequest("GET", "/api/email-templates", nil)
	if err != nil {
		t.Fatal(err)
	}

	rr := httptest.NewRecorder()
	handler := http.HandlerFunc(handlers.APIHandler(EmailTemplateIndex))

	r := chi.NewRouter()
	r.HandleFunc("/api/email-templates", handler)
	r.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v",
			status, http.StatusOK)
	}

	expected := `{"count":1,"email_templates":[{"id":1,"name":"GAP Ack","template_text":"","template_type":"GAP","language":"English","updated_at":"0001-01-01T00:00:00Z","updated_by_user_id":5103,"updated_by_user_name":"James Bond"}]}`
	if rr.Body.String() != expected {
		t.Errorf("handler returned unexpected body: got %v want %v",
			rr.Body.String(), expected)
	}

	if err := mock.ExpectationsWereMet(); err != nil {
		t.Errorf("there were unfulfilled expectations: %s", err)
	}
}

func TestEmailTemplateIndexQueryStr(t *testing.T) {
	mdb, mock, err := sqlmock.New()
	if err != nil {
		t.Fatal(err)
	}
	dbx := sqlx.NewDb(mdb, "sqlmock")
	db.DB = dbx
	q := regexp.QuoteMeta

	mock.ExpectQuery(`^select id, name, template_type, language from email_templates where deleted_at is null and language = 'English' and template_type = 'GAP' order by position asc, name desc$`).
		WillReturnRows(sqlmock.NewRows([]string{"id", "name", "template_type", "language"}).
			AddRow(1, "GAP Ack", "GAP", "English"))

	mock.ExpectQuery(q(`select email_template_updates.updated_at, updated_by_user_id, first_name, last_name from email_template_updates, users where email_template_id = $1 and updated_by_user_id= users.id order by updated_at desc limit 1`)).
		WithArgs(1).
		WillReturnRows(sqlmock.NewRows([]string{"updated_at", "updated_by_user_id", "first_name", "last_name"}).AddRow(time.Time{}, 5103, "James", "Bond"))

	mock.ExpectQuery(q(`select count(*) from email_templates where deleted_at is null`)).WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))

	req, err := http.NewRequest("GET", "/api/email-templates?language=English&template_type=GAP&sort_by_name=desc", nil)
	if err != nil {
		t.Fatal(err)
	}

	rr := httptest.NewRecorder()
	handler := http.HandlerFunc(handlers.APIHandler(EmailTemplateIndex))

	r := chi.NewRouter()
	r.HandleFunc("/api/email-templates", handler)
	r.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v",
			status, http.StatusOK)
	}

	expected := `{"count":1,"email_templates":[{"id":1,"name":"GAP Ack","template_text":"","template_type":"GAP","language":"English","updated_at":"0001-01-01T00:00:00Z","updated_by_user_id":5103,"updated_by_user_name":"James Bond"}]}`
	if rr.Body.String() != expected {
		t.Errorf("handler returned unexpected body: got %v want %v",
			rr.Body.String(), expected)
	}

	if err := mock.ExpectationsWereMet(); err != nil {
		t.Errorf("there were unfulfilled expectations: %s", err)
	}
}
