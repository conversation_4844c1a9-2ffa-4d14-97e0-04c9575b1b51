package gap

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/json"
	"encoding/xml"
	"fmt"
	"log"
	"net/http"
	"strconv"
	"strings"
	"time"

	"phizz/conf"
	"phizz/db"
	"phizz/handlers"
	"phizz/intacct"

	"github.com/go-chi/chi"
	"github.com/jmoiron/sqlx"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
	"gopkg.in/guregu/null.v3"
)

type gapClaimPaymentPayload struct {
	ID                  int                 `json:"id" db:"id"`
	AuthorizationNumber int                 `json:"authorization_number" db:"authorization_number"`
	GapClaimID          int                 `json:"gap_claim_id" db:"gap_claim_id"`
	CheckNumber         int                 `json:"check_number" db:"check_number"`
	Amount              decimal.NullDecimal `json:"amount" db:"amount"`
	PaidDate            null.Time           `json:"paid_date" db:"paid_date"`
	BatchKey            int                 `json:"batch_key" db:"batch_key"`
	BillKey             int                 `json:"bill_key" db:"bill_key"`
	BillMemo            string              `json:"bill_memo" db:"bill_memo"`
}

type intacctError struct {
	ErrorMessage string
	Correction   string
}

func (e *intacctError) Error() string {
	return fmt.Sprintf("Error: %s, Correction: %s", e.ErrorMessage, e.Correction)
}

// submitToIntacct function submits the gap claim to the intacct ap payment service
// This function creates a batch key for the given day if it does not exist already
// then using this batch key and other details, it creates a bill in intacct system
// This bill will be used by finance team to create a payment request from intacct WebUI
func submitToIntacct(ctx context.Context, tx *sqlx.Tx, gapClaim *gapClaimPayload, billNumber int) (*gapClaimPaymentPayload, error) {
	gapClaimPayment := gapClaimPaymentPayload{}
	vendorID, err := handlers.GetVendorID(ctx, gapClaim.BankAccountName, gapClaim.BankAddressZip, gapClaim.BankAddressStreet1)
	if err != nil {
		return nil, errors.Wrap(err, "Failed to get vendorID from intacct")
	}

	intacctResult := struct {
		AuthStatus   string `xml:"operation>authentication>status"`
		ResultStatus string `xml:"operation>result>status"`
		Key          int    `xml:"operation>result>key"`
		Error        []struct {
			ErrorMessage string `xml:"description2"`
			Correction   string `xml:"correction"`
		} `xml:"operation>result>errormessage>error"`
	}{}

	// Intacct uses Pacific timezone in billbatch, it's important to use the same timezone to create the bill batch
	// If we use UTC, there is difference of 8 hours, Hence at 4 PM- 02/27/17 Pacific time, the UTC time will be 00:00 Hrs
	// 02/28/17. So batch title in our system will be 02/28/17, which was generated on 02/27/17 Pacific time in Intacct
	// On next day, our system won't generate new batch as the date is still 02/28/17 in UTC. And Intacct rejects this key
	// because it was generated on 02/27/17 and a batchkey is valid only for that day
	// To avoid this issue, the timezone to calculate the date of batch should be what Intacct is using
	// we are using LoadLocation to take care of DST changes

	// loading this way the program won't start if the needed time zone data isn't available
	var loc = handlers.LoadLocOrPanic("America/Los_Angeles")
	currentDate := time.Now().In(loc)

	batchTitle := strings.Join(
		[]string{strconv.Itoa(int(currentDate.Month())), strconv.Itoa(currentDate.Day()), strconv.Itoa(currentDate.Year())}, "/")

	gapClaimPayment.BatchKey, err = getBatchKey(batchTitle)
	if err != nil {
		return nil, errors.Wrap(err, "Getting batch key failed")
	}

	if gapClaimPayment.BatchKey == 0 {
		batchKeyXML, err := intacct.CreateBillBatchRequest(batchTitle)
		if err != nil {
			return nil, errors.Wrap(err, "Creating batch request failed")
		}

		batchKeyResponse, err := intacct.Request(ctx, batchKeyXML)
		if err != nil {
			return nil, errors.Wrap(err, "Intacct Server communication error")
		}

		batchResBuf := bytes.NewBuffer(batchKeyResponse)
		err = xml.NewDecoder(batchResBuf).Decode(&intacctResult)
		if err != nil {
			return nil, errors.Wrap(err, "Failed to docode batch key response")
		}

		if intacctResult.ResultStatus != intacct.ResultSuccess {
			fmt.Printf("INTACCT_ERROR - batchTitle: %s, response: %s\n", batchTitle, string(batchKeyResponse))
			return nil, errors.New("Failed to create bill batch")
		}

		gapClaimPayment.BatchKey = intacctResult.Key
		err = storeBatchKey(tx, batchTitle, gapClaimPayment.BatchKey)
		if err != nil {
			return nil, errors.Wrap(err, "Storing batch key failed")
		}
	}

	gapClaimPayment.BillMemo = gapClaim.ContractNumber + " " + gapClaim.LastName + "," + gapClaim.FirstName
	description := "GAP claim for certificate GAP Contract #" + gapClaim.ContractNumber +
		" ACCT #" + gapClaim.BankAccountNumber + ", " + gapClaim.LastName + "," + gapClaim.FirstName
	if gapClaim.IsChild {
		gapClaimPayment.BillMemo = "Child GAP claim " + gapClaimPayment.BillMemo
		description = "Child " + description
	}
	billDetails := intacct.Bill{
		VendorID:     vendorID,
		AccountLabel: conf.Get().IntacctGap.AccountLabel,
		Amount:       gapClaim.CaseReserve,
		// Memo text format : Gap Contract Number LastName,FirstName
		Memo:       gapClaimPayment.BillMemo,
		LocationID: conf.Get().IntacctGap.LocationID,
		TotalPaid:  decimal.NewFromFloat(0),
		TotalDue:   gapClaim.CaseReserve,
		ProjectID:  conf.Get().IntacctGap.ProjectID,
		TermName:   conf.Get().IntacctGap.TermName,
		BatchKey:   gapClaimPayment.BatchKey,
		BillNo:     strconv.Itoa(billNumber),
		LoanNumber: gapClaim.BankAccountNumber,
		// Description text format : Gap claim for certificate "GAP Contract #" Acct # "Account Number", Last Name, First Name
		Description: description,
	}

	billXML, err := intacct.CreateBillRequest(billDetails)
	if err != nil {
		return nil, errors.Wrap(err, "Creating bill request failed")
	}

	billResponse, err := intacct.Request(ctx, billXML)
	if err != nil {
		return nil, errors.Wrap(err, "Intacct Server communication error")
	}

	billResBuf := bytes.NewBuffer(billResponse)
	err = xml.NewDecoder(billResBuf).Decode(&intacctResult)
	if err != nil {
		return nil, errors.Wrap(err, "Failed to decode create bill response")
	}

	if intacctResult.ResultStatus != intacct.ResultSuccess {
		fmt.Printf("BatchKey: %d, INTACCT_RESPONSE: %s\n", gapClaimPayment.BatchKey, billResBuf)

		// If error is provided in response then return that
		if len(intacctResult.Error) > 0 {
			return nil, &intacctError{ErrorMessage: intacctResult.Error[0].ErrorMessage, Correction: intacctResult.Error[0].Correction}
		}
		return nil, errors.Wrap(err, "failed response status received from intacct")
	}
	gapClaimPayment.BillKey = intacctResult.Key

	return &gapClaimPayment, nil
}

func getBatchKey(batchTitle string) (int, error) {
	batchKey := 0
	err := db.Get().Get(&batchKey, `select batch_key from intacct_bill_batches where batch_title = $1`, batchTitle)
	if err != nil {
		if err == sql.ErrNoRows {
			return batchKey, nil
		}
		return batchKey, errors.Wrap(err, "Error loading intacct batch data")
	}
	return batchKey, nil
}

func storeBatchKey(tx *sqlx.Tx, batchTitle string, batchKey int) error {
	batchInsert := `insert into intacct_bill_batches(batch_title, batch_key) values($1,$2)returning id`
	id := 0
	err := tx.Get(&id, batchInsert, batchTitle, batchKey)
	if err != nil {
		return errors.Wrap(err, "Error inserting batchkey in database")
	}
	return nil
}

func getPaymentKey(ctx context.Context, billKey int, amount decimal.Decimal) (int, error) {
	billKeyStr := strconv.Itoa(billKey)
	fields := "PAYMENTKEY, AMOUNT"
	query := "RECORDKEY = " + billKeyStr
	requestXML, err := intacct.GetReadByQueryXML(query, "apbillpayment", fields)
	if err != nil {
		return 0, errors.Wrap(err, "Creating XML request for getting apbillpayment failed")
	}

	response, err := intacct.Request(ctx, requestXML)
	if err != nil {
		return 0, errors.Wrap(err, "Intacct Server communication error")
	}

	type apBillPaymentType struct {
		PaymentKey int             `xml:"PAYMENTKEY"`
		Amount     decimal.Decimal `xml:"AMOUNT"`
	}
	type data struct {
		Count         int                 `xml:"count,attr"`
		ApBillPayment []apBillPaymentType `xml:"apbillpayment"`
	}
	type result struct {
		Status string `xml:"status"`
		Data   data   `xml:"data"`
	}
	type operation struct {
		Result result `xml:"result"`
	}
	resApPayment := struct {
		Operation operation `xml:"operation"`
	}{}

	apResBuf := bytes.NewBuffer(response)
	err = xml.NewDecoder(apResBuf).Decode(&resApPayment)
	if err != nil {
		return 0, errors.Wrap(err, "Failed to decode getPaymentKey response")
	}

	if resApPayment.Operation.Result.Status != intacct.ResultSuccess {
		return 0, errors.New("Failed to get appayment from Intacct")
	}
	var apBillPayment apBillPaymentType
	if resApPayment.Operation.Result.Data.Count == 0 {
		return 0, errors.Wrapf(errors.New("APBillPayment Not found"),
			"ApPayment with bill %d not found in intacct system", billKey)
	} else if resApPayment.Operation.Result.Data.Count == 1 {
		apBillPayment = resApPayment.Operation.Result.Data.ApBillPayment[0]
	} else if resApPayment.Operation.Result.Data.Count > 1 {
		for _, apBillPayment = range resApPayment.Operation.Result.Data.ApBillPayment {
			if apBillPayment.Amount.Equals(amount) {
				break
			}
		}
	}

	if apBillPayment.PaymentKey == 0 {
		return 0, errors.Wrapf(intacct.ErrPaymentNotComplete, "Payment not complete for bill %d", billKey)
	}
	return apBillPayment.PaymentKey, nil
}

func getPaymentDetailsFromIntacct(ctx context.Context, billKey int, caseReserve decimal.Decimal) (*gapClaimPaymentPayload, error) {
	paymentKey, err := getPaymentKey(ctx, billKey, caseReserve)
	if err != nil {
		return nil, errors.Wrap(err, "Could not get payment key")
	}
	fields := "STATE, TRX_TOTALPAID, DOCUMENTNUMBER, PAYMENTDATE"
	query := " RECORDNO = " + strconv.Itoa(paymentKey)
	requestXML, err := intacct.GetReadByQueryXML(query, "appayment", fields)
	if err != nil {
		return nil, errors.Wrap(err, "Creating XML request for getting payment details failed")
	}

	response, err := intacct.Request(ctx, requestXML)
	if err != nil {
		return nil, errors.Wrap(err, "Intacct Server communication error")
	}

	type apPaymentType struct {
		State          string          `xml:"STATE"`
		TrxTotalPaid   decimal.Decimal `xml:"TRX_TOTALPAID"`
		DocumentNumber string          `xml:"DOCUMENTNUMBER"`
		PaymentDate    string          `xml:"PAYMENTDATE"`
	}
	type data struct {
		Count     int             `xml:"count,attr"`
		ApPayment []apPaymentType `xml:"appayment"`
	}
	type result struct {
		Status string `xml:"status"`
		Data   data   `xml:"data"`
	}
	type operation struct {
		Result result `xml:"result"`
	}
	resApPayment := struct {
		Operation operation `xml:"operation"`
	}{}

	apResBuf := bytes.NewBuffer(response)
	err = xml.NewDecoder(apResBuf).Decode(&resApPayment)
	if err != nil {
		return nil, errors.Wrap(err, "Failed to decode getPaymentDetailsFromIntacct response")
	}

	if resApPayment.Operation.Result.Status != intacct.ResultSuccess {
		return nil, errors.New("Failed to get appayment from Intacct")
	}
	var apPayment apPaymentType
	if resApPayment.Operation.Result.Data.Count == 0 {
		return nil, errors.Wrapf(errors.New("APPayment Not found"),
			"ApPayment with memo %d not found in intacct system", billKey)
	} else if resApPayment.Operation.Result.Data.Count == 1 {
		apPayment = resApPayment.Operation.Result.Data.ApPayment[0]
	}

	if apPayment.State != intacct.PaymentStateComplete {
		return nil, errors.Wrapf(intacct.ErrPaymentNotComplete, "Payment not complete for memo %d", billKey)
	}

	gapClaimPayment := gapClaimPaymentPayload{}
	gapClaimPayment.CheckNumber, err = strconv.Atoi(apPayment.DocumentNumber)
	if err != nil {
		return nil, errors.Wrapf(err, "Error converting documentNumber for %d", billKey)
	}
	gapClaimPayment.PaidDate.Time = intacct.ToTime(apPayment.PaymentDate)
	gapClaimPayment.PaidDate.Valid = true
	gapClaimPayment.Amount.Decimal = apPayment.TrxTotalPaid
	gapClaimPayment.Amount.Valid = true
	return &gapClaimPayment, nil
}

type paymentInfo struct {
	ID                  int             `db:"id"`
	GapClaimID          int             `db:"gap_claim_id"`
	BillMemo            string          `db:"bill_memo"`
	DateOfLoss          time.Time       `db:"date_of_loss"`
	DateOfClaimReceived time.Time       `db:"date_of_claim_received"`
	CaseReserve         decimal.Decimal `db:"case_reserve"`
	BillKey             int             `db:"bill_key"`
	AuthorizationNumber int             `db:"authorization_number"`
	ContractNumber      string          `db:"contract_number"`
	CheckNumber         int             `db:"check_number"`
	Amount              decimal.Decimal `db:"amount"`
	PaidDate            time.Time       `db:"paid_date"`
}

// ReceiveFromIntacct function receives the gap claim paid info from intacct ap payment service
// Once the bill is generated using SubmitToIntacct function and finance team has done
// a. creating of payment request b. approving of the payment request from intacct WebUI
// The following function can fetch the check details from intacct system
// The function will update the check details in gap_claim_payments table, if only the appayment
// request is found in intacct and has a status 'Complete'
// This function is invoked by cmd/apreceive binary, which is scheduled to run as a cronjob on server
func ReceiveFromIntacct(ctx context.Context) (int, error) {
	pendingPayments := []paymentInfo{}
	query := `
	select
		gc.date_of_loss,
		gc.date_of_claim_received,
		gc.contract_number, gc.case_reserve,
        gcp.bill_key,
		gcp.authorization_number,
		gcp.id, gcp.gap_claim_id,
		gcp.bill_memo
        from gap_claims gc
			join gap_claim_payments gcp
            on gc.id = gcp.gap_claim_id
        where gcp.check_number = 0
			and gc.date_of_claim_received > now() - interval '365 days'`

	checkCount := 0
	err := db.Get().Select(&pendingPayments, query)
	if err != nil {
		if err == sql.ErrNoRows {
			return checkCount, nil
		}
		return checkCount, errors.Wrap(err, "Error loading GAP claim payment from database.")
	}

	for _, payment := range pendingPayments {
		gapClaimIntacct, err := getPaymentDetailsFromIntacct(ctx, payment.BillKey, payment.CaseReserve)
		if err != nil {
			log.Println("Error getting payment details for ", payment.BillMemo, err)
			continue
		}
		//update payments table with payment info and update claims table with status
		err = updateGapClaimPaidInformation(strconv.Itoa(payment.ID), strconv.Itoa(payment.GapClaimID), gapClaimIntacct)
		if err != nil {
			return checkCount, errors.Wrap(err, "Error updating paid info in gap_claim_payments")
		}
		// cancel contract for checkWritten claims
		note := "Claim is paid by Check# " + strconv.Itoa(gapClaimIntacct.CheckNumber) + "On Date:" + gapClaimIntacct.PaidDate.Time.String()
		handlers.ContractExpire(payment.ContractNumber, note)
		checkCount++
	}
	return checkCount, err
}

func updateGapClaimPaidInformation(id string, gapClaimID string, gapClaimPayment *gapClaimPaymentPayload) error {
	tx, err := db.Get().Beginx()
	if err != nil {
		return errors.Wrap(err, "Database error beginning transaction for GAP claim create")
	}

	query := `update gap_claim_payments set check_number = $1, amount = $2, paid_date = $3, updated_at = now() at time zone 'utc' where id = $4`
	_, err = tx.Exec(query, gapClaimPayment.CheckNumber, gapClaimPayment.Amount, gapClaimPayment.PaidDate, id)
	if err != nil {
		_ = tx.Rollback()
		return errors.Wrap(err, "Error updating GAP claim paid info")
	}

	updateGapClaimQuery := `update gap_claims set status = $1 where id = $2`
	_, err = tx.Exec(updateGapClaimQuery, db.GapClaimStatusCheckWritten, gapClaimID)
	if err != nil {
		_ = tx.Rollback()
		return errors.Wrap(err, "Error updating GAP claim paid info")
	}
	_, err = tx.Exec(`update gap_claims set gap_closed_at=now() at time zone 'utc' where id =$1`, gapClaimID)
	if err != nil {
		_ = tx.Rollback()
		return errors.Wrap(err, "Update of GAP closed at failed")
	}

	err = tx.Commit()
	return err
}

// first reverse the payment and then reverse the bill. It returns true in case if check is already voided in Intacct, else false.
func voidIntacctPayment(ctx context.Context, billKey int, caseReserve decimal.Decimal, reason string) (bool, error) {
	paymentKey, err := getPaymentKey(ctx, billKey, caseReserve)
	if err != nil {
		return false, errors.Wrap(err, "Could not get payment key")
	}

	var loc = handlers.LoadLocOrPanic("America/Los_Angeles")
	currentDate := time.Now().In(loc)

	// Reverse payment
	reversePaymentXML, err := intacct.GetReversePaymentXML(paymentKey, currentDate, reason)
	if err != nil {
		return false, errors.Wrap(err, "Creating XML request for reverse_payment failed")
	}

	response, err := intacct.Request(ctx, reversePaymentXML)
	if err != nil {
		return false, errors.Wrap(err, "Intacct Server communication error")
	}

	intacctResult := struct {
		AuthStatus   string `xml:"operation>authentication>status"`
		ResultStatus string `xml:"operation>result>status"`
		Key          int    `xml:"operation>result>key"`
		ErrorCode    string `xml:"operation>result>errormessage>error>errorno"`
	}{}

	apResBufReversePayment := bytes.NewBuffer(response)
	err = xml.NewDecoder(apResBufReversePayment).Decode(&intacctResult)
	if err != nil {
		return false, errors.Wrap(err, "Failed to decode reverse_payment response")
	}

	if intacctResult.ResultStatus != intacct.ResultSuccess {
		if intacctResult.ErrorCode == intacct.IntacctReversedClaimErrorCode {
			return true, errors.New("Already reversed in Intacct")
		}
		fmt.Printf("INTACCT_ERROR - paymentKey: %d, response: %s\n", paymentKey, string(response))
		return false, errors.New("Failed to reverse payment")
	}

	// Reverse bill
	requestXML, err := intacct.GetReverseBillXML(billKey, currentDate, reason)
	if err != nil {
		return false, errors.Wrap(err, "Creating XML request for reverse_bill failed")
	}

	response, err = intacct.Request(ctx, requestXML)
	if err != nil {
		return false, errors.Wrap(err, "Intacct Server communication error")
	}

	apResBufReverseBill := bytes.NewBuffer(response)
	err = xml.NewDecoder(apResBufReverseBill).Decode(&intacctResult)
	if err != nil {
		return false, errors.Wrap(err, "Failed to decode reverse_bill response")
	}

	if intacctResult.ResultStatus != intacct.ResultSuccess {
		if intacctResult.ErrorCode == intacct.IntacctReversedClaimErrorCode {
			return true, errors.New("Already reversed in Intacct")
		}
		fmt.Printf("INTACCT_ERROR - billKey: %d, response: %s\n", billKey, string(response))
		return false, errors.New("Failed to reverse bill")
	}

	return false, nil
}

// VerifyClaimVoid checks whether the particular claim is void in intacct
func VerifyClaimVoid(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	ctx := req.Context()
	claim := struct {
		ID             int    `json:"id" db:"id"`
		Status         string `json:"status" db:"status"`
		ContractNumber string `json:"-" db:"contract_number"`
	}{}

	err := db.Get().Get(&claim, `select id, status, contract_number from gap_claims where id=$1`, chi.URLParam(req, "id"))
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage(err, "The GAP claim was not found", nil)
		}
		handlers.ReportError(req, errors.Wrap(err, "Could not get claim data"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Could not get claim data", nil)
	}
	if claim.Status == db.GapClaimStatusCheckVoided {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Already voided", nil)
	}

	paymentInfo := struct {
		AuthorizationNumber int                 `db:"authorization_number"`
		CheckNumber         int                 `db:"check_number"`
		Amount              decimal.NullDecimal `db:"amount"`
		PaidDate            null.Time           `db:"paid_date"`
		BillKey             int                 `db:"bill_key"`
	}{}
	authQuery := "select authorization_number, check_number, amount, paid_date, bill_key from gap_claim_payments where gap_claim_id = $1 order by id desc limit 1"
	err = db.Get().Get(&paymentInfo, authQuery, claim.ID)
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage(err, "The GAP claim authorization was not found", nil)
		}
		handlers.ReportError(req, errors.Wrap(err, "Could not get claim payment data"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Could not get claim payment data", nil)
	}

	claimReason := req.FormValue("reason")

	if claimReason == "" {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Invalid request for void payment, reason is needed", nil)
	}

	reversedClaim, err := voidIntacctPayment(ctx, paymentInfo.BillKey, paymentInfo.Amount.Decimal, claimReason)
	if err != nil && !reversedClaim {
		handlers.ReportError(req, errors.Wrap(err, "Cannot reverse/void payment in Intacct for contract#:"+claim.ContractNumber))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Cannot reverse/void payment in Intacct for contract#: "+claim.ContractNumber, nil)
	}

	if reversedClaim {
		return http.StatusOK, map[string]interface{}{"checkNumber": paymentInfo.CheckNumber}
	}
	return http.StatusOK, map[string]interface{}{"checkNumber": -1}
}

// ClaimVoid voids the payment in intacct
func ClaimVoid(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	ctx := req.Context()
	claim := struct {
		ID             int    `json:"id" db:"id"`
		Status         string `json:"status" db:"status"`
		ContractNumber string `json:"-" db:"contract_number"`
	}{}
	err := db.Get().GetContext(ctx, &claim, `select id, status, contract_number from gap_claims where id=$1`, chi.URLParam(req, "id"))
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage(err, "The GAP claim was not found", nil)
		}
		handlers.ReportError(req, errors.Wrap(err, "could not get claim data"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Could not get claim data", nil)
	}
	if claim.Status == db.GapClaimStatusCheckVoided {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Already voided", nil)
	}

	paymentInfo := struct {
		AuthorizationNumber int                 `db:"authorization_number"`
		CheckNumber         int                 `db:"check_number"`
		Amount              decimal.NullDecimal `db:"amount"`
		PaidDate            null.Time           `db:"paid_date"`
		BillKey             int                 `db:"bill_key"`
	}{}
	authQuery := "select authorization_number, check_number, amount, paid_date, bill_key from gap_claim_payments where gap_claim_id = $1 order by paid_date desc limit 1"
	err = db.Get().GetContext(ctx, &paymentInfo, authQuery, claim.ID)
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage(err, "The GAP claim authorization was not found", nil)
		}
		handlers.ReportError(req, errors.Wrap(err, "could not get claim payment data"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Could not get claim payment data", nil)
	}

	voidClaim := struct {
		Reason string `json:"reason"`
	}{}
	dec := json.NewDecoder(req.Body)
	err = dec.Decode(&voidClaim)
	if err != nil || voidClaim.Reason == "" {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Invalid request for void payment, reason is needed", nil)
	}

	reversedClaim, err := voidIntacctPayment(ctx, paymentInfo.BillKey, paymentInfo.Amount.Decimal, voidClaim.Reason)
	if err != nil && !reversedClaim {
		handlers.ReportError(req, errors.Wrap(err, "cannot reverse/void payment in Intacct for contract#: "+claim.ContractNumber))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Cannot reverse/void payment in Intacct for contract#: "+claim.ContractNumber, nil)
	}

	// transaction
	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "database error beginning transaction for void payment"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error beginning transaction for void payment", nil)
	}

	// change status
	updateStatus := `update gap_claims set status = $1 where id = $2`
	_, err = tx.ExecContext(ctx, updateStatus, db.GapClaimStatusCheckVoided, claim.ID)
	if err != nil {
		_ = tx.Rollback()
		handlers.ReportError(req, errors.Wrap(err, "error updating GAP claim status"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error updating GAP claim status", nil)
	}

	// insert into gap_voided_transactions, the amount should be negative in this table hence multiplying by -1
	transactionQuery := `insert into gap_voided_transactions (date_of_void, gap_claim_id,check_amount,check_number) 
		values (now() at time zone 'utc',$1,$2,$3)`
	_, err = tx.ExecContext(ctx, transactionQuery, claim.ID, paymentInfo.Amount.Decimal.Mul(decimal.NewFromFloat(-1)), paymentInfo.CheckNumber)
	if err != nil {
		_ = tx.Rollback()
		handlers.ReportError(req, errors.Wrap(err, "error inserting the transaction for void payment"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error inserting the transaction for void payment", nil)
	}

	// insert note
	recordNote := `insert into record_notes (created_at,gap_claim_id,notes_text,created_by_user_id) values (now() at time zone 'utc',$1,$2,$3) returning id`
	paidDate := "NA"
	if paymentInfo.PaidDate.Valid {
		paidDate = paymentInfo.PaidDate.Time.Format("2006-01-02")
	}
	note := fmt.Sprintf("The check with Number: %d Amount: %s AuthNumber: %d Date: %s  is voided for reason '%s'",
		paymentInfo.CheckNumber, paymentInfo.Amount.Decimal.String(), paymentInfo.AuthorizationNumber, paidDate, voidClaim.Reason)
	recordNoteID := 0
	err = tx.Get(&recordNoteID, recordNote, claim.ID, note, user.ID)
	if err != nil {
		_ = tx.Rollback()
		handlers.ReportError(req, errors.Wrap(err, "error inserting the note for void payment"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error inserting the note for void payment", nil)
	}

	// commit
	err = tx.Commit()
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "database error committing transaction for void payment"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error committing transaction for void payment", nil)
	}
	return http.StatusOK, map[string]interface{}{"id": claim.ID}
}

func getClaimPayment(gapClaim *gapClaimPayload) (*gapClaimPaymentPayload, error) {

	var query string
	if gapClaim.Status == db.GapClaimStatusWaitingForCheck && !(gapClaim.IsCSClaim && !gapClaim.NotPaidByCS) {
		query = "select authorization_number from gap_claim_payments where gap_claim_id = $1 order by id desc limit 1"
	} else if gapClaim.Status == db.GapClaimStatusCheckWritten || gapClaim.Status == db.GapClaimStatusCheckVoided {
		query = "select authorization_number, check_number, amount, paid_date from gap_claim_payments where gap_claim_id = $1 order by id desc limit 1"
	} else {
		return nil, nil
	}
	gapClaimPayment := gapClaimPaymentPayload{}
	err := db.Get().Get(&gapClaimPayment, query, gapClaim.ID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return &gapClaimPayment, errors.Wrap(err, "error loading GAP claim payments from database.")
	}
	return &gapClaimPayment, nil
}
