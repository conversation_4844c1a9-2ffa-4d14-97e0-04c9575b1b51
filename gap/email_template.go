package gap

import (
	"database/sql"
	"encoding/base64"
	"encoding/json"
	"net/http"
	"strings"
	"time"

	"phizz/db"

	"phizz/handlers"

	"github.com/go-chi/chi"
	"github.com/pkg/errors"
)

// EmailTemplatePayload used for email template
type EmailTemplatePayload struct {
	ID                int       `json:"id" db:"id"`
	Name              string    `json:"name" db:"name"`
	TemplateText      string    `json:"template_text" db:"template_text"`
	TemplateType      string    `json:"template_type" db:"template_type"`
	Language          string    `json:"language" db:"language"`
	UpdatedAt         time.Time `json:"updated_at" db:"updated_at"`
	UpdatedByUserID   int       `json:"updated_by_user_id" db:"updated_by_user_id"`
	UpdatedByUserName string    `json:"updated_by_user_name" db:"updated_by_user_name"`
}

// EmailTemplateRequestType used for holding the template request type
type EmailTemplateRequestType int

type emailTemplateReorderPayload struct {
	TemplateIDs []int `db:"template_ids" json:"template_ids"`
}

func (payload emailTemplateReorderPayload) validate() (map[string]string, error) {
	validationErrs := map[string]string{}

	if payload.TemplateIDs == nil || len(payload.TemplateIDs) == 0 {
		validationErrs["template_ids"] = "Missing Templates"
	} else {
		for _, id := range payload.TemplateIDs {

			count := 0
			query := `select count(*) from email_templates where id = $1`
			err := db.Get().Get(&count, query, id)

			if err != nil {
				return nil, errors.Wrap(err, "Database error validating missing templates")
			}
			if count == 0 {
				validationErrs["template_ids"] = "Missing some templateIds. You may need to reset before re-ordering."
			}
		}
	}

	return validationErrs, nil
}

const (
	// EmailTemplateRequestCreate Template create request
	EmailTemplateRequestCreate EmailTemplateRequestType = iota
	// EmailTemplateRequestUpdate Template update request
	EmailTemplateRequestUpdate
)

// EmailTemplateIndex returns list of email-templates
func EmailTemplateIndex(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {

	emailTemplates := []EmailTemplatePayload{}

	wh := "" // where clause

	language := req.FormValue("language")
	if language == "English" || language == "Spanish" {
		wh += " language = '" + language + "'"
	}

	templateType := req.FormValue("template_type")

	if templateType != "" {
		if wh != "" {
			wh += " and "
		}
		wh += " template_type = '" + templateType + "'"
	}
	name := req.FormValue("name")
	if name != "" {
		if wh != "" {
			wh += " and "
		}
		wh += " name = $1 "
	}

	orderBy := ""

	if templateType == "GAP" {
		orderBy += " order by position asc"
	}

	sortByName := req.FormValue("sort_by_name")
	if sortByName == "desc" || sortByName == "asc" {
		if orderBy != "" {
			orderBy += ", name " + sortByName
		} else {
			orderBy += " order by name " + sortByName
		}
	}

	listQuery := `select id, name, template_type, language from email_templates
	where deleted_at is null`
	if wh != "" {
		listQuery += " and " + wh
	}
	listQuery += orderBy
	var err error
	if name == "" {
		err = db.Get().Select(&emailTemplates, listQuery)
	} else {
		err = db.Get().Select(&emailTemplates, listQuery, name)
	}
	if err != nil {
		err = errors.Wrap(err, "Database error getting email-template lists")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting email-template list data", nil)
	}

	lastUpdateQuery := `select email_template_updates.updated_at, updated_by_user_id, first_name, last_name from email_template_updates,
	users where email_template_id = $1 and updated_by_user_id= users.id order by updated_at desc limit 1`
	lastUpdate := struct {
		UpdatedAt       time.Time `db:"updated_at"`
		UpdatedByUserID int       `db:"updated_by_user_id"`
		FirstName       string    `db:"first_name"`
		LastName        string    `db:"last_name"`
	}{}
	for i, emailTemplate := range emailTemplates {
		err = db.Get().Get(&lastUpdate, lastUpdateQuery, emailTemplate.ID)
		if err != nil {
			if err == sql.ErrNoRows {
				return http.StatusNotFound, handlers.ErrorMessage(err, "Update for the email-template was not found", nil)
			}
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error loading email-template updates from database.", nil)
		}
		emailTemplates[i].UpdatedAt = lastUpdate.UpdatedAt
		emailTemplates[i].UpdatedByUserID = lastUpdate.UpdatedByUserID
		emailTemplates[i].UpdatedByUserName = strings.Join([]string{lastUpdate.FirstName, lastUpdate.LastName}, " ")
	}
	count := 0
	countQuery := `select count(*) from email_templates where deleted_at is null`
	if wh != "" {
		countQuery += " and " + wh
	}

	if name == "" {
		err = db.Get().Get(&count, countQuery)
	} else {
		err = db.Get().Get(&count, countQuery, name)
	}
	if err != nil {
		err = errors.Wrap(err, "Database error getting email-template lists count")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting email-template lists data", nil)
	}

	return http.StatusOK, map[string]interface{}{"email_templates": emailTemplates, "count": count}
}

// EmailTemplateCreate creates a new email-template
func EmailTemplateCreate(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	emailTemplate := EmailTemplatePayload{}
	err := emailTemplateFromReq(&emailTemplate, req)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(nil, "Malformed email-template data for create.", nil)
	}
	decodedString, err := base64.StdEncoding.DecodeString(emailTemplate.TemplateText)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error in decoding email-template text"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error in decoding email-template text", nil)
	}
	emailTemplate.TemplateText = string(decodedString)

	formErrors, err := validateEmailTemplate(&emailTemplate, EmailTemplateRequestCreate)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(errors.Wrap(err, "Error validating email-template"),
			"An error occurred validating the form values.", nil)
	}

	if len(formErrors) > 0 {
		return http.StatusBadRequest, handlers.ErrorMessage(nil, "Form validations errors.",
			map[string]interface{}{"errors": formErrors})
	}

	cleanEmailTemplate(&emailTemplate)

	//begin Transaction
	tx, err := db.Get().Beginx()
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error creating transaction in creating email-template", nil)
	}

	position := struct {
		Position int `db:"position"`
	}{}

	selectPosition := `select case when max(position) is null then 1 else max(position) + 1 end as position from email_templates`

	err = db.Get().Get(&position, selectPosition)

	if err != nil {
		_ = tx.Rollback()
		return http.StatusInternalServerError, handlers.ErrorMessage(errors.Wrap(err, "Error getting position for email-template"),
			"Error getting position for email-template", nil)
	}

	//add template
	insertQuery := `insert into email_templates(name, template_text, template_type, language, position) values($1, $2, $3, $4, $5) returning id`

	id := 0
	err = tx.Get(&id, insertQuery, emailTemplate.Name, emailTemplate.TemplateText, emailTemplate.TemplateType, emailTemplate.Language, position.Position)
	if err != nil {
		_ = tx.Rollback()
		return http.StatusInternalServerError, handlers.ErrorMessage(errors.Wrap(err, "Error creating email-template"),
			"Error creating email-template", nil)
	}

	//add template changelog
	changeLogQuery := `insert into email_template_updates(email_template_id, updated_by_user_id, updated_at) values ($1, $2, now() at time zone 'utc') returning id`
	changeID := 0
	err = tx.Get(&changeID, changeLogQuery, id, user.ID)
	if err != nil {
		_ = tx.Rollback()
		return http.StatusInternalServerError, handlers.ErrorMessage(errors.Wrap(err, "Error creating email-template"),
			"Error creating email-template", nil)
	}

	//commit
	err = tx.Commit()
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error creating email-template", nil)
	}

	return http.StatusOK, map[string]interface{}{"id": id}
}

// EmailTemplateShow shows email-template
func EmailTemplateShow(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {

	id := chi.URLParam(req, "id")
	// Get the email-template from database
	emailTemplate, err := EmailTemplateSelect(id)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting email-template from database", nil)
	}

	return http.StatusOK, map[string]interface{}{"email_template": emailTemplate}
}

// EmailTemplateSelect gets the email-template record from db
func EmailTemplateSelect(id string) (*EmailTemplatePayload, error) {
	query := "select * from email_templates where id = $1 and deleted_at is null limit 1"
	emailTemplate := EmailTemplatePayload{}
	err := db.Get().Unsafe().Get(&emailTemplate, query, id)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.Wrap(err, "The email-template was not found")
		}
		return nil, errors.Wrap(err, "Error loading email-template from database.")
	}

	lastUpdateQuery := `select email_template_updates.updated_at, updated_by_user_id, first_name, last_name from email_template_updates,
	users where email_template_id = $1 and updated_by_user_id= users.id order by updated_at desc limit 1`
	lastUpdate := struct {
		UpdatedAt       time.Time `db:"updated_at"`
		UpdatedByUserID int       `db:"updated_by_user_id"`
		FirstName       string    `db:"first_name"`
		LastName        string    `db:"last_name"`
	}{}
	err = db.Get().Get(&lastUpdate, lastUpdateQuery, id)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.Wrap(err, "Update for the email_template was not found")
		}
		return nil, errors.Wrap(err, "Error loading email templates updates from database.")
	}
	emailTemplate.UpdatedByUserID = lastUpdate.UpdatedByUserID
	emailTemplate.UpdatedAt = lastUpdate.UpdatedAt
	emailTemplate.UpdatedByUserName = strings.Join([]string{lastUpdate.FirstName, lastUpdate.LastName}, " ")
	return &emailTemplate, err
}

func emailTemplateFromReq(emailTemplate *EmailTemplatePayload, req *http.Request) error {
	dec := json.NewDecoder(req.Body)
	err := dec.Decode(&emailTemplate)
	return errors.Wrap(err, "decoding Email Template request failed")
}

// validateEmailTemplate validates a email-template record for correctness
func validateEmailTemplate(emailTemplate *EmailTemplatePayload, requestType EmailTemplateRequestType) (map[string]string, error) {
	formErrors := map[string]string{}
	switch requestType {
	case EmailTemplateRequestCreate:
		if emailTemplate.Name == "" {
			formErrors["name"] = "Name is required"
		}
		if emailTemplate.TemplateText == "" {
			formErrors["template_text"] = "TemplateText is required"
		}
		if emailTemplate.Language == "" || (emailTemplate.Language != "English" && emailTemplate.Language != "Spanish") {
			formErrors["language"] = "Language should be either English or Spanish"
		}
		if emailTemplate.TemplateType == "" {
			formErrors["template_type"] = "Template type should be GAP"
		}

		query := "select language from email_templates where lower(name)=lower($1) and deleted_at is null"
		languages := []string{}
		err := db.Get().Select(&languages, query, emailTemplate.Name)

		if err != nil {
			if err == sql.ErrNoRows {
				return formErrors, nil // there is no existing template with given name, it's not an error
			}
			return nil, errors.Wrap(err, "Error loading email-template from database.")
		}
		for _, language := range languages {
			if language == emailTemplate.Language {
				formErrors["name_language_combination"] = "Name language combination already exists"
			}
		}
	case EmailTemplateRequestUpdate:
		if emailTemplate.TemplateText == "" {
			formErrors["template_text"] = "TemplateText is required"
		}
	}
	return formErrors, nil
}

// EmailTemplateUpdate updates an email-template
func EmailTemplateUpdate(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	id := chi.URLParam(req, "id")

	// Get the email-template from database
	emailTemplate, err := EmailTemplateSelect(id)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting email-template from database", nil)
	}

	// using same object ensures, the object now has both fields, the ones need to modified and the ones need to
	// be retained
	err = emailTemplateFromReq(emailTemplate, req)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(nil, "Malformed email-template data for update.", nil)
	}

	decodedString, err := base64.StdEncoding.DecodeString(emailTemplate.TemplateText)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error in decoding email-template text"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error in decoding email-template text", nil)
	}
	emailTemplate.TemplateText = string(decodedString)

	formErrors, err := validateEmailTemplate(emailTemplate, EmailTemplateRequestUpdate)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(errors.Wrap(err, "Error validating email-template"),
			"An error occurred validating the form values.", nil)
	}
	if len(formErrors) > 0 {
		return http.StatusBadRequest, handlers.ErrorMessage(nil, "Form validations errors.",
			map[string]interface{}{"errors": formErrors})
	}

	userID := user.ID

	cleanEmailTemplate(emailTemplate)

	tx, err := db.Get().Beginx()
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error in creating transaction in update of email-template", nil)
	}
	query := `update email_templates set template_text = $1 where id = $2`
	_, err = tx.Exec(query, emailTemplate.TemplateText, emailTemplate.ID)
	if err != nil {
		_ = tx.Rollback()
		return http.StatusInternalServerError, handlers.ErrorMessage(errors.Wrap(err, "Error updating email-template"),
			"Error updating email-templates in database", nil)
	}

	//add template changelog
	changeLogQuery := `insert into email_template_updates(email_template_id, updated_by_user_id, updated_at) values ($1, $2, now() at time zone 'utc') returning id`
	changeID := 0
	err = tx.Get(&changeID, changeLogQuery, id, userID)
	if err != nil {
		_ = tx.Rollback()
		return http.StatusInternalServerError, handlers.ErrorMessage(errors.Wrap(err, "Error updating email-template-updates"),
			"Error updating email-template-updates", nil)
	}

	//commit
	err = tx.Commit()
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "error in email-template-updates", nil)
	}

	return http.StatusOK, map[string]interface{}{"id": id}
}

// EmailTemplateDelete deletes an email-template
func EmailTemplateDelete(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	id := chi.URLParam(req, "id")

	// Get the email-template from database
	emailTemplate, err := EmailTemplateSelect(id)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting email-template from database", nil)
	}

	// delete email-template
	query := `update email_templates set deleted_at=now() at time zone 'utc', deleted_by_user_id=$1 where id = $2`
	_, err = db.Get().Exec(query, user.ID, emailTemplate.ID)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(errors.Wrap(err, "Error deleting email-template"),
			"Error updating email-templates in database", nil)
	}

	return http.StatusOK, map[string]interface{}{"id": id}
}

// cleanEmailTemplate cleans up leading and trailing white-space etc...
func cleanEmailTemplate(emailTemplate *EmailTemplatePayload) {
	emailTemplate.Name = strings.TrimSpace(emailTemplate.Name)
	emailTemplate.TemplateText = strings.TrimSpace(emailTemplate.TemplateText)
	emailTemplate.TemplateType = strings.TrimSpace(emailTemplate.TemplateType)
	emailTemplate.Language = strings.TrimSpace(emailTemplate.Language)
}

// EmailTemplateReorder reorderEmailTemplate reorder position of email templates
func EmailTemplateReorder(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {

	emailTemplateOrder := emailTemplateReorderPayload{}
	dec := json.NewDecoder(req.Body)
	err := dec.Decode(&emailTemplateOrder)

	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(errors.Wrap(err, "Bad request. Could not read data."),
			"Bad request. Could not read data.", nil)
	}

	validationErrs, err := emailTemplateOrder.validate()

	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(errors.Wrap(err, "Error validating email-template reorder"),
			"An error occurred validating the email-template reorder.", nil)
	}
	if len(validationErrs) > 0 {
		return http.StatusBadRequest, handlers.ErrorMessage(nil, "validations errors.",
			map[string]interface{}{"errors": validationErrs})
	}

	tx, err := db.Get().Begin()
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "Database error creating transaction for Email Template update"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error in creating transaction in update of email-template reorder", nil)
	}

	updateQuery := `update email_templates set position = $1 where id = $2`
	stmt, err := tx.Prepare(updateQuery)
	if err != nil {
		_ = tx.Rollback()
		return http.StatusInternalServerError, handlers.ErrorMessage(errors.Wrap(err, "Error updating email-template reorder"),
			"Error updating email-templates position in database", nil)
	}

	defer func() { _ = stmt.Close() }()
	for i, id := range emailTemplateOrder.TemplateIDs {
		_, err = stmt.Exec(i+1, id)
		if err != nil {
			_ = tx.Rollback()
			return http.StatusInternalServerError, handlers.ErrorMessage(errors.Wrap(err, "Error updating email-template reorder"),
				"Error updating email-templates position in database", nil)
		}
	}
	err = tx.Commit()

	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "Database error committing transaction for Email Template update"))
		return http.StatusInternalServerError, handlers.ErrorMessage(errors.Wrap(err, "Error updating email-template reorder transaction"),
			"Error updating email-templates position in database transaction", nil)
	}

	return http.StatusOK, map[string]interface{}{}
}
