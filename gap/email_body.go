package gap

import (
	"net/http"
	"strconv"
	"strings"
	"time"

	"phizz/db"
	"phizz/handlers"

	"github.com/go-chi/chi"
	newrelic "github.com/newrelic/go-agent"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
)

// ClaimEmailBody returns emailBody for giving claimID and templateID
func ClaimEmailBody(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	gapClaimID := chi.URLParam(req, "claimid")
	templateID := chi.URLParam(req, "templateid")

	txn := w.(newrelic.Transaction)
	emailBody, err := getEmailBody(gapClaimID, templateID, txn, req)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Failed to get email body", nil)
	}
	return http.StatusOK, map[string]interface{}{"email_body": emailBody}
}

func getEmailBody(gapClaimID, templateID string, txn newrelic.Transaction, req *http.Request) (string, error) {
	gapClaim, err := gapClaimSelect(gapClaimID)
	if err != nil {
		return "", errors.Wrap(err, "Error getting GAP claim from database")
	}

	gapContract, err := handlers.GetContractByID(txn, req, gapClaim.ContractNumber)
	if err != nil {
		return "", errors.Wrap(err, "Error getting contract data")
	}

	emailTemplate, err := EmailTemplateSelect(templateID)
	if err != nil {
		return "", errors.Wrap(err, "Error getting email-template from database")
	}

	emailBody, err := replaceTokens(gapClaim, &gapContract, emailTemplate.TemplateText)
	if err != nil {
		return "", errors.Wrap(err, "Error replacing tokens")
	}

	return emailBody, nil
}

func replaceTokens(gapClaim *gapClaimPayload, gapContract *handlers.ContractData, templateText string) (string, error) {

	templateText = strings.Replace(templateText, db.TokenDateToday, formatDate(time.Now()), -1)

	// Values from contract
	if strings.Contains(templateText, db.TokenCustomerAddressFull) {
		address := gapContract.CustomerDetails.Address + "\n" + gapContract.CustomerDetails.City + ", " +
			gapContract.CustomerDetails.StateCode + " " + gapContract.CustomerDetails.PostalCode
		templateText = strings.Replace(templateText, db.TokenCustomerAddressFull, address, -1)
	}
	templateText = strings.Replace(templateText, db.TokenCustomerAddress, gapContract.CustomerDetails.Address, -1)
	templateText = strings.Replace(templateText, db.TokenCustomerCity, gapContract.CustomerDetails.City, -1)
	templateText = strings.Replace(templateText, db.TokenCustomerState, gapContract.CustomerDetails.StateCode, -1)
	templateText = strings.Replace(templateText, db.TokenCustomerZip, gapContract.CustomerDetails.PostalCode, -1)
	templateText = strings.Replace(templateText, db.TokenDealNumber, string(gapContract.Contract.DealNumber), -1)
	templateText = strings.Replace(templateText, db.TokenVehicleMake, gapContract.VehicleDetails.Make, -1)
	templateText = strings.Replace(templateText, db.TokenVehicleModel, gapContract.VehicleDetails.Model, -1)
	templateText = strings.Replace(templateText, db.TokenVehicleYear, gapContract.VehicleDetails.Year, -1)
	templateText = strings.Replace(templateText, db.TokenContractEffectiveDate, gapContract.Contract.EffectiveDate.Time.String(), -1)
	templateText = strings.Replace(templateText, db.TokenDealershipName, gapContract.Contract.IssuingDealer, -1)

	// values from claim
	templateText = strings.Replace(templateText, db.TokenCustomerNameFirst, gapClaim.FirstName, -1)
	templateText = strings.Replace(templateText, db.TokenCustomerNameLast, gapClaim.LastName, -1)
	customerNameFull := strings.Join([]string{gapClaim.FirstName, gapClaim.LastName}, " ")
	if gapClaim.IsBusiness {
		customerNameFull = customerNameFull + "/" + gapClaim.BusinessName.String
	}
	templateText = strings.Replace(templateText, db.TokenCustomerNameFull, customerNameFull, -1)
	templateText = strings.Replace(templateText, db.TokenGapContractNumber, gapClaim.ContractNumber, -1)

	// used in NO Gap Letter
	if strings.Contains(templateText, db.TokenGapCalculateCaseReserve) {
		templateText = strings.Replace(templateText, db.TokenGapCalculateCaseReserve, caseReserveCalculation(gapClaim), 1)
	}

	policeReportText, err := getPoliceReportNotes(gapClaim.ID)
	if err != nil {
		return "", errors.Wrap(err, "The token PoliceReport could not be replaced")
	}
	templateText = strings.Replace(templateText, db.TokenPoliceReport, policeReportText, -1)
	templateText = strings.Replace(templateText, db.TokenVINNumber, gapClaim.VIN, -1)
	templateText = strings.Replace(templateText, db.TokenDOLMileage, strconv.Itoa(gapClaim.ValuationReportMileage), -1)
	templateText = strings.Replace(templateText, db.TokenDOL, formatDate(gapClaim.DateOfLoss), -1)

	if strings.Contains(templateText, db.TokenGapMissing) {
		updateWaitingFor(gapClaim)
		description := gapMissingDescription(gapClaim)
		templateText = strings.Replace(templateText, db.TokenGapMissing, description, 1)
	}

	return templateText, nil
}

func formatCurrency(f decimal.Decimal) string {
	return f.StringFixed(2)
}

func formatDate(t time.Time) string {
	return t.Format("January 2, 2006")
}

func getPoliceReportNotes(gapClaimID int) (string, error) {
	notes, err := getFieldNotesByFieldID(gapClaimID, db.FieldIDPoliceReport)
	if err != nil {
		return "", errors.Wrap(err, "The fieldNotes for police report was not found")
	}
	// TODO: a separator to differentiate multiple notes, need to confirm with GAP team, handling of multiple notes
	notesStr := strings.Join(notes, "/")
	return notesStr, nil
}

func caseReserveCalculation(gapClaim *gapClaimPayload) string {
	getItem := func(sign, description string, value decimal.Decimal) string {
		paddingLen := (70.0 - float64(len(description))) * float64(1.8)
		padding := strings.Repeat(".", int(paddingLen))
		return "<li>" + sign + description + padding + "$ " + formatCurrency(value) + "</li>"
	}
	result := "<ul>"
	result += getItem("+", "Gross Amortized Loan Payoff Amount", gapClaim.RunAmortizationSheetValue)
	result += getItem("-", "Insurance Settlement", gapClaim.SettlementAmount)
	result += getItem("+", "Deductible Addition", gapClaim.InsurancePolicyDeductibleValueAddition)

	totalCancelValue := decimal.NewFromFloat(0.0)

	for _, contract := range gapClaim.Contracts {
		result += getItem("-", contract.ContractName+" Refund Amount ", contract.ContractValue)
		totalCancelValue = totalCancelValue.Add(contract.ContractValue)
	}

	if gapClaim.OtherValue1.Cmp(decimal.Zero) == 1 {
		result += getItem("-", gapClaim.OtherLabel1+" Refund Amount ", gapClaim.OtherValue1)
	}
	if gapClaim.OtherValue2.Cmp(decimal.Zero) == 1 {
		result += getItem("-", gapClaim.OtherLabel2+" Refund Amount ", gapClaim.OtherValue2)
	}
	if gapClaim.OtherValue3.Cmp(decimal.Zero) == 1 {
		result += getItem("-", gapClaim.OtherLabel3+" Refund Amount ", gapClaim.OtherValue3)
	}

	result += getItem("-", "Prior Damage Adjustments by Insurance", gapClaim.ValuationReportPriorDamageValue)
	result += getItem("-", "Misc Fee Value", gapClaim.ValuationReportMiscFeeValue)
	result += getItem("-", "Deductible Subtraction", gapClaim.InsurancePolicyDeductibleValueSubtraction)
	result += getItem("-", "Mileage Deduction", gapClaim.MileageDeduction)
	if gapClaim.Over150Percent.LessThan(decimal.Zero) {
		result += getItem("+", "Over 150%", gapClaim.Over150Percent)
	}
	// Case Reserve formula
	caseReserve := gapClaim.RunAmortizationSheetValue.Sub(gapClaim.SettlementAmount).Add(gapClaim.InsurancePolicyDeductibleValueAddition).
		Sub(totalCancelValue).Sub(gapClaim.OtherValue1).Sub(gapClaim.OtherValue2).Sub(gapClaim.OtherValue3).
		Sub(gapClaim.ValuationReportPriorDamageValue).Sub(gapClaim.ValuationReportMiscFeeValue).
		Sub(gapClaim.InsurancePolicyDeductibleValueSubtraction).Sub(gapClaim.MileageDeduction)

	if gapClaim.Over150Percent.LessThan(decimal.Zero) {
		caseReserve = caseReserve.Add(gapClaim.Over150Percent)
	}

	result += getItem("", "Total", caseReserve)
	result += "</ul>"
	return result
}

func gapMissingDescription(gapClaim *gapClaimPayload) string {
	result := "<ul>"
	missingItems := strings.Split(gapClaim.WaitingFor, ",")

	for _, item := range missingItems {
		result += "<li>" + db.GapMissingDescription[item] + "</li>"
	}
	result += "</ul>"
	return result
}
