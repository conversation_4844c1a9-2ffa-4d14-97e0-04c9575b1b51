package gap

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/csv"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"phizz/db"
	"phizz/handlers"

	"github.com/go-chi/chi"
	"github.com/jmoiron/sqlx"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
	"gopkg.in/guregu/null.v3"
)

// ClaimRequestType holds request type
type ClaimRequestType int

const (
	// ClaimRequestCreate claim create
	ClaimRequestCreate ClaimRequestType = iota
	// ClaimRequestUpdate claim update
	ClaimRequestUpdate
	// ClaimRequestAuthorize claim authorize
	ClaimRequestAuthorize
	// ClaimRequestDeny claim deny
	ClaimRequestDeny
	// ClaimRequestReturn claim return
	ClaimRequestReturn
	// ClaimRequestSubmit claim submit
	ClaimRequestSubmit
	// ClaimRequestCreateChild create child claim
	ClaimRequestCreateChild
)

type worklistClaim struct {
	ID                  int             `json:"id" db:"id"`
	InsuredName         string          `json:"insured_name" db:"insured_name"`
	ContractNumber      string          `json:"contract_number" db:"contract_number"`
	AssignedTo          string          `json:"assigned_to" db:"assigned_to"`
	Status              string          `json:"status" db:"status"`
	DateOfClaimReceived time.Time       `json:"date_of_claim_received" db:"date_of_claim_received"`
	DateOfLastIn        time.Time       `json:"date_of_last_in" db:"date_of_last_in"`
	WaitingFor          string          `json:"waiting_for" db:"waiting_for"`
	DateOfLastOut       time.Time       `json:"date_of_last_out" db:"date_of_last_out"`
	CaseReserve         decimal.Decimal `json:"case_reserve" db:"case_reserve"`
	DateOfLoss          time.Time       `json:"date_of_loss" db:"date_of_loss"`
	State               string          `json:"state" db:"state"`
}

func (claim *worklistClaim) ToSlice() []string {
	result := make([]string, 11)
	result[0] = claim.InsuredName
	result[1] = claim.ContractNumber
	result[2] = claim.AssignedTo
	result[3] = claim.Status
	result[4] = claim.DateOfClaimReceived.Format("2006-01-02")
	result[5] = claim.DateOfLastIn.Format("2006-01-02")
	result[6] = strings.Replace(claim.WaitingFor, ",", " ", -1)
	result[7] = claim.DateOfLastOut.Format("2006-01-02")
	result[8] = claim.CaseReserve.String()
	result[9] = claim.DateOfLoss.Format("2006-01-02")
	result[10] = claim.State
	return result
}

// ClaimIndex returns a list of claims
// This function returns all gapclaims created by the logged in User if no query parameters are provided
// The claims can be filtered by following query parameters
// q = filter by firstname, lastname, business_name or contract_number
// userID - createdby userid
// DOLSort - the claims will be sorted by date of last out
// Status - return claims with the given status
func ClaimIndex(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	claims := []worklistClaim{}

	wh := " where is_child = false " // child claims not to be shown in worklog
	args := []interface{}{}

	nameContractVin := req.FormValue("q")
	if nameContractVin != "" {
		wh = wh + `and (concat(c.first_name, ' ', c.last_name) ilike $1 or concat(c.last_name, ' ', c.first_name)
		 ilike $1 or c.business_name ilike $1 or contract_number ilike $1 or vin ilike $1)`
		args = append(args, "%"+strings.Join(strings.Fields(strings.TrimSpace(nameContractVin)), " ")+"%")
	}

	userID := req.FormValue("user_id")
	// if userID is not provided in query, don't restrict by userID, show all claims
	var err error
	if userID != "" {
		_, err := strconv.Atoi(userID) // converting to number ensures, it's a valid number
		if err == nil {
			wh = wh + " and owner_id = " + userID
		}
	}

	status := req.FormValue("status")
	if status != "" {
		statusQuery := mapGapStatus(status)
		if statusQuery != "" {
			wh = wh + " and " + statusQuery
		}
	}
	age := req.FormValue("age")
	if age != "" && age != "all" {
		if wh != "" {
			wh += " and "
		}

		switch age {
		case "LessThan30Days":
			wh += " ((extract(epoch from age(gap_claims.date_of_claim_received))/3600)/24)/30 <= 1 "
		case "ThirtyToSixtyDays":
			wh += " ((extract(epoch from age(gap_claims.date_of_claim_received))/3600)/24)/30 > 1 and ((extract(epoch from age(gap_claims.date_of_claim_received))/3600)/24)/30 <=2 "
		case "SixtyToNinetyDays":
			wh += " ((extract(epoch from age(gap_claims.date_of_claim_received))/3600)/24)/30 > 2 and ((extract(epoch from age(gap_claims.date_of_claim_received))/3600)/24)/30 <=3 "
		case "GreaterThanNinetyDays":
			wh += " ((extract(epoch from age(gap_claims.date_of_claim_received))/3600)/24)/30 > 3 "
		default:
			return http.StatusBadRequest, handlers.ErrorMessage(err, "Error getting gap claims lists data - Invalid age filter", nil)
		}
	}

	orderBy := ""

	dateOflastOutSort := req.FormValue("dol_sort")
	if dateOflastOutSort == "desc" || dateOflastOutSort == "asc" {
		orderBy = "date_of_last_out " + dateOflastOutSort
	}

	insuredNameSort := req.FormValue("insured_name_sort")
	if insuredNameSort == "desc" || insuredNameSort == "asc" {
		if orderBy != "" {
			orderBy = orderBy + ","
		}
		orderBy = orderBy + "insured_name " + insuredNameSort
	}

	openedSort := req.FormValue("opened_sort")
	if openedSort == "desc" || openedSort == "asc" {
		if orderBy != "" {
			orderBy = orderBy + ","
		}
		orderBy = orderBy + "date_of_claim_received " + openedSort
	}

	lastInSort := req.FormValue("last_in_sort")
	if lastInSort == "desc" || lastInSort == "asc" {
		if orderBy != "" {
			orderBy = orderBy + ","
		}
		orderBy = orderBy + "date_of_last_in " + lastInSort
	}

	assignedToSort := req.FormValue("assigned_to_sort")
	if assignedToSort == "desc" || assignedToSort == "asc" {
		if orderBy != "" {
			orderBy = orderBy + ","
		}
		orderBy = orderBy + "assigned_to " + assignedToSort
	}

	if orderBy != "" {
		orderBy = " order by " + orderBy
	} else {
		// default order should be status and DOL desc
		orderBy = "order by status, date_of_last_out desc"
	}

	selectClause := `gap_claims.id,
	case when c.is_business and (c.first_name!='' or c.last_name!='') then c.last_name || ',' || c.first_name || '/' || c.business_name
            when c.is_business and c.first_name='' and c.last_name='' then c.business_name
            else c.last_name || ',' || c.first_name end insured_name,
	contract_number, status,
	date_of_claim_received, date_of_last_in, waiting_for, date_of_last_out, case_reserve, date_of_loss, state,
	users.first_name || ' ' || users.last_name as assigned_to`

	fromClause := "gap_claims join customers c on customer_id = c.id join users on owner_id = users.id"

	countQuery := "select count(*) from " + fromClause + " " + wh

	sumTotalEstimateQuery := "select sum(case_reserve) from " + fromClause + " " + wh

	// handle pagination
	p := req.FormValue("page")
	var listQuery string
	if n, err := strconv.Atoi(p); err == nil && n > 0 {
		listQuery = fmt.Sprintf("select %s from %s %s %s limit %d offset %d", selectClause, fromClause, wh, orderBy, handlers.PerPageEntries, (n-1)*handlers.PerPageEntries)
	} else { // if page number is not provided, return all values
		listQuery = fmt.Sprintf("select %s from %s %s %s", selectClause, fromClause, wh, orderBy)
	}
	err = db.Get().Select(&claims, listQuery, args...)
	if err != nil {
		err = errors.Wrap(err, "Database error getting GAP claims lists")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting GAP claims lists data", nil)
	}
	count := 0
	err = db.Get().Get(&count, countQuery, args...)
	if err != nil {
		err = errors.Wrap(err, "Database error getting GAP claims lists count")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting GAP claims lists data", nil)
	}

	var totalEstimate sql.NullFloat64
	err = db.Get().Get(&totalEstimate, sumTotalEstimateQuery, args...)
	if err != nil {
		err = errors.Wrap(err, "Database error getting GAP claims total estimate sum")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting GAP claims lists data - total estimate sum", nil)
	}

	csv := req.FormValue("csv")
	var claimsCsv string
	if csv == "true" {
		claimsCsv = convertToCSV(claims)
		claims = nil
		return http.StatusOK, map[string]interface{}{"count": count, "gap_claims": claimsCsv}
	}
	return http.StatusOK, map[string]interface{}{"count": count, "gap_claims": claims, "totalEstimate": totalEstimate.Float64}
}

func convertToCSV(claims []worklistClaim) string {
	result := new(bytes.Buffer)
	w := csv.NewWriter(result)
	w.Write([]string{"Insured Name", "Contract#", "Assigned To", "Status", "Opened", "Last In", "Waiting For",
		"Last Out", "Estimate", "DOL", "State"})
	for _, claim := range claims {
		w.Write(claim.ToSlice())
	}
	w.Flush()
	return result.String()
}

func mapGapStatus(status string) string {
	queryString := ""
	switch status {
	case "In Inquiry":
		queryString += fmt.Sprintf(" (status = '%s') ", db.GapClaimStatusInquiry)
	case "In Process":
		queryString += fmt.Sprintf(" (status in ('%s','%s','%s','%s', '%s')) ",
			db.GapClaimStatusPending, db.GapClaimStatusPendingReopened, db.GapClaimStatusPendingDenial,
			db.GapClaimStatusReturnedForCorrections, db.GapClaimStatusReadyToProcess)
	case "In Review":
		queryString += fmt.Sprintf(" (status = '%s') ", db.GapClaimStatusWaitingForAuthorization)
	case "In Finance":
		queryString += fmt.Sprintf(" (status in ('%s','%s')) ", db.GapClaimStatusWaitingForCheck,
			db.GapClaimStatusWaitingForPayment)
	case "Closed":
		queryString += fmt.Sprintf(" (status in ('%s','%s','%s','%s','%s')) ", db.GapClaimStatusDeny,
			db.GapClaimStatusClosedNoResponse, db.GapClaimStatusNoGap, db.GapClaimStatusCheckWritten, db.GapClaimStatusCheckVoided)
	case "All Active":
		queryString += fmt.Sprintf(" (status not in ('%s','%s','%s','%s','%s')) ", db.GapClaimStatusInquiry,
			db.GapClaimStatusDeny, db.GapClaimStatusClosedNoResponse, db.GapClaimStatusNoGap, db.GapClaimStatusCheckWritten)
	}
	return queryString
}

type bankPayload struct {
	BankVendorID       string `json:"bank_vendor_id" db:"bank_vendor_id"`
	BankAccountName    string `json:"bank_account_name" db:"bank_account_name"`
	BankAddressStreet1 string `json:"bank_address_street1" db:"bank_address_street1"`
	BankAddressStreet2 string `json:"bank_address_street2" db:"bank_address_street2"`
	BankAddressCity    string `json:"bank_address_city" db:"bank_address_city"`
	BankAddressState   string `json:"bank_address_state" db:"bank_address_state"`
	BankAddressZip     string `json:"bank_address_zip" db:"bank_address_zip"`
}

type gapClaimContractPayload struct {
	ContractNumber     string                     `json:"contract_number" db:"contract_number"`
	ContractCode       string                     `json:"contract_code" db:"contract_code"`
	ContractName       string                     `json:"contract_name" db:"contract_name"`
	ContractFlag       bool                       `json:"contract_flag" db:"contract_flag"`
	ContractValue      decimal.Decimal            `json:"contract_value" db:"contract_value"`
	ManagerFlag        bool                       `json:"manager_flag" db:"manager_flag"`
	ContractFieldNotes []contractFieldNotePayload `json:"contract_field_notes" db:"-"`
}

type fieldNote struct {
	FieldID int                `json:"field_id" db:"-"`
	Count   int                `json:"count" db:"-"`
	Notes   []fieldNotePayload `json:"notes" db:"-"`
}

type gapClaimPayload struct {
	ID                                        int                       `json:"id" db:"id"`
	VIN                                       string                    `json:"vin" db:"vin"`
	Make                                      string                    `json:"make" db:"make"`
	Model                                     string                    `json:"model" db:"model"`
	Year                                      int                       `json:"year" db:"year"`
	CreatedByUserID                           int                       `json:"created_by_user_id" db:"created_by_user_id"`
	Status                                    string                    `json:"status" db:"status"`
	StatusChangeDescription                   string                    `json:"status_change_description" db:"status_change_description"`
	DateOfClaimReceived                       time.Time                 `json:"date_of_claim_received" db:"date_of_claim_received"`
	DateOfLastOut                             time.Time                 `json:"date_of_last_out" db:"date_of_last_out"`
	DateOfLastIn                              time.Time                 `json:"date_of_last_in" db:"date_of_last_in"`
	ContractNumber                            string                    `json:"contract_number" db:"contract_number"`
	DateOfLoss                                time.Time                 `json:"date_of_loss" db:"date_of_loss"`
	WaitingFor                                string                    `json:"waiting_for" db:"waiting_for"`
	WaitingForDescription                     string                    `json:"-" db:"-"`
	CustomerID                                int                       `json:"customer_id" db:"customer_id"`
	FirstName                                 string                    `json:"first_name" db:"first_name"`
	LastName                                  string                    `json:"last_name" db:"last_name"`
	IsBusiness                                bool                      `json:"is_business" db:"is_business"`
	BusinessName                              null.String               `json:"business_name" db:"business_name"`
	State                                     string                    `json:"state" db:"state"`
	City                                      string                    `json:"city" db:"city"`
	PostalCode                                string                    `json:"postal_code" db:"postal_code"`
	StreetAddress                             string                    `json:"street_address" db:"street_address"`
	EmailAddress                              string                    `json:"email_address" db:"email_address"`
	PhoneNumber                               string                    `json:"phone_number" db:"phone_number"`
	CaseReserve                               decimal.Decimal           `json:"case_reserve" db:"case_reserve"`
	AvgCaseReserve                            decimal.Decimal           `json:"avg_case_reserve" db:"-"`
	RunAmortizationSheetValue                 decimal.Decimal           `json:"run_amortization_sheet_value" db:"run_amortization_sheet_value"`
	CalculateAmortizationValue                bool                      `json:"calculate_amortization_value" db:"calculate_amortization_value"`
	HasCanceledContracts                      bool                      `json:"has_canceled_contracts" db:"has_canceled_contracts"`
	OtherLabel1                               string                    `json:"other_label1" db:"other_label1"`
	OtherValue1                               decimal.Decimal           `json:"other_value1" db:"other_value1"`
	OtherLabel2                               string                    `json:"other_label2" db:"other_label2"`
	OtherValue2                               decimal.Decimal           `json:"other_value2" db:"other_value2"`
	OtherLabel3                               string                    `json:"other_label3" db:"other_label3"`
	OtherValue3                               decimal.Decimal           `json:"other_value3" db:"other_value3"`
	Contracts                                 []gapClaimContractPayload `json:"contracts" db:"-"`
	HasSettlementAmount                       bool                      `json:"has_settlement_amount" db:"has_settlement_amount"`
	SettlementAmount                          decimal.Decimal           `json:"settlement_amount" db:"settlement_amount"`
	HasNumberOfDelinquentPayments             bool                      `json:"has_number_of_delinquent_payments" db:"has_number_of_delinquent_payments"`
	NumberOfDelinquentPaymentsManagerFlag     bool                      `json:"number_of_delinquent_payments_manager_flag" db:"number_of_delinquent_payments_manager_flag"`
	NumberOfDelinquentPayments                int                       `json:"number_of_delinquent_payments" db:"number_of_delinquent_payments"`
	HasTotalDelinquentPaymentsCovered         bool                      `json:"has_total_delinquent_payments_covered" db:"has_total_delinquent_payments_covered"`
	TotalDelinquentPaymentsCoveredManagerFlag bool                      `json:"total_delinquent_payments_covered_manager_flag" db:"total_delinquent_payments_covered_manager_flag"`
	TotalDelinquentPaymentsCovered            decimal.Decimal           `json:"total_delinquent_payments_covered" db:"total_delinquent_payments_covered"`
	HasNumberOfExtensions                     bool                      `json:"has_number_of_extensions" db:"has_number_of_extensions"`
	NumberOfExtensionsManagerFlag             bool                      `json:"number_of_extensions_manager_flag" db:"number_of_extensions_manager_flag"`
	NumberOfExtensions                        int                       `json:"number_of_extensions" db:"number_of_extensions"`
	HasTotalExtensionsCovered                 bool                      `json:"has_total_extensions_covered" db:"has_total_extensions_covered"`
	TotalExtensionsCoveredManagerFlag         bool                      `json:"total_extensions_covered_manager_flag" db:"total_extensions_covered_manager_flag"`
	TotalExtensionsCovered                    decimal.Decimal           `json:"total_extensions_covered" db:"total_extensions_covered"`
	HasNegativeEquityAmount                   bool                      `json:"has_negative_equity_amount" db:"has_negative_equity_amount"`
	NegativeEquityAmount                      decimal.Decimal           `json:"negative_equity_amount" db:"negative_equity_amount"`
	HasInsuranceCheckAmount                   bool                      `json:"has_insurance_check_amount" db:"has_insurance_check_amount"`
	InsuranceCheckAmount                      decimal.Decimal           `json:"insurance_check_amount" db:"insurance_check_amount"`
	IsValuationReportAvailable                bool                      `json:"is_valuation_report_available" db:"is_valuation_report_available"`
	ValuationReportAdjustments                decimal.Decimal           `json:"valuation_report_adjustments" db:"valuation_report_adjustments"`
	IsValuationReportMatchesBaseValue         bool                      `json:"is_valuation_report_matches_base_value" db:"is_valuation_report_matches_base_value"`
	ValuationReportMatchesBaseValue           decimal.Decimal           `json:"valuation_report_matches_base_value" db:"valuation_report_matches_base_value"`
	ValuationReportVinMatches                 bool                      `json:"valuation_report_vin_matches" db:"valuation_report_vin_matches"`
	HasValuationReportPriorDamage             bool                      `json:"has_valuation_report_prior_damage" db:"has_valuation_report_prior_damage"`
	ValuationReportPriorDamageValue           decimal.Decimal           `json:"valuation_report_prior_damage_value" db:"valuation_report_prior_damage_value"`
	HasValuationReportMiscFee                 bool                      `json:"has_valuation_report_misc_fee" db:"has_valuation_report_misc_fee"`
	ValuationReportMiscFeeValue               decimal.Decimal           `json:"valuation_report_misc_fee_value" db:"valuation_report_misc_fee_value"`
	ValuationReportMileage                    int                       `json:"valuation_report_mileage" db:"valuation_report_mileage"`
	ValuationReportType                       string                    `json:"valuation_report_type" db:"valuation_report_type"`
	HasOptionsMatchBookOutOver150Percent      bool                      `json:"has_options_match_book_out_over_150_percent" db:"has_options_match_book_out_over_150_percent"`
	Over150Percent                            decimal.Decimal           `json:"over_150_percent" db:"over_150_percent"`
	HasOriginalFinancingContract              bool                      `json:"has_original_financing_contract" db:"has_original_financing_contract"`
	OriginalFinancingContractValue            decimal.Decimal           `json:"original_financing_contract_value" db:"original_financing_contract_value"`
	HasMSRPValue                              bool                      `json:"has_msrp_value" db:"has_msrp_value"`
	MSRPValue                                 decimal.Decimal           `json:"msrp_value" db:"msrp_value"`
	ContractNumberMatches                     bool                      `json:"contract_number_matches" db:"contract_number_matches"`
	BankHistoryMatches                        bool                      `json:"bank_history_matches" db:"bank_history_matches"`
	IsPoliceReportAvailable                   bool                      `json:"is_police_report_available" db:"is_police_report_available"`
	HasInsurancePolicyDeductible              bool                      `json:"has_insurance_policy_deductible" db:"has_insurance_policy_deductible"`
	InsurancePolicyDeductibleValueAddition    decimal.Decimal           `json:"insurance_policy_deductible_value_addition" db:"insurance_policy_deductible_value_addition"`
	InsurancePolicyDeductibleValueSubtraction decimal.Decimal           `json:"insurance_policy_deductible_value_subtraction" db:"insurance_policy_deductible_value_subtraction"`
	InsurancePolicyDeductibleReason           string                    `json:"insurance_policy_deductible_reason" db:"insurance_policy_deductible_reason"`
	HasBankInformation                        bool                      `json:"has_bank_information" db:"has_bank_information"`
	IsFullLoanHistoryAvailable                bool                      `json:"is_full_loan_history_available" db:"is_full_loan_history_available"`
	PaymentAmount                             decimal.Decimal           `json:"payment_amount" db:"payment_amount"`
	InterestRate                              decimal.Decimal           `json:"interest_rate" db:"interest_rate"`
	FirstPaymentDate                          time.Time                 `json:"first_payment_date" db:"first_payment_date"`
	HasNewBankInformation                     bool                      `json:"has_new_bank_information" db:"has_new_bank_information"`
	BankID                                    int                       `json:"bank_id" db:"bank_id"`
	BankVendorID                              string                    `json:"bank_vendor_id" db:"bank_vendor_id"`
	BankAccountName                           string                    `json:"bank_account_name" db:"bank_account_name"`
	BankAccountNumber                         string                    `json:"bank_account_number" db:"bank_account_number"`
	BankAddressStreet1                        string                    `json:"bank_address_street1" db:"bank_address_street1"`
	BankAddressStreet2                        string                    `json:"bank_address_street2" db:"bank_address_street2"`
	BankAddressCity                           string                    `json:"bank_address_city" db:"bank_address_city"`
	BankAddressState                          string                    `json:"bank_address_state" db:"bank_address_state"`
	BankAddressZip                            string                    `json:"bank_address_zip" db:"bank_address_zip"`
	ContractDealDate                          time.Time                 `json:"contract_deal_date" db:"contract_deal_date"`
	ContractTermMonths                        int                       `json:"contract_term_months" db:"contract_term_months"`
	LastAction                                string                    `json:"last_action" db:"last_action"`
	OwnerID                                   int                       `json:"owner_id" db:"owner_id"`
	ChildClaimReason                          string                    `json:"child_claim_reason" db:"child_claim_reason"`
	ParentClaimID                             sql.NullInt64             `json:"parent_claim_id" db:"parent_claim_id"`
	IsChild                                   bool                      `json:"is_child" db:"is_child"`
	UpdatedAt                                 time.Time                 `json:"updated_at" db:"updated_at"`
	UpdatedByUserID                           int                       `json:"updated_by_user_id" db:"updated_by_user_id"`
	UpdatedByUserName                         string                    `json:"updated_by_user_name" db:"updated_by_user_name"`
	RunAmortizationManagerFlag                bool                      `json:"run_amortization_manager_flag" db:"run_amortization_manager_flag"`
	OtherLabel1ManagerFlag                    bool                      `json:"other_label1_manager_flag" db:"other_label1_manager_flag"`
	OtherLabel2ManagerFlag                    bool                      `json:"other_label2_manager_flag" db:"other_label2_manager_flag"`
	OtherLabel3ManagerFlag                    bool                      `json:"other_label3_manager_flag" db:"other_label3_manager_flag"`
	InsurancePaymentCheckManagerFlag          bool                      `json:"insurance_payment_check_manager_flag" db:"insurance_payment_check_manager_flag"`
	SettlementLetterManagerFlag               bool                      `json:"settlement_letter_manager_flag" db:"settlement_letter_manager_flag"`
	HasNegativeEquityAmountManagerFlag        bool                      `json:"has_negative_equity_amount_manager_flag" db:"has_negative_equity_amount_manager_flag"`
	ValuationReportManagerFlag                bool                      `json:"valuation_report_manager_flag" db:"valuation_report_manager_flag"`
	ValuationReportBaseValueManagerFlag       bool                      `json:"valuation_report_base_value_manager_flag" db:"valuation_report_base_value_manager_flag"`
	ValuationReportVinMatchesManagerFlag      bool                      `json:"valuation_report_vin_matches_manager_flag" db:"valuation_report_vin_matches_manager_flag"`
	ValuationReportPriorDamageManagerFlag     bool                      `json:"valuation_report_prior_damage_manager_flag" db:"valuation_report_prior_damage_manager_flag"`
	ValuationReportMiscFeeManagerFlag         bool                      `json:"valuation_report_misc_fee_manager_flag" db:"valuation_report_misc_fee_manager_flag"`
	ValuationReportMileageManagerFlag         bool                      `json:"valuation_report_mileage_manager_flag" db:"valuation_report_mileage_manager_flag"`
	ValuationReportDolManagerFlag             bool                      `json:"valuation_report_dol_manager_flag" db:"valuation_report_dol_manager_flag"`
	ValuationReportTypeManagerFlag            bool                      `json:"valuation_report_type_manager_flag" db:"valuation_report_type_manager_flag"`
	OptionsBookOutOver150PercentManagerFlag   bool                      `json:"options_book_out_over_150_percent_manager_flag" db:"options_book_out_over_150_percent_manager_flag"`
	OriginalFinancingManagerFlag              bool                      `json:"original_financing_manager_flag" db:"original_financing_manager_flag"`
	ContractNumberMatchesManagerFlag          bool                      `json:"contract_number_matches_manager_flag" db:"contract_number_matches_manager_flag"`
	BankHistoryMatchesManagerFlag             bool                      `json:"bank_history_matches_manager_flag" db:"bank_history_matches_manager_flag"`
	PoliceReportManagerFlag                   bool                      `json:"police_report_manager_flag" db:"police_report_manager_flag"`
	InsuranceDeductibleAdditionManagerFlag    bool                      `json:"insurance_deductible_addition_manager_flag" db:"insurance_deductible_addition_manager_flag"`
	InsuranceDeductibleSubtractionManagerFlag bool                      `json:"insurance_deductible_subtraction_manager_flag" db:"insurance_deductible_subtraction_manager_flag"`
	BankInformationManagerFlag                bool                      `json:"bank_information_manager_flag" db:"bank_information_manager_flag"`
	FullLoanHistoryManagerFlag                bool                      `json:"full_loan_history_manager_flag" db:"full_loan_history_manager_flag"`
	LoanNumberManagerFlag                     bool                      `json:"loan_number_manager_flag" db:"loan_number_manager_flag"`
	PaymentAmountManagerFlag                  bool                      `json:"payment_amount_manager_flag" db:"payment_amount_manager_flag"`
	InterestRateManagerFlag                   bool                      `json:"interest_rate_manager_flag" db:"interest_rate_manager_flag"`
	FirstPaymentManagerFlag                   bool                      `json:"first_payment_manager_flag" db:"first_payment_manager_flag"`
	ContractDealDateManagerFlag               bool                      `json:"contract_deal_date_manager_flag" db:"contract_deal_date_manager_flag"`
	ContractTermMonthsManagerFlag             bool                      `json:"contract_term_months_manager_flag" db:"contract_term_months_manager_flag"`
	MSRPValueManagerFlag                      bool                      `json:"msrp_value_manager_flag" db:"msrp_value_manager_flag"`
	IsCSClaim                                 bool                      `json:"is_cs_claim" db:"is_cs_claim"`
	CSCheckAmount                             decimal.Decimal           `json:"cs_check_amount" db:"cs_check_amount"`
	NotPaidByCS                               bool                      `json:"not_paid_by_cs" db:"not_paid_by_cs"`
	InsuranceCompany                          string                    `json:"insurance_company" db:"insurance_company"`
	HasInsuranceCompany                       bool                      `json:"has_insurance_company" db:"has_insurance_company"`
	PolicyNumber                              string                    `json:"policy_number" db:"policy_number"`
	HasPolicyNumber                           bool                      `json:"has_policy_number" db:"has_policy_number"`
	MileageDeduction                          decimal.Decimal           `json:"mileage_deduction" db:"mileage_deduction"`
	HasMileageDeduction                       bool                      `json:"has_mileage_deduction" db:"has_mileage_deduction"`
	NADA                                      decimal.Decimal           `json:"nada" db:"nada"`
	HasNADA                                   bool                      `json:"has_nada" db:"has_nada"`
	HasValuationNadaDifference                bool                      `json:"has_valuation_nada_difference" db:"has_valuation_nada_difference"`
	MileageDeductionManagerFlag               bool                      `json:"mileage_deduction_manager_flag" db:"mileage_deduction_manager_flag"`
	HasRecovery                               bool                      `json:"has_recovery" db:"has_recovery"`
	HasEstimateWithPhotos                     bool                      `json:"has_estimate_with_photos" db:"has_estimate_with_photos"`
	NadaManagerFlag                           bool                      `json:"nada_manager_flag" db:"nada_manager_flag"`
	GapClosedAt                               time.Time                 `json:"gap_closed_at" db:"gap_closed_at"`
	RecoveryCheckAmount                       decimal.Decimal           `json:"recovery_check_amount" db:"recovery_check_amount"`
	RecoveryStatus                            string                    `json:"recovery_status" db:"recovery_status"`
	VinRecordID                               sql.NullInt64             `json:"-" db:"vin_record_id"`
	IsInProgress                              bool                      `json:"is_in_progress" db:"is_in_progress"`
	ChildClaims                               []struct {
		ID               int             `json:"id" db:"id"`
		Status           string          `json:"status" db:"status"`
		ChildClaimReason string          `json:"child_claim_reason" db:"child_claim_reason"`
		CaseReserve      decimal.Decimal `json:"case_reserve" db:"case_reserve"`
	} `json:"child_claims" db:"-"`
}

// ClaimCreate creates a new Gap Claim
func ClaimCreate(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	ctx := req.Context()
	gapClaim := gapClaimPayload{}
	err := gapClaimFromReq(&gapClaim, req)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Malformed GAP Claim data for create.", nil)
	}

	cleanGapClaim(&gapClaim)
	formErrors, err := validateGapClaim(ctx, &gapClaim, ClaimRequestCreate)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(errors.Wrap(err, "Error validating GAP Claim"),
			"An error occurred validating the form values.", nil)
	}

	if len(formErrors) > 0 {
		return http.StatusBadRequest, handlers.ErrorMessage(nil, "Form validations errors.",
			map[string]interface{}{"errors": formErrors})
	}

	bankInfo := bankPayload{
		BankVendorID:       gapClaim.BankVendorID,
		BankAccountName:    gapClaim.BankAccountName,
		BankAddressStreet1: gapClaim.BankAddressStreet1,
		BankAddressStreet2: gapClaim.BankAddressStreet2,
		BankAddressCity:    gapClaim.BankAddressCity,
		BankAddressState:   gapClaim.BankAddressState,
		BankAddressZip:     gapClaim.BankAddressZip,
	}
	gapClaim.BankID, err = getBankID(&bankInfo)
	if err != nil {
		err = errors.Wrap(err, "Database error getting bank information")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error getting bank information", nil)
	}

	// Default values for fields
	gapClaim.CreatedByUserID = user.ID
	gapClaim.OwnerID = gapClaim.CreatedByUserID // at the time of claim creation, creator and owner is the same
	gapClaim.Status = db.GapClaimStatusInquiry  // New claim should have status 'I' Inquiry

	gapClaim.CaseReserve, err = calculateAverageLastQuarterPayout(time.Now())
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error calculating case reserve of GAP claim", nil)
	}

	gapClaim.OtherLabel1 = "Other"
	gapClaim.OtherLabel2 = gapClaim.OtherLabel1
	gapClaim.OtherLabel3 = gapClaim.OtherLabel1

	gapClaim.IsChild = false
	gapClaim.DateOfLoss = time.Now().UTC()
	gapClaim.FirstPaymentDate = time.Now().UTC()

	// CS claims contract number always begins with 'CS'
	if strings.HasPrefix(gapClaim.ContractNumber, "CS") {
		gapClaim.IsCSClaim = true
	}

	updateWaitingFor(&gapClaim) // updates gapClaim.WaitingFor based on check list

	id, err := gapClaimInsert(&gapClaim)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error creating GAP claim", nil)
	}
	return http.StatusOK, map[string]interface{}{"id": id}
}

func gapClaimInsert(gapClaim *gapClaimPayload) (int, error) {
	claimInsert := `insert into gap_claims (
		vin,
		contract_number,
		status,
		status_change_description,
		date_of_loss,
		date_of_claim_received,
		date_of_last_in,
		date_of_last_out,
		waiting_for,
		customer_id,
		case_reserve,
		created_by_user_id,
		run_amortization_sheet_value,
		has_canceled_contracts,
		other_label1,
		other_value1,
		other_label2,
		other_value2,
		other_label3,
		other_value3,
		has_settlement_amount,
		settlement_amount,
		has_insurance_check_amount,
		insurance_check_amount,
		is_valuation_report_available,
		valuation_report_adjustments,
		is_valuation_report_matches_base_value,
		valuation_report_matches_base_value,
		valuation_report_vin_matches,
		has_valuation_report_prior_damage,
		valuation_report_prior_damage_value,
		has_valuation_report_misc_fee,
		valuation_report_misc_fee_value,
		valuation_report_mileage,
		valuation_report_type,
		has_options_match_book_out_over_150_percent,
		over_150_percent,
		has_original_financing_contract,
		original_financing_contract_value,
		has_msrp_value,
		msrp_value,
		contract_number_matches,
		bank_history_matches,
		is_police_report_available,
		has_insurance_policy_deductible,
		insurance_policy_deductible_value_addition,
		insurance_policy_deductible_value_subtraction,
		insurance_policy_deductible_reason,
		has_bank_information,
		is_full_loan_history_available,
		payment_amount,
		interest_rate,
		first_payment_date,
		has_new_bank_information,
		bank_id,
		bank_account_number,
		contract_deal_date,
		contract_term_months,
		last_action,
		owner_id,
		is_child,
		parent_claim_id,
		run_amortization_manager_flag,
		other_label1_manager_flag,
		other_label2_manager_flag,
		other_label3_manager_flag,
		insurance_payment_check_manager_flag,
		settlement_letter_manager_flag,
		valuation_report_manager_flag,
		valuation_report_base_value_manager_flag,
		valuation_report_vin_matches_manager_flag,
		valuation_report_prior_damage_manager_flag,
		valuation_report_misc_fee_manager_flag,
		valuation_report_mileage_manager_flag,
		valuation_report_dol_manager_flag,
		valuation_report_type_manager_flag,
		options_book_out_over_150_percent_manager_flag,
		original_financing_manager_flag,
		contract_number_matches_manager_flag,
		bank_history_matches_manager_flag,
		police_report_manager_flag,
		insurance_deductible_addition_manager_flag,
		insurance_deductible_subtraction_manager_flag,
		bank_information_manager_flag,
		full_loan_history_manager_flag,
		loan_number_manager_flag,
		payment_amount_manager_flag,
		interest_rate_manager_flag,
		first_payment_manager_flag,
		contract_deal_date_manager_flag,
		contract_term_months_manager_flag,
		msrp_value_manager_flag,
		insurance_company,
		has_insurance_company,
		policy_number,
		has_policy_number,
		mileage_deduction,
		has_mileage_deduction,
		nada,
		has_nada,
		has_valuation_nada_difference,
		mileage_deduction_manager_flag,
		has_estimate_with_photos,
		nada_manager_flag,
		gap_closed_at,
		is_cs_claim,
                not_paid_by_cs,
		vin_record_id)

		values (:vin,
		:contract_number,
		:status,
		:status_change_description,
		:date_of_loss,
		now() at time zone 'utc',
		now() at time zone 'utc',
		now() at time zone 'utc',
		:waiting_for,
		:customer_id,
		:case_reserve,
		:created_by_user_id,
		:run_amortization_sheet_value,
		:has_canceled_contracts,
		:other_label1,
		:other_value1,
		:other_label2,
		:other_value2,
		:other_label3,
		:other_value3,
		:has_settlement_amount,
		:settlement_amount,
		:has_insurance_check_amount,
		:insurance_check_amount,
		:is_valuation_report_available,
		:valuation_report_adjustments,
		:is_valuation_report_matches_base_value,
		:valuation_report_matches_base_value,
		:valuation_report_vin_matches,
		:has_valuation_report_prior_damage,
		:valuation_report_prior_damage_value,
		:has_valuation_report_misc_fee,
		:valuation_report_misc_fee_value,
		:valuation_report_mileage,
		:valuation_report_type,
		:has_options_match_book_out_over_150_percent,
		:over_150_percent,
		:has_original_financing_contract,
		:original_financing_contract_value,
		:has_msrp_value,
		:msrp_value,
		:contract_number_matches,
		:bank_history_matches,
		:is_police_report_available,
		:has_insurance_policy_deductible,
		:insurance_policy_deductible_value_addition,
		:insurance_policy_deductible_value_subtraction,
		:insurance_policy_deductible_reason,
		:has_bank_information,
		:is_full_loan_history_available,
		:payment_amount,
		:interest_rate,
		:first_payment_date,
		:has_new_bank_information,
		:bank_id,
		:bank_account_number,
		:contract_deal_date,
		:contract_term_months,
		:last_action,
		:owner_id,
		:is_child,
		:parent_claim_id,
		:run_amortization_manager_flag,
		:other_label1_manager_flag,
		:other_label2_manager_flag,
		:other_label3_manager_flag,
		:insurance_payment_check_manager_flag,
		:settlement_letter_manager_flag,
		:valuation_report_manager_flag,
		:valuation_report_base_value_manager_flag,
		:valuation_report_vin_matches_manager_flag,
		:valuation_report_prior_damage_manager_flag,
		:valuation_report_misc_fee_manager_flag,
		:valuation_report_mileage_manager_flag,
		:valuation_report_dol_manager_flag,
		:valuation_report_type_manager_flag,
		:options_book_out_over_150_percent_manager_flag,
		:original_financing_manager_flag,
		:contract_number_matches_manager_flag,
		:bank_history_matches_manager_flag,
		:police_report_manager_flag,
		:insurance_deductible_addition_manager_flag,
		:insurance_deductible_subtraction_manager_flag,
		:bank_information_manager_flag,
		:full_loan_history_manager_flag,
		:loan_number_manager_flag,
		:payment_amount_manager_flag,
		:interest_rate_manager_flag,
		:first_payment_manager_flag,
		:contract_deal_date_manager_flag,
		:contract_term_months_manager_flag,
		:msrp_value_manager_flag,
		:insurance_company,
		:has_insurance_company,
		:policy_number,
		:has_policy_number,
		:mileage_deduction,
		:has_mileage_deduction,
		:nada,
		:has_nada,
		:has_valuation_nada_difference,
		:mileage_deduction_manager_flag,
		:has_estimate_with_photos,
		:nada_manager_flag,
		 now() at time zone 'utc',
		:is_cs_claim,
		:not_paid_by_cs,
                :vin_record_id) returning id`

	tx, err := db.Get().Beginx()
	if err != nil {
		return 0, errors.Wrap(err, "Database error beginning transaction for GAP claim create")
	}

	customerInsert := `insert into customers (
		first_name,
		last_name,
		is_business,
		business_name,
		email_address,
		phone_number,
		street_address,
		city,
		state,
		postal_code)

		values (
		:first_name,
		:last_name,
		:is_business,
		:business_name,
		:email_address,
		:phone_number,
		:street_address,
		:city,
		:state,
		:postal_code) returning id`
	// Prepare and execute customer info
	customerInsertStmt, err := tx.PrepareNamed(customerInsert)
	if err != nil {
		_ = tx.Rollback()
		return 0, errors.Wrap(err, "PrepareNamed failed for customer")
	}
	defer func() { _ = customerInsertStmt.Close() }()
	err = customerInsertStmt.Get(&gapClaim.CustomerID, gapClaim)
	if err != nil {
		_ = tx.Rollback()
		return 0, errors.Wrap(err, "Error creating customer")
	}

	// VIN Records
	vinExist := `select id from vin_records where vin = $1`
	err = tx.Get(&gapClaim.VinRecordID, vinExist, gapClaim.VIN)
	if err != nil && err != sql.ErrNoRows {
		_ = tx.Rollback()
		return 0, errors.Wrap(err, "Error getting vin_record")
	}

	if gapClaim.VinRecordID.Int64 == 0 {
		vinInsert := `insert into vin_records (created_at, vin, make, model, year, customer_id) values (
		now() at time zone 'utc', :vin, :make, :model, :year, :customer_id)
		returning id`
		vinInsertStmt, err := tx.PrepareNamed(vinInsert)
		if err != nil {
			_ = tx.Rollback()
			return 0, errors.Wrap(err, "PrepareNamed failed for vin_records")
		}
		defer func() { _ = vinInsertStmt.Close() }()

		err = vinInsertStmt.Get(&gapClaim.VinRecordID, gapClaim)
		if err != nil {
			_ = tx.Rollback()
			return 0, errors.Wrap(err, "Error creating vin_record")
		}
	}

	// Prepare and execute
	stmt, err := tx.PrepareNamed(claimInsert)
	if err != nil {
		_ = tx.Rollback()
		return 0, errors.Wrap(err, "PrepareNamed failed")
	}
	defer func() { _ = stmt.Close() }()
	id := 0
	err = stmt.Get(&id, gapClaim)
	if err != nil {
		_ = tx.Rollback()
		return id, errors.Wrap(err, "Scan error on ID after creating GAP claim")
	}

	// Add entry in update table for audit trail
	updateInsert := `insert into gap_claim_updates(gap_claim_id, updated_by_user_id, updated_at) values ($1, $2, now() at time zone 'utc')`
	_, err = tx.Exec(updateInsert, id, gapClaim.CreatedByUserID)
	if err != nil {
		_ = tx.Rollback()
		return id, errors.Wrap(err, "Error inserting gap_claim_updates")
	}

	// Add contracts
	contractInsert := `insert into gap_claim_contracts (gap_claim_id, contract_number, contract_code, contract_name,contract_value)
		values ($1,$2,$3,$4,$5)`

	for _, contract := range gapClaim.Contracts {
		_, err = tx.Exec(contractInsert, id, contract.ContractNumber, contract.ContractCode, contract.ContractName, contract.ContractValue)
		if err != nil {
			_ = tx.Rollback()
			return 0, errors.Wrap(err, "PrepareNamed failed for GAP claim contracts")
		}
	}

	err = tx.Commit()
	return id, err
}

func gapClaimFromReq(gapClaim *gapClaimPayload, req *http.Request) error {
	dec := json.NewDecoder(req.Body)
	err := dec.Decode(&gapClaim)
	return errors.Wrap(err, "decoding GAP Claim request failed")
}

// cleanGapClaim cleans up leading and trailing white-space etc...
func cleanGapClaim(gapClaim *gapClaimPayload) {
	gapClaim.VIN = strings.TrimSpace(gapClaim.VIN)
	gapClaim.Status = strings.TrimSpace(gapClaim.Status)
	gapClaim.StatusChangeDescription = strings.TrimSpace(gapClaim.StatusChangeDescription)
	gapClaim.ContractNumber = strings.TrimSpace(gapClaim.ContractNumber)
	gapClaim.WaitingFor = strings.TrimSpace(gapClaim.WaitingFor)
	gapClaim.FirstName = strings.TrimSpace(gapClaim.FirstName)
	gapClaim.LastName = strings.TrimSpace(gapClaim.LastName)
	gapClaim.State = strings.TrimSpace(gapClaim.State)
	gapClaim.OtherLabel1 = strings.TrimSpace(gapClaim.OtherLabel1)
	gapClaim.OtherLabel2 = strings.TrimSpace(gapClaim.OtherLabel2)
	gapClaim.OtherLabel3 = strings.TrimSpace(gapClaim.OtherLabel3)
	gapClaim.ValuationReportType = strings.TrimSpace(gapClaim.ValuationReportType)
	gapClaim.BankAccountNumber = strings.TrimSpace(gapClaim.BankAccountNumber)
	gapClaim.LastAction = strings.TrimSpace(gapClaim.LastAction)
	gapClaim.InsurancePolicyDeductibleReason = strings.TrimSpace(gapClaim.InsurancePolicyDeductibleReason)
	for i := range gapClaim.Contracts {
		gapClaim.Contracts[i].ContractCode = strings.TrimSpace(gapClaim.Contracts[i].ContractCode)
		gapClaim.Contracts[i].ContractNumber = strings.TrimSpace(gapClaim.Contracts[i].ContractNumber)
		gapClaim.Contracts[i].ContractName = strings.TrimSpace(gapClaim.Contracts[i].ContractName)
	}
	gapClaim.InsuranceCompany = strings.TrimSpace(gapClaim.InsuranceCompany)
	gapClaim.PolicyNumber = strings.TrimSpace(gapClaim.PolicyNumber)
}

// validateGapClaim validates a NewClaim record for correctness
func validateGapClaim(ctx context.Context, gapClaim *gapClaimPayload, requestType ClaimRequestType) (map[string]string, error) {
	formErrors := map[string]string{}

	switch requestType {
	case ClaimRequestCreate:
		if gapClaim.ContractNumber == "" {
			formErrors["contract_number"] = "Contract number is required"
		} else {
			id := 0
			err := db.Get().Get(&id, `select id from gap_claims where contract_number = $1`, gapClaim.ContractNumber)
			if err != nil && err != sql.ErrNoRows {
				return formErrors, errors.Wrap(err, "Database error in verifying uniqueness of claim")
			}
			if id != 0 {
				formErrors["claim_exists"] = "An earlier claim exists for this Contract."
			}
		}

	case ClaimRequestDeny:
		if gapClaim.StatusChangeDescription == "" {
			formErrors["status_change_description"] = "Reason for denial is required"
		}
	case ClaimRequestSubmit:
		if gapClaim.OwnerID == 0 {
			formErrors["owner_id"] = "OwnerID is required"
		}
		_, err := handlers.GetVendorID(ctx, gapClaim.BankAccountName, gapClaim.BankAddressZip, gapClaim.BankAddressStreet1)
		if err != nil {
			formErrors["bank_information"] = "Invalid bank information"
		}
	case ClaimRequestAuthorize:
		if gapClaim.CaseReserve.LessThanOrEqual(decimal.Zero) {
			formErrors["case_reserve"] = "Amount of claim should be greater than 0"
		}
		if gapClaim.Status != db.GapClaimStatusWaitingForAuthorization {
			formErrors["status"] = "The status should be WA ( Waiting for Authorization )"
		}
		if gapClaim.IsCSClaim && !gapClaim.NotPaidByCS {
			formErrors["cs_claim"] = "CS claim cannot be authorized"
		}
		if gapClaim.BankAccountNumber == "" {
			formErrors["bank_account_number"] = "Bank account number / loan number is required"
		}
		_, err := handlers.GetVendorID(ctx, gapClaim.BankAccountName, gapClaim.BankAddressZip, gapClaim.BankAddressStreet1)
		if err != nil {
			formErrors["bank_information"] = "Invalid bank information"
		}
		if gapClaim.LastName == "" {
			formErrors["last_name"] = "Last name is required"
		}
	case ClaimRequestCreateChild:
		if gapClaim.IsChild == true {
			formErrors["is_child"] = "Child claim cannot be created for a child claim"
		}
		if gapClaim.Status != db.GapClaimStatusCheckWritten {
			formErrors["invalid_status"] = "Parent claim status is invalid"
		}
		var childClaims []struct {
			ID     int    `db:"id"`
			Status string `db:"status"`
		}
		err := db.Get().Select(&childClaims, `select id, status from gap_claims where parent_claim_id = $1`, gapClaim.ID)
		if err != nil {
			formErrors["child_status"] = "could not verify other child status"
		}
		for _, child := range childClaims {
			if child.Status != db.GapClaimStatusCheckWritten {
				formErrors["child_status"] = "Other child status is invalid"
			}
		}
	}
	return formErrors, nil
}

// gapClaimSelect gets the gap claim record from db
func gapClaimSelect(id string) (*gapClaimPayload, error) {
	query := "select gap_claims.*, banks.*, customers.*" +
		"from gap_claims join customers on gap_claims.customer_id = customers.id " +
		"join banks on gap_claims.bank_id = banks.id " +
		"where gap_claims.id = $1 limit 1"

	gapClaim := gapClaimPayload{}
	err := db.Get().Unsafe().Get(&gapClaim, query, id)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.Wrap(err, "The GAP claim was not found")
		}
		return nil, errors.Wrap(err, "Error loading GAP claim from database.")
	}

	gapClaim.ID, err = strconv.Atoi(id)
	if err != nil {
		return nil, errors.Wrap(err, "Converting GAP claim id to number failed")
	}

	lastUpdateQuery := `select gap_claim_updates.updated_at, updated_by_user_id, first_name, last_name
	 from gap_claim_updates join users on updated_by_user_id = users.id
	 where gap_claim_id = $1 order by updated_at desc limit 1`
	lastUpdate := struct {
		UpdatedAt       time.Time `db:"updated_at"`
		UpdatedByUserID int       `db:"updated_by_user_id"`
		FirstName       string    `db:"first_name"`
		LastName        string    `db:"last_name"`
	}{}
	err = db.Get().Get(&lastUpdate, lastUpdateQuery, id)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.Wrap(err, "Update for the GAP claim was not found")
		}
		return nil, errors.Wrap(err, "Error loading GAP claim updates from database.")
	}
	gapClaim.UpdatedByUserID = lastUpdate.UpdatedByUserID
	gapClaim.UpdatedAt = lastUpdate.UpdatedAt
	gapClaim.UpdatedByUserName = strings.Join([]string{lastUpdate.FirstName, lastUpdate.LastName}, " ")

	// Get contracts
	getContractsQuery := `select contract_number, contract_code, contract_name, contract_flag, contract_value, manager_flag
	 from gap_claim_contracts
	 where gap_claim_id = $1`

	err = db.Get().Select(&gapClaim.Contracts, getContractsQuery, id)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.Wrap(err, "Contracts for the GAP claim was not found")
		}
		return nil, errors.Wrap(err, "Error loading GAP claim contracts from database.")
	}

	// Get child claims
	childClaimsQuery := `select id, status, child_claim_reason, case_reserve from gap_claims where parent_claim_id = $1 order by date_of_loss`

	err = db.Get().Select(&gapClaim.ChildClaims, childClaimsQuery, id)
	if err != nil && err != sql.ErrNoRows { // if no child claims found, that's not an error
		return nil, errors.Wrap(err, "Error loading GAP claim child claims from database.")
	}

	// Get recovery data
	recoveryQuery := `select status as recovery_status, check_amount as recovery_check_amount
	from gap_claim_recoveries where gap_claim_id = $1`
	err = db.Get().Get(&gapClaim, recoveryQuery, id)
	if err != nil && err != sql.ErrNoRows { // if no recovery found, that's not an error
		return nil, errors.Wrap(err, "Error loading GAP recovery data from database.")
	}
	if err == sql.ErrNoRows { // if no recovery found, set err to nil
		err = nil
	}

	return &gapClaim, err
}

// ClaimShow returns the gapclaim matching id
func ClaimShow(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	id := chi.URLParam(req, "id")
	// Get the gap claim from database
	gapClaim, err := gapClaimSelect(id)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting GAP claim from database", nil)
	}

	gapClaim.AvgCaseReserve, err = calculateAverageLastQuarterPayout(time.Now())
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error calculating case reserve of GAP claim", nil)
	}
	return http.StatusOK, map[string]interface{}{"gap_claim": gapClaim}
}

// ClaimUpdate updates a gap Claim
func ClaimUpdate(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	ctx := req.Context()

	id := chi.URLParam(req, "id")

	// Get the gap claim from database
	gapClaim, err := gapClaimSelect(id)
	if err != nil {
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting GAP claim from database", nil)
	}
	claimStatusBeforeUpdate := gapClaim.Status

	// using same object ensures, the object now has both fields, the ones need to modified and the ones need to
	// be retained
	err = gapClaimFromReq(gapClaim, req)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(nil, "Malformed GAP Claim data for update.", nil)
	}

	gapClaim.ID, _ = strconv.Atoi(id)

	cleanGapClaim(gapClaim)

	// Get bank id from bank information
	bankInfo := bankPayload{
		BankVendorID:       gapClaim.BankVendorID,
		BankAccountName:    gapClaim.BankAccountName,
		BankAddressStreet1: gapClaim.BankAddressStreet1,
		BankAddressStreet2: gapClaim.BankAddressStreet2,
		BankAddressCity:    gapClaim.BankAddressCity,
		BankAddressState:   gapClaim.BankAddressState,
		BankAddressZip:     gapClaim.BankAddressZip,
	}
	gapClaim.BankID, err = getBankID(&bankInfo)
	if err != nil {
		err = errors.Wrap(err, "Database error getting bank information")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error getting bank information", nil)
	}

	currentUser := user.ID

	// submit request, Waiting for authorization, WA or Pending denial PD
	if gapClaim.Status == db.GapClaimStatusWaitingForAuthorization || gapClaim.Status == db.GapClaimStatusPendingDenial {
		formErrors, err := validateGapClaim(ctx, gapClaim, ClaimRequestSubmit)
		if len(formErrors) > 0 {
			return http.StatusBadRequest, handlers.ErrorMessage(err, "Form validations errors.",
				map[string]interface{}{"errors": formErrors})
		}
	}
	// return request, RC
	if gapClaim.Status == db.GapClaimStatusReturnedForCorrections && claimStatusBeforeUpdate != db.GapClaimStatusReturnedForCorrections {
		formErrors, err := validateGapClaim(ctx, gapClaim, ClaimRequestReturn)
		if len(formErrors) > 0 {
			return http.StatusBadRequest, handlers.ErrorMessage(err, "Form validations errors.",
				map[string]interface{}{"errors": formErrors})
		}
		// In gap claim return, the owner is changed from the current owner to the one who submitted claim
		// to "GapClaimStatusWaitingForAuthorization" status
		lastOwner, err := getLastSubmittedByUser(gapClaim.ID)
		if err != nil {
			return http.StatusBadRequest, handlers.ErrorMessage(err, "Owner reassigning error", nil)
		}
		gapClaim.OwnerID = lastOwner
	}
	// deny request, D
	if gapClaim.Status == db.GapClaimStatusDeny {
		formErrors, err := validateGapClaim(ctx, gapClaim, ClaimRequestDeny)
		if len(formErrors) > 0 {
			return http.StatusBadRequest, handlers.ErrorMessage(err, "Form validations errors.",
				map[string]interface{}{"errors": formErrors})
		}
	}

	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		err = errors.Wrap(err, "Database error beginning transaction for GAP claim update")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error creating GAP claim update", nil)
	}

	if gapClaim.CalculateAmortizationValue {
		gapClaim.RunAmortizationSheetValue = getAmortizationValue(
			gapClaim.OriginalFinancingContractValue,
			gapClaim.InterestRate,
			gapClaim.PaymentAmount,
			gapClaim.ContractTermMonths,
			gapClaim.FirstPaymentDate,
			gapClaim.ContractDealDate,
			gapClaim.DateOfLoss,
			gapClaim.TotalDelinquentPaymentsCovered,
			gapClaim.TotalExtensionsCovered,
		)
	}

	if gapClaim.Status == db.GapClaimStatusWaitingForCheck && gapClaim.CaseReserve.LessThanOrEqual(decimal.Zero) {
		gapClaim.Status = db.GapClaimStatusNoGap
	}

	// TODO: Uncomment this when we want to bring back the auto recovery based on NADA value
	// Update recovery if valuation -nada > 0
	//autoUpdateRecovery := false
	//if gapClaim.NADA.Sub(gapClaim.ValuationReportAdjustments).GreaterThan(decimal.Zero) {
	//	gapClaim.HasRecovery = true
	//	err = insertRecovery(gapClaim.ID, user.ID, tx)
	//	if err != nil {
	//		_ = tx.Rollback()
	//		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error adding recovery", nil)
	//	}
	//	autoUpdateRecovery = true
	//} TODO:

	if claimStatusBeforeUpdate == db.GapClaimStatusWaitingForAuthorization || claimStatusBeforeUpdate == db.GapClaimStatusPendingDenial {
		err = gapClaimManagerUpdate(ctx, tx, gapClaim)
	} else {
		err = gapClaimAgentUpdate(ctx, tx, gapClaim)
	}
	if err != nil {
		_ = tx.Rollback()
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error updating GAP Claim", nil)
	}

	//Update gap_closed_at if the status is changed to closed
	if gapClaim.Status == db.GapClaimStatusDeny || gapClaim.Status == db.GapClaimStatusClosedNoResponse || gapClaim.Status == db.GapClaimStatusNoGap ||
		gapClaim.Status == db.GapClaimStatusCheckWritten || gapClaim.Status == db.GapClaimStatusCheckVoided {
		_, err = tx.ExecContext(ctx, `update gap_claims set gap_closed_at=now() at time zone 'utc' where id =$1`, gapClaim.ID)
		if err != nil {
			_ = tx.Rollback()
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Update of GAP closed failed", nil)
		}
	}

	if claimStatusBeforeUpdate != gapClaim.Status {
		recordNote := recordNotePayload{}
		recordNote.ID = gapClaim.ID
		recordNote.CreatedByUserID = currentUser
		recordNote.CreatedAt = time.Now()
		recordNote.NotesText = db.RecordNoteDescription[gapClaim.Status]
		_, err := insertRecordNote(&recordNote, tx)
		if err != nil {
			_ = tx.Rollback()
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error adding notes for gap claim", nil)
		}
	}

	// TODO: Uncomment this when we want to bring back the auto recovery based on NADA value
	//if autoUpdateRecovery {
	//	// Add recovery reason as a field not in Recovery Review
	//	fieldNote := fieldNotePayload{}
	//	fieldNote.GapClaimID = gapClaim.ID
	//	fieldNote.FieldID = db.FieldIDRecoveryReview
	//	fieldNote.CreatedByUserID = currentUser
	//	fieldNote.NotesText = db.GapClaimRecoveryNoteAdded + ": Auto update of recovery"
	//
	//	_, err = createFieldNote(&fieldNote, tx)
	//	if err != nil {
	//		_ = tx.Rollback()
	//		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error adding item notes for gap claim recovery", nil)
	//	}
	//} TODO:

	// Delete and add contracts
	contractDelete := `delete from gap_claim_contracts where gap_claim_id=$1`
	_, err = tx.ExecContext(ctx, contractDelete, gapClaim.ID)
	if err != nil {
		_ = tx.Rollback()
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Update GAP claim contracts failed in deleting", nil)
	}

	contractInsert := `insert into gap_claim_contracts (gap_claim_id, contract_number, contract_code, contract_name, contract_flag, contract_value, manager_flag)
		values ($1,$2,$3,$4,$5,$6,$7)`
	for _, contract := range gapClaim.Contracts {
		_, err = tx.ExecContext(ctx, contractInsert, gapClaim.ID, contract.ContractNumber, contract.ContractCode, contract.ContractName, contract.ContractFlag, contract.ContractValue, contract.ManagerFlag)
		if err != nil {
			_ = tx.Rollback()
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Update GAP claim contracts failed in adding", nil)
		}
	}

	// Add entry in update table for audit trail
	updateInsert := `insert into gap_claim_updates(gap_claim_id, updated_by_user_id, updated_at) values ($1, $2, now() at time zone 'utc')`
	_, err = tx.ExecContext(ctx, updateInsert, gapClaim.ID, currentUser)
	if err != nil {
		_ = tx.Rollback()
		err = errors.Wrap(err, "Error inserting gap_claim_updates")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error inserting gap_claim_updates", nil)
	}

	// authorize request, submitToIntacct, WC
	if gapClaim.Status == db.GapClaimStatusWaitingForCheck && claimStatusBeforeUpdate != db.GapClaimStatusWaitingForCheck && !(gapClaim.IsCSClaim && !gapClaim.NotPaidByCS) {
		err = gapClaimAuthorize(ctx, tx, gapClaim, currentUser)
		if err != nil {
			_ = tx.Rollback()
			handlers.ReportError(req, err)
			if isCustomError(err) {
				return http.StatusInternalServerError, handlers.ErrorMessage(err, err.Error(), nil)
			}
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error creating GAP claim authorization", nil)
		}
	}
	err = tx.Commit()
	if err != nil {
		err = errors.Wrap(err, "Database error committing transaction for GAP claim update")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error creating GAP claim authorization", nil)
	}

	return http.StatusOK, map[string]interface{}{"gap_claim_id": id}
}

func getLastSubmittedByUser(claimID int) (int, error) {
	userID := 0
	query := `select created_by_user_id from record_notes where gap_claim_id = $1 and (notes_text = $2 or notes_text = $3 or notes_text = $4 or notes_text = $5) order by created_at desc limit 1`
	err := db.Get().Get(&userID, query, claimID, db.RecordNoteDescription[db.GapClaimStatusWaitingForAuthorization], db.RecordNoteDescription[db.GapClaimStatusPendingDenial], db.RecordNoteDescription[db.GapClaimStatusNoGap], db.RecordNoteDescription[db.GapClaimStatusClosedNoResponse])
	if err != nil {
		err = errors.Wrap(err, "Database error getting record notes for claim")
	}
	return userID, err
}

func gapClaimAgentUpdate(ctx context.Context, tx *sqlx.Tx, gapClaim *gapClaimPayload) error {
	query := `update gap_claims
		set status = :status,
			status_change_description = :status_change_description,
			date_of_loss = :date_of_loss,
			date_of_last_in = :date_of_last_in,
			date_of_last_out = :date_of_last_out,
			waiting_for = :waiting_for,
			case_reserve = :case_reserve,
			run_amortization_sheet_value = :run_amortization_sheet_value,
			calculate_amortization_value = :calculate_amortization_value,
			has_canceled_contracts = :has_canceled_contracts,
			other_label1 = :other_label1,
			other_value1 = :other_value1,
			other_label2 = :other_label2,
			other_value2 = :other_value2,
			other_label3 = :other_label3,
			other_value3 = :other_value3,
			has_settlement_amount = :has_settlement_amount,
			settlement_amount = :settlement_amount,
			has_negative_equity_amount = :has_negative_equity_amount,
			negative_equity_amount = :negative_equity_amount,
			has_number_of_delinquent_payments = :has_number_of_delinquent_payments,
			number_of_delinquent_payments = :number_of_delinquent_payments,
			has_total_delinquent_payments_covered = :has_total_delinquent_payments_covered,
			total_delinquent_payments_covered = :total_delinquent_payments_covered,
			has_number_of_extensions = :has_number_of_extensions,
			number_of_extensions = :number_of_extensions,
			has_total_extensions_covered = :has_total_extensions_covered,
			total_extensions_covered = :total_extensions_covered,
			has_insurance_check_amount = :has_insurance_check_amount,
			insurance_check_amount = :insurance_check_amount,
			is_valuation_report_available = :is_valuation_report_available,
			valuation_report_adjustments = :valuation_report_adjustments,
			is_valuation_report_matches_base_value = :is_valuation_report_matches_base_value,
			valuation_report_matches_base_value = :valuation_report_matches_base_value,
			valuation_report_vin_matches = :valuation_report_vin_matches,
			has_valuation_report_prior_damage = :has_valuation_report_prior_damage,
			valuation_report_prior_damage_value = :valuation_report_prior_damage_value,
			has_valuation_report_misc_fee = :has_valuation_report_misc_fee,
			valuation_report_misc_fee_value = :valuation_report_misc_fee_value,
			valuation_report_mileage = :valuation_report_mileage,
			valuation_report_type = :valuation_report_type,
			has_options_match_book_out_over_150_percent = :has_options_match_book_out_over_150_percent,
			over_150_percent = :over_150_percent,
			has_original_financing_contract = :has_original_financing_contract,
			original_financing_contract_value = :original_financing_contract_value,
			has_msrp_value = :has_msrp_value,
			msrp_value = :msrp_value,
			contract_number_matches = :contract_number_matches,
			bank_history_matches = :bank_history_matches,
			is_police_report_available = :is_police_report_available,
			has_insurance_policy_deductible = :has_insurance_policy_deductible,
			insurance_policy_deductible_value_addition = :insurance_policy_deductible_value_addition,
			insurance_policy_deductible_value_subtraction = :insurance_policy_deductible_value_subtraction,
			insurance_policy_deductible_reason = :insurance_policy_deductible_reason,
			has_bank_information = :has_bank_information,
			is_full_loan_history_available = :is_full_loan_history_available,
			payment_amount = :payment_amount,
			interest_rate = :interest_rate,
			first_payment_date = :first_payment_date,
			has_new_bank_information = :has_new_bank_information,
			bank_id = :bank_id,
			bank_account_number = :bank_account_number,
			contract_deal_date = :contract_deal_date,
			contract_term_months = :contract_term_months,
			last_action = :last_action,
			owner_id = :owner_id,
			child_claim_reason = :child_claim_reason,
			cs_check_amount = :cs_check_amount,
			insurance_company = :insurance_company,
			has_insurance_company = :has_insurance_company,
			policy_number = :policy_number,
			has_policy_number = :has_policy_number,
			mileage_deduction = :mileage_deduction,
			has_mileage_deduction = :has_mileage_deduction,
			nada = :nada,
			has_nada = :has_nada,
			has_valuation_nada_difference = :has_valuation_nada_difference,
			has_estimate_with_photos = :has_estimate_with_photos,
			not_paid_by_cs = :not_paid_by_cs,
			is_in_progress = :is_in_progress
			where id = :id`

	stmt, err := tx.PrepareNamedContext(ctx, query)
	if err != nil {
		return errors.Wrap(err, "Error updating GAP claim, PrepareNamed failed")
	}
	defer func() { _ = stmt.Close() }()

	// update the WaitingFor field of GapClaim based on Check list status
	updateWaitingFor(gapClaim)

	_, err = stmt.ExecContext(ctx, gapClaim)
	if err != nil {
		return errors.Wrap(err, "Error updating GAP claim, database error")
	}
	return nil
}

func gapClaimManagerUpdate(ctx context.Context, tx *sqlx.Tx, gapClaim *gapClaimPayload) error {
	query := `update gap_claims
		set status = :status,
			status_change_description = :status_change_description,
			date_of_loss = :date_of_loss,
			date_of_last_in = :date_of_last_in,
			date_of_last_out = :date_of_last_out,
			waiting_for = :waiting_for,
			case_reserve = :case_reserve,
			run_amortization_sheet_value = :run_amortization_sheet_value,
			calculate_amortization_value = :calculate_amortization_value,
			has_canceled_contracts = :has_canceled_contracts,
			other_label1 = :other_label1,
			other_value1 = :other_value1,
			other_label2 = :other_label2,
			other_value2 = :other_value2,
			other_label3 = :other_label3,
			other_value3 = :other_value3,
			has_settlement_amount = :has_settlement_amount,
			settlement_amount = :settlement_amount,
			has_negative_equity_amount = :has_negative_equity_amount,
			negative_equity_amount = :negative_equity_amount,
			has_number_of_delinquent_payments = :has_number_of_delinquent_payments,
			number_of_delinquent_payments = :number_of_delinquent_payments,
			has_total_delinquent_payments_covered = :has_total_delinquent_payments_covered,
			total_delinquent_payments_covered = :total_delinquent_payments_covered,
			has_number_of_extensions = :has_number_of_extensions,
			number_of_extensions = :number_of_extensions,
			has_total_extensions_covered = :has_total_extensions_covered,
			total_extensions_covered = :total_extensions_covered,
			has_insurance_check_amount = :has_insurance_check_amount,
			insurance_check_amount = :insurance_check_amount,
			is_valuation_report_available = :is_valuation_report_available,
			valuation_report_adjustments = :valuation_report_adjustments,
			is_valuation_report_matches_base_value = :is_valuation_report_matches_base_value,
			valuation_report_matches_base_value = :valuation_report_matches_base_value,
			valuation_report_vin_matches = :valuation_report_vin_matches,
			has_valuation_report_prior_damage = :has_valuation_report_prior_damage,
			valuation_report_prior_damage_value = :valuation_report_prior_damage_value,
			has_valuation_report_misc_fee = :has_valuation_report_misc_fee,
			valuation_report_misc_fee_value = :valuation_report_misc_fee_value,
			valuation_report_mileage = :valuation_report_mileage,
			valuation_report_type = :valuation_report_type,
			has_options_match_book_out_over_150_percent = :has_options_match_book_out_over_150_percent,
			over_150_percent = :over_150_percent,
			has_original_financing_contract = :has_original_financing_contract,
			original_financing_contract_value = :original_financing_contract_value,
			has_msrp_value = :has_msrp_value,
			msrp_value = :msrp_value,
			contract_number_matches = :contract_number_matches,
			bank_history_matches = :bank_history_matches,
			is_police_report_available = :is_police_report_available,
			has_insurance_policy_deductible = :has_insurance_policy_deductible,
			insurance_policy_deductible_value_addition = :insurance_policy_deductible_value_addition,
			insurance_policy_deductible_value_subtraction = :insurance_policy_deductible_value_subtraction,
			insurance_policy_deductible_reason = :insurance_policy_deductible_reason,
			has_bank_information = :has_bank_information,
			is_full_loan_history_available = :is_full_loan_history_available,
			payment_amount = :payment_amount,
			interest_rate = :interest_rate,
			first_payment_date = :first_payment_date,
			has_new_bank_information = :has_new_bank_information,
			bank_id = :bank_id,
			bank_account_number = :bank_account_number,
			contract_deal_date = :contract_deal_date,
			contract_term_months = :contract_term_months,
			last_action = :last_action,
			owner_id = :owner_id,
			child_claim_reason = :child_claim_reason,
			run_amortization_manager_flag = :run_amortization_manager_flag,
			other_label1_manager_flag = :other_label1_manager_flag,
			other_label2_manager_flag = :other_label2_manager_flag,
			other_label3_manager_flag = :other_label3_manager_flag,
			insurance_payment_check_manager_flag = :insurance_payment_check_manager_flag,
			settlement_letter_manager_flag = :settlement_letter_manager_flag,
			has_negative_equity_amount_manager_flag = :has_negative_equity_amount_manager_flag,			
			number_of_delinquent_payments_manager_flag = :number_of_delinquent_payments_manager_flag,
			total_delinquent_payments_covered_manager_flag = :total_delinquent_payments_covered_manager_flag,
			number_of_extensions_manager_flag = :number_of_extensions_manager_flag,
			total_extensions_covered_manager_flag = :total_extensions_covered_manager_flag,
			valuation_report_manager_flag = :valuation_report_manager_flag,
			valuation_report_base_value_manager_flag = :valuation_report_base_value_manager_flag,
			valuation_report_vin_matches_manager_flag = :valuation_report_vin_matches_manager_flag,
			valuation_report_prior_damage_manager_flag = :valuation_report_prior_damage_manager_flag,
			valuation_report_misc_fee_manager_flag = :valuation_report_misc_fee_manager_flag,
			valuation_report_mileage_manager_flag = :valuation_report_mileage_manager_flag,
			valuation_report_dol_manager_flag = :valuation_report_dol_manager_flag,
			valuation_report_type_manager_flag = :valuation_report_type_manager_flag,
			options_book_out_over_150_percent_manager_flag = :options_book_out_over_150_percent_manager_flag,
			original_financing_manager_flag = :original_financing_manager_flag,
			contract_number_matches_manager_flag = :contract_number_matches_manager_flag,
			bank_history_matches_manager_flag = :bank_history_matches_manager_flag,
			police_report_manager_flag = :police_report_manager_flag,
			insurance_deductible_addition_manager_flag = :insurance_deductible_addition_manager_flag,
			insurance_deductible_subtraction_manager_flag = :insurance_deductible_subtraction_manager_flag,
			bank_information_manager_flag = :bank_information_manager_flag,
			full_loan_history_manager_flag = :full_loan_history_manager_flag,
			loan_number_manager_flag = :loan_number_manager_flag,
			payment_amount_manager_flag = :payment_amount_manager_flag,
			interest_rate_manager_flag = :interest_rate_manager_flag,
			first_payment_manager_flag = :first_payment_manager_flag,
			contract_deal_date_manager_flag = :contract_deal_date_manager_flag,
			contract_term_months_manager_flag = :contract_term_months_manager_flag,
			msrp_value_manager_flag = :msrp_value_manager_flag,
			cs_check_amount = :cs_check_amount,
			insurance_company = :insurance_company,
			has_insurance_company = :has_insurance_company,
			policy_number = :policy_number,
			has_policy_number = :has_policy_number,
			mileage_deduction = :mileage_deduction,
			has_mileage_deduction = :has_mileage_deduction,
			nada = :nada,
			has_nada = :has_nada,
			has_valuation_nada_difference = :has_valuation_nada_difference,
			mileage_deduction_manager_flag = :mileage_deduction_manager_flag,
			has_estimate_with_photos = :has_estimate_with_photos,
			nada_manager_flag = :nada_manager_flag,
			not_paid_by_cs = :not_paid_by_cs,
			is_in_progress = :is_in_progress
			where id = :id `

	stmt, err := tx.PrepareNamedContext(ctx, query)
	if err != nil {
		return errors.Wrap(err, "Error updating GAP claim, PrepareNamed failed")
	}
	defer func() { _ = stmt.Close() }()

	_, err = stmt.ExecContext(ctx, gapClaim)
	if err != nil {
		return errors.Wrap(err, "Error updating GAP claim, database error")
	}
	return nil
}

func isCustomError(e interface{}) bool {
	switch e.(type) {
	case *intacctError:
		return true
	default:
		return false
	}
}

func gapClaimAuthorize(ctx context.Context, tx *sqlx.Tx, gapClaim *gapClaimPayload, userID int) error {

	authQuery := `insert into gap_claim_payments(gap_claim_id) values($1) returning authorization_number`
	row := tx.QueryRow(authQuery, gapClaim.ID)
	authNumber := 0
	err := row.Scan(&authNumber)
	if err != nil {
		return errors.Wrap(err, "Database error inserting new payment request")
	}

	gapClaimPayment, err := submitToIntacct(ctx, tx, gapClaim, authNumber)
	if err != nil {
		// If custom error return as it is we dont want to change the message
		if isCustomError(err) {
			return err
		}
		return errors.Wrap(err, "Submit to Intacct failed")
	}

	updateQuery := `update gap_claim_payments set batch_key = $1, bill_key = $2, bill_memo = $3 where authorization_number = $4`
	_, err = tx.Exec(
		updateQuery,
		gapClaimPayment.BatchKey,
		gapClaimPayment.BillKey,
		gapClaimPayment.BillMemo,
		authNumber,
	)
	if err != nil {
		return errors.Wrap(err, "Database error updating gap payment details")
	}

	updateClaimStatus := `update gap_claims set status = $1 where id = $2`
	_, err = tx.Exec(updateClaimStatus, db.GapClaimStatusWaitingForCheck, gapClaim.ID)
	if err != nil {
		return errors.Wrap(err, "Error updating GAP claim status")
	}

	// Add entry in update table for audit trail
	updateInsert := `insert into gap_claim_updates(gap_claim_id, updated_by_user_id, updated_at) values ($1, $2, now() at time zone 'utc')`
	_, err = tx.Exec(updateInsert, gapClaim.ID, userID)
	if err != nil {
		return errors.Wrap(err, "Error inserting gap_claim_updates")
	}
	return err
}

func getBankID(bankInfo *bankPayload) (int, error) {
	cleanBank(bankInfo)
	insertQuery := `insert into banks(bank_vendor_id, bank_account_name, bank_address_street1, bank_address_street2,
			bank_address_city, bank_address_state, bank_address_zip) values($1,$2,$3,$4,$5,$6,$7)
			on conflict(bank_account_name, bank_address_zip, bank_address_street1) do
			update set bank_vendor_id=$1, bank_address_street1=$3, bank_address_street2=$4, bank_address_city=$5, bank_address_state=$6
			returning id`
	id := 0
	err := db.Get().Get(&id, insertQuery, bankInfo.BankVendorID, bankInfo.BankAccountName, bankInfo.BankAddressStreet1,
		bankInfo.BankAddressStreet2, bankInfo.BankAddressCity, bankInfo.BankAddressState,
		bankInfo.BankAddressZip)
	if err != nil {
		return id, errors.Wrap(err, "Error creating bank")
	}
	return id, err
}

func cleanBank(bankInfo *bankPayload) {
	bankInfo.BankVendorID = strings.TrimSpace(bankInfo.BankVendorID)
	bankInfo.BankAccountName = strings.TrimSpace(bankInfo.BankAccountName)
	bankInfo.BankAddressStreet1 = strings.TrimSpace(bankInfo.BankAddressStreet1)
	bankInfo.BankAddressStreet2 = strings.TrimSpace(bankInfo.BankAddressStreet2)
	bankInfo.BankAddressCity = strings.TrimSpace(bankInfo.BankAddressCity)
	bankInfo.BankAddressState = strings.TrimSpace(bankInfo.BankAddressState)
	bankInfo.BankAddressZip = strings.TrimSpace(bankInfo.BankAddressZip)
}

// Update the WaitingFor field of GapClaim based on which items from the checklist are pending for the GapClaim
// e.g if HasValuationReportPriorDamage, ValuationReportMileage and IsPoliceReportAvailable are not checked and rest of
// fields are checked the WaitingFor will have a value -> '1a,1c,3,'
func updateWaitingFor(gapClaim *gapClaimPayload) {
	waitingFor := []string{}
	waitingForDescription := []string{}
	if !gapClaim.HasInsuranceCheckAmount {
		waitingFor = append(waitingFor, db.WaitingForInsuranceCheck)
	}
	if !gapClaim.HasSettlementAmount {
		waitingFor = append(waitingFor, db.WaitingForSettlementAmount)
	}
	if !gapClaim.IsValuationReportAvailable {
		waitingFor = append(waitingFor, db.WaitingForValuationReport)
	}
	if !gapClaim.HasOriginalFinancingContract {
		waitingFor = append(waitingFor, db.WaitingForOriginalFinancingContract)
	}
	if !gapClaim.ContractNumberMatches {
		waitingFor = append(waitingFor, db.WaitingForContractNumberMatches)
	}
	if !gapClaim.IsPoliceReportAvailable {
		waitingFor = append(waitingFor, db.WaitingForPoliceReport)
	}
	if !gapClaim.HasInsurancePolicyDeductible {
		waitingFor = append(waitingFor, db.WaitingForInsurancePolicyDeductible)
	}
	if !gapClaim.HasBankInformation {
		waitingFor = append(waitingFor, db.WaitingForBankInformation)
	}
	if !gapClaim.IsFullLoanHistoryAvailable {
		waitingFor = append(waitingFor, db.WaitingForFullLoanHistory)
	}
	for _, cancelContract := range gapClaim.Contracts {
		if cancelContract.ContractCode == db.ProductCodeService && !cancelContract.ContractFlag {
			waitingFor = append(waitingFor, db.WaitingForCancelContractService)
		}
		if cancelContract.ContractCode == db.ProductCodeMaintenance && !cancelContract.ContractFlag {
			waitingFor = append(waitingFor, db.WaitingForCancelContractMaintenance)
		}
		if cancelContract.ContractCode == db.ProductCodeKeyReplacement && !cancelContract.ContractFlag {
			waitingFor = append(waitingFor, db.WaitingForCancelContractKey)
		}
		if cancelContract.ContractCode == db.ProductCodeTireWheel && !cancelContract.ContractFlag {
			waitingFor = append(waitingFor, db.WaitingForCancelContractTireWheel)
		}
	}

	gapClaim.WaitingFor = strings.Join(waitingFor, ",")
	gapClaim.WaitingForDescription = strings.Join(waitingForDescription, "\n")
}

type recordNotePayload struct {
	ID              int       `json:"id" db:"gap_claim_id"`
	NotesText       string    `json:"notes_text" db:"notes_text"`
	CreatedAt       time.Time `json:"created_at" db:"created_at"`
	CreatedByUserID int       `json:"created_by_user_id" db:"created_by_user_id"`
}

// RecordNoteCreate creates a new Gap Claim
func RecordNoteCreate(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	recordNote, err := recordNoteFromReq(req)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(nil, "Malformed Record Note data for create.", nil)
	}

	cleanRecordNote(recordNote)
	formErrors, err := validateRecordNote(recordNote)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(errors.Wrap(err, "Error validating Record Note"),
			"An error occurred validating the form values.", nil)
	}

	if len(formErrors) > 0 {
		return http.StatusBadRequest, handlers.ErrorMessage(nil, "Form validations errors.",
			map[string]interface{}{"errors": formErrors})
	}

	recordNote.CreatedByUserID = user.ID
	recordNote.CreatedAt = time.Now()

	noteID, err := insertRecordNote(recordNote, nil)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error inserting RecordNote", nil)
	}

	return http.StatusOK, map[string]interface{}{"record_note_id": noteID}
}

func insertRecordNote(recordNote *recordNotePayload, tx *sqlx.Tx) (int, error) {
	insertQuery := `insert into record_notes (created_at,gap_claim_id,notes_text,created_by_user_id) values (now() at time zone 'utc',:gap_claim_id,:notes_text,:created_by_user_id) returning id`
	id := 0

	var stmt *sqlx.NamedStmt
	var err error
	if tx != nil {
		stmt, err = tx.PrepareNamed(insertQuery)
	} else {
		stmt, err = db.Get().PrepareNamed(insertQuery)
	}
	if err != nil {
		return id, errors.Wrap(err, "An error occurred in PrepareNamed function while adding RecordNote.")
	}
	defer func() { _ = stmt.Close() }()

	err = stmt.Get(&id, recordNote)
	if err != nil {
		return id, errors.Wrap(err, "An error occurred while trying to add the RecordNote to the database.")
	}

	return id, err
}

// RecordNoteIndex returns a list of record notes for given contract number
func RecordNoteIndex(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	contractNumber := chi.URLParam(req, "gap_claim_id")
	recordNotes := []struct {
		GapClaimID      int       `json:"gap_claim_id" db:"gap_claim_id"`
		NotesText       string    `json:"notes_text" db:"notes_text"`
		CreatedAt       time.Time `json:"created_at" db:"created_at"`
		CreatedByUserID int       `json:"created_by_user_id" db:"created_by_user_id"`
		FirstName       string    `json:"first_name" db:"first_name"`
		LastName        string    `json:"last_name" db:"last_name"`
	}{}

	args := []interface{}{}
	args = append(args, contractNumber)

	autoNotesQuery := `select gap_claim_id, notes_text, record_notes.created_at, created_by_user_id, first_name, last_name
						from record_notes
						join users on (record_notes.created_by_user_id = users.id)
						where gap_claim_id = $1`

	custNotesQuery := ` union
		select gap_claims.id, customers_record_notes.notes_text, customers_record_notes.created_at, customers_record_notes.created_by_user_id, first_name, last_name
		from customers_record_notes
			join gap_claims on customers_record_notes.customer_id = gap_claims.customer_id
			join users on (customers_record_notes.created_by_user_id = users.id)
		where gap_claims.id = $1`

	orderOffsetClause := ` order by created_at desc limit $2 offset $3`

	var listQuery string
	var countQuery string
	listQuery = autoNotesQuery + custNotesQuery + orderOffsetClause
	countQuery = fmt.Sprintf("select count(*) from (%s %s) as co", autoNotesQuery, custNotesQuery)

	args = append(args, handlers.PerPageEntries)
	args = append(args, (handlers.GetPage(req)-1)*handlers.PerPageEntries)

	err := db.Get().Select(&recordNotes, listQuery, args...)
	if err != nil {
		errors.Wrap(err, "Database error getting record notes lists")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting record notes lists data", nil)
	}

	count := 0
	err = db.Get().Get(&count, countQuery, args[0])
	if err != nil {
		errors.Wrap(err, "Database error getting record notes lists count")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting record notes lists count", nil)
	}
	return http.StatusOK, map[string]interface{}{"record_notes": recordNotes, "count": count}
}

func recordNoteFromReq(req *http.Request) (*recordNotePayload, error) {
	recordNote := recordNotePayload{}

	dec := json.NewDecoder(req.Body)
	err := dec.Decode(&recordNote)

	return &recordNote, errors.Wrap(err, "decoding RecordNote request failed")
}

// cleanRecordNote cleans up leading and trailing white-space etc...
func cleanRecordNote(recordNote *recordNotePayload) {
	recordNote.NotesText = strings.TrimSpace(recordNote.NotesText)
}

// validateRecordNote validates a NewClaim record for correctness
func validateRecordNote(recordNote *recordNotePayload) (map[string]string, error) {
	formErrors := map[string]string{}

	if recordNote.ID == 0 {
		formErrors["gap_claim_id"] = "GAP claim id is required"
	}
	return formErrors, nil
}

// Returns the date range of last quarter for a given claim date
// e.g. if claim date is April 15, 2016, the function will return 2016-01-01 and 2016-03-31
func getLastQuarterDateRange(claimDate time.Time) (string, string) {

	// Quarter starts on Jan 1, April 1, July 1 and Oct 1
	quarterStartDates := []string{"01-01", "04-01", "07-01", "10-01"}
	// Quarter ends on March 31, June 30, Sept 30 and Dec 31
	quarterEndDates := []string{"03-31", "06-30", "09-30", "12-31"}

	prevQuarter := ((claimDate.Month() - 1) / 3) - 1 // 0 indexed quarter

	year := claimDate.Year()

	// For a date falling in Q1, the quarter is last year's Q4 (0 indexed quarter)
	if prevQuarter == -1 {
		prevQuarter = 3
		year = year - 1
	}

	quarterStartDate := strconv.Itoa(year) + "-" + quarterStartDates[prevQuarter]
	quarterEndDate := strconv.Itoa(year) + "-" + quarterEndDates[prevQuarter]

	return quarterStartDate, quarterEndDate
}

// Calculates average payment made towards gap claims for a given range
// uses avg query of psql
func calculateAverageLastQuarterPayout(claimDate time.Time) (decimal.Decimal, error) {
	quarterStartDate, quarterEndDate := getLastQuarterDateRange(claimDate)
	count := 0
	avg := decimal.NewFromFloat(0.0)

	err := db.Get().Get(&count, `select count(*) from gap_claim_payments where paid_date between $1 and $2`,
		quarterStartDate, quarterEndDate)
	if err != nil {
		return avg, errors.Wrap(err, "Database error lookup up GAP Claim Payments")
	}
	if count == 0 {
		return avg, nil
	}
	err = db.Get().Get(&avg, `select avg(amount) from gap_claim_payments where paid_date between $1 and $2`,
		quarterStartDate, quarterEndDate)
	if err != nil {
		return avg, errors.Wrap(err, "Database error getting average of GAP Claim Payments")
	}
	return avg, nil
}

// ClaimByContract returns the gapclaim matching GapContract#
func ClaimByContract(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	contractNumber := chi.URLParam(req, "id")
	// Get the gap claim from database
	query := "select * from gap_claims where contract_number = $1 and is_child=false limit 1"
	gapClaim := gapClaimPayload{}

	err := db.Get().Unsafe().Get(&gapClaim, query, contractNumber)
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage(err, "The GAP claim was not found", nil)
		}
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error loading GAP claim from database.", nil)
	}

	return http.StatusOK, map[string]interface{}{"gap_claim": gapClaim}
}

// ClaimPayment returns the payment details for the gap claim
func ClaimPayment(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	id := chi.URLParam(req, "id")

	gapClaim, err := gapClaimSelect(id)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Gap claim is not valid", nil)
	}

	query := ""
	if gapClaim.Status == db.GapClaimStatusWaitingForCheck && !(gapClaim.IsCSClaim && !gapClaim.NotPaidByCS) {
		query = "select authorization_number from gap_claim_payments where gap_claim_id = $1 order by id desc limit 1"
	} else if gapClaim.Status == db.GapClaimStatusCheckWritten || gapClaim.Status == db.GapClaimStatusCheckVoided {
		query = "select authorization_number, check_number, amount, paid_date from gap_claim_payments where gap_claim_id = $1 order by id desc limit 1"
	} else {
		return http.StatusBadRequest, handlers.ErrorMessage(errors.New("Invalid payment request for gap claim"), "Invalid request", nil)
	}
	// Get the gap claim payment information from database
	gapClaimPayments := gapClaimPaymentPayload{}
	err = db.Get().Get(&gapClaimPayments, query, id)

	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "The GAP claim payment was not found", nil)
		}
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error loading GAP claim payments from database.", nil)
	}

	return http.StatusOK, map[string]interface{}{"gap_claim_payment": gapClaimPayments}
}

func gapClaimExists(gapClaimID int) (bool, error) {
	id := 0
	err := db.Get().Get(&id, `select id from gap_claims where id=$1`, gapClaimID)
	if err != nil {
		if err == sql.ErrNoRows {
			return false, nil
		}
		return false, err
	}
	return true, nil
}

func cancelContractExists(gapClaimID int, contractNumber, contractCode string) (bool, error) {
	id := 0
	err := db.Get().Get(&id, `select id from gap_claim_contracts where gap_claim_id = $1 and
	contract_number = $2 and contract_code = $3`, gapClaimID, contractNumber, contractCode)
	if err != nil {
		if err == sql.ErrNoRows {
			return false, nil
		}
		return false, err
	}
	return true, nil
}

func getSubmittedClaimsByDates(startDate, endDate time.Time) ([]gapClaimPayload, error) {
	claims := []gapClaimPayload{}

	selectQuery := `select gap_claims.contract_number, customers.last_name, customers.first_name, gap_claims.payment_amount
	from gap_claims join customers on gap_claims.customer_id = customers.id
	where gap_claims.id IN (
	select record_notes.gap_claim_id from record_notes where record_notes.notes_text = $1
	and record_notes.created_at between $2 and  $3)`

	err := db.Get().Select(&claims, selectQuery, db.RecordNoteDescription[db.GapClaimStatusWaitingForCheck], startDate, endDate)
	if err != nil {
		return claims, errors.Wrap(err, "Database error getting GAP claims lists")
	}

	return claims, err
}

// getAmortizationValue calculates remaining balance amount to be paid by the customer till payoff date
// Remaining balance is calculated using following formula
// balance = (prev_balance + interest)
// Where
// prev_balance = prev_balance - principal_paid
// principal_paid = monthly_payment - interest
// interest = prev_balance * interest_rate / 365 * duration_in_days
// duration_in_days is number of days between last_payment_date and current date
// Assumptions : Current date is considered as payoff date
func getAmortizationValue(principalAmount, interestRate, firstPayment decimal.Decimal,
	term int,
	firstPaymentDate, dealDate, payoffDate time.Time,
	totalDelinquentPaymentsCovered, totalExtensionsCovered decimal.Decimal) decimal.Decimal {
	if !interestRate.GreaterThan(decimal.Zero) || !principalAmount.GreaterThan(decimal.Zero) || term <= 0 {
		return decimal.Zero
	}

	interest := interestRate.Div(decimal.NewFromFloat(100))

	// Calculate remaining principal balance amount after first payment
	duration := getTimeDifferenceInDays(firstPaymentDate, dealDate)           // Duration in days between first payment date and loan date
	interestAccrued := calculateInterest(principalAmount, interest, duration) // Interest amount on first payment
	interestPaid := decimal.Min(firstPayment, interestAccrued)
	interestBalance := interestAccrued.Sub(interestPaid)
	principalPaid := firstPayment.Sub(interestPaid)
	balanceAmount := principalAmount.Sub(principalPaid) // Remaining principal amount after first payment

	basicAmortization := runAmortization(firstPaymentDate, firstPaymentDate, payoffDate, interest, firstPayment, balanceAmount, interestBalance, 1, term-1).Round(2)

	withDelinqAndExtensions := basicAmortization.Add(totalDelinquentPaymentsCovered).Add(totalExtensionsCovered)

	return withDelinqAndExtensions
}

func runAmortization(prevPaymentDate, firstPaymentDate, payoffDate time.Time,
	interest, monthlyPayment, remainingPrincipalBalance, interestBalance decimal.Decimal,
	noOfPaymentsMade, term int) decimal.Decimal {
	nextPaymentDate := firstPaymentDate.AddDate(0, noOfPaymentsMade, 0)
	//time.AddDate normalizes its result. For example adding one to October 31 yields December 1, the normalized form for November 31
	//But as per amortization calculation sheet, if particular day is not present then last day of that month is considered as payment date.
	//For example if October 31 is payment date then next months payment date should be November 30 not November 31 or December 1
	if firstPaymentDate.Day() != nextPaymentDate.Day() {
		nextPaymentDate = nextPaymentDate.AddDate(0, 0, -nextPaymentDate.Day())
	}
	if nextPaymentDate.After(payoffDate) || getTimeDifferenceInDays(payoffDate, nextPaymentDate).Equal(decimal.Zero) {
		duration := getTimeDifferenceInDays(payoffDate, prevPaymentDate)
		interestToPay := calculateInterest(remainingPrincipalBalance, interest, duration)
		remainingPrincipalBalance = remainingPrincipalBalance.Add(interestToPay)
		return remainingPrincipalBalance
	}

	if noOfPaymentsMade > term {
		return remainingPrincipalBalance
	}

	duration := getTimeDifferenceInDays(nextPaymentDate, prevPaymentDate)               // Duration in days between last payment date and current payment date
	interestAccrued := calculateInterest(remainingPrincipalBalance, interest, duration) // Interest amount on current payment date
	interestPaid := decimal.Min(monthlyPayment, interestAccrued.Add(interestBalance))   // Actual interest paid
	interestBalance = (interestBalance.Add(interestAccrued)).Sub(interestPaid)          // Balance interest amount
	principalPaid := monthlyPayment.Sub(interestPaid)
	remainingPrincipalBalance = remainingPrincipalBalance.Sub(principalPaid)
	return runAmortization(nextPaymentDate, firstPaymentDate, payoffDate,
		interest, monthlyPayment, remainingPrincipalBalance, interestBalance,
		noOfPaymentsMade+1, term)
}

func getTimeDifferenceInDays(first, second time.Time) decimal.Decimal {
	return decimal.NewFromFloat((first.Sub(second).Hours()) / 24).Floor()
}

func calculateInterest(amount, interest, duration decimal.Decimal) decimal.Decimal {
	return amount.Mul(interest).Div(decimal.NewFromFloat(365)).Mul(duration).Round(2)
}

// ClaimCounts returns count of claims by status, agent and age
func ClaimCounts(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	ctx := req.Context()

	var claimsCount []struct {
		Name  null.String `json:"name" db:"name"`
		Count null.Int    `json:"count" db:"count"`
		ID    int         `json:"id" db:"id"`
	}

	countBy := chi.URLParam(req, "countBy")

	countQuery := ""
	avgWaitingQuery := ""
	args := []interface{}{}
	switch countBy {
	case "status":
		countQuery = `SELECT * from ( SELECT (case
				when ` + mapGapStatus("In Inquiry") + `
             			then 'In Inquiry'
				when ` + mapGapStatus("In Process") + `
             			then 'In Process'
				when ` + mapGapStatus("In Review") + `
             			then 'In Review'
				when ` + mapGapStatus("In Finance") + `
             			then 'In Finance' end) as name, count(*)
             		      FROM gap_claims WHERE status in ($1,$2,$3,$4,$5,$6,$7,$8,$9) and is_child=false
             		      GROUP BY (case
				when ` + mapGapStatus("In Inquiry") + `
             			then 'In Inquiry'
				when ` + mapGapStatus("In Process") + `
             			then 'In Process'
				when ` + mapGapStatus("In Review") + `
             			then 'In Review'
				when ` + mapGapStatus("In Finance") + `
             			then 'In Finance' end)) x
             		      ORDER BY (case
             		        when name = 'In Inquiry' then 1
               			when name = 'In Process' then 2
				when name = 'In Review' then 3
               			when name = 'In Finance' then 4 end) ASC;`
		args = append(args,
			db.GapClaimStatusInquiry,
			db.GapClaimStatusPending,
			db.GapClaimStatusReadyToProcess,
			db.GapClaimStatusPendingReopened,
			db.GapClaimStatusPendingDenial,
			db.GapClaimStatusReturnedForCorrections,
			db.GapClaimStatusWaitingForCheck,
			db.GapClaimStatusWaitingForPayment,
			db.GapClaimStatusWaitingForAuthorization)
	case "agent":
		countQuery = `select first_name || ' ' || last_name as name, count(*), users.id from gap_claims left join users on gap_claims.owner_id = users.id where status in ($1,$2,$3,$4,$5,$6,$7,$8) and is_child=false group by users.id ORDER BY count DESC`
		args = append(args,
			db.GapClaimStatusPending,
			db.GapClaimStatusReadyToProcess,
			db.GapClaimStatusPendingReopened,
			db.GapClaimStatusPendingDenial,
			db.GapClaimStatusReturnedForCorrections,
			db.GapClaimStatusWaitingForCheck,
			db.GapClaimStatusWaitingForPayment,
			db.GapClaimStatusWaitingForAuthorization)
	case "age":
		countQuery = `SELECT * from (SELECT (case
			  	when ((extract(epoch from age(gap_claims.date_of_claim_received))/3600)/24)/30 <= 1 then '< 30 Days'
				when ((extract(epoch from age(gap_claims.date_of_claim_received))/3600)/24)/30 > 1 and ((extract(epoch from age(gap_claims.date_of_claim_received))/3600)/24)/30 <= 2 then '30-60 Days'
				when ((extract(epoch from age(gap_claims.date_of_claim_received))/3600)/24)/30 > 2 and ((extract(epoch from age(gap_claims.date_of_claim_received))/3600)/24)/30 <= 3 then '60-90 Days'
				when ((extract(epoch from age(gap_claims.date_of_claim_received))/3600)/24)/30 > 3 then '> 90 Days' end) as name, count(*)
  			      FROM gap_claims where gap_claims.status in ($1,$2,$3,$4,$5,$6,$7,$8) and is_child=false
 			      GROUP BY (case
			  	when ((extract(epoch from age(gap_claims.date_of_claim_received))/3600)/24)/30 <= 1 then '< 30 Days'
				when ((extract(epoch from age(gap_claims.date_of_claim_received))/3600)/24)/30 > 1 and ((extract(epoch from age(gap_claims.date_of_claim_received))/3600)/24)/30 <= 2 then '30-60 Days'
				when ((extract(epoch from age(gap_claims.date_of_claim_received))/3600)/24)/30 > 2 and ((extract(epoch from age(gap_claims.date_of_claim_received))/3600)/24)/30 <= 3 then '60-90 Days'
				when ((extract(epoch from age(gap_claims.date_of_claim_received))/3600)/24)/30 > 3 then '> 90 Days' end)) x
             		      ORDER BY (case
             		        when name = '< 30 Days' then 1
               			when name = '30-60 Days' then 2
				when name = '60-90 Days' then 3
               			when name = '> 90 Days' then 4 end) ASC`
		avgWaitingQuery = `select ((extract(epoch from avg(AGE(now(),date_of_claim_received)))/3600)/24) from gap_claims where gap_claims.status in ($1,$2,$3,$4,$5,$6,$7,$8) and is_child=false`
		args = append(args,
			db.GapClaimStatusPending,
			db.GapClaimStatusReadyToProcess,
			db.GapClaimStatusPendingReopened,
			db.GapClaimStatusPendingDenial,
			db.GapClaimStatusReturnedForCorrections,
			db.GapClaimStatusWaitingForCheck,
			db.GapClaimStatusWaitingForPayment,
			db.GapClaimStatusWaitingForAuthorization)
	default:
		return http.StatusBadRequest, handlers.ErrorMessage(nil, "Invalid data passed", nil)
	}

	err := db.Get().SelectContext(ctx, &claimsCount, countQuery, args...)
	if err != nil {
		err = errors.Wrap(err, "Database error getting gap claims lists count")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting gap claims lists count data for "+countBy, nil)
	}

	if avgWaitingQuery != "" {
		var avgWaitingPeriod sql.NullFloat64
		err = db.Get().GetContext(ctx, &avgWaitingPeriod, avgWaitingQuery, args...)
		if err != nil {
			err = errors.Wrap(err, "Database error getting gap claims average waiting count")
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting gap claims lists count data for "+countBy, nil)
		}
		claimAvgWaiting := decimal.NewFromFloat(avgWaitingPeriod.Float64).Ceil().String()
		return http.StatusOK, map[string]interface{}{"gap_claims": claimsCount, "average_waiting_period": claimAvgWaiting}
	}

	return http.StatusOK, map[string]interface{}{"gap_claims": claimsCount}
}

// ClaimAuthorize authorizes the claim by submitting it to intacct
func ClaimAuthorize(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	ctx := req.Context()
	ids := struct {
		ClaimIDList []int `json:"claim_id_list"`
	}{}

	dec := json.NewDecoder(req.Body)
	err := dec.Decode(&ids)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Malformed data for claim authorize", nil)
	}
	batchID, err := createIntacctBatch()
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error creating GAP claim batch", nil)
	}
	for _, id := range ids.ClaimIDList {
		query := `select case_reserve from gap_claims where id=$1`
		var amount decimal.Decimal
		err := db.Get().Get(&amount, query, id)
		if err != nil {
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error adding GAP claim to batch", nil)
		}
		err = addClaimToBatch(id, batchID, amount)
		if err != nil {
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error adding GAP claim to batch:"+strconv.Itoa(id), nil)
		}
	}
	failedCount := 0
	for _, id := range ids.ClaimIDList {
		gapClaim, err := gapClaimSelect(strconv.Itoa(id))
		if err != nil {
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error GAP claim from database for ID:"+strconv.Itoa(id), nil)
		}

		formErrors, err := validateGapClaim(ctx, gapClaim, ClaimRequestAuthorize)
		if err != nil {
			return http.StatusInternalServerError, handlers.ErrorMessage(errors.Wrap(err, "Error validating GAP claim:"+gapClaim.ContractNumber),
				"An error occurred validating the form values.", nil)
		}

		if len(formErrors) > 0 {
			return http.StatusBadRequest, handlers.ErrorMessage(errors.New("Validation Error"), "Form validations errors for GAP claim:"+gapClaim.ContractNumber,
				map[string]interface{}{"errors": formErrors})
		}

		tx, err := db.Get().Beginx()
		if err != nil {
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error beginning transaction for claim auth", nil)
		}
		err = gapClaimAuthorize(ctx, tx, gapClaim, user.ID)
		if err != nil {
			_ = tx.Rollback()
			failedCount++
			// add failure note in claim
			notesText := "INTACCT_BATCH: Submit to Intacct failed in batch:" + strconv.Itoa(batchID)
			// If we have intacct error then add that to notes text
			if isCustomError(err) {
				notesText = err.Error() + " BatchID: " + strconv.Itoa(batchID)
			}

			recordNote := recordNotePayload{
				ID:              gapClaim.ID,
				CreatedByUserID: user.ID,
				CreatedAt:       time.Now(),
				NotesText:       notesText,
			}
			_, err := insertRecordNote(&recordNote, tx)
			if err != nil {
				_ = tx.Rollback()
				return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error adding notes for gap claim authorize failure", nil)
			}
			// We are processing the batch claims so if one claim fails instead of returning error
			// continue with next claim
			continue
		}
		err = claimSubmitSuccess(tx, gapClaim.ID, batchID)
		if err != nil {
			_ = tx.Rollback()
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error updating GAP claim in batch:"+gapClaim.ContractNumber, nil)
		}
		// add success note in claim
		recordNote := recordNotePayload{
			ID:              gapClaim.ID,
			CreatedByUserID: user.ID,
			CreatedAt:       time.Now(),
			NotesText:       "INTACCT_BATCH: Submit to Intacct successful in batch:" + strconv.Itoa(batchID),
		}
		// Ignoring the error of record note
		insertRecordNote(&recordNote, tx)
		err = tx.Commit()
		if err != nil {
			err = errors.Wrap(err, "Database error committing transaction for GAP claim auth")
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error creating GAP claim authorization", nil)
		}
	}
	return http.StatusOK, map[string]interface{}{"success": len(ids.ClaimIDList) - failedCount, "failed": failedCount}
}

func createIntacctBatch() (int, error) {
	query := `insert into gap_intacct_batches(created_at) values (now() at time zone 'utc') returning id`
	id := 0
	err := db.Get().Get(&id, query)
	if err != nil {
		return id, errors.Wrap(err, "Error creating GAP intacct batch")
	}
	return id, err
}

func addClaimToBatch(claimID, batchID int, amount decimal.Decimal) error {
	query := `insert into gap_intacct_batch_details(gap_claim_id,gap_intacct_batch_id,amount) values ($1,$2,$3)`
	_, err := db.Get().Exec(query, claimID, batchID, amount)
	if err != nil {
		return errors.Wrap(err, "Error adding GAP claim to batch")
	}
	return err
}

func claimSubmitSuccess(tx *sqlx.Tx, claimID, batchID int) error {
	query := `update gap_intacct_batch_details set is_success = true where gap_claim_id=$1 and gap_intacct_batch_id=$2`
	_, err := tx.Exec(query, claimID, batchID)
	if err != nil {
		return errors.Wrap(err, "Error update GAP claim to batch success")
	}
	return err
}

// ChildClaimCreate creates a new Gap Claim
func ChildClaimCreate(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	ctx := req.Context()
	gapClaim, err := gapClaimSelect(chi.URLParam(req, "id"))
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Gap claim is not valid", nil)
	}
	formErrors, err := validateGapClaim(ctx, gapClaim, ClaimRequestCreateChild)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(errors.Wrap(err, "Error validating GAP Claim"),
			"An error occurred validating the form values.", nil)
	}

	if len(formErrors) > 0 {
		return http.StatusBadRequest, handlers.ErrorMessage(nil, "Form validations errors.",
			map[string]interface{}{"errors": formErrors})
	}

	// child claim should be initialized with following values
	gapClaim.IsChild = true
	gapClaim.Status = db.GapClaimStatusInquiry // New child claim should have status 'I' Inquiry
	gapClaim.ParentClaimID.Int64 = int64(gapClaim.ID)
	gapClaim.ParentClaimID.Valid = true
	gapClaim.StatusChangeDescription = ""
	gapClaim.CaseReserve = decimal.Zero
	gapClaim.CreatedByUserID = user.ID

	id, err := gapClaimInsert(gapClaim)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error creating GAP claim", nil)
	}
	return http.StatusOK, map[string]interface{}{"id": id}
}

// ChildClaimShow returns the child claim
func ChildClaimShow(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {

	childClaim := struct {
		ID                      int             `json:"id" db:"id"`
		ChildClaimReason        string          `json:"child_claim_reason" db:"child_claim_reason"`
		Status                  string          `json:"status" db:"status"`
		CaseReserve             decimal.Decimal `json:"case_reserve" db:"case_reserve"`
		StatusChangeDescription string          `json:"status_change_description" db:"status_change_description"`
		ParentClaimID           int             `json:"parent_claim_id" db:"parent_claim_id"`
		CSCheckAmount           decimal.Decimal `json:"cs_check_amount" db:"cs_check_amount"` // used only in CS claim
	}{}

	query := `select id, child_claim_reason, status, case_reserve, status_change_description, parent_claim_id, cs_check_amount
	from gap_claims where id=$1 and parent_claim_id = $2 and is_child=true`
	err := db.Get().Get(&childClaim, query, chi.URLParam(req, "child_id"), chi.URLParam(req, "id"))
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage(err, "Child claim not found", nil)
		}
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error loading child claim", nil)
	}

	return http.StatusOK, map[string]interface{}{"child_claim": childClaim}
}

// ChildClaimUpdate updates the child claim, if there is status change to WC, request is submitted to intacct for payment
func ChildClaimUpdate(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	ctx := req.Context()
	id := 0
	err := db.Get().Get(&id, `select id from gap_claims where id=$1 and parent_claim_id = $2 and is_child=true`, chi.URLParam(req, "child_id"), chi.URLParam(req, "id"))
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage(err, "Child claim not found", nil)
		}
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error loading child claim", nil)
	}
	reqClaim := struct {
		ID                      int             `json:"id" db:"id"`
		ChildClaimReason        string          `json:"child_claim_reason" db:"child_claim_reason"`
		Status                  string          `json:"status" db:"status"`
		CaseReserve             decimal.Decimal `json:"case_reserve" db:"case_reserve"`
		StatusChangeDescription string          `json:"status_change_description" db:"status_change_description"`
	}{}

	dec := json.NewDecoder(req.Body)
	err = dec.Decode(&reqClaim)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "error in decoding the request", nil)
	}
	reqClaim.ID = id
	query := `update gap_claims
		set status = :status,
			status_change_description = :status_change_description,
			case_reserve = :case_reserve,
			child_claim_reason = :child_claim_reason
			where id = :id`

	tx, err := db.Get().Beginx()
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error beginning transaction for child claim update", nil)
	}
	stmt, err := tx.PrepareNamed(query)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error updating GAP child claim, PrepareNamed failed", nil)
	}
	defer func() { _ = stmt.Close() }()

	_, err = stmt.Exec(reqClaim)
	if err != nil {
		_ = tx.Rollback()
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error updating GAP child claim, database error", nil)
	}

	// authorize request, submitToIntacct, WC
	if reqClaim.Status == db.GapClaimStatusWaitingForCheck {
		claim, err := gapClaimSelect(strconv.Itoa(reqClaim.ID))
		if err != nil {
			_ = tx.Rollback()
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error loading child claim data for authorization", nil)
		}
		claim.ChildClaimReason = reqClaim.ChildClaimReason
		claim.CaseReserve = reqClaim.CaseReserve
		claim.StatusChangeDescription = reqClaim.StatusChangeDescription

		formErrors, err := validateGapClaim(ctx, claim, ClaimRequestAuthorize)
		if len(formErrors) > 0 {
			_ = tx.Rollback()
			return http.StatusBadRequest, handlers.ErrorMessage(err, "Form validations errors.",
				map[string]interface{}{"errors": formErrors})
		}
		err = gapClaimAuthorize(ctx, tx, claim, user.ID)
		if err != nil {
			_ = tx.Rollback()
			if isCustomError(err) {
				return http.StatusInternalServerError, handlers.ErrorMessage(err, err.Error(), nil)
			}
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error creating GAP claim authorization", nil)
		}
	}
	err = tx.Commit()
	if err != nil {
		err = errors.Wrap(err, "Database error committing transaction for GAP claim update")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error updating child claim", nil)
	}

	return http.StatusOK, map[string]interface{}{"id": id}
}

// BatchClaims returns details of batch : batch date, claim list, number of successfully submitted claims and number of failed claims
func BatchClaims(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	batchClaims := []struct {
		InsuredName    string          `json:"insured_name" db:"insured_name"`
		ContractNumber string          `json:"contract_number" db:"contract_number"`
		Amount         decimal.Decimal `json:"amount" db:"amount"`
		IsSuccess      bool            `json:"is_success" db:"is_success"`
		Note           string          `json:"note" db:"-"`
		GapClaimID     int             `json:"gap_claim_id" db:"gap_claim_id"`
	}{}

	batchID := chi.URLParam(req, "id")
	if batchID == "" {
		return http.StatusBadRequest, handlers.ErrorMessage(nil, "Invalid data", nil)
	}

	query := `select customers.first_name || ' ' || customers.last_name as insured_name,
       			 gap_claims.contract_number,
			 gap_intacct_batch_details.amount,
			 gap_intacct_batch_details.is_success,
			 gap_intacct_batch_details.gap_claim_id
		  from gap_intacct_batch_details
			join gap_claims on gap_intacct_batch_details.gap_claim_id = gap_claims.id
			join customers on gap_claims.customer_id = customers.id
		  where gap_intacct_batch_details.gap_intacct_batch_id = $1`

	orderByName := req.FormValue("order_by_name")
	if orderByName == "desc" || orderByName == "asc" {
		query += " order by insured_name " + orderByName
	}

	err := db.Get().Select(&batchClaims, query, batchID)
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage(err, "The claim list not found", nil)
		}
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error loading batch claims from database.", nil)
	}
	successCount := 0
	failureCount := 0
	for index, claim := range batchClaims {
		if !claim.IsSuccess {
			var note string
			var noteLike = "'INTACCT_BATCH%in batch:" + batchID + "'"
			noteQuery := "select notes_text from record_notes where gap_claim_id = $1 and notes_text like " + noteLike
			err := db.Get().Get(&note, noteQuery, claim.GapClaimID)
			if err != nil && err != sql.ErrNoRows {
				return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error loading batch claim note from database.", nil)
			}
			if err != sql.ErrNoRows {
				batchClaims[index].Note = note
				failureCount = failureCount + 1
			}
		} else {
			successCount = successCount + 1
		}
	}

	var batchDate time.Time
	batchQuery := "select created_at from gap_intacct_batches where id = $1"
	err = db.Get().Get(&batchDate, batchQuery, batchID)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error loading batch date from database.", nil)
	}

	return http.StatusOK, map[string]interface{}{
		"batchClaims":  batchClaims,
		"successCount": successCount,
		"failureCount": failureCount,
		"batchDate":    batchDate}
}

// BatchIndex returns list of batches
func BatchIndex(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	batchList := []struct {
		ID        int             `json:"id" db:"id"`
		CreatedAt time.Time       `json:"created_at" db:"created_at"`
		Amount    decimal.Decimal `json:"amount" db:"-"`
		Status    string          `json:"status" db:"-"`
		Note      string          `json:"note" db:"-"`
	}{}

	query := "select id, created_at from gap_intacct_batches"

	orderByDate := req.FormValue("order_by_date")
	if orderByDate == "desc" || orderByDate == "asc" {
		query += " order by created_at " + orderByDate
	}

	err := db.Get().Select(&batchList, query)
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage(err, "No batches found", nil)
		}
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error loading batches from database.", nil)
	}

	for index, batch := range batchList {
		batchDetails := struct {
			TotalAmount  decimal.Decimal `db:"total_amount"`
			TotalClaims  int             `db:"total_claims"`
			FailureCount int             `db:"failure_count"`
		}{}

		batchDetailsQuery := "select sum(amount) as total_amount, count(*) as total_claims, count(CASE WHEN is_success = false THEN 1 END) as failure_count from gap_intacct_batch_details where gap_intacct_batch_id = $1"
		err := db.Get().Get(&batchDetails, batchDetailsQuery, batch.ID)
		if err != nil {
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error loading batch details", nil)
		}
		batchList[index].Amount = batchDetails.TotalAmount
		if batchDetails.FailureCount == 0 {
			batchList[index].Status = "Success"
		} else {
			batchList[index].Status = "Issues"
			batchList[index].Note = strconv.Itoa(batchDetails.FailureCount) + " of " + strconv.Itoa(batchDetails.TotalClaims) + " failed"
		}

	}
	return http.StatusOK, map[string]interface{}{"batchList": batchList}
}
