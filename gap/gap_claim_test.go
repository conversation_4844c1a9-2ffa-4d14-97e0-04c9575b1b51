package gap

import (
	"net/http"
	"net/http/httptest"
	"reflect"
	"regexp"
	"strings"
	"testing"
	"time"

	"phizz/db"
	"phizz/handlers"

	sqlmock "github.com/DATA-DOG/go-sqlmock"
	"github.com/go-chi/chi"
	"github.com/jmoiron/sqlx"
	"github.com/shopspring/decimal"
)

func Test_gapClaimPayloadFromReq(t *testing.T) {
	body := `{"vin":"2XFWDI34523","waiting_for":"some reason","first_name":"<PERSON>"}`
	req := httptest.NewRequest("POST", "/foo", strings.NewReader(body))

	gapClaim := gapClaimPayload{}

	err := gapClaimFromReq(&gapClaim, req)
	if err != nil {
		t.Error("gapClaimFromReq returned an error", err)
	}
	if gapClaim.VIN != "2XFWDI34523" {
		t.Error("didn't parse name from gapclaim payload")
	}
	if gapClaim.WaitingFor != "some reason" {
		t.Error("didn't parse waiting_for from gapclaim payload")
	}
}

func DISABLEDTestGapClaimIndex(t *testing.T) {
	mdb, mock, err := sqlmock.New()
	if err != nil {
		t.Fatal(err)
	}
	dbx := sqlx.NewDb(mdb, "sqlmock")
	db.DB = dbx
	q := regexp.QuoteMeta

	mock.ExpectQuery(`^select gap_claims.id, customers.last_name || ' ' || customers.first_name as insured_name, contract_number, status, date_of_claim_received, date_of_last_in, waiting_for, date_of_last_out, case_reserve, date_of_loss, state, users.first_name || ' ' || users.last_name as assigned_to from gap_claims join customers on customer_id = customers.id join users on owner_id = users.id where is_child = false and owner_id = 5103 order by status, date_of_last_out desc limit 20 offset 0$`).
		WillReturnRows(sqlmock.NewRows([]string{"id", "insured_name", "contract_number", "status", "date_of_claim_received", "date_of_last_in", "waiting_for", "date_of_last_out", "case_reserve", "date_of_loss", "state", "assigned_to"}).
			AddRow(15, "Lopez Paul", "GAPS978638", "C-NR", time.Time{}, time.Time{}, "1a,3,4,7,", time.Time{}, 4004.04, time.Time{}, "UT", "Assigned To"))

	mock.ExpectQuery(q(`select count(*) from gap_claims join customers on customer_id = customers.id join users on owner_id = users.id where is_child = false and owner_id = 5103`)).WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))

	mock.ExpectQuery(q(`select sum(case_reserve) from gap_claims join customers on customer_id = customers.id join users on owner_id = users.id where is_child = false and owner_id = 5103`)).WillReturnRows(sqlmock.NewRows([]string{"totalEstimate"}).AddRow(4004.04))

	req, err := http.NewRequest("GET", "/api/gapclaims?user_id=5103&page=1", nil)
	if err != nil {
		t.Fatal(err)
	}

	rr := httptest.NewRecorder()
	handler := http.HandlerFunc(handlers.APIHandler(ClaimIndex))

	r := chi.NewRouter()
	r.HandleFunc("/api/gapclaims", handler)
	r.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v",
			status, http.StatusOK)
	}

	expected := `{"count":1,"gap_claims":[{"id":15,"insured_name":"Lopez Paul","contract_number":"GAPS978638","assigned_to":"Assigned To","status":"C-NR","date_of_claim_received":"0001-01-01T00:00:00Z","date_of_last_in":"0001-01-01T00:00:00Z","waiting_for":"1a,3,4,7,","date_of_last_out":"0001-01-01T00:00:00Z","case_reserve":"4004.04","date_of_loss":"0001-01-01T00:00:00Z","state":"UT"}],"totalEstimate":4004.04}`
	if rr.Body.String() != expected {
		t.Errorf("handler returned unexpected body: got %v want %v",
			rr.Body.String(), expected)
	}

	if err := mock.ExpectationsWereMet(); err != nil {
		t.Errorf("there were unfulfilled expectations: %s", err)
	}
}

func Test_updateWaitingFor(t *testing.T) {
	gapClaim := gapClaimPayload{
		ID:                           1,
		HasInsuranceCheckAmount:      false,
		HasSettlementAmount:          false,
		IsValuationReportAvailable:   false,
		HasOriginalFinancingContract: false,
		ContractNumberMatches:        false,
		IsPoliceReportAvailable:      true,
		HasInsurancePolicyDeductible: false,
		HasBankInformation:           true,
		IsFullLoanHistoryAvailable:   true,
		Contracts: []gapClaimContractPayload{
			{ContractCode: "VSC", ContractFlag: false},
			{ContractCode: "MNT", ContractFlag: true},
		},
	}
	updateWaitingFor(&gapClaim)
	if gapClaim.WaitingFor != "1a,1b,1c,2a,2b,4,6a" {
		t.Error("updateWaitingFor failed to update the field Waiting_for :expected value:1a,1b,1c,2a,2b,4,6a")
		t.Error(gapClaim.WaitingFor)
	}
}

func checkLastQuarterRange(t *testing.T, claimDateStr, expectedQtrStart, expectedQtrEnd string) {
	// Date in Jan
	claimDate, _ := time.Parse(time.RFC3339, claimDateStr)
	t1, t2 := getLastQuarterDateRange(claimDate)

	if t1 != expectedQtrStart {
		t.Errorf("function returned unpexpcted startdate: got %v want %v",
			expectedQtrStart, t1)
	}
	if t2 != expectedQtrEnd {
		t.Errorf("function returned unpexpcted enddate: got %v want %v",
			expectedQtrEnd, t2)
	}
}
func Test_getLastQuarterDateRange(t *testing.T) {

	// Test for first quarter date, the last quarter should be Q4 of prev year
	expectedQ4Start := "2016-10-01"
	expectedQ4End := "2016-12-31"

	checkLastQuarterRange(t, "2017-01-21T00:00:00+00:00", expectedQ4Start, expectedQ4End)
	checkLastQuarterRange(t, "2017-02-11T00:00:00+00:00", expectedQ4Start, expectedQ4End)
	checkLastQuarterRange(t, "2017-03-08T00:00:00+00:00", expectedQ4Start, expectedQ4End)

	// Test for second quarter date, the last quarter should be Q1 of the year
	expectedQ1Start := "2017-01-01"
	expectedQ1End := "2017-03-31"

	checkLastQuarterRange(t, "2017-04-21T00:00:00+00:00", expectedQ1Start, expectedQ1End)
	checkLastQuarterRange(t, "2017-05-11T00:00:00+00:00", expectedQ1Start, expectedQ1End)
	checkLastQuarterRange(t, "2017-06-08T00:00:00+00:00", expectedQ1Start, expectedQ1End)

	// Test for third quarter date, the last quarter should be Q2 of the year
	expectedQ2Start := "2017-04-01"
	expectedQ2End := "2017-06-30"

	checkLastQuarterRange(t, "2017-07-21T00:00:00+00:00", expectedQ2Start, expectedQ2End)
	checkLastQuarterRange(t, "2017-08-11T00:00:00+00:00", expectedQ2Start, expectedQ2End)
	checkLastQuarterRange(t, "2017-09-08T00:00:00+00:00", expectedQ2Start, expectedQ2End)

	// Test for forth quarter date, the last quarter should be Q3 of the year
	expectedQ3Start := "2017-07-01"
	expectedQ3End := "2017-09-30"

	checkLastQuarterRange(t, "2017-10-21T00:00:00+00:00", expectedQ3Start, expectedQ3End)
	checkLastQuarterRange(t, "2017-11-11T00:00:00+00:00", expectedQ3Start, expectedQ3End)
	checkLastQuarterRange(t, "2017-12-08T00:00:00+00:00", expectedQ3Start, expectedQ3End)

}

func Test_calculateAverageLastQuarterPayout(t *testing.T) {
	mdb, mock, err := sqlmock.New()
	if err != nil {
		t.Fatal(err)
	}
	dbx := sqlx.NewDb(mdb, "sqlmock")
	db.DB = dbx
	q := regexp.QuoteMeta

	mock.ExpectQuery(q(`select count(*) from gap_claim_payments where paid_date between $1 and $2`)).
		WithArgs("2016-01-01", "2016-03-31").
		WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(7))

	mock.ExpectQuery(q(`select avg(amount) from gap_claim_payments where paid_date between $1 and $2`)).
		WithArgs("2016-01-01", "2016-03-31").
		WillReturnRows(sqlmock.NewRows([]string{"avg"}).AddRow(365.5))

	claimDate, _ := time.Parse(time.RFC3339, "2016-04-21T00:00:00+00:00")
	avg, err := calculateAverageLastQuarterPayout(claimDate)
	if err != nil {
		t.Errorf("error in calculateAverageLastQuarterPayout")
	}

	if !avg.Equals(decimal.NewFromFloat(365.5)) {
		t.Errorf("handler returned unexpected body: got %v want %v",
			avg, 365.5)
	}

	if err := mock.ExpectationsWereMet(); err != nil {
		t.Errorf("there were unfulfilled expectations: %s", err)
	}
}

func DISABLEDTestGapClaimIndexNameContractVin(t *testing.T) {
	mdb, mock, err := sqlmock.New()
	if err != nil {
		t.Fatal(err)
	}
	dbx := sqlx.NewDb(mdb, "sqlmock")
	db.DB = dbx
	q := regexp.QuoteMeta

	mock.ExpectQuery(q(`select gap_claims.id, customers.last_name || ' ' || customers.first_name as insured_name, contract_number, status, date_of_claim_received, date_of_last_in, waiting_for, date_of_last_out, case_reserve, date_of_loss, state, users.first_name || ' ' || users.last_name as assigned_to from gap_claims join customers on customer_id = customers.id join users on owner_id = users.id where is_child = false and (concat(customers.first_name, ' ', customers.last_name) ilike $1 or concat(customers.last_name, ' ', customers.first_name) ilike $1 or contract_number ilike $1 or vin ilike $1) and owner_id = 5103 order by status, date_of_last_out desc limit 20 offset 0`)).
		WithArgs("%'Lopez'%").
		WillReturnRows(sqlmock.NewRows([]string{"id", "insured_name", "contract_number", "status", "date_of_claim_received", "date_of_last_in", "waiting_for", "date_of_last_out", "case_reserve", "date_of_loss", "state", "assigned_to"}).
			AddRow(15, "Lopez Paul", "GAPS978638", "C-NR", time.Time{}, time.Time{}, "1a,3,4,7", time.Time{}, 4004.04, time.Time{}, "UT", "Assigned To"))

	mock.ExpectQuery(q(`select count(*) from gap_claims join customers on customer_id = customers.id join users on owner_id = users.id where is_child = false and (concat(customers.first_name, ' ', customers.last_name) ilike $1 or concat(customers.last_name, ' ', customers.first_name) ilike $1 or contract_number ilike $1 or vin ilike $1) and owner_id = 5103`)).WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))

	mock.ExpectQuery(q(`select sum(case_reserve) from gap_claims join customers on customer_id = customers.id join users on owner_id = users.id where is_child = false and (concat(customers.first_name, ' ', customers.last_name) ilike $1 or concat(customers.last_name, ' ', customers.first_name) ilike $1 or contract_number ilike $1 or vin ilike $1) and owner_id = 5103`)).WillReturnRows(sqlmock.NewRows([]string{"totalEstimate"}).AddRow(4004.04))

	req, err := http.NewRequest("GET", "/api/gapclaims?q='Lopez'&user_id=5103&page=1", nil)
	if err != nil {
		t.Fatal(err)
	}

	rr := httptest.NewRecorder()
	handler := http.HandlerFunc(handlers.APIHandler(ClaimIndex))

	r := chi.NewRouter()
	r.HandleFunc("/api/gapclaims", handler)
	r.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v",
			status, http.StatusOK)
	}

	expected := `{"count":1,"gap_claims":[{"id":15,"insured_name":"Lopez Paul","contract_number":"GAPS978638","assigned_to":"Assigned To","status":"C-NR","date_of_claim_received":"0001-01-01T00:00:00Z","date_of_last_in":"0001-01-01T00:00:00Z","waiting_for":"1a,3,4,7","date_of_last_out":"0001-01-01T00:00:00Z","case_reserve":"4004.04","date_of_loss":"0001-01-01T00:00:00Z","state":"UT"}],"totalEstimate":4004.04}`
	if rr.Body.String() != expected {
		t.Errorf("handler returned unexpected body: got %v want %v",
			rr.Body.String(), expected)
	}

	if err := mock.ExpectationsWereMet(); err != nil {
		t.Errorf("there were unfulfilled expectations: %s", err)
	}
}

func DISABLEDTestGapClaimIndexUserID(t *testing.T) {
	mdb, mock, err := sqlmock.New()
	if err != nil {
		t.Fatal(err)
	}
	dbx := sqlx.NewDb(mdb, "sqlmock")
	db.DB = dbx
	q := regexp.QuoteMeta

	mock.ExpectQuery(q(`select gap_claims.id, customers.last_name || ' ' || customers.first_name as insured_name, contract_number, status, date_of_claim_received, date_of_last_in, waiting_for, date_of_last_out, case_reserve, date_of_loss, state, users.first_name || ' ' || users.last_name as assigned_to from gap_claims join customers on customer_id = customers.id join users on owner_id = users.id where is_child = false and owner_id = 5103 order by status, date_of_last_out desc limit 20 offset 0`)).
		WillReturnRows(sqlmock.NewRows([]string{"id", "insured_name", "contract_number", "status", "date_of_claim_received", "date_of_last_in", "waiting_for", "date_of_last_out", "case_reserve", "date_of_loss", "state", "assigned_to"}).
			AddRow(15, "Lopez Paul", "GAPS978638", "C-NR", time.Time{}, time.Time{}, "1a,3,4,7", time.Time{}, 4004.04, time.Time{}, "UT", "Assigned To"))

	mock.ExpectQuery(q(`select count(*) from gap_claims join customers on customer_id = customers.id join users on owner_id = users.id where is_child = false and owner_id = 5103`)).WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))

	mock.ExpectQuery(q(`select sum(case_reserve) from gap_claims join customers on customer_id = customers.id join users on owner_id = users.id where is_child = false and owner_id = 5103`)).WillReturnRows(sqlmock.NewRows([]string{"totalEstimate"}).AddRow(4004.04))

	req, err := http.NewRequest("GET", "/api/gapclaims?user_id=5103&page=1", nil)
	if err != nil {
		t.Fatal(err)
	}

	rr := httptest.NewRecorder()
	handler := http.HandlerFunc(handlers.APIHandler(ClaimIndex))

	r := chi.NewRouter()
	r.HandleFunc("/api/gapclaims", handler)
	r.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v",
			status, http.StatusOK)
	}

	expected := `{"count":1,"gap_claims":[{"id":15,"insured_name":"Lopez Paul","contract_number":"GAPS978638","assigned_to":"Assigned To","status":"C-NR","date_of_claim_received":"0001-01-01T00:00:00Z","date_of_last_in":"0001-01-01T00:00:00Z","waiting_for":"1a,3,4,7","date_of_last_out":"0001-01-01T00:00:00Z","case_reserve":"4004.04","date_of_loss":"0001-01-01T00:00:00Z","state":"UT"}],"totalEstimate":4004.04}`
	if rr.Body.String() != expected {
		t.Errorf("handler returned unexpected body: got %v want %v",
			rr.Body.String(), expected)
	}

	if err := mock.ExpectationsWereMet(); err != nil {
		t.Errorf("there were unfulfilled expectations: %s", err)
	}
}

func DISABLEDTestGapClaimIndexUserIDAll(t *testing.T) {
	mdb, mock, err := sqlmock.New()
	if err != nil {
		t.Fatal(err)
	}
	dbx := sqlx.NewDb(mdb, "sqlmock")
	db.DB = dbx
	q := regexp.QuoteMeta

	mock.ExpectQuery(q(`select gap_claims.id, customers.last_name || ' ' || customers.first_name as insured_name, contract_number, status, date_of_claim_received, date_of_last_in, waiting_for, date_of_last_out, case_reserve, date_of_loss, state, users.first_name || ' ' || users.last_name as assigned_to from gap_claims join customers on customer_id = customers.id join users on owner_id = users.id where is_child = false order by status, date_of_last_out desc`)).
		WillReturnRows(sqlmock.NewRows([]string{"id", "insured_name", "contract_number", "status", "date_of_claim_received", "date_of_last_in", "waiting_for", "date_of_last_out", "case_reserve", "date_of_loss", "state", "assigned_to"}).
			AddRow(15, "Lopez Paul", "GAPS978638", "C-NR", time.Time{}, time.Time{}, "1a,3,4,7", time.Time{}, 4004.04, time.Time{}, "UT", "Assigned To"))

	mock.ExpectQuery(q(`select count(*) from gap_claims join customers on customer_id = customers.id join users on owner_id = users.id where is_child = false`)).WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))

	mock.ExpectQuery(q(`select sum(case_reserve) from gap_claims join customers on customer_id = customers.id join users on owner_id = users.id where is_child = false`)).WillReturnRows(sqlmock.NewRows([]string{"totalEstimate"}).AddRow(4004.04))

	req, err := http.NewRequest("GET", "/api/gapclaims", nil)
	if err != nil {
		t.Fatal(err)
	}

	rr := httptest.NewRecorder()
	handler := http.HandlerFunc(handlers.APIHandler(ClaimIndex))

	r := chi.NewRouter()
	r.HandleFunc("/api/gapclaims", handler)
	r.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v",
			status, http.StatusOK)
	}

	expected := `{"count":1,"gap_claims":[{"id":15,"insured_name":"Lopez Paul","contract_number":"GAPS978638","assigned_to":"Assigned To","status":"C-NR","date_of_claim_received":"0001-01-01T00:00:00Z","date_of_last_in":"0001-01-01T00:00:00Z","waiting_for":"1a,3,4,7","date_of_last_out":"0001-01-01T00:00:00Z","case_reserve":"4004.04","date_of_loss":"0001-01-01T00:00:00Z","state":"UT"}],"totalEstimate":4004.04}`
	if rr.Body.String() != expected {
		t.Errorf("handler returned unexpected body: got %v want %v",
			rr.Body.String(), expected)
	}

	if err := mock.ExpectationsWereMet(); err != nil {
		t.Errorf("there were unfulfilled expectations: %s", err)
	}
}

func DISABLEDTestGapClaimStatusInProcess(t *testing.T) {
	mdb, mock, err := sqlmock.New()
	if err != nil {
		t.Fatal(err)
	}
	dbx := sqlx.NewDb(mdb, "sqlmock")
	db.DB = dbx
	q := regexp.QuoteMeta

	mock.ExpectQuery(q(`select gap_claims.id, customers.last_name || ' ' || customers.first_name as insured_name, contract_number, status, date_of_claim_received, date_of_last_in, waiting_for, date_of_last_out, case_reserve, date_of_loss, state, users.first_name || ' ' || users.last_name as assigned_to from gap_claims join customers on customer_id = customers.id join users on owner_id = users.id where is_child = false and owner_id = 5103 and (status in ('P','RO-RP','PD','RC', 'RP')) order by status, date_of_last_out desc limit 20 offset 0`)).
		WillReturnRows(sqlmock.NewRows([]string{"id", "insured_name", "contract_number", "status", "date_of_claim_received", "date_of_last_in", "waiting_for", "date_of_last_out", "case_reserve", "date_of_loss", "state", "assigned_to"}).
			AddRow(15, "Lopez Paul", "GAPS978638", "C-NR", time.Time{}, time.Time{}, "1a,3,4,7", time.Time{}, 4004.04, time.Time{}, "UT", "Assigned To"))

	mock.ExpectQuery(q(`select count(*) from gap_claims join customers on customer_id = customers.id join users on owner_id = users.id where is_child = false`)).WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))

	mock.ExpectQuery(q(`select sum(case_reserve) from gap_claims join customers on customer_id = customers.id join users on owner_id = users.id where is_child = false`)).WillReturnRows(sqlmock.NewRows([]string{"totalEstimate"}).AddRow(4004.04))

	req, err := http.NewRequest("GET", "/api/gapclaims?status=In Process&user_id=5103&page=1", nil)
	if err != nil {
		t.Fatal(err)
	}

	rr := httptest.NewRecorder()
	handler := http.HandlerFunc(handlers.APIHandler(ClaimIndex))

	r := chi.NewRouter()
	r.HandleFunc("/api/gapclaims", handler)
	r.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v",
			status, http.StatusOK)
	}

	expected := `{"count":1,"gap_claims":[{"id":15,"insured_name":"Lopez Paul","contract_number":"GAPS978638","assigned_to":"Assigned To","status":"C-NR","date_of_claim_received":"0001-01-01T00:00:00Z","date_of_last_in":"0001-01-01T00:00:00Z","waiting_for":"1a,3,4,7","date_of_last_out":"0001-01-01T00:00:00Z","case_reserve":"4004.04","date_of_loss":"0001-01-01T00:00:00Z","state":"UT"}],"totalEstimate":4004.04}`
	if rr.Body.String() != expected {
		t.Errorf("handler returned unexpected body: got %v want %v",
			rr.Body.String(), expected)
	}

	if err := mock.ExpectationsWereMet(); err != nil {
		t.Errorf("there were unfulfilled expectations: %s", err)
	}
}

func DISABLEDTestGapClaimStatusInReview(t *testing.T) {
	mdb, mock, err := sqlmock.New()
	if err != nil {
		t.Fatal(err)
	}
	dbx := sqlx.NewDb(mdb, "sqlmock")
	db.DB = dbx
	q := regexp.QuoteMeta

	mock.ExpectQuery(q(`select gap_claims.id, customers.last_name || ' ' || customers.first_name as insured_name, contract_number, status, date_of_claim_received, date_of_last_in, waiting_for, date_of_last_out, case_reserve, date_of_loss, state, users.first_name || ' ' || users.last_name as assigned_to from gap_claims join customers on customer_id = customers.id join users on owner_id = users.id where is_child = false and owner_id = 5103 and (status = 'WA') order by status, date_of_last_out desc limit 20 offset 0`)).
		WillReturnRows(sqlmock.NewRows([]string{"id", "insured_name", "contract_number", "status", "date_of_claim_received", "date_of_last_in", "waiting_for", "date_of_last_out", "case_reserve", "date_of_loss", "state", "assigned_to"}).
			AddRow(15, "Lopez Paul", "GAPS978638", "WA", time.Time{}, time.Time{}, "1a,3,4,7", time.Time{}, 4004.04, time.Time{}, "UT", "Assigned To"))

	mock.ExpectQuery(q(`select count(*) from gap_claims join customers on customer_id = customers.id join users on owner_id = users.id where is_child = false`)).WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))

	mock.ExpectQuery(q(`select sum(case_reserve) from gap_claims join customers on customer_id = customers.id join users on owner_id = users.id where is_child = false`)).WillReturnRows(sqlmock.NewRows([]string{"totalEstimate"}).AddRow(4004.04))

	req, err := http.NewRequest("GET", "/api/gapclaims?status=In Review&user_id=5103&page=1", nil)
	if err != nil {
		t.Fatal(err)
	}

	rr := httptest.NewRecorder()
	handler := http.HandlerFunc(handlers.APIHandler(ClaimIndex))

	r := chi.NewRouter()
	r.HandleFunc("/api/gapclaims", handler)
	r.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v",
			status, http.StatusOK)
	}

	expected := `{"count":1,"gap_claims":[{"id":15,"insured_name":"Lopez Paul","contract_number":"GAPS978638","assigned_to":"Assigned To","status":"WA","date_of_claim_received":"0001-01-01T00:00:00Z","date_of_last_in":"0001-01-01T00:00:00Z","waiting_for":"1a,3,4,7","date_of_last_out":"0001-01-01T00:00:00Z","case_reserve":"4004.04","date_of_loss":"0001-01-01T00:00:00Z","state":"UT"}],"totalEstimate":4004.04}`
	if rr.Body.String() != expected {
		t.Errorf("handler returned unexpected body: got %v want %v",
			rr.Body.String(), expected)
	}

	if err := mock.ExpectationsWereMet(); err != nil {
		t.Errorf("there were unfulfilled expectations: %s", err)
	}
}

func DISABLEDTestGapClaimStatusInFinance(t *testing.T) {
	mdb, mock, err := sqlmock.New()
	if err != nil {
		t.Fatal(err)
	}
	dbx := sqlx.NewDb(mdb, "sqlmock")
	db.DB = dbx
	q := regexp.QuoteMeta

	mock.ExpectQuery(q(`select gap_claims.id, customers.last_name || ' ' || customers.first_name as insured_name, contract_number, status, date_of_claim_received, date_of_last_in, waiting_for, date_of_last_out, case_reserve, date_of_loss, state, users.first_name || ' ' || users.last_name as assigned_to from gap_claims join customers on customer_id = customers.id join users on owner_id = users.id where is_child = false and owner_id = 5103 and (status in ('WC','WP')) order by status, date_of_last_out desc limit 20 offset 0`)).
		WillReturnRows(sqlmock.NewRows([]string{"id", "insured_name", "contract_number", "status", "date_of_claim_received", "date_of_last_in", "waiting_for", "date_of_last_out", "case_reserve", "date_of_loss", "state", "assigned_to"}).
			AddRow(15, "Lopez Paul", "GAPS978638", "WC", time.Time{}, time.Time{}, "1a,3,4,7", time.Time{}, 4004.04, time.Time{}, "UT", "Assigned To"))

	mock.ExpectQuery(q(`select count(*) from gap_claims join customers on customer_id = customers.id join users on owner_id = users.id where is_child = false`)).WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))

	mock.ExpectQuery(q(`select sum(case_reserve) from gap_claims join customers on customer_id = customers.id join users on owner_id = users.id where is_child = false`)).WillReturnRows(sqlmock.NewRows([]string{"totalEstimate"}).AddRow(4004.04))

	req, err := http.NewRequest("GET", "/api/gapclaims?status=In Finance&user_id=5103&page=1", nil)
	if err != nil {
		t.Fatal(err)
	}

	rr := httptest.NewRecorder()
	handler := http.HandlerFunc(handlers.APIHandler(ClaimIndex))

	r := chi.NewRouter()
	r.HandleFunc("/api/gapclaims", handler)
	r.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v",
			status, http.StatusOK)
	}

	expected := `{"count":1,"gap_claims":[{"id":15,"insured_name":"Lopez Paul","contract_number":"GAPS978638","assigned_to":"Assigned To","status":"WC","date_of_claim_received":"0001-01-01T00:00:00Z","date_of_last_in":"0001-01-01T00:00:00Z","waiting_for":"1a,3,4,7","date_of_last_out":"0001-01-01T00:00:00Z","case_reserve":"4004.04","date_of_loss":"0001-01-01T00:00:00Z","state":"UT"}],"totalEstimate":4004.04}`
	if rr.Body.String() != expected {
		t.Errorf("handler returned unexpected body: got %v want %v",
			rr.Body.String(), expected)
	}

	if err := mock.ExpectationsWereMet(); err != nil {
		t.Errorf("there were unfulfilled expectations: %s", err)
	}
}

func DISABLEDTestGapClaimStatusClosed(t *testing.T) {
	mdb, mock, err := sqlmock.New()
	if err != nil {
		t.Fatal(err)
	}
	dbx := sqlx.NewDb(mdb, "sqlmock")
	db.DB = dbx
	q := regexp.QuoteMeta

	mock.ExpectQuery(q(`select gap_claims.id, customers.last_name || ' ' || customers.first_name as insured_name, contract_number, status, date_of_claim_received, date_of_last_in, waiting_for, date_of_last_out, case_reserve, date_of_loss, state, users.first_name || ' ' || users.last_name as assigned_to from gap_claims join customers on customer_id = customers.id join users on owner_id = users.id where is_child = false and owner_id = 5103 and (status in ('D','C-NR','NG','C','CV')) order by status, date_of_last_out desc limit 20 offset 0`)).
		WillReturnRows(sqlmock.NewRows([]string{"id", "insured_name", "contract_number", "status", "date_of_claim_received", "date_of_last_in", "waiting_for", "date_of_last_out", "case_reserve", "date_of_loss", "state", "assigned_to"}).
			AddRow(15, "Lopez Paul", "GAPS978638", "WC", time.Time{}, time.Time{}, "1a,3,4,7", time.Time{}, 4004.04, time.Time{}, "UT", "Assigned To"))

	mock.ExpectQuery(q(`select count(*) from gap_claims join customers on customer_id = customers.id join users on owner_id = users.id where is_child = false and owner_id = 5103 and (status in ('D','C-NR','NG','C','CV'))`)).WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))

	mock.ExpectQuery(q(`select sum(case_reserve) from gap_claims join customers on customer_id = customers.id join users on owner_id = users.id where is_child = false and owner_id = 5103 and (status in ('D','C-NR','NG','C','CV'))`)).WillReturnRows(sqlmock.NewRows([]string{"totalEstimate"}).AddRow(4004.04))

	req, err := http.NewRequest("GET", "/api/gapclaims?status=Closed&user_id=5103&page=1", nil)
	if err != nil {
		t.Fatal(err)
	}

	rr := httptest.NewRecorder()
	handler := http.HandlerFunc(handlers.APIHandler(ClaimIndex))

	r := chi.NewRouter()
	r.HandleFunc("/api/gapclaims", handler)
	r.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v",
			status, http.StatusOK)
	}

	expected := `{"count":1,"gap_claims":[{"id":15,"insured_name":"Lopez Paul","contract_number":"GAPS978638","assigned_to":"Assigned To","status":"WC","date_of_claim_received":"0001-01-01T00:00:00Z","date_of_last_in":"0001-01-01T00:00:00Z","waiting_for":"1a,3,4,7","date_of_last_out":"0001-01-01T00:00:00Z","case_reserve":"4004.04","date_of_loss":"0001-01-01T00:00:00Z","state":"UT"}],"totalEstimate":4004.04}`
	if rr.Body.String() != expected {
		t.Errorf("handler returned unexpected body: got %v want %v",
			rr.Body.String(), expected)
	}

	if err := mock.ExpectationsWereMet(); err != nil {
		t.Errorf("there were unfulfilled expectations: %s", err)
	}
}

func DISABLEDTestGapClaimStatusAllActive(t *testing.T) {
	mdb, mock, err := sqlmock.New()
	if err != nil {
		t.Fatal(err)
	}
	dbx := sqlx.NewDb(mdb, "sqlmock")
	db.DB = dbx
	q := regexp.QuoteMeta

	mock.ExpectQuery(q(`select gap_claims.id, customers.last_name || ' ' || customers.first_name as insured_name, contract_number, status, date_of_claim_received, date_of_last_in, waiting_for, date_of_last_out, case_reserve, date_of_loss, state, users.first_name || ' ' || users.last_name as assigned_to from gap_claims join customers on customer_id = customers.id join users on owner_id = users.id where is_child = false and owner_id = 5103 and (status not in ('I','D','C-NR','NG','C')) order by status, date_of_last_out desc limit 20 offset 0`)).
		WillReturnRows(sqlmock.NewRows([]string{"id", "insured_name", "contract_number", "status", "date_of_claim_received", "date_of_last_in", "waiting_for", "date_of_last_out", "case_reserve", "date_of_loss", "state", "assigned_to"}).
			AddRow(15, "Lopez Paul", "GAPS978638", "WC", time.Time{}, time.Time{}, "1a,3,4,7", time.Time{}, 4004.04, time.Time{}, "UT", "Assigned To"))

	mock.ExpectQuery(q(`select count(*) from gap_claims join customers on customer_id = customers.id join users on owner_id = users.id where is_child = false and owner_id = 5103 and (status not in ('I','D','C-NR','NG','C'))`)).WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))

	mock.ExpectQuery(q(`select sum(case_reserve) from gap_claims join customers on customer_id = customers.id join users on owner_id = users.id where is_child = false and owner_id = 5103 and (status not in ('I','D','C-NR','NG','C'))`)).WillReturnRows(sqlmock.NewRows([]string{"totalEstimate"}).AddRow(4004.04))

	req, err := http.NewRequest("GET", "/api/gapclaims?status=All Active&user_id=5103&page=1", nil)
	if err != nil {
		t.Fatal(err)
	}

	rr := httptest.NewRecorder()
	handler := http.HandlerFunc(handlers.APIHandler(ClaimIndex))

	r := chi.NewRouter()
	r.HandleFunc("/api/gapclaims", handler)
	r.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v",
			status, http.StatusOK)
	}

	expected := `{"count":1,"gap_claims":[{"id":15,"insured_name":"Lopez Paul","contract_number":"GAPS978638","assigned_to":"Assigned To","status":"WC","date_of_claim_received":"0001-01-01T00:00:00Z","date_of_last_in":"0001-01-01T00:00:00Z","waiting_for":"1a,3,4,7","date_of_last_out":"0001-01-01T00:00:00Z","case_reserve":"4004.04","date_of_loss":"0001-01-01T00:00:00Z","state":"UT"}],"totalEstimate":4004.04}`
	if rr.Body.String() != expected {
		t.Errorf("handler returned unexpected body: got %v want %v",
			rr.Body.String(), expected)
	}

	if err := mock.ExpectationsWereMet(); err != nil {
		t.Errorf("there were unfulfilled expectations: %s", err)
	}
}

func DISABLEDTestGapClaimStatusInquiry(t *testing.T) {
	mdb, mock, err := sqlmock.New()
	if err != nil {
		t.Fatal(err)
	}
	dbx := sqlx.NewDb(mdb, "sqlmock")
	db.DB = dbx
	q := regexp.QuoteMeta

	mock.ExpectQuery(q(`select gap_claims.id, customers.last_name || ' ' || customers.first_name as insured_name, contract_number, status, date_of_claim_received, date_of_last_in, waiting_for, date_of_last_out, case_reserve, date_of_loss, state, users.first_name || ' ' || users.last_name as assigned_to from gap_claims join customers on customer_id = customers.id join users on owner_id = users.id where is_child = false and owner_id = 5103 and (status = 'I') order by status, date_of_last_out desc limit 20 offset 0`)).
		WillReturnRows(sqlmock.NewRows([]string{"id", "insured_name", "contract_number", "status", "date_of_claim_received", "date_of_last_in", "waiting_for", "date_of_last_out", "case_reserve", "date_of_loss", "state", "assigned_to"}).
			AddRow(15, "Lopez Paul", "GAPS978638", "WC", time.Time{}, time.Time{}, "1a,3,4,7", time.Time{}, 4004.04, time.Time{}, "UT", "Assigned To"))

	mock.ExpectQuery(q(`select count(*) from gap_claims join customers on customer_id = customers.id join users on owner_id = users.id where is_child = false and owner_id = 5103 and (status = 'I')`)).WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))

	mock.ExpectQuery(q(`select sum(case_reserve) from gap_claims join customers on customer_id = customers.id join users on owner_id = users.id where is_child = false and owner_id = 5103 and (status = 'I')`)).WillReturnRows(sqlmock.NewRows([]string{"totalEstimate"}).AddRow(4004.04))

	req, err := http.NewRequest("GET", "/api/gapclaims?status=In Inquiry&user_id=5103&page=1", nil)
	if err != nil {
		t.Fatal(err)
	}

	rr := httptest.NewRecorder()
	handler := http.HandlerFunc(handlers.APIHandler(ClaimIndex))

	r := chi.NewRouter()
	r.HandleFunc("/api/gapclaims", handler)
	r.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v",
			status, http.StatusOK)
	}

	expected := `{"count":1,"gap_claims":[{"id":15,"insured_name":"Lopez Paul","contract_number":"GAPS978638","assigned_to":"Assigned To","status":"WC","date_of_claim_received":"0001-01-01T00:00:00Z","date_of_last_in":"0001-01-01T00:00:00Z","waiting_for":"1a,3,4,7","date_of_last_out":"0001-01-01T00:00:00Z","case_reserve":"4004.04","date_of_loss":"0001-01-01T00:00:00Z","state":"UT"}],"totalEstimate":4004.04}`
	if rr.Body.String() != expected {
		t.Errorf("handler returned unexpected body: got %v want %v",
			rr.Body.String(), expected)
	}

	if err := mock.ExpectationsWereMet(); err != nil {
		t.Errorf("there were unfulfilled expectations: %s", err)
	}
}

func TestRecordNoteIndex(t *testing.T) {
	mdb, mock, err := sqlmock.New()
	if err != nil {
		t.Fatal(err)
	}
	dbx := sqlx.NewDb(mdb, "sqlmock")
	db.DB = dbx
	q := regexp.QuoteMeta

	mock.ExpectQuery(q(`select gap_claim_id, notes_text, record_notes.created_at, created_by_user_id, first_name, last_name
							from record_notes
								join users on (record_notes.created_by_user_id = users.id)
							where gap_claim_id = $1 union
							select gap_claims.id, customers_record_notes.notes_text, customers_record_notes.created_at, customers_record_notes.created_by_user_id, first_name, last_name
							from customers_record_notes
								join gap_claims on customers_record_notes.customer_id = gap_claims.customer_id
								join users on (customers_record_notes.created_by_user_id = users.id)
							where gap_claims.id = $1 order by created_at desc limit $2 offset $3`)).
		WillReturnRows(sqlmock.NewRows([]string{"gap_claim_id", "notes_text", "created_at", "created_by_user_id", "first_name", "last_name"}).
			AddRow(1, "Notes text", time.Time{}, 5103, "Gap", "Claims"))

	mock.ExpectQuery(q(`select count(*) from (select gap_claim_id, notes_text, record_notes.created_at, created_by_user_id, first_name, last_name
							from record_notes join users on (record_notes.created_by_user_id = users.id)
							where gap_claim_id = $1 union
							select gap_claims.id, customers_record_notes.notes_text, customers_record_notes.created_at, customers_record_notes.created_by_user_id, first_name, last_name
							from customers_record_notes
								join gap_claims on customers_record_notes.customer_id = gap_claims.customer_id
								join users on (customers_record_notes.created_by_user_id = users.id)
							where gap_claims.id = $1) as co`)).
		WillReturnRows(sqlmock.NewRows([]string{"count"}).
			AddRow(1))

	req, err := http.NewRequest("GET", "/api/gapclaims/record-notes", nil)
	if err != nil {
		t.Fatal(err)
	}

	rr := httptest.NewRecorder()
	handler := http.HandlerFunc(handlers.APIHandler(RecordNoteIndex))

	r := chi.NewRouter()
	r.HandleFunc("/api/gapclaims/record-notes", handler)
	r.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v",
			status, http.StatusOK)
	}

	expected := `{"count":1,"record_notes":[{"gap_claim_id":1,"notes_text":"Notes text","created_at":"0001-01-01T00:00:00Z","created_by_user_id":5103,"first_name":"Gap","last_name":"Claims"}]}`
	if rr.Body.String() != expected {
		t.Errorf("handler returned unexpected body: got %v want %v",
			rr.Body.String(), expected)
	}

	if err := mock.ExpectationsWereMet(); err != nil {
		t.Errorf("there were unfulfilled expectations: %s", err)
	}
}

func DISABLEDTestGapClaimByContract(t *testing.T) {
	mdb, mock, err := sqlmock.New()
	if err != nil {
		t.Fatal(err)
	}
	dbx := sqlx.NewDb(mdb, "sqlmock")
	db.DB = dbx
	q := regexp.QuoteMeta

	mock.ExpectQuery(q(`select * from gap_claims where contract_number = $1 and is_child=false limit 1`)).
		WillReturnRows(sqlmock.NewRows([]string{"id", "last_name", "first_name", "customer_id", "contract_number", "status", "date_of_claim_received", "date_of_last_in", "waiting_for", "date_of_last_out", "case_reserve", "date_of_loss", "state"}).
			AddRow(15, "Lopez", "Paul", 0, "GAPS978638", "C-NR", time.Time{}, time.Time{}, "1a,3,4,7,", time.Time{}, 4004.04, time.Time{}, "UT"))

	req, err := http.NewRequest("GET", "/api/gapclaims/contract/{id}", nil)
	if err != nil {
		t.Fatal(err)
	}

	rr := httptest.NewRecorder()
	handler := http.HandlerFunc(handlers.APIHandler(ClaimByContract))

	r := chi.NewRouter()
	r.HandleFunc("/api/gapclaims/contract/{id}", handler)
	r.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v",
			status, http.StatusOK)
	}

	expected := `{"gap_claim":{"id":15,"vin":"","make":"","model":"","year":0,"created_by_user_id":0,"status":"C-NR","status_change_description":"","date_of_claim_received":"0001-01-01T00:00:00Z","date_of_last_out":"0001-01-01T00:00:00Z","date_of_last_in":"0001-01-01T00:00:00Z","contract_number":"GAPS978638","date_of_loss":"0001-01-01T00:00:00Z","waiting_for":"1a,3,4,7,","customer_id":0,"first_name":"Paul","last_name":"Lopez","state":"UT","city":"","postal_code":"","street_address":"","email_address":"","phone_number":"","case_reserve":"4004.04","avg_case_reserve":"0","run_amortization_sheet_value":"0","calculate_amortization_value":false,"has_canceled_contracts":false,"other_label1":"","other_value1":"0","other_label2":"","other_value2":"0","other_label3":"","other_value3":"0","contracts":null,"has_settlement_amount":false,"settlement_amount":"0","has_number_of_delinquent_payments":false,"number_of_delinquent_payments_manager_flag":false,"number_of_delinquent_payments":0,"has_total_delinquent_payments_covered":false,"total_delinquent_payments_covered_manager_flag":false,"total_delinquent_payments_covered":"0","has_number_of_extensions":false,"number_of_extensions_manager_flag":false,"number_of_extensions":0,"has_total_extensions_covered":false,"total_extensions_covered_manager_flag":false,"total_extensions_covered":"0","has_negative_equity_amount":false,"negative_equity_amount":"0","has_insurance_check_amount":false,"insurance_check_amount":"0","is_valuation_report_available":false,"valuation_report_adjustments":"0","is_valuation_report_matches_base_value":false,"valuation_report_matches_base_value":"0","valuation_report_vin_matches":false,"has_valuation_report_prior_damage":false,"valuation_report_prior_damage_value":"0","has_valuation_report_misc_fee":false,"valuation_report_misc_fee_value":"0","valuation_report_mileage":0,"valuation_report_type":"","has_options_match_book_out_over_150_percent":false,"over_150_percent":"0","has_original_financing_contract":false,"original_financing_contract_value":"0","has_msrp_value":false,"msrp_value":"0","contract_number_matches":false,"bank_history_matches":false,"is_police_report_available":false,"has_insurance_policy_deductible":false,"insurance_policy_deductible_value_addition":"0","insurance_policy_deductible_value_subtraction":"0","insurance_policy_deductible_reason":"","has_bank_information":false,"is_full_loan_history_available":false,"payment_amount":"0","interest_rate":"0","first_payment_date":"0001-01-01T00:00:00Z","has_new_bank_information":false,"bank_id":0,"bank_vendor_id":"","bank_account_name":"","bank_account_number":"","bank_address_street1":"","bank_address_street2":"","bank_address_city":"","bank_address_state":"","bank_address_zip":"","contract_deal_date":"0001-01-01T00:00:00Z","contract_term_months":0,"last_action":"","owner_id":0,"child_claim_reason":"","parent_claim_id":{"Int64":0,"Valid":false},"is_child":false,"updated_at":"0001-01-01T00:00:00Z","updated_by_user_id":0,"updated_by_user_name":"","run_amortization_manager_flag":false,"other_label1_manager_flag":false,"other_label2_manager_flag":false,"other_label3_manager_flag":false,"insurance_payment_check_manager_flag":false,"settlement_letter_manager_flag":false,"has_negative_equity_amount_manager_flag":false,"valuation_report_manager_flag":false,"valuation_report_base_value_manager_flag":false,"valuation_report_vin_matches_manager_flag":false,"valuation_report_prior_damage_manager_flag":false,"valuation_report_misc_fee_manager_flag":false,"valuation_report_mileage_manager_flag":false,"valuation_report_dol_manager_flag":false,"valuation_report_type_manager_flag":false,"options_book_out_over_150_percent_manager_flag":false,"original_financing_manager_flag":false,"contract_number_matches_manager_flag":false,"bank_history_matches_manager_flag":false,"police_report_manager_flag":false,"insurance_deductible_addition_manager_flag":false,"insurance_deductible_subtraction_manager_flag":false,"bank_information_manager_flag":false,"full_loan_history_manager_flag":false,"loan_number_manager_flag":false,"payment_amount_manager_flag":false,"interest_rate_manager_flag":false,"first_payment_manager_flag":false,"contract_deal_date_manager_flag":false,"contract_term_months_manager_flag":false,"msrp_value_manager_flag":false,"is_cs_claim":false,"cs_check_amount":"0","not_paid_by_cs":false,"insurance_company":"","has_insurance_company":false,"policy_number":"","has_policy_number":false,"mileage_deduction":"0","has_mileage_deduction":false,"nada":"0","has_nada":false,"has_valuation_nada_difference":false,"mileage_deduction_manager_flag":false,"has_recovery":false,"has_estimate_with_photos":false,"nada_manager_flag":false,"gap_closed_at":"0001-01-01T00:00:00Z","recovery_check_amount":"0","recovery_status":"","is_in_progress":false,"child_claims":null}}`
	if rr.Body.String() != expected {
		t.Errorf("handler returned unexpected body: got %v want %v",
			rr.Body.String(), expected)
	}

	if err := mock.ExpectationsWereMet(); err != nil {
		t.Errorf("there were unfulfilled expectations: %s", err)
	}
}

func TestWorklistClaim_ToSlice(t *testing.T) {
	claimDate, _ := time.Parse(time.RFC3339, "2017-02-10T00:00:00+00:00")
	lastInDate, _ := time.Parse(time.RFC3339, "2017-02-11T00:00:00+00:00")
	lastOutDate, _ := time.Parse(time.RFC3339, "2017-02-12T00:00:00+00:00")
	dateOfLoss, _ := time.Parse(time.RFC3339, "2017-02-05T00:00:00+00:00")
	claim := worklistClaim{
		InsuredName:         "Stice Json",
		ContractNumber:      "GAP123456",
		Status:              "C",
		DateOfClaimReceived: claimDate,
		DateOfLastIn:        lastInDate,
		WaitingFor:          "1a,2b,3c,4b",
		DateOfLastOut:       lastOutDate,
		CaseReserve:         decimal.NewFromFloat(5000.30),
		DateOfLoss:          dateOfLoss,
		State:               "UT",
		AssignedTo:          "Assigned To",
	}
	expected := []string{"Stice Json", "GAP123456", "Assigned To", "C", "2017-02-10", "2017-02-11", "1a 2b 3c 4b", "2017-02-12",
		"5000.3", "2017-02-05", "UT"}
	result := claim.ToSlice()
	if !reflect.DeepEqual(result, expected) {
		t.Error("ToSlice failed to convert object to slice :expected value:", expected, result)
	}

}

func DISABLEDTestGapClaimIndexCSV(t *testing.T) {
	mdb, mock, err := sqlmock.New()
	if err != nil {
		t.Fatal(err)
	}
	dbx := sqlx.NewDb(mdb, "sqlmock")
	db.DB = dbx
	q := regexp.QuoteMeta

	mock.ExpectQuery(q(`select gap_claims.id, customers.last_name || ' ' || customers.first_name as insured_name, contract_number, status, date_of_claim_received, date_of_last_in, waiting_for, date_of_last_out, case_reserve, date_of_loss, state, users.first_name || ' ' || users.last_name as assigned_to from gap_claims join customers on customer_id = customers.id join users on owner_id = users.id where is_child = false and owner_id = 5103 order by status, date_of_last_out desc`)).
		WillReturnRows(sqlmock.NewRows([]string{"id", "insured_name", "contract_number", "status", "date_of_claim_received", "date_of_last_in", "waiting_for", "date_of_last_out", "case_reserve", "date_of_loss", "state", "assigned_to"}).
			AddRow(15, "Lopez Paul", "GAPS978638", "C", time.Time{}, time.Time{}, "1a,3,4,7", time.Time{}, 4004.04, time.Time{}, "UT", "Assigned To"))

	mock.ExpectQuery(q(`select count(*) from gap_claims join customers on customer_id = customers.id join users on owner_id = users.id where is_child = false and owner_id = 5103`)).WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))

	mock.ExpectQuery(q(`select sum(case_reserve) from gap_claims join customers on customer_id = customers.id join users on owner_id = users.id where is_child = false and owner_id = 5103`)).WillReturnRows(sqlmock.NewRows([]string{"totalEstimate"}).AddRow(4004.04))

	req, err := http.NewRequest("GET", "/api/gapclaims?user_id=5103&csv=true", nil)
	if err != nil {
		t.Fatal(err)
	}

	rr := httptest.NewRecorder()
	handler := http.HandlerFunc(handlers.APIHandler(ClaimIndex))

	r := chi.NewRouter()
	r.HandleFunc("/api/gapclaims", handler)
	r.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v",
			status, http.StatusOK)
	}

	expected := `{"count":1,"gap_claims":"Insured Name,Contract#,Assigned To,Status,Opened,Last In,Waiting For,Last Out,Estimate,DOL,State\nLopez Paul,GAPS978638,Assigned To,C,0001-01-01,0001-01-01,1a 3 4 7,0001-01-01,4004.04,0001-01-01,UT\n"}`
	if rr.Body.String() != expected {
		t.Errorf("handler returned unexpected body: got %v want %v",
			rr.Body.String(), expected)
	}

	if err := mock.ExpectationsWereMet(); err != nil {
		t.Errorf("there were unfulfilled expectations: %s", err)
	}
}

func DISABLEDTestGapClaimIndexSortAssignedTo(t *testing.T) {
	mdb, mock, err := sqlmock.New()
	if err != nil {
		t.Fatal(err)
	}
	dbx := sqlx.NewDb(mdb, "sqlmock")
	db.DB = dbx
	q := regexp.QuoteMeta

	mock.ExpectQuery(q(`select gap_claims.id, customers.last_name || ' ' || customers.first_name as insured_name, contract_number, status, date_of_claim_received, date_of_last_in, waiting_for, date_of_last_out, case_reserve, date_of_loss, state, users.first_name || ' ' || users.last_name as assigned_to from gap_claims join customers on customer_id = customers.id join users on owner_id = users.id where is_child = false and owner_id = 5103 order by assigned_to desc limit 20 offset 0`)).
		WillReturnRows(sqlmock.NewRows([]string{"id", "insured_name", "contract_number", "status", "date_of_claim_received", "date_of_last_in", "waiting_for", "date_of_last_out", "case_reserve", "date_of_loss", "state", "assigned_to"}).
			AddRow(15, "Lopez Paul", "GAPS978638", "C-NR", time.Time{}, time.Time{}, "1a,3,4,7,", time.Time{}, 4004.04, time.Time{}, "UT", "Assigned To"))

	mock.ExpectQuery(q(`select count(*) from gap_claims join customers on customer_id = customers.id join users on owner_id = users.id where is_child = false and owner_id = 5103`)).WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))

	mock.ExpectQuery(q(`select sum(case_reserve) from gap_claims join customers on customer_id = customers.id join users on owner_id = users.id where is_child = false and owner_id = 5103`)).WillReturnRows(sqlmock.NewRows([]string{"totalEstimate"}).AddRow(4004.04))

	req, err := http.NewRequest("GET", "/api/gapclaims?user_id=5103&assigned_to_sort=desc&page=1", nil)
	if err != nil {
		t.Fatal(err)
	}

	rr := httptest.NewRecorder()
	handler := http.HandlerFunc(handlers.APIHandler(ClaimIndex))

	r := chi.NewRouter()
	r.HandleFunc("/api/gapclaims", handler)
	r.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v",
			status, http.StatusOK)
	}

	expected := `{"count":1,"gap_claims":[{"id":15,"insured_name":"Lopez Paul","contract_number":"GAPS978638","assigned_to":"Assigned To","status":"C-NR","date_of_claim_received":"0001-01-01T00:00:00Z","date_of_last_in":"0001-01-01T00:00:00Z","waiting_for":"1a,3,4,7,","date_of_last_out":"0001-01-01T00:00:00Z","case_reserve":"4004.04","date_of_loss":"0001-01-01T00:00:00Z","state":"UT"}],"totalEstimate":4004.04}`
	if rr.Body.String() != expected {
		t.Errorf("handler returned unexpected body: got %v want %v",
			rr.Body.String(), expected)
	}

	if err := mock.ExpectationsWereMet(); err != nil {
		t.Errorf("there were unfulfilled expectations: %s", err)
	}
}
