# Config file for [Air](https://github.com/cosmtrek/air) in TOML format

# Working directory
# . or absolute path, please note that the directories following must be under root.
root = "." 
tmp_dir = "air_tmp"

[build]
# Just plain old shell command. You could use `make` as well.
cmd = "go build -o ./air_tmp/phizz main.go"
# Binary file yields from `cmd`.
bin = "air_tmp/phizz"
# Customize binary.
full_bin = "APP_ENV=dev APP_USER=air ./air_tmp/phizz"
# Watch these filename extensions.
include_ext = ["go", "tpl", "tmpl", "conf", "toml"]
# Ignore these filename extensions or directories.
exclude_dir = ["assets", "air_tmp", "tmp", "vendor", "node_modules", "migrations"]
# Watch these directories if you specified.
include_dir = []
# Exclude files.
exclude_file = []
# It's not necessary to trigger build each time file changes if it's too frequent.
delay = 1000 # ms
# Stop to run old binary when build errors occur.
stop_on_error = true
# This log file places in your tmp_dir.
log = "air_errors.log"

[log]
# Show log time
time = false

[color]
# Customize each part's color. If no color found, use the raw app log.
main = "magenta"
watcher = "cyan"
build = "yellow"
runner = "green"

[misc]
# Delete tmp directory on exit
clean_on_exit = true