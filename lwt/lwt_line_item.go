package lwt

import (
	"context"
	"database/sql"
	"encoding/json"
	"net/http"
	"strconv"
	"time"

	"phizz/db"
	"phizz/handlers"

	"github.com/go-chi/chi"
	"github.com/jmoiron/sqlx"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
	"gopkg.in/guregu/null.v3"
)

type lineItemsBase struct {
	ID              int             `json:"id" db:"id"`
	LWTClaimID      int             `json:"lwt_claim_id" db:"lwt_claim_id"`
	Description     string          `json:"description" db:"description"`
	Note            string          `json:"note" db:"note"`
	RequestedAmount decimal.Decimal `json:"requested_amount" db:"requested_amount"`
	ApprovedAmount  decimal.Decimal `json:"approved_amount" db:"approved_amount"`
	Status          string          `json:"status" db:"status"`
	DenialReasonID  null.Int        `json:"denial_reason_id" db:"denial_reason_id"`
	DenialNote      null.String     `json:"denial_note" db:"denial_note"`
	ImagesReceived  bool            `json:"is_images_received" db:"images_received"`
	ManagerApproval bool            `json:"manager_approval" db:"manager_approval"`
	CreatedByUserID int             `json:"created_by_user_id" db:"created_by_user_id"`
	CreatedAt       time.Time       `json:"created_at" db:"created_at"`
}

func lineItemFromReq(req *http.Request) (*lineItemsBase, error) {
	var lineItem lineItemsBase

	dec := json.NewDecoder(req.Body)
	err := dec.Decode(&lineItem)

	return &lineItem, errors.Wrap(err, "decoding Part request failed")
}

// LineItemShow returns single line item data
func LineItemShow(_ http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	ctx := req.Context()

	lineItemID, err := strconv.Atoi(chi.URLParam(req, "line_item_id"))
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Number conversion error for lineItemID", nil)
	}
	lineItem, err := lineItemByID(ctx, lineItemID)
	if err != nil {
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Could not get line item", nil)
	}
	return http.StatusOK, map[string]interface{}{"line_item": lineItem}
}

// LineItemCreate creates a new line item
func LineItemCreate(_ http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	ctx := req.Context()

	claimID, err := strconv.Atoi(chi.URLParam(req, "claim_id"))
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Number conversion error for claimID", nil)
	}
	claimExist, err := claimExists(ctx, claimID)
	if !claimExist || err != nil {
		handlers.ReportError(req, err)
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Could not get claim", nil)
	}
	lineItem, err := lineItemFromReq(req)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Malformed line item data for create.", nil)
	}

	lineItem.LWTClaimID = claimID
	lineItem.Status = db.LWTClaimComplaintStatusOpen
	lineItem.CreatedByUserID = user.ID

	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error beginning transaction for lwt claim update", nil)
	}

	lineItemID, err := insertLineItem(ctx, tx, lineItem)
	if err != nil {
		_ = tx.Rollback()
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error inserting line item", nil)
	}

	// Add note after creating new lineItem successfully
	_, err = insertRecordNote(ctx, tx, &recordNotePayload{
		LWTClaimID:      claimID,
		NotesText:       "New lineItem added",
		CreatedByUserID: user.ID,
	})
	if err != nil {
		_ = tx.Rollback()
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error inserting line item : Failed to add system note", nil)
	}

	err = tx.Commit()
	if err != nil {
		err = errors.Wrap(err, "database error committing transaction for lwt claim update")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error updating lwt claim", nil)
	}

	return http.StatusOK, map[string]interface{}{"line_item_id": lineItemID}
}

// LineItemUpdate updates a existing line item
func LineItemUpdate(_ http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	ctx := req.Context()

	lineItemID, err := strconv.Atoi(chi.URLParam(req, "id"))
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Number conversion error for claimID", nil)
	}

	lineItemExist, err := lineItemExists(ctx, lineItemID)
	if !lineItemExist || err != nil {
		handlers.ReportError(req, err)
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Could not get line item", nil)
	}

	lineItem, err := lineItemFromReq(req)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Malformed line item data for create.", nil)
	}

	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error beginning transaction for lwt claim update", nil)
	}

	err = updateLineItem(ctx, tx, lineItem)
	if err != nil {
		_ = tx.Rollback()
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error updating line item", nil)
	}

	// Add note after creating new lineItem successfully
	_, err = insertRecordNote(ctx, tx, &recordNotePayload{
		LWTClaimID:      lineItem.LWTClaimID,
		NotesText:       "LineItem updated",
		CreatedByUserID: user.ID,
	})
	if err != nil {
		_ = tx.Rollback()
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error updating line item : Failed to add system note", nil)
	}

	err = tx.Commit()
	if err != nil {
		err = errors.Wrap(err, "database error committing transaction for lwt claim update")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error updating line item", nil)
	}

	return http.StatusOK, map[string]interface{}{"line_item_id": lineItem.ID}
}

// LineItemDelete Deletes given line item
func LineItemDelete(_ http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	ctx := req.Context()

	lineItemID, err := strconv.Atoi(chi.URLParam(req, "id"))
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(errors.New("In valid line item id"), "Failed to delete line item", nil)
	}

	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error beginning transaction for deleting line item", nil)
	}

	claimID, err := deleteLineItem(ctx, tx, lineItemID)
	if err != nil {
		_ = tx.Rollback()
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error deleting Complaint", nil)
	}

	// Add note after deleting line item successfully
	_, err = insertRecordNote(ctx, tx, &recordNotePayload{
		LWTClaimID:      claimID,
		NotesText:       "Line Item deleted",
		CreatedByUserID: user.ID,
	})
	if err != nil {
		_ = tx.Rollback()
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error deleting line item : Failed to add system note", nil)
	}

	err = tx.Commit()
	if err != nil {
		err = errors.Wrap(err, "database error committing transaction for line item delete")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error deleting line item", nil)
	}

	return http.StatusOK, map[string]interface{}{"line_item_id": lineItemID}
}

func claimExists(ctx context.Context, claimID int) (bool, error) {
	id := 0
	err := db.Get().GetContext(ctx, &id, `select id from lwt_claims where id = $1`, claimID)
	if err != nil {
		if err == sql.ErrNoRows {
			return false, nil
		}
		return false, err
	}
	return true, nil
}

func insertLineItem(ctx context.Context, tx *sqlx.Tx, lineItem *lineItemsBase) (int, error) {
	insertQuery := `insert into lwt_claim_line_items(
		lwt_claim_id,
		description,
    	note,
    	requested_amount,
    	approved_amount,
		status,
    	denial_reason_id,
		denial_note,
    	created_at,
    	created_by_user_id)
	values (
		:lwt_claim_id,
		:description,
		:note,
		:requested_amount,
		:approved_amount,
		:status,
		:denial_reason_id,
		:denial_note,
		now() at time zone 'utc',
		:created_by_user_id) returning id`

	var id int
	stmt, err := tx.PrepareNamedContext(ctx, insertQuery)
	if err != nil {
		return id, errors.Wrap(err, "an error occurred in PrepareNamed function while adding line item.")
	}
	defer func() { _ = stmt.Close() }()

	err = stmt.GetContext(ctx, &id, lineItem)
	if err != nil {
		return id, errors.Wrap(err, "an error occurred while trying to add the line item to the database.")
	}

	return id, err
}

func lineItemByID(ctx context.Context, ID int) (*lineItemsBase, error) {
	var lineItem lineItemsBase
	err := db.Get().Unsafe().GetContext(ctx, &lineItem, `select * from lwt_claim_line_items where id = $1`, ID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, errors.Wrap(err, "error loading line items")
	}

	return &lineItem, nil
}

func lineItemExists(ctx context.Context, ID int) (bool, error) {
	id := 0
	err := db.Get().Unsafe().GetContext(ctx, &id, `select id from lwt_claim_line_items where id = $1`, ID)
	if err != nil {
		if err == sql.ErrNoRows {
			return false, nil
		}
		return false, errors.Wrap(err, "error loading line items")
	}

	return true, nil
}

func deleteLineItem(ctx context.Context, tx *sqlx.Tx, lineItemID int) (int, error) {
	var lwtClaimID int
	err := tx.GetContext(ctx, &lwtClaimID, `delete from lwt_claim_line_items where id = $1 returning lwt_claim_id`, lineItemID)
	if err != nil {
		return lwtClaimID, errors.Wrap(err, "could not delete complaint")
	}
	return lwtClaimID, nil
}

func updateLineItem(ctx context.Context, tx *sqlx.Tx, lineItem *lineItemsBase) error {
	query := `update lwt_claim_line_items
				set description= :description,
					note = :note,
					requested_amount = :requested_amount,
					approved_amount = :approved_amount,
					status = :status,
					denial_reason_id = :denial_reason_id,
					images_received = :images_received,
					denial_note = :denial_note,
					manager_approval = :manager_approval
				where id = :id`

	stmt, err := tx.PrepareNamedContext(ctx, query)
	if err != nil {
		return errors.Wrap(err, "error updating line item, PrepareNamed failed")
	}
	defer func() { _ = stmt.Close() }()

	_, err = stmt.Exec(lineItem)
	if err != nil {
		return errors.Wrap(err, "error updating line item, database error")
	}

	return nil
}
