package lwt

import (
	"context"
	"database/sql"
	"encoding/base64"
	"encoding/json"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"

	"phizz/conf"
	"phizz/db"
	"phizz/email"
	"phizz/handlers"
	"phizz/randstr"

	"github.com/go-chi/chi"
	newrelic "github.com/newrelic/go-agent"
	"github.com/pkg/errors"
)

type emailBodyPayload struct {
	EmailBody  string `json:"email_body" db:"-"`
	LetterType string `json:"letter_type" db:"-"`
}

func (p emailBodyPayload) validate() (map[string]string, error) {
	v := map[string]string{}
	if len(p.EmailBody) == 0 {
		v["email_body"] = "email_body is required"
	}
	if len(p.LetterType) == 0 {
		v["letter_type"] = "letter_type is required"
	}
	return v, nil
}

// ClaimLetter saves file to s3 and/or sends email to customer
func ClaimLetter(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	ctx := req.Context()
	const errTxt = "Error sending email"
	id := chi.URLParam(req, "id")
	document := documentPayload{}
	document.LWTClaimID, _ = strconv.Atoi(id)

	document.CreatedByUserID = user.ID

	emailBody, err := getEmailBodyFromPayload(req.Body)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Bad request", nil)
	}
	decodedString, err := base64.StdEncoding.DecodeString(emailBody.EmailBody)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error in decoding email body"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error in decoding email body", nil)
	}
	emailBody.EmailBody = string(decodedString)

	formErrs, err := emailBody.validate()
	if err != nil {
		err = errors.Wrap(err, "error validating email content")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error during validation of email", nil)
	}
	if len(formErrs) > 0 {
		return http.StatusBadRequest, map[string]interface{}{
			"validation_errors": formErrs,
		}
	}

	emailBodyPdf, err := handlers.HTMLToPdf(emailBody.EmailBody)
	if err != nil {
		err = errors.WithMessage(err, "error in HTML to PDF conversion")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, errTxt, nil)
	}
	document.FileContent = emailBodyPdf

	filePrefix := "LETTER_"
	documentTypeKey := LWTLetter
	if req.FormValue("email") == "true" {
		filePrefix = "EMAIL_"
		documentTypeKey = LWTEmail
	}

	documentTypeID := 0
	err = db.Get().Unsafe().GetContext(ctx, &documentTypeID, `select id from lwt_claim_document_types where key=$1`, documentTypeKey)
	if err != nil {
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, errTxt, nil)
	}
	document.DocumentTypeID = documentTypeID

	// send email
	if req.FormValue("email") == "true" {
		// Get email address for claim
		emailAddress, err := getEmailAddress(ctx, req, id)
		if err != nil {
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error in getting email address", nil)
		}

		if emailAddress == "" {
			return http.StatusNotFound, handlers.ErrorMessage(err, "Unable to find email address.", nil)
		}

		contractNumber, err := getContractNumber(ctx, id)
		if err != nil {
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error in getting contract number", nil)
		}
		err = email.SendHTMLSync(conf.Get().LWTEmail.From, []string{emailAddress}, "Contract Number# "+contractNumber, []byte(emailBody.EmailBody))
		if err != nil {
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error in sending email", nil)
		}
	}

	// send pdf to s3 for storing
	document.FileName = filePrefix + id + "_" + randstr.StringN(20)
	document.FileType = ".pdf"
	document.LetterType = emailBody.LetterType

	documentID, err := saveDocument(ctx, &document)
	if err != nil {
		err = errors.Wrap(err, "error in saving pdf to s3")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, errTxt, nil)
	}

	url, err := documentDownload(ctx, documentID)
	if err != nil {
		err = errors.Wrap(err, "error in getting download url")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Email is sent, failure in getting the s3 download url", nil)
	}

	return http.StatusOK, map[string]interface{}{"s3_document_url": url}
}

func getEmailBodyFromPayload(reader io.Reader) (emailBodyPayload, error) {
	var p emailBodyPayload

	dec := json.NewDecoder(reader)
	err := dec.Decode(&p)
	if err != nil {
		err = errors.Wrap(err, "error decoding email body payload")
		return p, err
	}

	return p, nil
}

func getEmailAddress(ctx context.Context, req *http.Request, id string) (string, error) {
	contractCode, err := getContractNumber(ctx, id)
	if err != nil {
		return "", errors.Wrap(err, "error loading email address from database.")
	}
	txn := newrelic.FromContext(ctx)
	contract, err := handlers.GetContractByID(txn, req, contractCode)
	if err != nil {
		return "", errors.Wrap(err, "error loading email address from database.")
	}
	return contract.CustomerDetails.Email, nil
}

func getContractNumber(ctx context.Context, id string) (string, error) {
	query := "select contract_number from lwt_claims where id=$1"
	contractNumber := ""
	err := db.Get().GetContext(ctx, &contractNumber, query, id)
	if err != nil {
		if err == sql.ErrNoRows {
			return "", errors.Wrap(err, "the LWT claim was not found")
		}
		return "", errors.Wrap(err, "error loading LWT claim from database.")
	}
	return contractNumber, nil
}

// ClaimLetterIndex returns a list of record notes for given contract number
func ClaimLetterIndex(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	id := chi.URLParam(req, "id")
	ctx := req.Context()
	var letters []struct {
		DocumentID string    `json:"document_id" db:"document_id"`
		LetterType string    `json:"letter_type" db:"letter_type"`
		SentAt     time.Time `json:"sent_at" db:"sent_at"`
		FileName   string    `json:"-" db:"file_name"`
	}

	var args []interface{}
	args = append(args, id)

	query := `select id as document_id, letter_type, created_at as sent_at, file_name from lwt_claim_documents where lwt_claim_id = $1
	and document_type_id in (select id from lwt_claim_document_types where key in ('lwt_letter','lwt_email')) order by sent_at asc`
	err := db.Get().SelectContext(ctx, &letters, query, args...)
	if err != nil {
		err = errors.Wrap(err, "Database error getting email letters")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting email letters data", nil)
	}
	for i, letter := range letters {
		if strings.Contains(letter.FileName, "EMAIL_") {
			letters[i].LetterType = "Email " + letters[i].LetterType
		} else {
			letters[i].LetterType = "Letter " + letters[i].LetterType
		}
	}
	return http.StatusOK, map[string]interface{}{"letters": letters, "count": len(letters)}
}
