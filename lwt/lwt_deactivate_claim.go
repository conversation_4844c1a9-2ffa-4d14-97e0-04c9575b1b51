package lwt

import (
	"context"
	"log"

	"phizz/db"

	"github.com/jmoiron/sqlx"
	"github.com/pkg/errors"
)

// DeactivateOldClaims deactivates unfinished claims which are older than 120 days and are in
// 'InProcess', 'PendingApproval', 'Inquiry', 'PendingDenial' status
func DeactivateOldClaims(ctx context.Context) error {

	// get all unfinished claims not updated in 120 days
	q := `select lc.id, lc.contract_number
		from lwt_claims lc 
		    join lwt_claim_updates lcu 
		        on lcu.lwt_claim_id = lc.id
		where status in (?)
		group by lc.id, lc.contract_number
		having max(updated_at) < (current_date - 120)`

	claimsQ, args, err := sqlx.In(q, []string{
		db.LwtClaimStatusClaimInProcess,
		db.LwtClaimStatusInquiry,
		db.LwtClaimStatusPendingApproval,
		db.LwtClaimStatusPendingDenial,
	})

	if err != nil {
		return errors.Wrap(err, "error in getting old claims for deactivation, In query failed")
	}
	query := db.Get().Rebind(claimsQ)

	var claims []struct {
		ID             int    `db:"id"`
		ContractNumber string `db:"contract_number"`
	}
	err = db.Get().SelectContext(ctx, &claims, query, args...)
	if err != nil {
		return errors.Wrap(err, "error in DeactivateOldClaims getting old claims")
	}

	if len(claims) == 0 {
		log.Println("no old claims found for deactivation")
		return nil
	}

	// Get system user
	userID := 0
	err = db.Get().GetContext(ctx, &userID, "select id from users where first_name = $1 limit 1", db.SystemUserName)
	if err != nil {
		return errors.Wrap(err, "database error while getting system user")
	}

	for _, claim := range claims {
		tx, err := db.Get().BeginTxx(ctx, nil)
		if err != nil {
			return errors.Wrap(err, "database error beginning transaction for DeactivateOldClaims")
		}

		_, err = db.Get().ExecContext(ctx, "update lwt_claims set status = $1 where id=$2", db.LwtClaimStatusDeactivated, claim.ID)
		if err != nil {
			return errors.Wrap(err, "error in DeactivateOldClaims for: "+claim.ContractNumber)
		}

		err = claimUpdated(ctx, tx, claim.ID, userID)
		if err != nil {
			_ = tx.Rollback()
			return errors.Wrap(err, "error inserting lwt_claim_updates in DeactivateOldClaims")
		}
		err = tx.Commit()
		if err != nil {
			return errors.Wrap(err, "error in committing transaction for DeactivateOldClaims")
		}
		log.Printf("Deactivated claim contract# %s ID %d", claim.ContractNumber, claim.ID)
	}

	return nil
}
