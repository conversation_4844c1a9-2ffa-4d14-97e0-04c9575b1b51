package lwt

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"phizz/conf"
	"phizz/db"
	"phizz/handlers"
	"phizz/s3util"
	"phizz/util"

	"github.com/go-chi/chi"
	"github.com/jmoiron/sqlx"
	newrelic "github.com/newrelic/go-agent"
	"github.com/pkg/errors"
	null "gopkg.in/guregu/null.v3"
)

type documentBase struct {
	ID              int         `json:"id" db:"id"`
	LWTClaimID      int         `json:"lwt_claim_id" db:"lwt_claim_id"`
	S3bucket        string      `json:"s3_bucket" db:"s3_bucket"`
	FileName        string      `json:"file_name" db:"file_name"`
	CreatedByUserID int         `json:"created_by_user_id" db:"created_by_user_id"`
	CreatedAt       time.Time   `json:"created_at" db:"created_at"`
	DocumentTypeID  int         `json:"document_type_id" db:"document_type_id"`
	LetterType      null.String `json:"letter_type" db:"letter_type"`
	DeletedAt       null.Time   `json:"deleted_at" db:"deleted_at"`
	DeletedByUserID null.Int    `json:"deleted_by_user_id" db:"deleted_by_user_id"`
}

type documentType struct {
	ID        int            `json:"id" db:"id"`
	Name      string         `json:"name" db:"name"`
	Key       string         `json:"key" db:"key"`
	Required  bool           `json:"required" db:"required"`
	DocType   string         `json:"doc_type" db:"doc_type"`
	Documents []documentBase `json:"documents" db:"-"`
}

type documentPayload struct {
	ID              int    `json:"-" db:"id"`
	LWTClaimID      int    `json:"lwt_claim_id" db:"lwt_claim_id"`
	DocumentTypeID  int    `json:"document_type_id" db:"document_type_id"`
	FileContent     string `json:"file_content" db:"-"`
	CreatedByUserID int    `json:"-" db:"created_by_user_id"`
	FileName        string `json:"file_name" db:"file_name"`
	FileType        string `json:"file_type" db:"-"`
	S3Bucket        string `json:"-" db:"s3_bucket"`
	LetterType      string `json:"letter_type" db:"letter_type"`
}

func (p *documentPayload) clean() {
	s := strings.TrimSpace

	p.FileName = s(p.FileName)
	p.FileType = s(p.FileType)
}

func (p documentPayload) validate() (map[string]string, error) {

	v := map[string]string{}
	if p.CreatedByUserID == 0 {
		v["created_by_user_id"] = "CreatedByUserID is required"
	}
	if p.LWTClaimID == 0 {
		v["vta_claim_id"] = "LWT claim ID is required."
	}
	if len(p.FileName) < 1 {
		v["file_name"] = "File name is required."
	}
	if len(p.FileType) < 1 {
		v["file_type"] = "File type is required."
	} else if !handlers.IsValidFileType(p.FileType) {
		v["file_type"] = "File type is not valid"
	}
	if len(p.FileContent) < 1 {
		v["file_content"] = "File content is required"
	} else if len(p.FileContent) > conf.Get().AWS.MaxSize {
		v["file_content"] = "File content size is more than " + strconv.Itoa(conf.Get().AWS.MaxSize)
	}
	if p.DocumentTypeID <= 0 {
		v["document_type_id"] = "Invalid Document Type ID"
	}
	return v, nil
}

func getLWTDocumentFromPayload(reader io.Reader) (documentPayload, error) {
	var p documentPayload

	dec := json.NewDecoder(reader)
	err := dec.Decode(&p)
	if err != nil {
		err = errors.Wrap(err, "error decoding document payload")
		return p, err
	}

	return p, nil
}

// DocumentTypeIndex returns all document types for lwt claims
func DocumentTypeIndex(_ http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	ctx := req.Context()

	lwtDocuments, err := getDocumentTypes(ctx)
	if err != nil {
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting document list", nil)
	}

	return http.StatusOK, map[string]interface{}{"lwt_documents": lwtDocuments}
}

// SaveDocument saves document to s3 and returns documentID from the lwt_claim_document table
func SaveDocument(_ http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	ctx := req.Context()

	p, err := getLWTDocumentFromPayload(req.Body)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Bad request", nil)
	}

	p.clean()
	p.CreatedByUserID = user.ID
	formErrs, err := p.validate()
	if err != nil {
		err = errors.Wrap(err, "error validating document")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error during validation", nil)
	}
	if len(formErrs) > 0 {
		return http.StatusBadRequest, map[string]interface{}{
			"validation_errors": formErrs,
		}
	}
	documentID, err := saveDocument(ctx, &p)
	if err != nil {
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error in saving document", nil)
	}
	return http.StatusOK, map[string]interface{}{"lwt_claim_document": documentID}

}

// DocumentIndex returns all documents for given claim
func DocumentIndex(_ http.ResponseWriter, req *http.Request, _ db.User) (int, map[string]interface{}) {
	ctx := req.Context()

	id, err := strconv.Atoi(chi.URLParam(req, "id"))
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Bad request", nil)
	}

	var docs struct {
		LWTDocuments []documentType `json:"lwt_documents"`
	}

	docs.LWTDocuments, err = getDocumentTypes(ctx)
	if err != nil {
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting document list", nil)
	}

	var documents []documentBase
	query := `select * from lwt_claim_documents where lwt_claim_id = $1 and deleted_at is null`
	err = db.Get().Unsafe().SelectContext(ctx, &documents, query, id)
	if err != nil && err != sql.ErrNoRows {
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting document list", nil)
	}

	for idx, v := range docs.LWTDocuments {
		for _, d := range documents {
			if d.DocumentTypeID == v.ID {
				docs.LWTDocuments[idx].Documents = append(docs.LWTDocuments[idx].Documents, d)
			}
		}
	}

	return http.StatusOK, map[string]interface{}{"docs": docs}
}

// DocumentDownload redirects to a pre-signed S3 URL for a the document
func DocumentDownload(w http.ResponseWriter, req *http.Request, user db.User) {
	ctx := req.Context()

	documentID, err := strconv.Atoi(chi.URLParam(req, "id"))
	if err != nil {
		fmt.Fprint(w, "Bad request. Could not read document ID.")
	}

	cf := struct {
		S3Bucket string `db:"s3_bucket"`
		S3Key    string `db:"s3_key"`
	}{}
	err = db.Get().GetContext(ctx, &cf, `select s3_bucket, file_name s3_key from lwt_claim_documents where deleted_at is null and id = $1`, documentID)
	if err != nil {
		if err == sql.ErrNoRows {
			fmt.Fprint(w, "The document not found")
		}
		handlers.ReportError(req, err)
		fmt.Fprint(w, "Error loading document data for download")
	}

	_, fileName := filepath.Split(cf.S3Key)
	extn := filepath.Ext(cf.S3Key)
	contentType := util.ContentTypeByExtension(extn)

	reverseProxy := s3util.GetS3ReverseProxy()
	signedURL, err := reverseProxy.GetSecureURL(
		s3util.DefaultRegion, cf.S3Bucket, url.PathEscape(cf.S3Key),
		url.PathEscape(fileName), contentType, user,
		time.Minute*conf.Get().S3ReverseProxy.DefaultLinkTimeoutMinutes)
	if err != nil {
		fmt.Fprint(w, "Error in downloading document")
	}

	http.Redirect(w, req, signedURL, http.StatusTemporaryRedirect)
}

// documentDownload returns a pre-signed S3 URL for a the document
func documentDownload(ctx context.Context, documentID int) (string, error) {
	cf := struct {
		S3Bucket string `db:"s3_bucket"`
		FileName string `db:"file_name"`
	}{}
	err := db.Get().GetContext(ctx, &cf, `select s3_bucket, file_name from lwt_claim_documents where deleted_at is null and id = $1`, documentID)
	if err != nil {
		if err == sql.ErrNoRows {
			return "", errors.Wrap(err, "The document not found")
		}
		return "", errors.Wrap(err, "error loading document data for download")
	}
	url, err := s3util.GetFileURL(cf.FileName, cf.S3Bucket)
	if err != nil {
		return "", errors.Wrap(err, "error getting download URL")
	}
	return url, nil
}

// DocumentDelete deletes S3 object
func DocumentDelete(_ http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	ctx := req.Context()

	id, err := strconv.Atoi(chi.URLParam(req, "id"))
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Bad request", nil)
	}

	updateDocumentFlag, err := lwtDocumentDelete(ctx, id, user.ID)
	if err != nil {
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Could not delete document", nil)
	}
	return http.StatusOK, map[string]interface{}{"lwt_claim_document": id, "update_document_flag": updateDocumentFlag}
}

func lwtDocumentDelete(ctx context.Context, documentID, userID int) (bool, error) {
	const errTxt = "Error deleting Document"

	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		err = errors.Wrap(err, "error starting document delete transaction")
		return false, errors.Wrap(err, errTxt)
	}

	var documentData []documentBase
	query := `select lcd.* from lwt_claim_documents lcd 
			where lcd.deleted_at is null 
			  	and lcd.lwt_claim_id = (select lwt_claim_id from lwt_claim_documents where id =$1)`
	err = tx.SelectContext(ctx, &documentData, query, documentID)
	if err != nil {
		_ = tx.Rollback()
		if err == sql.ErrNoRows {
			return false, errors.Wrap(err, "The document not found")
		}
		return false, errors.Wrap(err, "error loading document data for delete")
	}

	var deletedDocument documentBase
	for _, v := range documentData {
		if v.ID == documentID {
			deletedDocument = v
		}
	}

	// get document type details
	var docType documentType
	err = tx.GetContext(ctx,
		&docType,
		`select * from lwt_claim_document_types where id = $1`,
		deletedDocument.DocumentTypeID)
	if err != nil {
		_ = tx.Rollback()
		return false, errors.Wrap(err, "error loading document type data for delete")
	}

	// Check if we need to uncheck the document checkbox
	updateDocumentFlag := true
	for _, v := range documentData {
		// check if we have any other document for same document type
		if deletedDocument.ID != v.ID &&
			deletedDocument.DocumentTypeID == v.DocumentTypeID {
			updateDocumentFlag = false
		}
	}

	if updateDocumentFlag {
		query = `update lwt_claims set ` + docType.Key + ` = false where id = $1`
		_, err = tx.ExecContext(ctx, query, deletedDocument.LWTClaimID)
		if err != nil {
			_ = tx.Rollback()
			return false, errors.Wrap(err, "error updating document flag in claim")
		}
	}

	// delete document
	query = `update lwt_claim_documents set deleted_at=now() at time zone 'utc', deleted_by_user_id=$1 where id = $2`
	_, err = tx.ExecContext(ctx, query, userID, documentID)
	if err != nil {
		_ = tx.Rollback()
		return false, errors.Wrap(err, "error deleting document")
	}

	// add record note
	var recordNote recordNotePayload
	recordNote.LWTClaimID = deletedDocument.LWTClaimID
	recordNote.CreatedByUserID = userID
	recordNote.CreatedAt = time.Now()
	recordNote.NotesText = "Deleted the attached file " + deletedDocument.FileName + " to the " + docType.Name
	_, err = insertRecordNote(ctx, tx, &recordNote)
	if err != nil {
		_ = tx.Rollback()
		return false, errors.Wrap(err, "error adding notes for delete document")
	}

	err = tx.Commit()
	if err != nil {
		err = errors.Wrap(err, "error committing document transaction")
		return false, errors.Wrap(err, errTxt)
	}

	return updateDocumentFlag, nil
}

func getDocumentTypes(ctx context.Context) ([]documentType, error) {
	query := `select * from lwt_claim_document_types`
	var docTypes []documentType

	err := db.Get().Unsafe().SelectContext(ctx, &docTypes, query)
	if err != nil {
		return docTypes, errors.Wrap(err, "error getting document tyoes")
	}
	return docTypes, err
}

func saveDocument(ctx context.Context, p *documentPayload) (int, error) {
	const errTxt = "Error saving Document"
	txn := newrelic.FromContext(ctx)

	documentID := 0
	contractNumber, err := getContractNumber(ctx, strconv.Itoa(p.LWTClaimID))
	if err != nil {
		return documentID, errors.Wrap(err, "error loading lwt claim from database.")
	}

	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		err = errors.Wrap(err, "error starting document upload transaction")
		return documentID, errors.Wrap(err, errTxt)
	}

	if len(p.FileContent) > 0 {
		bucket := s3util.Bucket()
		p.S3Bucket = bucket
		name := "lwt-claims/" + contractNumber + "/" + p.FileName + "_" + strconv.Itoa(p.DocumentTypeID) + p.FileType
		p.FileName = name
		data, err := base64.StdEncoding.DecodeString(p.FileContent)
		if err != nil {
			_ = tx.Rollback()
			err = errors.Wrap(err, "error base64 decoding file data")
			return documentID, errors.Wrap(err, errTxt)
		}
		err = s3util.Put(txn, bytes.NewReader(data), s3util.DefaultRegion, p.S3Bucket, p.FileName)
		if err != nil {
			_ = tx.Rollback()
			err = errors.Wrap(err, "error uploading document file to s3")
			return documentID, errors.Wrap(err, errTxt)
		}
	}

	documentID, err = insertLwtDocumentEntry(ctx, tx, p)
	if err != nil {
		_ = tx.Rollback()
		return documentID, errors.Wrap(err, errTxt)
	}

	err = tx.Commit()
	if err != nil {
		err = errors.Wrap(err, "error committing document transaction")
		return documentID, errors.Wrap(err, errTxt)
	}

	return documentID, nil
}

func insertLwtDocumentEntry(ctx context.Context, tx *sqlx.Tx, payload *documentPayload) (int, error) {
	insertQuery := `insert into lwt_claim_documents (lwt_claim_id, document_type_id, s3_bucket, file_name, letter_type, created_by_user_id, created_at) values
	 (:lwt_claim_id, :document_type_id, :s3_bucket, :file_name, :letter_type, :created_by_user_id, now() at time zone 'utc') returning id`
	documentID := 0
	stmt, err := tx.PrepareNamedContext(ctx, insertQuery)
	if err != nil {
		err = errors.Wrap(err, "error creating document creating statement")
		return documentID, err
	}
	defer func() { _ = stmt.Close() }()

	err = stmt.GetContext(ctx, &documentID, payload)
	return documentID, err
}
