package lwt

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"phizz/db"
	"phizz/handlers"

	"github.com/go-chi/chi"
	"github.com/jmoiron/sqlx"
	"github.com/pkg/errors"
)

type recordNotePayload struct {
	LWTClaimID      int       `json:"id" db:"lwt_claim_id"`
	IsManual        bool      `json:"is_manual" db:"is_manual"`
	NotesText       string    `json:"notes_text" db:"notes_text"`
	CreatedAt       time.Time `json:"created_at" db:"created_at"`
	CreatedByUserID int       `json:"created_by_user_id" db:"created_by_user_id"`
}

// RecordNoteCreate creates a new record note in lwt claim
func RecordNoteCreate(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	ctx := req.Context()
	recordNote, err := recordNoteFromReq(req)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(nil, "Malformed Record Note data for create.", nil)
	}
	cleanRecordNote(recordNote)
	formErrors, err := validateRecordNote(recordNote)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(errors.Wrap(err, "error validating Record Note"),
			"An error occurred validating the form values.", nil)
	}
	if len(formErrors) > 0 {
		return http.StatusBadRequest, handlers.ErrorMessage(nil, "Form validations errors.",
			map[string]interface{}{"errors": formErrors})
	}

	recordNote.CreatedByUserID = user.ID
	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error beginning transaction for LWT claim create", nil)
	}
	noteID, err := insertRecordNote(ctx, tx, recordNote)
	if err != nil {
		_ = tx.Rollback()
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error inserting RecordNote", nil)
	}
	err = tx.Commit()
	if err != nil {
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error committing transaction for inserting RecordNote", nil)
	}

	return http.StatusOK, map[string]interface{}{"record_note_id": noteID}
}

// InsertRecordNote records note for claim
func insertRecordNote(ctx context.Context, tx *sqlx.Tx, recordNote *recordNotePayload) (int, error) {
	insertQuery := `insert into lwt_record_notes (is_manual,created_at,lwt_claim_id,notes_text,created_by_user_id)
	 values (:is_manual,now() at time zone 'utc',:lwt_claim_id,:notes_text,:created_by_user_id) returning id`
	id := 0

	var stmt *sqlx.NamedStmt
	var err error
	if tx != nil {
		stmt, err = tx.PrepareNamedContext(ctx, insertQuery)
	} else {
		stmt, err = db.Get().PrepareNamedContext(ctx, insertQuery)
	}
	if err != nil {
		return id, errors.Wrap(err, "an error occurred in PrepareNamed function while adding RecordNote.")
	}
	defer func() { _ = stmt.Close() }()

	err = stmt.GetContext(ctx, &id, recordNote)
	if err != nil {
		return id, errors.Wrap(err, "an error occurred while trying to add the RecordNote to the database.")
	}

	return id, err
}

// RecordNoteIndex returns a list of record notes for given contract number
func RecordNoteIndex(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	ctx := req.Context()
	claimID := chi.URLParam(req, "lwt_claim_id")
	var recordNotes []struct {
		LWTClaimID      int       `json:"lwt_claim_id" db:"lwt_claim_id"`
		NotesText       string    `json:"notes_text" db:"notes_text"`
		CreatedAt       time.Time `json:"created_at" db:"created_at"`
		CreatedByUserID int       `json:"created_by_user_id" db:"created_by_user_id"`
		UserName        string    `json:"user_name" db:"user_name"`
		FirstName       string    `json:"first_name" db:"first_name"`
		LastName        string    `json:"last_name" db:"last_name"`
	}

	args := []interface{}{}
	args = append(args, claimID)

	notesQuery := `select lwt_record_notes.lwt_claim_id, lwt_record_notes.notes_text, 
		lwt_record_notes.created_at, lwt_record_notes.created_by_user_id, first_name, last_name, 
		first_name || ' ' || last_name as user_name
	from lwt_record_notes
		join users on (lwt_record_notes.created_by_user_id = users.id)
	where lwt_claim_id = $1 and is_manual = $2`

	orderOffsetClause := ` order by created_at desc limit $3 offset $4`

	var listQuery string
	var countQuery string
	listQuery = notesQuery + orderOffsetClause
	countQuery = fmt.Sprintf("select count(*) from (%s) as co", notesQuery)
	if req.FormValue("is_manual") == "true" {
		args = append(args, true)
	} else {
		args = append(args, false)
	}

	args = append(args, handlers.PerPageEntries)
	args = append(args, (handlers.GetPage(req)-1)*handlers.PerPageEntries)

	err := db.Get().SelectContext(ctx, &recordNotes, listQuery, args...)
	if err != nil {
		_ = errors.Wrap(err, "database error getting record notes lists")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting record notes lists data", nil)
	}

	count := 0
	err = db.Get().GetContext(ctx, &count, countQuery, args[0], args[1])
	if err != nil {
		_ = errors.Wrap(err, "database error getting record notes lists count")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting record notes lists count", nil)
	}
	return http.StatusOK, map[string]interface{}{"record_notes": recordNotes, "count": count}
}

func recordNoteFromReq(req *http.Request) (*recordNotePayload, error) {
	var recordNote recordNotePayload

	dec := json.NewDecoder(req.Body)
	err := dec.Decode(&recordNote)

	return &recordNote, errors.Wrap(err, "decoding RecordNote request failed")
}

// cleanRecordNote cleans up leading and trailing white-space etc...
func cleanRecordNote(recordNote *recordNotePayload) {
	recordNote.NotesText = strings.TrimSpace(recordNote.NotesText)
}

// validateRecordNote validates a NewClaim record for correctness
func validateRecordNote(recordNote *recordNotePayload) (map[string]string, error) {
	formErrors := map[string]string{}

	if recordNote.LWTClaimID == 0 {
		formErrors["lwt_claim_id"] = "LWT claim id is required"
	}
	if recordNote.NotesText == "" {
		formErrors["lwt_record_notes"] = "Notes text is required"
	}
	return formErrors, nil
}
