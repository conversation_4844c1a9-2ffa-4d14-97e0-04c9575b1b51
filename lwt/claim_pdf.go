package lwt

import (
	"net/http"
	"strconv"

	"phizz/db"
	"phizz/handlers"

	"github.com/go-chi/chi"
	"github.com/jung-kurt/gofpdf"
	"github.com/pkg/errors"
)

// ClaimPdf generates the pdf for lwt claim
func ClaimPdf(w http.ResponseWriter, req *http.Request, user db.User) {

	id := chi.URLParam(req, "id")
	ctx := req.Context()
	lwtClaim, err := getLWTClaimByID(ctx, id)
	if err != nil {
		_ = handlers.Renderer().Text(w, http.StatusInternalServerError, "Error getting LWT claim from database")
		handlers.ReportError(req, errors.Wrap(err, "error getting lwt claim from database"))
		return
	}

	var paymentDetails []claimPaymentPayload
	if lwtClaim.Status == db.LwtClaimStatusWaitingForCheck ||
		lwtClaim.Status == db.LwtClaimStatusCheckWritten ||
		lwtClaim.Status == db.LwtClaimStatusAdjusted {
		paymentDetails, err = getPaymentDetailsByClaimID(ctx, lwtClaim.ID)
		if err != nil {
			_ = handlers.Renderer().Text(w, http.StatusInternalServerError, "Error getting LWT claim payment from database")
			handlers.ReportError(req, errors.Wrap(err, "error getting lwt claim payment from database"))
			return
		}
	}

	fpdf := gofpdf.New("Portrait", "in", "Letter", "")

	claimToPdf(fpdf, &lwtClaim, paymentDetails)

	w.Header().Set("Content-Type", "application/pdf")
	w.WriteHeader(http.StatusOK)
	err = fpdf.Output(w)
	if err != nil {
		err = errors.Wrap(err, "error writing lwt claim pdf")
		_ = handlers.Renderer().Text(w, http.StatusBadRequest, "Error writing lwt claim pdf")
		handlers.ReportError(req, err)
		return
	}
}

// claimToPdf generates the pdf for lwt claim
func claimToPdf(fpdf *gofpdf.Fpdf, claim *claimPayload, paymentDetails []claimPaymentPayload) {
	const dateFormat = "2006/01/02"
	const yesFlag = "[Y]"
	const noFlag = "[N]"
	printFlag := func(flag bool) {
		flagStr := noFlag
		if flag {
			flagStr = yesFlag
		}
		fpdf.CellFormat(0.15, 0.15, flagStr, "", 0, "CM", true, 0, "")
	}

	var (
		deniedReasonList = map[int]string{
			0: "Select Reason",
			1: "Excluded Item",
			2: "Commercial Use",
			3: "Over 90 Days",
			4: "Terminating Event",
		}
	)

	fpdf.SetFillColor(211, 211, 211)
	fpdf.AddPage()
	fpdf.SetFont("Helvetica", "B", 12)
	fpdf.CellFormat(4, 0.2, "LWT Claim -"+claim.Customer.FirstName+" "+claim.Customer.LastName, "", 0, "L", false, 0, "")
	fpdf.CellFormat(4, 0.2, claim.ContractNumber+" "+claim.Status, "", 0, "C", false, 0, "")

	fpdf.Ln(0.7)

	startY := fpdf.GetY()
	// 1st column
	fpdf.SetFont("Helvetica", "B", 8)
	fpdf.Cell(1.3, 0.2, "Invoice Number")
	fpdf.SetFont("Helvetica", "", 8)
	fpdf.Cell(1, 0.2, claim.InvoiceNumber)
	fpdf.Ln(-1)

	fpdf.SetFont("Helvetica", "B", 8)
	fpdf.Cell(1.3, 0.2, "Vehicle Returned Date")
	if !claim.VehicleReturnDate.Time.IsZero() {
		fpdf.SetFont("Helvetica", "", 8)
		fpdf.Cell(1, 0.2, claim.VehicleReturnDate.Time.Format(dateFormat))
	}
	fpdf.Ln(-1)

	fpdf.SetFont("Helvetica", "B", 8)
	fpdf.Cell(1.3, 0.2, "Loan Number")
	fpdf.SetFont("Helvetica", "", 8)
	fpdf.Cell(1, 0.2, claim.LoanNumber)
	fpdf.Ln(-1)

	// 2nd column
	fpdf.SetXY(2.7, startY)
	fpdf.SetFont("Helvetica", "B", 8)
	fpdf.Cell(1, 0.2, "Lender")
	fpdf.SetFont("Helvetica", "", 8)
	fpdf.Ln(-1)

	fpdf.SetX(2.7)
	fpdf.Cell(1, 0.2, claim.VendorName.ValueOrZero())
	fpdf.Ln(-1)

	fpdf.SetX(2.7)
	fpdf.Cell(1, 0.2, claim.VendorAddress.ValueOrZero())
	fpdf.Ln(-1)

	fpdf.SetX(2.7)
	fpdf.Cell(2, 0.2, claim.VendorCity.ValueOrZero()+", "+claim.VendorState.ValueOrZero()+" "+claim.VendorPostalCode.ValueOrZero())
	fpdf.Ln(-1)

	fpdf.SetX(2.7)
	fpdf.Cell(2, 0.2, claim.VendorID)
	fpdf.Ln(-1)

	// 3rd column
	fpdf.SetXY(4.7, startY)
	fpdf.SetFont("Helvetica", "B", 8)
	fpdf.Cell(1, 0.2, claim.Customer.FirstName+" "+claim.Customer.LastName)
	fpdf.SetFont("Helvetica", "", 8)
	fpdf.Ln(-1)

	fpdf.SetX(4.7)
	fpdf.Cell(1, 0.2, claim.Customer.StreetAddress)
	fpdf.Ln(-1)

	fpdf.SetX(4.7)
	fpdf.Cell(2, 0.2, claim.Customer.City+", "+claim.Customer.State+" "+claim.Customer.PostalCode)
	fpdf.Ln(-1)

	fpdf.SetX(4.7)
	fpdf.Cell(2, 0.2, claim.Customer.PhoneNumber)
	fpdf.Ln(-1)

	fpdf.SetX(4.7)
	fpdf.SetFont("Helvetica", "B", 8)
	fpdf.Cell(1, 0.2, claim.VIN)
	fpdf.Ln(-1)

	fpdf.SetX(4.7)
	fpdf.SetFont("Helvetica", "", 8)
	fpdf.Cell(1, 0.2, claim.Year+" "+claim.Make+" "+claim.Model)
	fpdf.Ln(-1)

	// 4th Column
	fpdf.SetXY(6.7, startY)
	fpdf.Cell(1, 0.2, claim.ContractLender)
	fpdf.Ln(-1)

	fpdf.SetX(6.7)
	fpdf.Cell(1, 0.2, claim.ContractLenderAddress)
	fpdf.Ln(-1)

	fpdf.SetX(6.7)
	fpdf.Cell(2, 0.2, claim.ContractLenderCity+", "+claim.ContractLenderState+" "+claim.ContractLenderPostalCode)
	fpdf.Ln(-1)

	fpdf.SetY(3)
	printFlag(claim.InProgress)
	fpdf.SetFont("Helvetica", "", 8)
	fpdf.Cell(1, 0.2, "In Progress")
	fpdf.Ln(-1)

	printFlag(claim.FinanceContract)
	fpdf.SetFont("Helvetica", "", 8)
	fpdf.Cell(1, 0.2, "Finance Contract / Lease Agreement")
	fpdf.Ln(-1)

	printFlag(claim.CompletedClaimForm)
	fpdf.SetFont("Helvetica", "", 8)
	fpdf.Cell(1, 0.2, "Completed Claim Form")
	fpdf.Ln(-1)

	printFlag(claim.VehicleConditionReport)
	fpdf.SetFont("Helvetica", "", 8)
	fpdf.Cell(1, 0.2, "Vehicle Condition Report")
	fpdf.Ln(-1)

	printFlag(claim.VinPlateImages)
	fpdf.SetFont("Helvetica", "", 8)
	fpdf.Cell(1, 0.2, "VIN Plate / Door Sticker Images")
	fpdf.Ln(-1)

	printFlag(claim.WearAndTearDamageImages)
	fpdf.SetFont("Helvetica", "", 8)
	fpdf.Cell(1, 0.2, "Wear and Tear Damage Images")
	fpdf.Ln(-1)

	printFlag(claim.FinalInvoiceFromLessor)
	fpdf.SetFont("Helvetica", "", 8)
	fpdf.Cell(1, 0.2, "Final Invoice from Lessor")
	fpdf.Ln(0.5)

	for i := range claim.LineItems {
		fpdf.SetFont("Helvetica", "B", 8)
		fpdf.CellFormat(0.75, 0.2, "LineItem# "+strconv.Itoa(i+1), "", 0, "", true, 0, "")
		fpdf.CellFormat(5.0, 0.2, "", "", 0, "", true, 0, "")
		fpdf.CellFormat(2, 0.2, claim.LineItems[i].Status+" - "+claim.LineItems[i].CreatedAt.Format(dateFormat), "", 0, "R", true, 0, "")
		fpdf.Ln(0.3)

		printFlag(claim.LineItems[i].ImagesReceived)
		fpdf.Cell(0.75, 0.2, "Images Received")
		fpdf.Ln(-1)

		fpdf.SetFont("Helvetica", "B", 8)
		fpdf.Cell(1.5, 0.2, "Description")
		fpdf.SetFont("Helvetica", "", 8)
		fpdf.MultiCell(6, 0.2, claim.LineItems[i].Description, "", "", false)
		fpdf.Ln(-1)
		fpdf.SetFont("Helvetica", "B", 8)
		fpdf.Cell(1.5, 0.2, "Note")
		fpdf.SetFont("Helvetica", "", 8)
		fpdf.MultiCell(6, 0.2, claim.LineItems[i].Note, "", "", false)
		fpdf.Ln(-1)
		fpdf.SetFont("Helvetica", "B", 8)
		fpdf.Cell(1.5, 0.2, "Requested Amount")
		fpdf.SetFont("Helvetica", "", 8)
		fpdf.Cell(6, 0.2, "$"+claim.LineItems[i].RequestedAmount.StringFixed(2))
		fpdf.Ln(-1)
		fpdf.SetFont("Helvetica", "B", 8)
		fpdf.Cell(1.5, 0.2, "Approved Amount")
		fpdf.SetFont("Helvetica", "", 8)
		fpdf.Cell(6, 0.2, "$"+claim.LineItems[i].ApprovedAmount.StringFixed(2))
		fpdf.Ln(-1)
		fpdf.Line(0.35, fpdf.GetY(), 8.2, fpdf.GetY())

		if claim.LineItems[i].Status == db.LwtClaimStatusDenied {
			fpdf.Ln(-1)
			fpdf.Cell(1.5, 0.2, "Denied Reason")
			fpdf.SetFont("Helvetica", "", 8)
			reason := "Select Reason"
			if claim.LineItems[i].DenialReasonID.Valid {
				reason = deniedReasonList[int(claim.LineItems[i].DenialReasonID.Int64)]
			}
			fpdf.Cell(6, 0.2, reason)

			fpdf.Ln(-1)
			fpdf.Cell(1.5, 0.2, "Denial Note")
			fpdf.SetFont("Helvetica", "", 8)
			if claim.LineItems[i].DenialNote.Valid {
				fpdf.Cell(6, 0.2, claim.LineItems[i].DenialNote.String)
			}
		}
	}

	fpdf.Ln(0.3)

	startY = fpdf.GetY()
	for _, pmt := range paymentDetails {
		fpdf.Cell(1, 0.2, "BillMemo #")
		fpdf.Cell(1, 0.2, pmt.BillMemo)
		fpdf.Ln(-1)

		fpdf.Cell(1, 0.2, "Bill #")
		fpdf.Cell(1, 0.2, pmt.BillNumber)
		fpdf.Ln(-1)

		if pmt.Reason.Valid {
			fpdf.Cell(1, 0.2, "Reason")
			fpdf.Cell(1, 0.2, pmt.Reason.ValueOrZero())
		}
		fpdf.Ln(-1)

		if !pmt.CheckNumber.IsZero() {
			fpdf.Cell(1, 0.2, "Check #")
			fpdf.Cell(1, 0.2, strconv.Itoa(int(pmt.CheckNumber.ValueOrZero())))
		}
		fpdf.Ln(-1)

		if pmt.Amount.Valid {
			fpdf.Cell(1, 0.2, "Amount")
			fpdf.Cell(1, 0.2, "$"+pmt.Amount.Decimal.StringFixed(2))
		}
		fpdf.Ln(-1)

		if pmt.PaidDate.Valid {
			fpdf.Cell(1, 0.2, "Paid Date")
			fpdf.Cell(1, 0.2, pmt.PaidDate.Time.Format(dateFormat))
		}
		fpdf.Ln(-1)
		fpdf.Line(0.35, fpdf.GetY(), 4, fpdf.GetY())
	}

	fpdf.SetXY(5, startY)
	fpdf.SetFont("Helvetica", "B", 8)
	fpdf.Cell(1, 0.2, "Claim Summary")
	fpdf.Ln(-1)

	fpdf.SetFont("Helvetica", "", 8)
	fpdf.SetX(5)
	fpdf.Cell(1, 0.2, "Requested Total")
	fpdf.Cell(1, 0.2, "$"+claim.RequestedAmount.StringFixed(2))
	fpdf.Ln(-1)
	fpdf.SetX(5)
	fpdf.Cell(1, 0.2, "Approved Total")
	fpdf.Cell(1, 0.2, "$"+claim.ApprovedAmount.StringFixed(2))

}
