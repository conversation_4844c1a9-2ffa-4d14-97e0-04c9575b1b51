package lwt

import (
	"context"
	"crypto/sha512"
	"database/sql"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"strconv"
	"strings"
	"time"

	"phizz/conf"
	"phizz/db"
	"phizz/handlers"
	"phizz/intacct"
	"phizz/nr"
	"phizz/types"

	"github.com/go-chi/chi"
	"github.com/jmoiron/sqlx"
	newrelic "github.com/newrelic/go-agent"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
	null "gopkg.in/guregu/null.v3"
)

// ClaimRequestType holds request type
type ClaimRequestType int

const (
	// ClaimRequestCreate claim create
	ClaimRequestCreate ClaimRequestType = iota
	// ClaimRequestUpdate claim update
	ClaimRequestUpdate
	// ClaimRequestPending claim authorize
	ClaimRequestPending
	// ClaimRequestDeny claim deny
	ClaimRequestDeny
	// ClaimRequestReturn claim return
	ClaimRequestReturn
	// ClaimRequestSubmit claim submit
	ClaimRequestSubmit
)

const (
	// FinanceContract key to the document Finance Contract
	FinanceContract string = "finance_contract"
	// CompletedClaimForm key to the document Completed Claim Form
	CompletedClaimForm string = "completed_claim_form"
	// VehicleConditionReport key to the document Vehicle Condition Report
	VehicleConditionReport string = "vehicle_condition_report"
	// VinPlateImages key to the document Vin Plate Images
	VinPlateImages string = "vin_plate_images"
	// WearAndTearDamageImages key to the document Wear And Tear Damage Images
	WearAndTearDamageImages string = "wear_and_tear_damage_images"
	// FinalInvoiceFromLessor key to the document Final Invoice From Lessor Contract
	FinalInvoiceFromLessor string = "final_invoice_from_lessor"
	// LWTLetter key to document type LWT letter
	LWTLetter string = "lwt_letter"
	// LWTEmail key to document type LWT email
	LWTEmail string = "lwt_email"
)

type claimDocumentType string

var lwtDocumentType = struct {
	ATTACHMENT claimDocumentType
	LETTER     claimDocumentType
	EMAIL      claimDocumentType
	OTHER      claimDocumentType
}{
	ATTACHMENT: "Attachment",
	LETTER:     "Letter",
	EMAIL:      "Email",
	OTHER:      "Other",
}

type claimCount struct {
	Name   string `db:"name" json:"name"`
	Count  string `db:"count" json:"count"`
	UserID int    `db:"id" json:"id"`
}

type customerBase struct {
	ID            int         `json:"id" db:"id"`
	FirstName     string      `json:"first_name" db:"first_name"`
	LastName      string      `json:"last_name" db:"last_name"`
	IsBusiness    bool        `json:"is_business" db:"is_business"`
	BusinessName  null.String `json:"business_name" db:"business_name"`
	State         string      `json:"state" db:"state"`
	City          string      `json:"city" db:"city"`
	PostalCode    string      `json:"postal_code" db:"postal_code"`
	StreetAddress string      `json:"street_address" db:"street_address"`
	EmailAddress  string      `json:"email_address" db:"email_address"`
	PhoneNumber   string      `json:"phone_number" db:"phone_number"`
}

// ClaimBase is struct for the lwt claim data
type ClaimBase struct {
	ID                       int             `json:"id" db:"id"`
	ContractNumber           string          `json:"contract_number" db:"contract_number"`
	StoreStateCode           string          `json:"store_state_code" db:"store_state_code"`
	StoreCode                string          `json:"store_code" db:"store_code"`
	InvoiceNumber            string          `json:"invoice_number" db:"invoice_number"`
	LoanNumber               string          `json:"loan_number" db:"loan_number"`
	VehicleReturnDate        null.Time       `json:"vehicle_return_date" db:"vehicle_return_date"`
	VIN                      string          `json:"vin" db:"vin"`
	Year                     string          `json:"year" db:"year"`
	Make                     string          `json:"make" db:"make"`
	Model                    string          `json:"model" db:"model"`
	VendorID                 string          `json:"vendor_id" db:"vendor_id"`
	VendorName               null.String     `json:"vendor_name" db:"vendor_name"`
	VendorAddress            null.String     `json:"vendor_address" db:"vendor_address"`
	VendorCity               null.String     `json:"vendor_city" db:"vendor_city"`
	VendorState              null.String     `json:"vendor_state" db:"vendor_state"`
	VendorPostalCode         null.String     `json:"vendor_postal_code" db:"vendor_postal_code"`
	Status                   string          `json:"status" db:"status"`
	InProgress               bool            `json:"in_progress" db:"in_progress"`
	RequestedAmount          decimal.Decimal `json:"requested_amount" db:"requested_amount"`
	ApprovedAmount           decimal.Decimal `json:"approved_amount" db:"approved_amount"`
	OwnerID                  int             `json:"owner_id" db:"owner_id"`
	DateOfClaimReceived      time.Time       `json:"date_of_claim_received" db:"date_of_claim_received"`
	CreatedByUserID          int             `json:"created_by_user_id" db:"created_by_user_id"`
	CreatedAt                time.Time       `json:"created_at" db:"created_at"`
	UpdatedAt                time.Time       `json:"updated_at" db:"updated_at"`
	UpdatedByUserID          int             `json:"updated_by_user_id" db:"updated_by_user_id"`
	UpdatedByUserName        string          `json:"updated_by_user_name" db:"updated_by_user_name"`
	CustomerID               int             `json:"customer_id" db:"customer_id"`
	ContractLender           string          `json:"contract_lender" db:"contract_lender"`
	ContractLenderAddress    string          `json:"contract_lender_address" db:"contract_lender_address"`
	ContractLenderCity       string          `json:"contract_lender_city" db:"contract_lender_city"`
	ContractLenderState      string          `json:"contract_lender_state" db:"contract_lender_state"`
	ContractLenderPostalCode string          `json:"contract_lender_postal_code" db:"contract_lender_postal_code"`
	FinanceContract          bool            `json:"finance_contract" db:"finance_contract"`
	CompletedClaimForm       bool            `json:"completed_claim_form" db:"completed_claim_form"`
	VehicleConditionReport   bool            `json:"vehicle_condition_report" db:"vehicle_condition_report"`
	VinPlateImages           bool            `json:"vin_plate_images" db:"vin_plate_images"`
	WearAndTearDamageImages  bool            `json:"wear_and_tear_damage_images" db:"wear_and_tear_damage_images"`
	FinalInvoiceFromLessor   bool            `json:"final_invoice_from_lessor" db:"final_invoice_from_lessor"`
	AdjustmentReason         string          `json:"-" db:"-"`
	AdjustmentExists         bool            `json:"adjustment_exists" db:"-"`
	Customer                 customerBase    `json:"customer" db:"-"`
	LineItems                []lineItemsBase `json:"line_items" db:"-"`
	Documents                []documentBase  `json:"documents" db:"-"`
}

type claimPayload struct {
	ClaimBase
}

func (c claimPayload) GetBill(billNumber string, billMemo string, batchKey int, description string) intacct.Bill {
	return intacct.Bill{
		// Intacct Details
		AccountLabel: conf.Get().IntacctLWT.AccountLabel,
		LocationID:   conf.Get().IntacctLWT.LocationID,
		ProjectID:    conf.Get().IntacctLWT.ProjectID,
		TermName:     conf.Get().IntacctLWT.TermName,

		//Amount to be paid details
		TotalPaid: decimal.NewFromFloat(0),
		Amount:    c.ApprovedAmount,
		TotalDue:  c.ApprovedAmount,

		//Other details related to payment
		Memo:        billMemo,
		Description: description,
		VendorID:    c.VendorID,
		BatchKey:    batchKey,
		BillNo:      billNumber,
		LoanNumber:  c.LoanNumber,
	}
}

func lwtClaimFromReq(lwtClaim *claimPayload, req *http.Request) error {
	dec := json.NewDecoder(req.Body)
	err := dec.Decode(&lwtClaim)
	return errors.Wrap(err, "decoding LWT Claim request failed")
}

func validateLWTClaim(ctx context.Context, lwtClaim claimPayload, requestType ClaimRequestType, claimStatusBeforeUpdate string) (map[string]string, error) {
	formErrors := map[string]string{}
	var err error

	if claimStatusBeforeUpdate != db.LwtClaimStatusInquiry && lwtClaim.Status == db.LwtClaimStatusInquiry {
		formErrors["status"] = "Claim status can not be Inquiry"
	}

	switch requestType {
	case ClaimRequestCreate:
		// No extra validation required for now
		// We can add it if required in future
		if !lwtClaim.InProgress {
			formErrors["in_progress"] = "In progress must be checked"
		}
	case ClaimRequestUpdate, ClaimRequestDeny, ClaimRequestReturn:
		if lwtClaim.InvoiceNumber == "" {
			formErrors["invoice_number"] = "Claim invoice number can not be empty"
		}

		if !lwtClaim.VehicleReturnDate.Valid {
			formErrors["vehicle_return_date"] = "Claim vehicle return date is not valid"
		}

		if lwtClaim.VehicleReturnDate.Valid && lwtClaim.VehicleReturnDate.Time.After(time.Now()) {
			formErrors["vehicle_return_date"] = "Claim vehicle return date is not valid"
		}

		if lwtClaim.OwnerID == 0 {
			formErrors["owner_id"] = "OwnerID is required"
		}

		if lwtClaim.VendorID == "" {
			formErrors["vendor_id"] = "Vendor is required"
		}
		return formErrors, nil
	case ClaimRequestPending, ClaimRequestSubmit:
		if lwtClaim.InvoiceNumber == "" {
			formErrors["invoice_number"] = "Claim invoice number can not be empty"
		}

		if lwtClaim.LoanNumber == "" {
			formErrors["loan_number"] = "Claim loan number can not be empty"
		}

		if !lwtClaim.VehicleReturnDate.Valid {
			formErrors["vehicle_return_date"] = "Claim vehicle return date is not valid"
		}

		if lwtClaim.OwnerID == 0 {
			formErrors["owner_id"] = "OwnerID is required"
		}

		if lwtClaim.VendorID == "" {
			formErrors["vendor_id"] = "Vendor is required"
		}

		// validate vendor id
		if lwtClaim.VendorID != "" {
			// if vendorId is present, validate vendor_id
			validVendor, err := isValidVendor(ctx, lwtClaim.VendorID)
			if err != nil {
				_ = errors.Wrap(err, "failed to validate vendor id from intacct")
			}
			if !validVendor {
				formErrors["vendor_id"] = "Invalid Vendor ID"
			}
		}

		// validate documents
		formErrors, err = validateDocuments(ctx, lwtClaim, formErrors)
		if err != nil || len(formErrors) > 0 {
			return formErrors, err
		}

		//validate line items
		formErrors, err = validateLineItems(lwtClaim, formErrors)
		if err != nil || len(formErrors) > 0 {
			return formErrors, err
		}
	}
	return formErrors, err
}

func validateLineItems(claim claimPayload, formErrors map[string]string) (map[string]string, error) {
	if len(claim.LineItems) == 0 {
		formErrors["line_items"] = "At least one line item is required"
		return formErrors, nil
	}

	for i, v := range claim.LineItems {
		if !v.ImagesReceived {
			formErrors["is_images_received_"+fmt.Sprint(i)] = "Images received should be checked"
		}
		if v.ApprovedAmount.GreaterThan(v.RequestedAmount) {
			formErrors["approved_amount_"+fmt.Sprint(i)] = "Approved amount is greater than requested amount"
		}
	}
	return formErrors, nil
}

func validateDocuments(ctx context.Context, claim claimPayload, formErrors map[string]string) (map[string]string, error) {
	var documentTypes []documentType
	err := db.Get().Unsafe().SelectContext(ctx, &documentTypes, `select * from lwt_claim_document_types`)
	if err != nil {
		return formErrors, errors.Wrap(err, "error getting document types")
	}

	dt := sliceToMap(documentTypes)

	if claim.FinanceContract {
		var isFound bool
		for _, v := range claim.Documents {
			if v.DocumentTypeID == dt[FinanceContract] {
				isFound = true
				break
			}
		}

		if !isFound {
			formErrors["finance_contract"] = "Finance Contract is required"
		}
	}

	if claim.CompletedClaimForm {
		var isFound bool
		for _, v := range claim.Documents {
			if v.DocumentTypeID == dt[CompletedClaimForm] {
				isFound = true
				break
			}
		}

		if !isFound {
			formErrors["completed_claim_form"] = "Completed claim form is required"
		}
	}

	if claim.VehicleConditionReport {
		var isFound bool
		for _, v := range claim.Documents {
			if v.DocumentTypeID == dt[VehicleConditionReport] {
				isFound = true
				break
			}
		}

		if !isFound {
			formErrors["vehicle_condition_report"] = "Vehicle Condition Report is required"
		}
	}

	if claim.VinPlateImages {
		var isFound bool
		for _, v := range claim.Documents {
			if v.DocumentTypeID == dt[VinPlateImages] {
				isFound = true
				break
			}
		}

		if !isFound {
			formErrors["vin_plate_images"] = "VIN plate images are required"
		}
	}

	if claim.WearAndTearDamageImages {
		var isFound bool
		for _, v := range claim.Documents {
			if v.DocumentTypeID == dt[WearAndTearDamageImages] {
				isFound = true
				break
			}
		}

		if !isFound {
			formErrors["wear_and_tear_damage_images"] = "Wear and tear damage images are required"
		}
	}

	if claim.FinalInvoiceFromLessor {
		var isFound bool
		for _, v := range claim.Documents {
			if v.DocumentTypeID == dt[FinalInvoiceFromLessor] {
				isFound = true
				break
			}
		}

		if !isFound {
			formErrors["final_invoice_from_lessor"] = "Final invoice from lessor is required"
		}
	}

	return formErrors, nil
}

func sliceToMap(dt []documentType) map[string]int {
	dTypes := make(map[string]int, len(dt))
	for _, v := range dt {
		dTypes[v.Key] = v.ID
	}
	return dTypes
}

// isValidVendor will make intacct api call and return true if vendor is exists
func isValidVendor(ctx context.Context, vendorID string) (bool, error) {
	vendorQuery := intacct.VendorQueryPayload{}.VendorID(vendorID)
	vendors, err := intacct.GetVendors(ctx, vendorQuery)
	return len(vendors) == 1, err
}

// ClaimCreate creates new lwt claims with given lwt contract details
func ClaimCreate(_ http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	ctx := req.Context()
	var lwtClaimCreatePayload struct {
		ID int `json:"id"`
	}
	dec := json.NewDecoder(req.Body)
	err := dec.Decode(&lwtClaimCreatePayload)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(nil, "Malformed LWT Claim data for create.", nil)
	}

	txn := newrelic.FromContext(ctx)
	lwtClaim, err := getLwtClaimFromContractDetails(txn, lwtClaimCreatePayload.ID)
	if err != nil {
		handlers.ReportError(req, err)
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Error in getting contract details", nil)
	}
	cleanLwtClaimData(&lwtClaim)

	lwtClaim.InProgress = true
	formErrors, err := validateLWTClaim(ctx, lwtClaim, ClaimRequestCreate, db.LwtClaimStatusInquiry)
	if err != nil {
		handlers.ReportError(req, err)
		return http.StatusBadRequest, handlers.ErrorMessage(err, err.Error(), nil)
	}
	if len(formErrors) > 0 {
		return http.StatusBadRequest, handlers.ErrorMessage(errors.New("Invalid input"), "Form validations errors.",
			map[string]interface{}{"errors": formErrors})
	}

	// Default values for fields
	lwtClaim.OwnerID = user.ID
	lwtClaim.CreatedByUserID = user.ID
	lwtClaim.Status = db.LwtClaimStatusInquiry

	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "error in beginning transaction for lwt claim",
			nil)
	}

	// insert new claim
	id, err := insertLWTClaim(ctx, tx, lwtClaim)
	if err != nil {
		_ = tx.Rollback()
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, err.Error(), nil)
	}

	// add record notes for new claim
	_, err = insertRecordNote(ctx, tx, &recordNotePayload{
		LWTClaimID:      id,
		CreatedByUserID: user.ID,
		CreatedAt:       time.Now(),
		NotesText:       "New Claim Started",
	})
	if err != nil {
		_ = tx.Rollback()
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error adding notes for lwt claim", nil)
	}

	// add record notes for status
	_, err = insertRecordNote(ctx, tx, &recordNotePayload{
		LWTClaimID:      id,
		CreatedByUserID: user.ID,
		CreatedAt:       time.Now(),
		NotesText:       db.LWTRecordNoteDescription[db.LwtClaimStatusInquiry],
	})
	if err != nil {
		_ = tx.Rollback()
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error adding notes for lwt claim", nil)
	}

	// Add entry in update table for audit trail
	err = claimUpdated(ctx, tx, id, lwtClaim.CreatedByUserID)
	if err != nil {
		_ = tx.Rollback()
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "claim updated entry for audit trail failed", nil)
	}

	err = tx.Commit()
	if err != nil {
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error creating LWT claim", nil)
	}
	return http.StatusOK, map[string]interface{}{"id": id}
}

// ClaimShow returns details of lwt claims for given claim id
func ClaimShow(_ http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	ctx := req.Context()
	// get lwt claim id
	id := chi.URLParam(req, "id")

	// get lwt claim from database
	lwtClaimFromDB, err := getLWTClaimByID(ctx, id)
	if err != nil {
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting LWT claim from database", nil)
	}

	return http.StatusOK, map[string]interface{}{"lwt_claim": lwtClaimFromDB}
}

// ClaimUpdate updates the existing claim for given claim id
func ClaimUpdate(_ http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	ctx := req.Context()
	id := chi.URLParam(req, "id")

	// Get the lwt claim from database
	lwtClaim, err := getLWTClaimByID(ctx, id)
	if err != nil {
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting LWT claim from database", nil)
	}
	claimStatusBeforeUpdate := lwtClaim.Status
	ownerBeforeUpdate := lwtClaim.OwnerID

	err = lwtClaimFromReq(&lwtClaim, req)
	if err != nil {
		handlers.ReportError(req, err)
		return http.StatusBadRequest, handlers.ErrorMessage(nil, "Malformed LWT Claim data for create.", nil)
	}

	lwtClaim.ID, _ = strconv.Atoi(id)
	currentUser := user.ID

	cleanLwtClaimData(&lwtClaim)

	// validate claim in process
	if lwtClaim.Status == db.LwtClaimStatusClaimInProcess || lwtClaim.Status == db.LwtClaimStatusInquiry {
		formErrors, err := validateLWTClaim(ctx, lwtClaim, ClaimRequestCreate, claimStatusBeforeUpdate)
		if len(formErrors) > 0 {
			return http.StatusBadRequest, handlers.ErrorMessage(err, "Form validations errors.",
				map[string]interface{}{"errors": formErrors})
		}
	}

	// validate claim for pending status
	if lwtClaim.Status == db.LwtClaimStatusPendingApproval {
		formErrors, err := validateLWTClaim(ctx, lwtClaim, ClaimRequestPending, claimStatusBeforeUpdate)
		if len(formErrors) > 0 {
			return http.StatusBadRequest, handlers.ErrorMessage(err, "Form validations errors.",
				map[string]interface{}{"errors": formErrors})
		}
	}

	// validate claim for approved status
	if lwtClaim.Status == db.LwtClaimStatusApproved {
		formErrors, err := validateLWTClaim(ctx, lwtClaim, ClaimRequestSubmit, claimStatusBeforeUpdate)
		if len(formErrors) > 0 {
			return http.StatusBadRequest, handlers.ErrorMessage(err, "Form validations errors.",
				map[string]interface{}{"errors": formErrors})
		}
	}

	// validate claim for closed status or pending for closed
	if lwtClaim.Status == db.LwtClaimStatusClosedNoResponse ||
		lwtClaim.Status == db.LwtClaimStatusDenied || lwtClaim.Status == db.LwtClaimStatusDeactivated ||
		lwtClaim.Status == db.LwtClaimStatusPendingDenial {
		formErrors, err := validateLWTClaim(ctx, lwtClaim, ClaimRequestDeny, claimStatusBeforeUpdate)
		if len(formErrors) > 0 {
			return http.StatusBadRequest, handlers.ErrorMessage(err, "Form validations errors.",
				map[string]interface{}{"errors": formErrors})
		}
	}

	if lwtClaim.Status == db.LwtClaimStatusReturnedClaim {
		formErrors, err := validateLWTClaim(ctx, lwtClaim, ClaimRequestReturn, claimStatusBeforeUpdate)
		if len(formErrors) > 0 {
			return http.StatusBadRequest, handlers.ErrorMessage(err, "Form validations errors.",
				map[string]interface{}{"errors": formErrors})
		}
	}

	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		err = errors.Wrap(err, "database error beginning transaction for LWT claim update")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error creating LWT claim update", nil)
	}

	err = lwtClaimUpdate(ctx, tx, lwtClaim)
	if err != nil {
		_ = tx.Rollback()
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error updating LWT Claim", nil)
	}

	//Update lwt_closed_at if the status is changed to closed
	if lwtClaim.Status == db.LWTClaimComplaintStatusDenied || lwtClaim.Status == db.LwtClaimStatusClosedNoResponse || lwtClaim.Status == db.LwtClaimStatusDeactivated ||
		lwtClaim.Status == db.GapClaimStatusCheckWritten || lwtClaim.Status == db.GapClaimStatusCheckVoided {
		_, err = tx.ExecContext(ctx, `update lwt_claims set lwt_closed_at=now() at time zone 'utc' where id =$1`, lwtClaim.ID)
		if err != nil {
			_ = tx.Rollback()
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Update of LWT claim closed failed", nil)
		}
	}

	if claimStatusBeforeUpdate != lwtClaim.Status {
		recordNote := recordNotePayload{
			LWTClaimID:      lwtClaim.ID,
			CreatedByUserID: currentUser,
			CreatedAt:       time.Now(),
			NotesText:       db.LWTRecordNoteDescription[lwtClaim.Status],
		}
		_, err := insertRecordNote(ctx, tx, &recordNote)
		if err != nil {
			_ = tx.Rollback()
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error adding notes for lwt claim", nil)
		}
	}

	if ownerBeforeUpdate != lwtClaim.OwnerID {
		type userData struct {
			FirstName string `db:"first_name"`
			LastName  string `db:"last_name"`
		}

		var userPayload userData
		userQuery := "select first_name, last_name from users where id = $1"
		err = db.Get().GetContext(ctx, &userPayload, userQuery, ownerBeforeUpdate)
		if err != nil {
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error beginning transaction for LWT claim reassignment", nil)
		}

		var currentUserPayload userData
		err = db.Get().GetContext(ctx, &currentUserPayload, userQuery, lwtClaim.OwnerID)
		if err != nil {
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error beginning transaction for LWT claim reassignment", nil)
		}

		noteText := "Claim assigned to " + currentUserPayload.FirstName + " " + currentUserPayload.LastName + " from " + userPayload.FirstName + " " + userPayload.LastName
		recordNote := recordNotePayload{
			LWTClaimID:      lwtClaim.ID,
			CreatedByUserID: currentUser,
			CreatedAt:       time.Now(),
			NotesText:       noteText,
		}
		_, err := insertRecordNote(ctx, tx, &recordNote)
		if err != nil {
			_ = tx.Rollback()
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error adding notes for lwt claim", nil)
		}
	}

	// Call intacct for if status is Approved
	if lwtClaim.Status == db.LwtClaimStatusApproved {
		_, err = claimAuthorize(ctx, tx, &lwtClaim, user.ID)
		if err != nil {
			_ = tx.Rollback()
			handlers.ReportError(req, err)
			if isCustomError(err) {
				return http.StatusInternalServerError, handlers.ErrorMessage(err, err.Error(), nil)
			}
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error creating LWT claim authorization", nil)
		}

		recordNote := recordNotePayload{
			LWTClaimID:      lwtClaim.ID,
			CreatedByUserID: currentUser,
			CreatedAt:       time.Now(),
			NotesText:       db.LWTRecordNoteDescription[lwtClaim.Status],
		}
		_, err := insertRecordNote(ctx, tx, &recordNote)
		if err != nil {
			_ = tx.Rollback()
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error adding notes for lwt claim", nil)
		}
	}

	// Add entry in update table for audit trail
	err = claimUpdated(ctx, tx, lwtClaim.ID, currentUser)
	if err != nil {
		_ = tx.Rollback()
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error inserting lwt claim update", nil)
	}

	err = tx.Commit()
	if err != nil {
		err = errors.Wrap(err, "Database error committing transaction for GAP claim update")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error creating GAP claim authorization", nil)
	}

	return http.StatusOK, map[string]interface{}{"lwt_claim_id": id}
}

// ClaimReopen reopen claim and change the status to inquiry
func ClaimReopen(_ http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	ctx := req.Context()

	claimID, err := strconv.Atoi(chi.URLParam(req, "lwt_claim_id"))
	if err != nil || claimID == 0 {
		return http.StatusBadRequest, handlers.ErrorMessage(nil, "Claim Id is invalid", nil)
	}
	status := ""
	claimQuery := "select status from lwt_claims where id = $1"
	err = db.Get().GetContext(ctx, &status, claimQuery, claimID)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error while getting claim status"))
		return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Database update failed", nil)
	}

	if status != db.LWTClaimComplaintStatusDenied &&
		status != db.LwtClaimStatusDeactivated &&
		status != db.LwtClaimStatusClosedNoResponse &&
		status != db.LwtClaimStatusWaitingForCheck &&
		status != db.LwtClaimStatusApproved {
		return http.StatusBadRequest, handlers.ErrorMessage(nil, "Invalid Claim Status", nil)
	}

	// if no attachment then status will be `LwtClaimStatusInquiry` else it will be `LwtClaimStatusPending`
	documentCount := 0
	countQuery := "select count(*) from lwt_claim_documents where lwt_claim_id = $1"
	err = db.Get().GetContext(ctx, &documentCount, countQuery, claimID)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error while fetching documents counts"))
		return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Database update failed", nil)
	}

	lineItemCount := 0
	countQuery = `select count(*) from lwt_claim_line_items where lwt_claim_id = $1`
	err = db.Get().GetContext(ctx, &lineItemCount, countQuery, claimID)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error while fetching line item counts"))
		return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Database update failed", nil)
	}

	var requiredDocumentCount int
	err = db.Get().GetContext(ctx, &requiredDocumentCount, `select count(*) from lwt_claim_document_types where doc_type = $1`, lwtDocumentType.ATTACHMENT)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error while fetching documents counts"))
		return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Database update failed", nil)
	}
	newStatus := db.LwtClaimStatusClaimInProcess
	if status != db.LwtClaimStatusClosedNoResponse && documentCount >= requiredDocumentCount && lineItemCount > 0 {
		newStatus = db.LwtClaimStatusPendingApproval
	}

	lastOwnerID, err := getLastSubmittedByUser(ctx, claimID)
	if err != nil {
		handlers.ReportError(req, err)
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Error getting last owner", nil)
	}

	// If we dont have any previous owner we will assign it to the current user
	if lastOwnerID == 0 {
		lastOwnerID = user.ID
	}

	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "database error beginning transaction for Auto claim reassignment"))
		return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Database update failed", nil)
	}

	// Update claim payment and set current payment as in valid if status is approved or waiting for check
	// We want to just mark invalid payment which are still yet to be paid
	// we dont want to mark already paid payment as invalid
	if status == db.LwtClaimStatusApproved || status == db.LwtClaimStatusWaitingForCheck {
		paymentQuery := `update lwt_claim_payments 
							set valid = false,
							    updated_at = now() at time zone 'utc' 
						where lwt_claim_id = $1
							and paid_date is null`
		_, err = tx.ExecContext(ctx, paymentQuery, claimID)
		if err != nil {
			_ = tx.Rollback()
			handlers.ReportError(req, errors.Wrap(err, "database error - failed to update claim status"))
			return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Database update failed", nil)
		}
	}

	updateQuery := "update lwt_claims set owner_id = $1, status = $2 where id = $3"
	_, err = tx.ExecContext(ctx, updateQuery, lastOwnerID, newStatus, claimID)
	if err != nil {
		_ = tx.Rollback()
		handlers.ReportError(req, errors.Wrap(err, "database error - failed to update claim status"))
		return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Database update failed", nil)
	}

	// Claim updated history
	err = claimUpdated(ctx, tx, claimID, user.ID)
	if err != nil {
		_ = tx.Rollback()
		handlers.ReportError(req, errors.Wrap(err, "database error - inserting lwt_claim_updates"))
		return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Database update failed", nil)
	}

	// add record notes for new claim
	_, err = insertRecordNote(ctx, tx, &recordNotePayload{
		LWTClaimID:      claimID,
		CreatedByUserID: user.ID,
		CreatedAt:       time.Now(),
		NotesText:       db.LWTRecordNoteDescription[newStatus],
	})

	if err != nil {
		_ = tx.Rollback()
		handlers.ReportError(req, errors.Wrap(err, "database error - inserting lwt_record_notes"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database update failed", nil)
	}

	err = tx.Commit()
	if err != nil {
		err = errors.Wrap(err, "database error committing transaction for lwt claim reassign")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database update failed", nil)
	}

	return http.StatusOK, map[string]interface{}{}
}

func getLastSubmittedByUser(ctx context.Context, claimID int) (int, error) {
	userID := 0
	query := `select created_by_user_id from lwt_record_notes 
			where lwt_claim_id = $1 
				and (notes_text = $2 or notes_text = $3 or notes_text = $4 
				         or notes_text = $5 or notes_text = $6 or notes_text = $7 
				         or notes_text = $8 or notes_text = $9) 
			order by created_at desc limit 1`
	err := db.Get().GetContext(ctx, &userID,
		query,
		claimID,
		db.LWTRecordNoteDescription[db.LwtClaimStatusPendingApproval],
		db.LWTRecordNoteDescription[db.LwtClaimStatusPendingDenial],
		db.LWTRecordNoteDescription[db.LwtClaimStatusClosedNoResponse],
		db.LWTRecordNoteDescription[db.LwtClaimStatusApproved],
		db.LWTRecordNoteDescription[db.LwtClaimStatusDeactivated],
		db.LWTRecordNoteDescription[db.LwtClaimStatusDenied],
		db.LWTRecordNoteDescription[db.LwtClaimStatusClaimInProcess],
		db.LWTRecordNoteDescription[db.LwtClaimStatusInquiry],
	)
	if err != nil && err != sql.ErrNoRows {
		err = errors.Wrap(err, "database error getting record notes for claim")
		return userID, err
	}

	return userID, nil
}

func cleanLwtClaimData(lwtClaim *claimPayload) {
	lwtClaim.VIN = strings.TrimSpace(lwtClaim.VIN)
	lwtClaim.Status = strings.TrimSpace(lwtClaim.Status)
	lwtClaim.Customer.City = strings.TrimSpace(lwtClaim.Customer.City)
	lwtClaim.ContractNumber = strings.TrimSpace(lwtClaim.ContractNumber)
	lwtClaim.Customer.EmailAddress = strings.TrimSpace(lwtClaim.Customer.EmailAddress)
	lwtClaim.Customer.FirstName = strings.TrimSpace(lwtClaim.Customer.FirstName)
	lwtClaim.Customer.LastName = strings.TrimSpace(lwtClaim.Customer.LastName)
	lwtClaim.Customer.PhoneNumber = strings.TrimSpace(lwtClaim.Customer.PhoneNumber)
	lwtClaim.Customer.PostalCode = strings.TrimSpace(lwtClaim.Customer.PostalCode)
	lwtClaim.Customer.State = strings.TrimSpace(lwtClaim.Customer.State)
	lwtClaim.Customer.StreetAddress = strings.TrimSpace(lwtClaim.Customer.StreetAddress)
}

func getLwtClaimFromContractDetails(txn newrelic.Transaction, contractID int) (claimPayload, error) {
	var lwtClaimPayload claimPayload
	url := conf.Get().Whiz.BaseURL + "/ext/contracts/" + strconv.Itoa(contractID)

	if conf.Get().Whiz.Log {
		log.Println("[Whiz-contract-detail] request URL:", url)
	}

	whizReq, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return lwtClaimPayload, errors.Wrap(err, "could not create Whiz-contract-detail request")
	}

	whizReq.Header.Set("Content-Type", "application/json")
	whizReq.Header.Set("Accept", "application/json")
	salt := []byte(conf.Get().Whiz.AuthSalt)
	checksum := sha512.Sum512(salt)
	whizReq.Header.Set("Whiz-Checksum", hex.EncodeToString(checksum[:]))

	client := http.Client{Timeout: time.Second * 30}
	resp, err := nr.External(txn, client, whizReq)
	if err != nil {
		return lwtClaimPayload, errors.Wrap(err, "Invalid response for Whiz-contract-detail")
	}
	defer func() { _ = resp.Body.Close() }()

	bodyBytes := make([]byte, 102400) // 100KB buffer for response
	n, err := io.ReadFull(resp.Body, bodyBytes)
	if err != nil && err != io.EOF && err != io.ErrUnexpectedEOF {
		return lwtClaimPayload, errors.Wrap(err, "invalid response for Whiz-contract-detail")
	}
	bodyBytes = bodyBytes[:n]
	if conf.Get().Whiz.Log {
		log.Println("[Whiz-contract-detail] response status:", resp.Status, "body:", string(bodyBytes))
	}

	if resp.StatusCode != http.StatusOK {
		return lwtClaimPayload, errors.Wrap(err, "invalid response for Whiz-contract-detail")
	}

	type data struct {
		Contract struct {
			Code            string `json:"code"`
			ProductTypeCode string `json:"product_type_code"`
			StoreStateCode  string `json:"store_state_code"`
			StoreCode       string `json:"store_code"`
		} `json:"contract"`
		CustomerDetails struct {
			ID           int         `json:"id"`
			FirstName    string      `json:"first_name"`
			LastName     string      `json:"last_name"`
			IsBusiness   bool        `json:"is_business"`
			BusinessName null.String `json:"business_name"`
			Address      string      `json:"address"`
			City         string      `json:"city"`
			StateCode    string      `json:"state_code"`
			PostalCode   string      `json:"postal_code"`
			Phone        string      `json:"phone"`
			Email        string      `json:"email"`
		} `json:"customer_details"`
		VehicleDetails struct {
			VIN   string `json:"vin"`
			Year  string `json:"year"`
			Make  string `json:"make"`
			Model string `json:"model"`
		} `json:"vehicle_details"`
		FinancingDetails struct {
			FinanceAmount types.JSNullDecimal `json:"finance_amount"`
			LenderName    string              `json:"lender_name" db:"lender_name"`
			LenderAddress string              `json:"lender_address" db:"lender_address"`
			LenderCity    string              `json:"lender_city" db:"lender_city"`
			LenderState   string              `json:"lender_state" db:"lender_state"`
			LenderZip     string              `json:"lender_zip" db:"lender_zip"`
		} `json:"financing_details"`
	}

	contractData := struct {
		Data data `json:"data"`
	}{}

	err = json.Unmarshal(bodyBytes, &contractData)
	if err != nil {
		return lwtClaimPayload, errors.Wrap(err, "invalid response for Whiz-contracts")
	}

	lwtClaimBase := ClaimBase{
		ContractNumber:           contractData.Data.Contract.Code,
		StoreCode:                contractData.Data.Contract.StoreCode,
		StoreStateCode:           contractData.Data.Contract.StoreStateCode,
		VIN:                      contractData.Data.VehicleDetails.VIN,
		Year:                     contractData.Data.VehicleDetails.Year,
		Make:                     contractData.Data.VehicleDetails.Make,
		Model:                    contractData.Data.VehicleDetails.Model,
		ContractLender:           contractData.Data.FinancingDetails.LenderName,
		ContractLenderAddress:    contractData.Data.FinancingDetails.LenderAddress,
		ContractLenderCity:       contractData.Data.FinancingDetails.LenderCity,
		ContractLenderState:      contractData.Data.FinancingDetails.LenderState,
		ContractLenderPostalCode: contractData.Data.FinancingDetails.LenderZip,
		Customer: customerBase{
			FirstName:     contractData.Data.CustomerDetails.FirstName,
			LastName:      contractData.Data.CustomerDetails.LastName,
			IsBusiness:    contractData.Data.CustomerDetails.IsBusiness,
			BusinessName:  contractData.Data.CustomerDetails.BusinessName,
			State:         contractData.Data.CustomerDetails.StateCode,
			City:          contractData.Data.CustomerDetails.City,
			PostalCode:    contractData.Data.CustomerDetails.PostalCode,
			StreetAddress: contractData.Data.CustomerDetails.Address,
			EmailAddress:  contractData.Data.CustomerDetails.Email,
			PhoneNumber:   contractData.Data.CustomerDetails.Phone,
		},
	}

	lwtClaimPayload = claimPayload{ClaimBase: lwtClaimBase}
	return lwtClaimPayload, nil
}

func insertLWTClaim(ctx context.Context, tx *sqlx.Tx, lwtClaim claimPayload) (int, error) {
	insertQuery := `insert into lwt_claims (
		contract_number,
        store_code,
        store_state_code,
		invoice_number,
        loan_number,
		vin,
		year,
		make,
		model,
		vendor_id,
		vendor_address,
		vendor_city,
		vendor_state,
		vendor_postal_code,
		contract_lender,
		contract_lender_address,
		contract_lender_city,
		contract_lender_state,
		contract_lender_postal_code,
		status,
		in_progress,
		requested_amount,
		approved_amount,
		owner_id,
		customer_id,
		date_of_claim_received,
		created_by_user_id,
		created_at
	)

	values (
		:contract_number,
	    :store_code,
        :store_state_code,
		:invoice_number,
		:loan_number,
		:vin,
		:year,
		:make,
		:model,
		:vendor_id,
		:vendor_address,
		:vendor_city,
		:vendor_state,
		:vendor_postal_code,
		:contract_lender,
		:contract_lender_address,
		:contract_lender_city,
		:contract_lender_state,
		:contract_lender_postal_code,
		:status,
		:in_progress,
		:requested_amount,
		:approved_amount,
		:owner_id,
		:customer_id,
		now() at time zone 'utc',
		:created_by_user_id,
		now() at time zone 'utc'
	) returning id`

	customerInsert := `insert into customers (
		first_name,
		last_name,
		is_business,
		business_name,
		email_address,
		phone_number,
		street_address,
		city,
		state,
		postal_code)

		values (
		:first_name,
		:last_name,
		:is_business,
		:business_name,
		:email_address,
		:phone_number,
		:street_address,
		:city,
		:state,
		:postal_code) returning id`

	// Prepare and execute customer info
	customerInsertStmt, err := tx.PrepareNamedContext(ctx, customerInsert)
	if err != nil {
		return 0, errors.Wrap(err, "prepareNamed failed for customer")
	}
	defer func() { _ = customerInsertStmt.Close() }()
	err = customerInsertStmt.GetContext(ctx, &lwtClaim.CustomerID, lwtClaim.Customer)
	if err != nil {
		return 0, errors.Wrap(err, "error creating customer")
	}

	// Prepare and execute
	stmt, err := tx.PrepareNamedContext(ctx, insertQuery)
	if err != nil {
		return 0, errors.Wrap(err, "prepareNamed failed")
	}
	defer func() { _ = stmt.Close() }()
	id := 0
	err = stmt.GetContext(ctx, &id, lwtClaim)
	if err != nil {
		return id, errors.Wrap(err, "scan error on ID after creating LWT claim")
	}
	return id, err
}

// claimUpdated : Add entry in update table for audit trail
func claimUpdated(ctx context.Context, tx *sqlx.Tx, claimID int, updatedByUserID int) error {
	updateInsert := `insert into lwt_claim_updates(lwt_claim_id, updated_by_user_id, updated_at) values ($1, $2, now() AT TIME ZONE 'utc')`
	_, err := tx.ExecContext(ctx, updateInsert, claimID, updatedByUserID)
	return err
}

// get lwt claim from database by lwt claim id
func getLWTClaimByID(ctx context.Context, id string) (claimPayload, error) {
	var lwtClaim claimPayload
	query := `select * from lwt_claims where id = $1 limit 1`

	err := db.Get().Unsafe().GetContext(ctx, &lwtClaim, query, id)
	if err != nil {
		if err == sql.ErrNoRows {
			return lwtClaim, errors.Wrap(err, "the LWT claim was not found")
		}
		return lwtClaim, errors.Wrap(err, "error loading LWT claim from database.")
	}

	customerQuery := `select * from customers where id = $1 limit 1`
	err = db.Get().Unsafe().GetContext(ctx, &lwtClaim.Customer, customerQuery, lwtClaim.CustomerID)
	if err != nil {
		if err == sql.ErrNoRows {
			return lwtClaim, errors.Wrap(err, "The customer was not found")
		}
		return lwtClaim, errors.Wrap(err, "error loading LWT claim from database.")
	}

	complaintQuery := `select * from lwt_claim_line_items where lwt_claim_id = $1`
	err = db.Get().Unsafe().SelectContext(ctx, &lwtClaim.LineItems, complaintQuery, id)
	if err != nil && err != sql.ErrNoRows {
		return lwtClaim, errors.Wrap(err, "error loading LWT claim from database.")
	}

	documentQuery := `select * from lwt_claim_documents where lwt_claim_id = $1 and deleted_at is null`
	err = db.Get().Unsafe().SelectContext(ctx, &lwtClaim.Documents, documentQuery, id)
	if err != nil && err != sql.ErrNoRows {
		return lwtClaim, errors.Wrap(err, "error loading LWT claim from database.")
	}

	lwtClaim.ID, err = strconv.Atoi(id)
	if err != nil {
		return lwtClaim, errors.Wrap(err, "converting LWT claim id to number failed")
	}

	var adjustments []struct {
		ID                int             `db:"id"`
		LWTClaimID        int             `db:"lwt_claim_id"`
		LWTClaimPaymentID int             `db:"lwt_claim_payment_id"`
		Amount            decimal.Decimal `db:"amount"`
	}
	adjustmentQuery := `select * from lwt_claim_adjustments where lwt_claim_id = $1`
	err = db.Get().Unsafe().SelectContext(ctx, &adjustments, adjustmentQuery, lwtClaim.ID)
	if err != nil && err != sql.ErrNoRows {
		return lwtClaim, errors.Wrap(err, "error loading adjustments from database")
	}

	// Adding adjustment amount to the approved and requested amount
	var totalAdjustedAmount decimal.Decimal
	for _, v := range adjustments {
		totalAdjustedAmount = totalAdjustedAmount.Add(v.Amount)
	}
	lwtClaim.ApprovedAmount = lwtClaim.ApprovedAmount.Add(totalAdjustedAmount)
	lwtClaim.RequestedAmount = lwtClaim.RequestedAmount.Add(totalAdjustedAmount)
	lwtClaim.AdjustmentExists = len(adjustments) > 0

	lastUpdateQuery := `select lwt_claim_updates.updated_at, updated_by_user_id, first_name, last_name
	 from lwt_claim_updates join users on updated_by_user_id = users.id
	 where lwt_claim_id = $1 order by updated_at desc limit 1`
	lastUpdate := struct {
		UpdatedAt       time.Time `db:"updated_at"`
		UpdatedByUserID int       `db:"updated_by_user_id"`
		FirstName       string    `db:"first_name"`
		LastName        string    `db:"last_name"`
	}{}
	err = db.Get().GetContext(ctx, &lastUpdate, lastUpdateQuery, id)
	if err != nil {
		if err == sql.ErrNoRows {
			return lwtClaim, errors.Wrap(err, "update for the LWT claim was not found")
		}
		return lwtClaim, errors.Wrap(err, "error loading LWT claim updates from database.")
	}

	lwtClaim.UpdatedByUserID = lastUpdate.UpdatedByUserID
	lwtClaim.UpdatedAt = lastUpdate.UpdatedAt
	lwtClaim.UpdatedByUserName = strings.Join([]string{lastUpdate.FirstName, lastUpdate.LastName}, " ")

	return lwtClaim, err
}

func lwtClaimUpdate(ctx context.Context, tx *sqlx.Tx, claim claimPayload) error {
	updateQuery := `update lwt_claims
					set invoice_number = :invoice_number,
					    loan_number = :loan_number,
    					vehicle_return_date = :vehicle_return_date,
    					vin = :vin,
    					year = :year,
    					make = :make,
    					model = :model,
    					vendor_id = :vendor_id,
						vendor_name = :vendor_name,
						vendor_address = :vendor_address,
						vendor_city = :vendor_city,
						vendor_state = :vendor_state,
						vendor_postal_code = :vendor_postal_code,
						contract_lender = :contract_lender,
    					contract_lender_address = :contract_lender_address,
    					contract_lender_city = :contract_lender_city,
    					contract_lender_state = :contract_lender_state,
    					contract_lender_postal_code = :contract_lender_postal_code,
    					status = :status,
    					in_progress = :in_progress,
    					requested_amount = :requested_amount,
    					approved_amount = :approved_amount,
    					owner_id = :owner_id,
    					customer_id = :customer_id,
    					date_of_claim_received = :date_of_claim_received,
    					finance_contract = :finance_contract,
    					completed_claim_form = :completed_claim_form,
						vehicle_condition_report = :vehicle_condition_report,
    					vin_plate_images = :vin_plate_images,
    					wear_and_tear_damage_images = :wear_and_tear_damage_images,
    					final_invoice_from_lessor = :final_invoice_from_lessor,
    					created_by_user_id = :created_by_user_id,
    					created_at = :created_at
					where id = :id`

	stmt, err := tx.PrepareNamedContext(ctx, updateQuery)
	if err != nil {
		return errors.Wrap(err, "error updating LWT claim, PrepareNamed failed")
	}
	defer func() { _ = stmt.Close() }()

	_, err = stmt.ExecContext(ctx, claim)
	if err != nil {
		return errors.Wrap(err, "error updating LWT claim, database error")
	}

	updateQuery = `update lwt_claim_line_items
					set description = :description,
    					note = :note,
    					requested_amount = :requested_amount,
    					approved_amount = :approved_amount,
    					status = :status,
    					denial_reason_id = :denial_reason_id,
    					images_received = :images_received,
    					denial_note = :denial_note
					where id = :id`

	itemStmt, err := tx.PrepareNamedContext(ctx, updateQuery)
	if err != nil {
		return errors.Wrap(err, "error updating LWT claim, PrepareNamed failed")
	}
	defer func() { _ = itemStmt.Close() }()

	for _, v := range claim.LineItems {
		_, err = itemStmt.ExecContext(ctx, v)
		if err != nil {
			return errors.Wrap(err, "error updating LWT claim, database error")
		}
	}
	return nil
}
