package lwt

import (
	"context"
	"encoding/json"
	"net/http"
	"strconv"
	"time"

	"phizz/db"
	"phizz/handlers"

	"github.com/jmoiron/sqlx"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
)

type claimAdjustment struct {
	ID                int             `json:"id" db:"id"`
	LWTClaimID        int             `json:"lwt_claim_id" db:"lwt_claim_id"`
	LWTClaimPaymentID int             `json:"lwt_claim_payment_id" db:"lwt_claim_payment_id"`
	Reason            string          `json:"reason" db:"reason"`
	Amount            decimal.Decimal `json:"amount" db:"amount"`
	CreatedByUserID   int             `json:"created_by_user_id" db:"created_by_user_id"`
	CreatedAt         time.Time       `json:"created_at" db:"created_at"`
}

// ClaimAdjustment adds the adjustment to the existing lwt claim
func ClaimAdjustment(_ http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	ctx := req.Context()

	var claimToAdjust struct {
		ID     int             `json:"id"`
		Amount decimal.Decimal `json:"amount"`
		Reason string          `json:"reason"`
	}

	dec := json.NewDecoder(req.Body)
	err := dec.Decode(&claimToAdjust)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Malformed data for claim adjustment", nil)
	}

	claim, err := getLWTClaimByID(ctx, strconv.Itoa(claimToAdjust.ID))
	if err != nil {
		handlers.ReportError(req, err)
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Invalid claim id", nil)
	}

	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error beginning transaction for LWT claim adjsutment payment update", nil)
	}

	// Update claim status
	claim.Status = db.LwtClaimStatusAdjusted
	_, err = tx.ExecContext(ctx, `update automotive_claims set status = $1 where id = $2`, claim.Status, claim.ID)
	if err != nil {
		_ = tx.Rollback()
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error updating claim status", nil)
	}

	// Add note on successful adjustment
	note := recordNotePayload{
		LWTClaimID:      claim.ID,
		CreatedByUserID: user.ID,
		CreatedAt:       time.Now(),
		NotesText:       "Adjusted claim with amount : $" + claimToAdjust.Amount.String(),
	}
	_, err = insertRecordNote(ctx, tx, &note)
	if err != nil {
		_ = tx.Rollback()
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Failed to add note", nil)
	}

	// Claim updated
	err = claimUpdated(ctx, tx, claim.ID, user.ID)
	if err != nil {
		_ = tx.Rollback()
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error inserting lwt_claim_updates", nil)
	}

	// Set required details for the adjustment
	claim.AdjustmentReason = claimToAdjust.Reason
	claim.ApprovedAmount = claimToAdjust.Amount

	claimPaymentID, err := claimAuthorize(ctx, tx, &claim, user.ID)
	if err != nil {
		_ = tx.Rollback()
		err = errors.Wrap(err, "error processing claim authorization")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error submitting adjustment claim", nil)
	}

	err = insertLwtClaimAdjustment(ctx, tx, claim, claimPaymentID, user.ID)
	if err != nil {
		_ = tx.Rollback()
		err = errors.Wrap(err, "error updating claim adjustment")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error submitting adjustment claim", nil)
	}

	// Add note on successful adjustment authorization
	note = recordNotePayload{
		LWTClaimID:      claim.ID,
		CreatedByUserID: user.ID,
		CreatedAt:       time.Now(),
		NotesText:       db.LWTRecordNoteDescription[claim.Status],
	}
	_, err = insertRecordNote(ctx, tx, &note)
	if err != nil {
		_ = tx.Rollback()
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Failed to add note", nil)
	}

	err = tx.Commit()
	if err != nil {
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error committing transaction for LWT claim reversal payment update", nil)
	}

	return http.StatusOK, map[string]interface{}{"id": claimToAdjust.ID}
}

func insertLwtClaimAdjustment(ctx context.Context, tx *sqlx.Tx, claim claimPayload, claimPaymentID int, userID int) error {
	query := `insert into lwt_claim_adjustments
				(
					lwt_claim_id,
					lwt_claim_payment_id,
					reason,
					amount,
					created_by_user_id,
					created_at
				) values
				(
					:lwt_claim_id,
					:lwt_claim_payment_id,
					:reason,
					:amount,
					:created_by_user_id,
					now() at time zone 'utc'
				)`

	adjustment := claimAdjustment{
		LWTClaimID:        claim.ID,
		LWTClaimPaymentID: claimPaymentID,
		Reason:            claim.AdjustmentReason,
		Amount:            claim.ApprovedAmount,
		CreatedByUserID:   userID,
	}

	adjustmentInsertStmt, err := tx.PrepareNamedContext(ctx, query)
	if err != nil {
		return errors.Wrap(err, "prepareNamed failed for claim adjustment")
	}
	defer func() { _ = adjustmentInsertStmt.Close() }()
	_, err = adjustmentInsertStmt.ExecContext(ctx, adjustment)
	if err != nil {
		return errors.Wrap(err, "error creating claim adjustment")
	}

	return nil
}
