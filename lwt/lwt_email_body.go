package lwt

import (
	"context"
	"net/http"
	"strings"
	"time"

	"phizz/db"
	"phizz/gap"
	"phizz/handlers"

	"github.com/go-chi/chi"
	newrelic "github.com/newrelic/go-agent"
	"github.com/pkg/errors"
)

// ClaimEmailBody returns emailBody for giving claimID and templateID
func ClaimEmailBody(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	ctx := req.Context()
	gapClaimID := chi.URLParam(req, "claimid")
	templateID := chi.URLParam(req, "templateid")

	txn := w.(newrelic.Transaction)
	emailBody, err := getEmailBody(ctx, gapClaimID, templateID, txn, req)
	if err != nil {
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Failed to get email body", nil)
	}
	return http.StatusOK, map[string]interface{}{"email_body": emailBody}
}

func getEmailBody(ctx context.Context, gapClaimID, templateID string, txn newrelic.Transaction, req *http.Request) (string, error) {
	lwtClaim, err := getLWTClaimByID(ctx, gapClaimID)
	if err != nil {
		return "", errors.Wrap(err, "error getting GAP claim from database")
	}

	gapContract, err := handlers.GetContractByID(txn, req, lwtClaim.ContractNumber)
	if err != nil {
		return "", errors.Wrap(err, "error getting contract data")
	}

	emailTemplate, err := gap.EmailTemplateSelect(templateID)
	if err != nil {
		return "", errors.Wrap(err, "error getting email-template from database")
	}

	emailBody, err := replaceTokens(&lwtClaim, &gapContract, emailTemplate.TemplateText)
	if err != nil {
		return "", errors.Wrap(err, "error replacing tokens")
	}

	return emailBody, nil
}

func replaceTokens(lwtClaim *claimPayload, gapContract *handlers.ContractData, templateText string) (string, error) {

	templateText = strings.Replace(templateText, db.TokenDateToday, formatDate(time.Now()), -1)

	// Values from contract
	if strings.Contains(templateText, db.TokenCustomerAddressFull) {
		address := gapContract.CustomerDetails.Address + "\n" + gapContract.CustomerDetails.City + ", " +
			gapContract.CustomerDetails.StateCode + " " + gapContract.CustomerDetails.PostalCode
		templateText = strings.Replace(templateText, db.TokenCustomerAddressFull, address, -1)
	}
	templateText = strings.Replace(templateText, db.TokenCustomerAddress, gapContract.CustomerDetails.Address, -1)
	templateText = strings.Replace(templateText, db.TokenCustomerCity, gapContract.CustomerDetails.City, -1)
	templateText = strings.Replace(templateText, db.TokenCustomerState, gapContract.CustomerDetails.StateCode, -1)
	templateText = strings.Replace(templateText, db.TokenCustomerZip, gapContract.CustomerDetails.PostalCode, -1)
	templateText = strings.Replace(templateText, db.TokenDealNumber, string(gapContract.Contract.DealNumber), -1)
	templateText = strings.Replace(templateText, db.TokenVehicleMake, gapContract.VehicleDetails.Make, -1)
	templateText = strings.Replace(templateText, db.TokenVehicleModel, gapContract.VehicleDetails.Model, -1)
	templateText = strings.Replace(templateText, db.TokenVehicleYear, gapContract.VehicleDetails.Year, -1)
	templateText = strings.Replace(templateText, db.TokenContractEffectiveDate, gapContract.Contract.EffectiveDate.Time.String(), -1)
	templateText = strings.Replace(templateText, db.TokenDealershipName, gapContract.Contract.IssuingDealer, -1)

	// values from claim
	templateText = strings.Replace(templateText, db.TokenCustomerNameFirst, gapContract.CustomerDetails.FirstName, -1)
	templateText = strings.Replace(templateText, db.TokenCustomerNameLast, gapContract.CustomerDetails.LastName, -1)
	customerNameFull := strings.Join([]string{gapContract.CustomerDetails.FirstName, gapContract.CustomerDetails.LastName}, " ")
	if gapContract.CustomerDetails.IsBusiness {
		customerNameFull = customerNameFull + "/" + gapContract.CustomerDetails.BusinessName
	}
	templateText = strings.Replace(templateText, db.TokenCustomerNameFull, customerNameFull, -1)
	templateText = strings.Replace(templateText, db.TokenContractNumber, gapContract.Contract.Code, -1)

	return templateText, nil
}

func formatDate(t time.Time) string {
	return t.Format("January 2, 2006")
}
