package lwt

import (
	"bytes"
	"database/sql"
	"encoding/csv"
	"fmt"
	"net/http"
	"phizz/types"
	"strconv"
	"strings"
	"time"

	"phizz/db"
	"phizz/handlers"

	"github.com/go-chi/chi"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
)

type workListClaim struct {
	ID                  int                `json:"id" db:"id"`
	CustomerName        string             `json:"customer_name" db:"customer_name"`
	ContractNumber      string             `json:"contract_number" db:"contract_number"`
	AssignedTo          string             `json:"assigned_to" db:"assigned_to"`
	Status              string             `json:"status" db:"status"`
	DateOfClaimReceived types.JSPQDate     `json:"date_of_claim_received" db:"date_of_claim_received"`
	PaidDate            types.JSPQNullDate `json:"paid_date" db:"paid_date"`
}

type claimListSlice []workListClaim

func (claim *workListClaim) slice() []string {
	result := make([]string, 6)
	result[0] = claim.CustomerName
	result[1] = claim.ContractNumber
	result[2] = claim.Status
	result[3] = claim.AssignedTo
	result[4] = claim.DateOfClaimReceived.Time.Format("2006-01-02")
	if claim.PaidDate.Valid {
		result[5] = claim.PaidDate.Time.Format("2006-01-02")
	} else {
		result[5] = ""
	}
	return result
}

func (claims claimListSlice) csv() string {
	result := new(bytes.Buffer)
	w := csv.NewWriter(result)
	_ = w.Write([]string{"Customer Name", "Contract#", "Status", "Assigned To", "Opened", "Paid Date"})
	for _, claim := range claims {
		_ = w.Write(claim.slice())
	}
	w.Flush()
	return result.String()
}

// ClaimCount returns count of claims for given status, agent, age
func ClaimCount(_ http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	ctx := req.Context()

	var statusArgs []interface{}
	var agentAgeArgs []interface{}

	countByStatusQuery := `select * from ( 
							select (case when ` + mapLWTStatus("In Inquiry") + ` then 'In Inquiry'
										when ` + mapLWTStatus("In Process") + ` then 'In Process'
										when ` + mapLWTStatus("In Review") + ` then 'In Review'
										when ` + mapLWTStatus("In Finance") + ` then 'In Finance' end) as name, count(*)
							from lwt_claims where status in ($1,$2,$3,$4,$5,$6,$7)
							group by (case when ` + mapLWTStatus("In Inquiry") + ` then 'In Inquiry'
										when ` + mapLWTStatus("In Process") + ` then 'In Process'
										when ` + mapLWTStatus("In Review") + ` then 'In Review'
										when ` + mapLWTStatus("In Finance") + ` then 'In Finance' end)) x
							order by (case when name = 'In Inquiry' then 1
										when name = 'In Process' then 2
										when name = 'In Review' then 3
										when name = 'In Finance' then 4 end) asc;`
	statusArgs = append(statusArgs,
		db.LwtClaimStatusInquiry,
		db.LwtClaimStatusPendingApproval,
		db.LwtClaimStatusApproved,
		db.LwtClaimStatusClaimInProcess,
		db.LwtClaimStatusPendingDenial,
		db.LwtClaimStatusReturnedClaim,
		db.LwtClaimStatusWaitingForCheck)

	countByAgentQuery := `select first_name || ' ' || last_name as name, count(*), users.id 
						from lwt_claims left join users on lwt_claims.owner_id = users.id 
						where lwt_claims.status in ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11)
						group by users.id 
						order by count desc`

	countByAgeQuery := `select * from (select (case
							when ((extract(epoch from age(lwt_claims.date_of_claim_received))/3600)/24)/7 <= 1 then '< 1 Week'
							when ((extract(epoch from age(lwt_claims.date_of_claim_received))/3600)/24)/7 > 1 
								and ((extract(epoch from age(lwt_claims.date_of_claim_received))/3600)/24)/7 <= 2 then '1-2 Weeks'
							when ((extract(epoch from age(lwt_claims.date_of_claim_received))/3600)/24)/7 > 2 
								and ((extract(epoch from age(lwt_claims.date_of_claim_received))/3600)/24)/7 <= 3 then '2-3 Weeks'
							when ((extract(epoch from age(lwt_claims.date_of_claim_received))/3600)/24)/7 > 3 
								and ((extract(epoch from age(lwt_claims.date_of_claim_received))/3600)/24)/7 <= 4 then '3-4 Weeks'
							when ((extract(epoch from age(lwt_claims.date_of_claim_received))/3600)/24)/7 > 4 then '> Month' end) as name, count(*)
						from lwt_claims where lwt_claims.status in ($1,$2,$3,$4,$5,$6,$7,$8, $9, $10, $11)
						group by (case
							when ((extract(epoch from age(lwt_claims.date_of_claim_received))/3600)/24)/7 <= 1 then '< 1 Week'
							when ((extract(epoch from age(lwt_claims.date_of_claim_received))/3600)/24)/7 > 1 
								and ((extract(epoch from age(lwt_claims.date_of_claim_received))/3600)/24)/7 <= 2 then '1-2 Weeks'
							when ((extract(epoch from age(lwt_claims.date_of_claim_received))/3600)/24)/7 > 2 
								and ((extract(epoch from age(lwt_claims.date_of_claim_received))/3600)/24)/7 <= 3 then '2-3 Weeks'
							when ((extract(epoch from age(lwt_claims.date_of_claim_received))/3600)/24)/7 > 3 
								and ((extract(epoch from age(lwt_claims.date_of_claim_received))/3600)/24)/7 <= 4 then '3-4 Weeks'
							when ((extract(epoch from age(lwt_claims.date_of_claim_received))/3600)/24)/7 > 4 then '> Month' end)) x
						order by (case
							when name = '< 1 Week' then 1
							when name = '1-2 Weeks' then 2
							when name = '2-3 Weeks' then 3
							when name = '3-4 Weeks' then 4
							when name = '> Month' then 5 end) ASC`

	agentAgeArgs = append(agentAgeArgs,
		db.LwtClaimStatusInquiry,
		db.LwtClaimStatusDeactivated,
		db.LwtClaimStatusClosedNoResponse,
		db.LWTClaimComplaintStatusDenied,
		db.LwtClaimStatusPendingApproval,
		db.LwtClaimStatusApproved,
		db.LwtClaimStatusClaimInProcess,
		db.LwtClaimStatusPendingDenial,
		db.LwtClaimStatusCheckWritten,
		db.LwtClaimStatusReturnedClaim,
		db.LwtClaimStatusWaitingForCheck)

	avgWaitingQuery := `select ((extract(epoch from avg(AGE(now(),date_of_claim_received)))/3600)/24)/7 
						from lwt_claims 
						where status in ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11)`

	var ageCount []claimCount
	err := db.Get().SelectContext(ctx, &ageCount, countByAgeQuery, agentAgeArgs...)
	if err != nil {
		err = errors.Wrap(err, "database error getting Auto claims lists count")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting lwt claims lists count data for age count", nil)
	}

	var agentCount []claimCount
	err = db.Get().SelectContext(ctx, &agentCount, countByAgentQuery, agentAgeArgs...)
	if err != nil {
		err = errors.Wrap(err, "database error getting Auto claims lists count")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting lwt claims lists count data for agent count", nil)
	}

	var statusCount []claimCount
	err = db.Get().SelectContext(ctx, &statusCount, countByStatusQuery, statusArgs...)
	if err != nil {
		err = errors.Wrap(err, "database error getting Auto claims lists count")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting lwt claims lists count data for status count", nil)
	}

	var avgWaitingPeriod decimal.NullDecimal
	err = db.Get().GetContext(ctx, &avgWaitingPeriod, avgWaitingQuery, agentAgeArgs...)
	if err != nil {
		err = errors.Wrap(err, "database error getting lwt claims lists count")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting Auto claims lists count data for claim count", nil)
	}
	claimAvgWaiting := avgWaitingPeriod.Decimal.Ceil().String()

	return http.StatusOK, map[string]interface{}{"age_count": ageCount, "agents_count": agentCount, "status_count": statusCount, "average_waiting_period": claimAvgWaiting}
}

// ClaimIndex returns lwt claims
func ClaimIndex(_ http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	ctx := req.Context()

	var claims claimListSlice
	var whereClauses []string
	args := struct {
		SearchQuery string `db:"search_query"`
		OwnerID     int    `db:"owner_id"`
	}{}

	nameContractVin := req.FormValue("q")

	if nameContractVin != "" {
		whereClauses = append(whereClauses, `(concat(c.first_name, ' ', c.last_name) ilike :search_query
		or concat(c.last_name, ' ', c.first_name) ilike :search_query
		or c.business_name ilike :search_query
		or contract_number ilike :search_query or vin ilike :search_query)`)
		args.SearchQuery = "%" + strings.Join(strings.Fields(strings.TrimSpace(nameContractVin)), " ") + "%"
	}

	userID := req.FormValue("user_id")
	if userID != "" {
		ownerID, err := strconv.Atoi(userID)
		if err == nil {
			whereClauses = append(whereClauses, " lc.owner_id = :owner_id")
			args.OwnerID = ownerID

		}
	}

	status := req.FormValue("status")
	if status != "" && status != "all" {
		statusQuery := mapLWTStatus(status)
		whereClauses = append(whereClauses, statusQuery)
	}

	age := req.FormValue("age")
	if age != "" && age != "all" {
		switch age {
		case "LessThan1Week":
			whereClauses = append(whereClauses, "(lc.date_of_claim_received > NOW() at time zone 'utc' - interval '7 days')")
		case "OneToTwoWeeks":
			whereClauses = append(whereClauses, "(lc.date_of_claim_received BETWEEN timezone('utc', now()) - interval '14 days' AND timezone('utc', now()) - interval '7 days')")
		case "TwoToThreeWeeks":
			whereClauses = append(whereClauses, "(lc.date_of_claim_received BETWEEN timezone('utc', now()) - interval '21 days' AND timezone('utc', now()) - interval '14 days')")
		case "ThreeToFourWeeks":
			whereClauses = append(whereClauses, "(lc.date_of_claim_received BETWEEN timezone('utc', now()) - interval '1 month' AND timezone('utc', now()) - interval '21 days')")
		case "GreaterThan1Month":
			whereClauses = append(whereClauses, "(lc.date_of_claim_received < NOW() at time zone 'utc' - '1 month'::::interval)")
		default:
			return http.StatusBadRequest, handlers.ErrorMessage(errors.New("Invalid age filter"), "Error getting Vta claims lists data - Invalid age filter", nil)
		}
	}

	orderBy := "date_of_claim_received desc"
	sortBy := req.FormValue("sort_by")
	if sortBy != "" {
		orderBy = mapLWTSortByColumn(sortBy)
	}

	sortOrder := req.FormValue("sort_order")
	if sortBy != "" && sortOrder == "asc" || sortOrder == "desc" {
		orderBy = orderBy + " " + sortOrder
	}

	wh := ""
	if len(whereClauses) > 0 {
		wh = "where " + strings.Join(whereClauses, " and ")
	}

	selectClause := `lc.id,
	case when c.is_business and (c.first_name!='' or c.last_name!='') then c.last_name || ',' || c.first_name || '/' || c.business_name
            when c.is_business and c.first_name='' and c.last_name='' then c.business_name
            else c.last_name || ',' || c.first_name end customer_name,
	lc.contract_number,
		lc.status, lc.date_of_claim_received, coalesce(max(lcp.paid_date), null) paid_date, 
		u.first_name || ' ' || u.last_name as assigned_to `

	fromClause := "lwt_claims lc" +
		" join customers c on lc.customer_id = c.id " +
		" join users u on lc.owner_id = u.id " +
		" left join lwt_claim_payments lcp on lc.id = lcp.lwt_claim_id "

	countFromClause := `lwt_claims lc
    		join customers c on lc.customer_id = c.id
    		join users u on lc.owner_id = u.id `

	countQuery := "select count(*) from " + countFromClause + " " + wh

	// handle pagination
	p := req.FormValue("page")
	var listQuery string

	if n, err := strconv.Atoi(p); err == nil && n > 0 {
		listQuery = fmt.Sprintf("select %s from %s %s group by lc.id, c.is_business, customer_name, lc.contract_number, "+
			"lc.status, lc.date_of_claim_received, u.first_name || ' ' || u.last_name"+
			" order by %s limit %d offset %d", selectClause, fromClause, wh, orderBy, handlers.PerPageEntries, (n-1)*handlers.PerPageEntries)
	} else { // if page number is not provided, return all values
		listQuery = fmt.Sprintf("select %s from %s %s group by lc.id, customer_name, lc.contract_number, "+
			"lc.status, lc.date_of_claim_received, u.first_name || ' ' || u.last_name order by %s", selectClause, fromClause, wh, orderBy)
	}

	stmt, err := db.Get().PrepareNamedContext(ctx, listQuery)
	if err != nil {
		err = errors.Wrap(err, "database error preparing get claims list query")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting claims", nil)
	}
	defer func() { _ = stmt.Close() }()
	err = stmt.SelectContext(ctx, &claims, args)
	if err != nil {
		err = errors.Wrap(err, "database error getting lwt claims lists")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting lwt claims lists data", nil)
	}

	stmt2, err := db.Get().PrepareNamedContext(ctx, countQuery)
	if err != nil {
		err = errors.Wrap(err, "database error preparing lwt claims count query")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting claims count", nil)
	}
	defer func() { _ = stmt2.Close() }()
	count := 0
	err = stmt2.GetContext(ctx, &count, args)
	if err != nil {
		err = errors.Wrap(err, "database error getting lwt claims lists count")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting lwt claims lists data", nil)
	}

	csv := req.FormValue("csv")
	if csv == "true" {
		claimsCsv := claims.csv()
		today := time.Now()
		fmt.Println("Worksheet_" + today.Format("2006_01_02T15_04_05") + ".csv")
		claims = nil
		return http.StatusOK, map[string]interface{}{"file": handlers.FilePayload{
			FileName: "Worksheet_" + today.Format("2006_01_02T15_04_05") + ".csv",
			Content:  claimsCsv,
		}}
	}

	return http.StatusOK, map[string]interface{}{"count": count, "lwt_claims": claims}
}

// ClaimByContract returns claims by contract number
func ClaimByContract(_ http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	ctx := req.Context()

	contractNumber := chi.URLParam(req, "id")
	// Get the lca claim from database
	query := "select id, contract_number, 1 as customer_id from lwt_claims where contract_number = $1"

	var lcaClaim struct {
		ID             int    `db:"id" json:"id"`
		ContractNumber string `db:"contract_number" json:"contract_number"`
		CustomerID     int    `db:"customer_id" json:"customer_id"`
	}

	err := db.Get().Unsafe().GetContext(ctx, &lcaClaim, query, contractNumber)
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage(err, "The lwt claim was not found", nil)
		}
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error loading LWT claim from database.", nil)
	}

	return http.StatusOK, map[string]interface{}{"lwt_claim": lcaClaim}
}

func mapLWTStatus(status string) string {
	queryString := ""
	switch status {
	case "In Inquiry":
		queryString += fmt.Sprintf(" (status = '%s') ", db.LwtClaimStatusInquiry)
	case "In Process":
		queryString += fmt.Sprintf(" (status in ('%s','%s','%s')) ",
			db.LwtClaimStatusClaimInProcess, db.LwtClaimStatusPendingDenial, db.LwtClaimStatusReturnedClaim)
	case "In Review":
		queryString += fmt.Sprintf(" (status in ('%s')) ", db.LwtClaimStatusPendingApproval)
	case "In Finance":
		queryString += fmt.Sprintf(" (status in ('%s','%s')) ", db.LwtClaimStatusWaitingForCheck, db.LwtClaimStatusApproved)
	case "Closed":
		queryString += fmt.Sprintf(" (status in ('%s','%s','%s','%s','%s')) ", db.LwtClaimStatusDenied,
			db.LwtClaimStatusClosedNoResponse, db.LwtClaimStatusDeactivated, db.LwtClaimStatusCheckWritten, db.LwtClaimStatusCheckVoided)
	case "All Active":
		queryString += fmt.Sprintf(" (status not in ('%s','%s')) ", db.LwtClaimStatusDeactivated, db.LwtClaimStatusDenied)
	case "all":
		queryString += fmt.Sprintf(" (status in ('%s','%s','%s','%s','%s', '%s', '%s','%s','%s','%s','%s')) ", db.LwtClaimStatusInquiry,
			db.LwtClaimStatusDeactivated, db.LwtClaimStatusClosedNoResponse, db.LWTClaimComplaintStatusDenied, db.LwtClaimStatusPendingApproval,
			db.LwtClaimStatusApproved, db.LwtClaimStatusClaimInProcess, db.LwtClaimStatusPendingDenial, db.LwtClaimStatusCheckWritten,
			db.LwtClaimStatusReturnedClaim, db.LwtClaimStatusWaitingForCheck)
	}

	if queryString == "" {
		queryString = fmt.Sprintf(" (status = '%s') ", status)
	}
	return queryString
}

func mapLWTSortByColumn(input string) string {
	switch input {
	case "customer_name":
		return "customer_name"
	case "contract_number":
		return "contract_number"
	case "status":
		return "status"
	case "assigned_to":
		return "assigned_to"
	case "date_of_claim_received":
		return "date_of_claim_received"
	case "paid_date":
		return "paid_date"
	}
	return ""
}
