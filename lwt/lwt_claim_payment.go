package lwt

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"phizz/types"
	"strconv"
	"time"

	"phizz/db"
	"phizz/handlers"
	"phizz/intacct"

	"github.com/go-chi/chi"
	"github.com/jmoiron/sqlx"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
	null "gopkg.in/guregu/null.v3"
)

type claimPaymentPayload struct {
	ID               int                 `json:"id" db:"id"`
	LWTClaimID       int                 `json:"lwt_claim_id" db:"lwt_claim_id"`
	CheckNumber      null.Int            `json:"check_number" db:"check_number"`
	Amount           decimal.NullDecimal `json:"amount" db:"amount"`
	PaidDate         types.JSPQNullDate  `json:"paid_date" db:"paid_date"`
	BatchKey         int                 `json:"batch_key" db:"batch_key"`
	<PERSON><PERSON><PERSON>          int                 `json:"bill_key" db:"bill_key"`
	Bill<PERSON>emo         string              `json:"bill_memo" db:"bill_memo"`
	BillNumber       string              `json:"bill_number" db:"bill_number"`
	AdjustmentAmount decimal.NullDecimal `json:"adjustment_amount" db:"adjustment_amount"`
	AdjustmentReason null.String         `json:"adjustment_reason" db:"adjustment_reason"`
	IsAdjustment     bool                `json:"is_adjustment" db:"is_adjustment"`
	Reason           null.String         `json:"reason" db:"reason"`
}

func claimAuthorize(ctx context.Context, tx *sqlx.Tx, claim *claimPayload, userID int) (int, error) {
	var claimPaymentID int

	authQuery := `insert into lwt_claim_payments(lwt_claim_id) values($1) returning id`
	row := tx.QueryRowContext(ctx, authQuery, claim.ID)
	err := row.Scan(&claimPaymentID)
	if err != nil {
		return claimPaymentID, errors.Wrap(err, "database error inserting new payment request")
	}

	var loc = handlers.LoadLocOrPanic(db.TimeZoneMountain)
	currentDate := time.Now().In(loc).Format("01-02-2006_15:04:05")

	billNumber := db.ProductCodeLeaseWearTear + "_" + claim.ContractNumber + "_" + currentDate
	billMemo := claim.ContractNumber + " " + claim.InvoiceNumber + " " + claim.Customer.LastName + "," + claim.Customer.FirstName
	description := "LWT claim for certificate LWT Contract #" + claim.ContractNumber +
		" ACCT #" + claim.VendorID + ", " + claim.Customer.FirstName + "," + claim.Customer.LastName

	payment, err := intacct.SubmitBill(ctx, tx, claim, billNumber, billMemo, description)
	if err != nil {
		// If custom error return as it is we dont want to change the message
		if isCustomError(err) {
			return claimPaymentID, err
		}
		return claimPaymentID, errors.Wrap(err, "submit to Intacct failed")
	}

	updateQuery := `update lwt_claim_payments set batch_key = $1, bill_key = $2, bill_memo = $3, bill_number = $4 where id = $5`
	_, err = tx.ExecContext(ctx,
		updateQuery,
		payment.BatchKey,
		payment.BillKey,
		billMemo,
		billNumber,
		claimPaymentID,
	)
	if err != nil {
		return claimPaymentID, errors.Wrap(err, "database error updating lwt payment details")
	}

	// Update claim status
	claim.Status = db.LwtClaimStatusWaitingForCheck
	updateClaimStatus := `update lwt_claims set status = $1 where id = $2`
	_, err = tx.ExecContext(ctx, updateClaimStatus, claim.Status, claim.ID)
	if err != nil {
		return claimPaymentID, errors.Wrap(err, "error updating lwt claim status")
	}

	// Add entry in update table for audit trail
	updateInsert := `insert into lwt_claim_updates(lwt_claim_id, updated_by_user_id, updated_at) values ($1, $2, now() at time zone 'utc')`
	_, err = tx.ExecContext(ctx, updateInsert, claim.ID, userID)
	if err != nil {
		return claimPaymentID, errors.Wrap(err, "error inserting lwt_claim_updates")
	}
	return claimPaymentID, err
}

// ClaimPayment returns the payment details for the lwt claim
func ClaimPayment(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	ctx := req.Context()

	id := chi.URLParam(req, "id")

	query := `select lca.amount adjustment_amount, 
       				lca.reason adjustment_reason,
       				case when lca.reason is null then false
       				    else true end is_adjustment,
       				lca.reason,
       				lcp.*
			from 
			    lwt_claim_payments lcp
				left join lwt_claim_adjustments lca
    				on lcp.id = lca.lwt_claim_payment_id
			where lcp.lwt_claim_id = $1
			and lcp.valid = true`

	// Get the lwt claim payment information from database
	var claimPayments []claimPaymentPayload
	err := db.Get().Unsafe().SelectContext(ctx, &claimPayments, query, id)
	if err != nil {
		handlers.ReportError(req, err)
		if err == sql.ErrNoRows {
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "The LWT claim payment was not found", nil)
		}
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error loading LWT claim payments from database.", nil)
	}

	return http.StatusOK, map[string]interface{}{"claim_payment": claimPayments}
}

// VerifyClaimVoid checks whether the particular claim is void in intacct
func VerifyClaimVoid(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	ctx := req.Context()
	claim := struct {
		ID             int    `json:"id" db:"id"`
		Status         string `json:"status" db:"status"`
		ContractNumber string `json:"-" db:"contract_number"`
	}{}

	err := db.Get().Get(&claim, `select id, status, contract_number from lwt_claims where id=$1`, chi.URLParam(req, "id"))
	if err != nil {
		handlers.ReportError(req, err)
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage(err, "The LWT claim was not found", nil)
		}
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Could not get claim data", nil)
	}
	if claim.Status != db.LwtClaimStatusCheckWritten {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Only checkWritten claim can be voided", nil)
	}

	paymentInfo := struct {
		CheckNumber int             `db:"check_number"`
		Amount      decimal.Decimal `db:"amount"`
		PaidDate    time.Time       `db:"paid_date"`
		BillKey     int             `db:"bill_key"`
	}{}
	authQuery := `select check_number, amount, paid_date, bill_key 
				from lwt_claim_payments 
				where lwt_claim_id = $1 
				  	and valid = true 
				limit 1`
	err = db.Get().Get(&paymentInfo, authQuery, claim.ID)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "could not get claim payment data"))
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage(err, "The LWT claim authorization was not found", nil)
		}
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Could not get claim payment data", nil)
	}
	claimReason := req.FormValue("reason")

	if claimReason == "" {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Invalid request for void payment, reason is needed", nil)
	}

	reversedClaim, err := intacct.VoidIntacctPayment(ctx, paymentInfo.BillKey, paymentInfo.Amount, claimReason)
	if err != nil && !reversedClaim {
		handlers.ReportError(req, errors.Wrap(err, "cannot reverse/void payment in Intacct for contract#:"+claim.ContractNumber))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Cannot reverse/void payment in Intacct for contract#: "+claim.ContractNumber, nil)
	}
	if reversedClaim {
		return http.StatusOK, map[string]interface{}{"checkNumber": paymentInfo.CheckNumber}
	}
	return http.StatusOK, map[string]interface{}{"checkNumber": -1}
}

// ClaimVoid voids the payment in intacct
func ClaimVoid(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	ctx := req.Context()

	var claim struct {
		ID             int    `json:"id" db:"id"`
		Status         string `json:"status" db:"status"`
		ContractNumber string `json:"-" db:"contract_number"`
	}
	err := db.Get().GetContext(ctx, &claim, `select id, status, contract_number from lwt_claims where id=$1`, chi.URLParam(req, "id"))
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage(err, "The LWT claim was not found", nil)
		}
		handlers.ReportError(req, errors.Wrap(err, "could not get claim data"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Could not get claim data", nil)
	}
	if claim.Status != db.LwtClaimStatusCheckWritten {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Only checkWritten claim can be voided", nil)
	}

	var paymentInfo struct {
		CheckNumber int             `db:"check_number"`
		Amount      decimal.Decimal `db:"amount"`
		PaidDate    time.Time       `db:"paid_date"`
		BillKey     int             `db:"bill_key"`
	}
	authQuery := `select check_number, amount, paid_date, bill_key 
					from lwt_claim_payments 
					where lwt_claim_id = $1
					and valid = true 
					limit 1`
	err = db.Get().GetContext(ctx, &paymentInfo, authQuery, claim.ID)
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage(err, "The LWT claim authorization was not found", nil)
		}
		handlers.ReportError(req, errors.Wrap(err, "could not get claim payment data"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Could not get claim payment data", nil)
	}

	var voidClaim struct {
		Reason string `json:"reason"`
	}
	dec := json.NewDecoder(req.Body)
	err = dec.Decode(&voidClaim)
	if err != nil || voidClaim.Reason == "" {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Invalid request for void payment, reason is needed", nil)
	}

	reversedClaim, err := intacct.VoidIntacctPayment(ctx, paymentInfo.BillKey, paymentInfo.Amount, voidClaim.Reason)
	if err != nil && !reversedClaim {
		handlers.ReportError(req, errors.Wrap(err, "cannot reverse/void payment in Intacct for contract#: "+claim.ContractNumber))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Cannot reverse/void payment in Intacct for contract#: "+claim.ContractNumber, nil)
	}

	// transaction
	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "database error beginning transaction for void payment"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error beginning transaction for void payment", nil)
	}

	// change status
	updateStatus := `update lwt_claims set status = $1 where id = $2`
	_, err = tx.ExecContext(ctx, updateStatus, db.LwtClaimStatusPendingApproval, claim.ID)
	if err != nil {
		_ = tx.Rollback()
		handlers.ReportError(req, errors.Wrap(err, "error updating LWT claim status"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error updating LWT claim status", nil)
	}

	// insert into lwt_voided_transactions, the amount should be negative hence multiplying by -1
	transactionQuery := `insert into lwt_voided_transactions (date_of_void, lwt_claim_id,check_amount,check_number) 
							values (now() at time zone 'utc',$1,$2,$3)`
	_, err = tx.ExecContext(ctx, transactionQuery, claim.ID, paymentInfo.Amount.Mul(decimal.NewFromFloat(-1)), paymentInfo.CheckNumber)
	if err != nil {
		_ = tx.Rollback()
		handlers.ReportError(req, errors.Wrap(err, "error inserting the transaction for void payment"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error inserting the transaction for void payment", nil)
	}

	// insert note
	recordNote := `insert into lwt_record_notes (created_at,is_manual,lwt_claim_id,notes_text,created_by_user_id) 
					values (now() at time zone 'utc',false, $1,$2,$3) returning id`
	note := fmt.Sprintf("The check with Number: %d Amount: %s Date: %s  is voided for reason '%s'",
		paymentInfo.CheckNumber, paymentInfo.Amount.String(), paymentInfo.PaidDate.Format("2006-01-02"), voidClaim.Reason)
	recordNoteID := 0
	err = tx.GetContext(ctx, &recordNoteID, recordNote, claim.ID, note, user.ID)
	if err != nil {
		_ = tx.Rollback()
		handlers.ReportError(req, errors.Wrap(err, "error inserting the note for void payment"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error inserting the note for void payment", nil)
	}

	// commit
	err = tx.Commit()
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "database error committing transaction for void payment"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error committing transaction for void payment", nil)
	}
	return http.StatusOK, map[string]interface{}{"id": claim.ID}
}

// ReceiveFromIntacct function receives the lwt claim batch paid info from intacct ap payment service
// the bill and payment request is generated by calling Intacct APIs
func ReceiveFromIntacct(ctx context.Context) (int, error) {

	var pendingPayments []struct {
		ID             int             `db:"id"`
		ContractNumber string          `db:"contract_number"`
		LWTClaimID     int             `db:"lwt_claim_id"`
		BillKey        int             `db:"bill_key"`
		BillMemo       string          `db:"bill_memo"`
		Amount         decimal.Decimal `db:"amount"`
		PaymentKey     sql.NullInt64   `db:"payment_key"`
		PaidDate       null.Time       `db:"paid_date"`
		CheckNumber    sql.NullInt64   `db:"check_number"`
	}
	checkCount := 0
	query := `select lcp.id, lcp.lwt_claim_id, lc.contract_number,
       				lcp.bill_key, lcp.bill_memo, lcp.payment_key,
       				lcp.paid_date, lcp.check_number, 
       				coalesce(lca.amount, lc.approved_amount) amount
				from lwt_claims lc
         			join lwt_claim_payments lcp
              			on lc.id = lcp.lwt_claim_id
        			left join lwt_claim_adjustments lca
            			on lc.id = lca.lwt_claim_id
                			and lcp.id = lca.lwt_claim_payment_id
				where lc.status = $1
					and lcp.valid = true`

	err := db.Get().Unsafe().SelectContext(ctx, &pendingPayments, query, db.LwtClaimStatusWaitingForCheck)
	if err != nil {
		return checkCount, errors.Wrap(err, "Error loading LWT claim payment from database.")
	}
	if len(pendingPayments) == 0 {
		fmt.Println("No pending payments for LWT")
		return checkCount, nil
	}

	for _, payment := range pendingPayments {
		log.Println("Updating LWT payment record for bill number and payment ", payment.BillKey, payment.Amount)

		// if payment key is 0 then get payment key from intacct
		if !(payment.PaymentKey.Valid) {
			paymentKey, err := intacct.GetPaymentKey(ctx, payment.BillKey, payment.Amount)
			if err != nil {
				log.Println(err)
				continue
			}
			payment.PaymentKey.Int64 = int64(paymentKey)

			// get paidInfo from intacct
			paidInfo, err := intacct.PaidInfo(ctx, int(payment.PaymentKey.Int64))
			if err != nil {
				log.Println(err)
				continue
			}

			payment.PaidDate = null.TimeFrom(paidInfo.PaidDate)
			payment.CheckNumber.Int64 = int64(paidInfo.CheckNumber)
			payment.Amount = paidInfo.Amount

			tx, err := db.Get().BeginTxx(ctx, nil)
			if err != nil {
				return checkCount, errors.Wrap(err, "database error beginning transaction for LWT claim ap receive payment update")
			}

			// update payments table with payment info and lwt claims table with status
			err = updateClaimPaid(ctx, tx, strconv.Itoa(payment.ID), strconv.Itoa(payment.LWTClaimID), paymentKey, paidInfo)
			if err != nil {
				_ = tx.Rollback()
				log.Println("Error updating paid info in lwt_claim_payments", err)
				continue
			}

			var userID int
			query := `select id from users where email = $1`
			err = tx.Unsafe().GetContext(ctx, &userID, query, db.SystemUserName)
			if err != nil {
				return checkCount, errors.Wrap(err, "error getting user details for script user")
			}

			// Add note on successful adjustment authorization
			note := recordNotePayload{
				LWTClaimID:      payment.LWTClaimID,
				CreatedByUserID: userID,
				CreatedAt:       time.Now(),
				NotesText:       db.LWTRecordNoteDescription[db.LwtClaimStatusCheckWritten],
			}
			_, err = insertRecordNote(ctx, tx, &note)
			if err != nil {
				_ = tx.Rollback()
				log.Println("Failed to add note", err)
				continue
			}
			log.Println("Updated successfully for LWT bill number and payment ", payment.BillKey, payment.Amount)

			err = tx.Commit()
			if err != nil {
				log.Println("Failed to commit database transaction", err)
				continue
			}

			// Expire contract once claim is paid and marked as CheckWritten
			expireNote := "Claim is paid by Check# " + strconv.Itoa(int(payment.CheckNumber.Int64)) + "On Date:" + payment.PaidDate.Time.String()
			_ = handlers.ContractExpire(payment.ContractNumber, expireNote)
			checkCount++
		}
	}
	return checkCount, nil
}

func isCustomError(e interface{}) bool {
	switch e.(type) {
	case *intacct.ErrorIntacct:
		return true
	default:
		return false
	}
}

// updateClaimPaid update payments table with payment info and lwt claims table with status
func updateClaimPaid(ctx context.Context, tx *sqlx.Tx, claimPaymentID string, claimID string, paymentKey int, claimPayment *intacct.ResponsePayload) error {
	acpQuery := `update lwt_claim_payments set payment_key = $1, check_number = $2, amount = $3,
		paid_date = $4, updated_at = now() at time zone 'utc' where id = $5 and valid = true`

	_, err := tx.ExecContext(ctx, acpQuery, paymentKey, claimPayment.CheckNumber, claimPayment.Amount, claimPayment.PaidDate, claimPaymentID)
	if err != nil {
		return errors.Wrap(err, "error updating lwt claim paid info")
	}

	acQuery := `update lwt_claims set status = $1 where id = $2`
	_, err = tx.ExecContext(ctx, acQuery, db.LwtClaimStatusCheckWritten, claimID)
	if err != nil {
		return errors.Wrap(err, "error updating lwt claim paid info")
	}
	return err
}

// Get the lwt claim payment information from database
func getPaymentDetailsByClaimID(ctx context.Context, claimID int) ([]claimPaymentPayload, error) {
	query := `select lca.amount adjustment_amount, lca.reason adjustment_reason,
		case when lca.reason is null then false else true end is_adjustment, lca.reason, lcp.*
		from lwt_claim_payments lcp left join lwt_claim_adjustments lca
			on lcp.id = lca.lwt_claim_payment_id
			where lcp.lwt_claim_id = $1 and lcp.valid = true`

	// Get the lwt claim payment information from database
	var claimPayments []claimPaymentPayload
	err := db.Get().Unsafe().SelectContext(ctx, &claimPayments, query, claimID)
	if err != nil {
		return claimPayments, errors.Wrap(err, "Error loading LWT claim payments from database.")
	}
	return claimPayments, nil
}
