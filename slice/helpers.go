package slice

import "time"

// ContainsTime returns true if the slice contains the specified value
func ContainsTime(slice []time.Time, val time.Time) bool {
	for _, v := range slice {
		if v.Equal(val) {
			return true
		}
	}
	return false
}

// ContainsString returns true if the slice contains the specified value
func ContainsString(slice []string, val string) bool {
	for _, v := range slice {
		if v == val {
			return true
		}
	}
	return false
}

// ContainsInt returns true if the slice contains the specified value
func ContainsInt(slice []int, val int) bool {
	for _, v := range slice {
		if v == val {
			return true
		}
	}
	return false
}
