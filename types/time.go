package types

import (
	"time"

	"github.com/lib/pq"
)

const (
	// JSDateFormat is the format an HTML input of type datetime-local accepts/returns as it's value
	JSDateFormat = "2006-01-02"
)

// JSPQNullDate is for marshaling and un-marshaling null-able date times via JSON
type JSPQNullDate struct {
	pq.NullTime
}

// UnmarshalJSON un-marshals a JSPQNullDate from JSON
func (ct *JSPQNullDate) UnmarshalJSON(b []byte) (err error) {
	if string(b) == "null" {
		ct.NullTime = pq.NullTime{}
		return
	}
	if b[0] == '"' && b[len(b)-1] == '"' {
		b = b[1 : len(b)-1]
	}
	if string(b) == "" {
		ct.NullTime = pq.NullTime{}
		return
	}
	ct.Valid = true
	ct.Time, err = time.Parse(JSDateFormat, string(b))
	return
}

// MarshalJSON marshals a JSPQNullDate to JSON
func (ct JSPQNullDate) MarshalJSON() ([]byte, error) {
	if ct.Valid {
		return []byte(`"` + ct.Time.Format(JSDateFormat) + `"`), nil
	}
	return []byte(`""`), nil
}

// JSPQDate is for marshaling and un-marshaling non-null-able date times via JSON
type JSPQDate struct {
	pq.NullTime
}

// UnmarshalJSON un-marshals a JSPQDate from JSON
func (ct *JSPQDate) UnmarshalJSON(b []byte) (err error) {
	if b[0] == '"' && b[len(b)-1] == '"' {
		b = b[1 : len(b)-1]
	}
	ct.Valid = true
	ct.Time, err = time.Parse(JSDateFormat, string(b))
	return
}

// MarshalJSON marshals a JSPQDate to JSON
func (ct JSPQDate) MarshalJSON() ([]byte, error) {
	if ct.Valid {
		return []byte(`"` + ct.Time.Format(JSDateFormat) + `"`), nil
	}
	return []byte(`""`), nil
}
