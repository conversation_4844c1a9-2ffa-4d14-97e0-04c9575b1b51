package types

import (
	"fmt"

	"github.com/shopspring/decimal"
)

// JSNullDecimal is for marshaling and un-marshaling nullable decimal values via JSON
type JSNullDecimal struct {
	decimal.NullDecimal
}

// UnmarshalJSON un-marshals a JSNullDecimal from JSON
func (dec *JSNullDecimal) UnmarshalJSON(b []byte) error {
	if string(b) == "null" {
		dec.NullDecimal = decimal.NullDecimal{}
		return nil
	}

	var err error
	dec.NullDecimal.Valid = true
	dec.NullDecimal.Decimal, err = decimal.NewFromString(string(b))
	if err != nil {
		dec.NullDecimal.Decimal = decimal.Zero
		dec.NullDecimal.Valid = false
	}

	return nil
}

// MarshalJSON marshals a JSNullDecimal to JSON
func (dec JSNullDecimal) MarshalJSON() ([]byte, error) {
	if dec.Valid {
		val, err := dec.Value()
		return []byte(fmt.Sprintf("%s", val)), err
	}
	return []byte(`null`), nil
}
