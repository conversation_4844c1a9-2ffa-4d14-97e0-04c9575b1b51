package types

import (
	"encoding/json"
	"testing"
	"time"
)

func TestUnMarshalJSPQNullTime(t *testing.T) {
	s := struct {
		DATime JSPQNullDate
	}{}
	s.DATime.Valid = true
	s.DATime.Time = time.Now()
	jsonBytes := []byte(`{"DATime":""}`)
	err := json.Unmarshal(jsonBytes, &s)
	if err != nil {
		t.<PERSON>rrorf(`Unmarshaling a JSPQNullTime of "" returned an error: %s`, err)
	}
	if s.DATime.Valid {
		t.Error(`Unmarshaling a value of "" didn't set Valid to false in the pq.NullTime`)
	}

	jsonBytes = []byte(`{"DATime":"2016-02-03"}`)
	err = json.Unmarshal(jsonBytes, &s)
	if err != nil {
		t.Errorf(`Unmarshaling a JSPQNullTime of "2016-02-03" returned an error: %s`, err)
	}
	if !s.DATime.Valid {
		t.Error(`Unmarshaling a value of "2016-02-03" set Valid to false in the pq.NullTime`)
	}
	if s.DATime.Time.Format(JSDateFormat) != "2016-02-03" {
		t.Error(`Unmarshaling gave the incorrect time`)
	}
}

func TestMarshalJSPQNullTime(t *testing.T) {
	s := struct {
		DATime JSPQNullDate
	}{}

	jsonBytes, err := json.Marshal(s)
	if err != nil {
		t.Error(`Marshaling a "null" JSPQNullTime failed with:`, err)
	}
	if string(jsonBytes) != `{"DATime":""}` {
		t.Error(`Marshaling a "null" JSPQNullTime returned`, string(jsonBytes))
	}

	s.DATime.Valid = true
	s.DATime.Time, _ = time.Parse(JSDateFormat, "2004-02-03")
	jsonBytes, err = json.Marshal(s)
	if err != nil {
		t.Error(`Marshaling a "non-null" JSPQNullTime failed with:`, err)
	}
	if string(jsonBytes) != `{"DATime":"2004-02-03"}` {
		t.Error(`Marshaling JSPQNullTime of 2004:02-03 returned`, string(jsonBytes))
	}

}
