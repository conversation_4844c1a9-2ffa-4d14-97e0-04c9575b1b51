package routes

import (
	"net/http"

	"github.com/go-chi/chi"
)

// StaticHandlers adds the static routes to the given router
func StaticHandlers(r chi.Router) {
	r.Get("/favicon.ico", http.HandlerFunc(func(w http.ResponseWriter, req *http.Request) {
		http.ServeFile(w, req, "public/favicon.ico")
	}))
	fs := http.StripPrefix("/static", http.FileServer(http.Dir("public")))
	r.Get("/static/*", http.HandlerFunc(func(w http.ResponseWriter, req *http.Request) {
		fs.ServeHTTP(w, req)
	}))
}
