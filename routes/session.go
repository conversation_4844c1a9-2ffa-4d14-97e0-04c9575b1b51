package routes

import (
	"github.com/go-chi/chi"
	"phizz/handlers"
)

// SessionHandlers adds the session routes to that router.
func SessionHandlers(r chi.Router) {
	r.<PERSON>("/auth", handlers.APIHandler(handlers.Auth))
	r.Get("/session", handlers.AuthenticatedAPIHandler(handlers.Session, nil))
	r.Get("/login-with-token", handlers.LoginWithToken)
	r.Delete("/session", handlers.APIHandler(handlers.Logout))
}
