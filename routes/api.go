package routes

import (
	"fmt"
	"net/http"
	"strings"

	"phizz/apreceiveui"
	"phizz/auto"
	"phizz/conf"
	"phizz/credit"
	"phizz/customer"
	"phizz/gap"
	"phizz/handlers"
	"phizz/lwt"
	"phizz/middleware"
	"phizz/vta"

	"github.com/go-chi/chi"
	"github.com/gorilla/csrf"
)

// APIHandlers sets up a new router with Authenticate middleware.
// Adds the API routes to that router.
func APIHandlers(r chi.Router) {
	config := conf.Get()
	s := chi.NewRouter()
	s.Use(middleware.Authenticate)
	s.Use(middleware.Active)
	s.Use(csrf.Protect(
		[]byte(config.CSRF.Key),
		csrf.Secure(config.CSRF.Secure),
		csrf.MaxAge(config.CSRF.MaxAge),
		csrf.ErrorHandler(http.HandlerFunc(handlers.APIHandler(handlers.CSRFError))),
	))

	addAPIRoute(s, "GET", "/health", handlers.Health)
	addAuthAPIRoute(s, "POST", "/email-templates", gap.EmailTemplateCreate, []string{"gap_claims_manager"})

	addAPIRoute(s, "GET", "/email-templates", gap.EmailTemplateIndex)
	addAPIRoute(s, "GET", "/email-templates/{id:[0-9]+}", gap.EmailTemplateShow)
	addAuthAPIRoute(s, "PUT", "/email-templates/{id:[0-9]+}", gap.EmailTemplateUpdate, []string{"gap_claims_manager"})
	addAuthAPIRoute(s, "DELETE", "/email-templates/{id:[0-9]+}", gap.EmailTemplateDelete, []string{"gap_claims_manager"})
	addAuthAPIRoute(s, "PUT", "/email-templates-reorder", gap.EmailTemplateReorder, []string{"gap_claims_manager"})

	addAPIRoute(s, "GET", "/gapclaims", gap.ClaimIndex)
	addAPIRoute(s, "GET", "/gapclaims/finance", gap.ClaimFinanceIndex)
	addAPIRoute(s, "GET", "/gapclaims/recovery", gap.ClaimRecoveryIndex)

	addAuthAPIRoute(s, "POST", "/gapclaims", gap.ClaimCreate, []string{"gap_claims", "gap_claims_manager"})
	addAPIRoute(s, "GET", "/gapclaims/{id:[0-9]+}", gap.ClaimShow)
	addAuthAPIRoute(s, "PUT", "/gapclaims/{id:[0-9]+}", gap.ClaimUpdate, []string{"gap_claims", "gap_claims_manager"})
	addAuthAPIRoute(s, "PUT", "/gapclaims/{id:[0-9]+}/void", gap.ClaimVoid, []string{"gap_claims_manager"})
	addAuthAPIRoute(s, "GET", "/gapclaims/{id:[0-9]+}/void", gap.VerifyClaimVoid, []string{"gap_claims_manager"})
	addAuthAPIRoute(s, "PUT", "/gapclaims/authorize", gap.ClaimAuthorize, []string{"gap_claims_manager"})

	addAuthAPIRoute(s, "POST", "/gapclaims/{id:[0-9]+}/child", gap.ChildClaimCreate, []string{"gap_claims", "gap_claims_manager"})
	addAPIRoute(s, "GET", "/gapclaims/{id:[0-9]+}/child/{child_id:[0-9]+}", gap.ChildClaimShow)
	addAuthAPIRoute(s, "PUT", "/gapclaims/{id:[0-9]+}/child/{child_id:[0-9]+}", gap.ChildClaimUpdate, []string{"gap_claims", "gap_claims_manager"})

	addAPIRoute(s, "GET", "/gapclaims/{id:[0-9]+}/payments", gap.ClaimPayment)

	addAuthAPIRoute(s, "POST", "/gapclaims/record-notes", gap.RecordNoteCreate, []string{"gap_claims", "gap_claims_manager", "view_only_claims"})
	addAPIRoute(s, "GET", "/gapclaims/record-notes/{gap_claim_id:[0-9]+}", gap.RecordNoteIndex)

	addAPIRoute(s, "GET", "/gapclaims/{id:[0-9]+}/field-notes", gap.FieldNoteIndex)
	addAuthAPIRoute(s, "POST", "/gapclaims/field-notes", gap.FieldNoteCreate, []string{"gap_claims", "gap_claims_manager", "recovery_team"})
	addAuthAPIRoute(s, "POST", "/gapclaims/contract-field-notes", gap.ContractFieldNoteCreate, []string{"gap_claims", "gap_claims_manager"})

	addAPIRoute(s, "GET", "/gapclaims/contract/{id}", gap.ClaimByContract)

	addAuthAPIRoute(s, "POST", "/gapclaims/documents", gap.SaveDocument, []string{"gap_claims", "gap_claims_manager", "recovery_team"})
	addAPIRoute(s, "GET", "/gapclaims/{id:[0-9]+}/documents", gap.DocumentIndex)
	addAuthRoute(s, "GET", "/gapclaims/documents/{id:[0-9]+}", gap.DocumentDownload, []string{})
	addAuthAPIRoute(s, "DELETE", "/gapclaims/documents/{id:[0-9]+}", gap.DocumentDelete, []string{"gap_claims", "gap_claims_manager", "recovery_team"})

	addAPIRoute(s, "GET", "/gapclaims/{claimid:[0-9]+}/email-body/{templateid:[0-9]+}", gap.ClaimEmailBody)
	addAuthAPIRoute(s, "POST", "/gapclaims/{id:[0-9]+}/letter", gap.ClaimLetter, []string{"gap_claims", "gap_claims_manager"})
	addAPIRoute(s, "GET", "/gapclaims/{id:[0-9]+}/letters", gap.ClaimLetterIndex)

	addAuthAPIRoute(s, "PUT", "/gapclaims/{id:[0-9]+}/recovery", gap.RecoveryUpdate, []string{"gap_claims", "gap_claims_manager", "recovery_team"})
	addAuthAPIRoute(s, "GET", "/gapclaims/{id:[0-9]+}/recovery", gap.RecoveryShow, []string{"gap_claims", "gap_claims_manager", "recovery_team", "accounting", "view_only_claims"})
	addAuthAPIRoute(s, "GET", "/gapclaims/{id:[0-9]+}/recovery-details", gap.RecoveryDetailsShow, []string{"recovery_team", "accounting", "view_only_claims"})
	addAuthAPIRoute(s, "PUT", "/gapclaims/{id:[0-9]+}/recovery-details", gap.RecoveryDetailsUpdate, []string{"recovery_team"})
	addAuthAPIRoute(s, "GET", "/gapclaims/{id:[0-9]+}/recovery-details/comments", gap.RecoveryCommentIndex, []string{"gap_claims", "gap_claims_manager", "recovery_team", "accounting", "view_only_claims"})
	addAuthAPIRoute(s, "POST", "/gapclaims/{id:[0-9]+}/recovery-details/comments", gap.RecoveryCommentCreate, []string{"recovery_team"})

	addAPIRoute(s, "GET", "/gapclaims/count/{countBy:[a-z]+}", gap.ClaimCounts)

	addAPIRoute(s, "GET", "/gapclaims/batch/{id:[0-9]+}", gap.BatchClaims)
	addAPIRoute(s, "GET", "/gapclaims/batches", gap.BatchIndex)

	addAPIRoute(s, "GET", "/contracts", handlers.ContractIndex)
	addAPIRoute(s, "GET", "/contract/{id:[0-9]+}", handlers.ContractShow)
	addRoute(s, "GET", "/contract/{id:[0-9]+}/download", handlers.ContractFormDownload)
	addAuthAPIRoute(s, "PUT", "/contracts/{id:[0-9]+}", handlers.ContractUpdate, []string{"gap_claims", "gap_claims_manager", "auto_claims", "auto_claims_manager", "recovery_team"})

	addAuthAPIRoute(s, "POST", "/contract/{code}/notes", handlers.ContractNoteCreate, []string{"gap_claims", "gap_claims_manager", "auto_claims", "auto_claims_manager", "recovery_team", "view_only_claims"})
	addAPIRoute(s, "GET", "/contract/{code}/notes", handlers.ContractNoteIndex)

	addAuthAPIRoute(s, "GET", "/contracts/coverage/{code}", handlers.CoverageDetails, []string{"auto_claims", "auto_claims_manager", "view_only_claims"})
	addAuthRoute(s, "POST", "/contracts/coverage/pdf", handlers.ContractCoverageAsPdf, []string{"auto_claims", "auto_claims_manager", "view_only_claims"})

	addAPIRoute(s, "GET", "/contract/{code}/attachments", handlers.ContractAttachmentIndex)
	addAPIRoute(s, "GET", "/contract/{code}/inspection/attachments", handlers.ContractInspectionAttachments)
	addRoute(s, "GET", "/contract/{contract_id:[0-9]+}/attachments/{id:[0-9]+}", handlers.ContractAttachmentDownload)

	addAPIRoute(s, "GET", "/customers/{code}", customer.Show)

	addAPIRoute(s, "GET", "/users", handlers.UsersIndex)
	addAuthAPIRoute(s, "GET", "/users/pre-approved-limits", handlers.UserLimitIndex, []string{"auto_claims_manager", "gap_claims_manager", "product_manager"})
	addAuthAPIRoute(s, "PUT", "/users/pre-approved-limits", handlers.UserLimitUpdate, []string{"auto_claims_manager", "gap_claims_manager", "product_manager"})
	addAPIRoute(s, "GET", "/users/claim-owners/gap", handlers.ClaimOwnersGAP)
	addAPIRoute(s, "GET", "/users/claim-owners/auto", handlers.ClaimOwnersAuto)
	addAPIRoute(s, "GET", "/users/claim-owners/vta", handlers.ClaimOwnersVTA)
	addAPIRoute(s, "GET", "/users/claim-owners/lwt", handlers.ClaimOwnersLWT)

	addAPIRoute(s, "GET", "/vendors", handlers.GetVendors)

	addAPIRoute(s, "GET", "/automotive-claims", auto.ClaimIndex)
	addAuthAPIRoute(s, "POST", "/automotive-claims", auto.ClaimCreate, []string{"auto_claims", "auto_claims_manager"})
	addAPIRoute(s, "GET", "/automotive-claims/{id:[0-9]+}", auto.ClaimShow)
	addAuthAPIRoute(s, "PUT", "/automotive-claims/{id:[0-9]+}", auto.ClaimUpdate, []string{"auto_claims", "auto_claims_manager"})
	addAuthAPIRoute(s, "PUT", "/automotive-claims/{id:[0-9]+}/cc-update", auto.CCClaimUpdate, []string{"accounting_claim_handler"})
	addAuthAPIRoute(s, "PUT", "/automotive-claims/{id:[0-9]+}/admin-update", auto.AdminClaimUpdate, []string{"accounting_claim_admin"})

	addAuthAPIRoute(s, "POST", "/automotive-chargeback-claims/{id:[0-9]+}", auto.ChargeBackClaimCreate, []string{"auto_claims_manager"})
	addAPIRoute(s, "GET", "/automotive-chargeback-claims/{id:[0-9]+}", auto.ChargeBackClaimShow)
	addAuthAPIRoute(s, "PUT", "/automotive-chargeback-claims/{id:[0-9]+}", auto.ChargeBackClaimUpdate, []string{"auto_claims_manager", "accounting"})

	addAuthAPIRoute(s, "POST", "/automotive-claims/record-notes", auto.RecordNoteCreate, []string{"auto_claims", "auto_claims_manager", "accounting", "view_only_claims"})
	addAPIRoute(s, "GET", "/automotive-claims/record-notes/{automotive_claim_id:[0-9]+}", auto.RecordNoteIndex)

	addAPIRoute(s, "GET", "/automotive-claims/claim-history/{contract_id}/{product_code}", auto.ClaimHistory)

	addAPIRoute(s, "GET", "/automotive-claims/{id:[0-9]+}/documents", auto.DocumentIndex)
	addAPIRoute(s, "GET", "/automotive-claims/{id:[0-9]+}/inspection/documents", auto.InspectionDocumentIndex)
	addAPIRoute(s, "GET", "/automotive-claims/{id:[0-9]+}/ccdocuments", auto.CCDocumentIndex)
	addAuthAPIRoute(s, "POST", "/automotive-claims/documents", auto.SaveDocument, []string{"auto_claims", "auto_claims_manager", "accounting_claim_handler"})
	addAuthRoute(s, "GET", "/automotive-claims/documents/{id:[0-9]+}", auto.DocumentDownload, []string{})
	addAuthAPIRoute(s, "DELETE", "/automotive-claims/documents/{id:[0-9]+}", auto.DocumentDelete, []string{"auto_claims", "auto_claims_manager", "accounting_claim_handler"})

	addAPIRoute(s, "GET", "/automotive-claims/{claim_id:[0-9]+}/complaints/{complaint_id:[0-9]+}", auto.ComplaintShow)
	addAuthAPIRoute(s, "POST", "/automotive-claims/{claim_id:[0-9]+}/complaints", auto.ComplaintCreate, []string{"auto_claims", "auto_claims_manager"})
	addAuthAPIRoute(s, "DELETE", "/automotive-claims/complaints/{id:[0-9]+}", auto.ComplaintDelete, []string{"auto_claims", "auto_claims_manager"})

	addAuthAPIRoute(s, "POST", "/automotive-claims/{claim_id:[0-9]+}/complaints/{complaint_id:[0-9]+}/parts", auto.PartCreate, []string{"auto_claims", "auto_claims_manager"})
	addAPIRoute(s, "GET", "/automotive-claims/{claim_id:[0-9]+}/complaints/{complaint_id:[0-9]+}/parts/{part_id:[0-9]+}", auto.PartShow)
	addAuthAPIRoute(s, "DELETE", "/automotive-claims/{claim_id:[0-9]+}/complaints/{complaint_id:[0-9]+}/parts/{part_id:[0-9]+}", auto.PartDelete, []string{"auto_claims", "auto_claims_manager"})

	addAuthAPIRoute(s, "POST", "/automotive-claims/{claim_id:[0-9]+}/complaints/{complaint_id:[0-9]+}/labors", auto.LaborCreate, []string{"auto_claims", "auto_claims_manager"})
	addAPIRoute(s, "GET", "/automotive-claims/{claim_id:[0-9]+}/complaints/{complaint_id:[0-9]+}/labors/{labor_id:[0-9]+}", auto.LaborShow)
	addAuthAPIRoute(s, "DELETE", "/automotive-claims/{claim_id:[0-9]+}/complaints/{complaint_id:[0-9]+}/labors/{labor_id:[0-9]+}", auto.LaborDelete, []string{"auto_claims", "auto_claims_manager"})

	addAuthAPIRoute(s, "POST", "/automotive-claims/{claim_id:[0-9]+}/complaints/{complaint_id:[0-9]+}/towings", auto.TowingCreate, []string{"auto_claims", "auto_claims_manager"})
	addAPIRoute(s, "GET", "/automotive-claims/{claim_id:[0-9]+}/complaints/{complaint_id:[0-9]+}/towings/{towing_id:[0-9]+}", auto.TowingShow)
	addAuthAPIRoute(s, "DELETE", "/automotive-claims/{claim_id:[0-9]+}/complaints/{complaint_id:[0-9]+}/towings/{towing_id:[0-9]+}", auto.TowingDelete, []string{"auto_claims", "auto_claims_manager"})

	addAuthAPIRoute(s, "POST", "/automotive-claims/{claim_id:[0-9]+}/complaints/{complaint_id:[0-9]+}/rentals", auto.RentalCreate, []string{"auto_claims", "auto_claims_manager"})
	addAPIRoute(s, "GET", "/automotive-claims/{claim_id:[0-9]+}/complaints/{complaint_id:[0-9]+}/rentals/{rental_id:[0-9]+}", auto.RentalShow)
	addAuthAPIRoute(s, "DELETE", "/automotive-claims/{claim_id:[0-9]+}/complaints/{complaint_id:[0-9]+}/rentals/{rental_id:[0-9]+}", auto.RentalDelete, []string{"auto_claims", "auto_claims_manager"})

	addAuthAPIRoute(s, "POST", "/automotive-claims/{claim_id:[0-9]+}/complaints/{complaint_id:[0-9]+}/sublets", auto.SubletCreate, []string{"auto_claims", "auto_claims_manager"})
	addAPIRoute(s, "GET", "/automotive-claims/{claim_id:[0-9]+}/complaints/{complaint_id:[0-9]+}/sublets/{sublet_id:[0-9]+}", auto.SubletShow)
	addAuthAPIRoute(s, "DELETE", "/automotive-claims/{claim_id:[0-9]+}/complaints/{complaint_id:[0-9]+}/sublets/{sublet_id:[0-9]+}", auto.SubletDelete, []string{"auto_claims", "auto_claims_manager"})

	addAuthAPIRoute(s, "POST", "/automotive-claims/{claim_id:[0-9]+}/complaints/{complaint_id:[0-9]+}/miscs", auto.MiscCreate, []string{"auto_claims", "auto_claims_manager"})
	addAPIRoute(s, "GET", "/automotive-claims/{claim_id:[0-9]+}/complaints/{complaint_id:[0-9]+}/miscs/{misc_id:[0-9]+}", auto.MiscShow)
	addAuthAPIRoute(s, "DELETE", "/automotive-claims/{claim_id:[0-9]+}/complaints/{complaint_id:[0-9]+}/miscs/{misc_id:[0-9]+}", auto.MiscDelete, []string{"auto_claims", "auto_claims_manager"})

	addAPIRoute(s, "GET", "/automotive-claims/count/{countBy:[a-z]+}", auto.ClaimCounts)
	addAPIRoute(s, "GET", "/automotive-claims/reconciliations", auto.ReconciliationsIndex)
	addAPIRoute(s, "GET", "/automotive-claims/reconciliations/history", auto.ReconciliationsHistory)
	addAuthAPIRoute(s, "PUT", "/automotive-claims/reconciliations", auto.ReconciliationsUpdate, []string{"auto_claims", "auto_claims_manager", "accounting"})
	addAuthAPIRoute(s, "PUT", "/automotive-claims/{id:[0-9]+}/customer", handlers.ClaimCustomerUpdate, []string{"auto_claims", "auto_claims_manager"})

	addAPIRoute(s, "GET", "/automotive-claims/reassign", auto.ClaimReassignIndex)
	addAuthAPIRoute(s, "PUT", "/automotive-claims/reassign/manager", auto.ClaimReassignManager, []string{"auto_claims_manager"})
	addAuthAPIRoute(s, "PUT", "/automotive-claims/reassign/agent", auto.ClaimReassignAgent, []string{"auto_claims"})
	addAuthAPIRoute(s, "PUT", "/automotive-claims/reassign/self", auto.ClaimReassignSelf, []string{"auto_claims", "auto_claims_manager"})
	addAuthAPIRoute(s, "PUT", "/automotive-claims/reassign/action", auto.ClaimReassignAcceptOrReject, []string{"auto_claims"})
	addAuthAPIRoute(s, "PUT", "/automotive-claims/{claim_id:[0-9]+}/reopen", auto.ClaimReopen, []string{"auto_claims", "auto_claims_manager"})

	addAuthAPIRoute(s, "PUT", "/automotive-claims/authorize", auto.ClaimAuthorize, []string{"accounting"})

	addFileAPIRoute(s, "GET", "/lwt", lwt.ClaimIndex)
	addAuthAPIRoute(s, "GET", "/lwt/count", lwt.ClaimCount, []string{"gap_claims", "view_only_claims"})
	addAPIRoute(s, "GET", "/lwt-claims/contract/{id}", lwt.ClaimByContract)
	addAuthAPIRoute(s, "POST", "/lwt-claims", lwt.ClaimCreate, []string{"gap_claims", "gap_claims_manager"})
	addAuthAPIRoute(s, "PUT", "/lwt-claims/{id:[0-9]+}", lwt.ClaimUpdate, []string{"gap_claims", "gap_claims_manager"})
	addAPIRoute(s, "GET", "/lwt-claims/{id:[0-9]+}", lwt.ClaimShow)
	addAuthAPIRoute(s, "PUT", "/lwt-claims/{lwt_claim_id:[0-9]+}/reopen", lwt.ClaimReopen, []string{"gap_claims", "gap_claims_manager"})
	addAuthAPIRoute(s, "POST", "/lwt-claims/documents", lwt.SaveDocument, []string{"gap_claims", "gap_claims_manager"})
	addAuthAPIRoute(s, "GET", "/lwt-claims/{id:[0-9]+}/documents", lwt.DocumentIndex, []string{"gap_claims", "gap_claims_manager", "view_only_claims"})
	addAuthAPIRoute(s, "GET", "/lwt-claims/document-types", lwt.DocumentTypeIndex, []string{"gap_claims", "gap_claims_manager", "view_only_claims"})
	addAuthRoute(s, "GET", "/lwt-claims/documents/{id:[0-9]+}", lwt.DocumentDownload, []string{})
	addAuthAPIRoute(s, "DELETE", "/lwt-claims/documents/{id:[0-9]+}", lwt.DocumentDelete, []string{"gap_claims", "gap_claims_manager"})
	addAPIRoute(s, "GET", "/lwt-claims/{claim_id:[0-9]+}/line-items/{line_item_id:[0-9]+}", lwt.LineItemShow)
	addAuthAPIRoute(s, "POST", "/lwt-claims/{claim_id:[0-9]+}/line-items", lwt.LineItemCreate, []string{"gap_claims", "gap_claims_manager"})
	addAuthAPIRoute(s, "PUT", "/lwt-claims/{id:[0-9]+}/line-items", lwt.LineItemUpdate, []string{"gap_claims_manager"})
	addAuthAPIRoute(s, "DELETE", "/lwt-claims/line-items/{id:[0-9]+}", lwt.LineItemDelete, []string{"gap_claims", "gap_claims_manager"})
	addAuthAPIRoute(s, "POST", "/lwt-claims/record-notes", lwt.RecordNoteCreate, []string{"gap_claims", "gap_claims_manager", "accounting", "view_only_claims"})
	addAPIRoute(s, "GET", "/lwt-claims/record-notes/{lwt_claim_id:[0-9]+}", lwt.RecordNoteIndex)
	addAuthAPIRoute(s, "GET", "/lwt-claims/{id:[0-9]+}/field-notes", lwt.FieldNoteIndex, []string{"gap_claims", "gap_claims_manager", "recovery_team", "view_only_claims"})
	addAuthAPIRoute(s, "POST", "/lwt-claims/field-notes", lwt.FieldNoteCreate, []string{"gap_claims", "gap_claims_manager", "recovery_team"})
	addAPIRoute(s, "GET", "/lwt-claims/{id:[0-9]+}/payments", lwt.ClaimPayment)
	addAuthAPIRoute(s, "PUT", "/lwt-claims/{id:[0-9]+}/void", lwt.ClaimVoid, []string{"gap_claims_manager"})
	addAuthAPIRoute(s, "GET", "/lwt-claims/{id:[0-9]+}/void", lwt.VerifyClaimVoid, []string{"gap_claims_manager"})
	addAuthAPIRoute(s, "POST", "/lwt-claims/adjust", lwt.ClaimAdjustment, []string{"gap_claims", "gap_claims_manager"})
	addAPIRoute(s, "GET", "/lwt-claims/{id:[0-9]+}/letters", lwt.ClaimLetterIndex)
	addAPIRoute(s, "GET", "/lwt-claims/{claimid:[0-9]+}/email-body/{templateid:[0-9]+}", lwt.ClaimEmailBody)
	addAuthAPIRoute(s, "POST", "/lwt-claims/{id:[0-9]+}/letter", lwt.ClaimLetter, []string{"gap_claims", "gap_claims_manager"})

	addAPIRoute(s, "GET", "/facilities", auto.FacilityIndex)
	addAuthAPIRoute(s, "GET", "/facilities/csv", auto.FacilityCsv, []string{"auto_claims_manager", "product_manager"})
	addAuthAPIRoute(s, "POST", "/facilities", auto.FacilityCreate, []string{"auto_claims", "auto_claims_manager", "accounting"})
	addAuthAPIRoute(s, "GET", "/facilities/{id}", auto.FacilityShow, []string{"auto_claims", "auto_claims_manager", "accounting", "view_only_claims"})
	addAuthAPIRoute(s, "PUT", "/facilities/vendor", auto.FacilityVendorUpdate, []string{"auto_claims", "auto_claims_manager", "accounting"})
	addAuthAPIRoute(s, "PUT", "/facilities/{id}", auto.FacilityUpdate, []string{"auto_claims", "auto_claims_manager", "accounting"})

	addAPIRoute(s, "GET", "/dms/ro/{storeID}/{roNumber}", handlers.DMSRO)
	addAPIRoute(s, "GET", "/dms/ro/customer", handlers.ROCustomer)

	addAPIRoute(s, "GET", "/zones", auto.ZoneIndex)
	addAPIRoute(s, "GET", "/zones/{id:[0-9]+}/facilities", auto.ZoneFacilityIndex)
	addAuthAPIRoute(s, "POST", "/zones", auto.ZoneCreate, []string{"auto_claims_manager"})
	addAuthAPIRoute(s, "PUT", "/zones/{id:[0-9]+}", auto.ZoneUpdate, []string{"auto_claims_manager"})
	addAuthAPIRoute(s, "PUT", "/zones/{id:[0-9]+}/facilities", auto.UpdateZoneFacilities, []string{"auto_claims_manager"})
	addAuthAPIRoute(s, "DELETE", "/zones/{id:[0-9]+}", auto.ZoneDelete, []string{"auto_claims_manager"})
	addAuthAPIRoute(s, "PUT", "/zones/{id:[0-9]+}/hold", auto.ZoneHold, []string{"auto_claims_manager"})
	addAuthAPIRoute(s, "PUT", "/zones/{id:[0-9]+}/unhold", auto.ZoneUnhold, []string{"auto_claims_manager"})

	addAPIRoute(s, "GET", "/automotive-claims/unassigned-agents", auto.UnassignedAgents)

	addAPIRoute(s, "GET", "/repair-codes/{code}", auto.RepairCodeIndex)
	addAPIRoute(s, "GET", "/insurance-companies", gap.InsuranceCompanyIndex)
	addAPIRoute(s, "GET", "/insurance-companies/{id:[0-9]+}", gap.InsuranceCompanyShow)
	addAPIRoute(s, "POST", "/insurance-companies", gap.InsuranceCompanyCreate)

	addAuthAPIRoute(s, "GET", "/automotive-claims/batches", auto.BatchIndex, []string{"auto_claims_manager", "accounting"})
	addAuthAPIRoute(s, "GET", "/automotive-claims/batch/{id:[0-9]+}", auto.BatchClaims, []string{"auto_claims_manager", "accounting"})

	addAPIRoute(s, "GET", "/automotive-claims/{id:[0-9]+}/payments", auto.ClaimPayment)
	addAuthAPIRoute(s, "GET", "/automotive-claims/approved-claims", auto.ApprovedClaims, []string{"auto_claims_manager", "accounting"})
	addAuthAPIRoute(s, "GET", "/automotive-claims/approved-claims/supporting-data", auto.ApprovedClaimsSupportingData, []string{"auto_claims_manager", "accounting"})

	addAuthAPIRoute(s, "GET", "/automotive-claims/{id:[0-9]+}/payee", auto.CustomerPayee, []string{"auto_claims", "auto_claims_manager"})
	addAuthAPIRoute(s, "POST", "/automotive-claims/{id:[0-9]+}/payee", auto.CustomerPayeeUpdate, []string{"auto_claims", "auto_claims_manager"})

	addAuthAPIRoute(s, "POST", "/automotive-claims/reverse", auto.ClaimReverse, []string{"auto_claims_manager"})
	addAuthAPIRoute(s, "POST", "/automotive-claims/adjust", auto.ClaimAdjustment, []string{"auto_claims", "auto_claims_manager"})

	addFileAPIRoute(s, "GET", "/vta-claims", vta.ClaimIndex)
	addAPIRoute(s, "GET", "/vta-claims/count/{countBy:[a-z]+}", vta.ClaimCounts)

	addAuthAPIRoute(s, "POST", "/vta-claims", vta.ClaimCreate, []string{"gap_claims", "gap_claims_manager"})
	addAPIRoute(s, "GET", "/vta-claims/{id:[0-9]+}", vta.ClaimShow)
	addAuthAPIRoute(s, "PUT", "/vta-claims/{id:[0-9]+}", vta.ClaimUpdate, []string{"gap_claims", "gap_claims_manager"})
	addAPIRoute(s, "GET", "/vta-claims/contract/{id}", vta.ClaimByContract)
	addAPIRoute(s, "GET", "/vta-claims/{id:[0-9]+}/payments", vta.ClaimPayment)
	addAuthAPIRoute(s, "PUT", "/vta-claims/{vta_claim_id:[0-9]+}/reopen", vta.ClaimReopen, []string{"gap_claims_manager"})

	addAuthAPIRoute(s, "POST", "/vta-claims/record-notes", vta.RecordNoteCreate, []string{"gap_claims", "gap_claims_manager", "view_only_claims"})
	addAuthAPIRoute(s, "GET", "/vta-claims/record-notes/{vta_claim_id:[0-9]+}", vta.RecordNoteIndex, []string{"gap_claims", "gap_claims_manager", "view_only_claims"})

	addAuthAPIRoute(s, "POST", "/vta-claims/documents", vta.SaveDocument, []string{"gap_claims", "gap_claims_manager"})
	addAuthAPIRoute(s, "GET", "/vta-claims/{id:[0-9]+}/documents", vta.DocumentIndex, []string{"gap_claims", "gap_claims_manager", "view_only_claims"})
	addAuthRoute(s, "GET", "/vta-claims/documents/{id:[0-9]+}", vta.DocumentDownload, []string{})
	addAuthAPIRoute(s, "DELETE", "/vta-claims/documents/{id:[0-9]+}", vta.DocumentDelete, []string{"gap_claims", "gap_claims_manager"})

	addAuthAPIRoute(s, "GET", "/vta-claims/{id:[0-9]+}/field-notes", vta.FieldNoteIndex, []string{"gap_claims", "gap_claims_manager", "recovery_team", "view_only_claims"})
	addAuthAPIRoute(s, "POST", "/vta-claims/field-notes", vta.FieldNoteCreate, []string{"gap_claims", "gap_claims_manager", "recovery_team"})

	addAuthAPIRoute(s, "GET", "/vta-claims/{id:[0-9]+}/gap", vta.GapClaimExist, []string{"gap_claims", "gap_claims_manager", "view_only_claims"})
	addAuthAPIRoute(s, "PUT", "/vta-claims/{id:[0-9]+}/gap", vta.UpdateFromGap, []string{"gap_claims", "gap_claims_manager"})

	addAuthAPIRoute(s, "PUT", "/vta-claims/{id:[0-9]+}/void", vta.ClaimVoid, []string{"gap_claims_manager"})
	addAuthAPIRoute(s, "GET", "/vta-claims/{id:[0-9]+}/void", vta.VerifyClaimVoid, []string{"gap_claims_manager"})

	addAuthAPIRoute(s, "GET", "/credit-card", credit.CardShow, []string{"auto_claims_manager"})
	addAuthAPIRoute(s, "GET", "/credit-card/record-notes", credit.RecordNoteIndex, []string{"auto_claims_manager"})
	addAuthAPIRoute(s, "PUT", "/credit-card", credit.CardUpdate, []string{"auto_claims_manager"})

	addAuthRoute(s, "GET", "/automotive-claims/{id:[0-9]+}/pdf", auto.ClaimPdf, []string{"auto_claims", "auto_claims_manager", "view_only_claims"})
	addAuthRoute(s, "GET", "/gap-claims/{id:[0-9]+}/pdf", gap.ClaimPdf, []string{"gap_claims", "gap_claims_manager", "view_only_claims"})
	addAuthRoute(s, "GET", "/vta-claims/{id:[0-9]+}/pdf", vta.ClaimPdf, []string{"gap_claims", "gap_claims_manager", "view_only_claims"})
	addAuthRoute(s, "GET", "/lwt-claims/{id:[0-9]+}/pdf", lwt.ClaimPdf, []string{"gap_claims", "gap_claims_manager", "view_only_claims"})

	addAuthAPIRoute(s, "PUT", "/apreceive", apreceiveui.APReceive, []string{"product_manager", "accounting"})

	rProxyConfig := conf.Get().S3ReverseProxy
	downloadRoutePath := strings.Replace(rProxyConfig.PathPrefix, "/api", "", 1)
	downloadRoutePath = fmt.Sprintf("%s/*", downloadRoutePath)
	addAuthRoute(s, "GET", downloadRoutePath, handlers.DownloadFile, nil)

	r.Mount("/api", s)
}
