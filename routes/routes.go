package routes

import (
	"net/http"
	"phizz/handlers"
	"phizz/nr"

	"github.com/go-chi/chi"
	"github.com/newrelic/go-agent"
)

var nrApp newrelic.Application

func init() {
	nrApp = nr.GetApp()
}

func addRoute(router chi.Router, method, route string, handler http.HandlerFunc) {
	nrRoute, nrHandlerFunc := newrelic.WrapHandleFunc(nrApp, route, handler)
	router.MethodFunc(method, nrRoute, nrHandlerFunc)
}

func addAuthRoute(router chi.Router, method, route string, handler handlers.AuthenticatedHandlerFunc, roles []string) {
	nrRoute, nrHandlerFunc := newrelic.WrapHandleFunc(nrApp, route, handlers.AuthenticatedHandler(handler, roles))
	router.MethodFunc(method, nrRoute, nrHandlerFunc)
}

func addFileAPIRoute(router chi.Router, method, route string, handler handlers.APIHandlerFunc) {
	nrRoute, nrHandlerFunc := newrelic.WrapHandleFunc(nrApp, route, handlers.APIFileHandler(handler))
	router.MethodFunc(method, nrRoute, nrHandlerFunc)
}

func addAPIRoute(router chi.Router, method, route string, handler handlers.APIHandlerFunc) {
	nrRoute, nrHandlerFunc := newrelic.WrapHandleFunc(nrApp, route, handlers.APIHandler(handler))
	router.MethodFunc(method, nrRoute, nrHandlerFunc)
}

func addAuthAPIRoute(router chi.Router, method, route string, handler handlers.AuthenticatedAPIHandlerFunc, roles []string) {
	nrRoute, nrHandlerFunc := newrelic.WrapHandleFunc(nrApp, route, handlers.AuthenticatedAPIHandler(handler, roles))
	router.MethodFunc(method, nrRoute, nrHandlerFunc)
}
