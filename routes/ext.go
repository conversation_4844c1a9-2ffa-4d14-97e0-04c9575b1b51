package routes

import (
	"phizz/auto"
	"phizz/handlers"
	"phizz/middleware"

	"github.com/go-chi/chi"
)

// ExtHandlers sets up a new router with /ext.
func ExtHandlers(r chi.Router) {
	s := chi.NewRouter()
	s.Use(middleware.ExternalAuthenticate)

	addAPIRoute(s, "GET", "/check-detail/{check_number}", handlers.CheckDetailList)
	addAPIRoute(s, "GET", "/checks", handlers.ChecksByDateRange)
	addAPIRoute(s, "GET", "/denied-claims", handlers.DeniedClaimList)
	addAPIRoute(s, "GET", "/auto-claims", auto.LCAClaimIndex)
	addAPIRoute(s, "POST", "/auto-claims/documents", auto.LCAClaimSaveDocument)
	addAPIRoute(s, "GET", "/auto-claims/{id:[0-9]+}/documents", auto.LCAClaimDocumentIndex)
	addAPIRoute(s, "GET", "/auto-claims/documents/{id:[0-9]+}", auto.LCADocumentDownload)
	addAPIRoute(s, "POST", "/auto-claims", auto.LCAClaimCreate)
	addAPIRoute(s, "GET", "/auto-claims/{contract_number}/{product_code}", auto.ContractClaims)
	addAPIRoute(s, "GET", "/claims/{contract_number}/{product_code}", handlers.ClaimData)
	addAPIRoute(s, "GET", "/auto-claims/{ro}", handlers.ClaimsByRO)
	addAPIRoute(s, "GET", "/facilities/{store_id:[0-9]+}", auto.FacilityByStore)
	addAPIRoute(s, "GET", "/facilities", auto.ListFacilities)
	addAPIRoute(s, "POST", "/store", handlers.CreateStore)
	addAPIRoute(s, "PUT", "/store/{store_id:[0-9]+}", handlers.UpdateStore)
	addAPIRoute(s, "POST", "/company", handlers.CreateCompany)
	addAPIRoute(s, "PUT", "/company/{company_id:[0-9]+}", handlers.UpdateCompany)
	addAPIRoute(s, "PUT", "/edit-vins", handlers.EditVINs)
	addAPIRoute(s, "GET", "/auto-claims/{id:[0-9]+}", auto.LCAClaimShow)
	addAPIRoute(s, "PUT", "/users/{email}/activate", handlers.UserActivate)
	addAPIRoute(s, "PUT", "/users/{email}/deactivate", handlers.UserDeactivate)

	r.Mount("/ext", s)
}
