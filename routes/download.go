package routes

import (
	"phizz/auto"
	"phizz/middleware"

	"github.com/go-chi/chi"
)

// DownloadHandlers contains the routes for download links
func DownloadHandlers(r chi.Router) {
	s := chi.NewRouter()
	s.Use(middleware.Authenticate)
	s.Use(middleware.Active)

	addAuthRoute(s, "GET", "/automotive-claims/{id:[0-9]+}/invoice/credit-card", auto.CCInvoice, []string{"auto_claims", "auto_claims_manager", "accounting_claim_handler"})

	r.<PERSON>("/download", s)
}
