package email

import (
	"phizz/conf"

	"github.com/jordan-wright/email"
	"github.com/pkg/errors"
)

// SendHTMLSync will send an HTML email based on config or use the logger for development
func SendHTMLSync(fromEmail string, toEmail []string, subject string, body []byte) error {
	config := conf.Get()
	if config.Email.UseLog {
		logEmail(fromEmail, toEmail, subject, body)
		return nil
	}
	e := email.NewEmail()
	e.From = fromEmail //"<EMAIL>"
	e.To = toEmail
	e.Subject = subject
	e.HTML = body
	err := e.Send(config.Email.Server(), config.Email.SMTPAuth())
	if err != nil {
		errors.Wrap(err, "Error in SendHTML")
	}
	return err
}
