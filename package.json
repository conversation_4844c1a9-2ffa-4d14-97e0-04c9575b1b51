{"name": "phizz", "version": "1.0.0", "description": "build assets (js and css)", "private": true, "devDependencies": {"@babel/cli": "^7.16.0", "@babel/core": "^7.16.0", "@babel/eslint-parser": "^7.16.0", "@babel/plugin-proposal-class-properties": "^7.16.0", "@babel/plugin-proposal-decorators": "^7.16.0", "@babel/plugin-proposal-do-expressions": "^7.16.0", "@babel/plugin-proposal-export-default-from": "^7.16.0", "@babel/plugin-proposal-export-namespace-from": "^7.16.0", "@babel/plugin-proposal-function-bind": "^7.16.0", "@babel/plugin-proposal-function-sent": "^7.16.0", "@babel/plugin-proposal-json-strings": "^7.16.0", "@babel/plugin-proposal-logical-assignment-operators": "^7.16.0", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.16.0", "@babel/plugin-proposal-numeric-separator": "^7.16.0", "@babel/plugin-proposal-optional-chaining": "^7.16.0", "@babel/plugin-proposal-pipeline-operator": "^7.16.0", "@babel/plugin-proposal-throw-expressions": "^7.16.0", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-syntax-import-meta": "^7.10.4", "@babel/preset-env": "^7.16.0", "@babel/preset-react": "^7.16.0", "autoprefixer": "^10.4.0", "babel-loader": "^8.2.3", "css-loader": "^6.5.1", "cypress": "^9.0.0", "eslint": "7.32.0", "eslint-plugin-chai-friendly": "^0.7.2", "eslint-plugin-cypress": "^2.12.1", "eslint-plugin-react": "^7.28.0", "eslint-plugin-react-hooks": "^4.3.0", "exports-loader": "^3.1.0", "file-loader": "^6.2.0", "imports-loader": "^3.1.1", "mini-css-extract-plugin": "^2.5.3", "npm-run-all": "^4.1.5", "resolve-url-loader": "^2.0.2", "sass": "^1.49.4", "sass-loader": "^12.4.0", "style-loader": "^3.3.1", "url-loader": "^4.1.1", "validate.js": "^0.13.1", "webpack": "^5.70.0", "webpack-cli": "^4.9.2", "webpack-dev-server": "^4.7.4"}, "dependencies": {"@fortawesome/fontawesome-free": "^5.15.4", "accounting": "^0.4.1", "bootstrap": "4.3.1", "core-js": "^3.20.3", "d3": "^4.9.1", "es6-promise": "^4.2.8", "history": "^2.0.1", "immstruct": "^2.0.0", "immutable": "^3.8.1", "jquery": "^3.6.0", "lodash": "^4.17.4", "moment": "2.29.4", "prop-types": "^15.5.10", "react": "^17.0.2", "react-autosize-textarea": "^3.0.3", "react-datepicker": "1.5.0", "react-dnd": "^2.4.0", "react-dnd-html5-backend": "^2.4.1", "react-dom": "^17.0.2", "react-loader-advanced": "^1.6.1", "react-router": "^3.0.5", "react-s-alert": "1.4.1", "react-select": "^1.0.0-rc.10", "react-tooltip": "^3.11.6", "regenerator-runtime": "^0.13.9", "rollbar": "^2.24.1"}, "optionalDependencies": {"fsevents": "^1.2.7"}, "scripts": {"lint": "eslint --fix --ext=js,jsx assets/js", "manifest": "printf '{\"version\":\"%s\"}' `date -u +%Y%m%d%H%M%S` > public/app.json", "watch-client": "NODE_ENV=development webpack-dev-server --compress --progress", "test": "go test ./...", "start-server": "go run main.go", "watch-server": "air", "dev": "run-p watch-server watch-client"}}