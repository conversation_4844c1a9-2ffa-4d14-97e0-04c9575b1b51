{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Launch Phizz",
            "type": "go",
            "request": "launch",
            "mode": "debug",
            "program": "${workspaceRoot}/main.go",
            "env": {
              "AWS_PROFILE": "tca-s3-dev"
            },
            "cwd": "${workspaceRoot}",
            "showLog": true
        },
        {
            "name": "Launch ApReceive",
            "type": "go",
            "request": "launch",
            "mode": "debug",
            "program": "${workspaceRoot}/cmd/apreceive/main.go",
            "cwd": "${workspaceRoot}",
            "showLog": true
        },
    ]
}