package session

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/gob"
	"fmt"
	"log"
	"net/http"
	"strconv"
	"time"

	"phizz/conf"
	"phizz/db"
	"phizz/stackutil"
	"phizz/util"

	"github.com/chmike/securecookie"
	"github.com/lib/pq/hstore"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
	"github.com/stvp/rollbar"
)

type contextKey string

func (c contextKey) String() string {
	return "context key " + string(c)
}

const (
	// ExpireDuration is the duration we allow one session instance to last
	ExpireDuration = 6 * time.Hour

	// storeIDCookie is the name of the cookie that stores the ID of the users selected store
	storeIDCookie = "_phizzStoreID"

	// ContextKeyReqID is the context key for a request ID
	ContextKeyReqID = contextKey("ReqID")

	// ContextKeyCurrentUser is the context key for current user
	// Warning: duplicated in util.contextKeyCurrentUser in order to avoid an import cycle
	ContextKeyCurrentUser = contextKey("CurrentUser")

	contextSessionKey = contextKey("Session")

	// storeIDMaxAge is the maximum age (in seconds) of the cookie that contains the storeID
	storeIDMaxAge = 2147483647
)

// Store is for use to send the store data down to the user
// It differs from db.Store in that there's no sensitive data
type Store struct {
	ID                 int           `db:"id" json:"id"`
	Code               string        `db:"code" json:"code"`
	Name               string        `db:"name" json:"name"`
	TimeZone           string        `db:"time_zone" json:"time_zone"`
	HasROIntegration   bool          `db:"has_ro_integration" json:"has_ro_integration"`
	HasDealIntegration bool          `db:"has_deal_integration" json:"has_deal_integration"`
	StateCode          string        `db:"state_code" json:"state_code"`
	InspectionSetID    sql.NullInt64 `db:"inspection_set_id" json:"inspection_set_id"`
}

// User is for use to send the user data down to the user
// It differs from db.User in that there's no sensitive data
type User struct {
	ID               int             `db:"id" json:"id"`
	Email            string          `db:"email" json:"email"`
	FirstName        string          `db:"first_name" json:"first_name"`
	LastName         string          `db:"last_name" json:"last_name"`
	ApprovalLimit    decimal.Decimal `db:"approval_limit" json:"approval_limit"`
	GAPApprovalLimit decimal.Decimal `db:"gap_approval_limit" json:"gap_approval_limit"`
	LWTApprovalLimit decimal.Decimal `db:"lwt_approval_limit" json:"lwt_approval_limit"`
	Roles            hstore.Hstore   `db:"roles" json:"roles"`
	Stores           []Store         `db:"stores" json:"stores"`
	BannerInfo       BannerInfo      `db:"-" json:"banner_info"`
	AppEnv           string          `db:"-" json:"appenv"`
}

// BannerInfo information regarding of banner display
type BannerInfo struct {
	ID      int    `json:"id" db:"id"`
	Enabled bool   `json:"enabled" db:"enabled"`
	Message string `json:"message" db:"message"`
	Header  string `json:"header" db:"header"`
}
type secureSessionData struct {
	UserID     int
	LastActive time.Time
}

type sessionData struct {
	secureSessionData
	StoreID         int
	LastBannerFetch time.Time
}
type responseWriter struct {
	http.ResponseWriter
	before func()
}

func (w *responseWriter) WriteHeader(status int) {
	w.before()
	w.ResponseWriter.WriteHeader(status)
}

// Middleware is chi middleware for processing the cookie-based session
func Middleware(name string, key []byte) func(http.Handler) http.Handler {
	sessionObj := securecookie.MustNew(name, key, securecookie.Params{
		Path:     "/",
		MaxAge:   int(ExpireDuration.Seconds()),
		Secure:   conf.Get().Session.Secure,
		HTTPOnly: true,
	})
	return func(next http.Handler) http.Handler {
		fn := func(w http.ResponseWriter, req *http.Request) {
			data := sessionData{}
			val, err := sessionObj.GetValue(nil, req)
			if err == nil {
				dec := gob.NewDecoder(bytes.NewReader(val))
				if err = dec.Decode(&data); err != nil {
					ReportError(req, errors.Wrap(err, "failed to decode session object"))
				}
			} else if err != http.ErrNoCookie {
				ReportError(req, errors.Wrap(err, "failed to get session object"))
			}

			cookie, err := req.Cookie(storeIDCookie)
			if err == nil {
				data.StoreID, _ = strconv.Atoi(cookie.Value)
			}

			req = req.WithContext(context.WithValue(req.Context(), contextSessionKey, &data))
			ctx := req.Context()
			w = &responseWriter{
				ResponseWriter: w,
				before: func() {
					if data.UserID == 0 {
						util.LogMessage(ctx, "Deleting session cookie")
						sessionObj.Delete(w)
					} else {
						buf := bytes.NewBuffer([]byte{})
						enc := gob.NewEncoder(buf)
						err = enc.Encode(&data.secureSessionData)
						if err != nil {
							ReportError(req, errors.Wrap(err, "Could not save a session (gob encode)"))
						}
						err = sessionObj.SetValue(w, buf.Bytes())
						if err != nil {
							ReportError(req, errors.Wrap(err, "Could not save a session (securecookie setValue)"))
						}

						// Save StoreID in a cookie that will not expire soon
						http.SetCookie(w, &http.Cookie{
							Name:     storeIDCookie,
							Value:    fmt.Sprintf("%d", data.StoreID),
							Path:     "/",
							MaxAge:   storeIDMaxAge,
							HttpOnly: true,
						})
					}
				},
			}

			user, _ := FindCurrentUser(req) // Just attempting to get current user
			if user != nil {
				req = req.WithContext(context.WithValue(req.Context(), ContextKeyCurrentUser, user))
			}

			next.ServeHTTP(w, req)
		}
		return http.HandlerFunc(fn)
	}
}

func get(req *http.Request) (*sessionData, error) {
	data, ok := req.Context().Value(contextSessionKey).(*sessionData)
	if !ok {
		return data, errors.New("sessionData not in request context")
	}
	return data, nil
}

// FindCurrentUser gets the current user from the session and returns a db.User struct
func FindCurrentUser(req *http.Request) (*db.User, error) {
	userID, err := GetUserID(req)
	if err != nil {
		return nil, errors.Wrap(err, "error from GetUserID in session.findCurrentUser")
	}

	user := db.User{}

	err = db.Get().Get(
		&user,
		`select "id", "created_at", "updated_at", "email", "first_name", "last_name", "company_id", "active", "roles", "confirmed_at", "confirmation_token_set_at", "confirmation_token", "reset_password_token", "reset_password_token_set_at" from users where id = $1`,
		userID,
	)
	if err != nil {
		return nil, errors.Wrapf(err, "Error getting user from DB in session.findCurrentUser for userID %d", userID)
	}

	return &user, nil
}

// GetUserID retrieves the user ID from the session
func GetUserID(req *http.Request) (int, error) {
	data, err := get(req)
	if err != nil {
		return 0, errors.Wrap(err, "Error getting session")
	}
	return data.UserID, nil
}

// SetUserID stores the user ID in the session
func SetUserID(req *http.Request, id int) error {
	data, err := get(req)
	if err != nil {
		return errors.Wrap(err, "Error getting session")
	}
	data.UserID = id
	return nil
}

// GetStoreID retrieves the store ID from the session
func GetStoreID(req *http.Request) (int, error) {
	data, err := get(req)
	if err != nil {
		return 0, errors.Wrap(err, "Error getting session")
	}
	return data.StoreID, nil
}

// SetStoreID stores the store ID in the session
func SetStoreID(req *http.Request, id int) error {
	data, err := get(req)
	if err != nil {
		return errors.Wrap(err, "Error getting session")
	}
	data.StoreID = id
	return nil
}

// GetLastActive returns the last active time for the user from the session
// if the session doesn't have a last active time nil is returned
func GetLastActive(req *http.Request) (time.Time, error) {
	data, err := get(req)
	if err != nil {
		return time.Time{}, errors.Wrap(err, "Error getting session")
	}
	return data.LastActive, nil
}

// SetLastActive updates the lastActive in the session
func SetLastActive(req *http.Request, lastActive time.Time) error {
	data, err := get(req)
	if err != nil {
		return errors.Wrap(err, "Error getting session")
	}
	data.LastActive = lastActive
	return nil
}

// GetLastBannerFetch returns the last time for the user when banner info fetched
// if the session doesn't have a last time for the user when banner info fetched nil is returned
func GetLastBannerFetch(req *http.Request) (*time.Time, error) {
	data, err := get(req)
	if err != nil {
		return nil, errors.Wrap(err, "error getting session")
	}
	return &data.LastBannerFetch, nil
}

// SetLastBannerFetch updates the lastBannerFetch in the session
func SetLastBannerFetch(req *http.Request, lastBannerFetch *time.Time) error {
	data, err := get(req)
	if err != nil {
		return errors.Wrap(err, "error getting session")
	}
	data.LastBannerFetch = *lastBannerFetch
	return nil
}

// Logout clears the session
func Logout(req *http.Request) {
	data, err := get(req)
	if err == nil {
		data.UserID = 0
		data.LastActive = time.Time{}
	}
}

// ReportError reports errors (with an HTTP context) to rollbar
func ReportError(req *http.Request, err error) {
	field := rollbar.Field{Name: "person", Data: map[string]string{"id": "unknown"}}
	if val := req.Context().Value(ContextKeyCurrentUser); val != nil {
		if user := val.(*db.User); user != nil {
			field.Data = map[string]string{
				"id":    strconv.Itoa(user.ID),
				"email": user.Email,
			}
		}
	}
	rollbar.RequestErrorWithStackSkip(rollbar.ERR, req, err, 1, &field)
	log.Printf("ERROR: %#v\n", err)
	if conf.Get().AppEnv == "development" { // TODO don't show backtrace from std lib / vendor
		log.Println(stackutil.BuildStack(2))
	}
}
