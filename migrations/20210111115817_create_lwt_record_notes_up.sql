CREATE TABLE lwt_record_notes (
  id serial NOT NULL,
  is_manual bool NOT NULL DEFAULT false,
  lwt_claim_id integer NOT NULL,
  notes_text varchar(255) NOT NULL,
  created_by_user_id integer NOT NULL,
  created_at timestamp NOT NULL,

  CONSTRAINT lwt_record_notes_pkey PRIMARY KEY (id),
  CONSTRAINT lwt_claim_id_fkey FOREIGN KEY (lwt_claim_id) REFERENCES lwt_claims (id) ON UPDATE CASCADE ON DELETE RESTRICT,
  CONSTRAINT lwt_record_notes_created_by_user_id_fkey FOREIGN KEY (created_by_user_id) REFERENCES users (id) ON UPDATE CASCADE ON DELETE RESTRICT
);

CREATE INDEX lwt_record_notes_created_by_user_id_idx on lwt_record_notes USING btree (created_by_user_id);
CREATE INDEX lwt_record_notes_created_at_idx on lwt_record_notes USING btree (created_at);
CREATE INDEX lwt_record_notes_lwt_claim_id_idx ON lwt_record_notes USING btree (lwt_claim_id);

CREATE TABLE lwt_claim_field_notes (
  id                 SERIAL,
  lwt_claim_id       INTEGER   NOT NULL,
  field_id           INTEGER   NOT NULL,
  notes_text         VARCHAR(255),
  created_by_user_id INTEGER   NOT NULL,
  created_at         TIMESTAMP NOT NULL,
  CONSTRAINT lwt_claim_field_notes_pkey PRIMARY KEY (id),
  CONSTRAINT lwt_claim_field_notes_user_id_fkey FOREIGN KEY (created_by_user_id) REFERENCES users (id) ON UPDATE CASCADE ON DELETE RESTRICT,
  CONSTRAINT lwt_claim_field_notes_claim_id_fkey FOREIGN KEY (lwt_claim_id) REFERENCES lwt_claims (id) ON UPDATE CASCADE ON DELETE RESTRICT
);
CREATE INDEX lwt_claim_field_notes_lwt_claim_id_idx ON lwt_claim_field_notes USING BTREE (lwt_claim_id);
CREATE INDEX lwt_claim_field_notes_field_id_idx ON lwt_claim_field_notes USING BTREE (field_id);
CREATE INDEX lwt_claim_field_notes_created_by_user_id_idx ON lwt_claim_field_notes USING BTREE (created_by_user_id);
CREATE INDEX lwt_claim_field_notes_created_at_idx ON lwt_claim_field_notes USING BTREE (created_at);