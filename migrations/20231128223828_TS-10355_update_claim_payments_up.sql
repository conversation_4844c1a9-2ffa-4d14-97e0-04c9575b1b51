CREATE TEMPORARY TABLE tmpClaims (
    contract_number VARCHAR(255) NOT NULL,
    ro VARCHAR(255) NOT NULL
);

INSERT INTO tmpClaims
    (contract_number, ro)
VALUES 
    ('PPM-B9314561', '868731'),
    ('PPM-B8177622', '868784'),
    ('PPM-B8217486', '868797'),
    ('PPM-B9154695', '868733'),
    ('PPM-A7490370', '868589'),
    ('PPM-B9215940', '868604'),
    ('MCS1910843', '868578'),
    ('PPM-B9300543', '868575'),
    ('PPM-B9145055', '868573'),
    ('PPM-B9023230', '868562'),
    ('PPM-A7426783', '868552'),
    ('MCS1915554', '868545'),
    ('PPM-A7764237', '868536'),
    ('PPM-B9296780', '868518'),
    ('PPM-B9210448', '868513'),
    ('PPM-B8219786', '868512'),
    ('PPM-A8026971', '868473'),
    ('PPM-B8877565', '868441'),
    ('PPM-B9145952', '868439'),
    ('PPM-B8376120', '868432'),
    ('PPM-B8744834', '868431'),
    ('PPM-B8065330', '868264'),
    ('PPM-A7996954', '868401'),
    ('PPM-A7526998', '868400'),
    ('PPM-A4139058', '868395'),
    ('PPM-B9115726', '868394'),
    ('PPM-B9256757', '868388'),
    ('PPM-B8970055', '868387'),
    ('PPM-B9296108', '868383'),
    ('PPM-B9201598', '868381'),
    ('PPM-B8780895', '868374'),
    ('PPM-A3769162', '868361'),
    ('PPM-B8894637', '868348'),
    ('MCS2022512', '868345'),
    ('PPM-B9222739', '868338'),
    ('PPM-A4095359', '868323'),
    ('PPM-B9250559', '868322'),
    ('PPM-A6912999', '868308'),
    ('PPM-B8938593', '868306'),
    ('PPM-A7833782', '868304'),
    ('PPM-A7904417', '868301'),
    ('PPM-B9212704', '868297'),
    ('PPM-B8770409', '868293'),
    ('PPM-B8799116', '868255'),
    ('PPM-B8388248', '868166'),
    ('PPM-B9010486', '868123'),
    ('PPM-A8017008', '865207'),
    ('PPM-B8953532', '868201'),
    ('PPM-A3757488', '868184'),
    ('PPM-B9227949', '868180'),
    ('PPM-B8817504', '868169'),
    ('PPM-A7423431', '868143'),
    ('PPM-B9297586', '868090'),
    ('PPM-B9308609', '868087'),
    ('PPM-A4285091', '868065'),
    ('PPM-B8154255', '868031'),
    ('PPM-B9312345', '868598'),
    ('PPM-B9266997', '868581'),
    ('PPM-B9274584', '868574'),
    ('PPM-B8498734', '867811'),
    ('PPM-B9113152', '868003'),
    ('PPM-B9007273', '867998'),
    ('PPM-B9160608', '867980'),
    ('PPM-B8803899', '867964'),
    ('PPM-B9242524', '867962'),
    ('PPM-A6993610', '867961'),
    ('PPM-B8726434', '867958'),
    ('PPM-A4269949', '867909'),
    ('PPM-B9266712', '867908'),
    ('PPM-B8867212', '867883'),
    ('PPM-A7676303', '867812'),
    ('PPM-B8685183', '867623'),
    ('PPM-B9221378', '867395'),
    ('PPM-A6910206', '866487'),
    ('PPM-A3725508', '867788'),
    ('PPM-B8943399', '867787'),
    ('PPM-A7745615', '867776'),
    ('PPM-B9322078', '867771'),
    ('PPM-A4124477', '867724'),
    ('PPM-B9288159', '867719'),
    ('PPM-B9199884', '868555'),
    ('PPM-B9003166', '867706'),
    ('PPM-B8580216', '867703'),
    ('PPM-A3514353', '867534'),
    ('PPM-B9174766', '867533'),
    ('PPM-A7573867', '867513'),
    ('PPM-B8982469', '867650'),
    ('PPM-A7128896', '867603'),
    ('PPM-B9321932', '867559'),
    ('PPM-B9278488', '867536'),
    ('PPM-A7101827', '867406'),
    ('PPM-B9202348', '867393'),
    ('PPM-B9310075', '867335'),
    ('PPM-B8167969', '866790'),
    ('PPM-B9170589', '864177'),
    ('PPM-B9221189', '868530'),
    ('PPM-B9208198', '868448'),
    ('PPM-B9252633', '868449'),
    ('PPM-B8872470', '868398'),
    ('PPM-B8779811', '868402')
;


-- Insert the Check entries
INSERT INTO automotive_claim_payment_checks (automotive_claim_payments_id, check_amount, check_number, paid_date, updated_at)
SELECT
    acp.id,
    ac.estimate,
    '0070690',
    '2023-11-27',
    now() at time zone 'utc'
FROM automotive_claim_payments acp
join automotive_claims ac on ac.id = acp.automotive_claim_id
join tmpClaims tc on tc.contract_number = ac.contract_number and tc.ro = ac.ro
where
    tc.ro = ac.ro
;

-- Update the Payment rows
-- Batch 
UPDATE automotive_claim_payments acp
SET
    batch_key = (select batch_key from automotive_intacct_bill_batches where batch_title = '11/27/2023' limit 1),
    bill_key = 1115550, -- This is based on looking at the payment rows with the same batch key and reviewing the bill_memo to find where the batch number 1115550 would fall under and found that there's no rows with this bill_key
    bill_memo = 'UTSFL_11-27-2023_15:59:58 #100164',
    updated_at = now() at time zone 'utc',
    intacct_bill_number = 'UTSFL_11-27-2023_15:59:58 #100164',
    is_complete = TRUE
FROM automotive_claims ac
JOIN tmpClaims tc on tc.contract_number = ac.contract_number and tc.ro = ac.ro
WHERE
    acp.automotive_claim_id = ac.id
;

-- Update Claim Status
UPDATE automotive_claims ac
SET
    status = 'CheckWritten'
FROM tmpClaims tc
WHERe
    tc.contract_number = ac.contract_number
    and tc.ro = ac.ro
;

DROP TABLE IF EXISTS tmpClaims;