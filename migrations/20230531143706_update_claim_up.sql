insert into automotive_claim_payment_checks(
    automotive_claim_payments_id,
    check_amount,
    check_number,
    paid_date,
    updated_at) values (
        (select id from automotive_claim_payments where automotive_claim_id =
            (select id from automotive_claims where contract_number='CPS4026690' and status = 'WaitingForReversed')
            and is_complete=false),8388.51,'1880','2023-05-30',now() at time zone 'utc');

update automotive_claims set status = 'CheckWritten'
    where id = (select id from automotive_claims where contract_number='CPS4026690' and status = 'WaitingForReversed');