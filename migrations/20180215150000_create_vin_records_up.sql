CREATE TABLE vin_records
(
  id         SERIAL,
  created_at TIMESTAMP WITHOUT TIME ZONE NOT NULL,
  vin        CHARACTER VARYING(255)      NOT NULL,
  year       INTEGER                     NOT NULL,
  make       CHARACTER VARYING(255)      NOT NULL,
  model      CHARACTER VARYING(255)      NOT NULL,
  CONSTRAINT vin_records_pkey PRIMARY KEY (id)
);

CREATE UNIQUE INDEX "vin_records_uidx"
  ON "vin_records" USING BTREE ("vin");

ALTER TABLE gap_claims
  ADD COLUMN vin_record_id INTEGER,
  ADD CONSTRAINT "gap_claims_vin_record_id_fkey" FOREIGN KEY ("vin_record_id") REFERENCES "vin_records" ("id") ON UPDATE CASCADE ON DELETE RESTRICT