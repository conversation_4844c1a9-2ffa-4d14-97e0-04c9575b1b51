ALTER TABLE automotive_claims
  ADD COLUMN facility_id INTEGER,
  DROP COLUMN facility_code,
  ADD CONSTRAINT automotive_claims_facility_id_fkey FOREIGN KEY (facility_id) REFERENCES automotive_facilities (id) MATCH SIMPLE ON UPDATE CASCADE ON DELETE RESTRICT;

ALTER TABLE automotive_facilities
  ADD COLUMN store_id INTEGER,
  ADD CONSTRAINT automotive_facilities_store_id_fkey FOREIGN KEY (store_id) REFERENCES stores (id) MATCH SIMPLE ON UPDATE CASCADE ON DELETE RESTRICT;

CREATE INDEX automotive_facilities_store_id_idx ON automotive_facilities USING btree (store_id);

ALTER TABLE automotive_claim_complaints ADD COLUMN is_manual boolean;