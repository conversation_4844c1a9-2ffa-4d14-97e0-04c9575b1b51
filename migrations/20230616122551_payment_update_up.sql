DO $$
DECLARE
  rec RECORD;
BEGIN
  FOR rec in select * from automotive_intacct_batch_details where automotive_intacct_batch_id = 88707
  LOOP
   update automotive_claim_payments set is_complete=true where id = rec.automotive_claim_payment_id;
   insert into automotive_claim_payment_checks(
       automotive_claim_payments_id,check_amount,check_number,paid_date,updated_at) values (
           rec.automotive_claim_payment_id,rec.amount,'0062840','2023-04-25',now() at time zone 'utc');
   update automotive_claims set status = 'CheckWritten' where id = rec.automotive_claim_id;
  END LOOP;
END $$;
