-- delete negative claim payment record
delete from automotive_claim_payments
    where automotive_claim_id = (
        select id
        from automotive_claims
        where
            contract_number = 'TCA-B10078562E'
            and RO = '113265'
            and status = 'Reversed')
        and amount = -210;

-- add note to the claim
insert into automotive_record_notes(
        automotive_claim_id,
        notes_text,
        created_by_user_id,
        created_at)
    values (
        (select id from automotive_claims
            where contract_number = 'TCA-B10078562E'
            and RO = '113265'
            and status = 'Reversed'),
        'Claim status changed from Reversed to CheckWritten from DB',
        (select id from users where email = 'SYSTEM'),
        now() at time zone 'UTC');

-- update claim status
update automotive_claims
    set status = 'CheckWritten'
    where
        contract_number = 'TCA-B10078562E'
        and RO = '113265'
        and status = 'Reversed';
