drop index gap_voided_transactions_check_number;

-- GAP-C9135051
insert into gap_voided_transactions
  (date_of_void, gap_claim_id, check_amount, check_number)
  values(now() at time zone 'utc', (select id from gap_claims where contract_number='GAP-C9135051'), -16902.65, 27253);

  insert into record_notes
  (created_at, gap_claim_id, notes_text, created_by_user_id)
values
  (now() at time zone 'utc', 
  (select id from gap_claims where contract_number='GAP-C9135051'), 
  'The check with number: 27253 Amount: 16902.65 Date: 1/13/2025 is voided', 
  (select id from users where email = 'SYSTEM'));

insert into gap_claim_payments
  (gap_claim_id, check_number, amount, paid_date, updated_at)
values
  ((select id from gap_claims where contract_number='GAP-C9135051'), 27258, 6451.46, '2025-01-13',
  now() at time zone 'utc');

--  GAP-D9441083FIFSE
insert into gap_voided_transactions
  (date_of_void, gap_claim_id, check_amount, check_number)
  values(now() at time zone 'utc', (select id from gap_claims where contract_number='GAP-D9441083FIFSE'), -16902.65, 27253);

insert into record_notes
    (created_at, gap_claim_id, notes_text, created_by_user_id)
    values
    (now() at time zone 'utc',
    (select id from gap_claims where contract_number='GAP-D9441083FIFSE'),
    'The check with number: 27253 Amount: 16902.65 Date: 1/13/2025 is voided',
    (select id from users where email = 'SYSTEM'));

insert into gap_claim_payments
    (gap_claim_id, check_number, amount, paid_date, updated_at)
    values
    ((select id from gap_claims where contract_number='GAP-D9441083FIFSE'), 27257, 10451.19, '2025-01-13',
    now() at time zone 'utc');
