alter table gap_claims
  add column book_out_requested_manager_flag boolean NOT NULL DEFAULT FALSE,
  add column run_amortization_manager_flag boolean NOT NULL DEFAULT FALSE,
  add column other_label1_manager_flag boolean NOT NULL DEFAULT FALSE,
  add column other_label2_manager_flag boolean NOT NULL DEFAULT FALSE,
  add column other_label3_manager_flag boolean NOT NULL DEFAULT FALSE,
  add column insurance_payment_check_manager_flag boolean NOT NULL DEFAULT FALSE,
  add column settlement_letter_manager_flag boolean NOT NULL DEFAULT FALSE,
  add column valuation_report_manager_flag boolean NOT NULL DEFAULT FALSE,
  add column valuation_report_base_value_manager_flag boolean NOT NULL DEFAULT FALSE,
  add column valuation_report_vin_matches_manager_flag boolean NOT NULL DEFAULT FALSE,
  add column valuation_report_prior_damage_manager_flag boolean NOT NULL DEFAULT FALSE,
  add column valuation_report_misc_fee_manager_flag boolean NOT NULL DEFAULT FALSE,
  add column valuation_report_mileage_manager_flag boolean NOT NULL DEFAULT FALSE,
  add column valuation_report_dol_manager_flag boolean NOT NULL DEFAULT FALSE,
  add column valuation_report_type_manager_flag boolean NOT NULL DEFAULT FALSE,
  add column options_book_out_match_manager_flag boolean NOT NULL DEFAULT FALSE,
  add column options_book_out_over_150_percent_manager_flag boolean NOT NULL DEFAULT FALSE,
  add column original_financing_manager_flag boolean NOT NULL DEFAULT FALSE,
  add column contract_number_matches_manager_flag boolean NOT NULL DEFAULT FALSE,
  add column bank_history_matches_manager_flag boolean NOT NULL DEFAULT FALSE,
  add column police_report_manager_flag boolean NOT NULL DEFAULT FALSE,
  add column insurance_deductible_addition_manager_flag boolean NOT NULL DEFAULT FALSE,
  add column insurance_deductible_subtraction_manager_flag boolean NOT NULL DEFAULT FALSE,
  add column bank_information_manager_flag boolean NOT NULL DEFAULT FALSE,
  add column full_loan_history_manager_flag boolean NOT NULL DEFAULT FALSE,
  add column loan_number_manager_flag boolean NOT NULL DEFAULT FALSE,
  add column payment_amount_manager_flag boolean NOT NULL DEFAULT FALSE,
  add column interest_rate_manager_flag boolean NOT NULL DEFAULT FALSE,
  add column first_payment_manager_flag boolean NOT NULL DEFAULT FALSE;