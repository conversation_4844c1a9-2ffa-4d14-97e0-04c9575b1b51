alter table automotive_claims
  DROP COLUMN ro_mileage,
  DROP COLUMN ro_opened_date,
  DROP COLUMN store_code,
  DROP COLUMN expiration_date,
  DROP COLUMN ending_miles,
  DROP COLUMN coverage,
  DROP COLUMN term,
  DROP COLUMN contract_status,
  DROP COLUMN disappearing_deductible,
  DROP COLUMN high_tech,
  DROP COLUMN seals_and_gasket,
  DROP COLUMN rental_upgrade,
  DROP COLUMN commercial_use,
  DROP COLUMN standard_powertrain_plus_option,
  DROP COLUMN smart_tech_option,
  DROP COLUMN canadian_vehicle,
  ADD COLUMN high_tech_flag boolean,
  ADD COLUMN seals_gasket_flag boolean,
  ADD COLUMN c_use_flag boolean,
  ADD COLUMN rental_flag boolean;
alter table automotive_claims rename column effective_date to effective_dates;
alter table automotive_claims rename column beginning_miles to effective_miles;