CREATE TABLE automotive_claim_payees
(
  "id"                   SERIAL,
  "automotive_claim_id"  INTEGER                                      NOT NULL,
  "name"                 VARCHAR(255) DEFAULT '' :: CHARACTER VARYING NOT NULL,
  "address"              VARCHAR(255) DEFAULT '' :: CHARAC<PERSON>R VARYING NOT NULL,
  "city"                 VARCHAR(255) DEFAULT '' :: CHARACTER VARYING NOT NULL,
  "state"                VARCHAR(255) DEFAULT '' :: CHARACTER VARYING NOT NULL,
  "postal_code"          VARCHAR(255) DEFAULT '' :: CHARACTER VARYING NOT NULL,
  "vendor_id"            VARCHAR(255) DEFAULT '' :: CHARACTER VARYING NOT NULL,
  "unidata_payee_number" SERIAL,

  CONSTRAINT "automotive_claim_payees_pkey" PRIMARY KEY ("id"),
  CONSTRAINT automotive_claims_id_fkey FOREIGN KEY (automotive_claim_id) REFERENCES automotive_claims (id) MATCH SIMPLE ON UPDATE CASCADE ON DELETE RESTRICT
);

CREATE INDEX automotive_claim_payees_automotive_claim_id_idx
  ON automotive_claim_payees
  USING BTREE (automotive_claim_id);

ALTER SEQUENCE automotive_claim_payees_unidata_payee_number_seq RESTART 1000;