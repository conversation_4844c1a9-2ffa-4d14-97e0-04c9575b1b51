create table if not exists lwt_claim_adjustments
(
    id serial,
    lwt_claim_id int not null,
    lwt_claim_payment_id int not null,
    reason varchar(255) not null,
    amount numeric not null,
    created_by_user_id int not null,
    created_at timestamp not null,

    constraint lwt_claim_adjustments_pk primary key (id),
    constraint lwt_claim_adjustments_lwt_claim_id_fk foreign key (lwt_claim_id) references lwt_claims (id) on update cascade on delete restrict,
    constraint lwt_claim_adjustments_lwt_claim_payment_id_fk foreign key (lwt_claim_payment_id) references lwt_claim_payments (id) on update cascade on delete restrict,
    constraint lwt_claim_adjustments_created_by_user_id_fk foreign key (created_by_user_id) references users (id) on update cascade on delete restrict
);
create index if not exists lwt_claim_adjustments_lwt_claim_id_idx on lwt_claim_adjustments using btree(lwt_claim_id);
create index if not exists lwt_claim_adjustments_created_at_idx on lwt_claim_adjustments using btree(created_at);
create index if not exists lwt_claim_adjustments_created_by_user_id on lwt_claim_adjustments using btree(created_by_user_id);
create index if not exists lwt_claim_adjustments_lwt_claim_payment_id on lwt_claim_adjustments using btree(lwt_claim_payment_id);