CREATE TABLE gap_claim_recoveries(
  id SERIAL,
  gap_claim_id INTEGER NOT NULL,
  allied_claim_number varchar(255) NOT NULL DEFAULT '',
  status VARCHAR(255) NOT NULL DEFAULT '',
  start_date TIMESTAMP NOT NULL,
  submitted_at TIMESTAMP,
  updated_at TIMESTAMP,
  updated_by_user_id INTEGER NOT NULL,
  insurance_claim_number VARCHAR(255) NOT NULL DEFAULT '',
  claim_type VARCHAR(255) NOT NULL DEFAULT '',
  case_comments VARCHAR(255) NOT NULL DEFAULT '',
  check_amount NUMERIC(12,2) NOT NULL DEFAULT 0.0,
  contract_balance NUMERIC(12,2) NOT NULL DEFAULT 0.0,

  CONSTRAINT gap_claim_recoveries_pkey PRIMARY KEY (id),
  CONSTRAINT gap_claim_recoveries_gap_claim_id_fkey FOREIGN KEY (gap_claim_id) REFERENCES gap_claims (id) ON UPDATE CASCADE ON DELETE RESTRICT,
  CONSTRAINT gap_claim_recoveries_updated_by_user_id_fkey FOREIGN KEY (updated_by_user_id) REFERENCES users (id) ON UPDATE CASCADE ON DELETE RESTRICT
);
CREATE INDEX gap_claim_recoveries_updated_by_user_id_idx ON gap_claim_recoveries USING btree (updated_by_user_id);

ALTER TABLE gap_claims
  DROP COLUMN allied_claim_number,
  DROP COLUMN recovery_status,
  DROP COLUMN recovery_start_date,
  DROP COLUMN recovery_submitted_at,
  DROP COLUMN recovery_updated_at;
