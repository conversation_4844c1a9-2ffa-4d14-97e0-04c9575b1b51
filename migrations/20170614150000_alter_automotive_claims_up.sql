CREATE TABLE "automotive_claim_updates" (
  "id" serial NOT NULL,
  "automotive_claim_id" integer NOT NULL,
  "updated_by_user_id" integer NOT NULL,
  "updated_at" timestamp NOT NULL,

  CONSTRAINT "automotive_claim_updates_pkey" PRIMARY KEY ("id"),
  CONSTRAINT "automotive_claim_updates_automotive_claim_id_fkey" FOREIGN KEY ("automotive_claim_id") REFERENCES "automotive_claims" ("id") ON UPDATE CASCADE ON DELETE RESTRICT,
  CONSTRAINT "automotive_claim_updates_updated_by_user_id_fkey" FOREIGN KEY ("updated_by_user_id") REFERENCES "users" ("id") ON UPDATE CASCADE ON DELETE RESTRICT
);

CREATE INDEX automotive_claim_updates_automotive_claim_id_idx ON automotive_claim_updates USING btree (automotive_claim_id);
CREATE INDEX automotive_claim_updates_updated_at_idx on automotive_claim_updates USING btree (updated_at);
CREATE INDEX automotive_claim_updates_updated_by_user_id_idx on automotive_claim_updates USING btree (updated_by_user_id);

CREATE TABLE "automotive_record_notes" (
  "id" serial NOT NULL,
  "is_manual" bool NOT NULL DEFAULT false,
  "automotive_claim_id" integer NOT NULL,
  "notes_text" varchar(255) NOT NULL,
  "created_by_user_id" integer NOT NULL,
  "created_at" timestamp NOT NULL,

  CONSTRAINT "automotive_record_notes_pkey" PRIMARY KEY ("id"),
  CONSTRAINT "automotive_claim_id_fkey" FOREIGN KEY ("automotive_claim_id") REFERENCES "automotive_claims" ("id") ON UPDATE CASCADE ON DELETE RESTRICT,
  CONSTRAINT "automotive_record_notes_created_by_user_id_fkey" FOREIGN KEY ("created_by_user_id") REFERENCES "users" ("id") ON UPDATE CASCADE ON DELETE RESTRICT
);

CREATE INDEX automotive_record_notes_created_by_user_id_idx on automotive_record_notes USING btree (created_by_user_id);
CREATE INDEX automotive_record_notes_created_at_idx on automotive_record_notes USING btree (created_at);
CREATE INDEX automotive_record_notes_automotive_claim_id_idx ON automotive_record_notes USING btree (automotive_claim_id);