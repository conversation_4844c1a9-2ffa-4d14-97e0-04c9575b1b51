/*
    Information for batch 118120:
        - <PERSON> and Intacct Bill Number: AZPNX_10-25-2024_15:08:25 #118120
        - Check Number: 0081709
        - Paid Date: 10/25/2024
        - Intacct Batch ID: 118120
*/

-- Insert Check data into the automotive_claim_payment_checks table for batch 118120
insert into automotive_claim_payment_checks 
    (automotive_claim_payments_id, check_amount, check_number, paid_date, updated_at)
select
	aibd.automotive_claim_payment_id,
	aibd.amount as check_amount,
	'0081709' as check_number,
	'2024-10-25' as paid_date,
	now() at time zone 'utc' as updated_at
from automotive_intacct_batch_details aibd 
where
	aibd.automotive_intacct_batch_id = 118120
;

-- Update Payment rows in the automotive_claim_payments table for batch 118120
update automotive_claim_payments acp
set
    batch_key = aibb.batch_key,
    bill_memo = 'AZPNX_10-25-2024_15:08:25 #118120',
    updated_at = now() at time zone 'utc',
    intacct_bill_number = 'AZPNX_10-25-2024_15:08:25 #118120',
    is_complete = true
from automotive_intacct_batch_details aibd
cross join automotive_intacct_bill_batches aibb
where
    aibd.automotive_claim_payment_id = acp.id
	and aibd.automotive_intacct_batch_id = 118120
	and aibb.batch_title = '10/25/2024'
;

-- Update Claim Status for batch 118120 to CheckWritten
update automotive_claims ac
set status = 'CheckWritten'
from automotive_intacct_batch_details aibd
where
    aibd.automotive_claim_id = ac.id
	and aibd.automotive_intacct_batch_id = 118120
;