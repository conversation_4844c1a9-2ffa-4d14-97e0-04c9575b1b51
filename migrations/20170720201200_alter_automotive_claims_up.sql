alter table automotive_claims
  ADD COLUMN store_code VARCHAR(255),
  ADD COLUMN ro_opened_date VARCHAR(255),
  ADD COLUMN ro_mileage INTEGER NOT NULL DEFAULT 0,
  ADD COLUMN expiration_date VARCHAR(255),
  ADD COLUMN ending_miles VARCHAR(255),
  ADD COLUMN coverage VARCHAR(255),
  ADD COLUMN term VARCHAR(255),
  ADD COLUMN contract_status VARCHAR(255),
  ADD COLUMN disappearing_deductible boolean NOT NULL DEFAULT FALSE,
  ADD COLUMN high_tech boolean NOT NULL DEFAULT FALSE,
  ADD COLUMN seals_and_gasket boolean NOT NULL DEFAULT FALSE,
  ADD COLUMN rental_upgrade boolean NOT NULL DEFAULT FALSE,
  ADD COLUMN commercial_use boolean NOT NULL DEFAULT FALSE,
  ADD COLUMN standard_powertrain_plus_option boolean NOT NULL DEFAULT FALSE,
  ADD COLUMN smart_tech_option boolean NOT NULL DEFAULT FALSE,
  ADD COLUMN canadian_vehicle boolean NOT NULL DEFAULT FALSE,
  DROP COLUMN high_tech_flag,
  DROP COLUMN seals_gasket_flag,
  DROP COLUMN c_use_flag,
  DROP COLUMN rental_flag;
alter table automotive_claims rename column effective_dates to effective_date;
alter table automotive_claims rename column effective_miles to beginning_miles;

