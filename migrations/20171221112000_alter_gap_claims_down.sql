ALTER TABLE gap_claims
  DROP COLUMN has_recovery,
  DROP COLUMN recovery_refund,
  DROP COLUMN recovery_status,
  DROP COLUMN insurance_company,
  DROP COLUMN has_insurance_company,
  DROP COLUMN policy_number,
  DROP COLUMN has_policy_number,
  DROP COLUMN mileage_deduction,
  DROP COLUMN has_mileage_deduction,
  DROP COLUMN nada,
  DROP COLUMN has_nada,
  DROP COLUMN has_valuation_nada_difference,
  DROP COLUMN mileage_deduction_manager_flag;

DROP TABLE gap_insurance_companies;