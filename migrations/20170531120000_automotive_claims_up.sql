alter table customers
  add column cell_number varchar(255),
  add column best_contact_method varchar(255);

/*CREATE UNIQUE INDEX  "customers_email_uidx" ON "customers" USING btree("email_address");*/

CREATE TABLE stores(
  id serial,
  created_at timestamp without time zone NOT NULL,
  updated_at timestamp without time zone NOT NULL,
  company_id integer NOT NULL,
  name varchar(255),
  code varchar(255),
  address varchar(255),
  city varchar(255),
  state_code varchar(255),
  postal_code varchar(255),
  phone varchar(255),
  fax varchar(255),
  email varchar(255),
  CONSTRAINT stores_pkey PRIMARY KEY (id),
  CONSTRAINT stores_company_id_fkey FOREIGN KEY (company_id) REFERENCES companies (id) MATCH SIMPLE ON UPDATE CASCADE ON DELETE RESTRICT
);

CREATE TABLE automotive_claims (
  id serial,
  vin varchar(255) NOT NULL,
  contract_number varchar(255) NOT NULL,
  status varchar(255) NOT NULL,
  customer_id integer NOT NULL,
  date_of_claim_received date NOT NULL,
  estimate numeric(12, 2) NOT NULL,
  created_by_user_id integer NOT NULL,
  store_id integer NOT NULL,
  owner_id integer NOT NULL,
  make varchar(255) NOT NULL,
  model varchar(255) NOT NULL,
  year varchar(255) NOT NULL,
  effective_dates varchar(255),
  effective_miles varchar(255) NOT NULL,
  high_tech_flag boolean,
  seals_gasket_flag boolean,
  c_use_flag boolean,
  rental_flag boolean,
  deductible numeric(12,2) NOT NULL,
  maintenance varchar(255),
  ro varchar(255),
  mileage varchar(255),
  advisor varchar(255),
  labor_rate varchar(255),
  tax_parts numeric(12,2),
  tax_labor numeric(12,2),

  CONSTRAINT automotive_claims_pkey PRIMARY KEY(id),
  CONSTRAINT store_id_fkey FOREIGN KEY (store_id) REFERENCES stores (id) MATCH SIMPLE ON UPDATE CASCADE ON DELETE RESTRICT,
  CONSTRAINT customer_id_fkey FOREIGN KEY (customer_id) REFERENCES customers (id) MATCH SIMPLE ON UPDATE CASCADE ON DELETE RESTRICT
);
CREATE INDEX "automotive_claims_store_id_idx" ON "automotive_claims" USING btree("store_id");
CREATE INDEX "automotive_claims_customer_id_idx" ON "automotive_claims" USING btree("customer_id");

CREATE TABLE automotive_claim_complaints (
  id serial,
  complaint_date timestamp,
  automotive_claim_id integer,
  complaint varchar(255),
  cause varchar(255),
  correction varchar(255),
  repair_code varchar(255),
  add_line_flag boolean,
  goodwill_flag boolean,
  status varchar(255),
  parts_total numeric(12,2),
  labor_total numeric(12,2),
  towing numeric(12,2),
  rental numeric(12,2),
  sublet numeric(12,2),

  CONSTRAINT automotive_claim_complaints_pkey PRIMARY KEY(id),
  CONSTRAINT automotive_claims_id_fkey FOREIGN KEY (automotive_claim_id) REFERENCES automotive_claims (id) MATCH SIMPLE ON UPDATE CASCADE ON DELETE RESTRICT
);
CREATE INDEX "automotive_claim_complaints_automotive_claim_id_idx" ON "automotive_claim_complaints" USING btree("automotive_claim_id");

CREATE TABLE automotive_claim_complaint_parts (
  id serial,
  automotive_claim_complaint_id integer,
  part_number varchar(255),
  description varchar(255),
  quantity integer,
  cost numeric(12,2),
  requested numeric(12,2),
  approved numeric(12,2),

  CONSTRAINT automotive_claim_complaint_parts_pkey PRIMARY KEY(id),
  CONSTRAINT automotive_claim_complaint_parts_complaint_id_fkey FOREIGN KEY (automotive_claim_complaint_id) REFERENCES automotive_claim_complaint_parts (id) MATCH SIMPLE
  ON UPDATE CASCADE ON DELETE RESTRICT
);
CREATE INDEX "automotive_claim_complaint_parts_automotive_claim_complaint_id_idx" ON "automotive_claim_complaint_parts" USING btree("automotive_claim_complaint_id");

CREATE TABLE automotive_claim_complaint_labors (
  id serial,
  automotive_claim_complaint_id integer,
  labor_description varchar(255),
  requested varchar(255),
  hours numeric(12,2),
  rate numeric(12,2),
  billed numeric(12,2),
  approved numeric(12,2),

  CONSTRAINT automotive_claim_complaint_labors_pkey PRIMARY KEY (id),
  CONSTRAINT automotive_claim_complaint_labors_complaint_id_fkey FOREIGN KEY (automotive_claim_complaint_id) REFERENCES automotive_claim_complaint_parts (id) MATCH SIMPLE
  ON UPDATE CASCADE ON DELETE RESTRICT
);

CREATE INDEX "automotive_claim_complaint_labors_automotive_claim_complaint_id_idx" ON "automotive_claim_complaint_labors" USING btree("automotive_claim_complaint_id");
