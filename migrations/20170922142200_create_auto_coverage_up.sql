CREATE TABLE "automotive_claim_coverage" (
  "id" serial NOT NULL,
  "automotive_claim_id" integer NOT NULL,
  "disappearing_deductible" boolean,
  "high_tech" boolean,
  "seals_and_gasket" boolean,
  "rental_upgrade" boolean,
  "commercial_use" INTEGER,
  "standard_powertrain_plus_option" boolean,
  "smart_tech_option" boolean,
  "canadian_vehicle" boolean,
  "paint" boolean,
  "fabric" boolean,
  "leather_or_vinyl" boolean,
  "dent_and_ding" boolean,
  "key_count" INTEGER,
  CONSTRAINT "automotive_claim_coverage_pkey" PRIMARY KEY ("id"),
  CONSTRAINT "automotive_claim_coverage_automotive_claim_id_fkey" FOREIGN KEY ("automotive_claim_id") REFERENCES "automotive_claims" ("id") ON UPDATE CASCADE ON DELETE RESTRICT
);

CREATE INDEX automotive_claim_coverage_automotive_claim_id_idx ON automotive_claim_coverage USING btree (automotive_claim_id);

alter table automotive_claims
  DROP COLUMN disappearing_deductible,
  DROP COLUMN high_tech,
  DROP COLUMN seals_and_gasket,
  DROP COLUMN rental_upgrade,
  DROP COLUMN commercial_use,
  DROP COLUMN standard_powertrain_plus_option,
  DROP COLUMN smart_tech_option,
  DROP COLUMN canadian_vehicle,
  DROP COLUMN key_count;