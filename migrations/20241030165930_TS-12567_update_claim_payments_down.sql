/*
    Previous Information for batch 118120:
        - Intacct Batch ID: 118120
        - Previous Status: WaitingF<PERSON><PERSON><PERSON><PERSON>
*/

-- Revert the Claim Status for batch 118120 back to WaitingForCheck
update automotive_claims ac
set status = 'WaitingForCheck'
from automotive_intacct_batch_details aibd
where
    aibd.automotive_claim_id = ac.id
	and aibd.automotive_intacct_batch_id = 118120
;

-- Revert the Payment rows back to original state for batch 118120
update automotive_claim_payments acp
set
    batch_key = null,
    bill_key = null,
    bill_memo = '',
    intacct_bill_number = '',
    is_complete = false,
    updated_at = now() at time zone 'utc'
from automotive_intacct_batch_details aibd
where
    aibd.automotive_claim_payment_id = acp.id
    and aibd.automotive_intacct_batch_id = 118120
;

-- Remove Check data from the automotive_claim_payment_checks table for batch 118120
delete from automotive_claim_payment_checks
where
    automotive_claim_payment_id in (
        select
            automotive_claim_payment_id
        from automotive_intacct_batch_details
        where
            automotive_intacct_batch_id = 118120
    )
;
