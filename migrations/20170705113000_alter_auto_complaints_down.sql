ALTER TABLE automotive_claim_complaint_parts
  DROP COLUMN notes,
  ALTER quantity DROP DEFAULT,
  ALTER cost DROP DEFAULT,
  ALTER requested DROP DEFAULT,
  ALTER approved DROP DEFAULT,
  DROP CONSTRAINT automotive_claim_complaint_parts_complaint_id_fkey,
  ADD CONSTRAINT automotive_claim_complaint_parts_complaint_id_fkey FOREIGN KEY (automotive_claim_complaint_id) REFERENCES automotive_claim_complaint_parts (id) MATCH SIMPLE
  ON UPDATE CASCADE ON DELETE RESTRICT;
ALTER TABLE automotive_claim_complaint_labors
  DROP COLUMN notes,
  ALTER requested DROP DEFAULT,
  ALTER hours DROP DEFAULT,
  ALTER rate DROP DEFAULT,
  ALTER billed DROP DEFAULT,
  ALTER approved DROP DEFAULT,
  DROP CONSTRAINT automotive_claim_complaint_labors_complaint_id_fkey,
  ADD CONSTRAINT automotive_claim_complaint_labors_complaint_id_fkey FOREIGN KEY (automotive_claim_complaint_id) REFERENCES automotive_claim_complaint_parts (id) MATCH SIMPLE
  ON UPDATE CASCADE ON DELETE RESTRICT;
ALTER TABLE automotive_claim_complaints
  ALTER add_line_flag DROP DEFAULT,
  ALTER goodwill_flag DROP DEFAULT,
  ALTER parts_total DROP DEFAULT,
  ALTER labor_total DROP DEFAULT,
  ALTER towing DROP DEFAULT,
  ALTER rental DROP DEFAULT,
  ALTER sublet DROP DEFAULT;
