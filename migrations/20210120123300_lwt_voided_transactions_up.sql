create table lwt_voided_transactions (
  id  serial not null,
  lwt_claim_id integer not null,
  check_number varchar(255) not null,
  date_of_void date not null,
  check_amount numeric(12,2) not null,
  CONSTRAINT lwt_voided_transactions_pkey primary key (id),
  CONSTRAINT lwt_voided_transactions_lwt_claim_id_fkey foreign key (lwt_claim_id) references lwt_claims (id) on update cascade on delete restrict
);
create index lwt_transactions_lwt_claim_id_idx on lwt_voided_transactions (lwt_claim_id);

