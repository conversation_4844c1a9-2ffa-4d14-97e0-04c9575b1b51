alter table vta_claim_payments
    add column voided_at timestamp with time zone,
    add column voided_by_user_id integer,
    add constraint vta_claim_payments_voided_by_user_id_fkey foreign key (voided_by_user_id)
        references users (id) on update cascade on delete restrict;
    
create index vta_claim_payments_voided_by_user_id_idx on vta_claim_payments using btree(voided_by_user_id);

update vta_claim_payments
		set voided_at = now() at time zone 'utc',
		voided_by_user_id = (select id from users where first_name = 'SYSTEM')
		where id = (
			select id
			from vta_claim_payments
			where vta_claim_id = (select id from vta_claims where contract_number = 'TAU8300762')
			order by id desc
			limit 1);