ALTER TABLE gap_claims
  ADD COLUMN parent_claim_id INTEGER,
  ADD CONSTRAINT gap_claims_parent_claim_id_fkey FOREIGN KEY (parent_claim_id)
  REFERENCES gap_claims (id) MATCH SIMPLE ON UPDATE CASCADE ON DELETE RESTRICT;


DO $$
DECLARE
  rec RECORD;
BEGIN
  FOR rec in SELECT gap_claims.id, gap_claims.child_claim_id FROM gap_claims WHERE has_child_claim = true
  LOOP
    UPDATE gap_claims SET parent_claim_id = rec.id WHERE id = rec.child_claim_id;
  END LOOP;
END $$;

ALTER TABLE gap_claims
    DROP COLUMN child_claim_id;