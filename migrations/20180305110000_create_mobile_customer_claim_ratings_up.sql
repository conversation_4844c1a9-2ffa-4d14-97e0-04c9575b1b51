CREATE TABLE customer_claim_ratings (
  id              SERIAL,
  customer_id     INTEGER,
  contract_number VARCHAR(255) NOT NULL,
  ro              VARCHAR(255) NOT NULL,
  rating          INTEGER      NOT NULL DEFAULT 0,
  feedback        VARCHAR(255) NOT NULL DEFAULT '',
  created_at      TIMESTAMP    NOT NULL,
  updated_at      TIMESTAMP    NOT NULL,

  CONSTRAINT "customer_claim_ratings_pkey" PRIMARY KEY ("id"),
  CONSTRAINT "customer_claim_ratings_customer_id_fkey" FOREIGN KEY ("customer_id")
  REFERENCES "customers" ("id") ON UPDATE CASCADE ON DELETE RESTRICT
);
CREATE INDEX "customer_claim_ratings_customer_idx"
  ON "customer_claim_ratings" USING BTREE ("customer_id");
CREATE INDEX customer_claim_ratings_contract_number_idx
  ON "customer_claim_ratings" USING BTREE (contract_number);
CREATE INDEX customer_claim_ratings_ro_idx
  ON "customer_claim_ratings" USING BTREE (ro);
CREATE UNIQUE INDEX "customer_claim_ratings_contract_number_ro_udx"
  ON "customer_claim_ratings" USING BTREE (contract_number, ro);