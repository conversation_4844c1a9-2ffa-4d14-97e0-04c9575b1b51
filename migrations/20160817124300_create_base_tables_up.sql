CREATE TABLE "companies" (
	"id" serial,
	"created_at" timestamp NOT NULL,
	"updated_at" timestamp NOT NULL,
	"name" varchar(255) NOT NULL,
	"code" varchar(255) NOT NULL,
	CONSTRAINT "companies_pkey" PRIMARY KEY ("id")
);
CREATE UNIQUE INDEX  "companies_code_uidx" ON "companies" USING btree("code");

CREATE TABLE "users" (
	"id" serial,
	"created_at" timestamp NOT NULL,
	"updated_at" timestamp NOT NULL,
	"company_id" integer NOT NULL,
	"email" varchar(255) NOT NULL DEFAULT '',
	"first_name" varchar(255) NOT NULL DEFAULT '',
	"last_name" varchar(255) NOT NULL DEFAULT '',
	"active" bool NOT NULL DEFAULT true,
	"roles" hstore,
	"password_digest" varchar(255) NOT NULL DEFAULT '',
	"reset_password_token" varchar(255) NOT NULL DEFAULT '',
	"reset_password_sent_at" timestamp NULL,
	"confirmation_token" varchar(255) NOT NULL DEFAULT '',
	"confirmation_token_sent_at" timestamp NULL,
	"confirmed_at" timestamp NULL,
	CONSTRAINT "users_pkey" PRIMARY KEY ("id"),
	CONSTRAINT "users_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "companies" ("id") ON UPDATE CASCADE ON DELETE RESTRICT
);
CREATE INDEX "users_company_id_idx" ON "users" USING btree("company_id");
CREATE UNIQUE INDEX  "users_email_uidx" ON "users" USING btree("email");

CREATE TABLE "customers" (
	"id" serial,
	"first_name" varchar(255) NOT NULL,
	"last_name" varchar(255) NOT NULL,
	"email_address" varchar(255),
	"phone_number" varchar(255),
	"street_address" varchar(255),
	"city" varchar(255),
	"state" varchar(255) NOT NULL,
	"postal_code" varchar(255),

	CONSTRAINT "customers_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "banks" (
	"id" serial,
	"bank_account_name" varchar(255) NOT NULL,
	"bank_address_street1" varchar(255) NOT NULL,
	"bank_address_street2" varchar(255) NOT NULL,
	"bank_address_city" varchar(255) NOT NULL,
	"bank_address_state" varchar(255) NOT NULL,
	"bank_address_zip" varchar(255) NOT NULL,

	CONSTRAINT "banks_pkey" PRIMARY KEY ("id")
);
CREATE UNIQUE INDEX "banks_name_zip_uidx" ON "banks" USING btree("bank_account_name","bank_address_zip");

CREATE TABLE "gap_claims" (
	"id" serial,
	"vin" varchar(255) NOT NULL,
	"contract_number" varchar(255) NOT NULL,
	"status" varchar(255) NOT NULL,
	"status_change_description" varchar(255) NOT NULL,
	"date_of_loss" date NOT NULL,
	"date_of_claim_received" date NOT NULL,
	"date_of_last_in" date NOT NULL,
	"date_of_last_out" date NOT NULL,
	"waiting_for" varchar(255) NOT NULL,
	"last_message" varchar(255) NOT NULL,
	"customer_id" integer NOT NULL,
	"case_reserve" numeric(12, 2) NOT NULL,
	"created_by_user_id" integer NOT NULL,
	"finance_manager" varchar(255),

	"has_requested_book_out" boolean NOT NULL DEFAULT FALSE,
	"book_out_requested_date" date,
	"has_run_amortization_sheet" boolean NOT NULL DEFAULT FALSE,
	"run_amortization_sheet_value" numeric(12,2),
	"has_canceled_contracts" boolean NOT NULL DEFAULT FALSE,
	"has_canceled_service_contract" boolean NOT NULL DEFAULT FALSE,
	"canceled_service_contract_value" numeric(12, 2),
	"has_canceled_maintenance_contract" boolean NOT NULL DEFAULT FALSE,
	"canceled_maintenance_contract_value" numeric(12,2),
	"has_canceled_gap_refund_contract" boolean NOT NULL DEFAULT FALSE,
	"gap_refund_contract_value" numeric(12, 2),
	"other_label1" varchar(255),
	"other_value1" numeric(12,2),
	"other_label2" varchar(255),
	"other_value2" numeric(12,2),
	"other_label3" varchar(255),
	"other_value3" numeric(12,2),
	"has_insurance_payment_check" boolean NOT NULL DEFAULT FALSE,
	"insurance_payment_check_value" numeric(12,2),
	"has_settlement_letter" boolean NOT NULL DEFAULT FALSE,
	"settlement_letter_value" numeric(12, 2) NOT NULL,
	"is_valuation_report_available" boolean NOT NULL DEFAULT FALSE,
	"valuation_report_adjustments" varchar(255),
	"is_valuation_report_matches_base_value" boolean NOT NULL DEFAULT FALSE,
	"valuation_report_matches_base_value" numeric(12, 2) NOT NULL,
	"valuation_report_vin_matches" boolean NOT NULL DEFAULT FALSE,
	"has_valuation_report_prior_damage" boolean NOT NULL DEFAULT FALSE,
	"valuation_report_prior_damage_value" numeric(12, 2),
	"has_valuation_report_misc_fee" boolean NOT NULL DEFAULT FALSE,
	"valuation_report_misc_fee_value" numeric(12,2),
	"valuation_report_mileage" integer NOT NULL,
	"valuation_report_type" varchar(255),
	"has_options_match_book_out" boolean NOT NULL DEFAULT FALSE,
	"options_match_book_out_mismatches" varchar(255),
	"has_options_match_book_out_over_150_percent" boolean NOT NULL DEFAULT FALSE,
	"options_match_book_out_over_150_percent_show_calculation" varchar(255),
	"has_original_financing_contract" boolean NOT NULL DEFAULT FALSE,
	"original_financing_contract_value" numeric(12, 2) NOT NULL,
	"contract_number_matches" boolean NOT NULL DEFAULT FALSE,
	"bank_history_matches" boolean NOT NULL DEFAULT FALSE,
	"is_police_report_available" boolean NOT NULL DEFAULT FALSE,
	"police_report_issues" varchar(255),
	"police_report_is_insured_at_fault" boolean NOT NULL DEFAULT FALSE,
	"has_insurance_policy_deductible" boolean NOT NULL DEFAULT FALSE,
	"insurance_policy_deductible_value" numeric(12, 2) NOT NULL,
	"has_bank_information" boolean NOT NULL DEFAULT FALSE,
	"is_full_loan_history_available" boolean NOT NULL DEFAULT FALSE,
	"payment_amount" numeric(12,2),
	"interest_rate" numeric(4,2),
	"first_payment_date" date,
	"has_new_bank_information" boolean NOT NULL DEFAULT FALSE,
	"bank_id" integer NOT NULL,
	"bank_account_number" varchar(255) NOT NULL,
	"last_action" varchar(255) NOT NULL,
	"owner_id" integer NOT NULL,
	"has_child_claim" boolean NOT NULL DEFAULT FALSE,
	"is_child" boolean NOT NULL DEFAULT FALSE,
	"child_claim_reason" VARCHAR(255) DEFAULT 'Child claim reason',
	"child_claim_id" integer NOT NULL DEFAULT 0,

	CONSTRAINT "gap_claims_pkey" PRIMARY KEY ("id"),
	CONSTRAINT "gap_claims_created_by_user_id_fkey" FOREIGN KEY ("created_by_user_id") REFERENCES "users" ("id") ON UPDATE CASCADE ON DELETE RESTRICT,
	CONSTRAINT "gap_claims_owner_id_fkey" FOREIGN KEY ("owner_id") REFERENCES "users" ("id") ON UPDATE CASCADE ON DELETE RESTRICT,
	CONSTRAINT "gap_claims_customer_id_fkey" FOREIGN KEY ("customer_id") REFERENCES "customers" ("id") ON UPDATE CASCADE ON DELETE RESTRICT,
	CONSTRAINT "gap_claims_bank_id_fkey" FOREIGN KEY ("bank_id") REFERENCES "banks" ("id") ON UPDATE CASCADE ON DELETE RESTRICT
);

CREATE TABLE "gap_claim_updates" (
	"id" serial NOT NULL,
	"gap_claim_id" integer NOT NULL,
	"updated_by_user_id" integer NOT NULL,
	"updated_at" timestamp NOT NULL,

	CONSTRAINT "gap_claim_updates_pkey" PRIMARY KEY ("id"),
	CONSTRAINT "gap_claim_updates_gap_claim_id_fkey" FOREIGN KEY ("gap_claim_id") REFERENCES "gap_claims" ("id") ON UPDATE CASCADE ON DELETE RESTRICT,
	CONSTRAINT "gap_claim_updates_updated_by_user_id_fkey" FOREIGN KEY ("updated_by_user_id") REFERENCES "users" ("id") ON UPDATE CASCADE ON DELETE RESTRICT
);

CREATE TABLE "record_notes" (
	"id" serial NOT NULL,
	"gap_claim_id" integer NOT NULL,
	"notes_text" varchar(255) NOT NULL,
	"created_by_user_id" integer NOT NULL,
	"created_at" timestamp NOT NULL,
	CONSTRAINT "record_notes_pkey" PRIMARY KEY ("id"),
	CONSTRAINT "gap_claim_id_fkey" FOREIGN KEY ("gap_claim_id") REFERENCES "gap_claims" ("id") ON UPDATE CASCADE ON DELETE RESTRICT,
	CONSTRAINT "record_notes_created_by_user_id_fkey" FOREIGN KEY ("created_by_user_id") REFERENCES "users" ("id") ON UPDATE CASCADE ON DELETE RESTRICT
);

CREATE TABLE "intacct_bill_batches" (
	"id" serial NOT NULL,
	"batch_title" varchar(255) NOT NULL,
	"batch_key" integer NOT NULL,
	CONSTRAINT "intacct_bill_batches_pkey" PRIMARY KEY ("id")
);
CREATE UNIQUE INDEX "intacct_bill_batches_batch_title_uidx" ON "intacct_bill_batches" USING btree("batch_title");
CREATE UNIQUE INDEX "intacct_bill_batches_batch_key_uidx" ON "intacct_bill_batches" USING btree("batch_key");


CREATE TABLE "gap_claim_payments" (
	"id" serial NOT NULL,
	"authorization_number" serial NOT NULL,
	"gap_claim_id" integer NOT NULL,
	"check_number" integer NOT NULL DEFAULT 0,
	"amount" numeric(12, 2),
	"paid_date" date,
	"batch_key" integer,
	"bill_key" integer NOT NULL DEFAULT 0,
	"payment_request_key" integer NOT NULL DEFAULT 0,
	CONSTRAINT "gap_claim_payment_pkey" PRIMARY KEY ("id"),
	CONSTRAINT "gap_claim_id_fkey" FOREIGN KEY ("gap_claim_id") REFERENCES "gap_claims" ("id") ON UPDATE CASCADE ON DELETE RESTRICT,
	CONSTRAINT "gap_claim_batch_key_fkey" FOREIGN KEY ("batch_key") REFERENCES "intacct_bill_batches" ("batch_key") ON UPDATE CASCADE ON DELETE RESTRICT
);

CREATE TABLE "intacct_request_response" (
	"id" serial NOT NULL,
	"intacct_request_xml" varchar(4096) NOT NULL,
	"intacct_response_xml" varchar(4096) NOT NULL,
	"logged_at" timestamp NOT NULL,
	CONSTRAINT "intacct_request_response_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "email_templates" (
	"id" serial NOT NULL,
	"name" varchar(256) NOT NULL,
	"template_text" varchar(4096) NOT NULL,

	CONSTRAINT "email_templates_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "email_template_updates" (
	"id" serial NOT NULL,
	"email_template_id" integer NOT NULL,
	"updated_by_user_id" integer NOT NULL,
	"updated_at" timestamp NOT NULL,

	CONSTRAINT "email_template_updates_pkey" PRIMARY KEY ("id"),
	CONSTRAINT "email_template_updates_email_template_id_fkey" FOREIGN KEY ("email_template_id") REFERENCES "email_templates" ("id") ON UPDATE CASCADE ON DELETE RESTRICT,
	CONSTRAINT "email_template_updates_updated_by_user_id_fkey" FOREIGN KEY ("updated_by_user_id") REFERENCES "users" ("id") ON UPDATE CASCADE ON DELETE RESTRICT
);
