ALTER TABLE email_templates ADD COLUMN position INTEGER;

DO $$
DECLARE
  rec RECORD;
BEGIN
  FOR rec in select id from email_templates where  template_type = 'GAP' and deleted_by_user_id is null ORDER BY id ASC
  LOOP
   update email_templates
    set position = t1.pos
    FROM (
           select case when max(position) is null then 1 else max(position) + 1 end as pos  from email_templates
           where  template_type = 'GAP' and deleted_by_user_id is null
         ) t1
    where  id = rec.id;
  END LOOP;
END $$;
