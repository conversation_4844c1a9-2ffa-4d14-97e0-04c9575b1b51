alter table gap_claim_documents
  add column field_id integer,
  add column contract_number varchar(255),
  add column contract_code varchar(255);

CREATE INDEX gap_claim_documents_gap_claim_id_idx ON gap_claim_documents USING btree (gap_claim_id);
CREATE INDEX gap_claim_documents_created_by_user_id_idx ON gap_claim_documents USING btree (created_by_user_id);
CREATE INDEX gap_claim_documents_field_id_idx ON gap_claim_documents USING btree (field_id);
CREATE INDEX gap_claim_documents_contract_number_idx ON gap_claim_documents USING btree (contract_number);
CREATE INDEX gap_claim_documents_contract_code_idx ON gap_claim_documents USING btree (contract_code);

