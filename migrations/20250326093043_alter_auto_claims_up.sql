alter table automotive_claims
    add column payment_adjustment numeric(12,2) not null default 0.0,
    add column actual_paid_amount numeric(12,2) not null default 0.0;

update automotive_claims
	set actual_paid_amount = r2.total
	from
		(with r as
		(select ac.id,
		case when (sum(coalesce(acc.parts_total,0)) + sum(coalesce(acc.labor_total,0))) > 0 then
			sum(coalesce(acc.parts_total,0)) + sum(coalesce(acc.labor_total,0))
		else (sum(coalesce(ac.total_parts,0)) + sum(coalesce(ac.total_labor,0)))
		end parts_labor_total,
		ac.total_tax,
		ac.deductible,
		sum(coalesce(acc.towings_total,0)) towings_total,
		sum(coalesce(acc.rentals_total,0)) rentals_total,
		sum(coalesce(acc.sublets_total,0)) sublets_total,
		sum(coalesce(acc.miscs_total,0)) miscs_total
	from automotive_claims ac
		left join automotive_claim_complaints acc
		on (ac.id = acc.automotive_claim_id and acc.status = 'Payable')
	where ac.status in ('CCPaid','CheckWritten','WaitingForCheck','Approved') and ac.chargeback = false
	group by ac.id, ac.contract_number, ac.total_tax, ac.deductible)
	select r.id, (r.parts_labor_total + r.towings_total + r.rentals_total + r.sublets_total + r.miscs_total + r.total_tax - r.deductible) as total from r) as r2
	where automotive_claims.id = r2.id
