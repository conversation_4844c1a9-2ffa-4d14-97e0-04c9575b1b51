-- Insert Check Entry
INSERT INTO automotive_claim_payment_checks (automotive_claim_payments_id, check_amount, check_number, paid_date, updated_at)
SELECT
    acp.id,
    -105.00,
    '201417',
    '2023-11-27',
    now() at time zone 'utc'
FROM automotive_claim_payments acp
join automotive_claims ac on ac.id = acp.automotive_claim_id
where
    (
        (ac.contract_number = 'HME-B9187627E' and ac.ro = '3426332' )
        or (ac.contract_number = 'TCA-A3959766' and ac.ro = '3426333')
    )
    and ac.facility_name = 'C&K AUTO PARTS CC'
    and acp.bill_memo = 'HME-B9187627E TAVIZON,LUIS PAID WRONG FACILITY'
;

update automotive_claim_payments acp
set is_complete = TRUE
from automotive_claims ac
where
    (
        (ac.contract_number = 'HME-B9187627E' and ac.ro = '3426332' )
        or (ac.contract_number = 'TCA-A3959766' and ac.ro = '3426333')
    )
    and ac.facility_name = 'C&K AUTO PARTS CC'
    and acp.bill_memo = 'HME-B9187627E TAVIZON,LUIS PAID WRONG FACILITY'
;


-- Update Claim Status
update automotive_claims
set status = 'CheckWritten'
where 
    (
        (contract_number = 'HME-B9187627E' and ro = '3426332' )
        or (contract_number = 'TCA-A3959766' and ro = '3426333')
    )
    and facility_name = 'C&K AUTO PARTS CC'
;
