ALTER TABLE automotive_claim_complaint_parts
  ADD COLUMN notes varchar(255),
  ALTER quantity SET DEFAULT 0,
  ALTER cost SET DEFAULT 0.0,
  ALTER requested SET DEFAULT 0.0,
  ALTER approved SET DEFAULT 0.0,
  DROP CONSTRAINT automotive_claim_complaint_parts_complaint_id_fkey,
  ADD CONSTRAINT automotive_claim_complaint_parts_complaint_id_fkey FOREIGN KEY (automotive_claim_complaint_id) REFERENCES automotive_claim_complaints (id) MATCH SIMPLE
  ON UPDATE CASCADE ON DELETE RESTRICT;
ALTER TABLE automotive_claim_complaint_labors
  ADD COLUMN notes varchar(255),
  ALTER requested SET DEFAULT 0.0,
  ALTER hours SET DEFAULT 0.0,
  ALTER rate SET DEFAULT 0.0,
  ALTER billed SET DEFAULT 0.0,
  ALTER approved SET DEFAULT 0.0,
  DROP CONSTRAINT automotive_claim_complaint_labors_complaint_id_fkey,
  ADD CONSTRAINT automotive_claim_complaint_labors_complaint_id_fkey FOREIGN KEY (automotive_claim_complaint_id) REFERENCES automotive_claim_complaints (id) MATCH SIMPLE
  ON UPDATE CASCADE ON DELETE RESTRICT;
ALTER TABLE automotive_claim_complaints
  ALTER add_line_flag SET DEFAULT FALSE,
  ALTER goodwill_flag SET DEFAULT FALSE,
  ALTER parts_total SET DEFAULT 0.0,
  ALTER labor_total SET DEFAULT 0.0,
  ALTER towing SET DEFAULT 0.0,
  ALTER rental SET DEFAULT 0.0,
  ALTER sublet SET DEFAULT 0.0;