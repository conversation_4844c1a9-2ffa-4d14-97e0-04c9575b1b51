ALTER TABLE gap_claims
  ADD COLUMN recovery_submitted_at timestamp,
  ADD COLUMN recovery_updated_at timestamp,
  ADD COLUMN gap_closed_at timestamp,
  ADD COLUMN allied_claim_number varchar(255) NOT NULL DEFAULT '',
  ADD COLUMN nada_manager_flag bool NOT NULL DEFAULT FALSE;

UPDATE gap_claims set recovery_start_date = date_of_loss, gap_closed_at = date_of_loss where date_of_loss is not null;

CREATE INDEX gap_claims_recovery_submitted_at_idx on gap_claims USING btree (recovery_submitted_at);
CREATE INDEX gap_claims_recovery_updated_at_idx on gap_claims USING btree (recovery_updated_at);
CREATE INDEX gap_claims_gap_closed_at_idx on gap_claims USING btree (gap_closed_at);
