CREATE INDEX automotive_claims_product_code_idx on automotive_claims USING btree (product_code);
CREATE INDEX automotive_claim_payments_check_number_idx on automotive_claim_payments USING btree (check_number);

alter table automotive_claims
  add column total_labor NUMERIC(12,2) NOT NULL DEFAULT 0.0,
  add column total_parts NUMERIC(12,2) NOT NULL DEFAULT 0.0;

DO $$
DECLARE
  rec RECORD;
  labor_total numeric(12,2);
  parts_total numeric(12,2);
BEGIN
  FOR rec in select id from automotive_claims where status = 'CheckWritten'
  LOOP
    select into labor_total case when sum(approved) is null then 0.0 else sum(approved) end from automotive_claim_complaint_labors where automotive_claim_complaint_id in (select id from automotive_claim_complaints where automotive_claim_id = rec.id);
    select into parts_total case when sum(approved) is null then 0.0 else sum(approved) end from automotive_claim_complaint_parts where automotive_claim_complaint_id in (select id from automotive_claim_complaints where automotive_claim_id = rec.id);
    UPDATE automotive_claims set total_labor = labor_total where id = rec.id;
    UPDATE automotive_claims set total_parts = parts_total where id = rec.id;
  END LOOP;
END $$;

