CREATE TEMPORARY TABLE tmpClaims (
    contract_number VARCHAR(255) NOT NULL,
    ro VARCHAR(255) NOT NULL
);

INSERT INTO tmpClaims
    (contract_number, ro)
VALUES 
    ('PPM-A3616331','188977'),
    ('PPM-A7074827','189184'),
    ('PPM-A7410285','189047'),
    ('PPM-A7744351','189051'),
    ('PPM-A7780688','189180'),
    ('PPM-A7845763','189086'),
    ('PPM-A7953550','189081'),
    ('PPM-A7956037','189198'),
    ('PPM-B8051223','189088'),
    ('PPM-B8127911','189230'),
    ('PPM-B8136297','189071'),
    ('PPM-B8262136','188959'),
    ('PPM-B8290443','189285'),
    ('PPM-B8313276','189372'),
    ('PPM-B8419893','189159'),
    ('PPM-B8419993','188923'),
    ('PPM-B8522087','189121'),
    ('PPM-B8540888','189227'),
    ('PPM-B8621576','189287'),
    ('PPM-B8765066','189260'),
    ('PPM-B8799848','189187'),
    ('PPM-B8857759','189246'),
    ('PPM-B8901358','189106'),
    ('PPM-B8902643','189084'),
    ('PPM-B8964972','189308'),
    ('PPM-B8965130','189348'),
    ('PPM-B8982384','188921'),
    ('PPM-B8991499','189231'),
    ('PPM-B9005483','188915'),
    ('PPM-B9012574','189197'),
    ('PPM-B9045420','189108'),
    ('PPM-B9066195','189241'),
    ('PPM-B9075762','188944'),
    ('PPM-B9079863','189313'),
    ('PPM-B9080623','188998'),
    ('PPM-B9086942','189221'),
    ('PPM-B9118185','188874'),
    ('PPM-B9126865','189339'),
    ('PPM-B9137803','189102'),
    ('PPM-B9159391','188935'),
    ('PPM-B9184027','188931'),
    ('PPM-B9184809','189179'),
    ('PPM-B9190210','188953'),
    ('PPM-B9198358','189146'),
    ('PPM-B9201107','189167'),
    ('PPM-B9201202','189264'),
    ('PPM-B9210781','188961'),
    ('PPM-B9213223','188987'),
    ('PPM-B9216956','189154'),
    ('PPM-B9218459','189031'),
    ('PPM-B9220879','189082'),
    ('PPM-B9224601','189145'),
    ('PPM-B9229207','189148'),
    ('PPM-B9232449','189289'),
    ('PPM-B9233579','189062'),
    ('PPM-B9236328','188986'),
    ('PPM-B9246164','189275'),
    ('PPM-B9251636','189265'),
    ('PPM-B9253505','189076'),
    ('PPM-B9259321','189298'),
    ('PPM-B9269323','189125'),
    ('PPM-B9280259','188994'),
    ('PPM-B9288224','189212'),
    ('PPM-B9304365','189251'),
    ('PPM-B9308572','188983'),
    ('PPM-B9310793','189021'),
    ('PPM-B9313757','189192'),
    ('PPM-B9317818E','189087'),
    ('PPM-B9317988','189206'),
    ('PPM-B9322727','189328'),
    ('PPM-B9324732','189098'),
    ('PPM-B9325674','189004'),
    ('PPM-B9328808','189005'),
    ('PPM-B9333520','188970'),
    ('PPM-B9335911','189124'),
    ('PPM-B9335988','189128'),
    ('PPM-B9337147','189270'),
    ('PPM-B9344372','189016'),
    ('PPM-B9344488E','189095'),
    ('PPM-B9345869','189273'),
    ('PPM-B9346979','189183'),
    ('PPM-B9354619','189177'),
    ('PPM-B9363323','189024'),
    ('PPM-B9364988E','188988'),
    ('PPM-B9366752','188711'),
    ('PPM-B9374084','189182'),
    ('PPM-B9380541','188958'),
    ('PPM-B9391317','189138'),
    ('PPM-B9394654','189053'),
    ('PPM-B9398340','188972'),
    ('PPM-B9400538','188985'),
    ('PPM-B9400952','189250'),
    ('PPM-B9402536','188997'),
    ('PPM-B9403155','188943'),
    ('PPM-B9407924','189244'),
    ('PPM-B9418106','189066'),
    ('PPM-B9421183','188978'),
    ('PPM-B9428399','189018'),
    ('PPM-B9430230','189261'),
    ('PPM-B9437457','189085')
;


-- Insert the Check entries
INSERT INTO automotive_claim_payment_checks (automotive_claim_payments_id, check_amount, check_number, paid_date, updated_at)
SELECT
    acp.id,
    ac.estimate,
    '0070089',
    '2023-11-13',
    now() at time zone 'utc'
FROM automotive_claim_payments acp
join automotive_claims ac on ac.id = acp.automotive_claim_id
join tmpClaims tc on tc.contract_number = ac.contract_number and tc.ro = ac.ro
where
    tc.ro = ac.ro
;

-- Update the Payment rows
-- Batch 
UPDATE automotive_claim_payments acp
SET
    batch_key = (select batch_key from automotive_intacct_bill_batches where batch_title = '11/13/2023' limit 1),
    bill_key = 1110923, -- This is based on looking at the payment rows with the same batch key and reviewing the bill_memo to find where the batch number 99433 would fall under and found that there's no rows with this bill_key
    bill_memo = 'CATSD_11-13-2023_14:58:52 #99433',
    updated_at = now() at time zone 'utc',
    intacct_bill_number = 'CATSD_11-13-2023_14:58:52 #99433',
    is_complete = TRUE
FROM automotive_claims ac
JOIN tmpClaims tc on tc.contract_number = ac.contract_number and tc.ro = ac.ro
WHERE
    acp.automotive_claim_id = ac.id
;

-- Update Claim Status
UPDATE automotive_claims ac
SET
    status = 'CheckWritten'
FROM tmpClaims tc
WHERe
    tc.contract_number = ac.contract_number
    and tc.ro = ac.ro
;

DROP TABLE IF EXISTS tmpClaims;