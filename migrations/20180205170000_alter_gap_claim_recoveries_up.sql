ALTER TABLE gap_claim_recoveries
  DROP COLUMN case_comments;

CREATE UNIQUE INDEX "gap_claim_recoveries_gap_claim_id_uidx"
  ON "gap_claim_recoveries" USING BTREE ("gap_claim_id");


CREATE TABLE "gap_recovery_comments" (
  "id"                 SERIAL    NOT NULL,
  "gap_recovery_id"    INTEGER   NOT NULL,
  "notes_text"         TEXT      NOT NULL,
  "created_by_user_id" INTEGER   NOT NULL,
  "created_at"         TIMESTAMP NOT NULL,
  CONSTRAINT "gap_recovery_comments_pkey" PRIMARY KEY ("id"),
  CONSTRAINT "gap_recovery_comments_gap_recovery_id_fkey" FOREIGN KEY ("gap_recovery_id") REFERENCES "gap_claim_recoveries" ("id") ON UPDATE CASCADE ON DELETE RESTRICT,
  CONSTRAINT "gap_recovery_comments_created_by_user_id_fkey" FOREIGN KEY ("created_by_user_id") REFERENCES "users" ("id") ON UPDATE CASCADE ON DELETE RESTRICT
);

CREATE INDEX gap_recovery_comments_created_by_user_id_idx on gap_recovery_comments USING btree (created_by_user_id);
CREATE INDEX gap_recovery_comments_created_at_idx on gap_recovery_comments USING btree (created_at);
CREATE INDEX gap_recovery_comments_gap_recovery_id_idx ON gap_recovery_comments USING btree (gap_recovery_id);