-- Delete check entries that were previously created
delete from automotive_claim_payment_checks
where
    check_number = '201417'
    and automotive_claim_payments_id IN (
        select
            acp.id
        from automotive_claim_payments acp
        join automotive_claims ac on ac.id = acp.automotive_claim_id
        where
            (
                (ac.contract_number = 'HME-B9187627E' and ac.ro = '3426332' )
                or (ac.contract_number = 'TCA-A3959766' and ac.ro = '3426333')
            )
            and ac.facility_name = 'C&K AUTO PARTS CC'
            and acp.bill_memo = 'HME-B9187627E TAVIZON,LUIS PAID WRONG FACILITY'
    )
;

-- Change completed status to False
update automotive_claim_payments acp
set is_complete = false
from automotive_claims ac
where
            (
                (ac.contract_number = 'HME-B9187627E' and ac.ro = '3426332' )
                or (ac.contract_number = 'TCA-A3959766' and ac.ro = '3426333')
            )
    and ac.facility_name = 'C&K AUTO PARTS CC'
    and acp.bill_memo = 'HME-B9187627E TAVIZON,LUIS PAID WRONG FACILITY'
;

-- Change the Claim Status back
update automotive_claims 
set status = 'WaitingForReversed'
where 
    (
        (contract_number = 'HME-B9187627E'  and ro = '3426332')
        or (contract_number = 'TCA-A3959766' and ro = '3426333')
    )
    and facility_name = 'C&K AUTO PARTS CC'
;
