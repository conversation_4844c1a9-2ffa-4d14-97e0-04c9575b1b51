CREATE TABLE automotive_intacct_bill_batches (
  id serial,
  batch_title character varying(255) NOT NULL,
  batch_key integer NOT NULL,

  CONSTRAINT automotive_intacct_bill_batches_pkey PRIMARY KEY (id)
);
CREATE UNIQUE INDEX automotive_intacct_bill_batches_batch_title_uidx ON automotive_intacct_bill_batches USING btree(batch_title);
CREATE UNIQUE INDEX automotive_intacct_bill_batches_batch_key_uidx ON automotive_intacct_bill_batches USING btree(batch_key);

CREATE TABLE automotive_claim_payments (
  id serial,
  authorization_number serial not null,
  automotive_claim_id integer not null,
  check_number integer not null default 0,
  amount numeric(12,2),
  paid_date date,
  batch_key integer,
  bill_key integer,
  payment_key integer,
  bill_memo character varying(255) not null default '',
  updated_at timestamp without time zone,

  CONSTRAINT automotive_claim_payments_pkey PRIMARY KEY (id),
  CONSTRAINT automotive_claim_payments_automotive_claim_id_fkey FOREIGN KEY (automotive_claim_id)
    REFERENCES automotive_claims (id) MATCH SIMPLE ON UPDATE CASCADE ON DELETE RESTRICT
);
CREATE INDEX automotive_claim_payments_automotive_claim_id_idx ON automotive_claim_payments USING btree (automotive_claim_id);
