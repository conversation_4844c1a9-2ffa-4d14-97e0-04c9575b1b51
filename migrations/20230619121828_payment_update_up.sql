DO $$
DECLARE
  claimID integer;
  acpID integer;
BEGIN
    select into claimID id
    from automotive_claims
    where contract_number='TCA-A3793328' and status = 'WaitingForReversed';

    insert into automotive_claim_payments(
        automotive_claim_id,updated_at) values (
            claimID, now() at time zone 'utc') returning id into acpID;

    insert into automotive_claim_payment_checks(
        automotive_claim_payments_id,
        check_amount,
        check_number,
        paid_date,
        updated_at) values (
            acpID ,1035,'5901','2023-04-18',now() at time zone 'utc');

    update automotive_claims set status = 'CheckWritten' where id = claimID;
END $$;