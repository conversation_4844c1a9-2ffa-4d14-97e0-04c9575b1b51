create table if not exists automotive_claim_complaint_towings
(
    id serial not null
        constraint automotive_claim_complaint_towings_pkey
            primary key,
    automotive_claim_complaint_id integer
        constraint automotive_claim_complaint_towings_complaint_id_fkey
            references automotive_claim_complaints
            on update cascade on delete restrict,
    description varchar(255),
    requested numeric(12,2) default 0.0,
    approved numeric(12,2) default 0.0,
    notes varchar(255)
);
create index if not exists automotive_claim_complaint_towing_automotive_claim_complaint_id on automotive_claim_complaint_towings (automotive_claim_complaint_id);

create table if not exists automotive_claim_complaint_rentals
(
    id serial not null
        constraint automotive_claim_complaint_rentals_pkey
            primary key,
    automotive_claim_complaint_id integer
        constraint automotive_claim_complaint_rentals_complaint_id_fkey
            references automotive_claim_complaints
            on update cascade on delete restrict,
    description varchar(255),
    requested numeric(12,2) default 0.0,
    approved numeric(12,2) default 0.0,
    notes varchar(255)
);
create index if not exists automotive_claim_complaint_rental_automotive_claim_complaint_id on automotive_claim_complaint_rentals (automotive_claim_complaint_id);

create table if not exists automotive_claim_complaint_sublets
(
    id serial not null
        constraint automotive_claim_complaint_sublets_pkey
            primary key,
    automotive_claim_complaint_id integer
        constraint automotive_claim_complaint_sublets_complaint_id_fkey
            references automotive_claim_complaints
            on update cascade on delete restrict,
    description varchar(255),
    requested numeric(12,2) default 0.0,
    approved numeric(12,2) default 0.0,
    notes varchar(255)
);
create index if not exists automotive_claim_complaint_sublet_automotive_claim_complaint_id on automotive_claim_complaint_sublets (automotive_claim_complaint_id);

insert into  automotive_claim_complaint_towings
(automotive_claim_complaint_id, description, requested, approved, notes)
select id automotive_claim_complaint_id, 'Towing Amount' description, coalesce(towing, 0) as requested, coalesce(towing, 0) as approved, '' notes
from automotive_claim_complaints
where towing > 0;

insert into  automotive_claim_complaint_rentals
(automotive_claim_complaint_id, description, requested, approved, notes)
select id automotive_claim_complaint_id, 'Rental Amount' description, coalesce(rental, 0) as requested, coalesce(rental, 0) as approved, '' notes
from automotive_claim_complaints
where rental > 0;

insert into  automotive_claim_complaint_sublets
(automotive_claim_complaint_id, description, requested, approved, notes)
select id automotive_claim_complaint_id, 'Sublet Amount' description, coalesce(sublet, 0) as requested, coalesce(sublet, 0) as approved, '' notes
from automotive_claim_complaints
where sublet > 0;



alter table automotive_claim_complaints
rename towing to towings_total;

alter table automotive_claim_complaints
rename rental to rentals_total;

alter table automotive_claim_complaints
rename sublet to sublets_total;