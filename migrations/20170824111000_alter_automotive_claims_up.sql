ALTER TABLE automotive_claims
  ALTER COLUMN estimate SET DEFAULT 0.0,
  ALTER COLUMN labor_rate TYPE NUMERIC(12,2) USING labor_rate::NUMERIC,
  ALTER COLUMN beginning_miles TYPE INTEGER USING beginning_miles::INTEGER ,
  ALTER COLUMN ending_miles TYPE INTEGER USING ending_miles::INTEGER,
  ALTER COLUMN effective_date TYPE DATE USING effective_date::date,
  ALTER COLUMN expiration_date TYPE DATE USING expiration_date::date,
  DROP COLUMN store_id;
ALTER TABLE automotive_claims rename COLUMN store_code to facility_code;