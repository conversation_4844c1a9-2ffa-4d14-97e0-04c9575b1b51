ALTER TABLE gap_claims
  ADD COLUMN has_requested_book_out boolean NOT NULL DEFAULT FALSE,
  ADD COLUMN book_out_requested_date date,
  ADD COLUMN book_out_requested_manager_flag boolean NOT NULL DEFAULT FALSE,
  ADD COLUMN has_options_match_book_out boolean NOT NULL DEFAULT FALSE,
  ADD COLUMN options_match_book_out_mismatches varchar(255),
  ADD COLUMN options_book_out_match_manager_flag boolean NOT NULL DEFAULT FALSE,
  ADD COLUMN police_report_issues varchar(255),
  ADD COLUMN last_message varchar(255),
  ADD COLUMN finance_manager varchar(255);
