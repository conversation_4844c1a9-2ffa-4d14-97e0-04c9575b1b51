insert into automotive_claim_payment_checks(
    automotive_claim_payments_id,check_amount,check_number,paid_date,updated_at)
        values (
            (select id
            from automotive_claim_payments acp
            where automotive_claim_id = (
                select id from automotive_claims where contract_number = 'PDR-D9356225E' and status = 'WaitingForReversed'
                ) and amount = -8.25),
                8.25,'PDR-D9356225ECHARGEBACK','2023-10-12',now() at time zone 'utc');

update automotive_claims set status = 'CheckWritten' where contract_number='PDR-D9356225E' and status = 'WaitingForReversed';

update automotive_claims set status = 'CheckWritten' where contract_number='TCA-B8112793' and status = 'WaitingForReversed';

update automotive_claims set status = 'CheckWritten' where contract_number='TCA-B8610254VW' and status = 'WaitingForReversed';

insert into automotive_claim_payment_checks(
    automotive_claim_payments_id,check_amount,check_number,paid_date,updated_at)
        values (
            (select id
            from automotive_claim_payments acp
            where automotive_claim_id = (
                select id from automotive_claims where contract_number = 'TCA-B9292707E' and status = 'WaitingForCheck')),
                1051.17,'0068812','2023-10-10',now() at time zone 'utc');

update automotive_claims set status = 'CheckWritten' where contract_number='TCA-B9292707E' and status = 'WaitingForCheck';
