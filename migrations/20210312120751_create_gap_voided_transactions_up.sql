create table gap_voided_transactions (
  id  serial not null,
  gap_claim_id integer not null,
  check_number varchar(255) not null,
  date_of_void date not null,
  check_amount numeric(12,2) not null,
  CONSTRAINT gap_voided_transactions_pkey primary key (id),
  CONSTRAINT gap_voided_transactions_gap_claim_id_fkey foreign key (gap_claim_id) references gap_claims (id) on update cascade on delete restrict
);
create index gap_transactions_gap_claim_id_idx on gap_voided_transactions (gap_claim_id);

