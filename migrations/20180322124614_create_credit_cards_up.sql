CREATE TABLE credit_cards
(
  id                 SERIAL,
  name_on_card       VA<PERSON>HAR(255) DEFAULT '',
  card_number        VARCHAR(255) DEFAULT '',
  expiration_month   INTEGER      DEFAULT 0,
  expiration_year    INTEGER      DEFAULT 0,
  manager_name       VARCHAR(255) DEFAULT '',
  manager_phone      VARCHAR(255) DEFAULT '',
  manager_fax        VARCHAR(255) DEFAULT '',
  updated_by_user_id INTEGER NOT NULL,
  updated_at         TIMESTAMP WITHOUT TIME ZONE,

  CONSTRAINT "credit_cards_pkey" primary key ("id")
);

CREATE TABLE credit_cards_record_notes (
  id                 SERIAL       NOT NULL,
  notes_text         VARCHAR(255) NOT NULL,
  created_by_user_id INTEGER      NOT NULL,
  created_at         TIMESTAMP    NOT NULL,

  CONSTRAINT "credit_cards_record_notes_pkey" PRIMARY KEY ("id"),
  CONSTRAINT "credit_cards_record_notes_created_by_user_id_fkey" FOREIGN KEY ("created_by_user_id") REFERENCES "users" ("id") ON UPDATE CASCADE ON DELETE RESTRICT
);
CREATE INDEX credit_cards_record_notes_created_by_user_id_idx
  ON credit_cards_record_notes (created_by_user_id);
CREATE INDEX credit_cards_record_notes_created_at_idx
  ON credit_cards_record_notes (created_at);

