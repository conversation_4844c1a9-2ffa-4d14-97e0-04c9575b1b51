ALTER TABLE automotive_facilities
  ADD COLUMN country VARCHAR(10) NOT NULL DEFAULT 'USA',
  ADD CONSTRAINT check_country
  CHECK (country = 'USA' OR country = 'Canada');

ALTER TABLE automotive_claims
  ADD COLUMN facility_country VARCHAR(10) DEFAULT '';

UPDATE automotive_facilities
  SET country = 'Canada'
WHERE state_code in ('AB', 'BC', 'MB', 'NS', 'ON', 'QC', 'SK');

UPDATE automotive_claims
  SET facility_country = CASE when facility_state_code in ('AB', 'BC', 'MB', 'NS', 'ON', 'QC', 'SK') then 'Canada' else 'USA' END
WHERE facility_id IS NOT NULL;