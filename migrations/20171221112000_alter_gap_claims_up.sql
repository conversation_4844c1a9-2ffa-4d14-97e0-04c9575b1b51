ALTER TABLE gap_claims
  ADD COLUMN has_recovery bool NOT NULL DEFAULT FALSE,
  ADD COLUMN recovery_refund numeric(12,2) NOT NULL DEFAULT 0.0,
  ADD COLUMN recovery_status VARCHAR(255) NOT NULL DEFAULT '',
  ADD COLUMN insurance_company VARCHAR(255) NOT NULL DEFAULT '',
  ADD COLUMN has_insurance_company BOOLEAN NOT NULL DEFAULT FALSE,
  ADD COLUMN policy_number VARCHAR(255) NOT NULL DEFAULT '',
  ADD COLUMN has_policy_number BOOLEAN NOT NULL DEFAULT FALSE,
  ADD COLUMN mileage_deduction numeric(12,2) NOT NULL DEFAULT 0.0,
  ADD COLUMN has_mileage_deduction BOOLEAN NOT NULL DEFAULT FALSE,
  ADD COLUMN nada numeric(12,2) NOT NULL DEFAULT 0.0,
  ADD COLUMN has_nada BOOLEAN NOT NULL DEFAULT FALSE,
  ADD COLUMN has_valuation_nada_difference BOOLEAN NOT NULL DEFAULT FALSE,
  ADD COLUMN mileage_deduction_manager_flag BOOLEAN NOT NULL DEFAULT FALSE;

CREATE TABLE gap_insurance_companies (
  id SERIAL,
  name VARCHAR(255) NOT NULL,
  CONSTRAINT gap_insurance_companies_pkey PRIMARY KEY (id)
);
CREATE UNIQUE INDEX  "gap_insurance_companies_name_uidx" ON "gap_insurance_companies" USING btree("name");

INSERT INTO gap_insurance_companies(name) values
  ('21st Century Insurance'),
  ('AAA Auto Insurance'),
  ('ACCC Insurance Company'),
  ('Acuity Insurance'),
  ('AIG'),
  ('Allied Insurance'),
  ('Allstate'),
  ('American Access Casualty Company '),
  ('American National Property & Casualty'),
  ('Ameriprise'),
  ('Amica Mutual Insurance'),
  ('Anchor General Insurance Company'),
  ('Auto Club Enterprises'),
  ('Auto-Owners Insurance'),
  ('Badger Mutual Insurance'),
  ('Bear River Insurance'),
  ('Bristol West'),
  ('California Casualty Insurance Company '),
  ('Casualty Underwriters Insurance '),
  ('Coast National Insurance'),
  ('CSAA Insurance'),
  ('Echelon Poperty & Casulaty Insurance Company'),
  ('Electric Insurance Company'),
  ('Esurance'),
  ('Farm Bureau'),
  ('Farmers Insurance'),
  ('GEICO'),
  ('Hallmark Insurance Company'),
  ('Hartford Insurance Group'),
  ('Infinity Insurance'),
  ('Kemper Auto Insurance'),
  ('Legacy Auto Insurance'),
  ('Liberty Mutual Insurance'),
  ('Loya Insurance Company'),
  ('Mercury Insurance'),
  ('MetLife'),
  ('Mountain West Farm Bureau'),
  ('National General Insurance'),
  ('Nationwide Insurance Company'),
  ('PEMCO'),
  ('Permanent General Company'),
  ('Progressive'),
  ('Qualitas'),
  ('Safe Auto Insurance Company'),
  ('SafeCo Insurance'),
  ('Safeway Insurance Group'),
  ('Secura Insurance'),
  ('Sentry Auto Insurance'),
  ('Shelter Insurance Company '),
  ('State Auto Insurance Company'),
  ('State Farm Insurance'),
  ('SWBC'),
  ('The General Auto Insurance'),
  ('The Hartford'),
  ('Travelers Insurance'),
  ('United Auto Insurance'),
  ('United Heritage Insurance'),
  ('United Insurance Group'),
  ('UAIC'),
  ('USAA');