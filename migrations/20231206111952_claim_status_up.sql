--TCA-A8096748
insert into automotive_claim_payments(automotive_claim_id,amount,paid_date,updated_at) values(
    (select id from automotive_claims where contract_number = 'TCA-A8096748' and status = 'InvoiceSent'),
    675.36,'2023-08-02',now() at time zone 'utc');

update automotive_claims set status = 'CCPaid' where contract_number = 'TCA-A8096748' and status = 'InvoiceSent';

--TCA-B8452618
insert into automotive_claim_payments(automotive_claim_id,amount,paid_date,updated_at) values(
    (select id from automotive_claims where contract_number = 'TCA-B8452618' and status = 'InvoiceSent'),
    1817.28,'2023-08-03',now() at time zone 'utc');

update automotive_claims set status = 'CCPaid' where contract_number = 'TCA-B8452618' and status = 'InvoiceSent';

--HME-A4377552
insert into automotive_claim_payments(automotive_claim_id,amount,paid_date,updated_at) values(
    (select id from automotive_claims where contract_number = 'HME-A4377552' and status = 'InvoiceSent'),
    1728,'2023-09-28',now() at time zone 'utc');

update automotive_claims set status = 'CCPaid' where contract_number = 'HME-A4377552' and status = 'InvoiceSent';