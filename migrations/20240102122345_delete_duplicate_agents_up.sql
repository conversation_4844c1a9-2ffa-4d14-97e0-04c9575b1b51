
--<EMAIL>
update automotive_claim_documents
	set created_by_user_id = (select id from users where email = '<EMAIL>')
	where created_by_user_id = (select id from users where email = '<EMAIL>');
		
update automotive_claim_documents
	set deleted_by_user_id = (select id from users where email = '<EMAIL>')
	where deleted_by_user_id = (select id from users where email = '<EMAIL>');
	
update automotive_claim_pre_auth_numbers
	set created_by_user_id = (select id from users where email = '<EMAIL>')
	where created_by_user_id = (select id from users where email = '<EMAIL>');

update automotive_claim_updates
	set updated_by_user_id = (select id from users where email = '<EMAIL>')
	where updated_by_user_id = (select id from users where email = '<EMAIL>');
	
update automotive_claims
	set reassigned_owner_id = (select id from users where email = '<EMAIL>')
	where reassigned_owner_id = (select id from users where email = '<EMAIL>');
	
update automotive_claims
	set owner_id = (select id from users where email = '<EMAIL>')
	where owner_id = (select id from users where email = '<EMAIL>');
	
update automotive_facilities
	set created_by_user_id = (select id from users where email = '<EMAIL>')
	where created_by_user_id = (select id from users where email = '<EMAIL>');
	
update automotive_facilities_effective_rates
	set created_by_user_id = (select id from users where email = '<EMAIL>')
	where created_by_user_id = (select id from users where email = '<EMAIL>');

update automotive_facility_record_notes
	set created_by_user_id = (select id from users where email = '<EMAIL>')
	where created_by_user_id = (select id from users where email = '<EMAIL>');

update automotive_record_notes
	set created_by_user_id = (select id from users where email = '<EMAIL>')
	where created_by_user_id = (select id from users where email = '<EMAIL>');

delete from user_approved_limits where user_id = (select id from users where email = '<EMAIL>');

delete from users where email = '<EMAIL>';


-- <EMAIL>
update automotive_claim_documents
	set created_by_user_id = (select id from users where email = '<EMAIL>')
	where created_by_user_id = (select id from users where email = '<EMAIL>');
		
update automotive_claim_documents
	set deleted_by_user_id = (select id from users where email = '<EMAIL>')
	where deleted_by_user_id = (select id from users where email = '<EMAIL>');
	
update automotive_claim_pre_auth_numbers
	set created_by_user_id = (select id from users where email = '<EMAIL>')
	where created_by_user_id = (select id from users where email = '<EMAIL>');

update automotive_claim_updates
	set updated_by_user_id = (select id from users where email = '<EMAIL>')
	where updated_by_user_id = (select id from users where email = '<EMAIL>');
	
update automotive_claims
	set reassigned_owner_id = (select id from users where email = '<EMAIL>')
	where reassigned_owner_id = (select id from users where email = '<EMAIL>');
	
update automotive_claims
	set owner_id = (select id from users where email = '<EMAIL>')
	where owner_id = (select id from users where email = '<EMAIL>');
	
update automotive_facilities
	set created_by_user_id = (select id from users where email = '<EMAIL>')
	where created_by_user_id = (select id from users where email = '<EMAIL>');
	
update automotive_facilities_effective_rates
	set created_by_user_id = (select id from users where email = '<EMAIL>')
	where created_by_user_id = (select id from users where email = '<EMAIL>');

update automotive_facility_record_notes
	set created_by_user_id = (select id from users where email = '<EMAIL>')
	where created_by_user_id = (select id from users where email = '<EMAIL>');

update automotive_record_notes
	set created_by_user_id = (select id from users where email = '<EMAIL>')
	where created_by_user_id = (select id from users where email = '<EMAIL>');

delete from user_approved_limits where user_id = (select id from users where email = '<EMAIL>');

update automotive_facility_zones
	set owner_id = (select id from users where email = '<EMAIL>')
	where owner_id = (select id from users where email = '<EMAIL>');

update customers_record_notes
	set created_by_user_id = (select id from users where email = '<EMAIL>')
	where created_by_user_id = (select id from users where email = '<EMAIL>');
	
delete from users where email = '<EMAIL>';


--Jade
update automotive_claim_documents
	set created_by_user_id = (select id from users where email = '<EMAIL>')
	where created_by_user_id in (select id from users where email in('<EMAIL>','<EMAIL>'));
		

update automotive_claim_documents
	set deleted_by_user_id = (select id from users where email = '<EMAIL>')
	where deleted_by_user_id in (select id from users where email  in('<EMAIL>','<EMAIL>'));
	
update automotive_claim_pre_auth_numbers
	set created_by_user_id = (select id from users where email = '<EMAIL>')
	where created_by_user_id in (select id from users where email  in('<EMAIL>','<EMAIL>'));

update automotive_claim_updates
	set updated_by_user_id = (select id from users where email = '<EMAIL>')
	where updated_by_user_id in (select id from users where email  in('<EMAIL>','<EMAIL>'));
	
update automotive_claims
	set reassigned_owner_id = (select id from users where email = '<EMAIL>')
	where reassigned_owner_id in (select id from users where email  in('<EMAIL>','<EMAIL>'));
	
update automotive_claims
	set owner_id = (select id from users where email = '<EMAIL>')
	where owner_id  in (select id from users where email  in('<EMAIL>','<EMAIL>'));
	
update automotive_facilities
	set created_by_user_id = (select id from users where email = '<EMAIL>')
	where created_by_user_id in (select id from users where email  in('<EMAIL>','<EMAIL>'));
	
update automotive_facilities_effective_rates
	set created_by_user_id = (select id from users where email = '<EMAIL>')
	where created_by_user_id  in (select id from users where email  in('<EMAIL>','<EMAIL>'));

update automotive_facility_record_notes
	set created_by_user_id = (select id from users where email = '<EMAIL>')
	where created_by_user_id  in (select id from users where email  in('<EMAIL>','<EMAIL>'));

update automotive_record_notes
	set created_by_user_id = (select id from users where email = '<EMAIL>')
	where created_by_user_id  in (select id from users where email  in('<EMAIL>','<EMAIL>'));

delete from user_approved_limits where user_id  in (select id from users where email in('<EMAIL>','<EMAIL>'));

update automotive_facility_zones
	set owner_id = (select id from users where email = '<EMAIL>')
	where owner_id  in (select id from users where email  in('<EMAIL>','<EMAIL>'));

update customers_record_notes
	set created_by_user_id = (select id from users where email = '<EMAIL>')
	where created_by_user_id in (select id from users where email  in('<EMAIL>','<EMAIL>'));
	
update gap_claim_contract_field_notes 
	set created_by_user_id = (select id from users where email = '<EMAIL>')
	where created_by_user_id in (select id from users where email  in('<EMAIL>','<EMAIL>'));
	
update email_template_updates 
	set updated_by_user_id = (select id from users where email = '<EMAIL>')
	where updated_by_user_id in (select id from users where email  in('<EMAIL>','<EMAIL>'));
	
update gap_claim_documents 
	set created_by_user_id = (select id from users where email = '<EMAIL>')
	where created_by_user_id in (select id from users where email  in('<EMAIL>','<EMAIL>'));

update gap_claim_documents 
	set deleted_by_user_id = (select id from users where email = '<EMAIL>')
	where deleted_by_user_id in (select id from users where email  in('<EMAIL>','<EMAIL>'));
	
update gap_claim_field_notes
	set created_by_user_id = (select id from users where email = '<EMAIL>')
	where created_by_user_id in (select id from users where email  in('<EMAIL>','<EMAIL>'));
	
update gap_claim_updates
	set updated_by_user_id = (select id from users where email = '<EMAIL>')
	where updated_by_user_id in (select id from users where email  in('<EMAIL>','<EMAIL>'));

update gap_claims
	set created_by_user_id = (select id from users where email = '<EMAIL>')
	where created_by_user_id in (select id from users where email  in('<EMAIL>','<EMAIL>'));
	
update gap_claims
	set owner_id = (select id from users where email = '<EMAIL>')
	where owner_id in (select id from users where email  in('<EMAIL>','<EMAIL>'));

update record_notes
	set created_by_user_id = (select id from users where email = '<EMAIL>')
	where created_by_user_id  in (select id from users where email  in('<EMAIL>','<EMAIL>'));

update vta_claim_updates
	set updated_by_user_id = (select id from users where email = '<EMAIL>')
	where updated_by_user_id in (select id from users where email  in('<EMAIL>','<EMAIL>'));

update vta_claims
	set created_by_user_id = (select id from users where email = '<EMAIL>')
	where created_by_user_id in (select id from users where email  in('<EMAIL>','<EMAIL>'));

update vta_record_notes
	set created_by_user_id = (select id from users where email = '<EMAIL>')
	where created_by_user_id in (select id from users where email  in('<EMAIL>','<EMAIL>'));
	
delete from users where email in ('<EMAIL>','<EMAIL>');

-- Kim  

update automotive_claim_documents
	set created_by_user_id = (select id from users where email = '<EMAIL>')
	where created_by_user_id in (select id from users where email in('<EMAIL>','<EMAIL>'));
		

update automotive_claim_documents
	set deleted_by_user_id = (select id from users where email = '<EMAIL>')
	where deleted_by_user_id in (select id from users where email  in('<EMAIL>','<EMAIL>'));
	
update automotive_claim_pre_auth_numbers
	set created_by_user_id = (select id from users where email = '<EMAIL>')
	where created_by_user_id in (select id from users where email  in('<EMAIL>','<EMAIL>'));

update automotive_claim_updates
	set updated_by_user_id = (select id from users where email = '<EMAIL>')
	where updated_by_user_id in (select id from users where email  in('<EMAIL>','<EMAIL>'));
	
update automotive_claims
	set reassigned_owner_id = (select id from users where email = '<EMAIL>')
	where reassigned_owner_id in (select id from users where email  in('<EMAIL>','<EMAIL>'));
	
update automotive_claims
	set owner_id = (select id from users where email = '<EMAIL>')
	where owner_id  in (select id from users where email  in('<EMAIL>','<EMAIL>'));
	
update automotive_facilities
	set created_by_user_id = (select id from users where email = '<EMAIL>')
	where created_by_user_id in (select id from users where email  in('<EMAIL>','<EMAIL>'));
	
update automotive_facilities_effective_rates
	set created_by_user_id = (select id from users where email = '<EMAIL>')
	where created_by_user_id  in (select id from users where email  in('<EMAIL>','<EMAIL>'));

update automotive_facility_record_notes
	set created_by_user_id = (select id from users where email = '<EMAIL>')
	where created_by_user_id  in (select id from users where email  in('<EMAIL>','<EMAIL>'));

update automotive_record_notes
	set created_by_user_id = (select id from users where email = '<EMAIL>')
	where created_by_user_id  in (select id from users where email  in('<EMAIL>','<EMAIL>'));

delete from user_approved_limits where user_id  in (select id from users where email in('<EMAIL>','<EMAIL>'));

update automotive_facility_zones
	set owner_id = (select id from users where email = '<EMAIL>')
	where owner_id  in (select id from users where email  in('<EMAIL>','<EMAIL>'));

update gap_claims
    set owner_id = (select id from users where email = '<EMAIL>')
    where owner_id  in (select id from users where email  in('<EMAIL>','<EMAIL>'));

delete from users where email in ('<EMAIL>','<EMAIL>');

-- MRoss

update gap_claim_contract_field_notes 
	set created_by_user_id = (select id from users where email = '<EMAIL>')
	where created_by_user_id in (select id from users where email = '<EMAIL>');

update gap_claim_documents 
	set created_by_user_id = (select id from users where email = '<EMAIL>')
	where created_by_user_id in (select id from users where email = '<EMAIL>');
	
update gap_claim_documents 
	set deleted_by_user_id = (select id from users where email = '<EMAIL>')
	where deleted_by_user_id in (select id from users where email = '<EMAIL>');

update gap_claim_field_notes 
	set created_by_user_id = (select id from users where email = '<EMAIL>')
	where created_by_user_id in (select id from users where email = '<EMAIL>');

update gap_claim_recoveries 
	set updated_by_user_id = (select id from users where email = '<EMAIL>')
	where updated_by_user_id in (select id from users where email = '<EMAIL>');

update gap_claim_updates 
	set updated_by_user_id = (select id from users where email = '<EMAIL>')
	where updated_by_user_id in (select id from users where email = '<EMAIL>');

update gap_claims
	set created_by_user_id = (select id from users where email = '<EMAIL>')
	where created_by_user_id in (select id from users where email = '<EMAIL>');

update gap_claims
	set owner_id = (select id from users where email = '<EMAIL>')
	where owner_id in (select id from users where email = '<EMAIL>');

update gap_recovery_comments
	set created_by_user_id = (select id from users where email = '<EMAIL>')
	where created_by_user_id in (select id from users where email = '<EMAIL>');
	
delete from user_approved_limits where user_id = (select id from users where email = '<EMAIL>');

update record_notes
	set created_by_user_id = (select id from users where email = '<EMAIL>')
	where created_by_user_id in (select id from users where email = '<EMAIL>');

update vta_claim_documents 
	set created_by_user_id = (select id from users where email = '<EMAIL>')
	where created_by_user_id in (select id from users where email = '<EMAIL>');
	
update vta_claim_updates 
	set updated_by_user_id = (select id from users where email = '<EMAIL>')
	where updated_by_user_id in (select id from users where email = '<EMAIL>');

update vta_record_notes
	set created_by_user_id = (select id from users where email = '<EMAIL>')
	where created_by_user_id in (select id from users where email = '<EMAIL>');

update vta_claims
    set created_by_user_id = (select id from users where email = '<EMAIL>')
	where created_by_user_id in (select id from users where email = '<EMAIL>');

update vta_claims
    set owner_id = (select id from users where email = '<EMAIL>')
	where owner_id in (select id from users where email = '<EMAIL>');

delete from users where email = '<EMAIL>';

-- pprice

update automotive_claim_documents
	set created_by_user_id = (select id from users where email = '<EMAIL>')
	where created_by_user_id = (select id from users where email = '<EMAIL>');
		
update automotive_claim_documents
	set deleted_by_user_id = (select id from users where email = '<EMAIL>')
	where deleted_by_user_id = (select id from users where email = '<EMAIL>');
	
update automotive_claim_pre_auth_numbers
	set created_by_user_id = (select id from users where email = '<EMAIL>')
	where created_by_user_id = (select id from users where email = '<EMAIL>');

update automotive_claim_updates
	set updated_by_user_id = (select id from users where email = '<EMAIL>')
	where updated_by_user_id = (select id from users where email = '<EMAIL>');
	
update automotive_claims
	set reassigned_owner_id = (select id from users where email = '<EMAIL>')
	where reassigned_owner_id = (select id from users where email = '<EMAIL>');
	
update automotive_claims
	set owner_id = (select id from users where email = '<EMAIL>')
	where owner_id = (select id from users where email = '<EMAIL>');
	
update automotive_facilities
	set created_by_user_id = (select id from users where email = '<EMAIL>')
	where created_by_user_id = (select id from users where email = '<EMAIL>');
	
update automotive_facilities_effective_rates
	set created_by_user_id = (select id from users where email = '<EMAIL>')
	where created_by_user_id = (select id from users where email = '<EMAIL>');

update automotive_facility_record_notes
	set created_by_user_id = (select id from users where email = '<EMAIL>')
	where created_by_user_id = (select id from users where email = '<EMAIL>');

update automotive_record_notes
	set created_by_user_id = (select id from users where email = '<EMAIL>')
	where created_by_user_id = (select id from users where email = '<EMAIL>');

delete from user_approved_limits where user_id = (select id from users where email = '<EMAIL>');

update automotive_facility_zones
	set owner_id = (select id from users where email = '<EMAIL>')
	where owner_id = (select id from users where email = '<EMAIL>');

update customers_record_notes
	set created_by_user_id = (select id from users where email = '<EMAIL>')
	where created_by_user_id = (select id from users where email = '<EMAIL>');

delete from users where email = '<EMAIL>';

-- rwilliams

delete from users where email = '<EMAIL>';

-- <EMAIL>','<EMAIL>

update automotive_claim_documents
	set created_by_user_id = (select id from users where email = '<EMAIL>')
	where created_by_user_id = (select id from users where email = '<EMAIL>');
	
	
update automotive_claim_documents
	set deleted_by_user_id = (select id from users where email = '<EMAIL>')
	where deleted_by_user_id = (select id from users where email = '<EMAIL>');
	
update automotive_claim_pre_auth_numbers
	set created_by_user_id = (select id from users where email = '<EMAIL>')
	where created_by_user_id = (select id from users where email = '<EMAIL>');
	
update automotive_claim_updates
	set updated_by_user_id = (select id from users where email = '<EMAIL>')
	where updated_by_user_id = (select id from users where email = '<EMAIL>');
	
update automotive_claims
	set reassigned_owner_id = (select id from users where email = '<EMAIL>')
	where reassigned_owner_id = (select id from users where email = '<EMAIL>');
	
update automotive_claims
	set owner_id = (select id from users where email = '<EMAIL>')
	where owner_id = (select id from users where email = '<EMAIL>');
	
update automotive_facilities
	set created_by_user_id = (select id from users where email = '<EMAIL>')
	where created_by_user_id = (select id from users where email = '<EMAIL>');
	
update automotive_facilities_effective_rates
	set created_by_user_id = (select id from users where email = '<EMAIL>')
	where created_by_user_id = (select id from users where email = '<EMAIL>');
	
update automotive_facility_record_notes
	set created_by_user_id = (select id from users where email = '<EMAIL>')
	where created_by_user_id = (select id from users where email = '<EMAIL>');
	
update automotive_record_notes
	set created_by_user_id = (select id from users where email = '<EMAIL>')
	where created_by_user_id = (select id from users where email = '<EMAIL>');
	
delete from user_approved_limits where user_id = (select id from users where email = '<EMAIL>');
	
update automotive_facility_zones
	set owner_id = (select id from users where email = '<EMAIL>')
	where owner_id = (select id from users where email = '<EMAIL>');
	
update automotive_facility_zones
	set updated_by_user_id = (select id from users where email = '<EMAIL>')
	where updated_by_user_id = (select id from users where email = '<EMAIL>');
	
update customers_record_notes
	set created_by_user_id = (select id from users where email = '<EMAIL>')
	where created_by_user_id = (select id from users where email = '<EMAIL>');

update credit_cards_record_notes
	set created_by_user_id = (select id from users where email = '<EMAIL>')
	where created_by_user_id = (select id from users where email = '<EMAIL>');

update user_approved_limits
    set updated_by_user_id = (select id from users where email = '<EMAIL>')
	where updated_by_user_id = (select id from users where email = '<EMAIL>');

delete from users where email = '<EMAIL>';

