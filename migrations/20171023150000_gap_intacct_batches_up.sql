CREATE TABLE gap_intacct_batches (
  id SERIAL,
  created_at timestamp NOT NULL,
  CONSTRAINT gap_intacct_batches_pkey PRIMARY KEY (id)
);

CREATE TABLE gap_intacct_batch_details (
  id SERIAL,
  gap_claim_id INTEGER NOT NULL,
  gap_intacct_batch_id INTEGER NOT NULL,
  amount numeric(12,2),
  is_success BOOLEAN NOT NULL DEFAULT FALSE,
  CONSTRAINT gap_intacct_batch_details_pkey PRIMARY KEY (id),
  CONSTRAINT gap_intacct_batch_details_gap_claim_id_fkey FOREIGN KEY (gap_claim_id)
    REFERENCES gap_claims (id) ON UPDATE CASCADE ON DELETE RESTRICT,
  CONSTRAINT gap_intacct_batch_details_gap_intacct_batch_id_fkey FOREIGN KEY (gap_intacct_batch_id)
    REFERENCES gap_intacct_batches (id) MATCH SIMPLE ON UPDATE CASCADE ON DELETE RESTRICT);
