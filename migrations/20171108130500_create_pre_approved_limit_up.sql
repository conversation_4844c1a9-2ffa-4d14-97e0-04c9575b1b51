CREATE TABLE user_approved_limits (
  id serial,
  user_id integer NOT NULL,
  updated_at timestamp NOT NULL,
  updated_by_user_id integer NOT NULL,
  approved_limit numeric(12,2) NOT NULL DEFAULT 0.00,

  CONSTRAINT user_approved_limits_pkey PRIMARY KEY (id),
  CONSTRAINT user_approved_limits_updated_by_user_id_fkey FOREIGN KEY (updated_by_user_id) REFERENCES users (id) MATCH SIMPLE ON UPDATE CASCADE ON DELETE RESTRICT,
  CONSTRAINT user_approved_limits_user_id_fkey FOREIGN KEY (user_id) REFERENCES users (id) MATCH SIMPLE ON UPDATE CASCADE ON DELETE RESTRICT,
  CONSTRAINT user_approved_limits_user_id_unique UNIQUE (user_id)
);

CREATE INDEX user_approved_limits_updated_by_user_id_idx ON user_approved_limits USING btree (updated_by_user_id);
CREATE INDEX user_approved_limits_user_id_idx ON user_approved_limits USING btree (user_id);