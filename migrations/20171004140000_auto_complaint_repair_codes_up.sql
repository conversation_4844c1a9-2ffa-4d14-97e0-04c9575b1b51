CREATE TABLE automotive_complaint_repair_codes (
  id SERIAL,
  code VA<PERSON><PERSON><PERSON>(10) NOT NULL,
  description VARCHAR(255) NOT NULL,
  updated_at timestamp NOT NULL,
  updated_by_user_id INTEGER NOT NULL,

  CONSTRAINT automotive_complaint_repair_codes_pkey PRIMARY KEY (id),
  CONSTRAINT automotive_complaint_repair_codes_updated_by_user_id_fkey FOREIGN KEY (updated_by_user_id) REFERENCES users (id) MATCH SIMPLE ON UPDATE CASCADE ON DELETE RESTRICT
);
CREATE UNIQUE INDEX  "automotive_complaint_repair_codes_code_uidx" ON "automotive_complaint_repair_codes" USING btree("code");


ALTER TABLE automotive_claim_complaints
  DROP COLUMN repair_code,
  ADD COLUMN repair_code_id INTEGER,
  ADD CONSTRAINT automotive_claim_complaints_repair_codes_id_fkey FOREIGN KEY (repair_code_id) REFERENCES automotive_complaint_repair_codes (id) MATCH SIMPLE ON UPDATE CASCADE ON DELETE RESTRICT;
