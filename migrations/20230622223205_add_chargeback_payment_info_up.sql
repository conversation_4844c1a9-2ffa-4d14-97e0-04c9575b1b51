-- Add the Payment Check entries for the two chargeback payments
insert into automotive_claim_payment_checks 
(
	automotive_claim_payments_id, 
	check_amount, 
	check_number, 
	paid_date, 
	updated_at
) 
values 
(
	(
		select 
			acp.id 
		from automotive_claim_payments acp 
		join automotive_claims ac on ac.id = acp.automotive_claim_id 
		where
			ac.contract_number = 'TCA-D9143406E'
			and ac.status = 'WaitingForChargeback'
	),
	-370.79,
	'TCA-D9143406E CHARGEBACK',
	'2023-06-15',
	now() at time zone 'utc'
),
(
	(
		select 
			acp.id 
		from automotive_claim_payments acp 
		join automotive_claims ac on ac.id = acp.automotive_claim_id 
		where
			ac.contract_number = 'TCA-D9291127E'
			and ac.status = 'WaitingForChargeback'
	),
	-1101.60,
	'TCA-D9291127E CHARGEBACK',
	'2023-06-15',
	now() at time zone 'utc'
);

-- Update the chargeback payments as complete
update automotive_claim_payments 
set
	is_complete = true
from automotive_claims
where
	automotive_claims.id = automotive_claim_payments.automotive_claim_id 
	and contract_number in ('TCA-D9143406E','TCA-D9291127E')
	and status = 'WaitingForChargeback'
;

-- Update the Chargeback Claims so the status is Chargeback
update automotive_claims
set
	status = 'Chargeback'
where
	contract_number in ('TCA-D9143406E','TCA-D9291127E')
	and status = 'WaitingForChargeback'
;