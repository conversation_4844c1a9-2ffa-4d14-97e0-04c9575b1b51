create table if not exists company_groups
(
    id                     serial,
    name                   varchar(255)            not null,
    created_at             timestamp default now() not null,
    updated_at             timestamp default now() not null,
    constraint company_groups_pkey primary key (id),
    constraint company_groups_name_unique unique (name)
);

INSERT INTO company_groups (
    id, name, created_at, updated_at
) VALUES
  (1, '<PERSON>', now() at time zone 'utc', now() at time zone 'utc'),
  (2, 'Arrowhead', now() at time zone 'utc', now() at time zone 'utc'),
  (4, 'John <PERSON>', now() at time zone 'utc', now() at time zone 'utc'),
  (5, '<PERSON>', now() at time zone 'utc', now() at time zone 'utc'),
  (7, 'Tech9', now() at time zone 'utc', now() at time zone 'utc'),
  (3, 'LHM', now() at time zone 'utc', now() at time zone 'utc'),
  (6, 'Asbury', now() at time zone 'utc', now() at time zone 'utc');

alter table companies
    add column company_group_id integer null;

alter table companies
    add constraint companies__company_group_id__fkey
        foreign key (company_group_id)
            references company_groups(id)
            on update cascade
            on delete restrict;

update companies set company_group_id = 3 where id = 94;
update companies set company_group_id = 3 where id = 95;
update companies set company_group_id = 1 where id = 98;
update companies set company_group_id = 7 where id = 103;
update companies set company_group_id = 5 where id = 105;
update companies set company_group_id = 2 where id = 123;
update companies set company_group_id = 6 where id = 135;
update companies set company_group_id = 6 where id = 137;
update companies set company_group_id = 6 where id = 139;
update companies set company_group_id = 4 where id = 142;
update companies set company_group_id = 6 where id = 143;
update companies set company_group_id = 6 where id = 144;
update companies set company_group_id = 6 where id = 145;

