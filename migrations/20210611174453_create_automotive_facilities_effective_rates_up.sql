create table automotive_facilities_effective_rates
(
    id serial,
    facility_code varchar(7),
    facility_id int not null,
    effective_rate decimal,
    rate_type varchar(12),
    effective_date date,
    active boolean,
    created_at timestamp,
    created_by_user_id int,

    constraint automotive_facilities_effective_rates_pk primary key (id),
    constraint automotive_facilities_effective_rates_facility_id foreign key (facility_id) references automotive_facilities(id) on update cascade on delete restrict,
    constraint automotive_facilities_effective_rates_created_by_user_id foreign key (created_by_user_id) references users(id) on update cascade on delete restrict
);

create index automotive_facilities_effective_rates_facility_id_idx on automotive_facilities_effective_rates using btree(facility_id);
create index automotive_facilities_effective_rates_facility_code_idx on automotive_facilities_effective_rates using btree(facility_code);
create index automotive_facilities_effective_rates_created_by_user_id_idx on automotive_facilities_effective_rates using btree(created_by_user_id);

insert into automotive_facilities_effective_rates
(facility_code, facility_id, effective_rate, rate_type, effective_date, active, created_at, created_by_user_id)
select facility_code, id as facility_id, parts_tax as effective_rate, 'PT' as rate_type,
                  to_date('2006-01-01', 'YYYY-MM-DD') as effective_date, true as active, created_at, created_by_user_id
                      from automotive_facilities
where is_active = true;

insert into automotive_facilities_effective_rates
(facility_code, facility_id, effective_rate, rate_type, effective_date, active, created_at, created_by_user_id)
select facility_code, id as facility_id, labor_rate as effective_rate, 'LR' as rate_type,
       to_date('2006-01-01', 'YYYY-MM-DD') as effective_date, true as active, created_at, created_by_user_id
from automotive_facilities
where is_active = true;

insert into automotive_facilities_effective_rates
(facility_code, facility_id, effective_rate, rate_type, effective_date, active, created_at, created_by_user_id)
select facility_code, id as facility_id, labor_tax as effective_rate, 'LT' as rate_type,
       to_date('2006-01-01', 'YYYY-MM-DD') as effective_date, true as active, created_at, created_by_user_id
from automotive_facilities
where is_active = true;