CREATE TABLE customer_users (
	id SERIAL,
	customer_id INTEGER,
	password_digest VARCHAR(255) NOT NULL,
	created_at TIMESTAMP NOT NULL,
	logged_in_at TIMESTAMP NOT NULL,
	updated_at TIMESTAMP NOT NULL,
	password_last_changed_at timestamp NOT NULL,

	CONSTRAINT "customer_users_pkey" PRIMARY KEY ("id"),
	CONSTRAINT "customer_users_customer_id_fkey" FOREIGN KEY ("customer_id") REFERENCES "customers" ("id") ON UPDATE CASCADE ON DELETE RESTRICT
);
CREATE UNIQUE INDEX  "customer_users_uidx" ON "customer_users" USING btree("customer_id");

