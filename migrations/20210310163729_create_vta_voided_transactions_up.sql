create table vta_voided_transactions (
  id  serial not null,
  vta_claim_id integer not null,
  check_number varchar(255) not null,
  date_of_void date not null,
  check_amount numeric(12,2) not null,
  CONSTRAINT vta_voided_transactions_pkey primary key (id),
  CONSTRAINT vta_voided_transactions_vta_claim_id_fkey foreign key (vta_claim_id) references vta_claims (id) on update cascade on delete restrict
);
create index vta_transactions_vta_claim_id_idx on vta_voided_transactions (vta_claim_id);
