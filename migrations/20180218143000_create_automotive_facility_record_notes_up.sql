create table automotive_facility_record_notes(
  id  serial not null,
  facility_id integer not null,
  notes_text varchar(255) not null,
  created_by_user_id  integer not null,
  created_at timestamp not null,

  CONSTRAINT "automotive_facility_record_notes_pkey" primary key ("id"),
  CONSTRAINT "automotive_facility_record_notes_facility_id_fkey" foreign key ("facility_id") references "automotive_facilities" ("id") on update cascade on delete restrict,
  CONSTRAINT "automotive_facility_record_notes_created_by_user_id_fkey" foreign key ("created_by_user_id") references "users" ("id") on update cascade on delete restrict
);
create index automotive_facility_record_notes_facility_id_idx on automotive_facility_record_notes (facility_id);
create index automotive_facility_record_notes_created_by_user_id_idx on automotive_facility_record_notes (created_by_user_id);
create index automotive_facility_record_notes_created_at_idx on automotive_facility_record_notes (created_at);

