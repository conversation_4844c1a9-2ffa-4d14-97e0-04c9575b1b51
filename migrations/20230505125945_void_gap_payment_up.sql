update gap_claims set status = 'CV' where contract_number = 'GAC-C8075702';

insert into gap_voided_transactions(
    gap_claim_id,check_number,date_of_void,check_amount) values (
        (select id from gap_claims where contract_number='GAC-C8075702'),
        24995,'2023-04-28','-3682.47');

insert into record_notes(
    gap_claim_id,notes_text,created_by_user_id,created_at) values (
        (select id from gap_claims where contract_number='GAC-C8075702'),
        'The check with Number: 24995 Amount: 3682.47 AuthNumber: 10343 Date: 2023-04-28 is voided',
        (select id from users where email='<EMAIL>'), now() at time zone 'utc');