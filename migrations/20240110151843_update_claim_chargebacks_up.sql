-- PPM-B8836791
insert into automotive_claim_payment_checks(
    automotive_claim_payments_id,check_amount,check_number,paid_date,updated_at)
    values (
    (select id from automotive_claim_payments where automotive_claim_id =
        (select id from automotive_claims where contract_number='PPM-B8836791' and status = 'WaitingForReversed') and amount = -59.79),
        -59.79,'CHARGEBACK CLAIMS 12.22.23','2023-12-22',now() at time zone 'utc');

update automotive_claims set status = 'CheckWritten' where contract_number = 'PPM-B8836791' and status = 'WaitingForReversed';

-- PPM-B9323748
insert into automotive_claim_payment_checks(
    automotive_claim_payments_id,check_amount,check_number,paid_date,updated_at)
    values (
    (select id from automotive_claim_payments where automotive_claim_id =
        (select id from automotive_claims where contract_number='PPM-B9323748' and status = 'WaitingForReversed') and amount = -32.61),
        -32.61,'CHARGEBACK CLAIMS 12.22.23','2023-12-22',now() at time zone 'utc');

update automotive_claims set status = 'CheckWritten' where contract_number = 'PPM-B9323748' and status = 'WaitingForReversed';

-- PPM-B9326242E
insert into automotive_claim_payment_checks(
    automotive_claim_payments_id,check_amount,check_number,paid_date,updated_at)
    values (
    (select id from automotive_claim_payments where automotive_claim_id =
        (select id from automotive_claims where contract_number='PPM-B9326242E' and status = 'WaitingForReversed') and amount = -59.22),
        -59.22,'CHARGEBACK CLAIMS 12.22.23','2023-12-22',now() at time zone 'utc');

update automotive_claims set status = 'CheckWritten' where contract_number = 'PPM-B9326242E' and status = 'WaitingForReversed';

-- PPM-B9512746
insert into automotive_claim_payment_checks(
    automotive_claim_payments_id,check_amount,check_number,paid_date,updated_at)
    values (
    (select id from automotive_claim_payments where automotive_claim_id =
        (select id from automotive_claims where contract_number='PPM-B9512746' and status = 'WaitingForReversed') and amount = -59.36),
        -59.36,'CHARGEBACK CLAIMS 12.22.23','2023-12-22',now() at time zone 'utc');

update automotive_claims set status = 'CheckWritten' where contract_number = 'PPM-B9512746' and status = 'WaitingForReversed';

-- PPM-B9522293
insert into automotive_claim_payment_checks(
    automotive_claim_payments_id,check_amount,check_number,paid_date,updated_at)
    values (
    (select id from automotive_claim_payments where automotive_claim_id =
        (select id from automotive_claims where contract_number='PPM-B9522293' and status = 'WaitingForReversed') and amount = -32.61),
        -32.61,'CHARGEBACK CLAIMS 12.22.23','2023-12-22',now() at time zone 'utc');

update automotive_claims set status = 'CheckWritten' where contract_number = 'PPM-B9522293' and status = 'WaitingForReversed';

-- PPM-B9557095
insert into automotive_claim_payment_checks(
    automotive_claim_payments_id,check_amount,check_number,paid_date,updated_at)
    values (
    (select id from automotive_claim_payments where automotive_claim_id =
        (select id from automotive_claims where contract_number='PPM-B9557095' and status = 'WaitingForReversed') and amount = -32.61),
        -32.61,'CHARGEBACK CLAIMS 12.22.23','2023-12-22',now() at time zone 'utc');

update automotive_claims set status = 'CheckWritten' where contract_number = 'PPM-B9557095' and status = 'WaitingForReversed';

-- PPM-B9649884
insert into automotive_claim_payment_checks(
    automotive_claim_payments_id,check_amount,check_number,paid_date,updated_at)
    values (
    (select id from automotive_claim_payments where automotive_claim_id =
        (select id from automotive_claims where contract_number='PPM-B9649884' and status = 'WaitingForReversed') and amount = -59.20),
        -59.20,'CHARGEBACK CLAIMS 12.22.23','2023-12-22',now() at time zone 'utc');

update automotive_claims set status = 'CheckWritten' where contract_number = 'PPM-B9649884' and status = 'WaitingForReversed';

-- PPM-B9650559
insert into automotive_claim_payment_checks(
    automotive_claim_payments_id,check_amount,check_number,paid_date,updated_at)
    values (
    (select id from automotive_claim_payments where automotive_claim_id =
        (select id from automotive_claims where contract_number='PPM-B9650559' and status = 'WaitingForReversed') and amount = -59.79),
        -59.79,'CHARGEBACK CLAIMS 12.22.23','2023-12-22',now() at time zone 'utc');

update automotive_claims set status = 'CheckWritten' where contract_number = 'PPM-B9650559' and status = 'WaitingForReversed';