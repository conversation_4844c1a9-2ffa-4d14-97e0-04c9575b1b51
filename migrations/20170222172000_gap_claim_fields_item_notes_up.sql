CREATE TABLE "gap_claim_field_notes" (
  "id" serial,
  "gap_claim_id" integer NOT NULL,
  "field_id" integer NOT NULL,
  "notes_text" varchar(255),
  "created_by_user_id" integer NOT NULL,
  "created_at" timestamp NOT NULL,
  CONSTRAINT "gap_claim_field_notes_pkey" PRIMARY KEY ("id"),
  CONSTRAINT "gap_claim_field_notes_user_id_fkey" FOREIGN KEY ("created_by_user_id") REFERENCES "users" ("id") ON UPDATE CASCADE ON DELETE RESTRICT,
  CONSTRAINT "gap_claim_field_notes_claim_id_fkey" FOREIGN KEY ("gap_claim_id") REFERENCES "gap_claims" ("id") ON UPDATE CASCADE ON DELETE RESTRICT
);
CREATE INDEX gap_claim_field_notes_gap_claim_id_idx ON gap_claim_field_notes USING btree (gap_claim_id);
CREATE INDEX gap_claim_field_notes_field_id_idx ON gap_claim_field_notes USING btree (field_id);
CREATE INDEX gap_claim_field_notes_created_by_user_id_idx ON gap_claim_field_notes USING btree (created_by_user_id);
CREATE INDEX gap_claim_field_notes_created_at_idx ON gap_claim_field_notes USING btree (created_at);

CREATE TABLE "gap_claim_contract_field_notes" (
  "id" serial NOT NULL,
  "gap_claim_id" integer NOT NULL,
  "contract_number" varchar(255) NOT NULL,
  "contract_code" varchar(255) NOT NULL,
  "notes_text" varchar(255) NOT NULL,
  "created_by_user_id" integer NOT NULL,
  "created_at" timestamp NOT NULL,
  CONSTRAINT "contract_field_notes_pkey" PRIMARY KEY ("id"),
  CONSTRAINT "contract_field_notes_gap_claim_id_fkey" FOREIGN KEY ("gap_claim_id") REFERENCES "gap_claims" ("id") ON UPDATE CASCADE ON DELETE RESTRICT,
  CONSTRAINT "contract_field_notes_created_by_user_id_fkey" FOREIGN KEY ("created_by_user_id") REFERENCES "users" ("id") ON UPDATE CASCADE ON DELETE RESTRICT
);
CREATE INDEX gap_claim_contract_field_notes_gap_claim_id_idx ON gap_claim_contract_field_notes USING btree (gap_claim_id);
CREATE INDEX gap_claim_contract_field_notes_contract_number_idx ON gap_claim_contract_field_notes USING btree (contract_number);
CREATE INDEX gap_claim_contract_field_notes_contract_code_idx ON gap_claim_contract_field_notes USING btree (contract_code);
CREATE INDEX gap_claim_contract_field_notes_created_by_user_id_idx ON gap_claim_contract_field_notes USING btree (created_by_user_id);
CREATE INDEX gap_claim_contract_field_notes_created_at_idx ON gap_claim_contract_field_notes USING btree (created_at);

CREATE INDEX gap_claims_created_by_user_id_idx on gap_claims USING btree (created_by_user_id);
CREATE INDEX gap_claims_owner_id_idx on gap_claims USING btree (owner_id);
CREATE INDEX gap_claims_customer_id_idx on gap_claims USING btree (customer_id);
CREATE INDEX gap_claims_bank_id_idx on gap_claims USING btree (bank_id);
CREATE INDEX gap_claims_contract_number_idx on gap_claims USING btree (contract_number);

CREATE INDEX gap_claim_updates_gap_claim_id_idx ON gap_claim_updates USING btree (gap_claim_id);
CREATE INDEX gap_claim_updates_updated_at_idx on gap_claim_updates USING btree (updated_at);
CREATE INDEX gap_claim_updates_updated_by_user_id_idx on gap_claim_updates USING btree (updated_by_user_id);

CREATE INDEX record_notes_created_by_user_id_idx on record_notes USING btree (created_by_user_id);
CREATE INDEX record_notes_created_at_idx on record_notes USING btree (created_at);
CREATE INDEX record_notes_gap_claim_id_idx ON record_notes USING btree (gap_claim_id);

CREATE INDEX gap_claim_payments_gap_claim_id_idx ON gap_claim_payments USING btree (gap_claim_id);
CREATE INDEX gap_claim_payments_check_number_idx ON gap_claim_payments USING btree (check_number);
CREATE INDEX gap_claim_payments_batch_key_idx ON gap_claim_payments USING btree (batch_key);

CREATE INDEX email_template_updates_email_template_id_idx ON email_template_updates USING btree (email_template_id);
CREATE INDEX email_template_updates_updated_by_user_id_idx ON email_template_updates USING btree (updated_by_user_id);
CREATE INDEX email_template_updates_updated_at_idx ON email_template_updates USING btree (updated_at);