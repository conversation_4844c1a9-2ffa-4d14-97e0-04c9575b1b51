ALTER TABLE vta_claims
  ADD "vendor_id" VARCHAR(255),
  ADD "has_vendor_id" BO<PERSON>EAN NOT NULL DEFAULT FALSE,
  ADD "vendor_id_manager_flag" BOOLEAN NOT NULL DEFAULT FALSE,
  ADD "has_case_reserve" B<PERSON><PERSON><PERSON>N NOT NULL DEFAULT FALSE,
  ADD "case_reserve_manager_flag" BOOLEAN NOT NULL DEFAULT FALSE,
  ADD "case_reserve" NUMERIC(12, 2) DEFAULT 0.0;


CREATE TABLE "vta_claim_payments" (
  "id"                   SERIAL,
  "authorization_number" SERIAL,
  "vta_claim_id"         INTEGER,
  "check_number"         INTEGER DEFAULT 0,
  "amount"               NUMERIC(12, 2),
  "paid_date"            DATE,
  "batch_key"            INTEGER,
  "bill_key"             INTEGER,
  "payment_key"          INTEGER,
  "bill_memo"            VARCHAR(255) DEFAULT '' :: CHARACTER VARYING NOT NULL,
  "updated_at"           TIMESTAMP,

  CONSTRAINT "vta_claim_payments_pkey" PRIMARY KEY ("id"),
  CONSTRAINT "vta_claim_payments_vta_claim_id_fkey" FOREIGN KEY (vta_claim_id)
    REFERENCES vta_claims (id) MATCH SIMPLE ON UPDATE CASCADE ON DELETE RESTRICT,
  CONSTRAINT "vta_claim_batch_key_fkey" FOREIGN KEY ("batch_key")
    REFERENCES "intacct_bill_batches" ("batch_key") ON UPDATE CASCADE ON DELETE RESTRICT

);

CREATE INDEX vta_claim_payments_vta_claim_id_idx
  ON vta_claim_payments (vta_claim_id);

