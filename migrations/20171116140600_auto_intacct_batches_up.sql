CREATE TABLE automotive_intacct_batches (
  id SERIAL,
  created_at timestamp NOT NULL,
  CONSTRAINT automotive_intacct_batches_pkey PRIMARY KEY (id)
);

CREATE TABLE automotive_intacct_batch_details (
  id SERIAL,
  automotive_claim_id INTEGER NOT NULL,
  automotive_intacct_batch_id INTEGER NOT NULL,
  amount numeric(12,2),
  is_success BOOLEAN NOT NULL DEFAULT FALSE,
  CONSTRAINT automotive_intacct_batch_details_pkey PRIMARY KEY (id),
  CONSTRAINT automotive_intacct_batch_details_automotive_claim_id_fkey FOREIGN KEY (automotive_claim_id) REFERENCES automotive_claims (id) ON UPDATE CASCADE ON DELETE RESTRICT,
  CONSTRAINT automotive_intacct_batch_details_automotive_intacct_batch_id_fkey FOREIGN KEY (automotive_intacct_batch_id) REFERENCES automotive_intacct_batches (id) MATCH SIMPLE ON UPDATE CASCADE ON DELETE RESTRICT);
