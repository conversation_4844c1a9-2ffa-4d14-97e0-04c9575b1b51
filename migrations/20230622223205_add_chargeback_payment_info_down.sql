-- Revert the is_complete value
update automotive_claim_payments
set
	is_complete = false
from automotive_claims
join automotive_claim_payment_checks on 
	automotive_claim_payment_checks.paid_date = '2023-06-15' 
	and automotive_claim_payment_checks.check_number in ('TCA-D9143406E CHARGEBACK','TCA-D9291127E CHARGEBACK')
where
	automotive_claims.id = automotive_claim_payments.automotive_claim_id 
	and automotive_claim_payment_checks.automotive_claim_payments_id = automotive_claim_payments.id
	and automotive_claims.contract_number in ('TCA-D9143406E','TCA-D9291127E')
	and automotive_claims.status = 'Chargeback'
;

-- Rever the status back to WaitingForChargeback
update automotive_claims
set
	status = 'WaitingForChargeback'
from automotive_claim_payments 
join automotive_claim_payment_checks on automotive_claim_payment_checks.automotive_claim_payments_id = automotive_claim_payments.id
where
	automotive_claim_payments.automotive_claim_id = automotive_claims.id
	and automotive_claims.contract_number in ('TCA-D9143406E','TCA-D9291127E')
	and automotive_claims.status = 'Chargeback'
	and automotive_claim_payment_checks.paid_date = '2023-06-15'
	and automotive_claim_payment_checks.check_number in ('TCA-D9143406E CHARGEBACK','TCA-D9291127E CHARGEBACK')
;

-- Delete the check entries previously created
delete from automotive_claim_payment_checks 
where
	paid_date = '2023-06-15'
	and check_number = ''
	and automotive_claim_payments_id in 
	(
		select
			acp.id
		from automotive_claim_payments acp
		join automotive_claims ac on ac.id = acp.automotive_claim_id 
		where 
			ac.contract_number in ('TCA-D9143406E','TCA-D9291127E')
			and ac.status = 'WaitingForChargeback'
	)
;