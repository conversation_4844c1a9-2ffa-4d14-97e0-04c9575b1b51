create table if not exists lwt_claims
(
    id serial not null,
    contract_number varchar(255) not null,
    invoice_number varchar(255) not null,
    vehicle_return_date date,
    vin varchar(255) not null,
    year varchar(12),
    make varchar(50),
    model varchar(50),
    vendor_id varchar(25),
    vendor_name varchar(255),
    vendor_address varchar(255),
    vendor_city varchar(55),
    vendor_state varchar(12),
    vendor_postal_code varchar(12),
    contract_lender varchar(255),
    contract_lender_address varchar(255),
    contract_lender_city varchar(55),
    contract_lender_state varchar(12),
    contract_lender_postal_code varchar(12),
    status varchar(255),
    in_progress boolean default true not null,
    requested_amount numeric(12, 2) default 0 not null,
    approved_amount numeric(12,2) default 0 not null,
    owner_id int not null,
    customer_id int not null,
    date_of_claim_received date,
    finance_contract boolean default false,
    completed_claim_form boolean default false,
    vehicle_condition_report boolean default false,
    vin_plate_images boolean default false,
    wear_and_tear_damage_images boolean default false,
    final_invoice_from_lessor boolean default false,
    lwt_closed_at timestamp,
    created_by_user_id int not null,
    created_at timestamp not null,

    constraint lwt_claims_pk primary key (id),
    constraint lwt_claims_owner_id_fk foreign key (owner_id) references users(id) on update cascade on delete restrict ,
    constraint lwt_claims_customer_id_fk foreign key (customer_id) references customers(id) on update cascade on delete restrict,
    constraint lwt_claims_created_by_user_id foreign key (created_by_user_id) references users(id) on update cascade on delete restrict
);
create index lwt_claims_contract_number on lwt_claims using btree(contract_number);
create index lwt_claims_vendor_id on lwt_claims using btree(vendor_id);
create index lwt_claims_created_at on lwt_claims using btree(created_at);
create index lwt_claims_created_by_user_id on lwt_claims using btree(created_by_user_id);
create index lwt_claims_assigned_to_user_id on lwt_claims using btree(owner_id);

create table if not exists lwt_claim_documents
(
    id serial,
    lwt_claim_id int not null,
    s3_bucket varchar(255) not null,
    file_name varchar(255) not null,
    created_by_user_id int not null,
    created_at timestamp not null,
    document_type_id int not null,
    deleted_at timestamp,
    deleted_by_user_id int,

    constraint lwt_claim_documents_pk primary key (id),
    constraint lwt_claim_documents_lwt_claim_id_fk foreign key (lwt_claim_id) references lwt_claims (id) on update cascade on delete restrict,
    constraint lwt_claim_documents_created_by_user_id_fk foreign key (created_by_user_id) references users (id) on update cascade on delete restrict
);
create index lwt_claim_documents_lwt_claim_id_idx on lwt_claim_documents using btree(lwt_claim_id);
create index lwt_claim_documents_created_at_idx on lwt_claim_documents using btree(created_at);
create index lwt_claim_documents_created_by_user_id on lwt_claim_documents using btree(created_by_user_id);
create index lwt_claim_documents_deleted_by_user_id on lwt_claim_documents using btree(deleted_by_user_id);
create index lwt_claim_documents_document_type_id on lwt_claim_documents using btree(document_type_id);

create table if not exists lwt_claim_document_types
(
    id serial,
    name varchar(255) not null,
    key varchar(255) not null,
    required boolean default false not null,

    constraint lwt_claim_document_types_pk primary key (id)
);

insert into lwt_claim_document_types
(name, key, required)
values ('Finance Contract / Lease Document', 'finance_contract', true),
       ('Completed Claim Form', 'completed_claim_form', true),
       ('Vehicle Condition Report', 'vehicle_condition_report', true),
       ('Vin Plate / Door Sticker', 'vin_plate_images', true),
       ('Wear and Tear Damage', 'wear_and_tear_damage_images', true),
       ('Final Invoice From Lessor', 'final_invoice_from_lessor', true);

create table if not exists lwt_claim_line_items
(
    id serial,
    lwt_claim_id integer,
    description text,
    note text,
    requested_amount numeric(12,2),
    approved_amount numeric(12,2),
    status varchar(255),
    denial_reason_id int,
    images_received boolean default false,
    denial_note text,
    created_at timestamp,
    created_by_user_id int,

    constraint lwt_claim_line_items_pk primary key (id),
    constraint lwt_claim_line_items_created_by_user_id_fk foreign key (created_by_user_id) references users(id) on update cascade on delete restrict,
    constraint lwt_claim_line_items_lwt_claim_id_fk foreign key (lwt_claim_id) references lwt_claims(id) on update cascade on delete restrict
);
create index lwt_claim_line_items_created_at_idx on lwt_claim_line_items using btree(created_at);
create index lwt_claim_line_items_created_by_user_id on lwt_claim_documents using btree(created_by_user_id);
create index lwt_claim_line_items_lwt_claim_id_idx on lwt_claim_line_items using btree(lwt_claim_id);

create table if not exists lwt_claim_payments
(
    id serial,
    authorization_number int,
    lwt_claim_id int,
    check_number int,
    amount numeric(12,2),
    paid_date date,
    batch_key int,
    bill_key int,
    bill_memo varchar(255),
    updated_at timestamp,

    constraint lwt_claim_payments_pk primary key (id),
    constraint lwt_claim_payments_lwt_claim_id foreign key (lwt_claim_id) references lwt_claims(id) on update cascade on delete restrict
);
create index lwt_claim_payments_lwt_claim_id_idx on lwt_claim_payments using btree(lwt_claim_id);

create table if not exists lwt_claim_updates
(
    id serial,
    lwt_claim_id integer,
    updated_by_user_id integer,
    updated_at timestamp,

    constraint lwt_claim_updates_pk primary key (id),
    constraint lwt_claim_updates_updated_by_user_id_fk foreign key (updated_by_user_id) references users(id) on update cascade on delete restrict,
    constraint lwt_claim_updates_lwt_claim_id_fk foreign key (lwt_claim_id) references lwt_claims(id) on update cascade on delete restrict
);
create index if not exists lwt_claim_updates_lwt_claim_id_idx on lwt_claim_updates using btree(lwt_claim_id);
create index if not exists lwt_claim_updates_updated_by_user_id_idx on lwt_claim_updates using btree(updated_by_user_id);