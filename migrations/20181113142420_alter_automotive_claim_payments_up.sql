CREATE TABLE automotive_claim_payment_checks (
  id serial,
  automotive_claim_payments_id integer not null,
  check_amount numeric(12,2) not null,
  check_number varchar(255) not null,
  paid_date timestamp not null,
  updated_at timestamp not null,
  CONSTRAINT automotive_claim_payment_checks_pkey PRIMARY KEY (id),
  CONSTRAINT automotive_claim_payment_checks_automotive_claim_payments_idfkey FOREIGN KEY (automotive_claim_payments_id) REFERENCES automotive_claim_payments (id) ON UPDATE CASCADE ON DELETE RESTRICT);

insert into automotive_claim_payment_checks(automotive_claim_payments_id, check_amount, check_number, paid_date, updated_at)
select id as automotive_claim_payments_id, amount as check_amount, check_number, paid_date, updated_at from automotive_claim_payments where automotive_claim_payments.check_number != '';

alter table automotive_claim_payments
  add column is_complete boolean not null default false,
  add column refund_claim_number integer default 0;


