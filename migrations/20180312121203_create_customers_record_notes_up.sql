create table customers_record_notes(
  id  serial not null,
  customer_id integer not null,
  notes_text varchar(255) not null,
  created_by_user_id  integer not null,
  created_at timestamp not null,

  CONSTRAINT "customers_record_notes_pkey" primary key ("id"),
  CONSTRAINT "customers_record_notes_customer_id_fkey" foreign key ("customer_id") references "customers" ("id") on update cascade on delete restrict,
  CONSTRAINT "customers_record_notes_created_by_user_id_fkey" foreign key ("created_by_user_id") references "users" ("id") on update cascade on delete restrict
);
create index customers_record_notes_customer_id_idx on customers_record_notes (customer_id);
create index customers_record_notes_created_by_user_id_idx on customers_record_notes (created_by_user_id);
create index customers_record_notes_created_at_idx on customers_record_notes (created_at);

