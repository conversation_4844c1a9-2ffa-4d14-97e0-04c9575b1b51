CREATE TABLE automotive_claim_pre_auth_numbers (
  id serial,
  pre_auth_number varchar(255),
  automotive_claim_id integer not null,
  created_at timestamp without time zone not null,
  created_by_user_id integer not null,

  CONSTRAINT automotive_claim_pre_auth_numbers_pkey PRIMARY KEY (id),
  CONSTRAINT automotive_claim_pre_auth_numbers_automotive_claim_id_fkey FOREIGN KEY (automotive_claim_id)
    REFERENCES automotive_claims (id) MATCH SIMPLE ON UPDATE CASCADE ON DELETE RESTRICT,
  CONSTRAINT automotive_claim_pre_auth_numbers_created_by_user_id_fkey FOREIGN KEY (created_by_user_id)
    REFERENCES users (id) MATCH SIMPLE ON UPDATE CASCADE ON DELETE RESTRICT
);
CREATE INDEX automotive_claim_pre_auth_numbers_automotive_claim_id_idx ON automotive_claim_pre_auth_numbers USING btree (automotive_claim_id);
CREATE UNIQUE INDEX automotive_claim_pre_auth_numbers_pre_auth_number_udx ON automotive_claim_pre_auth_numbers USING btree(pre_auth_number);
CREATE UNIQUE INDEX automotive_claim_pre_auth_numbers_automotive_claim_id_udx ON automotive_claim_pre_auth_numbers USING btree(automotive_claim_id);
