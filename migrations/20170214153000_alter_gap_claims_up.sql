CREATE TABLE "gap_claim_contracts" (
  "id" serial NOT NULL,
  "gap_claim_id" integer NOT NULL,
  "contract_number" varchar(255) NOT NULL,
  "contract_code" varchar(255) NOT NULL,
  "contract_name" varchar(255) NOT NULL,
  "contract_flag" boolean NOT NULL DEFAULT FALSE,
  "contract_value" numeric(12,2) NOT NULL,
  "manager_flag" boolean NOT NULL DEFAULT FALSE,
	CONSTRAINT "gap_claim_contracts_pkey" PRIMARY KEY ("id"),
	CONSTRAINT "gap_claim_contracts_gap_claim_id_fkey" FOREIGN KEY ("gap_claim_id") REFERENCES "gap_claims" ("id") ON UPDATE CASCADE ON DELETE RESTRICT
);
