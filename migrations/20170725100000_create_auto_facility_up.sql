CREATE TABLE automotive_facilities (
  id serial,
  facility_code varchar(5) NOT NULL,
  created_at timestamp NOT NULL,
  updated_at timestamp NOT NULL,
  created_by_user_id integer NOT NULL,
  name varchar(255) NOT NULL,
  address varchar(255) NOT NULL,
  city varchar(255) NOT NULL,
  state_code varchar(255) NOT NULL,
  postal_code varchar(255) NOT NULL,
  phone varchar(255) NOT NULL,
  fax varchar(255),
  email varchar(255),
  parts_tax numeric(12,2) NOT NULL DEFAULT 0.00,
  labor_tax numeric(12,2) NOT NULL DEFAULT 0.00,
  labor_rate numeric(12,2) NOT NULL DEFAULT 0.00,
  vendor_id varchar(255),
  contact varchar(255) NOT NULL,
  advisor varchar(255) NOT NULL,
  payment_type varchar(255) NOT NULL,
  pre_auth boolean NOT NULL DEFAULT FALSE,
  pre_auth_limit numeric(12,2) NOT NULL DEFAULT 0.00,

  CONSTRAINT automotive_facilities_pkey PRIMARY KEY (id),
  CONSTRAINT automotive_facilities_created_by_user_id_fkey FOREIGN KEY (created_by_user_id) REFERENCES users (id) MATCH SIMPLE ON UPDATE CASCADE ON DELETE RESTRICT,
  CONSTRAINT automotive_facilities_facility_code_unique UNIQUE (facility_code)
);

CREATE INDEX automotive_facilities_created_by_user_id_idx ON automotive_facilities USING btree (created_by_user_id);
CREATE INDEX automotive_facilities_facility_code_idx ON automotive_facilities USING btree (facility_code);