CREATE TABLE "vta_claims" (
  "id"                                   SERIAL,
  "vin"                                  VA<PERSON>HAR(255)   NOT NULL,
  "contract_number"                      VARCHAR(255)   NOT NULL,
  "is_police_report_available"           BOOLEAN        NOT NULL DEFAULT FALSE,
  "police_report_available_manager_flag" BOOLEAN        NOT NULL DEFAULT FALSE,
  "has_settlement_check"                 BOOLEAN        NOT NULL DEFAULT FALSE,
  "settlement_check_value"               NUMERIC(12, 2) NOT NULL,
  "settlement_check_manager_flag"        BOOLEAN        NOT NULL DEFAULT FALSE,
  "has_original_financing"               BOOLEAN        NOT NULL DEFAULT FALSE,
  "original_financing_value"             NUMERIC(12, 2) NOT NULL DEFAULT 0.0,
  "original_financing_manager_flag"      BOOLEAN        NOT NULL DEFAULT FALSE,
  "has_vta_contract"                     BOOLEAN        NOT NULL DEFAULT FALSE,
  "vta_contract_manager_flag"            BOOLEAN        NOT NULL DEFAULT FALSE,
  "has_insurance_not_recovered"          BO<PERSON>EAN        NOT NULL DEFAULT FALSE,
  "insurance_not_recovered_manager_flag" BOOLEAN        NOT NULL DEFAULT FALSE,
  "status"                               VARCHAR(255)   NOT NULL,
  "denied_reason"                        VARCHAR(255)   NOT NULL,
  "created_by_user_id"                   INTEGER        NOT NULL,
  "date_of_claim_received"               DATE           NOT NULL,
  "date_of_last_in"                      DATE           NOT NULL,
  "owner_id"                             INTEGER        NOT NULL,
  "customer_id"                          INTEGER        NOT NULL,

  CONSTRAINT "vta_claims_pkey" PRIMARY KEY ("id"),
  CONSTRAINT "vta_claims_created_by_user_id_fkey" FOREIGN KEY ("created_by_user_id") REFERENCES "users" ("id") ON UPDATE CASCADE ON DELETE RESTRICT,
  CONSTRAINT "vta_claims_owner_id_fkey" FOREIGN KEY ("owner_id") REFERENCES "users" ("id") ON UPDATE CASCADE ON DELETE RESTRICT,
  CONSTRAINT "vta_claims_customer_id_fkey" FOREIGN KEY ("customer_id") REFERENCES "customers" ("id") ON UPDATE CASCADE ON DELETE RESTRICT
);

CREATE INDEX vta_claims_created_by_user_id_idx
  ON vta_claims USING BTREE (created_by_user_id);
CREATE INDEX vta_claims_owner_id_idx
  ON vta_claims USING BTREE (owner_id);
CREATE INDEX vta_claims_customer_id_idx
  ON vta_claims USING BTREE (customer_id);
CREATE INDEX vta_claims_contract_number_idx
  ON vta_claims USING BTREE (contract_number);
CREATE INDEX vta_claims_vin_idx
  ON vta_claims USING BTREE (vin);

CREATE TABLE "vta_claim_updates" (
  "id"                 SERIAL    NOT NULL,
  "vta_claim_id"       INTEGER   NOT NULL,
  "updated_by_user_id" INTEGER   NOT NULL,
  "updated_at"         TIMESTAMP NOT NULL,
  CONSTRAINT "vta_claim_updates_pkey" PRIMARY KEY ("id"),
  CONSTRAINT "vta_claim_updates_vta_claim_id_fkey" FOREIGN KEY ("vta_claim_id") REFERENCES "vta_claims" ("id") ON UPDATE CASCADE ON DELETE RESTRICT,
  CONSTRAINT "vta_claim_updates_updated_by_user_id_fkey" FOREIGN KEY ("updated_by_user_id") REFERENCES "users" ("id") ON UPDATE CASCADE ON DELETE RESTRICT
);

CREATE INDEX vta_claim_updates_vta_claim_id_idx
  ON vta_claim_updates USING BTREE (vta_claim_id);
CREATE INDEX vta_claim_updates_updated_at_idx
  ON vta_claim_updates USING BTREE (updated_at);
CREATE INDEX vta_claim_updates_updated_by_user_id_idx
  ON vta_claim_updates USING BTREE (updated_by_user_id);

CREATE TABLE "vta_record_notes" (
  "id"                 SERIAL       NOT NULL,
  "vta_claim_id"       INTEGER      NOT NULL,
  "notes_text"         VARCHAR(255) NOT NULL,
  "created_by_user_id" INTEGER      NOT NULL,
  "created_at"         TIMESTAMP    NOT NULL,
  CONSTRAINT "vta_record_notes_pkey" PRIMARY KEY ("id"),
  CONSTRAINT "vta_claim_id_fkey" FOREIGN KEY ("vta_claim_id") REFERENCES "vta_claims" ("id") ON UPDATE CASCADE ON DELETE RESTRICT,
  CONSTRAINT "vta_record_notes_created_by_user_id_fkey" FOREIGN KEY ("created_by_user_id") REFERENCES "users" ("id") ON UPDATE CASCADE ON DELETE RESTRICT
);

CREATE INDEX vta_record_notes_created_by_user_id_idx
  ON vta_record_notes USING BTREE (created_by_user_id);
CREATE INDEX vta_record_notes_created_at_idx
  ON vta_record_notes USING BTREE (created_at);
CREATE INDEX vta_record_notes_vta_claim_id_idx
  ON vta_record_notes USING BTREE (vta_claim_id);

CREATE TABLE "vta_claim_field_notes" (
  "id"                 SERIAL,
  "vta_claim_id"       INTEGER   NOT NULL,
  "field_id"           INTEGER   NOT NULL,
  "notes_text"         VARCHAR(255),
  "created_by_user_id" INTEGER   NOT NULL,
  "created_at"         TIMESTAMP NOT NULL,
  CONSTRAINT "vta_claim_field_notes_pkey" PRIMARY KEY ("id"),
  CONSTRAINT "vta_claim_field_notes_user_id_fkey" FOREIGN KEY ("created_by_user_id") REFERENCES "users" ("id") ON UPDATE CASCADE ON DELETE RESTRICT,
  CONSTRAINT "vta_claim_field_notes_claim_id_fkey" FOREIGN KEY ("vta_claim_id") REFERENCES "vta_claims" ("id") ON UPDATE CASCADE ON DELETE RESTRICT
);
CREATE INDEX vta_claim_field_notes_vta_claim_id_idx
  ON vta_claim_field_notes USING BTREE (vta_claim_id);
CREATE INDEX vta_claim_field_notes_field_id_idx
  ON vta_claim_field_notes USING BTREE (field_id);
CREATE INDEX vta_claim_field_notes_created_by_user_id_idx
  ON vta_claim_field_notes USING BTREE (created_by_user_id);
CREATE INDEX vta_claim_field_notes_created_at_idx
  ON vta_claim_field_notes USING BTREE (created_at);

CREATE TABLE "vta_claim_documents" (
  "id"                 SERIAL       NOT NULL,
  "vta_claim_id"       INTEGER      NOT NULL,
  "s3_bucket"          VARCHAR(255) NOT NULL,
  "file_name"          VARCHAR(255) NOT NULL,
  "created_by_user_id" INTEGER      NOT NULL,
  "created_at"         TIMESTAMP    NOT NULL,
  "field_id"           INTEGER,
  "deleted_at"         TIMESTAMP,
  "deleted_by_user_id" INTEGER,
  CONSTRAINT "vta_claim_documents_pkey" PRIMARY KEY ("id"),
  CONSTRAINT "vta_claim_documents_vta_claim_id_fkey" FOREIGN KEY ("vta_claim_id") REFERENCES "vta_claims" ("id") ON UPDATE CASCADE ON DELETE RESTRICT,
  CONSTRAINT "vta_claim_documents_created_by_user_id_fkey" FOREIGN KEY ("created_by_user_id") REFERENCES "users" ("id") ON UPDATE CASCADE ON DELETE RESTRICT,
  CONSTRAINT "vta_claim_documents_deleted_by_user_id_fkey" FOREIGN KEY ("deleted_by_user_id") REFERENCES "users" ("id") ON UPDATE CASCADE ON DELETE RESTRICT
);

CREATE INDEX vta_claim_documents_vta_claim_id_idx
  ON vta_claim_documents USING BTREE (vta_claim_id);
CREATE INDEX vta_claim_documents_created_by_user_id_idx
  ON vta_claim_documents USING BTREE (created_by_user_id);
CREATE INDEX vta_claim_documents_created_at_idx
  ON vta_claim_documents USING BTREE (created_at);
CREATE INDEX vta_claim_documents_field_id_idx
  ON vta_claim_documents USING BTREE (field_id);



