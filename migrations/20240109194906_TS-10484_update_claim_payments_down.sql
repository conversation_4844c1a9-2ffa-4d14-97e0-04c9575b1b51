CREATE TEMPORARY TABLE tmpClaims (
    contract_number VARCHAR(255) NOT NULL,
    ro VARCHAR(255) NOT NULL
);

INSERT INTO tmpClaims
    (contract_number, ro)
VALUES 
    ('PPM-B9683788', '368001'),
    ('PPM-B9037391', '367985'),
    ('PPM-B9392312', '367993'),
    ('PPM-B9146057', '367969'),
    ('PPM-B9051679', '367944'),
    ('PPM-B9683536', '366871'),
    ('PPM-B9550971', '367932'),
    ('PPM-B8278672', '367909'),
    ('PPM-B8992577', '367190'),
    ('PPM-B9395694', '367883'),
    ('PPM-B9452140', '367904'),
    ('PPM-B9454990', '367919'),
    ('PPM-B9682113', '367881'),
    ('PPM-B9682104', '367862'),
    ('PPM-B9430577', '367876'),
    ('PPM-B9235782', '367908'),
    ('PPM-B9682057', '367874'),
    ('PPM-A4334345', '367877'),
    ('PPM-B9199302', '367790'),
    ('PPM-B9311857E', '367795'),
    ('PPM-B9525620E', '367776'),
    ('PPM-B9503925', '367552'),
    ('PPM-B9681506', '367744'),
    ('PPM-B9447810', '367826'),
    ('PPM-B9074551', '367794'),
    ('PPM-B8974002', '367835'),
    ('PPM-B9680950', '367836'),
    ('PPM-B8508730', '367742'),
    ('PPM-B9469866E', '367752'),
    ('PPM-B9175527', '367747'),
    ('PPM-B8860447', '367807'),
    ('PPM-B9591137', '367781'),
    ('PPM-B9485919', '367791'),
    ('PPM-B9501990', '367802'),
    ('PPM-B9589399E', '367810'),
    ('PPM-B9680728', '367748'),
    ('PPM-B9680637', '367808'),
    ('PPM-B9510658', '367749'),
    ('PPM-B9544792', '367784'),
    ('PPM-B9075815', '367774'),
    ('PPM-B9502780', '367759'),
    ('PPM-B9680380', '367751'),
    ('PPM-B9439656E', '367778'),
    ('PPM-B9519512', '367763'),
    ('PPM-B8720892', '367743'),
    ('PPM-B9680170', '367757'),
    ('PPM-A8001499', '367745'),
    ('PPM-B9585458', '367631'),
    ('PPM-B9456795E', '367484'),
    ('PPM-B9590554', '367720'),
    ('PPM-B9396599', '367696'),
    ('PPM-B9680105', '367440'),
    ('PPM-B9680104', '367664'),
    ('PPM-A7160267', '367629'),
    ('PPM-B9680102', '367703'),
    ('PPM-B9260307E', '367673'),
    ('PPM-B9588324', '367674'),
    ('PPM-B9304280E', '367645'),
    ('PPM-B9304586', '367632'),
    ('PPM-B9233012E', '367731'),
    ('PPM-B8920750', '367726'),
    ('PPM-B9488296', '367618'),
    ('PPM-B9102486', '367619'),
    ('PPM-B9679725', '367623'),
    ('PPM-B9489392', '367491'),
    ('PPM-B9484822', '367654'),
    ('PPM-B9393963E', '367646'),
    ('PPM-B9394467E', '367658'),
    ('PPM-B8914311', '367700'),
    ('PPM-B9340099', '367698'),
    ('PPM-B9442990E', '367721'),
    ('PPM-B8816928', '367725'),
    ('PPM-B8612635', '367630'),
    ('PPM-B9679543', '367665'),
    ('PPM-B9665355', '366648'),
    ('PPM-B9432578', '367657'),
    ('PPM-B9213969', '367686'),
    ('PPM-B9679404', '367642'),
    ('PPM-B9679275', '367695'),
    ('PPM-B9518874E', '367641'),
    ('PPM-A7949738', '367634'),
    ('PPM-B9309342', '367377'),
    ('PPM-B9395714', '367622'),
    ('PPM-B9355383', '367289'),
    ('PPM-B9013288', '367504'),
    ('PPM-B9386476E', '367537'),
    ('PPM-B9286828', '367307'),
    ('PPM-B9678704', '367506'),
    ('PPM-B9432634', '367508'),
    ('PPM-B9584358E', '367557'),
    ('PPM-B8592200', '367517'),
    ('PPM-B9678681', '367529'),
    ('PPM-B9678673', '367591'),
    ('PPM-B9548779', '367514'),
    ('PPM-B9581872', '367539'),
    ('PPM-B9290471E', '367563'),
    ('PPM-B9678367', '367569'),
    ('PPM-B9311027', '367571'),
    ('PPM-B9501914', '367498'),
    ('PPM-A6912261', '367485'),
    ('PPM-B9357462', '367523'),
    ('PPM-B9504495', '367497'),
    ('PPM-B9120183', '367556'),
    ('PPM-B9504527', '367500')
;

-- Delete the Payment Check Rows for just the Claims that had the Payment Check added for from the up migration script. There were already payment check rows with the same check number.
delete from automotive_claim_payment_checks 
where 
    check_number = '0072065'
    and automotive_claim_payments_id IN (
        select 
            acp.id 
        from automotive_claim_payments acp
        join automotive_claims ac on ac.id = acp.automotive_claim_id
        join tmpClaims tc on tc.contract_number = ac.contract_number
        where
            ac.ro = tc.ro
    )
;

-- Reset the columns that were updated from the up migration script.
update automotive_claim_payments acp
set
    batch_key = null,
    bill_key = null,
    bill_memo = '',
    updated_at = null,
    intacct_bill_number = null,
    is_complete = false
from automotive_claims ac
join tmpClaims tc on tc.contract_number = ac.contract_number and tc.ro = ac.ro
where
    acp.automotive_claim_id = ac.id
;

-- Change the claim status back to Waiting for Check of the claims that were previously updated from the up migration script.
update automotive_claims ac
set 
    status = 'WaitingForCheck' 
from tmpClaims tc
where 
    tc.contract_number = ac.contract_number
    and tc.ro = ac.ro
;

DROP TABLE IF EXISTS tmpClaims;