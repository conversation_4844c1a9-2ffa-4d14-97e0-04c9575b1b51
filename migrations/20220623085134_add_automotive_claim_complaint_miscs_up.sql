create table if not exists automotive_claim_complaint_miscs
(
    id serial not null
        constraint automotive_claim_complaint_miscs_pkey
            primary key,
    automotive_claim_complaint_id integer
        constraint automotive_claim_complaint_miscs_complaint_id_fkey
            references automotive_claim_complaints
            on update cascade on delete restrict,
    description varchar(255),
    requested numeric(12,2) default 0.0,
    approved numeric(12,2) default 0.0,
    notes varchar(255)
);
create index if not exists automotive_claim_complaint_misc_automotive_claim_complaint_id
    on automotive_claim_complaint_miscs (automotive_claim_complaint_id);

alter table automotive_claim_complaints
add column miscs_total numeric(12, 2) not null default 0;