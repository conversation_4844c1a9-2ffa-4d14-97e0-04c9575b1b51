CREATE TABLE "automotive_claim_documents" (
  "id" serial NOT NULL,
  "automotive_claim_id" integer NOT NULL,
  "s3_bucket" varchar(255) NOT NULL,
  "file_name" varchar(255) NOT NULL,
  "created_by_user_id" integer NOT NULL,
  "created_at" timestamp NOT NULL,
  "deleted_at" timestamp,
  "deleted_by_user_id" integer,
  CONSTRAINT "automotive_claim_documents_pkey" PRIMARY KEY ("id"),
  CONSTRAINT "automotive_claim_documents_automotive_claim_id_fkey" FOREIGN KEY ("automotive_claim_id") REFERENCES "automotive_claims" ("id") ON UPDATE CASCADE ON DELETE RESTRICT,
  CONSTRAINT "automotive_claim_documents_created_by_user_id_fkey" FOREIGN KEY ("created_by_user_id") REFERENCES "users" ("id") ON UPDATE CASCADE ON DELETE RESTRICT,
  CONSTRAINT "automotive_claim_documents_deleted_by_user_id_fkey" FOREIG<PERSON> KEY ("deleted_by_user_id") REFERENCES "users" ("id") ON UPDATE CASCADE ON DELETE RESTRICT
);

CREATE INDEX automotive_claim_documents_automotive_claim_id_idx ON automotive_claim_documents USING btree (automotive_claim_id);
CREATE INDEX automotive_claim_documents_created_by_user_id_idx ON automotive_claim_documents USING btree (created_by_user_id);