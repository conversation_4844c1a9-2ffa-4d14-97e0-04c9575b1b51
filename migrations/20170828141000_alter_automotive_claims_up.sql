ALTER TABLE automotive_claims
  ADD COLUMN reassigned_owner_id INTEGER,
  ADD COLUMN reassignment_status VARCHAR(10),
  ADD CONSTRAINT automotive_claims_reassigned_owner_fkey FOREIGN KEY (reassigned_owner_id) REFERENCES users (id) MATCH SIMPLE ON UPDATE CASCADE ON DELETE RESTRICT;

CREATE INDEX automotive_claims_reassigned_owner_idx ON automotive_claims USING btree (reassigned_owner_id);

CREATE TABLE automotive_facility_zones (
  id SERIAL,
  name VARCHAR(10) NOT NULL,
  zone_user_id INTEGER NOT NULL,
  updated_at timestamp NOT NULL,
  updated_by_user_id INTEGER NOT NULL,

  CONSTRAINT automotive_facility_zones_pkey PRIMARY KEY (id),
  CONSTRAINT automotive_facility_zones_zone_user_id_fkey FOREIGN KEY (zone_user_id) REFERENCES users (id) MATCH SIMPLE ON UPDATE CASCADE ON DELETE RESTRICT,
  CONSTRAINT automotive_facility_zones_updated_by_user_id_fkey FOREIGN KEY (updated_by_user_id) REFERENCES users (id) MATCH SIMPLE ON UPDATE CASCADE ON DELETE RESTRICT
);

CREATE INDEX automotive_facility_zones_zone_user_id_idx ON automotive_facility_zones USING btree (zone_user_id);
CREATE INDEX automotive_facility_zones_updated_by_user_id_idx on automotive_facility_zones USING btree (updated_by_user_id);

ALTER TABLE automotive_facilities
  ADD COLUMN zone_id INTEGER,
  ADD CONSTRAINT automotive_facilities_zone_id_fkey FOREIGN KEY (zone_id) REFERENCES automotive_facility_zones (id) MATCH SIMPLE ON UPDATE CASCADE ON DELETE RESTRICT;

CREATE INDEX automotive_facilities_zone_id_idx ON automotive_facilities USING btree (zone_id);

ALTER TABLE users
  ADD COLUMN time_off_start date,
  ADD COLUMN time_off_end date;