CREATE TABLE "gap_claim_documents" (
  "id" serial NOT NULL,
  "gap_claim_id" integer NOT NULL,
  "s3_bucket" varchar(255) NOT NULL,
  "file_name" varchar(255) NOT NULL,
  "created_by_user_id" integer NOT NULL,
  "created_at" timestamp NOT NULL,
  CONSTRAINT "gap_claim_documents_pkey" PRIMARY KEY ("id"),
  CONSTRAINT "gap_claim_documents_gap_claim_id_fkey" FOREIGN KEY ("gap_claim_id") REFERENCES "gap_claims" ("id") ON UPDATE CASCADE ON DELETE RESTRICT,
  CONSTRAINT "gap_claim_documents_created_by_user_id_fkey" FOREIGN KEY ("created_by_user_id") REFERENCES "users" ("id") ON UPDATE CASCADE ON DELETE RESTRICT
);