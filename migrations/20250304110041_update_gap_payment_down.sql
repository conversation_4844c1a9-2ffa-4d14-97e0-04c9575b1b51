-- GAP-C9135051
delete from gap_voided_transactions
where gap_claim_id = (select id from gap_claims where contract_number='GAP-C9135051') and check_number = '27253';

delete from record_notes
where gap_claim_id = (select id from gap_claims where contract_number='GAP-C9135051')
    and notes_text = 'The check with number: 27253 Amount: 16902.65 Date: 1/13/2025 is voided';
  
delete from gap_claim_payments
where gap_claim_id = (select id from gap_claims where contract_number='GAP-C9135051') and check_number = '27258';

--  GAP-D9441083FIFSE
delete from gap_voided_transactions
where gap_claim_id = (select id from gap_claims where contract_number='GAP-D9441083FIFSE') and check_number = '27253';

delete from record_notes
where gap_claim_id = (select id from gap_claims where contract_number='GAP-D9441083FIFSE')
    and notes_text = 'The check with number: 27253 Amount: 16902.65 Date: 1/13/2025 is voided';

delete from gap_claim_payments
where gap_claim_id = (select id from gap_claims where contract_number='GAP-D9441083FIFSE') and check_number = '27257';

create unique index  gap_voided_transactions_check_number on gap_voided_transactions using btree(check_number);