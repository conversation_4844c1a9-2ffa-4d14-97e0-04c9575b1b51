package pdftk

import (
	"fmt"
	"os/exec"

	"github.com/pkg/errors"
)

// Multistamp receives PDF filename for input file in pdftk, the filename of the pdf to stamp and filename for output
func Multistamp(pdfFilename string, stampingFilename string, outputFilename string) error {
	cmd := exec.Command("pdftk", pdfFilename, "multistamp", stampingFilename, "output", outputFilename)
	b, err := cmd.CombinedOutput()
	if err != nil {
		return errors.Wrap(err, fmt.Sprintf("error executing pdftk fill_form: %s", string(b)))
	}

	return nil
}
