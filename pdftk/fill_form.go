package pdftk

import (
	"fmt"
	"io/ioutil"
	"os"
	"os/exec"
	"phizz/xfdf"

	"github.com/pkg/errors"
)

// FillForm receives PDF filename for input file in pdftk and data that will be xfdf formatted for fill_form in pdftk
func FillForm(pdfFilename string, xfdfData map[string]string, outputFilename string) error {
	b, err := xfdf.Format(xfdfData)
	if err != nil {
		return errors.Wrap(err, "error formatting xfdf for pdftk fill_form")
	}

	f, err := ioutil.TempFile("", "pdftk-fill-form")
	if err != nil {
		return errors.Wrap(err, "error setting up temp file for pdftk fill_form input file")
	}
	defer func() { _ = os.Remove(f.Name()) }()

	_, err = f.Write(b)
	if err != nil {
		return errors.Wrap(err, "error writing temp file for pdftk fill_form input file")
	}

	cmd := exec.Command("pdftk", pdfFilename, "fill_form", f.Name(), "output", outputFilename, "flatten")
	b, err = cmd.CombinedOutput()
	if err != nil {
		return errors.Wrap(err, fmt.Sprintf("error executing pdftk fill_form: %s", string(b)))
	}

	return nil
}
