package pdftk

import (
	"os/exec"

	"github.com/pkg/errors"
)

// <PERSON> receives a slice of PDF filenames to merge into one PDF and return those bytes.
func Cat(mergingFilenames ...string) ([]byte, error) {
	if len(mergingFilenames) < 2 {
		return nil, errors.New("no reason to catenate PDF files if there are not two or more")
	}
	commandArgs := append(mergingFilenames, "output", "-")
	cmd := exec.Command("pdftk", commandArgs...)
	return cmd.CombinedOutput()
}

// CatPages receives a PDF filename and pages
func CatPages(filename string, outputFilename string, pages ...string) error {
	commandArgs := []string{filename, "cat"}
	commandArgs = append(commandArgs, pages...)
	commandArgs = append(commandArgs, "output", outputFilename)
	cmd := exec.Command("pdftk", commandArgs...)
	b, err := cmd.CombinedOutput()
	if err != nil {
		return errors.Wrapf(err, "error executing pdftk cat: %s", string(b))
	}
	return nil
}
