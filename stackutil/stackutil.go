package stackutil

import (
	"fmt"
	"os"
	"runtime"
	"strings"
)

// Frame is a single line of executed code in a Stack.
type Frame struct {
	Filename string `json:"filename"`
	Method   string `json:"method"`
	Line     int    `json:"lineno"`
}

// Stack represents a stacktrace as a slice of Frames.
type Stack []Frame

// BuildStack builds a full stacktrace for the current execution location.
func BuildStack(skip int) Stack {
	stack := make(Stack, 0)

	for i := skip; ; i++ {
		pc, file, line, ok := runtime.Caller(i)
		if !ok {
			break
		}
		stack = append(stack, Frame{file, functionName(pc), line})
	}

	return stack
}

func functionName(pc uintptr) string {
	fn := runtime.FuncForPC(pc)
	if fn == nil {
		return "???"
	}
	name := fn.Name()
	end := strings.LastIndex(name, string(os.PathSeparator))
	return name[end+1 : len(name)]
}

// String returns the stack as a human viewable string
func (s Stack) String() string {
	var strs []string
	for _, f := range s {
		if !strings.Contains(f.Filename, "vendor/") {
			strs = append(strs, fmt.Sprintf("%s %s:%d", f.Filename, f.Method, f.Line))
		}
	}
	return strings.Join(strs, "\n")
}
