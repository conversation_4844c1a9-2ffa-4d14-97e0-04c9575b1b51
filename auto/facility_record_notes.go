package auto

import (
	"github.com/jmoiron/sqlx"
	"github.com/pkg/errors"
	"time"
)

type facilityNotePayload struct {
	ID              int       `json:"-" db:"id"`
	FacilityID      int       `json:"facility_id" db:"facility_id"`
	NotesText       string    `json:"notes_text" db:"notes_text"`
	CreatedAt       time.Time `json:"created_at" db:"created_at"`
	CreatedByUserID int       `json:"created_by_user_id" db:"created_by_user_id"`
}

func insertFacilityNote(recordNote *facilityNotePayload, tx *sqlx.Tx) (int, error) {
	insertQuery := `insert into automotive_facility_record_notes (created_at,facility_id,notes_text,created_by_user_id)
			values (now() at time zone 'utc', :facility_id,:notes_text, :created_by_user_id) returning id`
	id := 0

	var stmt *sqlx.NamedStmt
	var err error
	stmt, err = tx.PrepareNamed(insertQuery)
	if err != nil {
		return id, errors.Wrap(err, "An error occurred in PrepareNamed function while adding RecordNote.")
	}

	defer func() { _ = stmt.Close() }()

	err = stmt.Get(&id, recordNote)
	if err != nil {
		return id, errors.Wrap(err, "An error occurred while trying to add the RecordNote to the database.")
	}

	return id, err
}
