package auto

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"phizz/db"
	"phizz/handlers"

	"github.com/go-chi/chi"
	"github.com/jmoiron/sqlx"
	"github.com/pkg/errors"
)

// RecordNotePayload structure for record note
type RecordNotePayload struct {
	AutomotiveClaimID int       `json:"automotive_claim_id" db:"automotive_claim_id"`
	IsManual          bool      `json:"is_manual" db:"is_manual"`
	NotesText         string    `json:"notes_text" db:"notes_text"`
	CreatedAt         time.Time `json:"created_at" db:"created_at"`
	CreatedByUserID   int       `json:"created_by_user_id" db:"created_by_user_id"`
}

// RecordNoteCreate creates a new record note in auto claim
func RecordNoteCreate(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	ctx := req.Context()
	recordNote, err := recordNoteFromReq(req)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(nil, "Malformed Record Note data for create.", nil)
	}

	cleanRecordNote(recordNote)
	formErrors, err := validateRecordNote(recordNote)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(errors.Wrap(err, "Error validating Record Note"),
			"An error occurred validating the form values.", nil)
	}

	if len(formErrors) > 0 {
		return http.StatusBadRequest, handlers.ErrorMessage(nil, "Form validations errors.",
			map[string]interface{}{"errors": formErrors})
	}

	recordNote.CreatedByUserID = user.ID

	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error beginning transaction for Automotive claim create", nil)
	}
	noteID, err := InsertRecordNote(ctx, recordNote, tx)
	if err != nil {
		_ = tx.Rollback()
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error inserting RecordNote", nil)
	}

	err = tx.Commit()
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error committing transaction for inserting RecordNote", nil)
	}

	return http.StatusOK, map[string]interface{}{"record_note_id": noteID}
}

// InsertRecordNote records note for claim
func InsertRecordNote(ctx context.Context, recordNote *RecordNotePayload, tx *sqlx.Tx) (int, error) {
	insertQuery := `insert into automotive_record_notes (is_manual,created_at,automotive_claim_id,notes_text,created_by_user_id)
	 values (:is_manual,now() at time zone 'utc',:automotive_claim_id,:notes_text,:created_by_user_id) returning id`
	id := 0

	var stmt *sqlx.NamedStmt
	var err error
	if tx != nil {
		stmt, err = tx.PrepareNamedContext(ctx, insertQuery)
	} else {
		stmt, err = db.Get().PrepareNamedContext(ctx, insertQuery)
	}
	if err != nil {
		return id, errors.Wrap(err, "An error occurred in PrepareNamed function while adding RecordNote.")
	}
	defer func() { _ = stmt.Close() }()

	err = stmt.GetContext(ctx, &id, recordNote)
	if err != nil {
		return id, errors.Wrap(err, "An error occurred while trying to add the RecordNote to the database.")
	}

	return id, err
}

// RecordNoteIndex returns a list of record notes for given contract number
func RecordNoteIndex(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	ctx := req.Context()

	autoClaimID := chi.URLParam(req, "automotive_claim_id")
	var recordNotes []struct {
		AutomotiveClaimID int       `json:"automotive_claim_id" db:"automotive_claim_id"`
		NotesText         string    `json:"notes_text" db:"notes_text"`
		CreatedAt         time.Time `json:"created_at" db:"created_at"`
		CreatedByUserID   int       `json:"created_by_user_id" db:"created_by_user_id"`
		UserName          string    `json:"user_name" db:"user_name"`
		FirstName         string    `json:"first_name" db:"first_name"`
		LastName          string    `json:"last_name" db:"last_name"`
	}

	var args []interface{}
	args = append(args, autoClaimID)

	var chargeBackClaimID int
	query := `select id from automotive_claims where parent_claim_id = $1`
	err := db.Get().GetContext(ctx, &chargeBackClaimID, query, autoClaimID)
	if err != nil && err != sql.ErrNoRows {
		err = errors.Wrap(err, "Database error getting record notes lists")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting record notes lists data", nil)
	}

	whereClause := `automotive_claim_id = $1 and is_manual = $2`
	orderOffsetClause := ` order by created_at desc limit $3 offset $4`
	if chargeBackClaimID > 0 {
		whereClause = `(automotive_claim_id = $1 or automotive_claim_id = $2) and is_manual = $3`
		orderOffsetClause = ` order by created_at desc limit $4 offset $5`
		args = append(args, chargeBackClaimID)
	}

	autoNotesQuery := `select automotive_record_notes.automotive_claim_id, automotive_record_notes.notes_text, 
		automotive_record_notes.created_at, automotive_record_notes.created_by_user_id, first_name, last_name, 
		first_name || ' ' || last_name as user_name
	from automotive_record_notes
		join users on (automotive_record_notes.created_by_user_id = users.id)
		join automotive_claims claims on automotive_record_notes.automotive_claim_id = claims.id
	where ` + whereClause

	custFacilityNotesQuery := ` union
		select automotive_claims.id, customers_record_notes.notes_text,
			customers_record_notes.created_at, customers_record_notes.created_by_user_id, first_name, last_name,
			first_name || ' ' || last_name as user_name
		from customers_record_notes
			join automotive_claims on customers_record_notes.customer_id = automotive_claims.customer_id
			join users on (customers_record_notes.created_by_user_id = users.id)
		where automotive_claims.id = $1`

	var listQuery string
	var countQuery string
	if req.FormValue("is_manual") == "true" {
		args = append(args, true)
		listQuery = autoNotesQuery + orderOffsetClause
		countQuery = fmt.Sprintf("select count(*) from (%s) as co", autoNotesQuery)
	} else {
		args = append(args, false)
		listQuery = autoNotesQuery + custFacilityNotesQuery + orderOffsetClause
		countQuery = fmt.Sprintf("select count(*) from (%s %s) as co", autoNotesQuery, custFacilityNotesQuery)
	}

	args = append(args, handlers.PerPageEntries)
	args = append(args, (handlers.GetPage(req)-1)*handlers.PerPageEntries)

	err = db.Get().SelectContext(ctx, &recordNotes, listQuery, args...)
	if err != nil {
		err = errors.Wrap(err, "Database error getting record notes lists")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting record notes lists data", nil)
	}

	count := 0
	if chargeBackClaimID > 0 {
		err = db.Get().GetContext(ctx, &count, countQuery, args[0], args[1], args[2])
	} else {
		err = db.Get().GetContext(ctx, &count, countQuery, args[0], args[1])
	}
	if err != nil {
		err = errors.Wrap(err, "Database error getting record notes lists count")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting record notes lists count", nil)
	}
	return http.StatusOK, map[string]interface{}{"record_notes": recordNotes, "count": count}
}

func recordNoteFromReq(req *http.Request) (*RecordNotePayload, error) {
	recordNote := RecordNotePayload{}

	dec := json.NewDecoder(req.Body)
	err := dec.Decode(&recordNote)

	return &recordNote, errors.Wrap(err, "decoding RecordNote request failed")
}

// cleanRecordNote cleans up leading and trailing white-space etc...
func cleanRecordNote(recordNote *RecordNotePayload) {
	recordNote.NotesText = strings.TrimSpace(recordNote.NotesText)
}

// validateRecordNote validates a NewClaim record for correctness
func validateRecordNote(recordNote *RecordNotePayload) (map[string]string, error) {
	formErrors := map[string]string{}

	if recordNote.AutomotiveClaimID == 0 {
		formErrors["automotive_claim_id"] = "Automotive claim id is required"
	}
	if recordNote.NotesText == "" {
		formErrors["automotive_record_notes"] = "Notes text is required"
	}
	if recordNote.CreatedByUserID == 0 {
		formErrors["automotive_user_id"] = "Created by user id is required"
	}
	return formErrors, nil
}
