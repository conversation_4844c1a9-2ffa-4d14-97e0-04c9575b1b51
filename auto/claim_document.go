package auto

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"phizz/conf"
	"phizz/db"
	"phizz/handlers"
	"phizz/s3util"
	"phizz/util"

	"github.com/go-chi/chi"
	newrelic "github.com/newrelic/go-agent"
	"github.com/pkg/errors"
)

type documentPayload struct {
	ID                      int    `json:"id" db:"id"`
	AutomotiveClaimID       int    `json:"automotive_claim_id" db:"automotive_claim_id"`
	FileContent             string `json:"file_content" db:"-"`
	CreatedByUserID         int    `json:"created_by_user_id" db:"created_by_user_id"`
	FileName                string `json:"file_name" db:"file_name"`
	FileType                string `json:"file_type" db:"file_type"`
	S3Bucket                string `json:"s3_bucket" db:"s3_bucket"`
	DocumentType            string `json:"document_type" db:"document_type"`
	IsInspectionAttachments bool   `json:"is_inspection_attachments"`
}

func (p documentPayload) validate() (map[string]string, error) {
	v := map[string]string{}
	if p.CreatedByUserID == 0 {
		v["created_by_user_id"] = "CreatedByUserID is required"
	}
	if p.AutomotiveClaimID == 0 {
		v["automotive_claim_id"] = "Auto claim ID is required."
	}
	if len(p.FileName) < 1 {
		v["file_name"] = "File name is required."
	}
	if len(p.FileType) < 1 {
		v["file_type"] = "File type is required."
	} else if !handlers.IsValidFileType(p.FileType) {
		v["file_type"] = "File type is not valid"
	}
	if len(p.FileContent) < 1 {
		v["file_content"] = "File content is required"
	} else if len(p.FileContent) > conf.Get().AWS.MaxSize {
		v["file_content"] = "File content size is more than " + strconv.Itoa(conf.Get().AWS.MaxSize)
	}

	return v, nil
}

// SaveDocument saves document to s3
func SaveDocument(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	ctx := req.Context()
	documentPayload, err := getDocumentFromPayload(req.Body)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Bad request", nil)
	}

	documentPayload.clean()
	documentPayload.CreatedByUserID = user.ID
	formErrs, err := documentPayload.validate()
	if err != nil {
		err = errors.Wrap(err, "error validating document")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error during validation", nil)
	}
	if len(formErrs) > 0 {
		return http.StatusBadRequest, map[string]interface{}{
			"validation_errors": formErrs,
		}
	}
	txn := w.(newrelic.Transaction)
	documentID, err := saveDocument(ctx, &documentPayload, txn, user)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error in saving document", nil)
	}
	return http.StatusOK, map[string]interface{}{"automotive_claim_document": documentID}

}

func saveDocument(ctx context.Context, documentPayload *documentPayload, txn newrelic.Transaction, user db.User) (int, error) {
	const errTxt = "Error saving Document"

	contractNumber := ""
	documentID := 0
	err := db.Get().GetContext(ctx, &contractNumber, "select contract_number from automotive_claims where id = $1", documentPayload.AutomotiveClaimID)
	if err != nil {
		if err == sql.ErrNoRows {
			return documentID, errors.Wrap(err, "The Automotive claim was not found")
		}
		return documentID, errors.Wrap(err, "Error loading Automotive claim from database.")
	}

	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		err = errors.Wrap(err, "error starting document upload transaction")
		return documentID, errors.Wrap(err, errTxt)
	}

	if documentPayload.IsInspectionAttachments {
		documentPayload.DocumentType = db.ClaimDocumentTypeInspectionDocument
	}

	insertQuery := `insert into automotive_claim_documents (automotive_claim_id, s3_bucket, file_name, created_by_user_id, created_at, document_type) values
	 (:automotive_claim_id, :s3_bucket, :file_name, :created_by_user_id, now() at time zone 'utc', :document_type) returning id`

	if len(documentPayload.FileContent) > 0 {
		bucket := s3util.Bucket()
		documentPayload.S3Bucket = bucket
		name := "automotive-claims/" + contractNumber + "/" + documentPayload.FileName + documentPayload.FileType
		documentPayload.FileName = name
		data, err := base64.StdEncoding.DecodeString(documentPayload.FileContent)
		if err != nil {
			_ = tx.Rollback()
			err = errors.Wrap(err, "error base64 decoding file data")
			return documentID, errors.Wrap(err, errTxt)
		}
		err = s3util.Put(txn, bytes.NewReader(data), s3util.DefaultRegion, documentPayload.S3Bucket, documentPayload.FileName)
		if err != nil {
			_ = tx.Rollback()
			err = errors.Wrap(err, "error uploading document file to s3")
			return documentID, errors.Wrap(err, errTxt)
		}
	}

	stmt, err := tx.PrepareNamedContext(ctx, insertQuery)
	if err != nil {
		_ = tx.Rollback()
		err = errors.Wrap(err, "error creating document creating statement")
		return documentID, errors.Wrap(err, errTxt)
	}
	defer func() { _ = stmt.Close() }()

	err = stmt.GetContext(ctx, &documentID, documentPayload)
	if err != nil {
		_ = tx.Rollback()
		err = errors.Wrap(err, "error inserting document form")
		return documentID, errors.Wrap(err, errTxt)
	}

	// Add note on successful new file attachment
	note := RecordNotePayload{}
	note.AutomotiveClaimID = documentPayload.AutomotiveClaimID
	note.IsManual = false
	note.CreatedByUserID = user.ID
	note.NotesText = "The file " + documentPayload.FileName + " was attached."
	_, err = InsertRecordNote(ctx, &note, tx)
	if err != nil {
		_ = tx.Rollback()
		return http.StatusInternalServerError, errors.Wrap(err, "Failed to add note")
	}

	err = tx.Commit()
	if err != nil {
		err = errors.Wrap(err, "error committing document transaction")
		return documentID, errors.Wrap(err, errTxt)
	}

	return documentID, nil
}

func (p *documentPayload) clean() {
	s := strings.TrimSpace
	p.FileName = s(p.FileName)
	p.FileType = s(p.FileType)
}

func getDocumentFromPayload(reader io.Reader) (documentPayload, error) {
	var p documentPayload

	dec := json.NewDecoder(reader)
	err := dec.Decode(&p)
	if err != nil {
		err = errors.Wrap(err, "error decoding document payload")
		return p, err
	}

	return p, nil
}

// DocumentDownload returns a pre-signed S3 URL for a the document
func DocumentDownload(w http.ResponseWriter, req *http.Request, user db.User) {
	id, err := strconv.Atoi(chi.URLParam(req, "id"))
	if err != nil {
		fmt.Fprint(w, "Bad request. Could not read document ID.")
	}

	cf, err := getClaimFileDetails(id)
	if err != nil {
		fmt.Fprint(w, "Error in downloading document")
		handlers.ReportError(req, err)
		return
	}

	reverseProxy := s3util.GetS3ReverseProxy()
	signedURL, err := reverseProxy.GetSecureURL(
		cf.Region, cf.Bucket, url.PathEscape(cf.Key),
		url.PathEscape(cf.FileName), cf.ContentType, user,
		time.Minute*conf.Get().S3ReverseProxy.DefaultLinkTimeoutMinutes)
	if err != nil {
		fmt.Fprint(w, "Error in downloading document")
		handlers.ReportError(req, err)
		return
	}

	http.Redirect(w, req, signedURL, http.StatusTemporaryRedirect)
}

type claimFileDetails struct {
	Bucket      string `db:"s3_bucket"`
	Key         string `db:"file_name"`
	FileName    string
	Region      string
	ContentType string
}

func getClaimFileDetails(documentID int) (claimFileDetails, error) {
	var cf claimFileDetails
	cf.Region = s3util.DefaultRegion

	err := db.Get().Get(&cf, `select s3_bucket, file_name from automotive_claim_documents where deleted_at is null and id = $1`, documentID)
	if err != nil {
		if err == sql.ErrNoRows {
			return claimFileDetails{}, errors.Wrap(err, "The document not found")
		}
		return claimFileDetails{}, errors.Wrap(err, "error loading document data for download")
	}

	_, cf.FileName = filepath.Split(cf.Key)
	extn := filepath.Ext(cf.Key)
	cf.ContentType = util.ContentTypeByExtension(extn)

	return cf, nil
}

// documentDownload returns a pre-signed S3 URL for a the document
func documentDownload(documentID int) (string, error) {
	cf, err := getClaimFileDetails(documentID)
	if err != nil {
		return "", errors.WithMessage(err, "error making document presigned download URL")

	}

	url, err := s3util.PresignedURL(cf.Region, cf.Bucket, cf.Key, cf.FileName, cf.ContentType)
	if err != nil {
		return "", errors.WithMessage(err, "error making document presigned download URL")

	}

	return url, nil
}

// DocumentDelete deletes S3 object
func DocumentDelete(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	id, err := strconv.Atoi(chi.URLParam(req, "id"))
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Bad request", nil)
	}

	err = documentDelete(id, user.ID)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Could not delete document", nil)
	}

	return http.StatusOK, map[string]interface{}{"automotive_claim_document": id}
}

// documentDelete
func documentDelete(documentID, userID int) error {
	const errTxt = "Error deleting Document"

	documentData := struct {
		AutomotiveClaimID int    `db:"automotive_claim_id"`
		FileName          string `db:"file_name"`
		FieldID           int    `db:"field_id"`
	}{}
	err := db.Get().Get(&documentData, `select automotive_claim_id, file_name from automotive_claim_documents where deleted_at is null and id = $1`, documentID)
	if err != nil {
		if err == sql.ErrNoRows {
			return errors.Wrap(err, "The document not found")
		}
		return errors.Wrap(err, "error loading document data for delete")
	}

	tx, err := db.Get().Beginx()
	if err != nil {
		err = errors.Wrap(err, "error starting document delete transaction")
		return errors.Wrap(err, errTxt)
	}

	// delete document
	query := `update automotive_claim_documents set deleted_at=now() at time zone 'utc', deleted_by_user_id=$1 where id = $2`
	_, err = tx.Exec(query, userID, documentID)
	if err != nil {
		_ = tx.Rollback()
		return errors.Wrap(err, "Error deleting document")
	}

	err = tx.Commit()
	if err != nil {
		err = errors.Wrap(err, "error committing document transaction")
		return errors.Wrap(err, errTxt)
	}

	return nil
}

type claimDocument struct {
	ID       int    `db:"id" json:"id"`
	FileName string `db:"file_name" json:"file_name"`
}

func documents(ctx context.Context, claimID int, getCCDocuments bool) ([]claimDocument, error) {
	exists, err := claimExists(claimID)
	if err != nil {
		return nil, errors.Wrap(err, "Error getting Automotive claim from database")
	}
	if !exists {
		return nil, errors.Wrap(err, "Automotive claim does not exist")
	}

	documentList := []claimDocument{}
	query := `select id, file_name from automotive_claim_documents where (deleted_at is null or document_type = $2) and automotive_claim_id = $1 order by created_at desc`
	if getCCDocuments {
		query = `select id, file_name from automotive_claim_documents where deleted_at is null and automotive_claim_id = $1 and document_type = $2 order by created_at desc`
	}

	err = db.Get().SelectContext(ctx, &documentList, query, claimID, ClaimDoucmentTypeCCPayment)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, errors.Wrap(err, "error loading document list ")

	}
	return documentList, nil
}

// DocumentIndex returns all documents for given claim
func DocumentIndex(_ http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	ctx := req.Context()
	id, err := strconv.Atoi(chi.URLParam(req, "id"))
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Bad request", nil)
	}

	docs, err := documents(ctx, id, false)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error getting document from database"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting document list", nil)
	}
	return http.StatusOK, map[string]interface{}{"docs": docs}
}

// InspectionDocumentIndex returns all inspection document for given claim
func InspectionDocumentIndex(_ http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	ctx := req.Context()
	id, err := strconv.Atoi(chi.URLParam(req, "id"))
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Bad request", nil)
	}

	documentList := []claimDocument{}
	query := `select id, file_name 
              from automotive_claim_documents 
              where deleted_at 
			  is null 
			  and automotive_claim_id = $1 
			  and document_type = $2 order by created_at desc`
	err = db.Get().SelectContext(ctx, &documentList, query, id, db.ClaimDocumentTypeInspectionDocument)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error getting document from database"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting document list", nil)
	}
	return http.StatusOK, map[string]interface{}{"docs": documentList}
}

// CCDocumentIndex returns CC statement documents for given credit card paymet type claim
func CCDocumentIndex(_ http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	ctx := req.Context()
	id, err := strconv.Atoi(chi.URLParam(req, "id"))
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Bad request", nil)
	}

	docs, err := documents(ctx, id, true)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error getting document from database"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting document list", nil)
	}
	return http.StatusOK, map[string]interface{}{"docs": docs}
}
