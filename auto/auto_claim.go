package auto

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/csv"
	"encoding/json"
	"fmt"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"phizz/db"
	"phizz/dms"
	"phizz/dmsfactory"
	"phizz/handlers"
	"phizz/intacct"
	"phizz/types"
	"phizz/unidata"

	"github.com/go-chi/chi"
	"github.com/jmoiron/sqlx"
	"github.com/lib/pq"
	newrelic "github.com/newrelic/go-agent"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
	null "gopkg.in/guregu/null.v3"
)

// ClaimRequestType holds request type
type ClaimRequestType int

const (
	// ClaimRequestCreate claim create
	ClaimRequestCreate ClaimRequestType = iota
	// ClaimRequestUpdate claim update
	ClaimRequestUpdate
	// ClaimRequestSubmitIntacct submit claim to intacct
	ClaimRequestSubmitIntacct
)

type claimListItem struct {
	ID                  int                 `json:"id" db:"id"`
	CustomerName        string              `json:"customer_name" db:"customer_name"`
	ContractNumber      string              `json:"contract_number" db:"contract_number"`
	VIN                 string              `json:"vin" db:"vin"`
	Status              string              `json:"status" db:"status"`
	RO                  string              `json:"ro" db:"ro"`
	DateOfClaimReceived time.Time           `json:"date_of_claim_received" db:"date_of_claim_received"`
	Facility            sql.NullString      `json:"facility" db:"facility"`
	Estimate            decimal.Decimal     `json:"estimate" db:"estimate"`
	Amount              decimal.NullDecimal `json:"-" db:"amount"`
	CustomerID          int                 `json:"customer_id" db:"customer_id"`
	ReassignmentStatus  sql.NullString      `json:"reassignment_status" db:"reassignment_status"`
	ReassignedOwnerID   sql.NullInt64       `json:"reassigned_owner_id" db:"reassigned_owner_id"`
	OwnerID             int                 `json:"owner_id" db:"owner_id"`
	ChargeBack          bool                `json:"chargeback" db:"chargeback"`
	ParentClaimID       null.Int            `json:"parent_claim_id" db:"parent_claim_id"`
	PreAuthNumber       null.String         `json:"pre_auth_number" db:"pre_auth_number"`
	VendorID            string              `json:"vendor_id" db:"vendor_id"`
	Adjustments         decimal.Decimal     `json:"adjustments" db:"adjustments"`
}

func (claim *claimListItem) slice() []string {
	result := make([]string, 11)
	result[0] = claim.CustomerName
	result[1] = claim.ContractNumber
	result[2] = claim.VIN
	result[3] = claim.Status
	if claim.PreAuthNumber.Valid {
		result[4] = claim.PreAuthNumber.String
	}
	result[5] = claim.RO
	result[6] = claim.DateOfClaimReceived.Format("2006-01-02")
	result[7] = claim.Facility.String
	result[8] = claim.Estimate.String()
	result[9] = claim.VendorID
	result[10] = claim.Adjustments.String()

	return result
}

type claimListSlice []claimListItem

func (claims claimListSlice) csv() string {
	result := new(bytes.Buffer)
	w := csv.NewWriter(result)
	_ = w.Write([]string{"Contract Holder", "Contract#", "VIN",
		"Status", "PreAuth#", "RO", "Opened", "Facility",
		"$ Estimate", "VendorID", "Adjustments"})
	for _, claim := range claims {
		_ = w.Write(claim.slice())
	}
	w.Flush()
	return result.String()
}

// ClaimIndex returns a list of claims
// This function returns all automotive claims if no query parameters are provided
// The claims can be filtered by following query parameters
// q = filter by firstname, lastname, contract_number or vin
// userID - createdby userid
// Status - return claims with the given status
func ClaimIndex(res http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	claims := claimListSlice{}

	var whereClauses []string // where clause
	args := struct {
		SearchQueryLike   string `db:"search_query_like"`
		SearchQueryExact  string `db:"search_query_exact"`
		OwnerID           int    `db:"owner_id"`
		ReassignedOwnerID int    `db:"reassigned_owner_id"`
		Status            string `db:"status"`
		ProductCode       string `db:"product_code"`
		PayType           string `db:"pay_type"`
		PayTypeCC         string `db:"pay_type_cc"`
		PayTypeCR         string `db:"pay_type_cr"`
		PayTypeRF         string `db:"pay_type_rf"`
	}{
		PayTypeCC: db.PayTypeCreditCard,
		PayTypeCR: db.PayTypeCustomer,
		PayTypeRF: db.PayTypeStore,
	}

	nameContractVin := req.FormValue("q")
	if nameContractVin != "" {
		whereClauses = append(whereClauses, `(
			first_name || ' ' ||last_name ilike :search_query_like or
			last_name|| ' ' ||first_name ilike :search_query_like or
			business_name ilike :search_query_like or
			contract_number = :search_query_exact or
			vin = :search_query_exact or
			ro = :search_query_exact or
			facility_code = :search_query_exact or
			facility_name = :search_query_exact or
			acpa.pre_auth_number = :search_query_exact)`)
		args.SearchQueryLike = "%" + strings.Join(strings.Fields(strings.TrimSpace(nameContractVin)), " ") + "%"
		args.SearchQueryExact = nameContractVin
	}

	status := req.FormValue("status")
	if status != "" && isValidClaimStatus(status) {
		whereClauses = append(whereClauses, "ac.status = :status")
		args.Status = status
	}

	statusGroup := req.FormValue("status_group")
	if statusGroup != "" && status == "" {
		switch statusGroup {
		case "InProcess":
			statuses := fmt.Sprintf("'%s','%s','%s','%s','%s','%s','%s','%s','%s','%s'",
				db.AutoClaimStatusOpen, db.AutoClaimStatusReturned, db.AutoClaimStatusPreAuth,
				db.AutoClaimStatusDenied, db.AutoClaimStatusWaitingOnVendor,
				db.AutoClaimStatusNeedRentalBill, db.AutoClaimStatusNeedSubletBill,
				db.AutoClaimStatusNeedSMToCall, db.AutoClaimStatusNeedClosedAccountingRO,
				db.AutoClaimStatusNeedProofOfDeductibleReimbursement)
			whereClauses = append(whereClauses, "ac.status in ('"+statuses+"')")
		default:
			return http.StatusBadRequest, handlers.ErrorMessage(errors.New("Invalid status group filter"), "Error getting Auto claims lists data - Invalid status group filter", nil)
		}
	}

	userID := req.FormValue("user_id")
	// if userID is not provided in query, don't restrict by userID, show all claims
	var err error
	if userID != "" {
		ownerID, err := strconv.Atoi(userID)
		if err == nil {
			wh := "ac.owner_id = :owner_id"
			args.OwnerID = ownerID
			reassignedOwnerID := req.FormValue("reassigned_user_id")
			if reassignedOwnerID != "" {
				reassignedID, err := strconv.Atoi(reassignedOwnerID)
				if err == nil {
					wh += " or ac.reassigned_owner_id = :reassigned_owner_id"
					args.ReassignedOwnerID = reassignedID
				}
			}
			whereClauses = append(whereClauses, wh)
		}
	}
	age := req.FormValue("age")
	if age != "" {
		switch age {
		case "LessThan4Hours":
			whereClauses = append(whereClauses, "(ac.date_of_claim_received > NOW() at time zone 'utc' - interval '4 hours')")
		case "LessThan8Hours":
			whereClauses = append(whereClauses, "(ac.date_of_claim_received > NOW() at time zone 'utc' - interval '8 hours')")
		case "LessThan1Day":
			whereClauses = append(whereClauses, "(ac.date_of_claim_received > NOW() at time zone 'utc' - interval '1 days')")
		case "LessThan1Week":
			whereClauses = append(whereClauses, "(ac.date_of_claim_received > NOW() at time zone 'utc' - interval '7 days')")
		case "LessThan1Month":
			whereClauses = append(whereClauses, "(ac.date_of_claim_received > NOW() at time zone 'utc' - '1 month'::::interval)")
		case "GreaterThan1Month":
			whereClauses = append(whereClauses, "(ac.date_of_claim_received < NOW() at time zone 'utc' - '1 month'::::interval)")
		default:
			return http.StatusBadRequest, handlers.ErrorMessage(errors.New("Invalid age filter"), "Error getting Auto claims lists data - Invalid age filter", nil)
		}
	}

	productCode := req.FormValue("product")
	if productCode != "" && isValidProductCode(productCode) {
		whereClauses = append(whereClauses, "ac.product_code = :product_code")
		args.ProductCode = productCode
	}

	payType := req.FormValue("pay_type")
	if payType != "" && isValidPayType(payType) {
		whereClauses = append(whereClauses, "ac.pay_type = :pay_type")
		args.PayType = payType
	}

	wh := ""
	if len(whereClauses) > 0 {
		wh = "where " + strings.Join(whereClauses, " and ")
	}

	sortBy := req.FormValue("sort_by")
	if sortBy == "" {
		sortBy = " date_of_claim_received "
	}
	sortOrder := req.FormValue("sort_order")
	if sortOrder == "" {
		sortOrder = " desc "
	}

	// default order should be date_of_claim_received
	orderBy := "order by " + sortBy + " " + sortOrder + " "

	selectClause := `ac.id,
	ac.contract_number,
	ac.vin,
	ac.status,
	ac.ro,
    ac.date_of_claim_received,
	ac.estimate,
	ac.reassignment_status,
	ac.owner_id,
    ac.reassigned_owner_id,
	ac.chargeback,
	ac.parent_claim_id,
	case when c.is_business and (c.first_name!='' or c.last_name!='')
		then c.last_name || ',' || c.first_name || '/' || c.business_name
	when c.is_business and c.first_name='' and c.last_name=''
		then c.business_name
    else c.last_name || ',' || c.first_name
	end customer_name,
	af.facility_code as facility,
	acp.amount,
	acpa.pre_auth_number,
	case when ac.pay_type = :pay_type_cr then coalesce(ac.customer_payee_vendor_id,'')
		when ac.pay_type = :pay_type_cc or ac.pay_type = :pay_type_rf then coalesce(af.vendor_id,'')
		else '' end vendor_id`

	fromClause := `automotive_claims ac
	%s
	join customers c
		on ac.customer_id = c.id
	left join automotive_facilities af
		on ac.facility_id = af.id
	left join automotive_claim_payments acp
		on acp.automotive_claim_id = ac.id
	left join automotive_claim_pre_auth_numbers acpa
		on acpa.automotive_claim_id = ac.id`

	joinSegmentForSearchOpt := `join (
		-- match columns from automotive_claims
		select distinct ac.id
            from automotive_claims ac
            where contract_number = :search_query_exact or
				ro = :search_query_exact or
				vin = :search_query_exact or
				facility_name = :search_query_exact
		union

		-- match columns from customers
		select distinct ac.id
            from automotive_claims ac
            join customers c
				on ac.customer_id = c.id
            where first_name || ' ' || last_name ilike :search_query_like
				or business_name ilike :search_query_like
		union

		-- match columns from automotive_facilities
		select distinct ac.id
            from automotive_claims ac
		left join automotive_facilities af
			on ac.facility_id = af.id
		where af.facility_code = :search_query_exact

		union
		-- match columns from automotive_claim_pre_auth_numbers
		select distinct ac.id
            from automotive_claims ac
		left join automotive_claim_pre_auth_numbers acpa
			on acpa.automotive_claim_id = ac.id
		where acpa.pre_auth_number = :search_query_exact

	) search_results on search_results.id = ac.id`
	if args.SearchQueryExact == "" && args.SearchQueryLike == "" {
		fromClause = fmt.Sprintf(fromClause, "")
	} else {
		fromClause = fmt.Sprintf(fromClause, joinSegmentForSearchOpt)
	}

	countQuery := "select count(distinct ac.id) from " + fromClause + " " + wh

	// handle pagination
	p := req.FormValue("page")
	var listQuery string
	var n int
	if n, err = strconv.Atoi(p); err == nil && n > 0 {
		listQuery = fmt.Sprintf("select %s from %s %s %s limit %d offset %d", selectClause, fromClause, wh, orderBy, handlers.PerPageEntries, (n-1)*handlers.PerPageEntries)
	} else { // if page number is not provided, return all values
		listQuery = fmt.Sprintf("select %s from %s %s %s", selectClause, fromClause, wh, orderBy)
	}
	stmt, err := db.Get().PrepareNamed(listQuery)
	if err != nil {
		err = errors.Wrap(err, "Database error preparing get claims list query")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting claims", nil)
	}
	defer func() { _ = stmt.Close() }()
	err = stmt.Select(&claims, args)

	if err != nil {
		err = errors.Wrap(err, "Database error getting Auto claims lists")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting Auto claims lists data", nil)
	}

	stmt2, err := db.Get().PrepareNamed(countQuery)
	if err != nil {
		err = errors.Wrap(err, "Database error preparing automotive claims count query")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting claims count", nil)
	}
	defer func() { _ = stmt2.Close() }()
	count := 0
	err = stmt2.Get(&count, args)
	if err != nil {
		err = errors.Wrap(err, "Database error getting Auto claims lists count")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting Auto claims lists data", nil)
	}
	if len(claims) > 0 {
		claims = summarizeClaims(claims)
	}
	csv := req.FormValue("csv")
	var claimsCsv string
	if csv == "true" {
		claimsCsv = claims.csv()
		claims = nil
		return http.StatusOK, map[string]interface{}{"count": count, "automotive_claims": claimsCsv}
	}

	return http.StatusOK, map[string]interface{}{"count": count, "automotive_claims": claims}
}

func summarizeClaims(claims claimListSlice) claimListSlice {
	temp := make(map[int]claimListItem)
	var summeryClaims []claimListItem

	for _, c := range claims {
		cs := claimListItem{
			ID:                  c.ID,
			CustomerName:        c.CustomerName,
			ContractNumber:      c.ContractNumber,
			VIN:                 c.VIN,
			Status:              c.Status,
			RO:                  c.RO,
			DateOfClaimReceived: c.DateOfClaimReceived,
			Facility:            c.Facility,
			Estimate:            c.Estimate,
			Amount:              c.Amount,
			CustomerID:          c.CustomerID,
			ReassignmentStatus:  c.ReassignmentStatus,
			ReassignedOwnerID:   c.ReassignedOwnerID,
			OwnerID:             c.OwnerID,
			ChargeBack:          c.ChargeBack,
			ParentClaimID:       c.ParentClaimID,
			PreAuthNumber:       c.PreAuthNumber,
			VendorID:            c.VendorID,
		}

		if v, ok := temp[c.ID]; ok {
			if cs.Amount.Valid {
				if cs.Amount.Decimal.LessThanOrEqual(decimal.Zero) {
					v.Estimate = cs.Estimate.Add(cs.Amount.Decimal)
					v.Amount = decimal.NullDecimal{
						Valid:   true,
						Decimal: v.Amount.Decimal.Add(cs.Amount.Decimal),
					}
				}
				v.Adjustments = v.Adjustments.Add(c.Amount.Decimal)
			}
			cs = v
		} else {
			if cs.Amount.Valid {
				if cs.Amount.Decimal.LessThanOrEqual(decimal.Zero) {
					cs.Estimate = cs.Estimate.Add(cs.Amount.Decimal)
					v.Amount = decimal.NullDecimal{
						Valid:   true,
						Decimal: cs.Amount.Decimal,
					}
				}
				cs.Adjustments = cs.Adjustments.Add(cs.Amount.Decimal)
			}
			summeryClaims = append(summeryClaims, cs)
		}
		temp[c.ID] = cs
	}

	// We need to maintain incoming claims order so we will update values
	// instead of creating new slice from map
	for i, v := range summeryClaims {
		c, ok := temp[v.ID]
		if ok {
			if !c.Estimate.Equal(v.Estimate) {
				summeryClaims[i].Estimate = c.Estimate
			}
			summeryClaims[i].Adjustments = c.Adjustments
		}
	}
	return summeryClaims
}

func isValidClaimStatus(status string) bool {
	switch status {
	case db.AutoClaimStatusPreAuth:
		return true
	case db.AutoClaimStatusOpen:
		return true
	case db.AutoClaimStatusReturned:
		return true
	case db.AutoClaimStatusPayable:
		return true
	case db.AutoClaimStatusApproved:
		return true
	case db.AutoClaimStatusDenied:
		return true
	case db.AutoClaimStatusWaitingForCheck:
		return true
	case db.AutoClaimStatusCheckWritten:
		return true
	case db.AutoClaimStatusCCPaid:
		return true
	case db.AutoClaimStatusDeactivated:
		return true
	case db.AutoClaimStatusWaitingOnVendor:
		return true
	case db.AutoClaimStatusReversed:
		return true
	case db.AutoClaimStatusWaitingForReversed:
		return true
	case db.AutoClaimStatusAdjusted:
		return true
	case db.AutoClaimStatusNeedRentalBill:
		return true
	case db.AutoClaimStatusNeedSubletBill:
		return true
	case db.AutoClaimStatusNeedSMToCall:
		return true
	case db.AutoClaimStatusNeedClosedAccountingRO:
		return true
	case db.AutoClaimStatusNeedProofOfDeductibleReimbursement:
		return true
	case db.AutoClaimStatusInvoiceSent:
		return true
	case db.AutoClaimStatusAuthorizedCCClaim:
		return true
	case db.AutoClaimStatusWaitingForChargeback:
		return true
	case db.AutoClaimStatusChargeback:
		return true
	case db.AutoClaimStatusChargebackCollected:
		return true
	case db.AutoClaimStatusDealerChargedBack:
		return true
	}
	return false
}

func isValidProductCode(productCode string) bool {
	switch productCode {
	case db.ProductCodeCentury, db.ProductCodeKey, db.ProductCodeDrivePur,
		db.ProductCodeService, db.ProductCodeMaintenance, db.ProductCodeAppearanceProtection,
		db.ProductCodePaintlessDentRepair, db.ProductCodeKeyReplacement, db.ProductCodeTireWheel,
		db.ProductCodeToyotaTireWheel:
		return true
	}
	return false
}

func isValidPayType(payType string) bool {
	switch payType {
	case db.PayTypeCreditCard, db.PayTypeCustomer, db.PayTypeStore:
		return true
	}
	return false
}

type coverageList struct {
	Flag   string `json:"flag"`
	Value  bool   `json:"value"`
	String string `json:"string"`
}

// ClaimPayload struct with required data
type ClaimPayload struct {
	ID                         int                `json:"id" db:"id"`
	VIN                        string             `json:"vin" db:"vin"`
	ContractNumber             string             `json:"contract_number" db:"contract_number"`
	ContractStatus             string             `json:"contract_status" db:"contract_status"`
	ReassignmentStatus         string             `json:"reassignment_status" db:"reassignment_status"`
	DateOfClaimReceived        time.Time          `json:"date_of_claim_received" db:"date_of_claim_received"`
	Status                     string             `json:"status" db:"status"`
	OwnerName                  string             `json:"owner_name" db:"owner_name"`
	CustomerName               string             `json:"customer_name" db:"customer_name"`
	CustomerID                 int                `json:"customer_id" db:"customer_id"`
	EmailAddress               string             `json:"email_address" db:"email_address"`
	AltPhoneNumber             string             `json:"alternate_phone_number" db:"alternate_phone_number"`
	StreetAddress              string             `json:"street_address" db:"street_address"`
	PhoneNumber                string             `json:"phone_number" db:"phone_number"`
	City                       string             `json:"city" db:"city"`
	State                      string             `json:"state" db:"state"`
	PostalCode                 string             `json:"postal_code" db:"postal_code"`
	CustomerContactMethod      string             `json:"best_contact_method" db:"best_contact_method"`
	IsROCustomerValid          string             `json:"is_ro_customer_valid" db:"is_ro_customer_valid"`
	IsROContractVinMatch       string             `json:"is_ro_contract_vin_match" db:"is_ro_contract_vin_match"`
	IsROContractMileageValid   string             `json:"is_ro_contract_mileage_valid" db:"is_ro_contract_mileage_valid"`
	ROAutoUpdate               bool               `json:"is_ro_auto" db:"-"`
	ROSelectedDate             time.Time          `json:"ro_selected_date" db:"-"`
	UpdatedByUserID            int                `json:"updated_by_user_id" db:"updated_by_user_id"`
	LastUpdatedBy              string             `json:"last_updated_by" db:"last_updated_by"`
	LastUpdatedAt              time.Time          `json:"last_updated_at" db:"last_updated_at"`
	Complaints                 []complaintPayload `json:"complaints" db:"-"`
	Model                      string             `json:"model" db:"model"`
	Make                       string             `json:"make" db:"make"`
	Year                       string             `json:"year" db:"year"`
	BeginningMiles             int                `json:"beginning_miles" db:"beginning_miles"`
	EndingMiles                null.Int           `json:"ending_miles" db:"ending_miles"`
	EffectiveDate              null.Time          `json:"effective_date" db:"effective_date"`
	ExpirationDate             null.Time          `json:"expiration_date" db:"expiration_date"`
	Coverage                   string             `json:"coverage" db:"coverage"`
	CoverageList               []coverageList     `json:"coverage_list" db:"-"`
	Deductible                 decimal.Decimal    `json:"deductible" db:"deductible"`
	Maintenance                string             `json:"maintenance" db:"maintenance"`
	RO                         string             `json:"ro" db:"ro"`
	RoOpenedDate               time.Time          `json:"ro_opened_date" db:"ro_opened_date"`
	RoMileage                  int                `json:"ro_mileage" db:"ro_mileage"`
	FacilityCode               string             `json:"facility_code" db:"-"`
	FacilityID                 int                `json:"facility_id" db:"facility_id"`
	FacilityName               string             `json:"facility_name" db:"facility_name"`
	FacilityAddress            string             `json:"facility_address" db:"facility_address"`
	FacilityPostalCode         string             `json:"facility_postal_code" db:"facility_postal_code"`
	FacilityCity               string             `json:"facility_city" db:"facility_city"`
	FacilityStateCode          string             `json:"facility_state_code" db:"facility_state_code"`
	FacilityCountry            string             `json:"facility_country" db:"facility_country"`
	FacilityPhone              string             `json:"facility_phone" db:"facility_phone"`
	FacilityFax                string             `json:"facility_fax" db:"facility_fax"`
	LaborRate                  decimal.Decimal    `json:"labor_rate" db:"labor_rate"`
	TaxLabor                   decimal.Decimal    `json:"tax_labor" db:"tax_labor"`
	TaxParts                   decimal.Decimal    `json:"tax_parts" db:"tax_parts"`
	Advisor                    string             `json:"advisor" db:"advisor"`
	Term                       string             `json:"term" db:"term"`
	ProductCode                string             `json:"product_code" db:"product_code"`
	Estimate                   decimal.Decimal    `json:"estimate" db:"estimate"`
	PayType                    string             `json:"pay_type" db:"pay_type"`
	TaxAdjustment              decimal.Decimal    `json:"tax_adjustment" db:"tax_adjustment"`
	OwnerID                    int                `json:"owner_id" db:"owner_id"`
	AutoApproved               bool               `json:"auto_approved" db:"auto_approved"`
	PreAuthAmount              decimal.Decimal    `json:"pre_auth_amount" db:"pre_auth_amount"`
	IsAutoSave                 bool               `json:"is_auto_save" db:"-"`
	IntacctCustID              string             `json:"-" db:"-"`
	TotalTax                   decimal.Decimal    `json:"total_tax" db:"total_tax"`
	RequestedTotal             decimal.Decimal    `json:"requested_total" db:"requested_total"`
	CanceledReason             string             `json:"canceled_reason" db:"canceled_reason"`
	CanceledOtherReason        null.String        `json:"canceled_other_reason" db:"canceled_other_reason"`
	ClaimType                  string             `json:"claim_type" db:"claim_type"`
	SBRecordKey                string             `json:"sb_record_key" db:"sb_record_key"`
	ContractStoreID            int                `json:"contract_store_id" db:"contract_store_id"`
	OriginatingDealership      string             `json:"originating_dealership" db:"originating_dealership"`
	CustomerPayeeVendorID      string             `json:"customer_payee_vendor_id" db:"customer_payee_vendor_id"`
	IsTransferred              bool               `json:"is_transferred" db:"is_transferred"`
	ClaimPaymentID             null.Int           `json:"claim_payment_id" db:"claim_payment_id"`
	AuthorizationNumber        null.Int           `json:"authorization_number" db:"authorization_number"`
	BillMemo                   null.String        `json:"bill_memo" db:"bill_memo"`
	UsedRO                     []string           `json:"used_ro" db:"-"`
	ChargeBackExists           bool               `json:"charge_back_exists" db:"-"`
	ChargeBack                 bool               `json:"chargeback" db:"-"`
	ParentClaimID              int                `json:"parent_claim_id" db:"parent_claim_id"`
	Notes                      string             `json:"notes" db:"notes"`
	Adjustments                decimal.Decimal    `json:"adjustments" db:"-"`
	GrandTotalAmount           decimal.Decimal    `json:"grand_total_amount" db:"-"`
	DeductibleCollect          bool               `json:"deductible_collect" db:"deductible_collect"`
	TotalParts                 decimal.Decimal    `json:"total_parts" db:"total_parts"`
	TotalLabor                 decimal.Decimal    `json:"total_labor" db:"total_labor"`
	ReviewInspection           string             `json:"review_inspection" db:"review_inspection"`
	InvoicedAt                 null.Time          `json:"invoiced_at" db:"-"`
	PreAuthNumber              null.String        `json:"pre_auth_number" db:"-"`
	WarningMessageNote         string             `json:"warning_message_note" db:"-"`
	PaymentAdjustment          decimal.Decimal    `json:"payment_adjustment" db:"payment_adjustment"`
	ActualPaidAmount           decimal.Decimal    `json:"actual_paid_amount" db:"actual_paid_amount"`
	RepairingFacilityLaborRate decimal.Decimal    `json:"repairing_facility_labor_rate" db:"repairing_facility_labor_rate"`
}

var serviceCoverageFlags = map[string]string{
	db.CoverageDisappearingDeductible:       "Disappearing Deductible",
	db.CoverageHighTech:                     "High Tech",
	db.CoverageSealsAndGasket:               "Seals and Gasket",
	db.CoverageRentalUpgrade:                "Rental Upgrade",
	db.CoverageCommercialUse:                "Commercial Use",
	db.CoverageStandardPowertrainPlusOption: "Standard Powertrain Plus Option",
	db.CoverageSmartTechOption:              "Smart Tech Option",
	db.CoverageCanadianVehicle:              "Canadian Vehicle",
}

var centuryCoverageFlags = map[string]string{
	db.CoveragePaint:          "Paint",
	db.CoverageFabric:         "Fabric",
	db.CoverageLeatherOrVinyl: "Leather/Vinyl",
	db.CoverageDentAndDing:    "Dent and Ding",
}

//var centuryCoverageProductCodes = map[string]string{
//	db.CoveragePaint:          "P",
//	db.CoverageFabric:         "F",
//	db.CoverageLeatherOrVinyl: "L",
//	db.CoverageDentAndDing:    "D",
//}

var serviceCoveragePlans = map[string][]string{
	"NV Comprehensive":                          {db.CoverageDisappearingDeductible, db.CoverageHighTech, db.CoverageSealsAndGasket, db.CoverageRentalUpgrade, db.CoverageCommercialUse, db.CoverageSmartTechOption, db.CoverageCanadianVehicle},
	"New Comprehensive - migration":             {db.CoverageDisappearingDeductible, db.CoverageHighTech, db.CoverageSealsAndGasket, db.CoverageRentalUpgrade, db.CoverageCommercialUse, db.CoverageSmartTechOption, db.CoverageCanadianVehicle},
	"UV Comprensive":                            {db.CoverageDisappearingDeductible, db.CoverageHighTech, db.CoverageSealsAndGasket, db.CoverageRentalUpgrade, db.CoverageCommercialUse, db.CoverageSmartTechOption, db.CoverageCanadianVehicle},
	"UV Comprehensive":                          {db.CoverageDisappearingDeductible, db.CoverageHighTech, db.CoverageSealsAndGasket, db.CoverageRentalUpgrade, db.CoverageCommercialUse, db.CoverageSmartTechOption, db.CoverageCanadianVehicle},
	"Used Comprehensive - migration":            {db.CoverageDisappearingDeductible, db.CoverageHighTech, db.CoverageSealsAndGasket, db.CoverageRentalUpgrade, db.CoverageCommercialUse, db.CoverageSmartTechOption, db.CoverageCanadianVehicle},
	"NV Comprehensive Wrap":                     {db.CoverageDisappearingDeductible, db.CoverageHighTech, db.CoverageSealsAndGasket, db.CoverageRentalUpgrade, db.CoverageCommercialUse, db.CoverageSmartTechOption, db.CoverageCanadianVehicle},
	"Comprehensive Wrap - migration":            {db.CoverageDisappearingDeductible, db.CoverageHighTech, db.CoverageSealsAndGasket, db.CoverageRentalUpgrade, db.CoverageCommercialUse, db.CoverageSmartTechOption, db.CoverageCanadianVehicle},
	"UV Cmprhnsv Certified":                     {db.CoverageDisappearingDeductible, db.CoverageHighTech, db.CoverageSealsAndGasket, db.CoverageRentalUpgrade, db.CoverageCommercialUse, db.CoverageSmartTechOption, db.CoverageCanadianVehicle},
	"Comprehensive Certified - migration":       {db.CoverageDisappearingDeductible, db.CoverageHighTech, db.CoverageSealsAndGasket, db.CoverageRentalUpgrade, db.CoverageCommercialUse, db.CoverageSmartTechOption, db.CoverageCanadianVehicle},
	"Lexus Cmprhnsv Ext":                        {db.CoverageDisappearingDeductible, db.CoverageHighTech, db.CoverageSealsAndGasket, db.CoverageRentalUpgrade, db.CoverageCommercialUse, db.CoverageSmartTechOption, db.CoverageCanadianVehicle},
	"Lexus Comprehensive Extension - migration": {db.CoverageDisappearingDeductible, db.CoverageHighTech, db.CoverageSealsAndGasket, db.CoverageRentalUpgrade, db.CoverageCommercialUse, db.CoverageSmartTechOption, db.CoverageCanadianVehicle},
	"Comprehensive":                             {db.CoverageDisappearingDeductible, db.CoverageHighTech, db.CoverageSealsAndGasket, db.CoverageRentalUpgrade, db.CoverageCommercialUse, db.CoverageCanadianVehicle},
	"UV Comp Certified":                         {db.CoverageDisappearingDeductible, db.CoverageHighTech, db.CoverageSealsAndGasket, db.CoverageRentalUpgrade, db.CoverageSmartTechOption, db.CoverageCanadianVehicle},
	"Lincoln Comp Ext":                          {db.CoverageDisappearingDeductible, db.CoverageHighTech, db.CoverageSealsAndGasket, db.CoverageRentalUpgrade, db.CoverageSmartTechOption, db.CoverageCanadianVehicle},
	"Wrap (Legacy)":                             {db.CoverageDisappearingDeductible, db.CoverageHighTech, db.CoverageSealsAndGasket, db.CoverageRentalUpgrade, db.CoverageCommercialUse, db.CoverageCanadianVehicle},
	"Wrap - migration":                          {db.CoverageDisappearingDeductible, db.CoverageHighTech, db.CoverageSealsAndGasket, db.CoverageRentalUpgrade, db.CoverageCommercialUse, db.CoverageCanadianVehicle},
	"Certified (Legacy)":                        {db.CoverageDisappearingDeductible, db.CoverageHighTech, db.CoverageSealsAndGasket, db.CoverageRentalUpgrade, db.CoverageCommercialUse, db.CoverageCanadianVehicle},
	"Certified - migration":                     {db.CoverageDisappearingDeductible, db.CoverageHighTech, db.CoverageSealsAndGasket, db.CoverageRentalUpgrade, db.CoverageCommercialUse, db.CoverageCanadianVehicle},
	"NV Standard":                               {db.CoverageDisappearingDeductible, db.CoverageHighTech, db.CoverageSealsAndGasket, db.CoverageRentalUpgrade, db.CoverageCommercialUse, db.CoverageSmartTechOption, db.CoverageCanadianVehicle},
	"New Standard - migration":                  {db.CoverageDisappearingDeductible, db.CoverageHighTech, db.CoverageSealsAndGasket, db.CoverageRentalUpgrade, db.CoverageCommercialUse, db.CoverageSmartTechOption, db.CoverageCanadianVehicle},
	"UV Standard":                               {db.CoverageDisappearingDeductible, db.CoverageHighTech, db.CoverageSealsAndGasket, db.CoverageRentalUpgrade, db.CoverageCommercialUse, db.CoverageSmartTechOption, db.CoverageCanadianVehicle},
	"Used Standard - migration":                 {db.CoverageDisappearingDeductible, db.CoverageHighTech, db.CoverageSealsAndGasket, db.CoverageRentalUpgrade, db.CoverageCommercialUse, db.CoverageSmartTechOption, db.CoverageCanadianVehicle},
	"Standard":                                  {db.CoverageDisappearingDeductible, db.CoverageHighTech, db.CoverageSealsAndGasket, db.CoverageRentalUpgrade, db.CoverageCommercialUse, db.CoverageCanadianVehicle},
	"NV Drivetrain":                             {db.CoverageDisappearingDeductible, db.CoverageSealsAndGasket, db.CoverageRentalUpgrade, db.CoverageCommercialUse, db.CoverageCanadianVehicle},
	"New Powertrain - migration":                {db.CoverageDisappearingDeductible, db.CoverageSealsAndGasket, db.CoverageRentalUpgrade, db.CoverageCommercialUse, db.CoverageCanadianVehicle},
	"UV Drivetrain":                             {db.CoverageDisappearingDeductible, db.CoverageSealsAndGasket, db.CoverageRentalUpgrade, db.CoverageCommercialUse, db.CoverageCanadianVehicle},
	"Used Powertrain - migration":               {db.CoverageDisappearingDeductible, db.CoverageSealsAndGasket, db.CoverageRentalUpgrade, db.CoverageCommercialUse, db.CoverageCanadianVehicle},
	"Drivetrain":                                {db.CoverageDisappearingDeductible, db.CoverageSealsAndGasket, db.CoverageRentalUpgrade, db.CoverageCommercialUse, db.CoverageCanadianVehicle},
	"UV High Mileage":                           {db.CoverageDisappearingDeductible, db.CoverageCommercialUse, db.CoverageCanadianVehicle},
	"High Mileage":                              {db.CoverageDisappearingDeductible, db.CoverageSealsAndGasket, db.CoverageCommercialUse, db.CoverageCanadianVehicle},
	"High Mileage - migration":                  {db.CoverageDisappearingDeductible, db.CoverageSealsAndGasket, db.CoverageCommercialUse, db.CoverageCanadianVehicle},
	"HME Powertrain":                            {db.CoverageDisappearingDeductible, db.CoverageHighTech, db.CoverageSealsAndGasket, db.CoverageStandardPowertrainPlusOption, db.CoverageSmartTechOption, db.CoverageCanadianVehicle},
	"Powertrain High Mileage - migration":       {db.CoverageDisappearingDeductible, db.CoverageHighTech, db.CoverageSealsAndGasket, db.CoverageStandardPowertrainPlusOption, db.CoverageSmartTechOption, db.CoverageCanadianVehicle},
	"Powertrain Plus High Mileage - migration":  {db.CoverageDisappearingDeductible, db.CoverageHighTech, db.CoverageSealsAndGasket, db.CoverageStandardPowertrainPlusOption, db.CoverageSmartTechOption, db.CoverageCanadianVehicle},
	"Limited":                                   {},
	"Powerstroke":                               {db.CoverageDisappearingDeductible, db.CoverageHighTech, db.CoverageSealsAndGasket, db.CoverageRentalUpgrade, db.CoverageCommercialUse, db.CoverageCanadianVehicle},
	"UV Lease":                                  {db.CoverageDisappearingDeductible, db.CoverageHighTech, db.CoverageSealsAndGasket, db.CoverageRentalUpgrade, db.CoverageCommercialUse, db.CoverageCanadianVehicle},
	"NV Lease":                                  {db.CoverageDisappearingDeductible, db.CoverageHighTech, db.CoverageSealsAndGasket, db.CoverageRentalUpgrade, db.CoverageCommercialUse, db.CoverageCanadianVehicle},
	"Unknown or Pre 1994":                       {db.CoverageDisappearingDeductible, db.CoverageHighTech, db.CoverageSealsAndGasket, db.CoverageRentalUpgrade, db.CoverageCommercialUse, db.CoverageStandardPowertrainPlusOption, db.CoverageSmartTechOption, db.CoverageCanadianVehicle},
}

// ClaimSelect gets the auto claim record from db
func ClaimSelect(txn newrelic.Transaction, req *http.Request, id string) (*ClaimPayload, error) {
	ctx := req.Context()

	query := `select ac.*,
	case when c.is_business and (c.first_name!='' or c.last_name!='') then c.last_name || ',' || c.first_name || '/' || c.business_name
	when c.is_business and c.first_name='' and c.last_name='' then c.business_name
	else c.last_name || ',' || c.first_name end customer_name,
		email_address,
			c.phone_number, c.city, c.state, c.postal_code, c.street_address, c.alternate_phone_number, c.best_contact_method,
			acpa.pre_auth_number, u.first_name || ' ' || u.last_name as owner_name
		from automotive_claims ac join customers c on ac.customer_id = c.id
			left join automotive_claim_pre_auth_numbers acpa on acpa.automotive_claim_id = ac.id
			join users u on u.id = ac.owner_id
		where ac.id = $1 limit 1`

	claimDB := struct {
		ID                         int                 `json:"id" db:"id"`
		VIN                        string              `json:"vin" db:"vin"`
		ContractNumber             string              `json:"contract_number" db:"contract_number"`
		ContractStatus             sql.NullString      `json:"contract_status" db:"contract_status"`
		Status                     string              `json:"status" db:"status"`
		CustomerName               string              `json:"customer_name" db:"customer_name"`
		OwnerName                  string              `json:"owner_name" db:"owner_name"`
		CustomerID                 int                 `json:"customer_id" db:"customer_id"`
		EmailAddress               sql.NullString      `json:"email_address" db:"email_address"`
		AltPhoneNumber             sql.NullString      `json:"alternate_phone_number" db:"alternate_phone_number"`
		StreetAddress              sql.NullString      `json:"street_address" db:"street_address"`
		PhoneNumber                sql.NullString      `json:"phone_number" db:"phone_number"`
		City                       string              `json:"city" db:"city"`
		State                      string              `json:"state" db:"state"`
		PostalCode                 string              `json:"postal_code" db:"postal_code"`
		CustomerContactMethod      sql.NullString      `json:"best_contact_method" db:"best_contact_method"`
		IsROCustomerValid          string              `json:"is_ro_customer_valid" db:"is_ro_customer_valid"`
		IsROContractVinMatch       string              `json:"is_ro_contract_vin_match" db:"is_ro_contract_vin_match"`
		IsROContractMileageValid   string              `json:"is_ro_contract_mileage_valid" db:"is_ro_contract_mileage_valid"`
		LastUpdatedBy              string              `json:"last_updated_by" db:"last_updated_by"`
		LastUpdatedAt              time.Time           `json:"last_updated_at" db:"last_updated_at"`
		Model                      string              `json:"model" db:"model"`
		Make                       string              `json:"make" db:"make"`
		Year                       string              `json:"year" db:"year"`
		BeginningMiles             int                 `json:"beginning_miles" db:"beginning_miles"`
		EndingMiles                null.Int            `json:"ending_miles" db:"ending_miles"`
		EffectiveDate              null.Time           `json:"effective_date" db:"effective_date"`
		ExpirationDate             null.Time           `json:"expiration_date" db:"expiration_date"`
		Coverage                   sql.NullString      `json:"coverage" db:"coverage"`
		CoverageList               []coverageList      `json:"coverage_list" db:"-"`
		Deductible                 decimal.Decimal     `json:"deductible" db:"deductible"`
		RO                         sql.NullString      `json:"ro" db:"ro"`
		RoOpenedDate               pq.NullTime         `json:"ro_opened_date" db:"ro_opened_date"`
		RoMileage                  sql.NullInt64       `json:"ro_mileage" db:"ro_mileage"`
		FacilityCode               sql.NullString      `json:"facility_code" db:"facility_code"`
		FacilityID                 sql.NullInt64       `json:"facility_id" db:"facility_id"`
		FacilityName               sql.NullString      `json:"facility_name" db:"facility_name"`
		FacilityAddress            sql.NullString      `json:"facility_address" db:"facility_address"`
		FacilityPostalCode         sql.NullString      `json:"facility_postal_code" db:"facility_postal_code"`
		FacilityCity               sql.NullString      `json:"facility_city" db:"facility_city"`
		FacilityStateCode          sql.NullString      `json:"facility_state_code" db:"facility_state_code"`
		FacilityCountry            sql.NullString      `json:"facility_country" db:"facility_country"`
		FacilityPhone              sql.NullString      `json:"facility_phone" db:"facility_phone"`
		FacilityFax                sql.NullString      `json:"facility_fax" db:"facility_fax"`
		LaborRate                  sql.NullFloat64     `json:"labor_rate" db:"labor_rate"`
		TaxLabor                   sql.NullFloat64     `json:"tax_labor" db:"tax_labor"`
		TaxParts                   sql.NullFloat64     `json:"tax_parts" db:"tax_parts"`
		Advisor                    sql.NullString      `json:"advisor" db:"advisor"`
		Term                       sql.NullString      `json:"term" db:"term"`
		ProductCode                sql.NullString      `json:"product_code" db:"product_code"`
		Estimate                   decimal.Decimal     `json:"estimate" db:"estimate"`
		PayType                    sql.NullString      `json:"pay_type" db:"pay_type"`
		TaxAdjustment              decimal.Decimal     `json:"tax_adjustment" db:"tax_adjustment"`
		OwnerID                    int                 `json:"owner_id" db:"owner_id"`
		PreAuthAmount              decimal.Decimal     `json:"pre_auth_amount" db:"pre_auth_amount"`
		TotalTax                   decimal.NullDecimal `json:"total_tax" db:"total_tax"`
		RequestedTotal             decimal.Decimal     `json:"requested_total" db:"requested_total"`
		CanceledReason             string              `json:"canceled_reason" db:"canceled_reason"`
		CanceledOtherReason        null.String         `json:"canceled_other_reason" db:"canceled_other_reason"`
		ClaimType                  string              `json:"claim_type" db:"claim_type"`
		SBRecordKey                string              `json:"sb_record_key" db:"sb_record_key"`
		ContractStoreID            sql.NullInt64       `json:"contract_store_id" db:"contract_store_id"`
		OriginatingDealership      string              `json:"originating_dealership" db:"originating_dealership"`
		CustomerPayeeVendorID      sql.NullString      `json:"customer_payee_vendor_id" db:"customer_payee_vendor_id"`
		DateOfClaimReceived        time.Time           `json:"date_of_claim_received" db:"date_of_claim_received"`
		ChargeBack                 bool                `json:"chargeback" db:"chargeback"`
		ParentClaimID              null.Int            `json:"parent_claim_id" db:"parent_claim_id"`
		Notes                      null.String         `json:"notes" db:"notes"`
		DeductibleCollect          bool                `json:"deductible_collect" db:"deductible_collect"`
		TotalParts                 decimal.Decimal     `json:"total_parts" db:"total_parts"`
		TotalLabor                 decimal.Decimal     `json:"total_labor" db:"total_labor"`
		PreAuthNumber              null.String         `json:"pre_auth_number" db:"pre_auth_number"`
		ReviewInspection           string              `json:"review_inspection" db:"review_inspection"`
		PaymentAdjustment          decimal.Decimal     `json:"payment_adjustment" db:"payment_adjustment"`
		ActualPaidAmount           decimal.Decimal     `json:"actual_paid_amount" db:"actual_paid_amount"`
		RepairingFacilityLaborRate decimal.Decimal     `json:"repairing_facility_labor_rate" db:"repairing_facility_labor_rate"`
	}{}
	err := db.Get().Unsafe().GetContext(ctx, &claimDB, query, id)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.Wrap(err, "The Auto claim was not found")
		}
		return nil, errors.Wrap(err, "Error loading Auto claim from database.")
	}

	if claimDB.ContractStoreID.Valid {
		err = db.Get().GetContext(ctx, &claimDB, "select name as originating_dealership from stores where id=$1", claimDB.ContractStoreID)
		if err != nil {
			if err == sql.ErrNoRows {
				return nil, errors.Wrap(err, "store name not found for originating_dealership")
			}
			return nil, errors.Wrap(err, "Error loading originating_dealership from database.")
		}
	}

	lastUpdateQuery := `select automotive_claim_updates.updated_at as last_updated_at, first_name || ' ' || last_name as last_updated_by
	 from automotive_claim_updates join users on updated_by_user_id = users.id
	 where automotive_claim_id = $1 order by last_updated_at desc limit 1`

	err = db.Get().Unsafe().GetContext(ctx, &claimDB, lastUpdateQuery, id)
	if err != nil && err != sql.ErrNoRows {
		return nil, errors.Wrap(err, "Error loading Auto claim updates from database.")
	}

	var usedRO []string
	query = `select ro from automotive_claims ac where contract_number = $1 and status != $2 and id != $3`
	err = db.Get().SelectContext(ctx, &usedRO, query, claimDB.ContractNumber, db.AutoClaimStatusDeactivated, id)
	if err != nil && err != sql.ErrNoRows {
		return nil, errors.Wrap(err, "Error loading originating_dealership from database.")
	}
	claim := ClaimPayload{}

	claim.ID = claimDB.ID
	claim.VIN = claimDB.VIN
	claim.ContractNumber = claimDB.ContractNumber
	claim.ContractStatus = claimDB.ContractStatus.String
	claim.Status = claimDB.Status
	claim.LastUpdatedBy = claimDB.LastUpdatedBy
	claim.LastUpdatedAt = claimDB.LastUpdatedAt

	claim.Complaints, err = complaints(ctx, claimDB.ID, "id")
	if err != nil {
		return nil, errors.Wrap(err, "error loading complaints")
	}

	claim.OwnerName = claimDB.OwnerName
	claim.CustomerName = claimDB.CustomerName
	claim.CustomerID = claimDB.CustomerID
	claim.EmailAddress = claimDB.EmailAddress.String
	claim.StreetAddress = claimDB.StreetAddress.String
	claim.PhoneNumber = claimDB.PhoneNumber.String
	claim.AltPhoneNumber = claimDB.AltPhoneNumber.String
	claim.CustomerContactMethod = claimDB.CustomerContactMethod.String
	claim.City = claimDB.City
	claim.State = claimDB.State
	claim.IsROCustomerValid = claimDB.IsROCustomerValid
	claim.IsROContractVinMatch = claimDB.IsROContractVinMatch
	claim.IsROContractMileageValid = claimDB.IsROContractMileageValid
	claim.PostalCode = claimDB.PostalCode
	claim.Model = claimDB.Model
	claim.Make = claimDB.Make
	claim.Year = claimDB.Year
	claim.BeginningMiles = claimDB.BeginningMiles
	claim.EndingMiles = claimDB.EndingMiles
	claim.EffectiveDate = claimDB.EffectiveDate
	claim.ExpirationDate = claimDB.ExpirationDate
	claim.Coverage = claimDB.Coverage.String
	claim.Deductible = claimDB.Deductible
	claim.RO = claimDB.RO.String
	claim.RoOpenedDate = claimDB.RoOpenedDate.Time
	claim.RoMileage = int(claimDB.RoMileage.Int64)
	claim.FacilityCode = claimDB.FacilityCode.String
	claim.FacilityID = int(claimDB.FacilityID.Int64)
	claim.FacilityName = claimDB.FacilityName.String
	claim.FacilityAddress = claimDB.FacilityAddress.String
	claim.FacilityPostalCode = claimDB.FacilityPostalCode.String
	claim.FacilityCity = claimDB.FacilityCity.String
	claim.FacilityStateCode = claimDB.FacilityStateCode.String
	claim.FacilityCountry = claimDB.FacilityCountry.String
	claim.FacilityPhone = claimDB.FacilityPhone.String
	claim.FacilityFax = claimDB.FacilityFax.String
	claim.LaborRate = decimal.NewFromFloat(claimDB.LaborRate.Float64)
	claim.TaxLabor = decimal.NewFromFloat(claimDB.TaxLabor.Float64)
	claim.TaxParts = decimal.NewFromFloat(claimDB.TaxParts.Float64)
	claim.Advisor = claimDB.Advisor.String
	claim.Term = claimDB.Term.String
	claim.ProductCode = claimDB.ProductCode.String
	claim.Estimate = claimDB.Estimate
	claim.PayType = claimDB.PayType.String
	claim.TaxAdjustment = claimDB.TaxAdjustment
	claim.OwnerID = claimDB.OwnerID
	claim.PreAuthAmount = claimDB.PreAuthAmount
	if claimDB.TotalTax.Valid {
		claim.TotalTax = claimDB.TotalTax.Decimal
	}
	claim.RequestedTotal = claimDB.RequestedTotal
	claim.CanceledReason = claimDB.CanceledReason
	claim.CanceledOtherReason = claimDB.CanceledOtherReason
	claim.ClaimType = claimDB.ClaimType
	claim.SBRecordKey = claimDB.SBRecordKey
	claim.ContractStoreID = int(claimDB.ContractStoreID.Int64)
	claim.OriginatingDealership = claimDB.OriginatingDealership
	claim.CustomerPayeeVendorID = claimDB.CustomerPayeeVendorID.String
	claim.DateOfClaimReceived = claimDB.DateOfClaimReceived
	claim.ChargeBack = claimDB.ChargeBack
	claim.Notes = claimDB.Notes.ValueOrZero()
	claim.ParentClaimID = int(claimDB.ParentClaimID.ValueOrZero())
	claim.DeductibleCollect = claimDB.DeductibleCollect
	claim.TotalParts = claimDB.TotalParts
	claim.TotalLabor = claimDB.TotalLabor
	claim.PreAuthNumber = claimDB.PreAuthNumber
	claim.ReviewInspection = claimDB.ReviewInspection
	claim.PaymentAdjustment = claimDB.PaymentAdjustment
	claim.ActualPaidAmount = claimDB.ActualPaidAmount
	claim.RepairingFacilityLaborRate = claimDB.RepairingFacilityLaborRate

	claim.UsedRO = []string{}
	if len(usedRO) > 0 {
		claim.UsedRO = usedRO
	}

	if txn != nil {
		contract, err := handlers.GetContractByID(txn, req, claimDB.ContractNumber)
		if err != nil {
			return nil, errors.Wrap(err, "Invalid response for Whiz-contract-detail")
		}
		claim.CustomerName = fmt.Sprint(contract.CustomerDetails.LastName, ",",
			contract.CustomerDetails.FirstName)
		if contract.CustomerDetails.IsBusiness {
			if strings.TrimSpace(claim.CustomerName) != "" {
				claim.CustomerName += "/"
			}
			claim.CustomerName += contract.CustomerDetails.BusinessName
		}
		claim.CustomerID = contract.CustomerDetails.ID
		claim.EmailAddress = contract.CustomerDetails.Email
		claim.StreetAddress = contract.CustomerDetails.Address
		claim.PhoneNumber = contract.CustomerDetails.Phone
		claim.AltPhoneNumber = contract.CustomerDetails.AlternatePhone
		claim.City = contract.CustomerDetails.City
		claim.State = contract.CustomerDetails.StateCode
		claim.PostalCode = contract.CustomerDetails.PostalCode
		claim.ContractStatus = contract.Contract.Status
		claim.InvoicedAt = contract.Contract.InvoicedAt
		if len(contract.Transfers) > 0 {
			claim.IsTransferred = true
		}
	}

	if !claim.ChargeBack {
		switch claim.ProductCode {
		case db.ProductCodeService:
			claim.CoverageList, err = getServiceCoverage(claim.ID, claim.Coverage)
		case db.ProductCodeKey:
			claim.CoverageList, err = getKeyCoverage(claim.ID)
		case db.ProductCodeCentury:
			claim.CoverageList, err = getCenturyCoverage(claim.ID, claim.ContractNumber)
		case db.ProductCodeMaintenance:
			claim.CoverageList, err = getMaintenanceCoverage(claim.ID)
		}
		if err != nil {
			return nil, errors.Wrap(err, "Error getting  coverage flag list for Auto claim")
		}
	}

	var chargeBackExists int
	query = `select count(1) from automotive_claims where parent_claim_id = $1`
	err = db.Get().GetContext(ctx, &chargeBackExists, query, claim.ID)
	if err != nil && err != sql.ErrNoRows {
		return nil, errors.Wrap(err, "Error getting charge back claim details")
	}

	if chargeBackExists > 0 {
		claim.ChargeBackExists = true
	}

	if isPayableStatus(claim.Status) {
		paymentDetails, negativeClaims, err := getPaymentDetailsByClaimID(ctx, claim.ID, claim.Status)
		if err != nil {
			return nil, errors.WithMessage(err, "Failed to get payment details")
		}
		if len(paymentDetails) > 1 || len(negativeClaims) > 0 {
			// First payment is always the original claim payment
			claim.Estimate = paymentDetails[0].PaymentAmount.Decimal

			//Calculate Adjustments
			for i, v := range paymentDetails {
				// For old claims we know 1st payment will never be adjustment
				if (v.ClaimPaymentType.Valid && v.ClaimPaymentType.String == db.AutoClaimPaymentType) || i == 0 {
					continue
				}
				claim.Adjustments = claim.Adjustments.Add(v.PaymentAmount.Decimal)
			}
			//Calculate Negative Adjustments
			for _, v := range negativeClaims {
				claim.Adjustments = claim.Adjustments.Add(v.Amount)
			}

			//Calculate Grand Total
			claim.GrandTotalAmount = claim.Adjustments.Add(claim.Estimate)
		}
	}

	return &claim, err
}

func getServiceCoverage(claimID int, coveragePlan string) ([]coverageList, error) {
	var coverageFlagList []coverageList
	coverageDB := struct {
		DisappearingDeductible       sql.NullBool  `db:"disappearing_deductible"`
		HighTech                     sql.NullBool  `db:"high_tech"`
		SealsAndGasket               sql.NullBool  `db:"seals_and_gasket"`
		RentalUpgrade                sql.NullBool  `db:"rental_upgrade"`
		CommercialUse                sql.NullInt64 `db:"commercial_use"`
		StandardPowertrainPlusOption sql.NullBool  `db:"standard_powertrain_plus_option"`
		SmartTechOption              sql.NullBool  `db:"smart_tech_option"`
		CanadianVehicle              sql.NullBool  `db:"canadian_vehicle"`
	}{}
	coverageQuery := "select disappearing_deductible, high_tech, seals_and_gasket, rental_upgrade, commercial_use, standard_powertrain_plus_option, smart_tech_option, canadian_vehicle from automotive_claim_coverage where automotive_claim_id = $1 limit 1"
	err := db.Get().Get(&coverageDB, coverageQuery, claimID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.Wrap(err, "The Auto claim coverage flags were not found")
		}
		return nil, errors.Wrap(err, "Error loading Auto claim coverage from database.")
	}
	coveragePlanMap := serviceCoveragePlans[coveragePlan]
	if coveragePlanMap == nil {
		// Fall back to a default plan coverage map if a specific match was not found
		coveragePlanMap = serviceCoveragePlans["Unknown or Pre 1994"]
	}
	for _, coverageFlag := range coveragePlanMap {
		coverageDetails := coverageList{}
		switch coverageFlag {
		case db.CoverageDisappearingDeductible:
			coverageDetails.Flag = serviceCoverageFlags[coverageFlag]
			coverageDetails.Value = coverageDB.DisappearingDeductible.Bool
			coverageFlagList = append(coverageFlagList, coverageDetails)
		case db.CoverageHighTech:
			coverageDetails.Flag = serviceCoverageFlags[coverageFlag]
			coverageDetails.Value = coverageDB.HighTech.Bool
			coverageFlagList = append(coverageFlagList, coverageDetails)
		case db.CoverageSealsAndGasket:
			coverageDetails.Flag = serviceCoverageFlags[coverageFlag]
			coverageDetails.Value = coverageDB.SealsAndGasket.Bool
			coverageFlagList = append(coverageFlagList, coverageDetails)
		case db.CoverageRentalUpgrade:
			coverageDetails.Flag = serviceCoverageFlags[coverageFlag]
			coverageDetails.Value = coverageDB.RentalUpgrade.Bool
			coverageFlagList = append(coverageFlagList, coverageDetails)
		case db.CoverageCommercialUse:
			coverageDetails.Value = false
			coverageDetails.Flag = serviceCoverageFlags[coverageFlag]
			if coverageDB.CommercialUse.Valid && coverageDB.CommercialUse.Int64 != 0 {
				if coverageDB.CommercialUse.Int64 == 1 {
					coverageDetails.Value = true
					coverageDetails.Flag = "1 ton" + serviceCoverageFlags[coverageFlag]
				} else if coverageDB.CommercialUse.Int64 == 3 {
					coverageDetails.Value = true
					coverageDetails.Flag = "3/4 ton" + serviceCoverageFlags[coverageFlag]
				}
			}
			coverageFlagList = append(coverageFlagList, coverageDetails)
		case db.CoverageStandardPowertrainPlusOption:
			coverageDetails.Flag = serviceCoverageFlags[coverageFlag]
			coverageDetails.Value = coverageDB.StandardPowertrainPlusOption.Bool
			coverageFlagList = append(coverageFlagList, coverageDetails)
		case db.CoverageSmartTechOption:
			coverageDetails.Flag = serviceCoverageFlags[coverageFlag]
			coverageDetails.Value = coverageDB.SmartTechOption.Bool
			coverageFlagList = append(coverageFlagList, coverageDetails)
		case db.CoverageCanadianVehicle:
			coverageDetails.Flag = serviceCoverageFlags[coverageFlag]
			coverageDetails.Value = coverageDB.CanadianVehicle.Bool
			coverageFlagList = append(coverageFlagList, coverageDetails)
		}
	}
	return coverageFlagList, nil
}

func getKeyCoverage(claimID int) ([]coverageList, error) {
	var coverageFlagList []coverageList
	var keyCount sql.NullInt64
	coverageQuery := "select key_count from automotive_claim_coverage where automotive_claim_id = $1 limit 1"
	err := db.Get().Get(&keyCount, coverageQuery, claimID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.Wrap(err, "The Auto claim coverage flags were not found")
		}
		return nil, errors.Wrap(err, "Error loading Auto claim coverage from database.")
	}
	var list []coverageList
	if keyCount.Int64 == 1 {
		list = append(list, coverageList{"1 Key", true, ""})
		list = append(list, coverageList{"2 Keys", false, ""})
	} else if keyCount.Int64 == 2 {
		list = append(list, coverageList{"1 Key", false, ""})
		list = append(list, coverageList{"2 Keys", true, ""})
	} else {
		list = append(list, coverageList{"1 Key", false, ""})
		list = append(list, coverageList{"2 Keys", false, ""})
	}
	coverageFlagList = append(coverageFlagList, list...)
	return coverageFlagList, nil
}

func getCenturyCoverage(claimID int, contractNumber string) ([]coverageList, error) {
	var coverageFlagList []coverageList
	coverageDB := struct {
		Paint          sql.NullBool `db:"paint"`
		Fabric         sql.NullBool `db:"fabric"`
		LeatherOrVinyl sql.NullBool `db:"leather_or_vinyl"`
		DingAndDent    sql.NullBool `db:"dent_and_ding"`
	}{}
	coverageQuery := "select paint, fabric, leather_or_vinyl, dent_and_ding from automotive_claim_coverage where automotive_claim_id = $1 limit 1"
	err := db.Get().Get(&coverageDB, coverageQuery, claimID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.Wrap(err, "The Auto claim coverage flags were not found")
		}
		return nil, errors.Wrap(err, "Error loading Auto claim coverage from database.")
	}
	// Only non PDRS century claims will have Paint, Fabrics and Leather/Vinyl coverage
	if !strings.HasPrefix(contractNumber, "PDRS") {
		coverageFlagList = append(coverageFlagList,
			coverageList{centuryCoverageFlags[db.CoveragePaint], coverageDB.Paint.Bool, ""},
			coverageList{centuryCoverageFlags[db.CoverageFabric], coverageDB.Fabric.Bool, ""},
			coverageList{centuryCoverageFlags[db.CoverageLeatherOrVinyl], coverageDB.LeatherOrVinyl.Bool, ""})
	}
	coverageFlagList = append(coverageFlagList, coverageList{centuryCoverageFlags[db.CoverageDentAndDing], coverageDB.DingAndDent.Bool, ""})
	return coverageFlagList, nil
}

func getMaintenanceCoverage(claimID int) ([]coverageList, error) {
	var coverageFlagList []coverageList
	coverageDB := struct {
		Plan      null.String `db:"plan"`
		Purchased int         `db:"purchased"`
		Remaining null.Int    `db:"remaining"`
	}{}
	coverageQuery := "select plan, purchased, remaining from automotive_claim_coverage where automotive_claim_id = $1 limit 1"
	err := db.Get().Get(&coverageDB, coverageQuery, claimID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.Wrap(err, "The Auto claim coverage flags were not found")
		}
		return nil, errors.Wrap(err, "Error loading Auto claim coverage from database.")
	}
	coverageFlagList = append(coverageFlagList,
		coverageList{"Plan", false, coverageDB.Plan.String},
		coverageList{"Purchased", false, strconv.Itoa(coverageDB.Purchased)},
		coverageList{"Remaining", false, strconv.Itoa(int(coverageDB.Remaining.Int64))})
	return coverageFlagList, nil
}

// get RO details from CDK
func getRO(ctx context.Context, claimID int, storeID int, roNumber string) ([]dms.RODetail, string, error) {
	var dmsProvider string
	err := db.Get().Get(&claimID, `select id from automotive_claims where id = $1`, claimID)
	if err != nil {
		return nil, dmsProvider, errors.Wrap(err, "Claim not found")
	}

	var store db.Store
	err = db.Get().Unsafe().Get(
		&store,
		`select * from stores where id = $1`,
		storeID,
	)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, dmsProvider, errors.Wrap(err, "Store with DMS provider and RO integration not found")
		}
		return nil, dmsProvider, errors.Wrap(err, "Database error getting store for DMS RO lookup")
	}
	dmsProvider = store.DMSProvider

	if !store.HasROIntegration {
		return nil, dmsProvider, errors.Wrap(err, "Store with DMS provider and has RO integration not found")
	}
	dmsProvider = store.DMSProvider
	var roDetails []dms.RODetail

	switch store.DMSProvider {
	case db.DMSProviderCDK, db.DMSProviderUCS, db.DMSProviderTekion:
		roDetails, err = dmsfactory.RODetail(ctx, &store, roNumber)
		if err != nil {
			return nil, dmsProvider, err
		}
	default:
		return nil, dmsProvider, errors.Wrap(err, "Store with DMS provider and has RO integration not found")
	}
	return roDetails, dmsProvider, nil
}

type gapClaimListPayload struct {
	Date        pq.NullTime     `json:"date" db:"date"`
	Miles       string          `json:"miles" db:"miles"`
	Amount      decimal.Decimal `json:"amount" db:"amount"`
	Status      string          `json:"status" db:"status"`
	PaidDate    pq.NullTime     `json:"paid_date" db:"paid_date"`
	CheckNumber null.String     `json:"check_number" db:"check_number"`
	ClaimID     int             `json:"claim_id" db:"claim_id"`
	DateOfLoss  time.Time       `json:"date_of_loss" db:"date_of_loss"`
}

func gapClaimsByContract(ctx context.Context, id string) ([]gapClaimListPayload, error) {
	gapClaimList := []gapClaimListPayload{}

	query := `select
					gc.id claim_id
					, date_of_claim_received as date
					, case when gcp.amount > 0 then gcp.amount else case_reserve end as amount
					, gc.status
					, gc.valuation_report_mileage as miles
					, gc.date_of_loss
					, gcp.paid_date
					, gcp.check_number
				from gap_claims gc
					left join gap_claim_payments gcp on gc.id = gcp.gap_claim_id
				where gc.contract_number = $1
				and gcp.id not in (select gcp.id from gap_claim_payments gcp join gap_voided_transactions gvt
                        on gcp.gap_claim_id = gvt.gap_claim_id and gcp.check_number::varchar(255) = gvt.check_number where gcp.gap_claim_id = gc.id)`

	err := db.Get().SelectContext(ctx, &gapClaimList, query, id)
	if err != nil {
		return gapClaimList, errors.Wrap(err, "Failed to get claim information.")
	}
	return gapClaimList, nil
}

type vtaClaimListPayload struct {
	Date     pq.NullTime     `json:"date" db:"date"`
	Amount   decimal.Decimal `json:"amount" db:"amount"`
	Status   string          `json:"status" db:"status"`
	PaidDate pq.NullTime     `json:"paid_date" db:"paid_date"`
	ClaimID  int             `json:"claim_id" db:"claim_id"`
}

func vtaClaimsByContract(ctx context.Context, id string) ([]vtaClaimListPayload, error) {
	vtaClaimList := []vtaClaimListPayload{}

	query := `select
					vc.id claim_id
					, date_of_claim_received as date
					, case when vcp.amount > 0 then vcp.amount else case_reserve end as amount
					, case when vcp.voided_at is not null then $1 else vc.status end as status
					, vcp.paid_date
				from vta_claims vc
					left join vta_claim_payments vcp on vc.id = vcp.vta_claim_id
				where vc.contract_number = $2`

	err := db.Get().SelectContext(ctx, &vtaClaimList, query, db.VtaClaimStatusCheckVoided, id)
	if err != nil {
		return vtaClaimList, errors.Wrap(err, "Failed to vta claim information.")
	}
	return vtaClaimList, nil
}

type lwtClaimListPayload struct {
	Date         pq.NullTime     `json:"date" db:"date"`
	Amount       decimal.Decimal `json:"amount" db:"amount"`
	Status       string          `json:"status" db:"status"`
	PaymentDates []time.Time     `json:"payment_dates" db:"payment_dates"`
	CheckNumber  []string        `json:"check_number" db:"check_number"`
	ClaimID      int             `json:"claim_id" db:"claim_id"`
}

func lwtClaimsByContract(ctx context.Context, id string) ([]lwtClaimListPayload, error) {
	var lwtClaimList []lwtClaimListPayload

	query := `select
					lc.id claim_id
					, lc.date_of_claim_received as date
					, lc.status
				from lwt_claims lc
				where lc.contract_number = $1`

	err := db.Get().SelectContext(ctx, &lwtClaimList, query, id)
	if err != nil {
		return lwtClaimList, errors.Wrap(err, "failed to get claim information.")
	}

	type payment struct {
		LWTClaimID  int             `db:"id"`
		CheckNumber sql.NullString  `db:"check_number"`
		Amount      decimal.Decimal `db:"amount"`
		PaidDate    pq.NullTime     `db:"paid_date"`
	}

	for i, v := range lwtClaimList {
		var payments []payment
		query := `select lc.id,
       					lcp.check_number,
       					coalesce(lca.amount, lcp.amount, lc.approved_amount, 0) amount,
       					lcp.paid_date
					from lwt_claims lc
         				left join lwt_claim_payments lcp on lc.id = lcp.lwt_claim_id
         				left join lwt_claim_adjustments lca on
             				lcp.id = lca.lwt_claim_payment_id and lcp.lwt_claim_id = lca.lwt_claim_id
				where lc.id = $1
					and (lcp.valid is null or lcp.valid = true)`

		err = db.Get().SelectContext(ctx, &payments, query, v.ClaimID)
		if err != nil && err != sql.ErrNoRows {
			return nil, errors.Wrap(err, "failed to get claim payments")
		}

		var totalAmount decimal.Decimal
		var paidDates []time.Time
		var checkNumbers []string
		for _, pay := range payments {
			totalAmount = totalAmount.Add(pay.Amount)
			if pay.PaidDate.Valid {
				paidDates = append(paidDates, pay.PaidDate.Time)
			}

			if pay.CheckNumber.Valid {
				checkNumbers = append(checkNumbers, pay.CheckNumber.String)
			}
		}

		lwtClaimList[i].CheckNumber = checkNumbers
		lwtClaimList[i].PaymentDates = paidDates
		lwtClaimList[i].Amount = totalAmount
	}

	return lwtClaimList, nil
}

// autoClaimsByContract gets information about auto claims and payments for those claims
func autoClaimsByContract(ctx context.Context, id, productCode, sortOrder string) (unidata.ClaimHistoryList, error) {
	query := `select
		ac.id as claim_id,
		ac.unidata_claim_number,
		ac.date_of_claim_received as date,
		ac.ro_mileage as miles,
		array_to_string(array_agg(acc.repair_code),',') failed_components,
		ac.maintenance as service,
		ac.status,
		ac.ro,
		coalesce(ac.advisor, '') advisor,
		coalesce(ac.pay_type, '') pay_type,
		acf.facility_code,
		ac.chargeback,
		ac.parent_claim_id
	from automotive_claims ac 
		left join automotive_claim_complaints acc on ac.id = acc.automotive_claim_id
		left join automotive_facilities acf on acf.id = ac.facility_id
	where contract_number like $1
		and product_code like $2 
	group by ac.id, acf.id
	order by date_of_claim_received ` + sortOrder

	var autoClaimList unidata.ClaimHistoryList

	err := db.Get().SelectContext(ctx, &autoClaimList, query, id, productCode)
	if err != nil {
		return nil, errors.Wrap(err, "Failed to get claim information.")
	}

	type payment struct {
		AutomotiveClaimID  int                 `db:"id"`
		AutoClaimPaymentID null.Int            `db:"auto_claim_payment_id"`
		CheckPayment       decimal.NullDecimal `db:"check_payment"`
		CheckNumber        sql.NullString      `db:"check_number"`
		AuthorizedAmount   decimal.Decimal     `db:"authorized_amount"`
		CheckAmount        decimal.Decimal     `db:"check_amount"`
		PaidDate           pq.NullTime         `db:"paid_date"`
	}

	for i, claim := range autoClaimList {
		var payments []payment
		err := db.Get().SelectContext(ctx, &payments,
			`select ac.id
     			, acp.id auto_claim_payment_id
				, ac.estimate authorized_amount
     			, acp.amount check_payment
				, acpc.check_number
				, coalesce(acpc.check_amount, acp.amount, ac.estimate, 0) check_amount
				, acpc.paid_date
			from automotive_claims ac
			left join automotive_claim_payments acp on acp.automotive_claim_id = ac.id
			left join automotive_claim_payment_checks acpc on acpc.automotive_claim_payments_id = acp.id
			where ac.id = $1`, claim.ClaimID)
		if err != nil && err != sql.ErrNoRows {
			return nil, errors.Wrap(err, "failed to get claims")
		}

		var foundAuthAmount bool
		var negativeCheckAmount decimal.Decimal
		var paymentDates []time.Time
		var checks []string
		var processedClaimPayments []int

		paymentProcessed := func(autoClaimPaymentID null.Int) bool {
			if !autoClaimPaymentID.Valid {
				return false
			}
			for _, v := range processedClaimPayments {
				if v == int(autoClaimPaymentID.Int64) {
					return true
				}
			}
			processedClaimPayments = append(processedClaimPayments, int(autoClaimPaymentID.Int64))
			return false
		}

		for _, payment := range payments {

			// Set authorization amount it will be always same for all payments
			if !payment.AuthorizedAmount.Equal(decimal.Zero) && !foundAuthAmount {
				foundAuthAmount = true
				autoClaimList[i].AuthorizedAmount = payment.AuthorizedAmount
			}

			// We add up all the ngeative claim amount
			if payment.CheckPayment.Valid && !paymentProcessed(payment.AutoClaimPaymentID) && payment.CheckPayment.Decimal.LessThan(decimal.Zero) {
				negativeCheckAmount = negativeCheckAmount.Add(payment.CheckPayment.Decimal)
			}

			if payment.PaidDate.Valid {
				paymentDates = append(paymentDates, payment.PaidDate.Time)
			}

			if payment.CheckNumber.Valid {
				checks = append(checks, payment.CheckNumber.String)
			}
		}

		// We calcualte actual amount by reducing negative check amount
		autoClaimList[i].Amount = autoClaimList[i].AuthorizedAmount.Add(negativeCheckAmount)
		autoClaimList[i].PaymentDates = paymentDates
		autoClaimList[i].CheckNumber = strings.Join(checks, " ")
	}
	return autoClaimList, nil
}

// ClaimShow returns the autoclaim matching id
func ClaimShow(res http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	id := chi.URLParam(req, "id")
	// Get the auto claim from database
	txn := res.(newrelic.Transaction)
	autoClaim, err := ClaimSelect(txn, req, id)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error getting auto claim from database"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting Auto claim from database", nil)
	}

	return http.StatusOK, map[string]interface{}{"auto_claim": autoClaim}
}

// ClaimHistory returns claim history for given contract_id
func ClaimHistory(res http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	ctx := req.Context()

	contractID := chi.URLParam(req, "contract_id")
	productCode := chi.URLParam(req, "product_code")

	sortOrder := "desc"
	if req.FormValue("sort_by_date") == "asc" {
		sortOrder = "asc"
	}
	if productCode == "GAP" {
		claims, err := gapClaimsByContract(ctx, contractID)
		if err != nil && err != sql.ErrNoRows {
			handlers.ReportError(req, errors.Wrap(err, "error getting gap claims from database"))
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting GAP claims from database", nil)
		}
		return http.StatusOK, map[string]interface{}{"auto_claim_history": claims, "count": len(claims)}
	}
	if productCode == "VTA" {
		claims, err := vtaClaimsByContract(ctx, contractID)
		if err != nil && err != sql.ErrNoRows {
			handlers.ReportError(req, errors.Wrap(err, "error getting vta claims from database"))
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting VTA claims from database", nil)
		}
		return http.StatusOK, map[string]interface{}{"auto_claim_history": claims, "count": len(claims)}
	}
	if productCode == "LWT" {
		claims, err := lwtClaimsByContract(ctx, contractID)
		if err != nil && err != sql.ErrNoRows {
			handlers.ReportError(req, errors.Wrap(err, "error getting lwt claims from database"))
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting LWT claims from database", nil)
		}
		return http.StatusOK, map[string]interface{}{"auto_claim_history": claims, "count": len(claims)}
	}
	if productCode == "KEY" {
		productCode = "KE%"
	}
	if productCode == "VSC" {
		if strings.HasSuffix(contractID, "VSC") && !strings.HasPrefix(contractID, "TCA") {
			contractID = contractID[:len(contractID)-len("VSC")] + "%"
		}
	}
	claims, err := autoClaimsByContract(ctx, contractID, productCode, sortOrder)
	if err != nil && err != sql.ErrNoRows {
		handlers.ReportError(req, errors.Wrap(err, "error getting claims from database"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting claims from database", nil)
	}

	return http.StatusOK, map[string]interface{}{"auto_claim_history": claims, "count": len(claims)}
}

func claimExists(claimID int) (bool, error) {
	id := 0
	err := db.Get().Get(&id, `select id from automotive_claims where id = $1`, claimID)
	if err != nil {
		if err == sql.ErrNoRows {
			return false, nil
		}
		return false, err
	}
	return true, nil
}

type claimPayloadDB struct {
	ID                         int             `db:"id"`
	OwnerID                    int             `db:"owner_id"`
	ReassignmentStatus         string          `db:"reassignment_status"`
	ProductCode                string          `db:"product_code"`
	RO                         string          `db:"ro"`
	Status                     string          `db:"status"`
	PayType                    string          `db:"pay_type"`
	RoOpenedDate               pq.NullTime     `db:"ro_opened_date"`
	DateOfClaimReceived        time.Time       `db:"date_of_claim_received"`
	RoMileage                  int             `db:"ro_mileage"`
	FacilityID                 sql.NullInt64   `db:"facility_id"`
	FacilityName               string          `db:"facility_name"`
	FacilityAddress            string          `db:"facility_address"`
	FacilityPostalCode         string          `db:"facility_postal_code"`
	FacilityCity               string          `db:"facility_city"`
	FacilityStateCode          string          `db:"facility_state_code"`
	FacilityCountry            string          `db:"facility_country"`
	FacilityPhone              string          `db:"facility_phone"`
	FacilityFax                string          `db:"facility_fax"`
	IsROCustomerValid          string          `db:"is_ro_customer_valid"`
	IsROContractVinMatch       string          `db:"is_ro_contract_vin_match"`
	IsROContractMileageValid   string          `db:"is_ro_contract_mileage_valid"`
	LaborRate                  decimal.Decimal `db:"labor_rate"`
	TaxLabor                   decimal.Decimal `db:"tax_labor"`
	TaxParts                   decimal.Decimal `db:"tax_parts"`
	Advisor                    string          `db:"advisor"`
	Estimate                   decimal.Decimal `db:"estimate"`
	TaxAdjustment              decimal.Decimal `db:"tax_adjustment"`
	AutoApproved               bool            `db:"auto_approved"`
	PreAuthAmount              decimal.Decimal `db:"pre_auth_amount"`
	Deductible                 decimal.Decimal `db:"deductible"`
	UnidataClaimNumber         int             `db:"unidata_claim_number"`
	TotalTax                   decimal.Decimal `db:"total_tax"`
	RequestedTotal             decimal.Decimal `db:"requested_total"`
	CanceledReason             string          `db:"canceled_reason"`
	CanceledOtherReason        null.String     `db:"canceled_other_reason"`
	ClaimType                  string          `db:"claim_type"`
	Coverage                   string          `db:"coverage"`
	ContractStoreID            int             `db:"contract_store_id"`
	ContractDeductible         decimal.Decimal `db:"contract_deductible"`
	DeductibleCollect          bool            `db:"deductible_collect"`
	ReviewInspection           string          `db:"review_inspection"`
	ActualPaidAmount           decimal.Decimal `db:"actual_paid_amount"`
	RepairingFacilityLaborRate decimal.Decimal `db:"repairing_facility_labor_rate"`
}

// ClaimUpdate updates a automotive Claim
func ClaimUpdate(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	ctx := req.Context()
	claimResp, err := getClaimByID(chi.URLParam(req, "id"))
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage(nil, "Claim not found", nil)
		}
		handlers.ReportError(req, errors.Wrap(err, "error while loading claim"))
		return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Error loading claim", nil)
	}

	if claimResp.ClaimType == db.ClaimTypeSB {
		handlers.ReportError(req, errors.Wrap(err, "Updating SB claims not allowed."))
		return http.StatusBadRequest, handlers.ErrorMessage(nil, "Updating SB claims not allowed.", nil)
	}

	claimReq := ClaimPayload{}
	err = claimFromReq(&claimReq, req)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "malformed claim data for create."))
		return http.StatusBadRequest, handlers.ErrorMessage(nil, "Malformed claim data for create.", nil)
	}
	if user.ID != claimResp.OwnerID {
		handlers.ReportError(req, errors.New("only claim owner can update claim"))
		return http.StatusUnauthorized, handlers.ErrorMessage(nil, "Only claim owner can update claim.", nil)
	}

	claimReq.UpdatedByUserID = user.ID
	claimReq.ProductCode = claimResp.ProductCode

	cleanClaim(&claimReq)

	formErrors, err := validateClaim(ctx, &claimReq, ClaimRequestUpdate, &user)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error while validating claim information"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error while validating claim information", nil)
	}
	if user.ID == claimResp.OwnerID && (claimResp.ReassignmentStatus == db.AutoClaimReassignmentStatusNew ||
		claimResp.ReassignmentStatus == db.AutoClaimReassignmentStatusRejected) {
		claimReq.ReassignmentStatus = db.AutoClaimReassignmentStatusAccepted
	} else {
		claimReq.ReassignmentStatus = claimResp.ReassignmentStatus
	}

	facilityDetails := struct {
		StoreID      null.Int       `db:"store_id"`
		FacilityCode string         `db:"facility_code"`
		VendorID     sql.NullString `db:"vendor_id"`
	}{}

	if claimReq.FacilityID > 0 {
		err = db.Get().GetContext(ctx, &facilityDetails, `select store_id, facility_code, vendor_id from automotive_facilities where id = $1`, claimReq.FacilityID)
		if err != nil {
			handlers.ReportError(req, errors.Wrap(err, "error while getting store information."))
			return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Error while getting store information", nil)
		}
		// Note : When a service contract has disappearing deductible as an option, we need to check that the Facility for the repair matches the store where the contract was purchased.
		// If they match, the deductible should be $0 rather than what is on the contract.
		if claimReq.ProductCode == db.ProductCodeService && int64(claimReq.FacilityID) != claimResp.FacilityID.Int64 {
			coverageList, err := getServiceCoverage(claimReq.ID, claimResp.Coverage)
			if err != nil {
				handlers.ReportError(req, errors.Wrap(err, "error while getting coverage information."))
				return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Error while getting coverage information", nil)
			}
			changedDeductible := false
			for _, coverage := range coverageList {
				if coverage.Flag == serviceCoverageFlags[db.CoverageDisappearingDeductible] {
					if coverage.Value && facilityDetails.StoreID.Int64 == int64(claimResp.ContractStoreID) {
						claimReq.Deductible = decimal.Zero
						changedDeductible = true
					}
					break
				}
			}
			if !changedDeductible {
				claimReq.Deductible = claimResp.ContractDeductible
			}
		}
		if facilityDetails.VendorID.String == "" && claimReq.Status == db.AutoClaimStatusOpen && claimReq.PayType != db.PayTypeCreditCard {
			claimReq.Status = db.AutoClaimStatusWaitingOnVendor
		}
	}

	vendorID := facilityDetails.VendorID.String
	if claimReq.PayType == db.PayTypeCustomer {
		err = db.Get().GetContext(ctx, &vendorID, "select customer_payee_vendor_id from automotive_claims where id = $1", claimReq.ID)
		if err != nil {
			handlers.ReportError(req, errors.Wrap(err, "error while getting customer payee vendor_id."))
			return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Error while getting customer payee vendor_id", nil)
		}
	}

	if claimReq.Status == db.AutoClaimStatusReturned {
		if claimResp.Status == db.AutoClaimStatusPayable {
			userID, err := userByStatus(claimReq.ID, db.AutoClaimStatusPayable)
			if err != nil {
				handlers.ReportError(req, errors.Wrap(err, "error while original owner of claim."))
				return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Not able to fetch the original owner of claim", nil)
			}
			claimReq.OwnerID = userID
		}
	}

	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "database error beginning transaction for Auto claim update."))
		return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Database error updating claim", nil)
	}

	prevGoodwillAmount, err := getGoodWillAmount(tx, claimReq.ID)
	if err != nil {
		_ = tx.Rollback()
		handlers.ReportError(req, errors.Wrap(err, "database error fetching pre goodwill amount"))
		return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Database error updating claim", nil)
	}

	// update complaint
	for _, complaint := range claimReq.Complaints {
		err = updateComplaint(tx, &complaint)
		if err != nil {
			_ = tx.Rollback()
			handlers.ReportError(req, errors.Wrap(err, "database error updating Auto claim"))
			return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Database error updating claim", nil)
		}
		for _, part := range complaint.Parts {
			err = updatePart(tx, &part)
			if err != nil {
				_ = tx.Rollback()
				handlers.ReportError(req, errors.Wrap(err, "database error updating Auto claim parts"))
				return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Database error updating claim", nil)
			}
		}
		for _, labor := range complaint.Labor {
			err = updateLabor(tx, &labor)
			if err != nil {
				_ = tx.Rollback()
				handlers.ReportError(req, errors.Wrap(err, "database error updating Auto claim labors"))
				return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Database error updating claim", nil)
			}
		}
		for _, towing := range complaint.Towings {
			err = updateTowing(ctx, tx, &towing)
			if err != nil {
				_ = tx.Rollback()
				handlers.ReportError(req, errors.Wrap(err, "database error updating Auto claim towings"))
				return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Database error updating claim", nil)
			}
		}

		for _, sublet := range complaint.Sublets {
			err = updateSublet(ctx, tx, &sublet)
			if err != nil {
				_ = tx.Rollback()
				handlers.ReportError(req, errors.Wrap(err, "database error updating Auto claim sublets"))
				return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Database error updating claim", nil)
			}
		}

		for _, rental := range complaint.Rentals {
			err = updateRental(ctx, tx, &rental)
			if err != nil {
				_ = tx.Rollback()
				handlers.ReportError(req, errors.Wrap(err, "database error updating Auto claim rentals"))
				return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Database error updating claim", nil)
			}
		}

		for _, misc := range complaint.Miscs {
			err = updateMisc(ctx, tx, &misc)
			if err != nil {
				_ = tx.Rollback()
				handlers.ReportError(req, errors.Wrap(err, "database error updating Auto claim miscs"))
				return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Database error updating claim", nil)
			}
		}
	}

	var complts []complaintPayload
	// Delete old complain if RO is changed
	if claimResp.RO != claimReq.RO {
		deletedCDKComplaints := false
		// First delete old cdk complaints
		for _, complaint := range claimReq.Complaints {
			if !complaint.IsManual {
				_, err := deleteComplaint(tx, complaint.ID)
				if err != nil {
					_ = tx.Rollback()
					handlers.ReportError(req, errors.Wrap(err, "failed to delete old CDK complaints"))
					return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Error deleting old CDK complaints", nil)
				}
				deletedCDKComplaints = true
			} else {
				complts = append(complts, complaint)
			}
		}

		if deletedCDKComplaints {
			err = addClaimNote(ctx, tx, claimReq.ID, claimReq.UpdatedByUserID, "Deleted CDK complaints for old RO: "+claimResp.RO)
			if err != nil {
				_ = tx.Rollback()
				handlers.ReportError(req, errors.Wrap(err, "error adding note for cdk"))
				return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Database error updating claim", nil)
			}
		}
		// if there is change in ro and claim has no complaints then avoid changing status to approved.
		if len(complts) == 0 && (claimReq.Status == db.AutoClaimStatusApproved || claimReq.Status == db.AutoClaimStatusAuthorizedCCClaim || claimReq.Status == db.AutoClaimStatusPayable) {
			formErrors["complaints"] = "There should be at least one complaint added to the claim."
			claimReq.Status = claimResp.Status
		}
	}

	claimReq.FacilityCode = facilityDetails.FacilityCode
	// Create new CDK complaints if RO is found
	if claimReq.ROAutoUpdate {
		if !facilityDetails.StoreID.Valid {
			_ = tx.Rollback()
			handlers.ReportError(req, errors.New("store id is missing"))
			return http.StatusBadRequest, handlers.ErrorMessage(nil, fmt.Sprintf("Store id is missing for facility %s", facilityDetails.FacilityCode), nil)
		}

		roDetails, dmsProvider, err := getRO(ctx, claimReq.ID, int(facilityDetails.StoreID.Int64), claimReq.RO)
		if err != nil {
			_ = tx.Rollback()
			handlers.ReportError(req, err)
			if strings.HasPrefix(err.Error(), "RO Number not found") {
				return http.StatusNotFound, handlers.ErrorMessage(nil, "RO Not found", nil)
			}
			return http.StatusInternalServerError, handlers.ErrorMessage(nil, "DMS Provider service error", nil) // TODO timeout check
		}

		var ro dms.RODetail
		if len(roDetails) > 1 {
			if claimReq.ROSelectedDate.IsZero() {
				return http.StatusConflict, map[string]interface{}{"ros": roDetails}
			}
			for _, roDetail := range roDetails {
				if roDetail.Date.Equal(claimReq.ROSelectedDate) {
					ro = roDetail
					break
				}
			}
		} else if len(roDetails) == 1 {
			ro = roDetails[0]
		} else {
			return http.StatusNotFound, handlers.ErrorMessage(nil, "Insufficient RO Data for Creating Claim", nil)
		}

		if ro.Customer.Email != claimReq.EmailAddress ||
			ro.Customer.PostalCode != claimReq.PostalCode ||
			ro.Customer.City != claimReq.City ||
			ro.Customer.State != claimReq.State ||
			ro.Customer.Address != claimReq.StreetAddress ||
			ro.Customer.Phone != claimReq.PhoneNumber {
			claimReq.IsROCustomerValid = "false"
		}

		claimReq.RoOpenedDate = ro.Date
		claimReq.RoMileage = ro.Vehicle.Odometer

		activeRates, err := getFacilityActiveRates(ctx, claimReq.FacilityID, claimReq.RoOpenedDate)
		if err != nil {
			_ = tx.Rollback()
			handlers.ReportError(req, errors.Wrap(err, "failed to get facility details"))
			return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Database error updating claim", nil)
		}

		for _, v := range activeRates {
			if v.RateType == LaborRate {
				claimReq.LaborRate = v.EffectiveRate
			}
			if v.RateType == LaborTax {
				claimReq.TaxLabor = v.EffectiveRate
			}
			if v.RateType == PartsTax {
				claimReq.TaxParts = v.EffectiveRate
			}
		}

		// when there is change in the RO reset the flag of ROMileage and ROVin if it's invalid, the code below will set to false again
		claimReq.IsROContractMileageValid = ""
		claimReq.IsROContractVinMatch = ""

		// if ending miles is set then only do the mileage check
		if int(claimReq.EndingMiles.Int64) > 0 && (ro.Vehicle.Odometer < claimReq.BeginningMiles || ro.Vehicle.Odometer > int(claimReq.EndingMiles.Int64)) {
			claimReq.IsROContractMileageValid = "false"
		}
		if ro.Vehicle.VIN != claimReq.VIN {
			claimReq.IsROContractVinMatch = "false"
		}
		// Fetch complaints from cdk only if store is available
		var newCDKComplaints []complaintPayload
		newCDKComplaints, err = complaintCreateRO(tx, ro.ROLines, claimReq.ID, claimResp.ProductCode, ro.Date, dmsProvider)
		if err != nil {
			_ = tx.Rollback()
			handlers.ReportError(req, errors.Wrap(err, "failed to add complaints"))
			return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Database error updating claim", nil)
		}

		// Get updated new complaints list
		claimReq.Complaints = append(complts, newCDKComplaints...)
		// update advisor in request, as fetched from CDK RO
		if claimReq.Advisor == "" {
			claimReq.Advisor = ro.ServiceAdvisor
		}
		// add note when RO lookup is success, it may not add new complaints
		err = addClaimNote(ctx, tx, claimReq.ID, claimReq.UpdatedByUserID, "Added cdk complaints for new RO. ")
		if err != nil {
			_ = tx.Rollback()
			handlers.ReportError(req, errors.Wrap(err, "error adding note for add complaints"))
			return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Database error updating claim", nil)
		}
	}

	// used ro list required for verification, as only manager can approved used ro claim
	var usedRO []string
	query := `select ro from automotive_claims ac where contract_number = $1 and status != $2 and id != $3`
	err = db.Get().SelectContext(ctx, &usedRO, query, claimReq.ContractNumber, db.AutoClaimStatusDeactivated, claimReq.ID)
	if err != nil && err != sql.ErrNoRows {
		handlers.ReportError(req, errors.Wrap(err, "error in getting used ro"))
		return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Database error in getting used ro.", nil)
	}

	duplicateROUsed := false
	for _, ro := range usedRO {
		if ro == claimReq.RO {
			duplicateROUsed = true
		}
	}

	var approvedLimit decimal.Decimal
	err = db.Get().GetContext(ctx, &approvedLimit, `
	select approved_limit
	from user_approved_limits
	where user_id = $1 and deleted_at is null`,
		user.ID)
	if err != nil && err != sql.ErrNoRows {
		handlers.ReportError(req, errors.Wrap(err, "error while fetching pre approved limit for user."))
		return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Error while fetching pre approved limit for user", nil)
	}

	// Auto approval should not be done if there is a VIN warning
	if claimReq.Status == db.AutoClaimStatusPayable && (claimReq.PayType == db.PayTypeCreditCard || vendorID != "") &&
		claimReq.IsROContractMileageValid != "false" && claimReq.IsROContractVinMatch != "false" && !duplicateROUsed {

		if claimReq.Estimate.LessThanOrEqual(approvedLimit) {
			claimReq.Status = db.AutoClaimStatusApproved
			if claimReq.PayType == db.PayTypeCreditCard {
				claimReq.Status = db.AutoClaimStatusAuthorizedCCClaim
			}
			claimReq.AutoApproved = true
		}
	}

	// If previous status is not approved/AutoClaimStatusAuthorizedCCClaim and now we are approving the claim check for contract status
	// If contract status anything other than Active, only manager should be able to approve the claim
	if ((claimReq.Status == db.AutoClaimStatusApproved && claimResp.Status != db.AutoClaimStatusApproved) ||
		(claimReq.Status == db.AutoClaimStatusAuthorizedCCClaim && claimResp.Status != db.AutoClaimStatusAuthorizedCCClaim)) &&
		!user.HasRole(db.RoleAutoClaimsManager) {
		if claimReq.IsROContractMileageValid == "false" || claimReq.IsROContractVinMatch == "false" {
			return http.StatusUnauthorized, handlers.ErrorMessage(err, "Contract has VIN warning. Contact your manager.", nil)
		}

		txn := newrelic.FromContext(ctx)
		contract, err := handlers.GetContractByID(txn, req, claimReq.ContractNumber)
		if err != nil {
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Invalid response for Whiz-contract-detail", nil)
		}

		if contract.Contract.Status != db.ContractStatusActive {
			return http.StatusUnauthorized, handlers.ErrorMessage(err, "Contract is in non active status. Contact your manager.", nil)
		}
	}

	// manager cannot approve beyond own limit
	if (claimReq.Status == db.AutoClaimStatusApproved && claimResp.Status != db.AutoClaimStatusApproved) ||
		(claimReq.Status == db.AutoClaimStatusAuthorizedCCClaim && claimResp.Status != db.AutoClaimStatusAuthorizedCCClaim) {
		// agent/ manager can not approve claim above his/her own limit
		if claimReq.Estimate.GreaterThan(approvedLimit) {
			return http.StatusUnauthorized, handlers.ErrorMessage(errors.New("agent/ manager cannot approve above own limit"),
				"Agent / Manager cannot approve beyond own approval limit", nil)
		}
	}

	// in case of autosave don't save status
	if claimReq.IsAutoSave && claimReq.Status != claimResp.Status {
		claimReq.Status = claimResp.Status
		claimReq.OwnerID = claimResp.OwnerID
	}

	if (claimReq.Status == db.AutoClaimStatusReturned || claimReq.Status == db.AutoClaimStatusDenied) && claimReq.CanceledReason == "" {
		formErrors["canceled_reason"] = "Reason is required when claim is returned or denied"
		claimReq.Status = claimResp.Status
	}

	// if status is payable or approved but form data is incorrect,
	// then change status back to previous status
	if (claimReq.Status == db.AutoClaimStatusPayable || claimReq.Status == db.AutoClaimStatusApproved || claimReq.Status == db.AutoClaimStatusAuthorizedCCClaim || claimReq.Status == db.AutoClaimStatusDenied || claimReq.Status == db.AutoClaimStatusReturned) &&
		len(formErrors) > 0 && claimReq.Status != claimResp.Status {
		formErrors["change_status"] = fmt.Sprintf("Status changed to %s from %s", claimResp.Status, claimReq.Status)
		claimReq.Status = claimResp.Status
		claimReq.OwnerID = claimResp.OwnerID
	}

	// if claim is Deactivated, claim status must be Open or PreAuth
	if claimReq.Status == db.AutoClaimStatusDeactivated &&
		!(claimResp.Status == db.AutoClaimStatusPreAuth || claimResp.Status == db.AutoClaimStatusOpen) {
		formErrors["change_status"] = fmt.Sprintf("Claim can only be deactivated from Open or PreAuth status. Status changed to %s from %s", claimResp.Status, claimReq.Status)
		claimReq.Status = claimResp.Status
	}

	newClaimStatus := claimReq.Status

	err = updateRecordsNotes(ctx, &claimReq, claimResp, prevGoodwillAmount, tx)
	if err != nil {
		_ = tx.Rollback()
		handlers.ReportError(req, errors.Wrap(err, "database error updating record notes in auto claim"))
		return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Database error updating auto claim", nil)
	}

	// if claim status is approved or payable then don't save status it now,
	// save it after unidata status is successful
	if (claimReq.Status == db.AutoClaimStatusApproved || claimReq.Status == db.AutoClaimStatusPayable) && claimReq.PayType != db.PayTypeCreditCard {
		claimReq.Status = claimResp.Status
	}

	err = updateClaim(ctx, tx, &claimReq)
	if err != nil {
		_ = tx.Rollback()
		handlers.ReportError(req, errors.Wrap(err, "error adding note for add goodwill"))
		return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Database error updating claim", nil)
	}

	if claimReq.WarningMessageNote != "" {
		note := RecordNotePayload{}
		note.AutomotiveClaimID = claimReq.ID
		note.IsManual = true
		note.CreatedByUserID = claimReq.UpdatedByUserID
		note.NotesText = fmt.Sprintf("Outstanding Warning Note: %s", claimReq.WarningMessageNote)
		_, err := InsertRecordNote(ctx, &note, tx)
		if err != nil {
			_ = tx.Rollback()
			handlers.ReportError(req, errors.Wrap(err, "error adding note for data mismatch warning"))
			return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Database error updating claim", nil)
		}
	}

	// Update contract status
	if db.ProductCodeMaintenance == claimReq.ProductCode {
		var c []claimDB
		txn := newrelic.FromContext(ctx)

		query := `select ac.id, ac.date_of_claim_received claim_date, ac.ro repair_order_number,
					case when ac.status in('CheckWritten', 'Reversed', 'CCPaid', 'Adjusted') 
					then coalesce(acp.amount, ac.estimate) else ac.estimate end claim_amount, 
					ac.status claim_status, coalesce(acp.amount, ac.estimate) as authorized_amount
				from automotive_claims ac 
					left join automotive_claim_complaints acc on acc.automotive_claim_id = ac.id
					left join automotive_claim_payments acp on ac.id = acp.automotive_claim_id
					left join automotive_claim_payment_checks acpc on acp.id = acpc.automotive_claim_payments_id
				where contract_number = $1 
					and product_code = $2 
					and ac.status not in ('Deactivated', 'Denied')
				group by ac.id, ac.date_of_claim_received, ac.ro_mileage, ac.ro, ac.estimate, 
					acpc.paid_date, ac.advisor, ac.status, acpc.check_number, acp.amount`

		err = db.Get().SelectContext(ctx, &c, query, claimReq.ContractNumber, claimReq.ProductCode)
		if err != nil {
			_ = tx.Rollback()
			handlers.ReportError(req, errors.Wrap(err, "error getting claim details"))
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error getting claim details", nil)
		}

		usedCoupons := getUsedCouponCount(c)

		var couponCount = len(usedCoupons)
		if newClaimStatus == db.AutoClaimStatusDeactivated || newClaimStatus == db.AutoClaimStatusDenied {
			var removeCurrentClaim bool
			for k := range usedCoupons {
				if k == claimReq.ID {
					removeCurrentClaim = true
					break
				}
			}

			if removeCurrentClaim {
				couponCount--
			}
		}
		err = handlers.UpdateContractStatus(txn, req, claimReq.ContractNumber, couponCount)
		if err != nil {
			_ = tx.Rollback()
			handlers.ReportError(req, errors.Wrap(err, "error updating contract status"))
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error updating claim status	", nil)
		}
	}

	err = tx.Commit()
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "Database error committing transaction for Auto claim update"))
		return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Database error updating claim", nil)
	}

	// start new transaction
	tx, err = db.Get().Beginx()
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "database error beginning transaction for Auto claim update."))
		return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Database error updating claim", nil)
	}
	if (newClaimStatus == db.AutoClaimStatusApproved || newClaimStatus == db.AutoClaimStatusPayable || newClaimStatus == db.AutoClaimStatusReturned || newClaimStatus == db.AutoClaimStatusDenied) && !claimReq.IsAutoSave && claimReq.PayType != db.PayTypeCreditCard {
		// update claim
		err = updateClaimStatus(claimReq.ID, newClaimStatus, tx)
		if err != nil {
			_ = tx.Rollback()
			handlers.ReportError(req, errors.Wrap(err, "error updating claim status"))
			return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Claim update in unidata failed.", nil)
		}
	}

	if newClaimStatus != claimResp.Status {
		err := addClaimNote(ctx, tx, claimReq.ID, claimReq.UpdatedByUserID, db.AutomotiveRecordNoteDescription[newClaimStatus])
		if err != nil {
			_ = tx.Rollback()
			handlers.ReportError(req, errors.Wrap(err, "errors in updating records notes"))
			return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Database error updating auto claim.", nil)
		}
	}

	// Add pre-auth number when claim is put in pre-auth status
	if claimReq.Status == db.AutoClaimStatusPreAuth {
		err = insertClaimPreAuthNumber(ctx, tx, &claimReq)
		if err != nil {
			_ = tx.Rollback()
			handlers.ReportError(req, errors.Wrap(err, "error inserting pre-auth number"))
			return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Database error updating claim", nil)
		}
	}

	err = tx.Commit()
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "Database error committing transaction for Auto claim update"))
		return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Database error updating claim", nil)
	}

	if len(formErrors) > 0 {
		return http.StatusMultiStatus, map[string]interface{}{"id": claimReq.ID, "errors": formErrors}
	}

	return http.StatusOK, map[string]interface{}{"id": claimReq.ID}
}

func getFacilityActiveRates(ctx context.Context, facilityID int, roOpenedDate time.Time) ([]effectiveRate, error) {
	var laborTax []effectiveRate
	var laborRate []effectiveRate
	var partsTax []effectiveRate
	var activeRates []effectiveRate

	query := `select * from automotive_facilities_effective_rates where facility_id = $1 and rate_type = $2 order by effective_date desc, id desc`
	err := db.Get().Unsafe().SelectContext(ctx, &laborTax, query, facilityID, LaborTax)
	if err != nil {
		return activeRates, errors.Wrap(err, "error getting labor tax")
	}

	err = db.Get().Unsafe().SelectContext(ctx, &laborRate, query, facilityID, LaborRate)
	if err != nil {
		return activeRates, errors.Wrap(err, "error getting labor rate")
	}

	err = db.Get().Unsafe().SelectContext(ctx, &partsTax, query, facilityID, PartsTax)
	if err != nil {
		return activeRates, errors.Wrap(err, "error getting part tax")
	}

	currentActiveLaborRate, err := getExistingActiveLaborRate(ctx, facilityID)
	if err != nil {
		return activeRates, errors.Wrap(err, "error getting labor rate")
	}

	currentActiveLaborTax, err := getExistingActiveLaborTax(ctx, facilityID)
	if err != nil {
		return activeRates, errors.Wrap(err, "error getting labor tax")
	}

	currentActivePartsTax, err := getExistingActivePartsTax(ctx, facilityID)
	if err != nil {
		return activeRates, errors.Wrap(err, "error getting parts tax")
	}

	activeRates = append(activeRates, getActiveRates(laborRate, currentActiveLaborRate, roOpenedDate))
	activeRates = append(activeRates, getActiveRates(laborTax, currentActiveLaborTax, roOpenedDate))
	activeRates = append(activeRates, getActiveRates(partsTax, currentActivePartsTax, roOpenedDate))

	return activeRates, nil
}

func getActiveRates(rates []effectiveRate, currentActiveRate effectiveRate, roOpenedDate time.Time) effectiveRate {
	var activeRate effectiveRate

	if len(rates) == 0 {
		return activeRate
	}

	if len(rates) == 1 {
		return rates[0]
	}

	maxEffectiveRate := time.Time{}
	for _, v := range rates {
		if (v.EffectiveDate.Time.Equal(roOpenedDate) || v.EffectiveDate.Time.Before(roOpenedDate)) && v.EffectiveDate.Time.After(maxEffectiveRate) {
			maxEffectiveRate = v.EffectiveDate.Time
			activeRate = effectiveRate{
				ID:              v.ID,
				FacilityID:      v.FacilityID,
				FacilityCode:    v.FacilityCode,
				EffectiveRate:   v.EffectiveRate,
				EffectiveDate:   v.EffectiveDate,
				RateType:        v.RateType,
				Active:          true,
				CreatedAt:       v.CreatedAt,
				CreatedByUserID: v.CreatedByUserID,
				IsNew:           v.IsNew,
			}
		}
	}

	if !activeRate.Active {
		activeRate = effectiveRate{
			ID:              currentActiveRate.ID,
			FacilityID:      currentActiveRate.FacilityID,
			FacilityCode:    currentActiveRate.FacilityCode,
			EffectiveRate:   currentActiveRate.EffectiveRate,
			EffectiveDate:   currentActiveRate.EffectiveDate,
			RateType:        currentActiveRate.RateType,
			Active:          true,
			CreatedAt:       currentActiveRate.CreatedAt,
			CreatedByUserID: currentActiveRate.CreatedByUserID,
			IsNew:           currentActiveRate.IsNew,
		}
	}

	return activeRate
}

func userByStatus(claimID int, status string) (int, error) {
	query := `select users.id
				from automotive_record_notes
				join users on users.id = automotive_record_notes.created_by_user_id
				join automotive_claims on automotive_record_notes.automotive_claim_id = automotive_claims.id
				where automotive_claims.id = $1  and notes_text = $2 order by automotive_record_notes.created_at desc limit 1`
	userID := 0
	err := db.Get().Get(&userID, query, claimID, db.AutomotiveRecordNoteDescription[status])
	return userID, err
}

func getClaimByID(id string) (claimPayloadDB, error) {
	query := `select id, owner_id, product_code, status, product_code, status, unidata_claim_number, date_of_claim_received, 
		facility_id, is_ro_customer_valid, is_ro_contract_vin_match, is_ro_contract_mileage_valid, estimate, tax_adjustment, total_tax, auto_approved, deductible, ro_mileage, pre_auth_amount, claim_type,
		ro_opened_date, requested_total,
		case when labor_rate is null then 0.0 else labor_rate end,
		case when tax_parts is null then 0.0 else tax_parts end,
		case when tax_labor is null then 0.0 else tax_labor end,
		case when reassignment_status is null then '' else reassignment_status end,
		case when advisor is null then '' else advisor end,
		case when pay_type is null then '' else pay_type end, 
		case when ro is null then '' else ro end,
		case when contract_store_id is null then 0 else contract_store_id end,
		coverage,
		contract_deductible, deductible_collect,
		repairing_facility_labor_rate
	from automotive_claims
	where id = $1`
	payload := claimPayloadDB{}
	err := db.Get().Get(&payload, query, id)
	return payload, err
}

func addClaimNote(ctx context.Context, tx *sqlx.Tx, claimID int, userID int, noteText string) error {
	note := RecordNotePayload{}
	note.AutomotiveClaimID = claimID
	note.IsManual = false
	note.CreatedByUserID = userID
	note.NotesText = noteText
	_, err := InsertRecordNote(ctx, &note, tx)
	return err
}

func calculateTotalTax(claim *ClaimPayload) decimal.Decimal {
	var totalTax decimal.Decimal
	for _, complaint := range claim.Complaints {
		if complaint.Status == db.AutoClaimComplaintStatusPayable {
			var partsAmount decimal.Decimal
			var laborsAmount decimal.Decimal
			for _, part := range complaint.Parts {
				partsAmount = partsAmount.Add(part.Approved)
			}
			for _, labor := range complaint.Labor {
				laborsAmount = laborsAmount.Add(labor.Approved)
			}
			decimal100 := decimal.NewFromFloat(100.0)

			totalTax = totalTax.Add(partsAmount.Mul(claim.TaxParts).Div(decimal100))
			totalTax = totalTax.Add(laborsAmount.Mul(claim.TaxLabor).Div(decimal100))
		}
	}
	return totalTax
}

func calculateGrandTotal(claim *ClaimPayload) decimal.Decimal {
	var grandTotal decimal.Decimal
	for _, complaint := range claim.Complaints {
		if complaint.Status == db.AutoClaimComplaintStatusPayable {
			var partsTotal decimal.Decimal
			var laborTotal decimal.Decimal
			var towingTotal decimal.Decimal
			var rentalTotal decimal.Decimal
			var subletTotal decimal.Decimal
			for _, part := range complaint.Parts {
				partsTotal = partsTotal.Add(part.Approved)
			}
			for _, labor := range complaint.Labor {
				laborTotal = laborTotal.Add(labor.Approved)
			}

			for _, towing := range complaint.Towings {
				towingTotal = towingTotal.Add(towing.Approved)
			}

			for _, rental := range complaint.Rentals {
				rentalTotal = rentalTotal.Add(rental.Approved)
			}

			for _, sublet := range complaint.Sublets {
				subletTotal = subletTotal.Add(sublet.Approved)
			}

			// Add to total count
			grandTotal = grandTotal.Add(partsTotal)
			grandTotal = grandTotal.Add(laborTotal)
			grandTotal = grandTotal.Add(towingTotal)
			grandTotal = grandTotal.Add(rentalTotal)
			grandTotal = grandTotal.Add(subletTotal)
		}
	}
	grandTotal = grandTotal.Sub(claim.Deductible)
	grandTotal = grandTotal.Add(claim.TotalTax)
	return grandTotal
}

func validateClaim(ctx context.Context, claim *ClaimPayload, requestType ClaimRequestType, user *db.User) (map[string]string, error) {
	formErrors := map[string]string{}

	stringOneOf := func(s string, list []string) bool {
		for _, l := range list {
			if l == s {
				return true
			}
		}
		return false
	}

	status := ""
	err := db.Get().Get(&status, `select status from automotive_claims where id = $1`, claim.ID)
	if err != nil {
		formErrors["status"] = "could not verify status of claim"
	}

	validateForUniData := false
	if claim.Status == db.AutoClaimStatusPayable || claim.Status == db.AutoClaimStatusApproved || claim.Status == db.AutoClaimStatusAuthorizedCCClaim || claim.PayType == db.PayTypeCreditCard || claim.Status == db.AutoClaimStatusDenied || claim.Status == db.AutoClaimStatusReturned {
		validateForUniData = true
	}

	if claim.CanceledReason == db.AutoClaimComplaintStatusReasonOther && len(claim.CanceledOtherReason.String) == 0 {
		formErrors["canceled_other_reason"] = "Denial note is required for other status reason."
	}

	if claim.Status == db.AutoClaimComplaintStatusDenied {
		for index := range claim.Complaints {
			if claim.Complaints[index].Status != db.AutoClaimComplaintStatusDenied {
				formErrors["status"] = "One or more complaint has not been denied."
				break
			}
		}
	}

	if validateForUniData && claim.ClaimType != db.ClaimTypeSB && claim.ClaimType != db.ClaimTypeLCA {
		if claim.FacilityID <= 0 {
			formErrors["facility"] = "Please select or add a facility to continue."
		}
		if claim.Advisor == "" && !claim.ChargeBack {
			formErrors["advisor"] = "Advisor is required"
		}
	}

	if requestType != ClaimRequestSubmitIntacct {
		if len(claim.Complaints) > 0 {
			for _, v := range claim.Complaints {
				if v.Status != db.AutoClaimComplaintStatusOpen && claim.ReviewInspection == "" {
					formErrors["claim_inspection"] = "Claim Inspection is required"
				}
			}
		}
	}

	if claim.Status == db.AutoClaimStatusPayable || claim.Status == db.AutoClaimStatusApproved || claim.Status == db.AutoClaimStatusAuthorizedCCClaim || claim.PayType == db.PayTypeCreditCard {
		if claim.RO == "" {
			formErrors["ro"] = "RO is required"
		}

		if claim.Estimate.LessThanOrEqual(decimal.Zero) && !claim.ChargeBack {
			formErrors["estimate"] = "Invalid claim estimate"
		}

		if claim.ClaimType != db.ClaimTypeSB && claim.ClaimType != db.ClaimTypeLCA {
			if len(claim.Complaints) == 0 && !claim.ChargeBack {
				formErrors["complaints"] = "There should be at least one complaint added to the claim."
			}

			if claim.Advisor == "" && !claim.ChargeBack {
				formErrors["advisor"] = "Advisor is required"
			}

			if calculateGrandTotal(claim).LessThanOrEqual(decimal.Zero) && !claim.ChargeBack {
				formErrors["invalid_total"] = "Total should be a non-zero value."
			}

			// validate complaints
			if !claim.ChargeBack {
				formErrors = validateComplaints(claim.Complaints, formErrors)
			}

			// Validate requested total
			if claim.RequestedTotal.LessThanOrEqual(decimal.Zero) && !claim.ChargeBack {
				formErrors["requested_total"] = "Requested total is required."
			}
		}

		if !stringOneOf(claim.PayType, db.PayTypes) {
			formErrors["pay_type"] = "Invalid PayType"
		}

		if claim.ProductCode == db.ProductCodeDrivePur {
			intacctCustomerID, err := validateDP(ctx, claim.ContractStoreID)
			if err != nil || intacctCustomerID == "" {
				formErrors["drive_pur_customer_id"] = "could not get customer_id for drive_pur claim " + err.Error()
			} else {
				claim.IntacctCustID = intacctCustomerID
			}
		}

		if claim.PayType != db.PayTypeCustomer {
			facilityCode := ""
			err = db.Get().Get(&facilityCode, "select facility_code from automotive_facilities where id = $1", claim.FacilityID)
			if err != nil {
				return formErrors, err
			}
		}
	}

	switch requestType {
	case ClaimRequestUpdate:
		if status == db.AutoClaimStatusApproved || status == db.AutoClaimStatusCheckWritten ||
			status == db.AutoClaimStatusWaitingForCheck || status == db.AutoClaimStatusCCPaid ||
			status == db.AutoClaimStatusDeactivated || status == db.AutoClaimStatusAuthorizedCCClaim {
			formErrors["read_only_status"] = "cannot modify claim when in approved or cc paid or deactivated status"
		}
		if !isValidClaimStatus(claim.Status) {
			formErrors["status"] = "claim status is invalid"
		}

	case ClaimRequestSubmitIntacct:
		if claim.Status != db.AutoClaimStatusApproved && claim.Status != db.AutoClaimStatusInvoiceSent &&
			claim.Status != db.AutoClaimStatusReversed && claim.Status != db.AutoClaimStatusAdjusted {
			formErrors["invalid_status"] = "invalid status for submitToIntacct"
		}
	}
	return formErrors, nil
}

// DP product should have associated customerID for the store from which the contract was purchased
func validateDP(ctx context.Context, storeID int) (string, error) {
	customerID := ""

	storeCode := ""
	err := db.Get().Get(&storeCode, "select code from stores where id = $1", storeID) // Validate store exists in DB
	if err != nil {
		return customerID, errors.Wrap(err, "Store not found, could not validate DP product")
	}

	customerID, err = intacct.CustomerID(ctx, storeCode)
	if err != nil {
		return customerID, errors.Wrap(err, "intacct lookup failed: could not get customerID for the store: "+storeCode)
	} else if customerID == "" {
		return customerID, errors.New("intacct lookup failed: could not get customerID for the store: " + storeCode)
	}
	return customerID, nil
}

func claimFromReq(claim *ClaimPayload, req *http.Request) error {
	dec := json.NewDecoder(req.Body)
	err := dec.Decode(&claim)
	return errors.Wrap(err, "decoding claim request failed")
}

// cleans up leading and trailing white-space etc...
func cleanClaim(claim *ClaimPayload) {
	claim.VIN = strings.TrimSpace(claim.VIN)
	claim.ContractNumber = strings.TrimSpace(claim.ContractNumber)
	claim.Status = strings.TrimSpace(claim.Status)
	claim.Advisor = strings.TrimSpace(claim.Advisor)
	claim.RO = strings.TrimSpace(claim.RO)
}

func updateClaim(ctx context.Context, tx *sqlx.Tx, claim *ClaimPayload) error {
	claimForDB := claimPayloadDB{}
	claimForDB.ID = claim.ID
	claimForDB.RO = claim.RO
	if !claim.RoOpenedDate.IsZero() {
		claimForDB.RoOpenedDate = pq.NullTime{Time: claim.RoOpenedDate, Valid: true}
	}

	claimForDB.RoMileage = claim.RoMileage
	if claim.FacilityID > 0 {
		claimForDB.FacilityID = sql.NullInt64{Int64: int64(claim.FacilityID), Valid: true}
	}
	claimForDB.FacilityName = claim.FacilityName
	claimForDB.IsROCustomerValid = claim.IsROCustomerValid
	claimForDB.IsROContractVinMatch = claim.IsROContractVinMatch
	claimForDB.IsROContractMileageValid = claim.IsROContractMileageValid
	claimForDB.FacilityAddress = claim.FacilityAddress
	claimForDB.FacilityPostalCode = claim.FacilityPostalCode
	claimForDB.FacilityCity = claim.FacilityCity
	claimForDB.FacilityStateCode = claim.FacilityStateCode
	claimForDB.FacilityCountry = claim.FacilityCountry
	claimForDB.FacilityPhone = claim.FacilityPhone
	claimForDB.FacilityFax = claim.FacilityFax
	claimForDB.LaborRate = claim.LaborRate
	claimForDB.TaxLabor = claim.TaxLabor
	claimForDB.TaxParts = claim.TaxParts
	claimForDB.Advisor = claim.Advisor
	claimForDB.ReassignmentStatus = claim.ReassignmentStatus
	claimForDB.Estimate = claim.Estimate
	claimForDB.PayType = claim.PayType
	claimForDB.TaxAdjustment = claim.TaxAdjustment
	claimForDB.Status = claim.Status
	claimForDB.OwnerID = claim.OwnerID
	claimForDB.AutoApproved = claim.AutoApproved
	claimForDB.PreAuthAmount = claim.PreAuthAmount
	claimForDB.Deductible = claim.Deductible
	claimForDB.TotalTax = claim.TotalTax
	claimForDB.RequestedTotal = claim.RequestedTotal
	claimForDB.CanceledReason = claim.CanceledReason
	claimForDB.CanceledOtherReason = claim.CanceledOtherReason
	claimForDB.DeductibleCollect = claim.DeductibleCollect
	claimForDB.ReviewInspection = claim.ReviewInspection
	claimForDB.ActualPaidAmount = claim.ActualPaidAmount
	claimForDB.RepairingFacilityLaborRate = claim.RepairingFacilityLaborRate

	query := `update automotive_claims
		set ro =:ro,
		ro_opened_date =:ro_opened_date,
		ro_mileage =:ro_mileage,
		facility_id =:facility_id,
		facility_name =:facility_name,
		facility_address =:facility_address,
		facility_postal_code =:facility_postal_code,
		facility_city =:facility_city,
		facility_state_code =:facility_state_code,
		facility_country =:facility_country,
		facility_phone =:facility_phone,
		facility_fax =:facility_fax,
		labor_rate =:labor_rate,
		tax_labor =:tax_labor,
		tax_parts =:tax_parts,
		advisor =:advisor,
		reassignment_status =:reassignment_status,
		estimate =:estimate,
		pay_type =:pay_type,
		tax_adjustment =:tax_adjustment,
		status =:status,
		owner_id =:owner_id,
		auto_approved =:auto_approved,
		is_ro_customer_valid =:is_ro_customer_valid,
		is_ro_contract_vin_match =:is_ro_contract_vin_match,
		is_ro_contract_mileage_valid =:is_ro_contract_mileage_valid,
		pre_auth_amount =:pre_auth_amount,
		deductible =:deductible,
		total_tax =:total_tax,
		requested_total =:requested_total,
		canceled_reason =:canceled_reason,
		canceled_other_reason = :canceled_other_reason,
		deductible_collect= :deductible_collect,
		review_inspection = :review_inspection,
		actual_paid_amount = :actual_paid_amount,
		repairing_facility_labor_rate = :repairing_facility_labor_rate
		where id =:id`

	stmt, err := tx.PrepareNamedContext(ctx, query)
	if err != nil {
		return errors.Wrap(err, "Error updating Claim, PrepareNamed failed")
	}
	defer func() { _ = stmt.Close() }()
	_, err = stmt.ExecContext(ctx, claimForDB)
	if err != nil {
		return errors.Wrap(err, "Error updating Claim, database error")
	}

	err = ClaimUpdated(ctx, tx, claim.ID, claim.UpdatedByUserID)
	if err != nil {
		return errors.Wrap(err, "Error inserting automotive_claim_updates")
	}

	return nil
}

func insertClaimPreAuthNumber(ctx context.Context, tx *sqlx.Tx, claim *ClaimPayload) error {
	query := `select id from automotive_claim_pre_auth_numbers where automotive_claim_id = $1`
	exID := 0
	err := tx.GetContext(ctx, &exID, query, claim.ID)
	if err != nil && err != sql.ErrNoRows {
		tx.Rollback()
		return err
	}
	if exID > 0 {
		// ID already exists, don't do anything
		return nil
	}
	query = `insert into automotive_claim_pre_auth_numbers(automotive_claim_id,created_by_user_id,created_at)
	values($1,$2,now() at time zone 'utc') on conflict(automotive_claim_id) do nothing returning id`
	newID := 0
	err = tx.GetContext(ctx, &newID, query, claim.ID, claim.UpdatedByUserID)
	if err != nil && err != sql.ErrNoRows {
		tx.Rollback()
		return err
	}
	if newID != 0 {
		preAuthNumber := "PA" + strconv.Itoa(newID)
		query = `update automotive_claim_pre_auth_numbers
		set pre_auth_number = $1
		where automotive_claim_id=$2`
		_, err = tx.ExecContext(ctx, query, preAuthNumber, claim.ID)
		if err != nil {
			tx.Rollback()
			return err
		}
	}
	return nil
}

func updateRecordsNotes(ctx context.Context, claimReq *ClaimPayload, claimDB claimPayloadDB, prevGoodwillAmount decimal.Decimal, tx *sqlx.Tx) error {
	var notes []string
	if claimReq.RO != claimDB.RO {
		if claimDB.RO == "" {
			notes = append(notes, fmt.Sprintf("RO %s is added", claimReq.RO))
		} else if claimReq.RO == "" {
			notes = append(notes, fmt.Sprintf("RO %s is removed", claimDB.RO))
		} else {
			notes = append(notes, fmt.Sprintf("RO is changed to %s from %s", claimReq.RO, claimDB.RO))
		}
	}

	if !claimReq.RoOpenedDate.Equal(claimDB.RoOpenedDate.Time) {
		if claimDB.RoOpenedDate.Time.IsZero() {
			notes = append(notes, fmt.Sprintf("RO opened date %s is added", claimReq.RoOpenedDate.Format("01-02-2006")))
		} else if claimReq.RoOpenedDate.IsZero() {
			notes = append(notes, fmt.Sprintf("RO opened date %s is removed", claimDB.RoOpenedDate.Time.Format("01-02-2006")))
		} else {
			notes = append(notes, fmt.Sprintf("RO opened date is changed to %s from %s",
				claimReq.RoOpenedDate.Format("01-02-2006"), claimDB.RoOpenedDate.Time.Format("01-02-2006")))
		}
	}
	if claimReq.RoMileage != claimDB.RoMileage {
		if claimDB.RoMileage == 0 {
			notes = append(notes, fmt.Sprintf("Mileage %d is added", claimReq.RoMileage))
		} else if claimReq.RoMileage == 0 {
			notes = append(notes, fmt.Sprintf("Mileage %d is removed", claimDB.RoMileage))
		} else {
			notes = append(notes, fmt.Sprintf("Mileage is changed to %d from %d", claimReq.RoMileage, claimDB.RoMileage))
		}
	}
	if claimReq.Advisor != claimDB.Advisor {
		if claimDB.Advisor == "" {
			notes = append(notes, fmt.Sprintf("Advisor %s is added", claimReq.Advisor))
		} else if claimReq.Advisor == "" {
			notes = append(notes, fmt.Sprintf("Advisor %s is removed", claimDB.Advisor))
		} else {
			notes = append(notes, fmt.Sprintf("Advisor is changed to %s from %s", claimReq.Advisor, claimDB.Advisor))
		}
	}
	if claimReq.FacilityID != int(claimDB.FacilityID.Int64) {
		if claimDB.FacilityID.Int64 == 0 {
			notes = append(notes, fmt.Sprintf("Facility %s is added", claimReq.FacilityName))
		} else if claimReq.FacilityID == 0 {
			notes = append(notes, "Facility is removed")
		} else {
			notes = append(notes, fmt.Sprintf("Facility is changed to %s", claimReq.FacilityName))
		}
	}

	if !claimReq.LaborRate.Equal(claimDB.LaborRate) {
		notes = append(notes, fmt.Sprintf("Labor rate is changed from %s to %s  in claim", claimDB.LaborRate, claimReq.LaborRate))
	}

	if !claimReq.TaxParts.Equal(claimDB.TaxParts) {
		notes = append(notes, fmt.Sprintf("Tax parts is changed from %s to %s in claim", claimDB.TaxParts, claimReq.TaxParts))
	}

	if !claimReq.TaxLabor.Equal(claimDB.TaxLabor) {
		notes = append(notes, fmt.Sprintf("Tax labor is changed from %s to %s in claim", claimDB.TaxLabor, claimReq.TaxLabor))
	}

	updatedEstimate := claimReq.Estimate.Mul(decimal.NewFromFloat(100.0)).DivRound(decimal.NewFromFloat(100.0), 2)
	existingEstimate := claimDB.Estimate.Mul(decimal.NewFromFloat(100.0)).DivRound(decimal.NewFromFloat(100.0), 2)

	if !updatedEstimate.Equal(existingEstimate) {
		notes = append(notes, fmt.Sprintf("Estimate is changed from %s to %s", claimDB.Estimate, claimReq.Estimate))
	}

	if claimReq.PayType != claimDB.PayType {
		notes = append(notes, fmt.Sprintf("Payment type is changed from %s to %s", claimDB.PayType, claimReq.PayType))
	}

	if claimReq.AutoApproved != claimDB.AutoApproved {
		if claimDB.AutoApproved {
			notes = append(notes, "Auto approved flag is set")
		} else {
			notes = append(notes, "Auto approved flag is reset")
		}
	}

	if !claimReq.PreAuthAmount.Equal(claimDB.PreAuthAmount) {
		notes = append(notes, fmt.Sprintf("PreAuth amount is changed from %s to %s", claimDB.PreAuthAmount, claimReq.PreAuthAmount))
	}
	if !claimReq.RepairingFacilityLaborRate.Equal(claimDB.RepairingFacilityLaborRate) {
		notes = append(notes, fmt.Sprintf("Repairing facility labor rate is changed from %s to %s", claimDB.RepairingFacilityLaborRate, claimReq.RepairingFacilityLaborRate))
	}
	if !claimReq.Deductible.Equal(claimDB.Deductible) {
		notes = append(notes, fmt.Sprintf("Deductible amount is changed from %s to %s", claimDB.Deductible, claimReq.Deductible))
	}

	// check goodwill amount
	newGoodWillAmount := totalGoodwillAmount(claimReq)
	if !prevGoodwillAmount.Equal(newGoodWillAmount) {
		notes = append(notes, fmt.Sprintf("Goodwill amount is updated from %s to %s", prevGoodwillAmount, newGoodWillAmount))
	}

	if !claimReq.TotalTax.Equal(claimDB.TotalTax) {

		notes = append(notes, fmt.Sprintf("Total Tax is changed from %s to %s", claimDB.TotalTax, claimReq.TotalTax))

		calculatedTax := calculateTotalTax(claimReq)
		if !calculatedTax.Equal(claimReq.TotalTax) {
			notes = append(notes, fmt.Sprintf("Total Tax %s is different from calculated tax %s", claimReq.TotalTax, calculatedTax))
		}
	}

	updatedRequestedTotal := claimReq.RequestedTotal.Add(claimReq.TotalTax).Mul(decimal.NewFromFloat(100.0)).DivRound(decimal.NewFromFloat(100.0), 2)
	existingRequestedTotal := claimDB.RequestedTotal.Add(claimDB.TotalTax).Mul(decimal.NewFromFloat(100.0)).DivRound(decimal.NewFromFloat(100.0), 2)

	if !updatedRequestedTotal.Equal(existingRequestedTotal) {
		notes = append(notes, fmt.Sprintf("Requested total amount is changed from $%s to $%s", existingRequestedTotal, updatedRequestedTotal))
	}

	if claimReq.OwnerID != claimDB.OwnerID {
		type userPayload struct {
			FirstName string `db:"first_name"`
			LastName  string `db:"last_name"`
		}
		newUserPayload := userPayload{}
		currentUserPayload := userPayload{}
		userQuery := "select first_name, last_name from users where id = $1"
		// get new owner details
		err := db.Get().Get(&newUserPayload, userQuery, claimReq.OwnerID)
		if err != nil {
			return errors.Wrap(err, "Errors in updating records notes : Could not find new owner details")
		}
		// get current owner details
		err = db.Get().Get(&currentUserPayload, userQuery, claimDB.OwnerID)
		if err != nil {
			return errors.Wrap(err, "Errors in updating records notes : Could not find current owner details")
		}
		notes = append(notes, fmt.Sprintf("Owner is changed from %s to %s", currentUserPayload.FirstName+" "+currentUserPayload.LastName, newUserPayload.FirstName+" "+newUserPayload.LastName))
	}

	for _, note := range notes {
		err := addClaimNote(ctx, tx, claimReq.ID, claimReq.UpdatedByUserID, note)
		if err != nil {
			return errors.Wrap(err, "Errors in updating records notes")
		}
	}
	return nil
}

func totalGoodwillAmount(payload *ClaimPayload) decimal.Decimal {
	sum := decimal.Zero
	for _, complaint := range payload.Complaints {
		if complaint.GoodwillFlag {
			sum = sum.Add(complaint.GoodwillAmount)
		}
	}
	return sum
}

// ClaimUpdated update table for claim updates
func ClaimUpdated(ctx context.Context, tx *sqlx.Tx, claimID, updatedByUserID int) error {
	updateInsert := `insert into automotive_claim_updates(automotive_claim_id,updated_by_user_id,updated_at) values($1,$2,now() at time zone 'utc')`
	_, err := tx.ExecContext(ctx, updateInsert, claimID, updatedByUserID)
	return err
}

// ClaimCounts returns count of claims for given status, agent, age
func ClaimCounts(res http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	var claimsCount []struct {
		Name  string `json:"name" db:"name"`
		Count int    `json:"count" db:"count"`
		ID    int    `json:"id" db:"id"`
	}

	countBy := chi.URLParam(req, "countBy")

	countQuery := ""
	avgWaitingQuery := ""
	var args []interface{}
	var query string
	var err error
	switch countBy {
	case "status":
		countQuery = `select status as name, count(*)
		from automotive_claims
		where status in (?) group by status`
		query, args, err = sqlx.In(countQuery, db.DashClaimsStatuses)
		if err != nil {
			handlers.ReportError(req, errors.Wrap(err, "db error In query for auto claim lists count data for "+countBy))
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting Auto claims lists count data for "+countBy, nil)
		}
		countQuery = db.Get().Rebind(query)

	case "agent":
		countQuery = `select first_name || ' ' || last_name as name, count(*), users.id
		from automotive_claims join
			users on automotive_claims.owner_id = users.id
			where status = '` + db.AutoClaimStatusOpen +
			`' group by users.id ORDER BY count DESC`
	case "age":
		countQuery = `SELECT (case
			  	when automotive_claims.date_of_claim_received > NOW() at time zone 'utc' - interval '4 hours'
             			then '< 4 hour'
				when automotive_claims.date_of_claim_received > NOW() at time zone 'utc' - interval '8 hours'
             			then '< 8 hour'
				when automotive_claims.date_of_claim_received > NOW() at time zone 'utc' - interval '1 days'
             			then '< Day'
				when automotive_claims.date_of_claim_received > NOW() at time zone 'utc' - interval '7 days'
             			then '< Week'
				when automotive_claims.date_of_claim_received > NOW() at time zone 'utc' - '1 month'::interval
             			then '< Month'
				when automotive_claims.date_of_claim_received < NOW() at time zone 'utc' - '1 month'::interval
             		   	then '> Month' end) as name, count(*)
  			      FROM automotive_claims where automotive_claims.status = '` + db.AutoClaimStatusOpen + `'
 			      GROUP BY (case
 			      	when automotive_claims.date_of_claim_received > NOW() at time zone 'utc' - interval '4 hours'
             			then '< 4 hour'
				when automotive_claims.date_of_claim_received > NOW() at time zone 'utc' - interval '8 hours'
             			then '< 8 hour'
				when automotive_claims.date_of_claim_received > NOW() at time zone 'utc' - interval '1 days'
             			then '< Day'
				when automotive_claims.date_of_claim_received > NOW() at time zone 'utc' - interval '7 days'
             			then '< Week'
				when automotive_claims.date_of_claim_received > NOW() at time zone 'utc' - '1 month'::interval
             			then '< Month'
				when automotive_claims.date_of_claim_received < NOW() at time zone 'utc' - '1 month'::interval
             		   	then '> Month' end)
             		      ORDER BY count DESC`
		avgWaitingQuery = `select ((extract(epoch from avg(AGE(now(),date_of_claim_received)))/3600)/24)/7 from automotive_claims where automotive_claims.status = '` + db.AutoClaimStatusOpen + `'`
	default:
		return http.StatusBadRequest, handlers.ErrorMessage(nil, "Invalid data passed", nil)
	}

	err = db.Get().Select(&claimsCount, countQuery, args...)
	if err != nil {
		err = errors.Wrap(err, "Database error getting Auto claims lists count")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting Auto claims lists count data for "+countBy, nil)
	}

	if avgWaitingQuery != "" {
		var avgWaitingPeriod decimal.NullDecimal
		err = db.Get().Get(&avgWaitingPeriod, avgWaitingQuery)
		if err != nil {
			err = errors.Wrap(err, "Database error getting Auto claims lists count")
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting Auto claims lists count data for "+countBy, nil)
		}
		claimAvgWaiting := avgWaitingPeriod.Decimal.Ceil().String()
		return http.StatusOK, map[string]interface{}{"automotive_claims": claimsCount, "average_waiting_period": claimAvgWaiting}
	}

	return http.StatusOK, map[string]interface{}{"automotive_claims": claimsCount}
}

func updateClaimStatus(id int, status string, tx *sqlx.Tx) error {
	query := `update automotive_claims set status = $1 where id = $2`
	_, err := tx.Exec(query, status, id)
	if err != nil {
		return errors.Wrap(err, "Error updating unidata claim status ")
	}
	return nil
}

type newClaimPayloadDB struct {
	VIN                          string             `json:"vin" db:"vin"`
	ContractNumber               string             `json:"contract_number" db:"contract_number"`
	ContractStatus               string             `json:"contract_status" db:"contract_status"`
	Status                       string             `json:"status" db:"status"`
	CreatedByUserID              int                `json:"created_by_user_id" db:"created_by_user_id"`
	OwnerID                      int                `json:"owner_id" db:"owner_id"`
	CustomerID                   int                `json:"customer_id" db:"customer_id"`
	Model                        string             `json:"model" db:"model"`
	Make                         string             `json:"make" db:"make"`
	Year                         string             `json:"year" db:"year"`
	BeginningMiles               int                `json:"beginning_miles" db:"beginning_miles"`
	EndingMiles                  int                `json:"ending_miles" db:"ending_miles"`
	EffectiveDate                time.Time          `json:"effective_date" db:"effective_date"`
	ExpirationDate               time.Time          `json:"expiration_date" db:"expiration_date"`
	Coverage                     string             `json:"coverage" db:"coverage"`
	DisappearingDeductible       bool               `json:"disappearing_deductible" db:"disappearing_deductible"`
	HighTech                     bool               `json:"high_tech" db:"high_tech"`
	SealsAndGasket               bool               `json:"seals_and_gasket" db:"seals_and_gasket"`
	RentalUpgrade                bool               `json:"rental_upgrade" db:"rental_upgrade"`
	CommercialUse                int                `json:"commercial_use" db:"commercial_use"`
	StandardPowertrainPlusOption bool               `json:"standard_powertrain_plus_option" db:"standard_powertrain_plus_option"`
	SmartTechOption              bool               `json:"smart_tech_option" db:"smart_tech_option"`
	CanadianVehicle              bool               `json:"canadian_vehicle" db:"canadian_vehicle"`
	Deductible                   decimal.Decimal    `json:"deductible" db:"deductible"`
	Term                         string             `json:"term" db:"term"`
	FirstName                    string             `json:"first_name" db:"first_name"`
	LastName                     string             `json:"last_name" db:"last_name"`
	IsBusiness                   bool               `json:"is_business" db:"is_business"`
	BusinessName                 null.String        `json:"business_name" db:"business_name"`
	EmailAddress                 string             `json:"email_address" db:"email_address"`
	PhoneNumber                  string             `json:"phone_number" db:"phone_number"`
	StreetAddress                string             `json:"street_address" db:"street_address"`
	City                         string             `json:"city" db:"city"`
	State                        string             `json:"state" db:"state"`
	PostalCode                   string             `json:"postal_code" db:"postal_code"`
	Maintenance                  string             `json:"maintenance" db:"maintenance"`
	ProductCode                  string             `json:"product_code" db:"product_code"`
	KeyCount                     int                `json:"key_count" db:"key_count"`
	Paint                        bool               `json:"paint" db:"paint"`
	Fabric                       bool               `json:"fabric" db:"fabric"`
	LeatherOrVinyl               bool               `json:"leather_or_vinyl" db:"leather_or_vinyl"`
	DentAndDing                  bool               `json:"dent_and_ding" db:"dent_and_ding"`
	Plan                         string             `json:"plan" db:"plan"`
	Purchased                    int                `json:"purchased" db:"purchased"`
	Remaining                    int                `json:"remaining" db:"remaining"`
	ID                           int                `json:"id" db:"automotive_claim_id"`
	UnidataClaimNumber           int                `json:"unidata_claim_number" db:"unidata_claim_number"`
	RO                           string             `json:"ro" db:"ro"`
	ROMileage                    int                `json:"ro_mileage" db:"ro_mileage"`
	RoOpenedDate                 types.JSPQNullDate `json:"ro_opened_date" db:"ro_opened_date"`
	FacilityID                   int                `json:"facility_id" db:"facility_id"`
	FacilityName                 string             `json:"facility_name" db:"facility_name"`
	FacilityAddress              string             `json:"facility_address" db:"facility_address"`
	FacilityPostalCode           string             `json:"facility_postal_code" db:"facility_postal_code"`
	FacilityCity                 string             `json:"facility_city" db:"facility_city"`
	FacilityStateCode            string             `json:"facility_state_code" db:"facility_state_code"`
	FacilityPhone                string             `json:"facility_phone" db:"facility_phone"`
	FacilityFax                  string             `json:"facility_fax" db:"facility_fax"`
	LaborRate                    decimal.Decimal    `json:"labor_rate" db:"labor_rate"`
	TaxLabor                     decimal.Decimal    `json:"tax_labor" db:"tax_labor"`
	TaxParts                     decimal.Decimal    `json:"tax_parts" db:"tax_parts"`
	PayType                      string             `json:"pay_type" db:"pay_type"`
	Estimate                     decimal.Decimal    `json:"estimate" db:"estimate"`
	DateOfClaimReceived          time.Time          `json:"date_of_claim_received" db:"date_of_claim_received"`
	ClaimType                    string             `json:"claim_type" db:"claim_type"`
	SBRecordKey                  string             `json:"sb_record_key" db:"sb_record_key"`
	AutoApproved                 bool               `json:"auto_approved" db:"auto_approved"`
	ContractStoreCode            string             `json:"contract_store_code" db:"-"`
	ContractStoreID              int                `json:"contract_store_id" db:"contract_store_id"`
	ContractDeductible           decimal.Decimal    `json:"contract_deductible" db:"contract_deductible"`
	ProductVariantDisplayName    string             `json:"product_variant_display_name" db:"-"`
	TotalLabor                   decimal.Decimal    `json:"total_labor" db:"total_labor"`
	TotalParts                   decimal.Decimal    `json:"total_parts" db:"total_parts"`
	IsROLookup                   bool               `json:"is_ro_lookup" db:"-"`
	RepairingFacilityLaborRate   decimal.Decimal    `json:"repairing_facility_labor_rate" db:"repairing_facility_labor_rate"`
}

// Claim struct to store claim information
type claimDB struct {
	ID                int                 `db:"id"`
	ClaimDate         types.JSPQNullDate  `db:"claim_date"`
	RepairOrderNumber string              `db:"repair_order_number"`
	ClaimAmount       types.JSNullDecimal `db:"claim_amount"`
	ClaimStatus       string              `db:"claim_status"`
	AuthorizedAmount  decimal.Decimal     `db:"authorized_amount"`
}

// ClaimCreate creates a new automotive Claim
func ClaimCreate(_ http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	// Create context
	ctx := req.Context()
	txn := newrelic.FromContext(ctx)

	claim := newClaimPayloadDB{}

	dec := json.NewDecoder(req.Body)
	err := dec.Decode(&claim)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Malformed Automotive Claim data for create.", nil)
	}

	if strings.Contains(claim.ProductVariantDisplayName, "NSD") ||
		strings.Contains(claim.ProductVariantDisplayName, "Alpha") {
		return http.StatusForbidden, handlers.ErrorMessage(errors.New("can not create claim for this product"),
			"Can not create claim for this products.", nil)
	}

	claim.CreatedByUserID = user.ID
	claim.OwnerID = claim.CreatedByUserID
	claim.Status = db.AutoClaimStatusOpen
	claim.Maintenance = claim.Term + " " + claim.ContractStatus
	claim.Deductible = claim.ContractDeductible

	err = db.Get().Get(&claim.ContractStoreID, `select id from stores where code = $1`, claim.ContractStoreCode)
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusBadRequest, handlers.ErrorMessage(err, "Invalid contract information - Store code not found", nil)
		}
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Failed while fetching store information", nil)
	}

	// Check for claim validation
	if claim.ContractNumber == "" {
		return http.StatusBadRequest, handlers.ErrorMessage(errors.New("Invalid contract information"), "Invalid contract information - Missing contract number", nil)
	}
	if claim.VIN == "" {
		return http.StatusBadRequest, handlers.ErrorMessage(errors.New("Invalid contract information"), "Invalid contract information - Missing vin", nil)
	}

	err = insertTCAClaim(ctx, &claim)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error in adding automotive claim", nil)
	}

	if db.ProductCodeMaintenance == claim.ProductCode {
		var c []claimDB
		query := `select ac.id, ac.date_of_claim_received claim_date, ac.ro repair_order_number,
					case when ac.status in('CheckWritten', 'Reversed', 'CCPaid', 'Adjusted') 
					then coalesce(acp.amount, ac.estimate) else ac.estimate end claim_amount, 
					ac.status claim_status, coalesce(acp.amount, ac.estimate) as authorized_amount
				from automotive_claims ac 
					left join automotive_claim_complaints acc on acc.automotive_claim_id = ac.id
					left join automotive_claim_payments acp on ac.id = acp.automotive_claim_id
					left join automotive_claim_payment_checks acpc on acp.id = acpc.automotive_claim_payments_id
				where contract_number = $1 
					and product_code = $2 
					and ac.status not in ('Deactivated', 'Denied')
				group by ac.id, ac.date_of_claim_received, ac.ro_mileage, ac.ro, ac.estimate, 
					acpc.paid_date, ac.advisor, ac.status, acpc.check_number, acp.amount`

		err = db.Get().SelectContext(ctx, &c, query, claim.ContractNumber, claim.ProductCode)
		if err != nil {
			handlers.ReportError(req, errors.Wrap(err, "error getting claim details"))
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error getting claim details", nil)
		}

		usedCoupons := getUsedCouponCount(c)
		err = handlers.UpdateContractStatus(txn, req, claim.ContractNumber, len(usedCoupons))
		if err != nil {
			handlers.ReportError(req, errors.Wrap(err, "error updating contract status"))
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error updating claim status	", nil)
		}
	}

	return http.StatusOK, map[string]interface{}{"id": claim.ID}
}

func getUsedCouponCount(claims []claimDB) map[int]int {
	// Find the total payment amount for each claim
	claimTotalPayments := make(map[int]decimal.Decimal, len(claims))
	for _, c := range claims {
		claimAmount := c.ClaimAmount.Decimal

		// If claim status is anything other than deactivated and denied and claimAmount zero
		// then set it to non zero value, Because we want to count that claim
		if c.ClaimStatus != db.AutoClaimStatusDeactivated &&
			c.ClaimStatus != db.AutoClaimStatusDenied &&
			claimAmount.Equal(decimal.Zero) {
			claimAmount = decimal.NewFromFloat(1)
		}

		if subtotal, ok := claimTotalPayments[c.ID]; ok {
			claimTotalPayments[c.ID] = subtotal.Add(claimAmount)
		} else {
			claimTotalPayments[c.ID] = claimAmount
		}
	}

	// if the total of the payments for a claim is > 0, then a coupon is used
	couponCount := make(map[int]int, len(claims))
	for id, total := range claimTotalPayments {
		if total.GreaterThan(decimal.Zero) {
			couponCount[id] = 1
		}
	}

	return couponCount
}

func insertTCAClaim(ctx context.Context, claim *newClaimPayloadDB) error {

	claimInsert := `insert into automotive_claims (
		vin,
		contract_number,
		contract_status,
		date_of_claim_received,
		status,
		created_by_user_id,
		owner_id,
		customer_id,
		model,
		make,
		year,
		beginning_miles,
		ending_miles,
		effective_date,
		expiration_date,
		coverage,
		deductible,
		maintenance,
		term,
		product_code,
		contract_store_id,
		contract_deductible,
		claim_type)

		values (:vin,
		:contract_number,
		:contract_status,
		now() at time zone 'utc',
		:status,
		:created_by_user_id,
		:owner_id,
		:customer_id,
		:model,
		:make,
		:year,
		:beginning_miles,
		:ending_miles,
		:effective_date,
		:expiration_date,
		:coverage,
		:deductible,
		:maintenance,
		:term,
		:product_code,
		:contract_store_id,
		:contract_deductible,
		:claim_type) returning id`

	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		return errors.Wrap(err, "Database error beginning transaction for Automotive claim create")
	}

	err = addClaimCustomer(tx, claim)
	if err != nil {
		_ = tx.Rollback()
		return errors.Wrap(err, "Error creating customer")
	}

	// Prepare and execute
	stmt, err := tx.PrepareNamedContext(ctx, claimInsert)
	if err != nil {
		_ = tx.Rollback()
		return errors.Wrap(err, "PrepareNamed failed")
	}
	defer func() { _ = stmt.Close() }()
	err = stmt.GetContext(ctx, &claim.ID, claim)
	if err != nil {
		_ = tx.Rollback()
		return errors.Wrap(err, "Scan error on ID after creating Atuomotive claim")
	}

	// Add coverage flags
	err = addCoverageFlags(tx, claim)
	if err != nil {
		return errors.Wrap(err, "Error while adding coverage flags for the claim")
	}

	// Add note on successful new claim creation
	note := RecordNotePayload{}
	note.AutomotiveClaimID = claim.ID
	note.IsManual = false
	note.CreatedByUserID = claim.CreatedByUserID
	// for claim create, note text should be New claim started
	note.NotesText = db.AutomotiveRecordNoteDescription[db.AutoClaimStatusNew]
	_, err = InsertRecordNote(ctx, &note, tx)
	if err != nil {
		_ = tx.Rollback()
		return errors.Wrap(err, "Failed to add note")
	}

	err = ClaimUpdated(ctx, tx, claim.ID, claim.CreatedByUserID)
	if err != nil {
		_ = tx.Rollback()
		return errors.Wrap(err, "Error inserting automotive_claim_updates")
	}

	err = tx.Commit()
	if err != nil {
		return errors.Wrap(err, "Database error committing transaction for Auto claim insert")
	}
	return nil
}

// add new claim customer details
func addClaimCustomer(tx *sqlx.Tx, payload *newClaimPayloadDB) error {
	customerInsert := `insert into customers (
		first_name,
		last_name,
		is_business,
		business_name,
		email_address,
		phone_number,
		street_address,
		city,
		state,
		postal_code)

		values (
		:first_name,
		:last_name,
		:is_business,
		:business_name,
		:email_address,
		:phone_number,
		:street_address,
		:city,
		:state,
		:postal_code) returning id`
	// Prepare and execute customer info
	customerInsertStmt, err := tx.PrepareNamed(customerInsert)
	if err != nil {
		return errors.Wrap(err, "PrepareNamed failed for customer")
	}
	defer func() { _ = customerInsertStmt.Close() }()
	err = customerInsertStmt.Get(&payload.CustomerID, payload)
	if err != nil {
		return errors.Wrap(err, "Failed to add customer information for claim")
	}
	return nil
}

// add new claim coverage flags
func addCoverageFlags(tx *sqlx.Tx, payload *newClaimPayloadDB) error {
	var coverageInsert string
	switch payload.ProductCode {
	case db.ProductCodeService:
		coverageInsert = `insert into automotive_claim_coverage (
		automotive_claim_id,
		disappearing_deductible,
		high_tech,
		seals_and_gasket,
		rental_upgrade,
		commercial_use,
		standard_powertrain_plus_option,
		smart_tech_option,
		canadian_vehicle)

		values (
		:automotive_claim_id,
		:disappearing_deductible,
		:high_tech,
		:seals_and_gasket,
		:rental_upgrade,
		:commercial_use,
		:standard_powertrain_plus_option,
		:smart_tech_option,
		:canadian_vehicle)`
	case db.ProductCodeKey:
		coverageInsert = `insert into automotive_claim_coverage (automotive_claim_id, key_count)
		values (:automotive_claim_id, :key_count)`
	case db.ProductCodeCentury:
		coverageInsert = `insert into automotive_claim_coverage (
		automotive_claim_id,
		paint,
		fabric,
		leather_or_vinyl,
		dent_and_ding)

		values (
		:automotive_claim_id,
		:paint,
		:fabric,
		:leather_or_vinyl,
		:dent_and_ding)`
	case db.ProductCodeMaintenance:
		coverageInsert = `insert into automotive_claim_coverage (
		automotive_claim_id,
		plan,
		purchased,
		remaining)
		
		values (
		:automotive_claim_id,
		:plan,
		:purchased,
		:remaining
		)`
	}
	if coverageInsert != "" {
		coverageInsertStatement, err := tx.PrepareNamed(coverageInsert)
		if err != nil {
			return errors.Wrap(err, "PrepareNamed failed for claim coverage")
		}
		defer func() { _ = coverageInsertStatement.Close() }()
		_, err = coverageInsertStatement.Exec(payload)
		if err != nil {
			return errors.Wrap(err, "Scan error on ID after creating Atuomotive claim coverage")
		}
	}
	return nil
}

// ClaimReassignManager reassign claim request from a manager
func ClaimReassignManager(res http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	ctx := req.Context()
	requestData := struct {
		OwnerID    string `json:"owner_id"`
		ClaimsList []int  `json:"claims_list"`
	}{}

	dec := json.NewDecoder(req.Body)
	err := dec.Decode(&requestData)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Invalid Automotive Claim data to reassign owner.", nil)
	}

	reassignClaimsQuery := "update automotive_claims set owner_id = ?, reassignment_status = ? where id IN (?)"
	noteText := "Claim reassigned by manager"

	query, args, err := sqlx.In(reassignClaimsQuery, requestData.OwnerID, db.AutoClaimReassignmentStatusNew, requestData.ClaimsList)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error reassigning Claim, In query failed", nil)
	}

	err = claimsReassign(ctx, query, args, requestData.ClaimsList, noteText, user)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error in claim reassignment", nil)
	}

	return http.StatusOK, map[string]interface{}{}
}

// ClaimReopen reopen claim and assign to agent who had change status to payable
func ClaimReopen(res http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	ctx := req.Context()
	claimID, err := strconv.Atoi(chi.URLParam(req, "claim_id"))
	if err != nil || claimID == 0 {
		return http.StatusBadRequest, handlers.ErrorMessage(nil, "Claim Id is invalid", nil)
	}

	var claimDetails struct {
		Status         string `db:"status"`
		ClaimType      string `db:"claim_type"`
		ProductCode    string `db:"product_code"`
		ContractNumber string `db:"contract_number"`
		PayType        string `db:"pay_type"`
	}
	claimQuery := "select status, claim_type, contract_number, product_code, pay_type from automotive_claims where id = $1"
	err = db.Get().GetContext(ctx, &claimDetails, claimQuery, claimID)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error while original status of claim."))
		return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Database update failed", nil)
	}

	if claimDetails.Status != db.AutoClaimComplaintStatusDenied &&
		claimDetails.Status != db.AutoClaimStatusApproved &&
		claimDetails.Status != db.AutoClaimStatusAuthorizedCCClaim &&
		claimDetails.Status != db.AutoClaimStatusInvoiceSent &&
		claimDetails.Status != db.AutoClaimStatusDeactivated &&
		claimDetails.Status != db.AutoClaimStatusDealerChargedBack {
		return http.StatusBadRequest, handlers.ErrorMessage(nil, "Invalid Claim Status", nil)
	}

	if (claimDetails.Status == db.AutoClaimStatusApproved ||
		claimDetails.Status == db.AutoClaimStatusAuthorizedCCClaim ||
		claimDetails.Status == db.AutoClaimStatusInvoiceSent ||
		claimDetails.Status == db.AutoClaimStatusDeactivated ||
		claimDetails.Status == db.AutoClaimStatusDealerChargedBack) &&
		!user.HasRole(db.RoleAutoClaimsManager) {
		return http.StatusUnauthorized, handlers.ErrorMessage(nil, "Only manager can change reopen approved claim", nil)
	}

	// For LCA claim we get facility user and use that user to process the claim further
	var userID int
	if claimDetails.ClaimType != db.ClaimTypeLCA {
		userID, err = userByStatus(claimID, claimDetails.Status)
		if err != nil {
			handlers.ReportError(req, errors.Wrap(err, "not able to fetch the who denied the claim"))
			return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Database update failed", nil)
		}
	} else {
		facilityUserID := 0
		err = db.Get().Get(&facilityUserID, "select id from users where first_name = $1 limit 1", db.FacilityUserName)
		if err != nil {
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error while fetching Facility User", nil)
		}
		userID = facilityUserID
	}

	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "database error beginning transaction for Auto claim reassignment"))
		return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Database update failed", nil)
	}

	updateQuery := `update automotive_claims set owner_id = $1, status = $2, canceled_reason = '' , canceled_other_reason = null where id = $3`

	_, err = tx.ExecContext(ctx, updateQuery, userID, db.AutoClaimStatusOpen, claimID)
	if err != nil {
		_ = tx.Rollback()
		handlers.ReportError(req, errors.Wrap(err, "database error - failed to update claim status and owner"))
		return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Database update failed", nil)
	}

	// Update contract status if they are reopened from denied or deactivated status
	if db.ProductCodeMaintenance == claimDetails.ProductCode &&
		(claimDetails.Status == db.AutoClaimComplaintStatusDenied || claimDetails.Status == db.AutoClaimStatusDeactivated) {
		var c []claimDB
		txn := newrelic.FromContext(ctx)

		query := `select ac.id, ac.date_of_claim_received claim_date, ac.ro repair_order_number,
					case when ac.status in('CheckWritten', 'Reversed', 'CCPaid', 'Adjusted') 
					then coalesce(acp.amount, ac.estimate) else ac.estimate end claim_amount, 
					ac.status claim_status, coalesce(acp.amount, ac.estimate) as authorized_amount
				from automotive_claims ac 
					left join automotive_claim_complaints acc on acc.automotive_claim_id = ac.id
					left join automotive_claim_payments acp on ac.id = acp.automotive_claim_id
					left join automotive_claim_payment_checks acpc on acp.id = acpc.automotive_claim_payments_id
				where contract_number = $1 
					and product_code = $2 
					and ac.status not in ('Deactivated', 'Denied')
				group by ac.id, ac.date_of_claim_received, ac.ro_mileage, ac.ro, ac.estimate, 
					acpc.paid_date, ac.advisor, ac.status, acpc.check_number, acp.amount`

		err = tx.SelectContext(ctx, &c, query, claimDetails.ContractNumber, claimDetails.ProductCode)
		if err != nil {
			_ = tx.Rollback()
			handlers.ReportError(req, errors.Wrap(err, "error getting claim details"))
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error getting claim details", nil)
		}

		usedCoupons := getUsedCouponCount(c)
		err = handlers.UpdateContractStatus(txn, req, claimDetails.ContractNumber, len(usedCoupons))
		if err != nil {
			_ = tx.Rollback()
			handlers.ReportError(req, errors.Wrap(err, "error updating contract status"))
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error updating claim status	", nil)
		}
	}

	// Claim updated history
	err = ClaimUpdated(ctx, tx, claimID, user.ID)
	if err != nil {
		_ = tx.Rollback()
		handlers.ReportError(req, errors.Wrap(err, "database error - inserting automotive_claim_updates"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database update failed", nil)
	}

	noteText := "Claim is reopened"
	err = addClaimNote(ctx, tx, claimID, user.ID, noteText)
	if err != nil {
		_ = tx.Rollback()
		handlers.ReportError(req, errors.Wrap(err, "database error - failed to update record note"))
		return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Database update failed", nil)
	}

	if claimDetails.PayType == db.PayTypeCreditCard && (claimDetails.Status == db.AutoClaimStatusInvoiceSent || claimDetails.Status == db.AutoClaimStatusAuthorizedCCClaim) {
		query := `select id from automotive_claim_documents where automotive_claim_id = $1 and document_type= $2 and deleted_at is null`
		var documentID int
		err = tx.GetContext(ctx, &documentID, query, claimID, ClaimDoucmentTypeCCPayment)
		if err != nil && err != sql.ErrNoRows {
			handlers.ReportError(req, errors.Wrap(err, "error getting claim document details from database"))
			return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Database update failed", nil)
		}

		if documentID != 0 {
			err = documentDelete(documentID, user.ID)
			if err != nil {
				handlers.ReportError(req, errors.Wrap(err, "error deleting claim document from database"))
				return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Database update failed", nil)
			}
		}
	}

	err = tx.Commit()
	if err != nil {
		err = errors.Wrap(err, "Database error committing transaction for Auto claim reassign")
		handlers.ReportError(req, errors.Wrap(err, "database error - failed to commit transaction"))
		return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Database update failed", nil)
	}

	return http.StatusOK, map[string]interface{}{}
}

// ClaimReassignAgent re-assigns claim request agent
func ClaimReassignAgent(res http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	ctx := req.Context()
	requestData := struct {
		OwnerID    string `json:"owner_id"`
		ClaimsList []int  `json:"claims_list"`
	}{}

	dec := json.NewDecoder(req.Body)
	err := dec.Decode(&requestData)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Invalid Automotive Claim data to reassign owner.", nil)
	}

	reassignClaimsQuery := "update automotive_claims set reassigned_owner_id = ?, reassignment_status = ? where id IN (?)"
	noteText := "Claim reassignment requested by agent"

	query, args, err := sqlx.In(reassignClaimsQuery, requestData.OwnerID, db.AutoClaimReassignmentStatusPending, requestData.ClaimsList)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error reassigning Claim, In query failed", nil)
	}

	err = claimsReassign(ctx, query, args, requestData.ClaimsList, noteText, user)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error in claim reassignment request", nil)
	}

	return http.StatusOK, map[string]interface{}{}
}

func isValidManagerReassignStatus(status string) bool {
	switch status {
	case db.AutoClaimStatusOpen:
		return true
	case db.AutoClaimStatusReturned:
		return true
	case db.AutoClaimStatusPayable:
		return true
	case db.AutoClaimStatusPreAuth:
		return true
	case db.AutoClaimStatusCheckWritten:
		return true
	case db.AutoClaimStatusWaitingForCheck:
		return true
	case db.AutoClaimStatusReversed:
		return true
	case db.AutoClaimStatusWaitingForReversed:
		return true
	case db.AutoClaimStatusAdjusted:
		return true
	case db.AutoClaimStatusWaitingOnVendor:
		return true
	case db.AutoClaimStatusNeedRentalBill:
		return true
	case db.AutoClaimStatusNeedSubletBill:
		return true
	case db.AutoClaimStatusNeedSMToCall:
		return true
	case db.AutoClaimStatusNeedClosedAccountingRO:
		return true
	case db.AutoClaimStatusNeedProofOfDeductibleReimbursement:
		return true
	}
	return false
}

func isValidAgentReassignStatus(status string) bool {
	switch status {
	case db.AutoClaimStatusOpen:
		return true
	case db.AutoClaimStatusReturned:
		return true
	case db.AutoClaimStatusPreAuth:
		return true
	case db.AutoClaimStatusWaitingOnVendor:
		return true
	case db.AutoClaimStatusNeedRentalBill:
		return true
	case db.AutoClaimStatusNeedSubletBill:
		return true
	case db.AutoClaimStatusNeedSMToCall:
		return true
	case db.AutoClaimStatusNeedClosedAccountingRO:
		return true
	case db.AutoClaimStatusNeedProofOfDeductibleReimbursement:
		return true
	}
	return false
}

// ClaimReassignSelf reassign claim request from a non-owner user
func ClaimReassignSelf(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	ctx := req.Context()
	requestData := struct {
		ClaimID int `json:"claim_id"`
	}{}

	dec := json.NewDecoder(req.Body)
	err := dec.Decode(&requestData)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Invalid Automotive Claim data to reassign owner.", nil)
	}

	if requestData.ClaimID == 0 {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Please provide claim id", nil)
	}

	claimQuery := "select status, owner_id from automotive_claims where id = $1"
	claimPayload := struct {
		Status  string `db:"status"`
		OwnerID string `db:"owner_id"`
	}{}

	err = db.Get().Get(&claimPayload, claimQuery, requestData.ClaimID)

	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage(nil, "Claim not found", nil)
		}
		handlers.ReportError(req, errors.Wrap(err, "error while loading claim"))
		return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Error loading claim", nil)
	}

	if user.HasRole(db.RoleAutoClaimsManager) {
		if !isValidManagerReassignStatus(claimPayload.Status) {
			handlers.ReportError(req, errors.Wrap(err, "Manager can only assign open, payable or returned or checkWritten or reversed  claim to self"))
			return http.StatusUnauthorized, handlers.ErrorMessage(nil, "Manager can only assign open, payable or returned or checkWritten or reversed claim to self", nil)
		}
	} else if user.HasRole(db.RoleAutoClaims) {
		if !isValidAgentReassignStatus(claimPayload.Status) {
			handlers.ReportError(req, errors.Wrap(err, "Agent can only assign open or returned claim to self"))
			return http.StatusUnauthorized, handlers.ErrorMessage(nil, "Agent can only assign open or returned claim to self", nil)
		}
	}

	userPayload := struct {
		FirstName string `db:"first_name"`
		LastName  string `db:"last_name"`
	}{}

	userQuery := "select first_name, last_name from users where id = $1"
	err = db.Get().Get(&userPayload, userQuery, claimPayload.OwnerID)

	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error beginning transaction for Auto claim reassignment", nil)
	}

	noteText := "Claim assigned to " + user.FirstName + " " + user.LastName + " from " + userPayload.FirstName + " " + userPayload.LastName

	tx, err := db.Get().Beginx()

	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error beginning transaction for Auto claim reassignment", nil)
	}

	reassignClaimsQuery := "update automotive_claims set owner_id = $1 where id = $2"

	_, err = tx.Exec(reassignClaimsQuery, user.ID, requestData.ClaimID)

	if err != nil {
		_ = tx.Rollback()
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error reassigning Claim, Exec failed", nil)
	}

	err = ClaimUpdated(ctx, tx, requestData.ClaimID, user.ID)

	if err != nil {
		_ = tx.Rollback()
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error inserting automotive_claim_updates", nil)
	}

	note := RecordNotePayload{}
	note.AutomotiveClaimID = requestData.ClaimID
	note.IsManual = false
	note.CreatedByUserID = user.ID
	note.NotesText = noteText
	_, err = InsertRecordNote(ctx, &note, tx)
	if err != nil {
		_ = tx.Rollback()
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Failed to add note", nil)
	}

	err = tx.Commit()

	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error reassigning Auto claim", nil)
	}

	return http.StatusOK, map[string]interface{}{}
}

func claimsReassign(ctx context.Context, reassignClaimsQuery string, args []interface{}, claimsList []int, noteText string, user db.User) error {
	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		return errors.Wrap(err, "Database error beginning transaction for Auto claim reassignment")
	}

	query := tx.Rebind(reassignClaimsQuery)
	_, err = tx.ExecContext(ctx, query, args...)
	if err != nil {
		_ = tx.Rollback()
		return errors.Wrap(err, "Error reassigning Claim, Exec failed")
	}

	for _, claimID := range claimsList {
		err = ClaimUpdated(ctx, tx, claimID, user.ID)
		if err != nil {
			_ = tx.Rollback()
			return errors.Wrap(err, "Error inserting automotive_claim_updates")
		}
		// Add note on successful claim reassignment
		note := RecordNotePayload{}
		note.AutomotiveClaimID = claimID
		note.IsManual = false
		note.CreatedByUserID = user.ID
		note.NotesText = noteText
		_, err = InsertRecordNote(ctx, &note, tx)
		if err != nil {
			_ = tx.Rollback()
			return errors.Wrap(err, "Failed to add note")
		}
	}

	err = tx.Commit()
	if err != nil {
		err = errors.Wrap(err, "Database error committing transaction for Auto claim reassign")
		return errors.Wrap(err, "Database error reassigning Auto claim")
	}
	return nil
}

// ClaimReassignAcceptOrReject handles agent's claim accept or reject
func ClaimReassignAcceptOrReject(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	ctx := req.Context()
	requestData := struct {
		Action  string `json:"action"`
		ClaimID int    `json:"claim_id"`
	}{}

	dec := json.NewDecoder(req.Body)
	err := dec.Decode(&requestData)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Invalid Automotive Claim data to reassign owner.", nil)
	}

	// claimExist
	reassignedOwnerID := 0
	err = db.Get().Get(&reassignedOwnerID, `select reassigned_owner_id from automotive_claims where id = $1`, requestData.ClaimID)
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage(err, "Claim not found", nil)
		}
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error while loading claim", nil)
	}

	if user.ID != reassignedOwnerID {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Unauthorized user", nil)
	}

	updateClaimQuery := ""
	noteText := ""
	var args []interface{}
	if requestData.Action == "accept" {
		updateClaimQuery = "update automotive_claims set owner_id = $1, reassigned_owner_id = null, reassignment_status = $2 where id = $3"
		noteText = "Claim accpted by agent"
		args = append(args, strconv.Itoa(user.ID), db.AutoClaimReassignmentStatusNew, requestData.ClaimID)
	} else if requestData.Action == "reject" {
		updateClaimQuery = "update automotive_claims set reassigned_owner_id = null, reassignment_status = $1 where id = $2"
		noteText = "Claim rejected by agent"
		args = append(args, db.AutoClaimReassignmentStatusRejected, requestData.ClaimID)
	} else {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Invalid Action", nil)
	}

	tx, err := db.Get().Beginx()
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error beginning transaction for Auto claim action", nil)
	}

	// update claim
	_, err = tx.Exec(updateClaimQuery, args...)
	if err != nil {
		_ = tx.Rollback()
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error Accepting/Rejecting Claim, Exec failed", nil)
	}

	// Claim updated history
	err = ClaimUpdated(ctx, tx, requestData.ClaimID, user.ID)
	if err != nil {
		_ = tx.Rollback()
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error inserting automotive_claim_updates", nil)
	}

	// Record note
	note := RecordNotePayload{}
	note.AutomotiveClaimID = requestData.ClaimID
	note.IsManual = false
	note.CreatedByUserID = user.ID
	note.NotesText = noteText
	_, err = InsertRecordNote(ctx, &note, tx)
	if err != nil {
		_ = tx.Rollback()
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Failed to add note", nil)
	}

	err = tx.Commit()
	if err != nil {
		err = errors.Wrap(err, "Database error committing transaction for Auto claim actions")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error accepting/rejecting Auto claim", nil)
	}

	return http.StatusOK, map[string]interface{}{}
}

// BatchIndex returns list of batches
func BatchIndex(res http.ResponseWriter, req *http.Request, _ db.User) (int, map[string]interface{}) {
	var batchList []struct {
		ID          int             `json:"id" db:"id"`
		CreatedAt   time.Time       `json:"created_at" db:"created_at"`
		Facility    string          `json:"facility" db:"facility"`
		Amount      decimal.Decimal `json:"amount" db:"amount"`
		Status      string          `json:"status" db:"status"`
		Note        string          `json:"note" db:"note"`
		CheckNumber string          `json:"check_number" db:"-"`
		PayType     string          `json:"pay_type" db:"pay_type"`
	}

	type args struct {
		FacilityID  int    `db:"facility_id"`
		ProductCode string `db:"product_code"`
		BatchID     int    `db:"batch_id"`
	}

	limitClause := ""
	p := req.FormValue("page")
	if p != "" {
		pageOffset, err := strconv.Atoi(p)
		if err != nil {
			return http.StatusBadRequest, handlers.ErrorMessage(errors.New("Invalid page number"), "Please provide valid page number", nil)
		}
		limitClause = fmt.Sprintf(" limit %d offset %d", handlers.PerPageEntries, (pageOffset-1)*handlers.PerPageEntries)
	}

	batchQueryArgs := args{}

	var whereClauses []string

	batchQueryArgs.FacilityID, _ = strconv.Atoi(req.FormValue("facility"))
	if batchQueryArgs.FacilityID > 0 {
		whereClauses = append(whereClauses, `facilities.id = :facility_id`)
	}

	batchQueryArgs.ProductCode = req.FormValue("product")
	if batchQueryArgs.ProductCode != "" {
		whereClauses = append(whereClauses, `claim.product_code = :product_code`)
	}

	if batchQueryArgs.FacilityID <= 0 && batchQueryArgs.ProductCode == "" {
		return http.StatusBadRequest, handlers.ErrorMessage(errors.New("Please provide facility or product"), "Please provide facility or product", nil)
	}

	selectQuery := `select batch.id as id, facilities.name as facility, batch.created_at, sum(details.amount) as amount,
						case when count(CASE WHEN is_success = false THEN 1 END) = 0
                            then 'Success'
                            else 'Issues' end as status,
						case when count(CASE WHEN is_success = false THEN 1 END) = 0
                            then ''
                        else count(CASE WHEN is_success = false THEN 1 END) || ' of ' || count(details.id) || ' failed' end as note,
						pay_type
					from automotive_intacct_batch_details details
                        join automotive_intacct_batches batch on details.automotive_intacct_batch_id = batch.id
						join automotive_claims claim on details.automotive_claim_id = claim.id
                        join automotive_facilities facilities on claim.facility_id = facilities.id`

	if len(whereClauses) > 0 {
		selectQuery += " where " + strings.Join(whereClauses, " and ")
	}

	selectQuery += ` group by batch.id, facilities.name, pay_type `

	countQuery := `select count(*) from (` + selectQuery + `) as q`

	sortOrder := req.FormValue("sort_order")
	if sortOrder == "" {
		sortOrder = "desc"
	}
	sortBy := req.FormValue("sort_by")
	if sortBy == "" {
		sortBy = "created_at"
	}
	selectQuery += fmt.Sprintf(" order by %s %s ", sortBy, sortOrder)

	if limitClause != "" {
		selectQuery += limitClause
	}

	// get batch list
	stmt, err := db.Get().PrepareNamed(selectQuery)
	if err != nil {
		err = errors.Wrap(err, "Database error preparing get batch list query")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting batches", nil)
	}

	defer func() { _ = stmt.Close() }()
	err = stmt.Select(&batchList, batchQueryArgs)
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage(err, "No batches found", nil)
		}
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error loading batches from database.", nil)
	}

	// get total batch count
	count := 0
	countStmt, err := db.Get().PrepareNamed(countQuery)
	if err != nil {
		err = errors.Wrap(err, "Database error preparing get total batch count query")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting batch count", nil)
	}

	defer func() { _ = countStmt.Close() }()

	err = countStmt.Get(&count, batchQueryArgs)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error loading total batch count", nil)
	}

	// get check numbers
	checkQuery := `select distinct automotive_claim_payment_checks.check_number
				   from automotive_claim_payment_checks
					join automotive_claim_payments on automotive_claim_payment_checks.automotive_claim_payments_id = automotive_claim_payments.id
					join automotive_claims claim on claim.id = automotive_claim_payments.automotive_claim_id
					join automotive_intacct_batch_details details on details.automotive_claim_id = claim.id and automotive_claim_payments.id = details.automotive_claim_payment_id
					join automotive_intacct_batches batch on details.automotive_intacct_batch_id = batch.id
					join automotive_facilities facilities on claim.facility_id = facilities.id
				   where batch.id = :batch_id`
	if len(whereClauses) > 0 {
		checkQuery += " and " + strings.Join(whereClauses, " and ")
	}
	var wg sync.WaitGroup
	var queryError error
	for index, batch := range batchList {
		wg.Add(1)
		go func(claimIndex, batchID int, queryArgs args) {
			queryArgs.BatchID = batchID
			stmtBatch, err := db.Get().PrepareNamed(checkQuery)
			if err != nil {
				wg.Done()
				queryError = errors.Wrap(err, "Database error preparing get batch list checks query")
				return
			}
			defer func() { _ = stmtBatch.Close() }()
			checks := []string{}
			err = stmtBatch.Select(&checks, queryArgs)
			if err != nil && err != sql.ErrNoRows {
				wg.Done()
				queryError = errors.Wrap(err, "Error loading batch checks from database.")
				return
			}
			batchList[claimIndex].CheckNumber = strings.Join(checks, ", ")
			wg.Done()
		}(index, batch.ID, batchQueryArgs)
	}
	wg.Wait()
	if queryError != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(queryError, "Failed to get check details", nil)
	}
	return http.StatusOK, map[string]interface{}{"batchList": batchList, "count": count}
}

// BatchClaims returns details of batch :
// batch date,
// Vendor
// Product
// claim list,
// number of successfully submitted claims and number of failed claims
func BatchClaims(res http.ResponseWriter, req *http.Request, _ db.User) (int, map[string]interface{}) {
	batchID := chi.URLParam(req, "id")
	if batchID == "" {
		return http.StatusBadRequest, handlers.ErrorMessage(nil, "Invalid data", nil)
	}

	// List of claim which were submitted in a batch
	type batchClaimSlice []struct {
		ContractNumber    string          `json:"contract_number" db:"contract_number"`
		RO                string          `json:"ro" db:"ro"`
		Amount            decimal.Decimal `json:"amount" db:"amount"`
		IsSuccess         bool            `json:"is_success" db:"is_success"`
		Note              string          `json:"note" db:"-"`
		CheckNumber       string          `json:"check_number" db:"-"`
		AutomotiveClaimID int             `json:"automotive_claim_id" db:"automotive_claim_id"`
	}

	// Details of a batch
	batchDetails := struct {
		CreatedAt    time.Time       `json:"created_at" db:"created_at"`
		Amount       decimal.Decimal `json:"amount" db:"amount"`
		VendorID     sql.NullString  `json:"vendor_id" db:"vendor_id"`
		FacilityName string          `json:"facility_name" db:"facility_name"`
		ProductCode  string          `json:"product_code" db:"product_code"`
		TotalClaims  int             `json:"total_claims" db:"total_claims"`
		PayType      string          `json:"pay_type" db:"pay_type"`
		BatchClaims  batchClaimSlice `json:"batch_claims" db:"-"`
		CheckNumber  string          `json:"check_number" db:"-"`
	}{}

	claimListQuery := `select sum(automotive_intacct_batch_details.amount) as amount,
			   is_success,
			   automotive_claim_id,
               contract_number,
			   ro
		  	   from automotive_claims
		  	   join automotive_intacct_batch_details on automotive_intacct_batch_details.automotive_claim_id = automotive_claims.id
		  	   where automotive_intacct_batch_details.automotive_intacct_batch_id = $1 group by is_success, automotive_claim_id, contract_number, ro`

	orderByRO := req.FormValue("order_by_ro")
	if orderByRO == "desc" {
		claimListQuery += " order by ro " + orderByRO
	} else {
		// default order is asc
		claimListQuery += " order by ro asc"
	}

	err := db.Get().Select(&batchDetails.BatchClaims, claimListQuery, batchID)
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage(err, "The claim list not found", nil)
		}
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error loading batch claims history from database.", nil)
	}
	batchChecks := []string{}
	successCount := 0
	failureCount := 0
	for index, claim := range batchDetails.BatchClaims {
		if !claim.IsSuccess {
			var note string
			var noteLike = "'INTACCT_BATCH%in batch:" + batchID + "'"
			noteQuery := "select notes_text from automotive_record_notes where automotive_claim_id = $1 and notes_text like " + noteLike
			err := db.Get().Get(&note, noteQuery, claim.AutomotiveClaimID)
			if err != nil && err != sql.ErrNoRows {
				return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error loading batch claim note from database.", nil)
			}
			if err != sql.ErrNoRows {
				batchDetails.BatchClaims[index].Note = note
				failureCount = failureCount + 1
			}
		} else {
			checks := []string{}
			err := db.Get().Select(&checks,
				`select distinct automotive_claim_payment_checks.check_number
						from automotive_claim_payment_checks
							join automotive_claim_payments on automotive_claim_payment_checks.automotive_claim_payments_id = automotive_claim_payments.id
							join automotive_intacct_batch_details details
								on (details.automotive_claim_id = automotive_claim_payments.automotive_claim_id 
									and details.amount = automotive_claim_payment_checks.check_amount)
						where automotive_claim_payments.automotive_claim_id = $1 and details.automotive_intacct_batch_id = $2`, claim.AutomotiveClaimID, batchID)
			if err != nil && err != sql.ErrNoRows {
				return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error loading batch claim check details from database.", nil)
			}
			batchDetails.BatchClaims[index].CheckNumber = strings.Join(checks, ", ")
			if batchDetails.BatchClaims[index].CheckNumber == "" {
				batchDetails.BatchClaims[index].Note = "Pending check details"
			}
			for _, c := range checks {
				includes := func() bool {
					for _, batchCheck := range batchChecks {
						if batchCheck == c {
							return true
						}
					}
					return false
				}
				if !includes() {
					batchChecks = append(batchChecks, c)
				}
			}
			successCount = successCount + 1
		}
	}

	batchDetailsQuery := `select automotive_intacct_batches.created_at,
							sum(automotive_intacct_batch_details.amount) as amount,
							vendor_id,
							name as facility_name,
							product_code,
							count(*) as total_claims,
							pay_type
		       from automotive_intacct_batches
		       join automotive_intacct_batch_details on automotive_intacct_batches.id = automotive_intacct_batch_details.automotive_intacct_batch_id
		       join automotive_claims on automotive_intacct_batch_details.automotive_claim_id = automotive_claims.id
		       join automotive_facilities on automotive_claims.facility_id = automotive_facilities.id
		       where automotive_intacct_batches.id = $1 group by automotive_intacct_batches.created_at, vendor_id, automotive_facilities.name, product_code, pay_type`
	err = db.Get().Get(&batchDetails, batchDetailsQuery, batchID)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error loading batch date from database.", nil)
	}

	batchDetails.CheckNumber = strings.Join(batchChecks, ", ")

	isCsv := req.FormValue("csv")
	if isCsv == "true" {
		result := new(bytes.Buffer)
		w := csv.NewWriter(result)
		_ = w.Write([]string{"RO#", "Contract#", "$ Amount", "Notes"})
		for _, claim := range batchDetails.BatchClaims {
			row := make([]string, 4)
			row[0] = claim.RO
			row[1] = claim.ContractNumber
			row[2] = claim.Amount.String()
			row[3] = claim.Note
			_ = w.Write(row)
		}
		w.Flush()
		return http.StatusOK, map[string]interface{}{"batchDetails": result.String()}
	}

	return http.StatusOK, map[string]interface{}{"batchDetails": batchDetails}
}

// ApprovedClaims returns facility wise list of approved claims
func ApprovedClaims(res http.ResponseWriter, req *http.Request, _ db.User) (int, map[string]interface{}) {
	ctx := req.Context()
	type claimSlice []struct {
		ID             int             `json:"id" db:"id"`
		ContractNumber string          `json:"contract_number" db:"contract_number"`
		RO             sql.NullString  `json:"ro" db:"ro"`
		Estimate       decimal.Decimal `json:"estimate" db:"estimate"`
		Agent          sql.NullString  `json:"agent" db:"agent"`
		Approver       sql.NullString  `json:"approver" db:"approver"`
		AutoApproved   bool            `json:"auto_approved" db:"auto_approved"`
		ClaimType      string          `json:"claim_type" db:"claim_type"`
		ClaimPaymentID null.Int        `json:"claim_payment_id" db:"claim_payment_id"`
	}

	type productSlice []struct {
		ProductCode string     `json:"product_code" db:"product_code"`
		Claims      claimSlice `json:"claims" db:"-"`
	}

	type batchDetails struct {
		FacilityID        int             `json:"facility_id" db:"facility_id"`
		FacilityName      string          `json:"facility_name" db:"facility_name"`
		TotalClaims       int             `json:"total_claims" db:"total_claims"`
		TotalAmount       decimal.Decimal `json:"total_amount" db:"total_amount"`
		TotalProducts     int             `json:"total_products" db:"total_products"`
		CustomerPayeeName string          `json:"customer_payee_name" db:"-"`
		PayType           string          `json:"pay_type" db:"pay_type"`
		VendorID          string          `json:"vendor_id" db:"vendor_id"`
		Products          productSlice    `json:"products" db:"-"`
	}

	args := struct {
		FacilityID     int    `db:"facility_id"`
		ProductCode    string `db:"product_code"`
		Status         string `db:"status"`
		PayType        string `db:"pay_type"`
		CompanyGroupID int    `db:"company_group_id"`
	}{}

	var whereClauses []string

	args.FacilityID, _ = strconv.Atoi(req.FormValue("facility"))
	if args.FacilityID > 0 {
		whereClauses = append(whereClauses, `facility_id = :facility_id`)
	}

	args.ProductCode = req.FormValue("product")
	if args.ProductCode != "" {
		whereClauses = append(whereClauses, `product_code = :product_code`)
	}

	args.CompanyGroupID, _ = strconv.Atoi(req.FormValue("companyGroup"))
	if args.CompanyGroupID > 0 {
		whereClauses = append(whereClauses, `company_group_id = :company_group_id`)
	}

	reqPayType := req.FormValue("pay_type")
	batches := []batchDetails{}
	if reqPayType == "all" || reqPayType == db.PayTypeCreditCard {
		ccBatches := []batchDetails{}
		args.PayType = db.PayTypeCreditCard
		args.Status = db.AutoClaimStatusInvoiceSent
		whereQuery := `where status in (:status, '` + db.AutoClaimStatusReversed + `', '` + db.AutoClaimStatusAdjusted + `', '` + db.AutoClaimStatusApproved + `') 
						and pay_type = :pay_type and (is_complete is null or is_complete = false)`
		if len(whereClauses) != 0 {
			whereQuery += ` and ` + strings.Join(whereClauses, " and ")
		}
		// generate batch for story or cc pay claims
		batchQueryStore := `select
                            facility_id,
                            automotive_facilities.name as facility_name,
                            pay_type,
                            sum(coalesce(amount, estimate)) as total_amount,
                            count(*) as total_claims,
                            count(distinct product_code) as total_products
						from automotive_claims
							left join automotive_claim_payments acp on automotive_claims.id = acp.automotive_claim_id
                            left join automotive_facilities on automotive_claims.facility_id = automotive_facilities.id
							left join stores on stores.id = automotive_facilities.store_id
							left join companies on companies.id = stores.company_id
 			            ` + whereQuery + `
                      group by pay_type, automotive_facilities.name, facility_id`
		// Get list of batches which has approved claims with store pay
		storeStmt, err := db.Get().PrepareNamed(batchQueryStore)
		if err != nil {
			err = errors.Wrap(err, "Database error preparing get batch batch list batchQueryStore")
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting approved claims", nil)
		}

		defer func() { _ = storeStmt.Close() }()
		err = storeStmt.Select(&ccBatches, args)
		if err != nil && err != sql.ErrNoRows {
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error loading approved claims from database for store payments.", nil)
		}
		batches = append(batches, ccBatches...)
	}

	if reqPayType == "all" || reqPayType == db.PayTypeStore {
		storeBatches := []batchDetails{}
		args.PayType = db.PayTypeStore
		args.Status = db.AutoClaimStatusApproved
		whereQuery := `where status in (:status, '` + db.AutoClaimStatusReversed + `', '` + db.AutoClaimStatusAdjusted + `') 
						and pay_type = :pay_type and (is_complete is null or is_complete = false)`
		if len(whereClauses) != 0 {
			whereQuery += ` and ` + strings.Join(whereClauses, " and ")
		}
		// generate batch for story or cc pay claims
		batchQueryStore := `select
                            facility_id,
                            automotive_facilities.name as facility_name,
                            pay_type,
                            sum(coalesce(amount, estimate)) as total_amount,
                            count(*) as total_claims,
                            count(distinct product_code) as total_products
						from automotive_claims
							left join automotive_claim_payments acp on automotive_claims.id = acp.automotive_claim_id
                            left join automotive_facilities on automotive_claims.facility_id = automotive_facilities.id
							left join stores on stores.id = automotive_facilities.store_id
							left join companies on companies.id = stores.company_id
 			            ` + whereQuery + `
                      group by pay_type, automotive_facilities.name, facility_id`
		// Get list of batches which has approved claims with store pay
		storeStmt, err := db.Get().PrepareNamed(batchQueryStore)
		if err != nil {
			err = errors.Wrap(err, "Database error preparing get batch batch list batchQueryStore")
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting approved claims", nil)
		}

		defer func() { _ = storeStmt.Close() }()
		err = storeStmt.Select(&storeBatches, args)
		if err != nil && err != sql.ErrNoRows {
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error loading approved claims from database for store payments.", nil)
		}
		batches = append(batches, storeBatches...)
	}

	if reqPayType == "all" || reqPayType == db.PayTypeCustomer {
		customerBatches := []batchDetails{}
		args.PayType = db.PayTypeCustomer
		args.Status = db.AutoClaimStatusApproved
		whereQuery := `where status in (:status, '` + db.AutoClaimStatusReversed + `', '` + db.AutoClaimStatusAdjusted + `') 
					and pay_type = :pay_type and (is_complete is null or is_complete = false)`
		if len(whereClauses) != 0 {
			whereQuery += ` and ` + strings.Join(whereClauses, " and ")
		}
		// generate batch for customer pay claims
		batchQueryCustomer := `select
							facility_id,
                            pay_type,
                            customer_payee_vendor_id as vendor_id,
                            sum(coalesce(amount, estimate)) as total_amount,
                            count(*) as total_claims,
                            count(distinct product_code) as total_products
						from automotive_claims
							left join automotive_claim_payments acp on automotive_claims.id = acp.automotive_claim_id
                            left join automotive_facilities on automotive_claims.facility_id = automotive_facilities.id
							left join stores on stores.id = automotive_facilities.store_id
							left join companies on companies.id = stores.company_id
 			            ` + whereQuery + `
                        group by pay_type, customer_payee_vendor_id, facility_id`

		// Get list of batches which has approved claims with customer pay
		customerStmt, err := db.Get().PrepareNamed(batchQueryCustomer)
		if err != nil {
			err = errors.Wrap(err, "Database error preparing get batch batch list batchQueryCustomer")
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting approved claims", nil)
		}

		defer func() { _ = customerStmt.Close() }()
		err = customerStmt.Select(&customerBatches, args)
		if err != nil && err != sql.ErrNoRows {
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error loading approved claims from database for customer payments.", nil)
		}

		batches = append(batches, customerBatches...)
	}

	if len(batches) == 0 {
		return http.StatusNotFound, handlers.ErrorMessage(errors.New("No approved claims found"), "No approved claims found", nil)
	}

	var warnings []string
	var finalBatches []batchDetails
	for batchIndex, batch := range batches {
		productArgs := struct {
			FacilityID  int    `db:"facility_id"`
			ProductCode string `db:"product_code"`
			Status      string `db:"status"`
			PayType     string `db:"pay_type"`
			VendorID    string `db:"vendor_id"`
		}{FacilityID: batch.FacilityID, ProductCode: args.ProductCode, Status: args.Status, PayType: batch.PayType, VendorID: batch.VendorID}

		productFilter := ""
		if productArgs.ProductCode != "" {
			productFilter = `and product_code = :product_code`
		}

		productQuery := `select product_code
						 from automotive_claims
						 where facility_id = :facility_id and status in(:status, '` + db.AutoClaimStatusReversed + `', '` + db.AutoClaimStatusAdjusted + `', '` + db.AutoClaimStatusApproved + `') 
								and pay_type = :pay_type ` + productFilter + ` group by product_code`

		if batch.PayType == db.PayTypeCustomer {
			productQuery = `select product_code
						 from automotive_claims
						 where status in(:status, '` + db.AutoClaimStatusReversed + `', '` + db.AutoClaimStatusAdjusted + `', '` + db.AutoClaimStatusApproved + `') 
							and pay_type = :pay_type and customer_payee_vendor_id = :vendor_id ` + productFilter + ` group by product_code`
			// get vendor query payload
			vendorQuery := intacct.VendorQueryPayload{}.VendorID(batches[batchIndex].VendorID)
			// get vendors from intacct
			vendors, err := intacct.GetVendors(ctx, vendorQuery)
			if err != nil {
				return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error in getting vendors from intacct", nil)
			}
			if len(vendors) == 0 {
				warnings = append(warnings, fmt.Sprintf("VendorID %s not found in intacct", productArgs.VendorID))
				continue
			}
			batches[batchIndex].CustomerPayeeName = vendors[0].Name
		}

		if batch.PayType == db.PayTypeCreditCard {
			productArgs.Status = db.AutoClaimStatusInvoiceSent
		}

		// Get list of products which has approved claims belong to given batch
		productStmt, err := db.Get().PrepareNamed(productQuery)
		if err != nil {
			err = errors.Wrap(err, "Database error preparing get batch product list productQuery")
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting approved claims", nil)
		}
		defer func() { _ = productStmt.Close() }()

		err = productStmt.Select(&batch.Products, productArgs)
		if err != nil {
			if err == sql.ErrNoRows {
				return http.StatusNotFound, handlers.ErrorMessage(err, "The product list not found", nil)
			}
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error loading batch products from database.", nil)
		}
		batches[batchIndex].Products = batch.Products
		for productIndex, product := range batch.Products {
			claimArgs := struct {
				PayableNote            string `db:"payable_note"`
				ApprovedNote           string `db:"approved_note"`
				ChargebackApprovedNote string `db:"chargeback_approved_note"`
				ProductCode            string `db:"product_code"`
				FacilityID             int    `db:"facility_id"`
				Status                 string `db:"status"`
				PayType                string `db:"pay_type"`
				VendorID               string `db:"vendor_id"`
				ReversedNote           string `db:"reversed_note"`
			}{
				PayableNote:            db.AutomotiveRecordNoteDescription[db.AutoClaimStatusPayable],
				ApprovedNote:           db.AutomotiveRecordNoteDescription[db.AutoClaimStatusApproved],
				ChargebackApprovedNote: db.AutomotiveChargebackRecordNoteDescription[db.AutoClaimStatusApproved],
				FacilityID:             batch.FacilityID,
				ProductCode:            product.ProductCode,
				Status:                 args.Status,
				PayType:                batch.PayType,
				VendorID:               batch.VendorID,
				ReversedNote:           db.AutomotiveRecordNoteDescription[db.AutoClaimStatusReversed],
			}
			selectAgent := `select users.first_name || ' ' || users.last_name
							from automotive_record_notes
							join users on users.id = automotive_record_notes.created_by_user_id
							where notes_text in (:payable_note, :reversed_note) and automotive_record_notes.automotive_claim_id = automotive_claims.id order by automotive_record_notes.created_at desc limit 1`
			selectApprover := `select users.first_name || ' ' || users.last_name
							from automotive_record_notes
							join users on users.id = automotive_record_notes.created_by_user_id
							where notes_text in (:approved_note, :chargeback_approved_note) and automotive_record_notes.automotive_claim_id = automotive_claims.id order by automotive_record_notes.created_at desc limit 1`
			claimsQuery := `select automotive_claims.id, contract_number, ro, 
								case when acp.id is null then estimate else coalesce(acp.amount, estimate) end as estimate,
								(` + selectAgent + `) as agent,
								(` + selectApprover + `) as approver, auto_approved, claim_type, acp.id as claim_payment_id
							from automotive_claims
								left join automotive_claim_payments acp on automotive_claims.id = acp.automotive_claim_id
							where product_code = :product_code and facility_id = :facility_id 
								and status in (:status, '` + db.AutoClaimStatusReversed + `', '` + db.AutoClaimStatusAdjusted + `', '` + db.AutoClaimStatusApproved + `') 
								and pay_type = :pay_type
								and (is_complete is null or is_complete = false)`

			if batch.PayType == db.PayTypeCustomer {
				claimsQuery = `select automotive_claims.id, contract_number, ro, 
									case when acp.id is null then estimate else coalesce(acp.amount, estimate) end as estimate,
									(` + selectAgent + `) as agent,
									(` + selectApprover + `) as approver, auto_approved, claim_type, acp.id as claim_payment_id
								from automotive_claims 
									left join automotive_claim_payments acp on automotive_claims.id = acp.automotive_claim_id 
								where product_code = :product_code 
									and status in (:status, '` + db.AutoClaimStatusReversed + `', '` + db.AutoClaimStatusAdjusted + `', '` + db.AutoClaimStatusApproved + `') 
									and pay_type = :pay_type and customer_payee_vendor_id = :vendor_id
									and (is_complete is null or is_complete = false)`
			}

			if batch.PayType == db.PayTypeCreditCard {
				claimArgs.Status = db.AutoClaimStatusInvoiceSent
				claimArgs.ApprovedNote = db.AutomotiveRecordNoteDescription[db.AutoClaimStatusInvoiceSent]
			}

			// Get list of approved claims from given product
			claimStmt, err := db.Get().PrepareNamed(claimsQuery)
			if err != nil {
				err = errors.Wrap(err, "Database error preparing get claims list claimQuery")
				return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting approved claims", nil)
			}
			defer func() { _ = claimStmt.Close() }()

			err = claimStmt.Select(&product.Claims, claimArgs)
			if err != nil {
				if err == sql.ErrNoRows {
					return http.StatusNotFound, handlers.ErrorMessage(err, "The claims list not found", nil)
				}
				return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error loading batch claims from database.", nil)
			}

			batches[batchIndex].Products[productIndex].Claims = product.Claims
		}

		// Filter out batches that we had to skip because of problems
		finalBatches = append(finalBatches, batches[batchIndex])
	}

	return http.StatusOK, map[string]interface{}{
		"facilities": finalBatches,
		"message":    warnings,
	}
}

// CreateBillNumber generates bill number
// createBillNumber append `X` + `n` linecode for each manual complaint where `n` is number of manual complaint.
// To get `n` number, it will search for all manual complaint with same RO and contract number in database excluding self claim.
// For ex. facilityCode is "ABCDE" and RO is "12345", bill number will be "ABCDE_12345X1" where X1 is manual complaint line code if there is any manual complaint
func CreateBillNumber(ctx context.Context, tx *sqlx.Tx, claim *ClaimPayload, facilityCode string, contractNumber string) (string, error) {
	lineCountQuery := `select count(complaint.id) from automotive_claims claim
	join automotive_claim_complaints complaint on claim.id = complaint.automotive_claim_id
  	where claim.contract_number = $1
        and claim.ro = $2
        and complaint.is_manual = true
    	and claim.status in ($3, $4, $5)
		and claim.id != $6`

	var count int
	err := tx.GetContext(ctx, &count, lineCountQuery, claim.ContractNumber, claim.RO, db.AutoClaimStatusCCPaid, db.AutoClaimStatusCheckWritten,
		db.AutoClaimStatusWaitingForCheck, claim.ID)
	if err != nil {
		return "", err
	}

	billNumber := ""
	if claim.PayType == db.PayTypeCreditCard {
		billNumber = db.CreditCardBillPrefix + facilityCode
	} else if claim.PayType == db.PayTypeCustomer {
		billNumber = db.CustomerBillPrefix
	} else {
		billNumber = facilityCode
	}

	billNumber = billNumber + "_" + claim.ProductCode + "_" + claim.RO + "_" + contractNumber

	lineCodes := []string{}
	for _, complaint := range claim.Complaints {
		count++
		if complaint.IsManual && complaint.LineCode == "" {
			complaint.LineCode = fmt.Sprintf("X%d", count)

			// update line code
			err = updateLineCode(ctx, tx, complaint.ID, complaint.LineCode)
			if err != nil {
				return "", err
			}
		}
		lineCodes = append(lineCodes, complaint.LineCode)
	}

	// sort complaint by LineCode
	sort.Slice(lineCodes[:], func(i, j int) bool {
		return strings.Compare(lineCodes[i], lineCodes[j]) < 0
	})
	billNumber = billNumber + strings.Join(lineCodes, "")

	// We want unique bill number for every bill
	var loc = handlers.LoadLocOrPanic(db.TimeZoneMountain)
	currentDate := time.Now().In(loc).Format("01-02-2006_15:04:05")
	billNumber = billNumber + "_" + currentDate

	return billNumber, nil
}

// CustomerPayee returns the customer payee information for the claim
func CustomerPayee(res http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	ctx := req.Context()
	vendorID := ""
	err := db.Get().Get(&vendorID, "select customer_payee_vendor_id from automotive_claims where id = $1", chi.URLParam(req, "id"))
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage(err, "Claim not found", nil)
		}
		handlers.ReportError(req, errors.Wrap(err, "error while loading claim"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error loading claim", nil)
	}

	if vendorID == "" {
		return http.StatusNotFound, handlers.ErrorMessage(errors.New("vendor id not set"), "vendor id not set", nil)
	}
	// get vendor query payload
	vendorQuery := intacct.VendorQueryPayload{}.
		VendorID(vendorID)

	// get vendors from intacct
	vendors, err := intacct.GetVendors(ctx, vendorQuery)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error in getting vendors from intacct", nil)
	}
	if len(vendors) == 0 {
		return http.StatusNotFound, handlers.ErrorMessage(errors.New("vendor id not found in intacct"), "vendor id not found in intacct", nil)
	}
	payload := payeePayload{}
	payload.VendorID = vendorID
	payload.Address = vendors[0].Address1
	payload.City = vendors[0].City
	payload.State = vendors[0].State
	payload.PostalCode = vendors[0].Zip
	payload.Name = vendors[0].Name

	return http.StatusOK, map[string]interface{}{"payee": payload}
}

// CustomerPayeeUpdate add or update the customer payee information for the claim
func CustomerPayeeUpdate(res http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	ctx := req.Context()
	claimDB := struct {
		ID       int    `db:"id"`
		VendorID string `db:"customer_payee_vendor_id"`
	}{}
	err := db.Get().GetContext(ctx, &claimDB, "select id,customer_payee_vendor_id from automotive_claims where id = $1", chi.URLParam(req, "id"))
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage(nil, "Claim not found", nil)
		}
		handlers.ReportError(req, errors.Wrap(err, "error while loading claim"))
		return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Error loading claim", nil)
	}

	payload := payeePayload{}
	dec := json.NewDecoder(req.Body)
	err = dec.Decode(&payload)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "decoding claim request failed while updating payee info", nil)
	}

	formErrors, err := validatePayee(ctx, payload.VendorID)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, err.Error()))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, err.Error(), nil)
	}
	if len(formErrors) > 0 {
		return http.StatusBadRequest, map[string]interface{}{"errors": formErrors}
	}

	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "database error beginning transaction for add or update claim payee"))
		return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Database begin transaction failed in add/update payee information", nil)
	}

	_, err = db.Get().ExecContext(ctx, "update automotive_claims set customer_payee_vendor_id = $1 where id = $2", payload.VendorID, claimDB.ID)
	if err != nil {
		_ = tx.Rollback()
		handlers.ReportError(req, errors.Wrap(err, "Failed to update payee information"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Failed to update payee information", nil)
	}

	noteText := ""
	if claimDB.VendorID != "" {
		noteText = "Updated customer payee information"
	} else {
		noteText = "Added customer payee information"
	}

	err = addClaimNote(ctx, tx, claimDB.ID, user.ID, noteText)
	if err != nil {
		_ = tx.Rollback()
		handlers.ReportError(req, errors.Wrap(err, "Failed : Error in updating record note for payee"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Failed : Error in updating record note", nil)
	}

	err = tx.Commit()
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "Failed : Transaction error in payee update/delete"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Failed : Transaction error", nil)
	}
	return http.StatusOK, map[string]interface{}{"message": "Updated payee information successfully"}
}

func validatePayee(ctx context.Context, vendorID string) ([]string, error) {
	formErrors := []string{}

	// get vendor query payload
	vendorQuery := intacct.VendorQueryPayload{}.
		VendorID(vendorID)

	// get vendors from intacct
	vendors, err := intacct.GetVendors(ctx, vendorQuery)
	if err != nil {
		return formErrors, errors.New("Error in getting vendors from intacct")
	}
	if len(vendors) != 1 {
		formErrors = append(formErrors, "Invalid vendor")
	}

	return formErrors, nil
}

// ClaimReassignIndex returns a list of claims for reassignment page
// This function returns all automotive claims if no query parameters are provided
// The claims can be filtered by following query parameters
// q = filter by firstname, lastname, contract_number or facility code
// userID - createdby userid
// Status - return claims with the given status
func ClaimReassignIndex(res http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	ctx := req.Context()
	claims := claimListSlice{}
	var whereClauses []string // where clause
	args := struct {
		SearchQuery       string `db:"search_query"`
		OwnerID           int    `db:"owner_id"`
		ReassignedOwnerID int    `db:"reassigned_owner_id"`
		Status            string `db:"status"`
	}{}

	searchQuery := req.FormValue("q")
	if searchQuery != "" {
		whereClauses = append(whereClauses, "concat(customers.first_name, ' ', customers.last_name) ilike :search_query or concat(customers.last_name, ' ', customers.first_name) ilike :search_query or contract_number ilike :search_query or facility_code ilike :search_query or concat(users.first_name,' ', users.last_name) ilike :search_query or concat(users.last_name,' ', users.first_name) ilike :search_query or automotive_claims.status ilike :search_query")
		args.SearchQuery = "%" + strings.Join(strings.Fields(strings.TrimSpace(searchQuery)), " ") + "%"
	}

	status := req.FormValue("status")
	if status != "" && isValidClaimStatus(status) {
		whereClauses = append(whereClauses, "automotive_claims.status = :status")
		args.Status = status
	}

	userID := req.FormValue("user_id")
	// if userID is not provided in query, don't restrict by userID, show all claims
	var err error
	if userID != "" {
		ownerID, err := strconv.Atoi(userID)
		if err == nil {
			wh := "automotive_claims.owner_id = :owner_id"
			args.OwnerID = ownerID
			reassignedOwnerID := req.FormValue("reassigned_user_id")
			if reassignedOwnerID != "" {
				reassignedID, err := strconv.Atoi(reassignedOwnerID)
				if err == nil {
					wh += " or automotive_claims.reassigned_owner_id = :reassigned_owner_id"
					args.ReassignedOwnerID = reassignedID
				}
			}
			whereClauses = append(whereClauses, wh)
		}
	}

	if args.Status == "" && args.OwnerID <= 0 && args.SearchQuery == "" && args.ReassignedOwnerID <= 0 {

		statuses := fmt.Sprintf("'%s','%s','%s','%s','%s','%s','%s','%s','%s','%s'",
			db.AutoClaimStatusOpen, db.AutoClaimStatusReturned, db.AutoClaimStatusPreAuth,
			db.AutoClaimStatusDenied, db.AutoClaimStatusWaitingOnVendor,
			db.AutoClaimStatusNeedRentalBill, db.AutoClaimStatusNeedSubletBill,
			db.AutoClaimStatusNeedSMToCall, db.AutoClaimStatusNeedClosedAccountingRO,
			db.AutoClaimStatusNeedProofOfDeductibleReimbursement)
		whereClauses = append(whereClauses, "automotive_claims.status in ("+statuses+")")
	}

	wh := ""
	if len(whereClauses) > 0 {
		wh = "where " + strings.Join(whereClauses, " and ")
	}

	sortBy := req.FormValue("sort_by")
	if sortBy == "" {
		sortBy = " date_of_claim_received "
	}
	sortOrder := req.FormValue("sort_order")
	if sortOrder == "" {
		sortOrder = " desc "
	}

	// default order should be date_of_claim_received
	orderBy := "order by " + sortBy + " " + sortOrder + " "

	selectClause := `automotive_claims.id, customers.last_name || ',' || customers.first_name as customer_name, contract_number, vin, status, date_of_claim_received, facility_code as facility, estimate, reassignment_status, owner_id, reassigned_owner_id`
	fromClause := `automotive_claims left join customers on automotive_claims.customer_id = customers.id
									 left join automotive_facilities on automotive_claims.facility_id = automotive_facilities.id
									 left join users on automotive_claims.owner_id = users.id`

	countQuery := "select count(*) from " + fromClause + " " + wh

	// handle pagination
	p := req.FormValue("page")
	var listQuery string
	var n int
	if n, err = strconv.Atoi(p); err == nil && n > 0 {
		listQuery = fmt.Sprintf("select %s from %s %s %s limit %d offset %d", selectClause, fromClause, wh, orderBy, handlers.PerPageEntries, (n-1)*handlers.PerPageEntries)
	} else { // if page number is not provided, return all values
		listQuery = fmt.Sprintf("select %s from %s %s %s", selectClause, fromClause, wh, orderBy)
	}

	stmt, err := db.Get().PrepareNamedContext(ctx, listQuery)
	if err != nil {
		err = errors.Wrap(err, "Database error preparing get claims list query")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting claims", nil)
	}
	defer func() { _ = stmt.Close() }()
	err = stmt.SelectContext(ctx, &claims, args)
	if err != nil {
		err = errors.Wrap(err, "Database error getting Auto claims lists")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting Auto claims lists data", nil)
	}

	stmt2, err := db.Get().PrepareNamedContext(ctx, countQuery)
	if err != nil {
		err = errors.Wrap(err, "Database error preparing automotive claims count query")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting claims count", nil)
	}
	defer func() { _ = stmt2.Close() }()
	count := 0
	err = stmt2.GetContext(ctx, &count, args)
	if err != nil {
		err = errors.Wrap(err, "Database error getting Auto claims lists count")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting Auto claims lists data", nil)
	}

	return http.StatusOK, map[string]interface{}{"count": count, "automotive_claims": claims}
}

// CCClaimUpdate udpates claim to invocie sent status
func CCClaimUpdate(_ http.ResponseWriter, req *http.Request, _ db.User) (int, map[string]interface{}) {
	ctx := req.Context()
	claimResp, err := getClaimByID(chi.URLParam(req, "id"))
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage(nil, "Claim not found", nil)
		}
		handlers.ReportError(req, errors.Wrap(err, "error while loading claim"))
		return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Error loading claim", nil)
	}

	if claimResp.Status != db.AutoClaimStatusAuthorizedCCClaim {
		return http.StatusBadRequest, handlers.ErrorMessage(nil, "Invalid status of the claim", nil)
	}

	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error creating transaction"))
		return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Database error updating auto claim", nil)
	}

	query := `update automotive_claims set status = $1 where id = $2`
	_, err = tx.ExecContext(ctx, query, db.AutoClaimStatusInvoiceSent, chi.URLParam(req, "id"))
	if err != nil {
		_ = tx.Rollback()
		handlers.ReportError(req, errors.Wrap(err, "error updating cc claim status"))
		return http.StatusBadRequest, handlers.ErrorMessage(nil, " Error updating claim status", nil)
	}

	err = addClaimNote(ctx, tx, claimResp.ID, claimResp.OwnerID, db.AutomotiveRecordNoteDescription[db.AutoClaimStatusInvoiceSent])
	if err != nil {
		_ = tx.Rollback()
		handlers.ReportError(req, errors.Wrap(err, "errors in updating records notes"))
		return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Database error updating auto claim.", nil)
	}

	err = tx.Commit()
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error committing transaction"))
		return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Database error updating auto claim", nil)
	}

	return http.StatusOK, map[string]interface{}{"id": claimResp.ID}
}

// ApprovedClaimsSupportingData returns supporting data for approved claims filter
func ApprovedClaimsSupportingData(_ http.ResponseWriter, req *http.Request, _ db.User) (int, map[string]interface{}) {
	ctx := req.Context()
	type companyGroup struct {
		ID   int    `db:"id" json:"id"`
		Name string `db:"name" json:"name"`
	}

	var companyGroups []companyGroup
	query := `select id, name from company_groups order by name`
	if err := db.Get().SelectContext(ctx, &companyGroups, query); err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage(nil, "Company groups not found", nil)
		}
		handlers.ReportError(req, errors.Wrap(err, "error getting company groups"))
		return http.StatusInternalServerError, nil
	}

	return http.StatusOK, map[string]interface{}{"company_groups": companyGroups}
}
