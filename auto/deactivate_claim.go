package auto

import (
	"context"
	"log"

	"phizz/db"

	"github.com/jmoiron/sqlx"
	"github.com/pkg/errors"
)

// DeactivateOldClaims deactivates unfinished claims which are older than 120 days and are in
// 'PreAuthorization', 'Payable', 'Open', 'Waiting on Vendor #', 'Waiting on Dealership' status
func DeactivateOldClaims(ctx context.Context) error {

	// get all unfinished claims not updated in 120 days
	q := `select ac.id, ac.contract_number
		from automotive_claims ac join automotive_claim_updates acu on acu.automotive_claim_id = ac.id
		where status in (?)
		group by ac.id, ac.contract_number
		having max(updated_at) < (current_date - 120)`

	claimsQ, args, err := sqlx.In(q, []string{
		db.AutoClaimStatusPreAuth, db.AutoClaimStatusPayable, db.AutoClaimStatusOpen,
		db.AutoClaimStatusWaitingOnVendor, db.AutoClaimStatusNeedRentalBill,
		db.AutoClaimStatusNeedSubletBill, db.AutoClaimStatusNeedSMToCall,
		db.AutoClaimStatusNeedClosedAccountingRO, db.AutoClaimStatusNeedProofOfDeductibleReimbursement,
	})

	if err != nil {
		return errors.Wrap(err, "Error in getting old claims for deactivation, In query failed")
	}
	query := db.Get().Rebind(claimsQ)

	var claims []struct {
		ID             int    `db:"id"`
		ContractNumber string `db:"contract_number"`
	}
	err = db.Get().SelectContext(ctx, &claims, query, args...)
	if err != nil {
		return errors.Wrap(err, "Error in DeactivateOldClaims getting old claims")
	}

	if len(claims) == 0 {
		log.Println("No old claims found for deactivation")
		return nil
	}

	// Get system user
	userID := 0
	err = db.Get().GetContext(ctx, &userID, "select id from users where first_name = $1 limit 1", db.SystemUserName)
	if err != nil {
		return errors.Wrap(err, "Database error while getting system user")
	}

	for _, claim := range claims {
		tx, err := db.Get().BeginTxx(ctx, nil)
		if err != nil {
			return errors.Wrap(err, "Database error beginning transaction for DeactivateOldClaims")
		}

		_, err = db.Get().ExecContext(ctx, "update automotive_claims set status = $1 where id=$2", db.AutoClaimStatusDeactivated, claim.ID)
		if err != nil {
			return errors.Wrap(err, "Error in DeactivateOldClaims for:"+claim.ContractNumber)
		}

		err = ClaimUpdated(ctx, tx, claim.ID, userID)
		if err != nil {
			_ = tx.Rollback()
			return errors.Wrap(err, "Error inserting automotive_claim_updates in DeactivateOldClaims")
		}
		err = tx.Commit()
		if err != nil {
			return errors.Wrap(err, "Error in committing transaction for DeactivateOldClaims")
		}
		log.Printf("Deactivated claim contract# %s ID %d", claim.ContractNumber, claim.ID)
	}

	return nil
}
