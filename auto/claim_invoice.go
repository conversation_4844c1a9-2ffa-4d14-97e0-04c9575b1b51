package auto

import (
	"bytes"
	"database/sql"
	"encoding/base64"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"strconv"
	"time"

	"phizz/db"
	"phizz/handlers"
	"phizz/pdftk"
	"phizz/s3util"

	"github.com/go-chi/chi"
	"github.com/pkg/errors"
)

const (
	// ClaimDoucmentTypeCCPayment indecates document type for credit card payment print
	ClaimDoucmentTypeCCPayment = "CCPAYMENT"
)

// CCInvoice downloads credit card invoice information
func CCInvoice(w http.ResponseWriter, req *http.Request, user db.User) {
	ctx := req.Context()
	id := chi.URLParam(req, "id")
	payload := struct {
		Date           string
		CardName       string
		CardNumber     string
		ExpirationDate string
		ManagerName    string
		ManagerFax     string
		ManagerPhone   string
		FacilityName   string `db:"facility_name"`
		FacilityFax    string `db:"facility_fax"`
		FacilityEmail  string `db:"facility_email"`
		ContractNumber string `db:"contract_number"`
		Customer       string `db:"customer"`
		AgentName      string `db:"agent_name"`
		PaymentAmount  string `db:"estimate"`
		RO             string `db:"ro"`
		PreAuthNumber  string `db:"pre_auth_number"`
	}{
		Date: time.Now().Format("Mon, _2 Jan 2006"),
	}

	if id == "" {
		w.WriteHeader(http.StatusBadRequest)
		fmt.Fprint(w, "Invalid request")
		return
	}

	query := `select facility_name, contract_number, estimate, facility_fax, ro,
		  case when facility.email is null then '' else facility.email end as facility_email,
		  cust.first_name || ' ' || cust.last_name as customer,
		  u.first_name  || ' ' || u.last_name as agent_name,
		  coalesce(acpan.pre_auth_number, '') as pre_auth_number
		from automotive_claims claim
		  join automotive_facilities facility on claim.facility_id = facility.id
		  join customers cust on claim.customer_id = cust.id
		  join users u on claim.owner_id = u.id
		  left join automotive_claim_pre_auth_numbers acpan on claim.id = acpan.automotive_claim_id
		where claim.id = $1`

	err := db.Get().GetContext(ctx, &payload, query, id)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error getting claim details from database"))
		w.WriteHeader(http.StatusInternalServerError)
		fmt.Fprint(w, `Error in getting auto claims information from db for credit card invoice`)
		return
	}

	var loc = handlers.LoadLocOrPanic(db.TimeZoneMountain)
	currentDate := time.Now().In(loc).Format("01/02/2006")
	currentTimeStamp := time.Now().In(loc).Format("01/02/2006 15:04:05")

	stampedFile, err := ioutil.TempFile("", "temp-claim-cc-authorization")
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error creating temp file for pdf"))
		w.WriteHeader(http.StatusInternalServerError)
		fmt.Fprint(w, `Error in genrating pdf`)
		return
	}
	fields := map[string]string{
		"genrated_date":        currentDate,
		"repair_facility":      payload.FacilityName,
		"fax_email":            fmt.Sprintf("%s  %s", payload.FacilityFax, payload.FacilityEmail),
		"customer_name":        payload.Customer,
		"contract_number":      payload.ContractNumber,
		"ro_number":            payload.RO,
		"authorization_number": payload.PreAuthNumber,
		"claim_agent":          payload.AgentName,
		"authorization_amount": payload.PaymentAmount,
		"generated_at":         currentTimeStamp,
	}

	err = pdftk.FillForm("files/Credit_Card_Authorization.pdf", fields, stampedFile.Name())
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error filling pdf details for pdf"))
		w.WriteHeader(http.StatusInternalServerError)
		fmt.Fprint(w, `Error in genrating pdf`)
		return
	}

	b := bytes.NewBuffer(nil)
	io.Copy(b, stampedFile)

	// add activity note for printing cc invoice
	claimID, _ := strconv.Atoi(id)
	err = addClaimNote(ctx, nil, claimID, user.ID, "CC invoice printed with estimate amount $"+payload.PaymentAmount)
	if err != nil {
		w.WriteHeader(http.StatusInternalServerError)
		fmt.Fprint(w, "Error adding note for cc invoice print")
		return
	}

	query = `select id from automotive_claim_documents where automotive_claim_id = $1 and document_type= $2 and deleted_at is null`
	var documentID int
	err = db.Get().GetContext(ctx, &documentID, query, claimID, ClaimDoucmentTypeCCPayment)
	if err != nil && err != sql.ErrNoRows {
		handlers.ReportError(req, errors.Wrap(err, "error getting claim document details from database"))
		w.WriteHeader(http.StatusInternalServerError)
		fmt.Fprint(w, `Error in getting auto claims information from db for credit card invoice`)
		return
	}

	if documentID != 0 {
		err = documentDelete(documentID, user.ID)
		if err != nil {
			handlers.ReportError(req, errors.Wrap(err, "error deleting claim document from database"))
			w.WriteHeader(http.StatusInternalServerError)
			fmt.Fprint(w, `Error in getting auto claims information from db for credit card invoice`)
			return
		}
	}

	document := documentPayload{
		AutomotiveClaimID: claimID,
		FileContent:       base64.StdEncoding.EncodeToString(b.Bytes()),
		CreatedByUserID:   user.ID,
		FileName:          fmt.Sprintf("%s-%s-%s.pdf", payload.ContractNumber, payload.RO, time.Now().In(loc).Format("01022006150405")),
		S3Bucket:          s3util.Bucket(),
		DocumentType:      ClaimDoucmentTypeCCPayment,
	}

	_, err = saveDocument(ctx, &document, nil, user)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error aving claim document to database"))
		w.WriteHeader(http.StatusInternalServerError)
		fmt.Fprint(w, "Error in saving document")
		return
	}

	w.Header().Set("Content-Type", "application/pdf")
	w.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s-%s.pdf\"", payload.ContractNumber, payload.RO))
	w.WriteHeader(http.StatusOK)
	_, _ = w.Write(b.Bytes())
}
