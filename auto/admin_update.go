package auto

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"net/http"
	"phizz/db"
	"phizz/handlers"
	"phizz/types"
	"strings"

	"github.com/go-chi/chi"
	"github.com/jmoiron/sqlx"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
)

type acuPayload struct {
	ID                int             `json:"id"`
	PreviousStatus    string          `json:"previous_status"`
	NewStatus         string          `json:"new_status"`
	UpdateCheck       bool            `json:"update_check"`
	CheckNumber       string          `json:"check_number"`
	Date              types.JSPQDate  `json:"date"`
	Amount            decimal.Decimal `json:"amount"`
	Notes             string          `json:"notes"`
	PaymentAdjustment decimal.Decimal `json:"payment_adjustment"`
	ActualPaidAmount  decimal.Decimal `json:"actual_paid_amount"`
}

func getAcuPayload(req *http.Request) (*acuPayload, error) {
	claim := acuPayload{}
	dec := json.NewDecoder(req.Body)
	err := dec.Decode(&claim)
	if err != nil {
		return nil, errors.Wrap(err, "decoding claim request failed")
	}
	return &claim, nil
}

func (acu *acuPayload) clean() {
	acu.PreviousStatus = strings.TrimSpace(acu.PreviousStatus)
	acu.NewStatus = strings.TrimSpace(acu.NewStatus)
	acu.Notes = strings.TrimSpace(acu.Notes)
}

func (acu *acuPayload) validate() error {
	if acu.PreviousStatus == "" {
		return errors.New("previous_status is required")
	}

	switch acu.PreviousStatus {
	case db.AutoClaimStatusWaitingForCheck, db.AutoClaimStatusInvoiceSent, db.AutoClaimStatusWaitingForReversed,
		db.AutoClaimStatusCheckWritten, db.AutoClaimStatusCCPaid:
	default: // any other previous status is invalid
		return errors.New("previous_status is invalid")
	}

	if acu.NewStatus == "" {
		return errors.New("new_status is required")
	}

	switch acu.PreviousStatus {
	case db.AutoClaimStatusInvoiceSent:
		if acu.NewStatus != db.AutoClaimStatusCCPaid {
			return errors.New("new_status is invalid")
		}
	case db.AutoClaimStatusWaitingForCheck:
		if acu.NewStatus != db.AutoClaimStatusCheckWritten {
			return errors.New("new_status is invalid")
		}
	case db.AutoClaimStatusWaitingForReversed:
		if acu.NewStatus != db.AutoClaimStatusCheckWritten {
			return errors.New("new_status is invalid")
		}
	case db.AutoClaimStatusCheckWritten:
		if acu.NewStatus != db.AutoClaimStatusCheckWritten {
			return errors.New("new_status is invalid")
		}
	case db.AutoClaimStatusCCPaid:
		if acu.NewStatus != db.AutoClaimStatusCCPaid {
			return errors.New("new_status is invalid")
		}
	}

	if acu.Date.Time.IsZero() {
		return errors.New("date is required")
	}

	if (acu.PreviousStatus == db.AutoClaimStatusWaitingForCheck && acu.NewStatus == db.AutoClaimStatusCheckWritten && acu.CheckNumber == "") ||
		(acu.PreviousStatus == db.AutoClaimStatusCheckWritten && acu.UpdateCheck && acu.CheckNumber == "") {
		return errors.New("check_number is required")
	}

	return nil
}

// AdminClaimUpdate updates the claim status which are submitted to intacct
func AdminClaimUpdate(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {

	ctx := req.Context()
	claimIDStr := chi.URLParam(req, "id")

	var claimID int
	err := db.Get().GetContext(ctx, &claimID, `select id from automotive_claims where id = $1`, claimIDStr)
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage(err, "Claim not found", nil)
		}
		handlers.ReportError(req, errors.Wrap(err, "getting claim id failed"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting claim", nil)
	}

	claim, err := getAcuPayload(req)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "decoding claim request failed"))
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Decoding claim request failed", nil)

	}

	claim.clean()
	err = claim.validate()
	if err != nil {
		err = errors.Wrap(err, "claim validation failed")
		handlers.ReportError(req, err)
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Claim validation failed", nil)

	}

	switch claim.PreviousStatus {
	case db.AutoClaimStatusWaitingForCheck:
		err = updateAdminClaimWaitingForCheck(ctx, *claim, user.ID)

	case db.AutoClaimStatusWaitingForReversed:
		err = updateAdminClaimWaitingForReversed(ctx, *claim, user.ID)

	case db.AutoClaimStatusCheckWritten:
		err = updateAdminClaimCheckWritten(ctx, *claim, user.ID)

	case db.AutoClaimStatusCCPaid:
		err = updateAdminClaimCCPaid(ctx, *claim, user.ID)
	}

	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "updating claim status failed"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Updating claim status failed", nil)
	}

	return http.StatusOK, map[string]interface{}{
		"status": "success",
	}
}

func addAdminUpdateNote(ctx context.Context, tx *sqlx.Tx, claim acuPayload, userID int) error {
	note := RecordNotePayload{}
	note.AutomotiveClaimID = claim.ID

	note.CreatedByUserID = userID
	if claim.Notes == "" {
		note.IsManual = false
		if claim.NewStatus == db.AutoClaimStatusCheckWritten {
			note.NotesText = fmt.Sprintf(`Claim status changed from %s to %s Check number:%s, Date:%s, Amount:%s.
			Payment Adjustment:%s Actual Paid Amount:%s Updated`,
				claim.PreviousStatus, claim.NewStatus, claim.CheckNumber, claim.Date.Time.Format("01-02-2006"), claim.Amount.StringFixed(2),
				claim.PaymentAdjustment.StringFixed(2), claim.ActualPaidAmount.StringFixed(2))
		} else {
			note.NotesText = fmt.Sprintf(`Payment Adjustment:%s Actual Paid Amount:%s Updated`,
				claim.PaymentAdjustment.StringFixed(2), claim.ActualPaidAmount.StringFixed(2))
		}
	} else {
		note.IsManual = true
		note.NotesText = claim.Notes
	}
	_, err := InsertRecordNote(ctx, &note, tx)
	if err != nil {
		_ = tx.Rollback()
		return errors.Wrap(err, "error inserting recordNote")
	}
	return nil
}

func updateAdminClaimWaitingForReversed(ctx context.Context, claim acuPayload, userID int) error {

	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		return errors.Wrap(err, "starting transaction failed")
	}

	query := `insert into automotive_claim_payment_checks(
	    automotive_claim_payments_id,
		check_amount,
		check_number,
		paid_date,
		updated_at)
	    values (
	    (select id from automotive_claim_payments where automotive_claim_id =$1 order by id desc limit 1),
	        $2,
			$3,
			$4,
			now() at time zone 'utc');`

	_, err = tx.ExecContext(ctx, query, claim.ID, claim.Amount.Mul(decimal.NewFromFloat(-1)), claim.CheckNumber, claim.Date)
	if err != nil {
		_ = tx.Rollback()
		return errors.Wrap(err, "admin claim update: insert into automotive_claim_payment_checks failed")
	}

	err = updateAdminAutoClaim(ctx, tx, claim)
	if err != nil {
		_ = tx.Rollback()
		return errors.Wrap(err, "admin claim update: update automotive_claims failed")
	}

	err = addAdminUpdateNote(ctx, tx, claim, userID)
	if err != nil {
		_ = tx.Rollback()
		return errors.Wrap(err, "adding note failed")
	}
	err = tx.Commit()
	if err != nil {
		return errors.Wrap(err, "committing transaction failed")
	}
	return nil
}

func updateAdminClaimCCPaid(ctx context.Context, claim acuPayload, userID int) error {

	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		return errors.Wrap(err, "starting transaction failed")
	}
	defer tx.Rollback()

	err = updateAdminAutoClaim(ctx, tx, claim)
	if err != nil {
		_ = tx.Rollback()
		return errors.Wrap(err, "updating auto claims from admin update failed")
	}

	err = addAdminUpdateNote(ctx, tx, claim, userID)
	if err != nil {
		_ = tx.Rollback()
		return errors.Wrap(err, "adding note failed")
	}

	err = tx.Commit()
	if err != nil {
		return errors.Wrap(err, "committing transaction failed")
	}
	return nil
}

func updateAdminClaimWaitingForCheck(ctx context.Context, claim acuPayload, userID int) error {
	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		return errors.Wrap(err, "starting transaction failed")
	}

	query := `insert into automotive_claim_payment_checks(
	    automotive_claim_payments_id,
		check_amount,
		check_number,
		paid_date,
		updated_at)
	    values (
	    (select id from automotive_claim_payments where automotive_claim_id =$1 order by id desc limit 1),
	        $2,
			$3,
			$4,
			now() at time zone 'utc');`

	_, err = tx.ExecContext(ctx, query, claim.ID, claim.Amount, claim.CheckNumber, claim.Date)
	if err != nil {
		_ = tx.Rollback()
		return errors.Wrap(err, "updating claim status from waitingForCheck to checkWritten failed")
	}

	err = updateAdminAutoClaim(ctx, tx, claim)
	if err != nil {
		_ = tx.Rollback()
		return errors.Wrap(err, "updating auto claims from admin update failed")
	}

	err = addAdminUpdateNote(ctx, tx, claim, userID)
	if err != nil {
		_ = tx.Rollback()
		return errors.Wrap(err, "adding note failed")
	}

	err = tx.Commit()
	if err != nil {
		return errors.Wrap(err, "committing transaction failed")
	}
	return nil
}

func updateAdminClaimCheckWritten(ctx context.Context, claim acuPayload, userID int) error {
	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		return errors.Wrap(err, "starting transaction failed")
	}

	if claim.UpdateCheck {
		query := `update automotive_claim_payment_checks
                set check_amount = $1,
                check_number = $2,
                paid_date = $3,
                updated_at = now() at time zone 'utc'
                where id = (select id from automotive_claim_payment_checks where automotive_claim_payments_id in (select id
                        from automotive_claim_payments
                        where automotive_claim_id =$4
                        order by id desc limit 1) order by id desc limit 1);`

		_, err = tx.ExecContext(ctx, query, claim.Amount, claim.CheckNumber, claim.Date, claim.ID)
		if err != nil {
			_ = tx.Rollback()
			return errors.Wrap(err, "updating check data failed")
		}
	}

	err = updateAdminAutoClaim(ctx, tx, claim)
	if err != nil {
		_ = tx.Rollback()
		return errors.Wrap(err, "updating auto claims from admin update failed")
	}

	err = addAdminUpdateNote(ctx, tx, claim, userID)
	if err != nil {
		_ = tx.Rollback()
		return errors.Wrap(err, "adding note failed")
	}

	err = tx.Commit()
	if err != nil {
		return errors.Wrap(err, "committing transaction failed")
	}
	return nil
}

func updateAdminAutoClaim(ctx context.Context, tx *sqlx.Tx, claim acuPayload) error {
	query := `update automotive_claims
	set status = $1,
	payment_adjustment = $2,
	actual_paid_amount = $3
	where id = $4`
	_, err := tx.ExecContext(ctx, query, claim.NewStatus, claim.PaymentAdjustment, claim.ActualPaidAmount, claim.ID)
	if err != nil {
		_ = tx.Rollback()
		return errors.Wrap(err, "admin claim update: update automotive_claims failed")
	}
	return nil
}
