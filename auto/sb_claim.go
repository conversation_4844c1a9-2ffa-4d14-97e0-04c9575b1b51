package auto

import (
	"context"
	"log"
	"strconv"
	"time"

	"phizz/db"

	"github.com/pkg/errors"
)

// SubmitSBClaims submits the maintenance SB claims to intacct
// It finds all the facilities for which there are approved claims
// Invokes submitSBClaims for each facility, which submits one bill for each facility
func SubmitSBClaims(ctx context.Context) error {
	var facilities []int
	// get all the facilities for which bill/batch need to be submitted
	facilityQ := `select distinct(facility_id) from automotive_claims where status = $1 and pay_type = $2 and claim_type = $3 and product_code = $4`
	err := db.Get().Select(&facilities, facilityQ, db.AutoClaimStatusApproved, db.PayTypeStore, db.ClaimTypeSB, db.ProductCodeMaintenance)
	if err != nil {
		return errors.Wrap(err, "Error in SubmitSBClaims getting distinct facilities")
	}
	if len(facilities) == 0 {
		log.Println("No claims to add in SubmitSBClaims batch")
		return nil
	}

	for _, facilityID := range facilities {
		err = submitSBClaims(ctx, facilityID)
		if err != nil {
			return errors.Wrap(err, "Error in SubmitSBClaims for facility"+strconv.Itoa(facilityID))
		}
	}
	return nil
}

// submitSBClaims, submits SB claims of a given facility into a single intacct bill
func submitSBClaims(ctx context.Context, facilityID int) error {
	//get claimIDs for each facility
	var claimIDs []int
	claimsQ := `select id from automotive_claims where facility_id = $1 and status = $2 and pay_type = $3 and claim_type = $4 and product_code = $5`
	err := db.Get().SelectContext(ctx, &claimIDs, claimsQ, facilityID, db.AutoClaimStatusApproved, db.PayTypeStore, db.ClaimTypeSB, db.ProductCodeMaintenance)
	if err != nil {
		return errors.Wrap(err, "error in getting claim list for facilityID"+strconv.Itoa(facilityID))
	}

	intacctAccountDetails, err := getIntacctAccountDetail(db.ProductCodeMaintenance)
	if err != nil {
		return errors.Wrap(err, "error in getting intacct details for auto maintenance batch")
	}

	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		return errors.Wrap(err, "Database error beginning transaction for auto claim authorization in batchA")
	}

	batch := BatchData{}
	batch.BatchID, err = CreateIntacctBatch(ctx, tx)
	if err != nil {
		_ = tx.Rollback()
		return errors.Wrap(err, "Database error creating auto claim batch in batchA")
	}

	batch.VendorID, err = FacilityVendorID(ctx, facilityID)
	if err != nil || batch.VendorID == "" {
		return errors.Wrap(err, "error getting vendorID for facility "+strconv.Itoa(facilityID))
	}
	batch.FacilityCode, err = facilityCode(facilityID)
	if err != nil || batch.FacilityCode == "" {
		return errors.Wrap(err, "error getting facilityCode for facility "+strconv.Itoa(facilityID))
	}
	batch.ProjectID = intacctAccountDetails.ProjectID
	batch.AccountLabel = intacctAccountDetails.AccountLabel
	batch.ProductCode = db.ProductCodeMaintenance

	var batchClaims []*ClaimPayload // claim list to be submitted to INTACCT
	// Validate claims and add to claims to batch
	for _, id := range claimIDs {
		claim, err := ClaimSelect(nil, nil, strconv.Itoa(id))
		if err != nil {
			_ = tx.Rollback()
			return errors.Wrap(err, "Error getting auto claim from database for ID:"+strconv.Itoa(id))
		}

		err = AddClaimToBatch(ctx, tx, id, batch.BatchID, claim.Estimate)
		if err != nil {
			_ = tx.Rollback()
			return errors.Wrap(err, "Error adding auto claim to batch:"+strconv.Itoa(id))
		}
		log.Printf("Adding claim for contract# %s in batch", claim.ContractNumber)
		batchClaims = append(batchClaims, claim)
	}

	err = tx.Commit()
	if err != nil {
		return errors.Wrap(err, "Database error committing transaction for auto claim authorization")
	}

	// Get facility user
	userID := 0
	err = db.Get().GetContext(ctx, &userID, "select id from users where first_name = $1 limit 1", db.FacilityUserName)
	if err != nil {
		return errors.Wrap(err, "Database error while getting facility user")
	}

	// Authorize maintenance claims as one bill
	_, batchErrors, err := claimMaintenanceBatchAuthorize(ctx, nil, batchClaims, &batch, userID, intacctAccountDetails)
	if err != nil {
		for _, claimID := range claimIDs {
			recordNote := RecordNotePayload{
				AutomotiveClaimID: claimID,
				CreatedByUserID:   userID,
				CreatedAt:         time.Now(),
				NotesText:         "INTACCT_BATCH: Submit to Intacct failed in batch:" + strconv.Itoa(batch.BatchID),
			}
			_, err = InsertRecordNote(ctx, &recordNote, nil)
			if err != nil {
				log.Println("error in adding notes for claimID", claimID)
			}
		}
		// batchErrors is always of length 1, hence sending the first element to log
		log.Printf("Error in claimMaintenanceBatchAuthorize for batch# %d -> %s", batch.BatchID, batchErrors[0])
		return errors.Wrap(err, "Failed to authorize maintenance batch with ID: "+strconv.Itoa(batch.BatchID))
	}

	return nil
}
