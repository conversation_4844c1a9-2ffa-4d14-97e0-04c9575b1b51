package auto

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"

	"phizz/conf"
	"phizz/db"
	"phizz/email"
	"phizz/handlers"
	"phizz/util"

	"github.com/jmoiron/sqlx"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
)

// ReconciliationsIndex returns list of reconciliationPayload which is required in Reconciliation Page
func ReconciliationsIndex(_ http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	return reconciliations(req, false)
}

func reconciliations(req *http.Request, isReconciled bool) (int, map[string]interface{}) {
	whereSlice := []string{
		fmt.Sprintf(` where claim.is_reconciled = %s and claim.status = 'CCPaid' `, strconv.FormatBool(isReconciled)),
	}
	args := struct {
		SearchQuery string `db:"search_query"`
		Amount      int    `db:"amount"`
	}{}

	nameContractVENAmount := req.FormValue("q")

	if nameContractVENAmount != "" {
		searchWhereClauses := ` (facility.name ilike :search_query or claim.contract_number ilike :search_query or facility.vendor_id ilike :search_query `
		args.SearchQuery = "%" + nameContractVENAmount + "%"
		amount, err := strconv.Atoi(nameContractVENAmount)
		if err == nil {
			searchWhereClauses = searchWhereClauses + ` or claim.estimate = :amount `
			args.Amount = amount
		}
		searchWhereClauses += `) `
		whereSlice = append(whereSlice, searchWhereClauses)
	}

	// parameters - sort by and sort order
	orderBy := req.FormValue("sort_by")
	if orderBy != "" {
		sortOrder := req.FormValue("sort_order")
		if sortOrder != "" && strings.ToUpper(sortOrder) == "ASC" || strings.ToUpper(sortOrder) == "DESC" {
			orderBy = fmt.Sprintf("%s %s", orderBy, sortOrder)
		}
	} else {
		// default order by value
		orderBy = " date_of_payment_received desc "
	}

	fromClause := ` from automotive_claims claim
		join automotive_claim_payments payment on claim.id = payment.automotive_claim_id
		join automotive_facilities facility on claim.facility_id = facility.id `

	// select query
	selectClause := `select
		claim.id as id,
		claim.estimate as estimate,
		claim.contract_number as contract_number,
		claim.ro as ro,
		case when payment.amount is null then 0.0 else payment.amount end as statement_amount,
		payment.paid_date as date_of_payment_received,
		facility.name as facility_name,
		facility.vendor_id as vendor_id `

	// sort order and sort by
	orderByClause := fmt.Sprintf(" order by %s ", orderBy)
	whereClause := strings.Join(whereSlice, " and ")

	selectQuery := selectClause + fromClause + whereClause + orderByClause
	countQuery := `select count(*) ` + fromClause + whereClause

	stmt, err := db.Get().PrepareNamed(selectQuery)
	if err != nil {
		err = errors.Wrap(err, "Database error preparing get reconciliations list query")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting reconciliations", nil)
	}
	defer func() { _ = stmt.Close() }()
	var reconciliations reconciliationsPayload
	err = stmt.Select(&reconciliations, args)
	if err != nil {
		err = errors.Wrap(err, "Database error getting reconciliations lists")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting reconciliations lists data", nil)
	}

	stmt2, err := db.Get().PrepareNamed(countQuery)
	if err != nil {
		err = errors.Wrap(err, "Database error preparing reconciliations count query")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting reconciliations count", nil)
	}
	defer func() { _ = stmt2.Close() }()
	count := 0
	err = stmt2.Get(&count, args)
	if err != nil {
		err = errors.Wrap(err, "Database error getting reconciliations lists count")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting reconciliations lists data", nil)
	}

	return http.StatusOK, map[string]interface{}{"reconciliations": reconciliations, "counts": count}
}

// ReconciliationsHistory returns history of reconciliation
func ReconciliationsHistory(_ http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	return reconciliations(req, true)
}

// ReconciliationsUpdate updates the list of statement_amount and is_reconciled
// for the list of reconciliations list only which records has been updated
func ReconciliationsUpdate(_ http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	ctx := req.Context()
	reqReconciliations, err := reconciliationsFromReq(req)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Malformed reconciliations data for update.", nil)
	}
	if len(reqReconciliations) == 0 {
		return http.StatusBadRequest, handlers.ErrorMessage(errors.New("Invalid Input"), "Malformed reconciliations data for update.", nil)
	}

	ids := reqReconciliations.ids()
	selectQuery := `select
		claim.id AS id,
		case when payment.amount is null then 0.0 else payment.amount end as statement_amount,
		case when claim.estimate is null then 0.0 else claim.estimate end as estimate,
		claim.is_reconciled as is_reconciled,
		claim.contract_number as contract_number,
		users.email as email,
		facility.name as facility_name
	from automotive_claims claim
		join automotive_claim_payments payment on claim.id = payment.automotive_claim_id
		join users on claim.owner_id = users.id
		join automotive_facilities facility on claim.facility_id = facility.id
	where claim.status = 'CCPaid' and claim.id in (?)`

	query, args, err := sqlx.In(selectQuery, ids)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, `error generating "in" query for reconciliations update`,
			nil)
	}
	query = db.Get().Rebind(query)

	var storedReconciliations reconciliationsPayload
	err = db.Get().SelectContext(ctx, &storedReconciliations, query, args...)
	if err != nil {
		err = errors.Wrap(err, "Database error getting reconciliations lists")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting reconciliations lists data",
			nil)
	}

	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		err = errors.Wrap(err, "Database error getting transaction in reconciliations lists")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting transaction in reconciliations lists data",
			nil)
	}
	for _, reconciliation := range storedReconciliations {
		reqReconciliation := reqReconciliations.find(reconciliation.ID)
		if reqReconciliation == nil {
			_ = tx.Rollback()
			return http.StatusNotFound, handlers.ErrorMessage(errors.New("Requested claim not found"),
				"Error getting reconciliations lists data", nil)
		}

		if reqReconciliation.StatementAmount != reconciliation.StatementAmount || reqReconciliation.IsReconciled != reconciliation.IsReconciled {
			// Update requested reconciliation
			err = updateClaimWithReconciliationData(ctx, reqReconciliation, user.ID, tx)
			if err != nil {
				_ = tx.Rollback()
				err = errors.Wrap(err, "Database error getting reconciliations lists")
				return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting reconciliations lists data", nil)
			}
			// if claims is reconciled and its amount is more than cc threshold limit, then send notification to agent
			if reqReconciliation.IsReconciled &&
				reqReconciliation.StatementAmount.Sub(reconciliation.Amount).
					GreaterThanOrEqual(decimal.NewFromFloat(conf.Get().AutoClaims.CCThreshold)) {
				// send mail to agent
				err = sendMail(reconciliation, reqReconciliation.StatementAmount)
				if err != nil {
					_ = tx.Rollback()
					err = errors.Wrap(err, "Error in sending notification mail to agent")
					return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting reconciliations lists data", nil)
				}
			}
		}
	}
	err = tx.Commit()
	if err != nil {
		err = errors.Wrap(err, "Database error committing transaction for updating Auto claim for reconciliation request")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error updating Auto claim for reconciliation request", nil)
	}
	return http.StatusOK, map[string]interface{}{"ids": ids}

}

// sendMail will send email to agent if claims has been overcharged
func sendMail(recon reconciliationPayload, statementAmount decimal.Decimal) error {
	tmplData := struct {
		ID              int
		URL             string
		ContractNumber  string
		FacilityName    string
		PreAuthAmount   decimal.Decimal
		StatementAmount decimal.Decimal
	}{
		ID:              recon.ID,
		URL:             conf.Get().AppURL, // get url from config
		PreAuthAmount:   recon.Amount,
		StatementAmount: statementAmount,
		ContractNumber:  recon.ContractNumber,
		FacilityName:    recon.FacilityName,
	}
	body, err := util.ParseTemplate("cc_agent_advise.tmpl", tmplData)
	if err != nil {
		return err
	}
	return email.SendHTMLSync(conf.Get().GapEmail.From, []string{recon.OwnerEmail}, "CC Notification", body)
}

// updateClaimWithReconciliationData update is_reconciled and amount of reconciliation data
func updateClaimWithReconciliationData(ctx context.Context, reconciliation *reconciliationPayload, updatedByUserID int, tx *sqlx.Tx) error {
	// update claim table
	updateClaimQuery := `update automotive_claims SET
		is_reconciled = :is_reconciled
		where id = :id`
	claimStmt, err := tx.PrepareNamedContext(ctx, updateClaimQuery)
	if err != nil {
		return errors.Wrap(err, "Error updating reconciliation claim details, PrepareNamed failed")
	}
	defer func() { _ = claimStmt.Close() }()
	_, err = claimStmt.ExecContext(ctx, reconciliation)
	if err != nil {
		return errors.Wrap(err, "Error reconciliation claim details, database error")
	}

	// update payment table
	updatePaymentQuery := `update automotive_claim_payments set amount = :statement_amount where automotive_claim_id = :id`
	paymentStmt, err := tx.PrepareNamedContext(ctx, updatePaymentQuery)
	if err != nil {
		return errors.Wrap(err, "Error updating reconciliation payment details, PrepareNamed failed")
	}
	defer func() { _ = paymentStmt.Close() }()
	_, err = paymentStmt.ExecContext(ctx, reconciliation)
	if err != nil {
		return errors.Wrap(err, "Error reconciliation payment details, database error")
	}

	err = ClaimUpdated(ctx, tx, reconciliation.ID, updatedByUserID)
	if err != nil {
		return errors.Wrap(err, "Error inserting automotive_claim_updates")
	}
	return nil
}

// reconciliationsFromReq will convert request into Reconciliations payload
func reconciliationsFromReq(req *http.Request) (reconciliationsPayload, error) {
	var reconciliations reconciliationsPayload
	dec := json.NewDecoder(req.Body)
	err := dec.Decode(&reconciliations)
	return reconciliations, errors.Wrap(err, "decoding reconciliations request failed")
}
