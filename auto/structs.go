package auto

import (
	"database/sql"
	"time"

	"github.com/shopspring/decimal"
)

// reconciliationPayload includes list of properties to be displayed in Reconciliation Page
type reconciliationPayload struct {
	ID                    int             `json:"id" db:"id"`
	Amount                decimal.Decimal `json:"estimate" db:"estimate"`
	DateOfPaymentReceived time.Time       `json:"date_of_payment_received" db:"date_of_payment_received"`
	VendorID              sql.NullString  `json:"vendor_id" db:"vendor_id"`
	FacilityName          string          `json:"facility_name" db:"facility_name"`
	ContractNumber        string          `json:"contract_number" db:"contract_number"`
	RO                    string          `json:"ro" db:"ro"`
	StatementAmount       decimal.Decimal `json:"statement_amount" db:"statement_amount"`
	IsReconciled          bool            `json:"is_reconciled" db:"is_reconciled"`
	OwnerEmail            string          `json:"-" db:"email"`
}

// Reconciliations is a Slice of reconciliationPayload
type reconciliationsPayload []reconciliationPayload

// find reconciliationPayload from list of Reconciliation list
func (reconciliations reconciliationsPayload) find(ID int) *reconciliationPayload {
	for _, reconciliation := range reconciliations {
		if reconciliation.ID == ID {
			return &reconciliation
		}
	}
	return nil
}

// ids will return array of ids of Reconciliations
func (reconciliations reconciliationsPayload) ids() []int {
	var ids []int
	for _, reconciliation := range reconciliations {
		ids = append(ids, reconciliation.ID)
	}
	return ids
}

type payeePayload struct {
	ID         int    `json:"id" db:"id"`
	Name       string `json:"name" db:"name"`
	Address    string `json:"address" db:"address"`
	City       string `json:"city" db:"city"`
	State      string `json:"state" db:"state"`
	PostalCode string `json:"postal_code" db:"postal_code"`
	VendorID   string `json:"vendor_id" db:"vendor_id"`
}
