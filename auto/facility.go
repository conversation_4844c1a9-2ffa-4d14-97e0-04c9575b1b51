package auto

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/csv"
	"encoding/json"
	"fmt"
	"net/http"
	"phizz/conf"
	"phizz/db"
	"phizz/email"
	"phizz/handlers"
	"phizz/intacct"
	"phizz/types"
	"strconv"
	"strings"
	"time"

	"github.com/go-chi/chi"
	"github.com/jmoiron/sqlx"
	"github.com/lib/pq"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
	null "gopkg.in/guregu/null.v3"
)

const (
	// FacilitySubroutine unidata subroutine
	FacilitySubroutine = "/LINSC/subroutine/REST.GET.REPFAC"
)

const (
	//PartsTax is constant for Parts Tax
	PartsTax = "PT"
	//LaborRate is constant for Labor Rate
	LaborRate = "LR"
	//LaborTax is constant for Labor Tax
	LaborTax = "LT"
)

type effectiveRate struct {
	ID              int                `db:"id" json:"id"`
	FacilityID      int                `db:"facility_id" json:"facility_id"`
	FacilityCode    string             `db:"facility_code" json:"facility_code"`
	EffectiveRate   decimal.Decimal    `db:"effective_rate" json:"effective_rate"`
	EffectiveDate   types.JSPQDate     `db:"effective_date" json:"effective_date"`
	EndDate         types.JSPQNullDate `db:"end_date" json:"end_date"`
	RateType        string             `db:"rate_type" json:"rate_type"`
	Active          bool               `db:"active" json:"active"`
	CreatedAt       null.Time          `db:"created_at" json:"created_at"`
	CreatedByUserID null.Int           `db:"created_by_user_id" json:"created_by_user_id"`
	CreatedBy       string             `db:"created_by" json:"created_by"`
	IsNew           bool               `db:"-" json:"is_new"`
}

// FacilityCsvRow holds a csv record
type FacilityCsvRow struct {
	ID        int64     `json:"facility_id" db:"facility_id"`
	Code      string    `json:"facility_code" db:"facility_code"`
	NotesText string    `json:"notes_text" db:"notes_text"`
	UpdatedAt time.Time `json:"updated_at" db:"updated_at"`
	UpdatedBy string    `json:"updated_by" db:"updated_by"`
}

// ToSlice creates a csv record
func (facility *FacilityCsvRow) ToSlice() []string {
	result := make([]string, 5)
	result[0] = strconv.FormatInt(facility.ID, 10)
	result[1] = facility.Code
	result[2] = facility.NotesText
	result[3] = facility.UpdatedAt.Format("2006-01-02")
	result[4] = facility.UpdatedBy
	return result
}

func getConfig() (emailFromID string, facilityManager []string) {
	emailFromID = conf.Get().AutoEmail.From
	facilityManager = conf.Get().AutoEmail.FacilityManager
	return
}

var sendHTMLSync func(string, []string, string, []byte) error
var getConfigDetails func() (string, []string)

func init() {
	sendHTMLSync = email.SendHTMLSync
	getConfigDetails = getConfig
}

// FacilityIndex returns a list of stores
// Search query can be Name, State Code or Zip Code
func FacilityIndex(_ http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {

	var facilities []struct {
		ID          int             `json:"id" db:"id"`
		Code        string          `json:"facility_code" db:"facility_code"`
		Name        string          `json:"name" db:"name"`
		City        string          `json:"city" db:"city"`
		State       string          `json:"state_code" db:"state_code"`
		Country     string          `json:"country" db:"country"`
		Zip         string          `json:"postal_code" db:"postal_code"`
		IsActive    bool            `json:"is_active" db:"is_active"`
		PreAuthFlag sql.NullBool    `json:"pre_auth" db:"pre_auth"`
		LaborRate   sql.NullFloat64 `json:"labor_rate" db:"labor_rate"`
		UpdatedAt   pq.NullTime     `json:"updated_at" db:"updated_at"`
	}

	args := struct {
		SearchQuery string `db:"search_query"`
		Name        string `db:"name"`
		PostalCode  string `db:"postal_code"`
		Phone       string `db:"phone"`
	}{}

	selectClause := `id, facility_code, name, city, state_code, postal_code, country, pre_auth, labor_rate, updated_at, is_active`
	whereClause := "where name ilike :search_query or postal_code ilike :search_query or phone ilike :search_query or facility_code ilike :search_query"
	fromClause := "automotive_facilities"

	queryString := req.FormValue("search")
	if queryString == "" {
		name := req.FormValue("name")
		zip := req.FormValue("zip")
		phone := req.FormValue("phone")

		if name == "" || zip == "" || phone == "" {
			return http.StatusBadRequest, handlers.ErrorMessage(nil, "Invalid Parameters", nil)
		}
		args.Name = name
		args.Phone = phone
		args.PostalCode = zip
		whereClause = "where name = :name and postal_code = :postal_code and phone = :phone"
	}
	args.SearchQuery = "%" + queryString + "%"
	countQuery := "select count(*) from " + fromClause + " " + whereClause

	//sort facilities by name
	sortByName := req.FormValue("sortByName")
	orderBy := ""

	if sortByName == "desc" || sortByName == "asc" {
		orderBy = "name " + sortByName
	}

	if orderBy != "" {
		orderBy = " order by " + orderBy
	}

	// handle pagination
	p := req.FormValue("page")
	var listQuery string
	if n, err := strconv.Atoi(p); err == nil && n > 0 {
		listQuery = fmt.Sprintf("select %s from %s %s %s limit %d offset %d", selectClause, fromClause, whereClause, orderBy, handlers.PerPageEntries, (n-1)*handlers.PerPageEntries)
	} else { // if page number is not provided, return all values
		listQuery = fmt.Sprintf("select %s from %s %s %s", selectClause, fromClause, whereClause, orderBy)
	}

	stmt, err := db.Get().PrepareNamed(listQuery)
	if err != nil {
		err = errors.Wrap(err, "Database error preparing get facility list query")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting claims", nil)
	}
	defer func() { _ = stmt.Close() }()
	err = stmt.Select(&facilities, args)
	if err != nil {
		err = errors.Wrap(err, "Database error getting Auto facility lists")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting Auto claims lists data", nil)
	}

	stmt2, err := db.Get().PrepareNamed(countQuery)
	if err != nil {
		err = errors.Wrap(err, "Database error preparing automotive facility count query")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting claims count", nil)
	}
	defer func() { _ = stmt2.Close() }()
	count := 0
	err = stmt2.Get(&count, args)
	if err != nil {
		err = errors.Wrap(err, "Database error getting Auto claims lists count")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting Auto claims lists data", nil)
	}

	return http.StatusOK, map[string]interface{}{"facilities": facilities, "count": count}
}

// FacilityCsv returns list of facilities
func FacilityCsv(_ http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	ctx := req.Context()

	beginDate := req.FormValue("begin_date")
	if beginDate == "" {
		return http.StatusBadRequest, handlers.ErrorMessage(nil, "Malformed begin_date is empty.", nil)
	}

	endDate := req.FormValue("end_date")
	if endDate == "" {
		return http.StatusBadRequest, handlers.ErrorMessage(nil, "Malformed end_date is empty.", nil)
	}

	facilities := []FacilityCsvRow{}

	query := `select afrn.facility_id , af.facility_code, afrn.notes_text, 
	DATE(afrn.created_at) updated_at, u.first_name || ' ' || u.last_name updated_by
	from automotive_facility_record_notes afrn
			 join users u on afrn.created_by_user_id = u.id
			 join automotive_facilities af on afrn.facility_id = af.id
	where afrn.created_at >= $1 and afrn.created_at <= $2
	order by afrn.created_at desc`

	err := db.Get().SelectContext(ctx, &facilities, query, beginDate, endDate)
	if err != nil {
		err = errors.Wrap(err, "error getting facilities")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error getting facilities", nil)
	}

	var csv string
	csv = convertToCSV(facilities)
	return http.StatusOK, map[string]interface{}{"facilities_csv": csv}
}

func convertToCSV(facilities []FacilityCsvRow) string {
	result := new(bytes.Buffer)
	w := csv.NewWriter(result)
	w.Write([]string{"Facility Id", "Facility Code", "Notes Text", "Updated At", "Updated By"})
	for _, entry := range facilities {
		w.Write(entry.ToSlice())
	}
	w.Flush()
	return result.String()
}

// FacilityShow returns a details of facility
// Search query is code of the facility
func FacilityShow(_ http.ResponseWriter, req *http.Request, _ db.User) (int, map[string]interface{}) {
	ctx := req.Context()
	whereClause := ""
	searchParams := struct {
		ID   int    `db:"id"`
		Code string `db:"code"`
	}{}

	reqQuery := chi.URLParam(req, "id")
	if reqQuery != "" {
		searchParams.ID, _ = strconv.Atoi(reqQuery)
		searchParams.Code = strings.ToUpper(reqQuery)
		whereClause = `id = :id or facility_code = :code`
	}

	if whereClause == "" {
		return http.StatusBadRequest, handlers.ErrorMessage(nil, "Invalid Parameters", nil)
	}

	var facility struct {
		ID                 int             `json:"id" db:"id"`
		FacilityCode       string          `json:"facility_code" db:"facility_code"`
		Name               string          `json:"name" db:"name"`
		Address            sql.NullString  `json:"address" db:"address"`
		City               string          `json:"city" db:"city"`
		State              string          `json:"state_code" db:"state_code"`
		Zip                string          `json:"postal_code" db:"postal_code"`
		Country            string          `json:"country" db:"country"`
		VendorID           sql.NullString  `json:"vendor_id" db:"vendor_id"`
		Phone              sql.NullString  `json:"phone" db:"phone"`
		Fax                sql.NullString  `json:"fax" db:"fax"`
		Email              sql.NullString  `json:"email" db:"email"`
		Contact            sql.NullString  `json:"contact" db:"contact"`
		PartsTax           []effectiveRate `json:"parts_tax" db:"-"`
		LaborTax           []effectiveRate `json:"labor_tax" db:"-"`
		LaborRate          []effectiveRate `json:"labor_rate" db:"-"`
		PreAuthorized      sql.NullBool    `json:"pre_auth" db:"pre_auth"`
		PreAuthorizedLimit sql.NullFloat64 `json:"pre_autho_limit" db:"pre_auth_limit"`
		StoreID            null.Int        `json:"store_id" db:"store_id"`
		EIN                string          `json:"ein" db:"ein"`
		IsActive           bool            `json:"is_active" db:"is_active"`
		TaxType            string          `json:"tax_type" db:"tax_type"`
		PaymentType        string          `json:"payment_type" db:"payment_type"`
	}

	query := `select * from automotive_facilities where ` + whereClause
	stmt, err := db.Get().Unsafe().PrepareNamedContext(ctx, query)
	if err != nil {
		err = errors.Wrap(err, "Database error preparing get claims list query")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting claims", nil)
	}
	defer func() { _ = stmt.Close() }()

	err = stmt.Unsafe().GetContext(ctx, &facility, searchParams)
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage(err, "The facility was not found", nil)
		}
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error while loading facility", nil)
	}

	query = `select afer.*, concat(u.first_name, ' ', u.last_name) created_by
			from automotive_facilities_effective_rates afer
				left join users u on afer.created_by_user_id = u.id
			where facility_id = $1 
			  and rate_type = $2 
			order by effective_date desc, id desc`
	err = db.Get().Unsafe().SelectContext(ctx, &facility.LaborTax, query, facility.ID, LaborTax)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error getting labor tax"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error while loading facility", nil)
	}

	err = db.Get().Unsafe().SelectContext(ctx, &facility.LaborRate, query, facility.ID, LaborRate)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error getting labor rate"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error while loading facility", nil)
	}

	err = db.Get().Unsafe().SelectContext(ctx, &facility.PartsTax, query, facility.ID, PartsTax)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error getting part tax"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error while loading facility", nil)
	}

	laborRate, err := getExistingActiveLaborRate(ctx, facility.ID)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error getting part tax"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error while loading facility", nil)
	}
	laborTax, err := getExistingActiveLaborTax(ctx, facility.ID)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error getting part tax"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error while loading facility", nil)
	}
	partsTax, err := getExistingActivePartsTax(ctx, facility.ID)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error getting part tax"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error while loading facility", nil)
	}

	for i, v := range facility.LaborRate {
		if v.ID == laborRate.ID {
			facility.LaborRate[i].Active = true
			continue
		}
		facility.LaborRate[i].Active = false
	}

	for i, v := range facility.LaborTax {
		if v.ID == laborTax.ID {
			facility.LaborTax[i].Active = true
			continue
		}
		facility.LaborTax[i].Active = false
	}

	for i, v := range facility.PartsTax {
		if v.ID == partsTax.ID {
			facility.PartsTax[i].Active = true
			continue
		}
		facility.PartsTax[i].Active = false
	}

	return http.StatusOK, map[string]interface{}{"facility": facility}
}

type facilityPayload struct {
	ID              int             `json:"id" db:"id"`
	FacilityCode    string          `json:"facility_code" db:"facility_code"`
	CreatedByUserID int             `json:"created_by_user_id" db:"created_by_user_id"`
	Name            string          `json:"name" db:"name"`
	Address         string          `json:"address" db:"address"`
	City            string          `json:"city" db:"city"`
	StateCode       string          `json:"state_code" db:"state_code"`
	PostalCode      string          `json:"postal_code" db:"postal_code"`
	Country         string          `json:"country" db:"country"`
	Phone           string          `json:"phone" db:"phone"`
	Fax             string          `json:"fax" db:"fax"`
	Email           string          `json:"email" db:"email"`
	LaborRate       []effectiveRate `json:"labor_rate" db:"-"`
	PartsTax        []effectiveRate `json:"parts_tax" db:"-"`
	LaborTax        []effectiveRate `json:"labor_tax" db:"-"`
	VendorID        string          `json:"vendor_id" db:"vendor_id"`
	Contact         string          `json:"contact" db:"contact"`
	PaymentType     string          `json:"payment_type" db:"payment_type"`
	PreAuth         bool            `json:"pre_auth" db:"pre_auth"`
	PreAuthLimit    decimal.Decimal `json:"pre_auth_limit" db:"pre_auth_limit"`
	IsActive        bool            `json:"is_active" db:"is_active"`
	EIN             string          `json:"ein" db:"ein"`
	TaxType         string          `json:"tax_type" db:"tax_type"`
}

// FacilityCreate creates a new facility
func FacilityCreate(_ http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	ctx := req.Context()

	facility, err := facilityFromReq(req)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Malformed facility data for create.", nil)
	}

	facility.CreatedByUserID = user.ID

	cleanFacility(facility)
	formErrors, err := validateFacility(ctx, facility, true)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(errors.Wrap(err, "Error validating facility"),
			"An error occurred validating the form values.", nil)
	}

	if len(formErrors) > 0 {
		return http.StatusBadRequest, handlers.ErrorMessage(nil, "Form validations errors.",
			map[string]interface{}{"errors": formErrors})
	}

	// check if facility vendor id is in use
	if facility.VendorID != "" {
		exists, err := vendorExists(facility.VendorID)
		if err != nil {
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Could not verify if facility vendor id exists", nil)
		}
		if exists {
			return http.StatusBadRequest, handlers.ErrorMessage(errors.New("Facility vendor id already exist"), "facility vendor id already exists", nil)
		}
	}

	// check if facility code in use
	exists, err := facilityExists(facility.FacilityCode)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Could not verify if facility code exists", nil)
	}
	if exists {
		return http.StatusBadRequest, handlers.ErrorMessage(errors.New("Facility code already exist"), "facility code already exists", nil)
	}

	// check if facility details exists
	facilityID := 0
	err = db.Get().GetContext(ctx, &facilityID, `select id from automotive_facilities where name = $1 and postal_code = $2 and facility_code = $3 and state_code = $4`, facility.Name, facility.PostalCode, facility.Phone, facility.StateCode)
	if err != nil && err != sql.ErrNoRows {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error while loading facility", nil)
	}

	if facilityID != 0 {
		return http.StatusInternalServerError, handlers.ErrorMessage(errors.New("duplicate facility"), "facility already exists", nil)
	}

	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error beginning transaction for facility create", nil)
	}

	facility.FacilityCode = strings.ToUpper(facility.FacilityCode) // Facility code stores in upper case

	id, err := insertFacility(ctx, tx, facility)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error inserting facility", nil)
	}

	facility.ID = id
	err = insertEffectiveRates(ctx, tx, getNewEffectiveRates(facility, user.ID))
	if err != nil {
		_ = tx.Rollback()
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error inserting facility", nil)
	}

	// Add new facility note
	_, err = addFacilityNote(tx, "Added new facility.", id, user.ID)
	if err != nil {
		_ = tx.Rollback()
		return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Failed to add new facility note", nil)
	}

	facilityHTML := facilityFormatHTML(facility)
	emailFromID, facilityManager := getConfigDetails()
	err = sendHTMLSync(emailFromID, facilityManager, "Facility Added", facilityHTML.Bytes())
	if err != nil {
		_ = tx.Rollback()
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error in sending email", nil)
	}
	err = tx.Commit()
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Could not write facility to database", nil)
	}

	return http.StatusOK, map[string]interface{}{"facility_id": id}
}

func getNewEffectiveRates(facility *facilityPayload, userID int) []effectiveRate {
	var newEffectiveRates []effectiveRate
	for index, v := range facility.LaborRate {
		if v.IsNew {
			facility.LaborRate[index].FacilityCode = facility.FacilityCode
			facility.LaborRate[index].FacilityID = facility.ID
			facility.LaborRate[index].CreatedByUserID = null.IntFrom(int64(userID))
			newEffectiveRates = append(newEffectiveRates, facility.LaborRate[index])
		}
	}

	for index, v := range facility.LaborTax {
		if v.IsNew {
			facility.LaborTax[index].FacilityCode = facility.FacilityCode
			facility.LaborTax[index].FacilityID = facility.ID
			facility.LaborTax[index].CreatedByUserID = null.IntFrom(int64(userID))
			newEffectiveRates = append(newEffectiveRates, facility.LaborTax[index])
		}
	}

	for index, v := range facility.PartsTax {
		if v.IsNew {
			facility.PartsTax[index].FacilityCode = facility.FacilityCode
			facility.PartsTax[index].FacilityID = facility.ID
			facility.PartsTax[index].CreatedByUserID = null.IntFrom(int64(userID))
			newEffectiveRates = append(newEffectiveRates, facility.PartsTax[index])
		}
	}
	return newEffectiveRates
}

func getExistingActiveLaborRate(ctx context.Context, facilityID int) (effectiveRate, error) {
	var existingLaborRate effectiveRate
	query := `select * from automotive_facilities_effective_rates where facility_id = $1 and effective_date <= current_date and rate_type = $2 order by effective_date desc, id desc limit 1`
	err := db.Get().Unsafe().GetContext(ctx, &existingLaborRate, query, facilityID, LaborRate)
	if err != nil && err != sql.ErrNoRows {
		return existingLaborRate, errors.Wrap(err, "error getting existing error rates")
	}
	return existingLaborRate, nil
}

func getExistingActiveLaborTax(ctx context.Context, facilityID int) (effectiveRate, error) {
	var existingLaborTax effectiveRate
	query := `select * from automotive_facilities_effective_rates where facility_id = $1 and effective_date <= current_date and rate_type = $2 order by effective_date desc, id desc limit 1`
	err := db.Get().Unsafe().GetContext(ctx, &existingLaborTax, query, facilityID, LaborTax)
	if err != nil && err != sql.ErrNoRows {
		return existingLaborTax, errors.Wrap(err, "error getting existing error rates")
	}
	return existingLaborTax, nil
}

func getExistingActivePartsTax(ctx context.Context, facilityID int) (effectiveRate, error) {
	var existingPartsTax effectiveRate
	query := `select * from automotive_facilities_effective_rates where facility_id = $1 and effective_date <= current_date and rate_type = $2 order by effective_date desc, id desc limit 1`
	err := db.Get().Unsafe().GetContext(ctx, &existingPartsTax, query, facilityID, PartsTax)
	if err != nil && err != sql.ErrNoRows {
		return existingPartsTax, errors.Wrap(err, "error getting existing error rates")
	}
	return existingPartsTax, nil
}

func insertEffectiveRates(ctx context.Context, tx *sqlx.Tx, newRates []effectiveRate) error {
	var stmt *sqlx.NamedStmt
	defer func() {
		if stmt != nil {
			_ = stmt.Close()
		}
	}()
	var err error
	for _, v := range newRates {
		query := `insert into automotive_facilities_effective_rates
					(facility_code, facility_id, effective_rate, rate_type, effective_date, active, created_at, created_by_user_id)
					values(:facility_code, :facility_id, :effective_rate, :rate_type, :effective_date, :active, now() at time zone 'utc', :created_by_user_id)`

		stmt, err = tx.PrepareNamedContext(ctx, query)
		if err != nil {
			return errors.Wrap(err, "error occurred in PrepareNamed function while adding new effective rates for facility.")
		}
		_, err = stmt.ExecContext(ctx, v)
		if err != nil {
			return errors.Wrap(err, "error occurred while trying to add the new effective rates for facility to the database.")
		}
	}
	return nil
}

func insertFacility(ctx context.Context, tx *sqlx.Tx, facility *facilityPayload) (int, error) {
	insertQuery := `insert into automotive_facilities (
	facility_code,
	created_at,
	updated_at,
	created_by_user_id,
	name,
	address,
	city,
	state_code,
	postal_code,
	country,
	phone,
	fax,
	email,
	contact,
	payment_type,
	pre_auth,
	pre_auth_limit,
	ein,
	tax_type,
	vendor_id) values (
	:facility_code,
	now() at time zone 'utc',
	now() at time zone 'utc',
	:created_by_user_id,
	:name,
	:address,
	:city,
	:state_code,
	:postal_code,
	:country,
	:phone,
	:fax,
	:email,
	:contact,
	:payment_type,
	:pre_auth,
	:pre_auth_limit,
	:ein,
	:tax_type,
	:vendor_id) returning id`
	id := 0

	stmt, err := tx.PrepareNamedContext(ctx, insertQuery)
	if err != nil {
		_ = tx.Rollback()
		return id, errors.Wrap(err, "An error occurred in PrepareNamed function while adding facility.")
	}
	defer func() { _ = stmt.Close() }()

	err = stmt.GetContext(ctx, &id, facility)
	if err != nil {
		_ = tx.Rollback()
		return id, errors.Wrap(err, "An error occurred while trying to add the facility to the database.")
	}

	return id, err
}

func facilityFromReq(req *http.Request) (*facilityPayload, error) {
	facility := facilityPayload{}

	dec := json.NewDecoder(req.Body)
	err := dec.Decode(&facility)

	return &facility, errors.Wrap(err, "decoding facility request failed")
}

// cleanRecordNote cleans up leading and trailing white-space etc...
func cleanFacility(facility *facilityPayload) {
	facility.FacilityCode = strings.TrimSpace(facility.FacilityCode)
	facility.Name = strings.TrimSpace(facility.Name)
	facility.Address = strings.TrimSpace(facility.Address)
	facility.City = strings.TrimSpace(facility.City)
	facility.StateCode = strings.TrimSpace(facility.StateCode)
	facility.PostalCode = strings.TrimSpace(facility.PostalCode)
	facility.Country = strings.TrimSpace(facility.Country)
	facility.Phone = strings.TrimSpace(facility.Phone)
	facility.Fax = strings.TrimSpace(facility.Fax)
	facility.Email = strings.TrimSpace(facility.Email)
	facility.Contact = strings.TrimSpace(facility.Contact)
	facility.PaymentType = strings.TrimSpace(facility.PaymentType)
}

// validateFacility validates a NewClaim record for correctness
func validateFacility(ctx context.Context, facility *facilityPayload, isNewFacility bool) (map[string]string, error) {
	formErrors := map[string]string{}

	if len(facility.FacilityCode) < 5 || len(facility.FacilityCode) > 7 {
		formErrors["facility_code"] = "facility_code is invalid"
	}
	if facility.Name == "" {
		formErrors["name"] = "name is required"
	}
	if facility.Address == "" {
		formErrors["address"] = "address is required"
	}
	if facility.City == "" {
		formErrors["city"] = "city is required"
	}
	if facility.PostalCode == "" {
		formErrors["postal_code"] = "postal_code is required"
	}
	if facility.StateCode == "" {
		formErrors["state_code"] = "state_code is required"
	}
	if facility.Country == "" {
		formErrors["country"] = "country is required"
	}
	if facility.Phone == "" {
		formErrors["phone"] = "phone number is required"
	}
	if facility.Contact == "" && isNewFacility {
		formErrors["contact"] = "advisor is required"
	}
	if facility.PaymentType == "" && isNewFacility {
		formErrors["payment_type"] = "payment_type is required"
	}

	if len(facility.LaborRate) == 0 {
		formErrors["labor_rate"] = "labor rate is required"
	} else {
		for _, v := range facility.LaborRate {
			if v.IsNew && v.EffectiveRate.LessThanOrEqual(decimal.Zero) {
				formErrors["labor_rate"] = "labor rate is required"
			}
		}
	}

	if facility.PreAuth {
		if facility.PreAuthLimit.LessThanOrEqual(decimal.Zero) {
			formErrors["pre_auth_limit"] = "preauth limit is required"
		}
	}

	if facility.VendorID != "" {
		// Validate vendor id
		// get vendor query payload
		vendorQuery := intacct.VendorQueryPayload{}.
			VendorID(facility.VendorID)

		// get vendors from intacct
		vendors, err := intacct.GetVendors(ctx, vendorQuery)
		if err != nil || len(vendors) == 0 {
			formErrors["vendor_id"] = "Invalid vendor"
		}
		if len(vendors) > 1 {
			formErrors["vendor_id"] = "Multiple vendors found for given vendor id, please correct vendor id"
		}
	}

	return formErrors, nil
}

func facilityExists(code string) (bool, error) {
	//check if facility exist in phizz db
	facilityCount := 0
	err := db.Get().Get(&facilityCount, `select count(*) from automotive_facilities where facility_code=$1`, code)
	if err != nil {
		return false, err
	}
	if facilityCount == 0 {
		return false, nil
	}
	return true, nil
}

func vendorExists(vendorID string) (bool, error) {
	//check if facility exist in phizz db
	facilityCount := 0
	err := db.Get().Get(&facilityCount, `select count(*) from automotive_facilities where vendor_id=$1`, vendorID)
	if err != nil {
		return false, err
	}
	if facilityCount == 0 {
		return false, nil
	}
	return true, nil
}

func facilityFormatHTML(facility *facilityPayload) bytes.Buffer {
	var facilityHTML bytes.Buffer
	facilityHTML.WriteString("<p> Code:" + facility.FacilityCode + "</p>")
	facilityHTML.WriteString("<p> Name:" + facility.Name + "</p>")
	facilityHTML.WriteString("<p> Address:" + facility.Address + "</p>")
	facilityHTML.WriteString("<p> City:" + facility.City + "</p>")
	facilityHTML.WriteString("<p> State:" + facility.StateCode + "</p>")
	facilityHTML.WriteString("<p> Zip:" + facility.PostalCode + "</p>")
	facilityHTML.WriteString("<p> Country:" + facility.Country + "</p>")
	facilityHTML.WriteString("<p> Phone:" + facility.Phone + "</p>")
	facilityHTML.WriteString("<p> Fax:" + facility.Fax + "</p>")
	facilityHTML.WriteString("<p> Email:" + facility.Email + "</p>")
	facilityHTML.WriteString("<p> Contact:" + facility.Contact + "</p>")
	newRates := getNewEffectiveRates(facility, 0)
	for _, v := range newRates {
		rate := v.EffectiveRate.String()
		if v.RateType == LaborRate {
			facilityHTML.WriteString("<p> Labor Rate:$" + rate + "</p>")
		}
		if v.RateType == LaborTax {
			facilityHTML.WriteString("<p> Labor Tax:" + rate + "%</p>")
		}
		if v.RateType == PartsTax {
			facilityHTML.WriteString("<p> Parts Tax:" + rate + "%</p>")
		}
	}
	facilityHTML.WriteString("<p> Payment type:" + facility.PaymentType + "</p>")
	facilityHTML.WriteString("<p> Preauth:" + strconv.FormatBool(facility.PreAuth) + "</p>")
	facilityHTML.WriteString("<p> Preauth limit:$" + facility.PreAuthLimit.String() + "</p>")
	return facilityHTML
}

// The function returns facilities matching given zoneID
// To get unassigned facilities, the facilities which are not assigned to any zone, 0 should be passed as zoneID
func getFacilitiesByZone(zoneID int, searchQuery string) ([]string, error) {
	var facilities []string
	var err error
	if zoneID > 0 {
		err = db.Get().Select(&facilities, `select facility_code from automotive_facilities where zone_id = $1 and facility_code ilike $2 order by facility_code`, zoneID, "%"+searchQuery+"%")
	} else {
		err = db.Get().Select(&facilities, `select facility_code from automotive_facilities where zone_id is null and facility_code ilike $1 order by facility_code`, "%"+searchQuery+"%")
	}
	if err != nil {
		if err == sql.ErrNoRows {
			return facilities, errors.Wrap(err, "No facility for given zone")
		}
		return facilities, errors.Wrap(err, "error loading facility data")
	}
	return facilities, nil
}

// FacilityVendorUpdate updates vendor id for given facility
func FacilityVendorUpdate(_ http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {

	details := struct {
		ID       int    `json:"id"`
		VendorID string `json:"vendor_id"`
	}{}
	dec := json.NewDecoder(req.Body)
	err := dec.Decode(&details)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error while getting vendor", nil)
	}

	if details.VendorID == "" || details.ID <= 0 {
		return http.StatusBadRequest, handlers.ErrorMessage(nil, "Malformed facility data for update.", nil)
	}

	tx, err := db.Get().Beginx()
	if err != nil {
		err = errors.Wrap(err, "error starting document delete transaction")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "error starting facility update transaction", nil)
	}

	updateQuery := "update automotive_facilities set vendor_id = $1 where id = $2"
	// update facility vendor
	_, err = tx.Exec(updateQuery, details.VendorID, details.ID)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Failed to update vendor id in facility", nil)
	}

	// Add update note
	_, err = addFacilityNote(tx, "Changed vendor to "+details.VendorID, details.ID, user.ID)
	if err != nil {
		_ = tx.Rollback()
		return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Failed add vendor update note", nil)
	}

	err = tx.Commit()
	if err != nil {
		err = errors.Wrap(err, "Database error committing transaction for facility vendor update")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error updating facility vendor", nil)
	}

	return http.StatusOK, map[string]interface{}{"facility_id": details.ID}
}

// FacilityUpdate updates vendor id for given facility
func FacilityUpdate(_ http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	ctx := req.Context()
	facilityID, err := strconv.Atoi(chi.URLParam(req, "id"))
	if err != nil || facilityID <= 0 {
		return http.StatusBadRequest, handlers.ErrorMessage(nil, "Malformed facility id for update.", nil)
	}

	facilityDetails := struct {
		ID           int             `json:"id" db:"id"`
		FacilityCode string          `json:"facility_code" db:"facility_code"`
		Name         string          `json:"name" db:"name"`
		Address      sql.NullString  `json:"address" db:"address"`
		PostalCode   string          `json:"postal_code" db:"postal_code"`
		City         string          `json:"city" db:"city"`
		StateCode    string          `json:"state_code" db:"state_code"`
		Country      string          `json:"country" db:"country"`
		Phone        sql.NullString  `json:"phone" db:"phone"`
		Fax          sql.NullString  `json:"fax" db:"fax"`
		LaborRate    []effectiveRate `json:"labor_rate" db:"-"`
		PartsTax     []effectiveRate `json:"parts_tax" db:"-"`
		LaborTax     []effectiveRate `json:"labor_tax" db:"-"`
		EIN          string          `json:"ein" db:"ein"`
		IsActive     bool            `json:"is_active" db:"is_active"`
		VendorID     sql.NullString  `json:"vendor_id" db:"vendor_id"`
		TaxType      string          `json:"tax_type" db:"tax_type"`
		Contact      null.String     `json:"contact" db:"contact"`
		PaymentType  null.String     `json:"payment_type" db:"payment_type"`
	}{}

	query := `select id, facility_code, name, address, postal_code, city, state_code, country, phone, fax, ein, is_active, vendor_id, contact, payment_type from automotive_facilities where id = $1`
	err = db.Get().GetContext(ctx, &facilityDetails, query, facilityID)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting facility", nil)
	}

	var reqPayload facilityPayload
	reqPayload.ID = facilityDetails.ID
	reqPayload.Name = facilityDetails.Name
	reqPayload.FacilityCode = facilityDetails.FacilityCode
	reqPayload.Address = facilityDetails.Address.String
	reqPayload.PostalCode = facilityDetails.PostalCode
	reqPayload.City = facilityDetails.City
	reqPayload.StateCode = facilityDetails.StateCode
	reqPayload.Country = facilityDetails.Country
	reqPayload.Phone = facilityDetails.Phone.String
	reqPayload.Fax = facilityDetails.Fax.String
	reqPayload.EIN = facilityDetails.EIN
	reqPayload.IsActive = facilityDetails.IsActive
	reqPayload.VendorID = facilityDetails.VendorID.String
	reqPayload.TaxType = facilityDetails.TaxType
	reqPayload.Contact = facilityDetails.Contact.String
	reqPayload.PaymentType = facilityDetails.PaymentType.String

	dec := json.NewDecoder(req.Body)
	err = dec.Decode(&reqPayload)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error while getting facility details", nil)
	}

	cleanFacility(&reqPayload)
	formErrors, err := validateFacility(ctx, &reqPayload, false)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(errors.Wrap(err, "Error validating facility"),
			"An error occurred validating the form values.", nil)
	}

	if len(formErrors) > 0 {
		return http.StatusBadRequest, handlers.ErrorMessage(nil, "Form validations errors.",
			map[string]interface{}{"errors": formErrors})
	}

	existingLaborRate, err := getExistingActiveLaborRate(ctx, facilityID)
	if err != nil {
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error updating facility", nil)
	}
	existingLaborTax, err := getExistingActiveLaborTax(ctx, facilityID)
	if err != nil {
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error updating facility", nil)
	}
	existingPartsTax, err := getExistingActivePartsTax(ctx, facilityID)
	if err != nil {
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error updating facility", nil)
	}

	tx, err := db.Get().Beginx()
	if err != nil {
		err = errors.Wrap(err, "database error beginning transaction for facility update")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error creating facility update", nil)
	}

	var notes []string
	updateRatesQuery := `update automotive_facilities_effective_rates set end_date = $1 where id = $2`
	// By default we want to set end date to one day before next effective date
	timeToAdd := -1
	for _, v := range reqPayload.LaborRate {
		if v.IsNew {
			notes = append(notes, "Changed facility labor rate from "+existingLaborRate.EffectiveRate.String()+" to "+v.EffectiveRate.String())

			if v.EffectiveDate.Time.Equal(existingLaborRate.EffectiveDate.Time) {
				// If both the effective dates are same then set the same date as end date for the previous record
				timeToAdd = 0
			}
			_, err = tx.ExecContext(ctx, updateRatesQuery, v.EffectiveDate.Time.AddDate(0, 0, timeToAdd), existingLaborRate.ID)
			if err != nil {
				_ = tx.Rollback()
				handlers.ReportError(req, errors.Wrap(err, "error updating facility labor rate"))
				return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error updating facility, database error", nil)
			}
		}
	}
	for _, v := range reqPayload.PartsTax {
		if v.IsNew {
			notes = append(notes, "Changed facility parts tax from "+existingPartsTax.EffectiveRate.String()+" to "+v.EffectiveRate.String())

			if v.EffectiveDate.Time.Equal(existingPartsTax.EffectiveDate.Time) {
				// If both the effective dates are same then set the same date as end date for the previous record
				timeToAdd = 0
			}
			_, err = tx.ExecContext(ctx, updateRatesQuery, v.EffectiveDate.Time.AddDate(0, 0, timeToAdd), existingPartsTax.ID)
			if err != nil {
				_ = tx.Rollback()
				handlers.ReportError(req, errors.Wrap(err, "error updating facility parts tax"))
				return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error updating facility, database error", nil)
			}
		}
	}
	for _, v := range reqPayload.LaborTax {
		if v.IsNew {
			notes = append(notes, "Changed facility labor tax from "+existingLaborTax.EffectiveRate.String()+" to "+v.EffectiveRate.String())
			if v.EffectiveDate.Time.Equal(existingLaborTax.EffectiveDate.Time) {
				// If both the effective dates are same then set the same date as end date for the previous record
				timeToAdd = 0
			}
			_, err = tx.ExecContext(ctx, updateRatesQuery, v.EffectiveDate.Time.AddDate(0, 0, timeToAdd), existingLaborTax.ID)
			if err != nil {
				_ = tx.Rollback()
				handlers.ReportError(req, errors.Wrap(err, "error updating facility labor tax"))
				return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error updating facility, database error", nil)
			}
		}
	}

	if facilityDetails.VendorID.String != reqPayload.VendorID {
		if reqPayload.VendorID != "" {
			// Check new vendor id exists in current facilities
			vendorExists, err := vendorExists(reqPayload.VendorID)
			if err != nil {
				return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error while checking duplicate vendor", nil)
			}
			if vendorExists {
				return http.StatusBadRequest, handlers.ErrorMessage(errors.New("Duplicate Vendor ID"), "Given vendor already used in existing facility", nil)
			}
			notes = append(notes, "Updated facility vendor ID to "+reqPayload.VendorID)
		} else {
			notes = append(notes, "Removed vendor ID")
		}

	}
	if facilityDetails.EIN != reqPayload.EIN {
		notes = append(notes, "Updated facility EIN to "+reqPayload.EIN)
	}
	if facilityDetails.TaxType != reqPayload.TaxType {
		notes = append(notes, "Updated facility Tax type from "+facilityDetails.TaxType+" to "+reqPayload.TaxType)
	}
	if facilityDetails.FacilityCode != reqPayload.FacilityCode {
		// Verify facility code should be between 5 and 7 characters long
		if len(reqPayload.FacilityCode) < 5 || len(reqPayload.FacilityCode) > 7 {
			return http.StatusBadRequest, handlers.ErrorMessage(errors.New("The Facility Code needs to be between five and seven characters long"), "The Facility Code needs to be between five and seven characters long, please correct.", nil)
		}
		// Verify new facility code already in use
		exists, err := facilityExists(reqPayload.FacilityCode)
		if err != nil {
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Could not verify if facility code exists", nil)
		}
		if exists {
			return http.StatusBadRequest, handlers.ErrorMessage(errors.New("Facility code already exist"), "facility code already exists", nil)
		}
		notes = append(notes, "Updated facility code to "+reqPayload.FacilityCode+" from "+facilityDetails.FacilityCode)
	}

	var managerUpdates string
	if user.HasRole("auto_claims_manager") {
		managerUpdates = `name = :name,
					facility_code = :facility_code,
					address = :address,
					postal_code = :postal_code,
					city = :city,
					state_code = :state_code,
					country = :country,
					phone = :phone,
					fax = :fax,
					is_active = :is_active,`
		if facilityDetails.Name != reqPayload.Name ||
			facilityDetails.FacilityCode != reqPayload.FacilityCode ||
			facilityDetails.Address.String != reqPayload.Address ||
			facilityDetails.PostalCode != reqPayload.PostalCode ||
			facilityDetails.City != reqPayload.City ||
			facilityDetails.StateCode != reqPayload.StateCode ||
			facilityDetails.Country != reqPayload.Country ||
			facilityDetails.Phone.String != reqPayload.Phone ||
			facilityDetails.Fax.String != reqPayload.Fax ||
			facilityDetails.Contact.String != reqPayload.Contact ||
			facilityDetails.PaymentType.String != reqPayload.PaymentType {
			notes = append(notes, "Updated facility address and contact details.")
		}
		if facilityDetails.IsActive != reqPayload.IsActive {
			if reqPayload.IsActive {
				notes = append(notes, "Updated facility status to active")
			} else {
				notes = append(notes, "Updated facility status to in-active")
			}
		}
	}

	updateQuery := `update automotive_facilities
					set ` + managerUpdates + `
					vendor_id = :vendor_id,
					ein = :ein,
					tax_type = :tax_type,
					contact = :contact,
					payment_type = :payment_type,
					updated_at = now() at time zone 'utc'
					where id = :id`
	stmt, err := tx.PrepareNamedContext(ctx, updateQuery)
	if err != nil {
		_ = tx.Rollback()
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error updating facility, PrepareNamed failed", nil)
	}
	defer func() { _ = stmt.Close() }()

	_, err = stmt.Exec(reqPayload)
	if err != nil {
		_ = tx.Rollback()
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error updating facility, database error", nil)
	}

	newEffectiveRates := getNewEffectiveRates(&reqPayload, user.ID)
	err = insertEffectiveRates(ctx, tx, newEffectiveRates)
	if err != nil {
		_ = tx.Rollback()
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error updating facility, database error", nil)
	}

	for _, v := range newEffectiveRates {
		if v.RateType == LaborRate {
			existingLaborRate = v
		}

		if v.RateType == LaborTax {
			existingLaborTax = v
		}

		if v.RateType == PartsTax {
			existingPartsTax = v
		}
	}

	// Update new rates in open automotive claims
	var autoClaims []struct {
		ID                 int             `db:"id"`
		LaborRate          decimal.Decimal `db:"labor_rate"`
		TaxLabor           decimal.Decimal `db:"tax_labor"`
		TaxParts           decimal.Decimal `db:"tax_parts"`
		FacilityName       string          `db:"facility_name"`
		FacilityAddress    string          `db:"facility_address"`
		FacilityPostalCode string          `db:"facility_postal_code"`
		FacilityCity       string          `db:"facility_city"`
		FacilityStateCode  string          `db:"facility_state_code"`
		FacilityCountry    string          `db:"facility_country"`
		FacilityPhone      string          `db:"facility_phone"`
		FacilityFax        string          `db:"facility_fax"`
		ROOpenedDate       types.JSPQDate  `db:"ro_opened_date"`
	}
	err = tx.Select(&autoClaims, `select id from automotive_claims where facility_id = $1 and status = $2`, reqPayload.ID, db.AutoClaimComplaintStatusOpen)
	if err != nil && err != sql.ErrNoRows {
		_ = tx.Rollback()
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting list of claims using facility, database error", nil)
	}

	for _, claim := range autoClaims {
		claim.FacilityName = reqPayload.Name
		claim.FacilityAddress = reqPayload.Address
		claim.FacilityPostalCode = reqPayload.PostalCode
		claim.FacilityCity = reqPayload.City
		claim.FacilityStateCode = reqPayload.StateCode
		claim.FacilityCountry = reqPayload.Country
		claim.FacilityPhone = reqPayload.Phone
		claim.FacilityFax = reqPayload.Fax
		if !claim.ROOpenedDate.Time.Before(existingLaborRate.EffectiveDate.Time) {
			claim.LaborRate = existingLaborRate.EffectiveRate
		}
		if !claim.ROOpenedDate.Time.Before(existingPartsTax.EffectiveDate.Time) {
			claim.TaxParts = existingPartsTax.EffectiveRate
		}
		if !claim.ROOpenedDate.Time.Before(existingLaborTax.EffectiveDate.Time) {
			claim.TaxLabor = existingLaborTax.EffectiveRate
		}
		updateClaim := `update automotive_claims
						set facility_name = :facility_name,
						facility_address = :facility_address,
						facility_postal_code = :facility_postal_code,
						facility_city = :facility_city,
						facility_state_code = :facility_state_code,
						facility_country = :facility_country,
						facility_phone = :facility_phone,
						facility_fax = :facility_fax,
						labor_rate = :labor_rate,
						tax_labor = :tax_labor,
						tax_parts = :tax_parts
						where id = :id`
		claimUpdateStmt, err := tx.PrepareNamed(updateClaim)
		if err != nil {
			_ = tx.Rollback()
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error updating facility details in claim, PrepareNamed failed", nil)
		}
		defer func() { _ = claimUpdateStmt.Close() }()
		_, err = claimUpdateStmt.Exec(claim)
		if err != nil {
			_ = tx.Rollback()
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error updating facility details in claim, database error", nil)
		}
	}

	for _, note := range notes {
		_, err = addFacilityNote(tx, note, reqPayload.ID, user.ID)
		if err != nil {
			_ = tx.Rollback()
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error adding notes for facility update", nil)
		}
	}

	err = tx.Commit()
	if err != nil {
		err = errors.Wrap(err, "Database error committing transaction for facility update")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error updating facility", nil)
	}

	return http.StatusOK, map[string]interface{}{"facility_id": reqPayload.ID}
}

func addFacilityNote(tx *sqlx.Tx, noteText string, facilityID, userID int) (int, error) {
	id, err := insertFacilityNote(&facilityNotePayload{
		FacilityID:      facilityID,
		NotesText:       noteText,
		CreatedByUserID: userID,
	}, tx)
	return id, err
}

// FacilityByStore returns facility based on store_id
func FacilityByStore(_ http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	ctx := req.Context()
	storeID := 0
	err := db.Get().GetContext(ctx, &storeID, "select id from stores where id = $1", chi.URLParam(req, "store_id"))
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage(err, "store_id not found", nil)
		}
		handlers.ReportError(req, errors.Wrap(err, "database error in getting store id"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error in getting store id", nil)
	}

	var facility struct {
		ID       int             `json:"id" db:"id"`
		PartsTax []effectiveRate `json:"parts_tax" db:"parts_tax"`
		LaborTax []effectiveRate `json:"labor_tax" db:"labor_tax"`
	}
	query := `select id from automotive_facilities where store_id = $1`
	err = db.Get().GetContext(ctx, &facility, query, storeID)
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage(err, "facility for store_id not found", nil)
		}
		err = errors.Wrap(err, "database error getting facility by store")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting facility by store", nil)
	}

	query = `select * from automotive_facilities_effective_rates where facility_id = $1 and rate_type = $2 order by effective_date desc, id desc`
	err = db.Get().Unsafe().SelectContext(ctx, &facility.LaborTax, query, facility.ID, LaborTax)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error getting labor tax"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error while loading facility", nil)
	}

	err = db.Get().Unsafe().SelectContext(ctx, &facility.PartsTax, query, facility.ID, PartsTax)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error getting part tax"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error while loading facility", nil)
	}

	return http.StatusOK, map[string]interface{}{"facility": facility}
}

// ListFacilities returns valid and active facilities list
func ListFacilities(_ http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	ctx := req.Context()
	var facilities []struct {
		ID           int      `json:"id" db:"id"`
		FacilityCode string   `json:"facility_code" db:"facility_code"`
		Name         string   `json:"name" db:"name"`
		StoreID      null.Int `json:"store_id" db:"store_id"`
	}
	query := `select id, facility_code, name, store_id from automotive_facilities order by name`
	err := db.Get().SelectContext(ctx, &facilities, query)
	if err != nil {
		err = errors.Wrap(err, "database error getting facilitis list")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting facilities", nil)
	}

	return http.StatusOK, map[string]interface{}{"facilities": facilities}
}
