package auto

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/json"
	"encoding/xml"
	"fmt"
	"log"
	"net/http"
	"strconv"
	"strings"
	"time"

	"phizz/conf"
	"phizz/db"
	"phizz/handlers"
	"phizz/intacct"

	"github.com/go-chi/chi"
	"github.com/jmoiron/sqlx"
	newrelic "github.com/newrelic/go-agent"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
	null "gopkg.in/guregu/null.v3"
)

// ClaimPaymentPayload struct for claim payment
type ClaimPaymentPayload struct {
	ID                       int             `json:"id" db:"id"`
	AuthorizationNumber      int             `json:"authorization_number" db:"authorization_number"`
	AutomotiveClaimID        int             `json:"automotive_claim_id" db:"automotive_claim_id"`
	CheckNumber              string          `json:"check_number" db:"check_number"`
	Amount                   decimal.Decimal `json:"amount" db:"amount"`
	PaidDate                 time.Time       `json:"paid_date" db:"paid_date"`
	BatchKey                 int             `json:"batch_key" db:"batch_key"`
	BillKey                  int             `json:"bill_key" db:"bill_key"`
	BillMemo                 string          `json:"bill_memo" db:"bill_memo"`
	AutomotiveIntacctBatchID int             `json:"automotive_intacct_batch_id" db:"automotive_intacct_batch_id"`
	BillNumber               string          `json:"bill_number" db:"intacct_bill_number"`
	RefundClaimNumber        int             `json:"refund_claim_number" db:"refund_claim_number"`
}

// SubmitBillsToIntacct function submits the automotive claim to the intacct ap payment service
// This function creates a batch key for the given day if it does not exist already
// then using this batch key and other details, it creates a bill in intacct system
func SubmitBillsToIntacct(ctx context.Context, tx *sqlx.Tx, claim *ClaimPayload, batch *BatchData, intacctAccountDetails conf.IntacctAccountDetails) (*ClaimPaymentPayload, error) {
	claimPayment := ClaimPaymentPayload{}
	var err error

	intacctResult := struct {
		AuthStatus   string `xml:"operation>authentication>status"`
		ResultStatus string `xml:"operation>result>status"`
		Key          int    `xml:"operation>result>key"`
		ErrorMessage struct {
			Error []struct {
				Description string `xml:"description2"`
			} `xml:"error"`
		} `xml:"operation>result>errormessage"`
	}{}

	// Intacct uses Pacific timezone in billbatch, it's important to use the same timezone to create the bill batch
	// If we use UTC, there is difference of 8 hours, Hence at 4 PM- 02/27/17 Pacific time, the UTC time will be 00:00 Hrs
	// 02/28/17. So batch title in our system will be 02/28/17, which was generated on 02/27/17 Pacific time in Intacct
	// On next day, our system won't generate new batch as the date is still 02/28/17 in UTC. And Intacct rejects this key
	// because it was generated on 02/27/17 and a batchkey is valid only for that day
	// To avoid this issue, the timezone to calculate the date of batch should be what Intacct is using
	// we are using LoadLocation to take care of DST changes

	// loading this way the program won't start if the needed time zone data isn't available
	var loc = handlers.LoadLocOrPanic("America/Los_Angeles")
	currentDate := time.Now().In(loc)

	batchTitle := strings.Join(
		[]string{strconv.Itoa(int(currentDate.Month())), strconv.Itoa(currentDate.Day()), strconv.Itoa(currentDate.Year())}, "/")

	claimPayment.BatchKey, err = batchKey(batchTitle)
	if err != nil {
		return nil, errors.Wrap(err, "Getting batch key failed")
	}

	if claimPayment.BatchKey == 0 {
		batchKeyXML, err := intacct.CreateBillBatchRequest(batchTitle)
		if err != nil {
			return nil, errors.Wrap(err, "Creating batch request failed")
		}

		batchKeyResponse, err := intacct.Request(ctx, batchKeyXML)
		if err != nil {
			return nil, errors.Wrap(err, "Intacct Server communication error")
		}

		batchResBuf := bytes.NewBuffer(batchKeyResponse)
		err = xml.NewDecoder(batchResBuf).Decode(&intacctResult)
		if err != nil {
			return nil, errors.Wrap(err, "Failed to docode batch key response")
		}

		if intacctResult.ResultStatus != intacct.ResultSuccess {
			return nil, errors.New("Failed to create bill batch")
		}

		claimPayment.BatchKey = intacctResult.Key
		err = storeBatchKey(ctx, tx, batchTitle, claimPayment.BatchKey)
		if err != nil {
			return nil, errors.Wrap(err, "Storing batch key failed")
		}
	}

	claimPayment.BillMemo = batch.Memo
	// The specific memo will be available for adjustment and negative claims
	if claim.BillMemo.Valid {
		claimPayment.BillMemo = claim.BillMemo.String
	}
	claimPayment.BillNumber = batch.IntacctBillNumber
	claimPayment.Amount = claim.Estimate.Round(2)

	billDetails := intacct.Bill{
		VendorID:     batch.VendorID,
		AccountLabel: batch.AccountLabel,
		Amount:       claimPayment.Amount,
		Memo:         claimPayment.BillMemo,
		LocationID:   intacctAccountDetails.LocationID,
		TotalPaid:    decimal.NewFromFloat(0),
		TotalDue:     claimPayment.Amount,
		ProjectID:    batch.ProjectID,
		TermName:     intacctAccountDetails.TermName,
		BatchKey:     claimPayment.BatchKey,
		BillNo:       claimPayment.BillNumber,
		LoanNumber:   "",
		Description:  claimPayment.BillMemo,
	}

	if claim.ProductCode == db.ProductCodeDrivePur {
		billDetails.CustomerID = claim.IntacctCustID
	}

	billXML, err := intacct.CreateBillRequest(billDetails)
	if err != nil {
		return nil, errors.Wrap(err, "Creating bill request failed")
	}

	billResponse, err := intacct.Request(ctx, billXML)
	if err != nil {
		return nil, errors.Wrap(err, "Intacct Server communication error")
	}

	billResBuf := bytes.NewBuffer(billResponse)
	err = xml.NewDecoder(billResBuf).Decode(&intacctResult)
	if err != nil {
		return nil, errors.Wrap(err, "Failed to decode create bill response")
	}

	if intacctResult.ResultStatus != intacct.ResultSuccess {
		if len(intacctResult.ErrorMessage.Error) > 0 {
			return nil, errors.New(intacctResult.ErrorMessage.Error[0].Description)
		}
		return nil, errors.New("Failed to create bill")
	}
	claimPayment.BillKey = intacctResult.Key

	return &claimPayment, nil
}

func getIntacctAccountDetail(productCode string) (conf.IntacctAccountDetails, error) {
	switch productCode {
	case db.ProductCodeService:
		return conf.Get().IntacctService, nil
	case db.ProductCodeMaintenance:
		return conf.Get().IntacctMaintenance, nil
	case db.ProductCodeAppearanceProtection:
		return conf.Get().IntacctAPP, nil
	case db.ProductCodePaintlessDentRepair:
		return conf.Get().IntacctPDR, nil
	case db.ProductCodeKeyReplacement:
		return conf.Get().IntacctKey, nil
	case db.ProductCodeDrivePur:
		return conf.Get().IntacctDrivePur, nil
	case db.ProductCodeTireWheel:
		return conf.Get().IntacctTireWheel, nil
	default:
		return conf.IntacctAccountDetails{}, errors.New("Invalid product code")
	}
}

// batchKey is to be generated for everyday in intacct, batchKey is pre-req for creating bill
func batchKey(batchTitle string) (int, error) {
	batchKey := 0
	err := db.Get().Get(&batchKey, `select batch_key from automotive_intacct_bill_batches where batch_title = $1`, batchTitle)
	if err != nil && err != sql.ErrNoRows {
		return batchKey, errors.Wrap(err, "Error loading intacct batch data")
	}
	return batchKey, nil
}

// FacilityVendorID gives vendor ID for facility.
func FacilityVendorID(ctx context.Context, facilityID int) (string, error) {
	var vendorID sql.NullString
	err := db.Get().GetContext(ctx, &vendorID, `select vendor_id from automotive_facilities where id = $1`, facilityID)
	if err != nil && err != sql.ErrNoRows {
		return "", errors.Wrap(err, "Error loading facilities data")
	}
	return vendorID.String, nil
}

func facilityCode(facilityID int) (string, error) {
	facilityCode := ""
	// facilityCode is must, if return value is sql.ErrNoRows, this is still an error
	err := db.Get().Get(&facilityCode, `select facility_code from automotive_facilities where id = $1`, facilityID)
	if err != nil {
		return facilityCode, errors.Wrap(err, "Error loading facilities data")
	}
	return facilityCode, nil
}

func storeBatchKey(ctx context.Context, tx *sqlx.Tx, batchTitle string, batchKey int) error {
	batchInsert := `insert into automotive_intacct_bill_batches(batch_title, batch_key) values($1,$2)returning id`
	id := 0
	err := tx.GetContext(ctx, &id, batchInsert, batchTitle, batchKey)
	if err != nil {
		return errors.Wrap(err, "Error inserting batchkey in database")
	}
	return nil
}

// CreateIntacctBatch create intacct batch
func CreateIntacctBatch(ctx context.Context, tx *sqlx.Tx) (int, error) {
	query := `insert into automotive_intacct_batches(created_at) values (now() at time zone 'utc') returning id`
	id := 0
	row := tx.QueryRowContext(ctx, query)
	err := row.Scan(&id)
	if err != nil {
		return id, errors.Wrap(err, "Database error inserting new batch")
	}
	return id, err
}

// AddClaimToBatch adds claim to batch
func AddClaimToBatch(ctx context.Context, tx *sqlx.Tx, claimID, batchID int, amount decimal.Decimal) error {
	query := `insert into automotive_intacct_batch_details(automotive_claim_id,automotive_intacct_batch_id,amount) values ($1,$2,$3)`
	_, err := tx.ExecContext(ctx, query, claimID, batchID, amount)
	if err != nil {
		return errors.Wrap(err, "Error adding Automotive claim to batch")
	}
	return err
}

// ClaimSubmitSuccess method to update claim submission success
func ClaimSubmitSuccess(ctx context.Context, tx *sqlx.Tx, claimID, batchID int) error {
	query := `update automotive_intacct_batch_details set is_success = true where automotive_claim_id=$1 and automotive_intacct_batch_id=$2`
	_, err := tx.ExecContext(ctx, query, claimID, batchID)
	if err != nil {
		return errors.Wrap(err, "Error update auto claim to batch success")
	}
	return err
}

// BatchData struct stores payment batch data
type BatchData struct {
	BatchID           int
	FacilityCode      string
	ProductCode       string
	VendorID          string
	ProjectID         string
	AccountLabel      string
	IntacctBillNumber string
	Memo              string
}

// ClaimAuthorize function submits claim batch to intacct
func ClaimAuthorize(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	var claims struct {
		ClaimIDList []struct {
			ID             int      `json:"id"`
			ClaimPaymentID null.Int `json:"claim_payment_id,omitempty"`
		} `json:"claim_id_list"`
		FacilityID  int             `json:"facility_id"`
		ProductCode string          `json:"product_code"`
		PayType     string          `json:"pay_type"`
		TotalAmount decimal.Decimal `json:"total_amount"`
	}
	ctx := req.Context()

	dec := json.NewDecoder(req.Body)
	err := dec.Decode(&claims)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Malformed data for claim authorize", nil)
	}

	if len(claims.ClaimIDList) == 0 || claims.ProductCode == "" {
		return http.StatusBadRequest, handlers.ErrorMessage(errors.New("invalid input"), "invalid request for auto claim authorize", nil)
	}

	if claims.FacilityID == 0 && claims.PayType != db.PayTypeCustomer {
		return http.StatusBadRequest, handlers.ErrorMessage(errors.New("invalid input"), "invalid request for auto claim authorize, facility id is not available", nil)
	}

	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error beginning transaction for auto claim authorization", nil)
	}

	intacctAccountDetails, err := getIntacctAccountDetail(claims.ProductCode)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Failed to get intacct account details, Invalid product code", nil)
	}

	batch := BatchData{}
	batch.BatchID, err = CreateIntacctBatch(ctx, tx)
	if err != nil {
		_ = tx.Rollback()
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error creating auto claim batch", nil)
	}

	if claims.PayType == db.PayTypeCustomer {
		var claimIDs []int
		for _, c := range claims.ClaimIDList {
			claimIDs = append(claimIDs, c.ID)
		}
		vendor := ""
		countVendorsQuery, args, err := sqlx.In(`select distinct(customer_payee_vendor_id) from automotive_claims where id in (?)`, claimIDs)
		if err != nil {
			_ = tx.Rollback()
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error in getting customer vendor, In query failed", nil)
		}

		query := tx.Rebind(countVendorsQuery)
		err = tx.GetContext(ctx, &vendor, query, args...)
		if err != nil {
			_ = tx.Rollback()
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error while getting vendor details", nil)
		}
		if vendor == "" {
			_ = tx.Rollback()
			return http.StatusInternalServerError, handlers.ErrorMessage(errors.New("Vendor is missing"), "Vendor is missing for batch", nil)
		}

		batch.VendorID = vendor

	} else if claims.PayType == db.PayTypeStore || claims.PayType == db.PayTypeCreditCard {
		batch.VendorID, err = FacilityVendorID(ctx, claims.FacilityID)
		if err != nil || batch.VendorID == "" {
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "error getting facilityVendorID for facility "+strconv.Itoa(claims.FacilityID), nil)
		}
	}

	errorMessage, err := UpdateBatchDetails(&batch, claims.FacilityID, claims.ProductCode, claims.PayType, intacctAccountDetails)
	if err != nil {
		_ = tx.Rollback()
		return http.StatusInternalServerError, handlers.ErrorMessage(err, errorMessage, nil)
	}
	var batchClaims []*ClaimPayload // claim list to be submitted to INTACCT
	// Validate claims and add to claims to batch
	txn := w.(newrelic.Transaction)
	for _, c := range claims.ClaimIDList {
		claim, err := ClaimSelect(txn, req, strconv.Itoa(c.ID))
		if err != nil {
			_ = tx.Rollback()
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting auto claim from database for ID:"+strconv.Itoa(c.ID), nil)
		}

		formErrors, err := validateClaim(ctx, claim, ClaimRequestSubmitIntacct, &user)
		if err != nil {
			_ = tx.Rollback()
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error while validating claim information", nil)
		}
		if len(formErrors) > 0 {
			_ = tx.Rollback()
			return http.StatusBadRequest, handlers.ErrorMessage(errors.New("validation error"),
				"Form validations errors for auto claim:"+claim.ContractNumber,
				map[string]interface{}{"errors": formErrors})
		}

		if (claim.FacilityID != claims.FacilityID && claim.PayType != db.PayTypeCustomer) || claim.ProductCode != claims.ProductCode {
			_ = tx.Rollback()
			return http.StatusBadRequest, handlers.ErrorMessage(errors.New("validate error"),
				"the claim should have the same facilityID and productCode as the batch", nil)
		}

		if claim.Status == db.AutoClaimStatusReversed || claim.Status == db.AutoClaimStatusAdjusted {
			var claimPayment struct {
				ID                  int             `db:"id"`
				AuthorizationNumber int             `db:"authorization_number"`
				Amount              decimal.Decimal `db:"amount"`
				BillMemo            string          `db:"bill_memo"`
			}
			query := `select id, amount, authorization_number, bill_memo from automotive_claim_payments where id = $1 and automotive_claim_id = $2`
			err := db.Get().GetContext(ctx, &claimPayment, query, c.ClaimPaymentID, c.ID)
			if err != nil {
				_ = tx.Rollback()
				return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting auto claim payments for claim: "+strconv.Itoa(c.ID), nil)
			}

			claim.Estimate = claimPayment.Amount
			claim.ClaimPaymentID = null.IntFrom(int64(claimPayment.ID))
			claim.AuthorizationNumber = null.IntFrom(int64(claimPayment.AuthorizationNumber))
			claim.BillMemo = null.NewString(claimPayment.BillMemo, true)

			if claim.ProductCode == db.ProductCodeDrivePur {
				intacctCustomerID, err := validateDP(ctx, claim.ContractStoreID)
				if err != nil || intacctCustomerID == "" {
					return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting customerID for drive pur claim : "+strconv.Itoa(c.ID), nil)
				}
				claim.IntacctCustID = intacctCustomerID
			}
		}

		err = AddClaimToBatch(ctx, tx, c.ID, batch.BatchID, claim.Estimate)
		if err != nil {
			_ = tx.Rollback()
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error adding auto claim to batch:"+strconv.Itoa(c.ID), nil)
		}

		batchClaims = append(batchClaims, claim)
	}

	err = tx.Commit()
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error committing transaction for auto claim authorization", nil)
	}

	batchErrors := []string{}
	var authotizedClaimsIDs []int // claim list successfully submitted to INTACCT
	// Authorize maintenance claims as one bill
	if claims.ProductCode == db.ProductCodeMaintenance && claims.PayType == db.PayTypeStore && len(batchClaims) > 0 {
		authotizedClaimsIDs, batchErrors, err = claimMaintenanceBatchAuthorize(ctx, req, batchClaims, &batch, user.ID, intacctAccountDetails)
		if err != nil {
			handlers.ReportError(req, errors.Wrap(err, "Failed to authorize maintenance batch with ID: "+strconv.Itoa(batch.BatchID)))
			// add failure note in claim
			for _, claim := range batchClaims {
				noteTX, err := db.Get().BeginTxx(ctx, nil)
				if err != nil {
					handlers.ReportError(req, errors.Wrap(err, "error in adding notes"))
					batchErrors = append(batchErrors, err.Error())
				}
				recordNote := RecordNotePayload{
					AutomotiveClaimID: claim.ID,
					CreatedByUserID:   user.ID,
					CreatedAt:         time.Now(),
					NotesText:         "INTACCT_BATCH: Submit to Intacct failed in batch:" + strconv.Itoa(batch.BatchID),
				}
				_, err = InsertRecordNote(ctx, &recordNote, noteTX)
				if err != nil {
					handlers.ReportError(req, errors.Wrap(err, "error in adding notes"))
					batchErrors = append(batchErrors, err.Error())
				}
				err = noteTX.Commit()
				if err != nil {
					handlers.ReportError(req, errors.Wrap(err, "error in adding notes"))
					batchErrors = append(batchErrors, err.Error())
				}
			}
		}
	} else {
		// Authorize individual claims, create bills
		for _, claim := range batchClaims {
			batchTx, err := db.Get().Beginx()
			if err != nil {
				return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error beginning transaction for auto claim authorization", nil)
			}

			err = ClaimAuthorization(ctx, batchTx, claim, &batch, user.ID, intacctAccountDetails)
			if err != nil {
				_ = batchTx.Rollback()
				handlers.ReportError(req, errors.Wrap(err, "Failed to authorize claim for contract :"+claim.ContractNumber))
				batchErrors = append(batchErrors, err.Error())
				// add failure note in claim
				noteTX, err := db.Get().BeginTxx(ctx, nil)
				if err != nil {
					handlers.ReportError(req, errors.Wrap(err, "error in adding notes"))
					batchErrors = append(batchErrors, err.Error())
				}
				recordNote := RecordNotePayload{
					AutomotiveClaimID: claim.ID,
					CreatedByUserID:   user.ID,
					CreatedAt:         time.Now(),
					NotesText:         "INTACCT_BATCH: Submit to Intacct failed in batch:" + strconv.Itoa(batch.BatchID),
				}
				_, err = InsertRecordNote(ctx, &recordNote, noteTX)
				if err != nil {
					handlers.ReportError(req, errors.Wrap(err, "error in adding notes"))
					batchErrors = append(batchErrors, err.Error())
				}
				err = noteTX.Commit()
				if err != nil {
					handlers.ReportError(req, errors.Wrap(err, "error in adding notes"))
					batchErrors = append(batchErrors, err.Error())
				}
			} else {
				err = ClaimSubmitSuccess(ctx, batchTx, claim.ID, batch.BatchID)
				if err != nil {
					// do not rollback, intacct submission is already successful
					handlers.ReportError(req, errors.Wrap(err, "Error updating auto claim in batch:"+claim.ContractNumber))
					batchErrors = append(batchErrors, err.Error())
				}
				// add success note in claim
				recordNote := RecordNotePayload{
					AutomotiveClaimID: claim.ID,
					CreatedByUserID:   user.ID,
					CreatedAt:         time.Now(),
					NotesText:         "INTACCT_BATCH: Submit to Intacct successful in batch:" + strconv.Itoa(batch.BatchID),
				}

				_, err = InsertRecordNote(ctx, &recordNote, batchTx)
				if err != nil {
					// do not rollback, intacct submission is already successful
					handlers.ReportError(req, errors.Wrap(err, "error in adding notes"))
					batchErrors = append(batchErrors, err.Error())
				}
				// Bill was created successfully
				authotizedClaimsIDs = append(authotizedClaimsIDs, claim.ID)
				err = batchTx.Commit()
				if err != nil {
					return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error committing transaction for auto claim authorization", nil)
				}
			}
		}
	}

	return http.StatusOK, map[string]interface{}{"success": len(authotizedClaimsIDs), "batchID": batch.BatchID, "batchErrors": batchErrors}
}

// claimMaintenanceBatchAuthorize methods generates single intacct bill request for maintenance claims batch if payment type is store
// All claims from single batch will have respective auth numbers, payment entries, notes and unidata bill numbers but it will generate single
// bill request to intacct with summation of estimated values of each claim.
func claimMaintenanceBatchAuthorize(ctx context.Context, req *http.Request, claims []*ClaimPayload, batch *BatchData, userID int, intacctAccountDetails conf.IntacctAccountDetails) ([]int, []string, error) {
	claimIDs := []int{}
	batchErrors := []string{}
	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		return claimIDs, batchErrors, errors.Wrap(err, "Database error beginning transaction for auto claim authorization")
	}

	var loc = handlers.LoadLocOrPanic(db.TimeZoneMountain)
	currentDate := time.Now().In(loc).Format("01-02-2006_15:04:05")
	// New format for the bill numbers with batch number included
	batch.IntacctBillNumber = batch.FacilityCode + "_" + currentDate + ` #` + strconv.Itoa(batch.BatchID)
	batch.Memo = batch.IntacctBillNumber
	paymentDetails := ClaimPayload{}

	var authNumbers []int
	var exceptionAuths []int
	for _, claim := range claims {
		// Create bill number for unidata
		unidataBillNumber, err := CreateBillNumber(ctx, tx, claim, batch.FacilityCode, claim.ContractNumber)
		if err != nil {
			_ = tx.Rollback()
			return claimIDs, batchErrors, errors.Wrap(err, "Failed to create bill number.")
		}

		// We dont want to delete claim payments for negative claims
		// because that is the only data we have related to negative claim
		authNumber := 0
		claimPaymentID := 0
		if claim.Status != db.AutoClaimStatusReversed && claim.Status != db.AutoClaimStatusAdjusted {
			deleteOldAuthQuery := `delete from automotive_claim_payments where automotive_claim_id=$1`
			_, err = tx.ExecContext(ctx, deleteOldAuthQuery, claim.ID)
			if err != nil {
				_ = tx.Rollback()
				return claimIDs, batchErrors, errors.Wrap(err, "Database error deleting old authorization")
			}
		}

		// For negative claims we will just update existing claim payment details with bill number
		if claim.Status == db.AutoClaimStatusReversed || claim.Status == db.AutoClaimStatusAdjusted {
			authQuery := `update automotive_claim_payments
						 set unidata_bill_number = $2
                         where automotive_claim_id = $1
							and id = $3`
			_, err = tx.ExecContext(ctx, authQuery, claim.ID, unidataBillNumber, claim.ClaimPaymentID)
			if err != nil {
				_ = tx.Rollback()
				return claimIDs, batchErrors, errors.Wrap(err, "Database error updating claim pauments")
			}
			authNumber = int(claim.AuthorizationNumber.ValueOrZero())
			exceptionAuths = append(exceptionAuths, authNumber)
			claimPaymentID = int(claim.ClaimPaymentID.Int64)
		} else {
			authQuery := `insert into automotive_claim_payments(automotive_claim_id, unidata_bill_number) values($1, $2) returning authorization_number, id`
			row := tx.QueryRowContext(ctx, authQuery, claim.ID, unidataBillNumber)
			err = row.Scan(&authNumber, &claimPaymentID)
			if err != nil {
				_ = tx.Rollback()
				return claimIDs, batchErrors, errors.Wrap(err, "Database error getting new authNumber")
			}
			authNumbers = append(authNumbers, authNumber)
		}
		if err != nil {
			_ = tx.Rollback()
			return claimIDs, batchErrors, errors.Wrap(err, "Database error inserting new payment request")
		}

		updateQuery := `update automotive_intacct_batch_details set automotive_claim_payment_id = $1 where automotive_intacct_batch_id = $2 and automotive_claim_id = $3`
		_, err = tx.Exec(updateQuery, claimPaymentID, batch.BatchID, claim.ID)
		if err != nil {
			_ = tx.Rollback()
			return claimIDs, batchErrors, errors.Wrap(err, "Database error udpating batch details for payment")
		}

		status := db.AutoClaimStatusWaitingForCheck
		if claim.Status == db.AutoClaimStatusReversed {
			status = db.AutoClaimStatusWaitingForReversed
		}

		// If chargeback claim set appropriate status
		if claim.ChargeBack {
			status = db.AutoClaimStatusWaitingForChargeback
		}

		updateClaimStatus := `update automotive_claims set status = $1 where id = $2`
		_, err = tx.ExecContext(ctx, updateClaimStatus, status, claim.ID)
		if err != nil {
			_ = tx.Rollback()
			return claimIDs, batchErrors, errors.Wrap(err, "Error updating auto claim status")
		}

		// Add entry in update table for audit trail
		err = ClaimUpdated(ctx, tx, claim.ID, userID)
		if err != nil {
			_ = tx.Rollback()
			return claimIDs, batchErrors, errors.Wrap(err, "Error inserting auto_claim_updates")
		}

		// update batch entry
		err = ClaimSubmitSuccess(ctx, tx, claim.ID, batch.BatchID)
		if err != nil {
			_ = tx.Rollback()
			return claimIDs, batchErrors, errors.Wrap(err, "Error updating batch entry")
		}

		// add success note in claim
		recordNote := RecordNotePayload{
			AutomotiveClaimID: claim.ID,
			CreatedByUserID:   userID,
			CreatedAt:         time.Now(),
			NotesText:         "INTACCT_BATCH: Submit to Intacct successful in batch:" + strconv.Itoa(batch.BatchID),
		}

		_, err = InsertRecordNote(ctx, &recordNote, tx)
		if err != nil {
			_ = tx.Rollback()
			return claimIDs, batchErrors, errors.Wrap(err, "Error adding batch record note")
		}
		// Bill was created successfully
		claimIDs = append(claimIDs, claim.ID)

		paymentDetails.Estimate = paymentDetails.Estimate.Add(claim.Estimate)
	}

	claimPayment, err := SubmitBillsToIntacct(ctx, tx, &paymentDetails, batch, intacctAccountDetails)
	if err != nil {
		batchErrors = append(batchErrors, err.Error())
		_ = tx.Rollback()
		return claimIDs, batchErrors, err
	}

	err = tx.Commit()
	if err != nil {
		return claimIDs, batchErrors, errors.Wrap(err, "Database error committing transaction for auto claim authorization")
	}

	// This is added to get more details tod fix TS-5050
	// Remove this code once it is fixed
	if req != nil {
		handlers.ReportError(req, errors.New(fmt.Sprintf("Batch Submitted with %d authnumbers", len(authNumbers))))
		handlers.ReportError(req, errors.New(fmt.Sprintf("Batch Submitted with %d exception authnumbers", len(exceptionAuths))))
	}

	if len(authNumbers) > 0 {
		queryPaymentUpdate := `update automotive_claim_payments
			  set batch_key = ?,
				  bill_key = ?,
				  intacct_bill_number = ?,
				  bill_memo = ?
			  where authorization_number in (?)`
		queryPaymentUpdate, args, err := sqlx.In(queryPaymentUpdate, claimPayment.BatchKey, claimPayment.BillKey, claimPayment.BillNumber, claimPayment.BillMemo, authNumbers)
		if err != nil {
			batchErrors = append(batchErrors, err.Error())
		}
		queryPaymentUpdate = db.Get().Rebind(queryPaymentUpdate)
		_, err = db.Get().ExecContext(ctx, queryPaymentUpdate, args...)
		if err != nil {
			batchErrors = append(batchErrors, err.Error())
		}
	}

	// We dont want to update bill memo for the adjustment and negative payments
	if len(exceptionAuths) > 0 {
		queryPaymentUpdate := `update automotive_claim_payments
			  set batch_key = ?,
				  bill_key = ?,
				  intacct_bill_number = ?
			  where authorization_number in (?)`
		queryPaymentUpdate, args, err := sqlx.In(queryPaymentUpdate, claimPayment.BatchKey, claimPayment.BillKey, claimPayment.BillNumber, exceptionAuths)
		if err != nil {
			batchErrors = append(batchErrors, err.Error())
		}
		queryPaymentUpdate = db.Get().Rebind(queryPaymentUpdate)
		_, err = db.Get().ExecContext(ctx, queryPaymentUpdate, args...)
		if err != nil {
			batchErrors = append(batchErrors, err.Error())
		}
	}

	return claimIDs, batchErrors, nil
}

// ClaimAuthorization authorization of the claim
func ClaimAuthorization(ctx context.Context, tx *sqlx.Tx, claim *ClaimPayload, batch *BatchData, userID int, intacctAccountDetails conf.IntacctAccountDetails) error {
	var err error
	batch.IntacctBillNumber, err = CreateBillNumber(ctx, tx, claim, batch.FacilityCode, claim.ContractNumber)
	if err != nil {
		return err
	}
	batch.Memo = claim.ContractNumber + " " + claim.CustomerName
	claimPayment, authNumber, err := submitToIntacct(ctx, tx, claim, batch, intacctAccountDetails)
	if err != nil {
		return err
	}

	claimPayment.AuthorizationNumber = authNumber
	if claim.PayType == db.PayTypeCreditCard {
		err := updateClaimPayment(ctx, tx, claimPayment)
		if err != nil {
			return err
		}

		// No need to update status for reversed claims
		status := db.AutoClaimStatusCCPaid
		if claim.Status == db.AutoClaimStatusReversed {
			status = db.AutoClaimStatusWaitingForReversed
		}

		if claim.Status == db.AutoClaimStatusApproved && claim.ChargeBack {
			status = db.AutoClaimStatusChargeback
		}

		updateClaimStatus := `update automotive_claims set status = $1 where id = $2`
		_, err = tx.ExecContext(ctx, updateClaimStatus, status, claim.ID)
		if err != nil {
			return errors.Wrap(err, "Error updating auto claim status")
		}
	} else {
		updateQuery := `update automotive_claim_payments
						set batch_key = $1,
							bill_key = $2,
							intacct_bill_number = $3,
							bill_memo = $4
						where authorization_number = $5`

		billMemo := claimPayment.BillMemo
		if claim.ChargeBack {
			billMemo = claim.Notes
		}

		_, err = tx.ExecContext(
			ctx,
			updateQuery,
			claimPayment.BatchKey,
			claimPayment.BillKey,
			claimPayment.BillNumber,
			billMemo,
			claimPayment.AuthorizationNumber,
		)
		if err != nil {
			return errors.Wrap(err, "Database error updating auto payment details")
		}

		status := db.AutoClaimStatusWaitingForCheck
		if claim.Status == db.AutoClaimStatusReversed {
			status = db.AutoClaimStatusWaitingForReversed
		}

		if claim.ChargeBack {
			status = db.AutoClaimStatusWaitingForChargeback
		}

		updateClaimStatus := `update automotive_claims set status = $1 where id = $2`
		_, err = tx.ExecContext(ctx, updateClaimStatus, status, claim.ID)
		if err != nil {
			return errors.Wrap(err, "Error updating auto claim status")
		}
	}

	// Add entry in update table for audit trail
	err = ClaimUpdated(ctx, tx, claim.ID, userID)
	if err != nil {
		return errors.Wrap(err, "Error inserting auto_claim_updates")
	}
	return err
}

func updateClaimPayment(ctx context.Context, tx *sqlx.Tx, claimPayment *ClaimPaymentPayload) error {
	updateQuery := `update automotive_claim_payments
					set batch_key = $1,
						bill_key = $2,
						intacct_bill_number = $3,
						bill_memo = $4,
						paid_date = now() at time zone 'utc',
						updated_at = now() at time zone 'utc',
						amount = $5
					where authorization_number = $6`

	_, err := tx.ExecContext(
		ctx,
		updateQuery,
		claimPayment.BatchKey,
		claimPayment.BillKey,
		claimPayment.BillNumber,
		claimPayment.BillMemo,
		claimPayment.Amount,
		claimPayment.AuthorizationNumber,
	)
	if err != nil {
		return errors.Wrap(err, "Database error updating auto payment details")
	}
	return nil
}

// UpdateBatchDetails updates batch details
func UpdateBatchDetails(batch *BatchData, facilityID int, productCode string, payType string, intacctAccountDetails conf.IntacctAccountDetails) (string, error) {
	var err error
	batch.ProjectID = intacctAccountDetails.ProjectID
	batch.AccountLabel = intacctAccountDetails.AccountLabel
	batch.ProductCode = productCode

	if payType != db.PayTypeCustomer {
		batch.FacilityCode, err = facilityCode(facilityID)
	}

	if err != nil {
		return "error getting facility code", err
	}

	return "", nil
}

func submitToIntacct(ctx context.Context, tx *sqlx.Tx, claim *ClaimPayload, batch *BatchData, intacctAccountDetails conf.IntacctAccountDetails) (*ClaimPaymentPayload, int, error) {
	// Need to check if there is already an authorization done for this automotive_claim_id. In case of re-opening the same
	// claim (the claim which had WC, Waiting for Check status, there is already an entry in automotive_claim_payments table
	// before authorizing the the automotive_claim, the earlier entry should be deleted
	authNumber := 0
	if claim.Status != db.AutoClaimStatusReversed && claim.Status != db.AutoClaimStatusAdjusted {
		deleteOldAuthQuery := `delete from automotive_claim_payments where automotive_claim_id=$1`
		_, err := tx.ExecContext(ctx, deleteOldAuthQuery, claim.ID)
		if err != nil {
			return nil, authNumber, errors.Wrap(err, "Database error deleting old authorization")
		}
	}

	var err error
	if claim.Status == db.AutoClaimStatusReversed || claim.Status == db.AutoClaimStatusAdjusted {
		authQuery := `update automotive_claim_payments
						 set unidata_bill_number = $2
                         where automotive_claim_id = $1
							and id = $3`
		_, err = tx.Exec(authQuery, claim.ID, batch.IntacctBillNumber, claim.ClaimPaymentID)
		authNumber = int(claim.AuthorizationNumber.ValueOrZero())
	} else {
		authQuery := `insert into automotive_claim_payments(automotive_claim_id) values($1) returning authorization_number, id`
		row := tx.QueryRowContext(ctx, authQuery, claim.ID)
		err = row.Scan(&authNumber, &claim.ClaimPaymentID)
	}
	if err != nil {
		return nil, authNumber, errors.Wrap(err, "Database error inserting new payment request")
	}

	updateQuery := `update automotive_intacct_batch_details set automotive_claim_payment_id = $1 where automotive_intacct_batch_id = $2 and automotive_claim_id = $3`
	_, err = tx.Exec(updateQuery, claim.ClaimPaymentID, batch.BatchID, claim.ID)
	if err != nil {
		return nil, authNumber, errors.Wrap(err, "Database error udpating batch details for payment")
	}

	claimPayment, err := SubmitBillsToIntacct(ctx, tx, claim, batch, intacctAccountDetails)
	if err != nil {
		return nil, authNumber, err
	}

	return claimPayment, authNumber, nil
}

type paymentInfo struct {
	ID                  int             `db:"id"`
	AutomotiveClaimID   int             `db:"automotive_claim_id"`
	UnidataClaimNumber  int             `db:"unidata_claim_number"`
	ContractNumber      string          `db:"contract_number"`
	CheckNumber         string          `db:"check_number"`
	Amount              decimal.Decimal `db:"amount"`
	PaidDate            time.Time       `db:"paid_date"`
	ProductCode         string          `db:"product_code"`
	RO                  string          `db:"ro"`
	Estimate            decimal.Decimal `db:"estimate"`
	IntacctBillNumber   string          `db:"intacct_bill_number"`
	UnidataBillNumber   string          `db:"unidata_bill_number"`
	BillKey             int             `db:"bill_key"`
	UserID              string          `db:"owner_id"`
	PayType             string          `db:"pay_type"`
	AuthorizationNumber int             `db:"authorization_number"`
	ClaimType           string          `db:"claim_type"`
	SBRecordKey         string          `db:"sb_record_key"`
	Status              string          `db:"status"`
	Chargeback          bool            `db:"chargeback"`
}

// ReceiveFromIntacct function receives the auto claim paid info from intacct ap payment service
// the bill and payment request is generated by calling Intacct APIs
// This function will update the check details in automotive_claim_payments table, if only the appayment
// request is found in intacct and has a status 'Complete'
// This function is invoked by cmd/apreceive binary, which is scheduled to run as a cronjob on server
func ReceiveFromIntacct(ctx context.Context) (int, error) { // add failure note in claim
	pendingPayments := []paymentInfo{}

	// MNT payments are submitted as single batch with single bill so it needs different handling

	query := `select
				acp.id,
				acp.automotive_claim_id,
				coalesce(acp.amount, 0.0) amount,
				ac.unidata_claim_number,
				ac.product_code,
				ac.owner_id,
				ac.contract_number,
				ac.ro,
				ac.estimate,
				ac.claim_type,
				coalesce(ac.sb_record_key,'') sb_record_key,
				acp.intacct_bill_number,
				coalesce(acp.unidata_bill_number,'') unidata_bill_number,
				acp.bill_key,
				pay_type,
				coalesce(authorization_number,0) authorization_number
			from automotive_claims ac
			join automotive_claim_payments acp on ac.id = acp.automotive_claim_id
			where acp.is_complete = false
				and acp.bill_key is not null
				and ac.pay_type != $1
				and ac.product_code != $2
				and ac.date_of_claim_received > now() - interval '180 days'`

	checkCount := 0
	err := db.Get().Select(&pendingPayments, query, db.PayTypeCreditCard, db.ProductCodeMaintenance)
	if err != nil {
		if err == sql.ErrNoRows {
			return checkCount, nil
		}
		return checkCount, errors.Wrap(err, "Error loading Automotive maintenance claim payment from database.")
	}

	for _, payment := range pendingPayments {
		if payment.Amount.LessThan(decimal.Zero) {
			continue
		}
		claimPayments, claimCredits, err := intacctPaidInfo(ctx, payment.BillKey)
		if err != nil {
			log.Println("Error getting payment details for contractNumber: "+payment.ContractNumber, " Bill Number:", payment.BillKey, err)
			continue
		}

		tx, err := db.Get().Beginx()
		if err != nil {
			log.Println("Database error beginning transaction for Automotive claim payment update for contract ", err)
			continue
		}

		// Note : claimPayments is a list of checks generated for particular batch
		// Note : claimCredits is a list of credits applied on particular batch. Tip : When claim is reversed, reversed amount gets reduced from total vendor's payable amount.
		totalCreditAmount, err := updateClaimCredits(tx, claimPayments, claimCredits)
		if err != nil {
			_ = tx.Rollback()
			log.Println("Error updating claim credit details details for contractNumber: "+payment.ContractNumber, " Bill Number:", payment.BillKey, err)
			continue
		}

		// update claims payment check number in db
		totalPaidAmount, err := updateClaimCheckNumbers(tx, payment, claimPayments)
		if err != nil {
			_ = tx.Rollback()
			log.Println("Error updating check details for contractNumber: "+payment.ContractNumber, " Bill Number:", payment.BillKey, err)
			continue
		}

		//update payments table with payment info and update claims table with status
		if totalPaidAmount.Add(totalCreditAmount).Equal(payment.Estimate) {
			payment.Amount = payment.Estimate
			err := tx.Get(&payment, `select check_number, paid_date from automotive_claim_payment_checks where automotive_claim_payments_id = $1 order by paid_date limit 1`, payment.ID)
			if err != nil {
				_ = tx.Rollback()
				log.Println("Error getting check number and paid date before updating claim", payment.ContractNumber, err)
				continue
			}

			err = updateClaimPaid(tx, strconv.Itoa(payment.AutomotiveClaimID))
			if err != nil {
				_ = tx.Rollback()
				log.Println("Error updating paid info in automotive_claim_payments for claim ", strconv.Itoa(payment.AutomotiveClaimID), err)
				continue
			}
			acpQuery := `update automotive_claim_payments set is_complete = true, updated_at = now() at time zone 'utc' where id = $1`
			_, err = tx.Exec(acpQuery, payment.ID)
			if err != nil {
				_ = tx.Rollback()
				log.Println("Error updating Automotive claim paid info", err)
				continue
			}
			checkCount++
		}

		err = tx.Commit()
		if err != nil {
			log.Printf("Failed to commit transaction for contract %s and bill %d\n %+v",
				payment.ContractNumber, payment.BillKey, err)
			continue
		}
	}
	return checkCount, err
}

// ReceiveMCBatchesFromIntacct function receives the maintenance auto claim batch paid info from intacct ap payment service
// the bill and payment request is generated by calling Intacct APIs
// This function will update the check details in automotive_claim_payments table, if only the appayment
// request is found in intacct and has a status 'Complete'
// This function is invoked by cmd/apreceive binary, which is scheduled to run as a cronjob on server
func ReceiveMCBatchesFromIntacct(ctx context.Context) (int, error) {
	var pendingBatchPayments []struct {
		BillKey       int             `db:"bill_key"`
		BatchEstimate decimal.Decimal `db:"batch_estimate"`
		BillMemo      string          `db:"bill_memo"`
		Amount        decimal.Decimal `db:"amount"`
	}
	query := `select
                distinct (coalesce(acp.bill_key, 0)) as bill_key,
                sum(ac.estimate) as batch_estimate,
				bill_memo,
				sum(coalesce(acp.amount, 0)) as amount
			from automotive_claims ac
			join automotive_claim_payments acp on ac.id = acp.automotive_claim_id
			where acp.is_complete = false
				and ac.pay_type != $1
				and ac.product_code = $2
				and ac.date_of_claim_received > now() - interval '180 days'
			group by acp.bill_key, acp.bill_memo
			`
	checkCount := 0
	err := db.Get().Select(&pendingBatchPayments, query, db.PayTypeCreditCard, db.ProductCodeMaintenance)
	if err != nil {
		if err == sql.ErrNoRows {
			return checkCount, nil
		}
		return checkCount, errors.Wrap(err, "Error loading Automotive claim payment from database.")
	}

	for _, payment := range pendingBatchPayments {
		if payment.Amount.LessThan(decimal.Zero) {
			continue
		}
		// Note : batchPayments is a list of checks generated for particular batch
		// Note : batchCredits is a list of credits applied on particular batch. Tip : When claim is reversed, reversed amount gets reduced from total vendor's payable amount.
		batchPayments, batchCredits, err := intacctPaidInfo(ctx, payment.BillKey)
		if err != nil {
			log.Println("Error getting MC batch payment details for Bill Number:", payment.BillKey, err)
			continue
		}

		tx, err := db.Get().Beginx()
		if err != nil {
			log.Println("Database error beginning transaction for Automotive claim payment update for contract ", err)
			continue
		}

		// save reversed payment details
		_, err = updateClaimCredits(tx, batchPayments, batchCredits)
		if err != nil {
			_ = tx.Rollback()
			log.Println("Error updating reversed claim details details for Bill Number:", payment.BillKey, err)
			continue
		}

		// Get total payment made to vendor in this bill
		totalPaidAmount := decimal.Zero
		var batchChecks []string
		var batchLastPiadDate time.Time
		for _, payment := range batchPayments {
			totalPaidAmount = totalPaidAmount.Add(payment.Amount)
			batchChecks = append(batchChecks, payment.CheckNumber)
			batchLastPiadDate = payment.PaidDate
		}

		// update payment information for all claims from the batch
		paymentComplete := true
		batchClaims := []paymentInfo{}
		claimsQuery := `select
					automotive_claim_payments.id,
					automotive_claim_payments.automotive_claim_id,
					unidata_claim_number,
					automotive_claims.product_code,
					automotive_claims.owner_id,
					automotive_claims.contract_number,
					automotive_claims.ro,
					automotive_claims.status,
					-- if we have an amount for the claim payment, then use that as the paid amount
					-- otherwise use the claim estimate.
					coalesce(automotive_claim_payments.amount, automotive_claims.estimate) amount,

					automotive_claims.estimate,
					automotive_claims.claim_type,
					coalesce(automotive_claims.sb_record_key, '') sb_record_key,
					automotive_claim_payments.intacct_bill_number,
					coalesce(automotive_claim_payments.unidata_bill_number, '') unidata_bill_number,
					pay_type,
					coalesce(authorization_number, 0) authorization_number
			from automotive_claims
				join automotive_claim_payments on automotive_claims.id = automotive_claim_id
			where bill_key = $1`
		err = tx.Select(&batchClaims, claimsQuery, payment.BillKey)
		if err != nil {
			_ = tx.Rollback()
			log.Println("Error getting claim details for Bill Number:", payment.BillKey, err)
			continue
		}
		checks := strings.Join(batchChecks, ", ")

		for _, claim := range batchClaims {
			claim.CheckNumber = checks
			claim.PaidDate = batchLastPiadDate
			count := 0 // verify check number already updated
			amount := claim.Estimate
			if claim.Status == db.AutoClaimStatusWaitingForReversed || claim.Amount.Abs().GreaterThan(decimal.Zero) {
				amount = claim.Amount
			}

			err := tx.Get(&count, `select count(id) from automotive_claim_payment_checks where check_number = $1 and check_amount = $2 and automotive_claim_payments_id = $3`, checks, amount, claim.ID)
			if err != nil {
				_ = tx.Rollback()
				log.Println("Error verifying check details for contract: "+claim.ContractNumber, err)
				paymentComplete = false
				break
			}
			if count == 0 {
				_, err = tx.Exec(`insert into automotive_claim_payment_checks(automotive_claim_payments_id, check_amount, check_number, paid_date, updated_at) values ($1,$2,$3,$4,now() at time zone 'utc')`,
					claim.ID, claim.Amount, checks, claim.PaidDate)
				if err != nil {
					_ = tx.Rollback()
					log.Println("Database error inserting check details for Automotive claim payment update for contract "+claim.CheckNumber, err)
					paymentComplete = false
					break
				}

				err = updateClaimPaid(tx, strconv.Itoa(claim.AutomotiveClaimID))
				if err != nil {
					_ = tx.Rollback()
					paymentComplete = false
					log.Println("Error updating paid info in automotive_claim_payments for claim ", strconv.Itoa(claim.AutomotiveClaimID), err)
					break
				}

			}
		}
		if paymentComplete {
			acpQuery := `update automotive_claim_payments set is_complete = true, updated_at = now() at time zone 'utc' where bill_key = $1`
			_, err = tx.Exec(acpQuery, payment.BillKey)
			if err != nil {
				_ = tx.Rollback()
				log.Println("Error updating Automotive claim paid info", err)
				return checkCount, nil
			}
		}

		// The tx may have already been rolled back, but it is safer to check for ErrTxDone here
		// than to use a variable to track if the transaction has been rolled back and then only
		//  commit if it has not been rolled back.
		err = tx.Commit()
		if err != nil && err != sql.ErrTxDone {
			log.Println("Error committing transaction for automotive claims")
		}
		checkCount++
	}
	return checkCount, nil
}

func updateClaimCheckNumbers(tx *sqlx.Tx, claimPayment paymentInfo, checks []intacctPaymentDetails) (decimal.Decimal, error) {
	totalPaidAmount := decimal.Zero
	for _, check := range checks {
		count := 0 // verify check number already updated
		err := tx.Get(&count, `select count(id) from automotive_claim_payment_checks where check_number = $1 and check_amount = $2 and automotive_claim_payments_id = $3`, check.CheckNumber, check.Amount, claimPayment.ID)
		if err != nil {
			return totalPaidAmount, errors.Wrap(err, "Database error verifying existing check updates")
		}
		if count == 0 {
			_, err = tx.Exec(`insert into automotive_claim_payment_checks(automotive_claim_payments_id, check_amount, check_number, paid_date, updated_at) values ($1,$2,$3,$4,now() at time zone 'utc')`,
				claimPayment.ID, check.Amount, check.CheckNumber, check.PaidDate)
			if err != nil {
				return totalPaidAmount, errors.Wrap(err, "Database error inserting check details for Automotive claim payment update")
			}
		}
		totalPaidAmount = totalPaidAmount.Add(check.Amount)
	}
	return totalPaidAmount, nil
}

func updateClaimCredits(tx *sqlx.Tx, parentPaymentInfo []intacctPaymentDetails, credits []intacctReversedPaymentDetails) (decimal.Decimal, error) {
	totalCreditedAmount := decimal.Zero
	var err error
	for _, credit := range credits {
		// parent Check number
		parentCheckNumber := ""
		for _, payment := range parentPaymentInfo {
			if credit.ParentPaymentKey == payment.PaymentKey { // Check if check number is available for credited amount
				parentCheckNumber = payment.CheckNumber
				break
			}
		}
		// get negative claim payment ID
		negativeClaimPaymentInfo := paymentInfo{}
		_ = tx.Get(&negativeClaimPaymentInfo,
			`select automotive_claim_payments.id,
                        automotive_claim_payments.automotive_claim_id,
                        case when automotive_claim_payments.amount is null and not automotive_claims.chargeback then 0.0 
                            when automotive_claims.chargeback then automotive_claims.estimate 
                            else automotive_claim_payments.amount end,
                        automotive_claims.contract_number,
                        automotive_claims.product_code,
       					automotive_claims.chargeback
					from automotive_claim_payments join automotive_claims on automotive_claims.id = automotive_claim_payments.automotive_claim_id
					where bill_key = $1 and is_complete = false`, credit.BillKey)
		if err != nil && err != sql.ErrNoRows {
			return totalCreditedAmount, errors.Wrap(err, "Database error in getting negative claim payment id for Automotive claim payment update")
		}
		if parentCheckNumber != "" && negativeClaimPaymentInfo.ID != 0 && err == nil {
			count := 0 // verify check number already updated
			err := tx.Get(&count, `select count(id) from automotive_claim_payment_checks where check_number = $1 and check_amount = $2 and automotive_claim_payments_id = $3`, parentCheckNumber, credit.Amount.Neg(), negativeClaimPaymentInfo.ID)
			if err != nil {
				return totalCreditedAmount, errors.Wrap(err, "Database error verifying existing check updates")
			}
			if count == 0 {
				_, err = tx.Exec(`insert into automotive_claim_payment_checks(
                                            automotive_claim_payments_id,
                                            check_amount,
                                            check_number,
                                            paid_date,
                                            updated_at) values ($1,$2,$3,$4,now() at time zone 'utc')`, negativeClaimPaymentInfo.ID, credit.Amount.Neg(), parentCheckNumber, credit.PaidDate)
				if err != nil {
					return totalCreditedAmount, errors.Wrap(err, "Database error inserting check details for Automotive claim payment update")
				}
				// Update claim to unidata if payment is complete
				var totalPaidCheckAmount sql.NullFloat64
				err = tx.Get(&totalPaidCheckAmount, `select sum(check_amount) from automotive_claim_payment_checks where automotive_claim_payments_id = $1`, negativeClaimPaymentInfo.ID)
				if err != nil {
					return totalCreditedAmount, errors.Wrap(err, "Database error getting total check amount for reversed claim for contract "+negativeClaimPaymentInfo.ContractNumber)
				}
				totalPaid := decimal.NewFromFloat(totalPaidCheckAmount.Float64)
				if totalPaid.Equal(negativeClaimPaymentInfo.Amount) {
					negativeClaimPaymentInfo.CheckNumber = parentCheckNumber
					negativeClaimPaymentInfo.PaidDate = credit.PaidDate
					err = updateReversedClaimPaid(tx, strconv.Itoa(negativeClaimPaymentInfo.ID), strconv.Itoa(negativeClaimPaymentInfo.AutomotiveClaimID), "0", negativeClaimPaymentInfo.Chargeback)
					if err != nil {
						return totalCreditedAmount, errors.Wrap(err, "Database error updating total check amount for reversed claim for contract "+negativeClaimPaymentInfo.ContractNumber)
					}
				}
			}
		}
		totalCreditedAmount = totalCreditedAmount.Add(credit.Amount)
	}
	return totalCreditedAmount, nil
}

type intacctPaymentDetails struct {
	CheckNumber string
	Amount      decimal.Decimal
	PaidDate    time.Time
	PaymentKey  int
}

type intacctReversedPaymentDetails struct {
	Amount           decimal.Decimal
	PaidDate         time.Time
	BillKey          int
	ParentPaymentKey int
}

func intacctPaidInfo(ctx context.Context, billKey int) ([]intacctPaymentDetails, []intacctReversedPaymentDetails, error) {
	paymentKeys, err := intacct.GetPaymentKeys(ctx, billKey)
	if err != nil {
		return nil, nil, errors.Wrap(err, "Could not get payment key")
	}

	claimPayments := []intacctPaymentDetails{}
	claimCredits := []intacctReversedPaymentDetails{}
	for _, paymentKeyItem := range paymentKeys {
		if paymentKeyItem.State != intacct.BillPaymentStateComplete {
			return nil, nil, errors.Wrapf(intacct.ErrPaymentNotComplete, "Payment not complete for paymentKey %d and bill key %d", paymentKeyItem.PaymentKey, billKey)
		}
		query := " RECORDNO = " + strconv.Itoa(paymentKeyItem.PaymentKey)
		fields := "STATE, DOCUMENTNUMBER"
		requestXML, err := intacct.GetReadByQueryXML(query, "appayment", fields)
		if err != nil {
			return nil, nil, errors.Wrap(err, "Creating XML request for getting payment details failed")
		}
		response, err := intacct.Request(ctx, requestXML)
		if err != nil {
			return nil, nil, errors.Wrap(err, "Intacct Server communication error")
		}

		type apPaymentType struct {
			State          string `xml:"STATE"`
			DocumentNumber string `xml:"DOCUMENTNUMBER"`
		}
		type data struct {
			Count     int             `xml:"count,attr"`
			ApPayment []apPaymentType `xml:"appayment"`
		}
		type result struct {
			Status string `xml:"status"`
			Data   data   `xml:"data"`
		}
		type operation struct {
			Result result `xml:"result"`
		}

		resApPayment := struct {
			Operation operation `xml:"operation"`
		}{}

		apResBuf := bytes.NewBuffer(response)
		err = xml.NewDecoder(apResBuf).Decode(&resApPayment)
		if err != nil {
			return nil, nil, errors.Wrap(err, "Failed to decode getPaymentDetailsFromIntacct response")
		}

		if resApPayment.Operation.Result.Status != intacct.ResultSuccess {
			return nil, nil, errors.New("Failed to get appayment from Intacct")
		}
		if resApPayment.Operation.Result.Data.Count == 1 {
			apPayment := resApPayment.Operation.Result.Data.ApPayment[0]
			if apPayment.State != intacct.PaymentStateComplete {
				continue
			}
			claimPayment := intacctPaymentDetails{}
			claimPayment.CheckNumber = apPayment.DocumentNumber
			claimPayment.PaidDate = intacct.ToTime(paymentKeyItem.PaymentDate)
			claimPayment.Amount = paymentKeyItem.Amount
			claimPayment.PaymentKey = paymentKeyItem.PaymentKey
			claimPayments = append(claimPayments, claimPayment)
		} else {
			// payment details without check number
			claimCredit := intacctReversedPaymentDetails{}
			claimCredit.Amount = paymentKeyItem.Amount
			claimCredit.PaidDate = intacct.ToTime(paymentKeyItem.PaymentDate)
			claimCredit.BillKey = paymentKeyItem.PaymentKey // In case of negative claims, paymentkey is actual bill key of negative claim
			claimCredit.ParentPaymentKey = paymentKeyItem.ParentPaymentKey
			claimCredits = append(claimCredits, claimCredit)
		}
	}
	if len(claimPayments) == 0 && len(claimCredits) == 0 {
		return nil, nil, errors.Wrapf(intacct.ErrPaymentNotComplete, "Payment not complete for billKey"+strconv.Itoa(billKey))
	}

	return claimPayments, claimCredits, nil
}

func updateClaimPaid(tx *sqlx.Tx, autoClaimID string) error {
	laborQ := `select case when sum(approved) is null then 0.0 else sum(approved) end
		from automotive_claim_complaint_labors
		where automotive_claim_complaint_id in (select id from automotive_claim_complaints where automotive_claim_id = $1)`
	totalLabor := decimal.Zero
	err := tx.Get(&totalLabor, laborQ, autoClaimID)
	if err != nil {
		return errors.Wrap(err, "Error while getting total labor for claim id #"+autoClaimID)
	}

	partsQ := `select case when sum(approved) is null then 0.0 else sum(approved) end
		from automotive_claim_complaint_parts
		where automotive_claim_complaint_id in (select id from automotive_claim_complaints where automotive_claim_id = $1)`

	totalParts := decimal.Zero
	err = tx.Get(&totalParts, partsQ, autoClaimID)
	if err != nil {
		return errors.Wrap(err, "Error while getting total parts for claim id#"+autoClaimID)
	}

	var acQuery string
	if totalLabor.GreaterThan(decimal.Zero) || totalParts.GreaterThan(decimal.Zero) {
		acQuery = `update automotive_claims set status = $1, total_labor=$2, total_parts=$3 where id = $4`
		_, err = tx.Exec(acQuery, db.AutoClaimStatusCheckWritten, totalLabor, totalParts, autoClaimID)
	} else {
		acQuery = `update automotive_claims set status = $1 where id = $2`
		_, err = tx.Exec(acQuery, db.AutoClaimStatusCheckWritten, autoClaimID)
	}

	if err != nil {
		return errors.Wrap(err, "Error updating Auotomotive claim paid info")
	}
	return nil
}

func updateReversedClaimPaid(tx *sqlx.Tx, claimPaymentID, autoClaimID, refundClaimNumber string, isChargeBack bool) error {
	acpQuery := `update automotive_claim_payments set is_complete = true, refund_claim_number = $1, updated_at = now() at time zone 'utc' where id = $2`
	_, err := tx.Exec(acpQuery, refundClaimNumber, claimPaymentID)
	if err != nil {
		return errors.Wrap(err, "Error updating Automotive claim paid info")
	}

	acQuery := `update automotive_claims set status = $1 where id = $2`
	status := db.AutoClaimStatusCheckWritten
	if isChargeBack {
		status = db.AutoClaimStatusChargeback
	}
	_, err = tx.Exec(acQuery, status, autoClaimID)
	if err != nil {
		return errors.Wrap(err, "Error updating Auotomotive claim paid info")
	}
	return nil
}

func isPayableStatus(status string) bool {
	if status == db.AutoClaimStatusCheckWritten ||
		status == db.AutoClaimStatusWaitingForCheck ||
		status == db.AutoClaimStatusCCPaid ||
		status == db.AutoClaimStatusReversed ||
		status == db.AutoClaimStatusWaitingForReversed ||
		status == db.AutoClaimStatusAdjusted ||
		status == db.AutoClaimStatusWaitingForChargeback ||
		status == db.AutoClaimStatusChargeback ||
		status == db.AutoClaimStatusChargebackCollected {
		return true
	}
	return false
}

// Get the automotive claim payment information from database
func getPaymentDetailsByClaimID(ctx context.Context, claimID int, status string) ([]paymentPayload, []paymentPayload, error) {
	var paymentDetails []paymentPayload

	if !isPayableStatus(status) {
		return paymentDetails, nil, errors.New("Invalid payment request for automotive claim")
	}

	query := `select acp.id,
					acp.automotive_claim_id,
					case when max(automotive_intacct_batch_id) is null then 0 else max(automotive_intacct_batch_id) end as automotive_intacct_batch_id,
					authorization_number,
					intacct_bill_number,
					bill_key,
					bill_memo,
					estimate as amount,
					acp.amount payment_amount,
					acp.claim_payment_type
			from automotive_claim_payments acp
				join automotive_claims ac
					on acp.automotive_claim_id = ac.id
				left join automotive_intacct_batch_details aibd
					on acp.automotive_claim_id = aibd.automotive_claim_id
			where ac.id = $1
				and (ac.chargeback or aibd.amount > 0)
				and (ac.chargeback or acp.amount is null or acp.amount >= 0)
			group by acp.id, acp.automotive_claim_id, authorization_number, intacct_bill_number, bill_key, bill_memo, estimate`

	err := db.Get().SelectContext(ctx, &paymentDetails, query, claimID)
	if err != nil {
		return paymentDetails, nil, errors.Wrap(err, "Error loading Automotive claim payments from database.")
	}
	if len(paymentDetails) == 0 {
		return paymentDetails, nil, errors.New("The Automotive claim payment was not found")
	}

	// More than one payment suggests adjustment on the claim it can be either positive of negative
	if len(paymentDetails) > 1 {
		paymentDetails[0].PaymentAmount = decimal.NullDecimal{
			Decimal: getClaimPaymentAmount(paymentDetails[0].Amount, paymentDetails),
			Valid:   true,
		}
	} else {
		paymentDetails[0].PaymentAmount = decimal.NullDecimal{
			Decimal: paymentDetails[0].Amount,
			Valid:   true,
		}
	}

	for i, v := range paymentDetails {
		err = db.Get().Select(&paymentDetails[i].CheckDetails, `select check_number, check_amount as amount, paid_date from automotive_claim_payment_checks where automotive_claim_payments_id = $1`, v.ID)
		if err != nil {
			return paymentDetails, nil, errors.Wrap(err, "Error loading Automotive claim payments check details from database.")
		}

		// We want to get correct batch details only if we have more than one payment
		if len(paymentDetails) > 1 {
			var totalAmount decimal.Decimal
			if len(paymentDetails[i].CheckDetails) > 0 {
				for _, v := range paymentDetails[i].CheckDetails {
					totalAmount = totalAmount.Add(v.Amount)
				}
			} else {
				// We need to get amount from claim payments
				var amount decimal.NullDecimal
				err = db.Get().Get(&amount, `select amount from automotive_claim_payments where id = $1`, v.ID)
				if err != nil {
					return paymentDetails, nil, errors.Wrap(err, "Error loading Automotive claim payments details from database.")
				}
				if amount.Valid {
					totalAmount = amount.Decimal
				}
			}

			batchQuery := `select automotive_intacct_batch_id 
							from automotive_claim_payments acp
								left join automotive_claim_payment_checks acpc
									on acp.id = acpc.automotive_claim_payments_id
								join automotive_intacct_batch_details aibd
    								on (acp.automotive_claim_id = aibd.automotive_claim_id 
										and aibd.amount =  coalesce(acp.amount, acpc.check_amount))
							where acp.automotive_claim_id = $1
								and aibd.amount = $2`
			err = db.Get().Get(&paymentDetails[i].AutomotiveIntacctBatchID, batchQuery, v.AutomotiveClaimID, totalAmount)
			if err != nil && err != sql.ErrNoRows {
				return paymentDetails, nil, errors.Wrap(err, "Error loading Automotive claim payments batch details from database.")
			}
			if err == sql.ErrNoRows {
				paymentDetails[i].AutomotiveIntacctBatchID = 0
			}
		}
	}

	// Get negative claims, if any
	negativeClaims := []paymentPayload{}
	err = db.Get().Select(&negativeClaims, `select acp.id,
		acp.authorization_number,
		acp.amount,
		acp.bill_memo,
		acp.intacct_bill_number,
		acp.batch_key,
		coalesce(aibd.automotive_intacct_batch_id, 0) as automotive_intacct_batch_id
		from automotive_claim_payments acp
		left join automotive_claim_payment_checks acpc
			on acp.id = acpc.automotive_claim_payments_id
		left join automotive_intacct_batch_details aibd 
			on (acp.automotive_claim_id = aibd.automotive_claim_id
				and aibd.amount =  coalesce(acp.amount, acpc.check_amount))
		where acp.automotive_claim_id = $1
			and acp.amount < 0`,
		claimID)
	if err != nil && err != sql.ErrNoRows {
		return paymentDetails, nil, errors.Wrap(err, "Database error in getting automotive claim negative payment data")
	}

	var processed []paymentPayload
	claimProcessed := func(current paymentPayload) (bool, int) {
		for i, v := range processed {
			if v.ID == current.ID {
				return true, i
			}
		}
		processed = append(processed, current)
		return false, len(processed) - 1
	}

	for _, nClaim := range negativeClaims {
		ok, index := claimProcessed(nClaim)
		if ok {
			continue
		}
		err = db.Get().Select(&processed[index].CheckDetails, `select check_number, check_amount as amount, paid_date from automotive_claim_payment_checks where automotive_claim_payments_id = $1`, nClaim.ID)
		if err != nil {
			return paymentDetails, nil, errors.Wrap(err, "Error loading Automotive claim payments check details from database.")
		}
	}
	return paymentDetails, processed, nil
}

func getClaimPaymentAmount(currentAmount decimal.Decimal, claimPayments []paymentPayload) decimal.Decimal {
	for i, v := range claimPayments {
		// For old claims we know 1st payment will never be adjustment
		if (v.ClaimPaymentType.Valid && v.ClaimPaymentType.String == db.AutoClaimPaymentType) || i == 0 {
			continue
		}
		currentAmount = currentAmount.Sub(v.PaymentAmount.Decimal)
	}
	return currentAmount
}

type paymentPayload struct {
	ID                       int                 `json:"id" db:"id"`
	AuthorizationNumber      int                 `json:"authorization_number" db:"authorization_number"`
	AutomotiveClaimID        int                 `json:"automotive_claim_id" db:"automotive_claim_id"`
	BatchKey                 null.Int            `json:"batch_key" db:"batch_key"`
	BillKey                  null.Int            `json:"bill_key" db:"bill_key"`
	BillMemo                 null.String         `json:"bill_memo" db:"bill_memo"`
	AutomotiveIntacctBatchID int                 `json:"automotive_intacct_batch_id" db:"automotive_intacct_batch_id"`
	BillNumber               null.String         `json:"bill_number" db:"intacct_bill_number"`
	Amount                   decimal.Decimal     `json:"amount" db:"amount"`
	PaymentAmount            decimal.NullDecimal `json:"payment_amount" db:"payment_amount"`
	ClaimPaymentType         null.String         `json:"claim_payment_type" db:"claim_payment_type"`
	CheckDetails             []struct {
		CheckNumber string          `json:"check_number" db:"check_number"`
		Amount      decimal.Decimal `json:"amount" db:"amount"`
		PaidDate    time.Time       `json:"paid_date" db:"paid_date"`
	} `json:"check_details"`
	RefundClaimNumber int `json:"refund_claim_number" db:"refund_claim_number"`
}

// ClaimPayment returns the payment details for the automotive claim
func ClaimPayment(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	ctx := req.Context()
	claim := struct {
		ID     int    `json:"id" db:"id"`
		Status string `json:"status" db:"status"`
	}{}

	err := db.Get().Get(&claim, `select id, status from automotive_claims where id = $1`, chi.URLParam(req, "id"))
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage(err, "Automotive payment not found", nil)
		}
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error in getting automotive claim payment data", nil)
	}

	paymentDetails, negativeClaims, err := getPaymentDetailsByClaimID(ctx, claim.ID, claim.Status)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Failed to get payment details", nil)
	}

	return http.StatusOK, map[string]interface{}{"auto_claim_payment": paymentDetails, "negative_claim_payments": negativeClaims}
}

// ClaimReverse processes reversed automotive claims
//   - Creates intacct bill request with negative amount
func ClaimReverse(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	ctx := req.Context()
	txn := newrelic.FromContext(ctx)

	var claimToReverse struct {
		ID             int             `json:"id"`
		ReversalAmount decimal.Decimal `json:"negative_amount"`
		Reason         string          `json:"reason"`
	}

	dec := json.NewDecoder(req.Body)
	err := dec.Decode(&claimToReverse)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Malformed data for claim reverse", nil)
	}

	claim, err := ClaimSelect(txn, req, strconv.Itoa(claimToReverse.ID))
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Invalid claim id", nil)
	}

	originalEstimate := claim.Estimate
	// validate negative claim
	// It count total number of negative claims already applied on contract claim
	negativeClaimsDetail := struct {
		PrevNegativeAmtTotal sql.NullFloat64 `db:"prev_negative_amt_total"`
		NegativeClaimsCount  int             `db:"negative_claims_count"`
	}{}

	err = db.Get().GetContext(ctx, &negativeClaimsDetail, `select sum(amount) as prev_negative_amt_total, count(*) as negative_claims_count from automotive_claim_payments where automotive_claim_id = $1 and amount < 0`, claim.ID)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error in getting automotive claim negative payment data", nil)
	}

	// Old negative claim values + new negative claim value
	totalNegativeValue := claimToReverse.ReversalAmount.Neg().Add(decimal.NewFromFloat(negativeClaimsDetail.PrevNegativeAmtTotal.Float64))
	if claim.Estimate.LessThan(totalNegativeValue.Neg()) {
		return http.StatusBadRequest, handlers.ErrorMessage(errors.New("Invalide reversal amount"), "Total reversal amount is exceeding actual claim amount ", nil)
	}

	var completeReversal bool
	if claim.Estimate.Abs().Equal(totalNegativeValue.Abs()) {
		completeReversal = true
	}

	claim.Estimate = claimToReverse.ReversalAmount.Neg()
	if claim.ProductCode == db.ProductCodeDrivePur {
		intacctCustomerID, err := validateDP(ctx, claim.ContractStoreID)
		if err != nil || intacctCustomerID == "" {
			return http.StatusInternalServerError, handlers.ErrorMessage(errors.New("could not get customer_id for drive_pur claim "+err.Error()), "Failed to get intacct customer id for dp claim", nil)
		}
		claim.IntacctCustID = intacctCustomerID
	}

	tx, err := db.Get().Beginx()
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error beginning transaction for Automotive claim reversal payment update", nil)
	}

	// Update claim status
	claim.Status = db.AutoClaimStatusReversed
	_, err = tx.ExecContext(ctx, `update automotive_claims set status = $1 where id = $2`, claim.Status, claim.ID)
	if err != nil {
		_ = tx.Rollback()
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error updating claim status", nil)
	}

	// Add note on successful new reversal creation
	note := RecordNotePayload{}
	note.AutomotiveClaimID = claim.ID
	note.IsManual = false
	note.CreatedByUserID = user.ID
	note.NotesText = "Reversed claim with amount : $" + claim.Estimate.String()
	_, err = InsertRecordNote(ctx, &note, tx)
	if err != nil {
		_ = tx.Rollback()
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Failed to add note", nil)
	}

	// Claim updated
	err = ClaimUpdated(ctx, tx, claim.ID, user.ID)
	if err != nil {
		_ = tx.Rollback()
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error inserting automotive_claim_updates", nil)
	}

	// Update Connect contract status
	if claim.ProductCode == db.ProductCodeMaintenance {
		if originalEstimate.LessThanOrEqual(totalNegativeValue.Abs()) {
			var c []claimDB
			query := `select ac.id, ac.date_of_claim_received claim_date, ac.ro repair_order_number,
					case when ac.status in('CheckWritten', 'Reversed', 'CCPaid', 'Adjusted') 
					then coalesce(acp.amount, ac.estimate) else ac.estimate end claim_amount, 
					ac.status claim_status, coalesce(acp.amount, ac.estimate) as authorized_amount
				from automotive_claims ac 
					left join automotive_claim_complaints acc on acc.automotive_claim_id = ac.id
					left join automotive_claim_payments acp on ac.id = acp.automotive_claim_id
					left join automotive_claim_payment_checks acpc on acp.id = acpc.automotive_claim_payments_id
				where contract_number = $1 
					and product_code = $2 
					and ac.status not in ('Deactivated', 'Denied')
				group by ac.id, ac.date_of_claim_received, ac.ro_mileage, ac.ro, ac.estimate, 
					acpc.paid_date, ac.advisor, ac.status, acpc.check_number, acp.amount`

			err = db.Get().SelectContext(ctx, &c, query, claim.ContractNumber, claim.ProductCode)
			if err != nil {
				_ = tx.Rollback()
				handlers.ReportError(req, errors.Wrap(err, "error getting claim details"))
				return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error getting claim details", nil)
			}
			usedCoupons := getUsedCouponCount(c)

			var removeCurrentClaim bool
			// We dont have automotive claims entry yet for reversed claim so we check full reversal
			for k := range usedCoupons {
				// If we have claim to be reversed as counted in coupon
				// then check if its full reversal
				if k == claim.ID && completeReversal {
					removeCurrentClaim = true
					break
				}
			}

			couponCount := len(usedCoupons)
			if removeCurrentClaim {
				// We need to remove current claim as it is full reversal
				couponCount = len(usedCoupons) - 1
			}

			err = handlers.UpdateContractStatus(txn, req, claim.ContractNumber, couponCount)
			if err != nil {
				_ = tx.Rollback()
				handlers.ReportError(req, errors.Wrap(err, "error updating contract status"))
				return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error updating contract status", nil)
			}
		}
	}
	billMemo := claim.ContractNumber + " " + claim.CustomerName + " " + claimToReverse.Reason
	insertQuery := `insert into automotive_claim_payments(automotive_claim_id, amount, bill_memo, updated_at, claim_payment_type) values($1, $2, $3, now() at time zone 'utc', $4)`
	_, err = tx.ExecContext(ctx, insertQuery, claim.ID, claim.Estimate, billMemo, db.AutoClaimNegativePaymentType)
	if err != nil {
		_ = tx.Rollback()
		err = errors.Wrap(err, "error inserting claim payments")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error submitting negative claim", nil)
	}

	// Add note on successful new reversal creation
	note = RecordNotePayload{}
	note.AutomotiveClaimID = claim.ID
	note.IsManual = false
	note.CreatedByUserID = claim.OwnerID
	note.NotesText = db.AutomotiveRecordNoteDescription[db.AutoClaimStatusReversed]
	_, err = InsertRecordNote(ctx, &note, tx)
	if err != nil {
		_ = tx.Rollback()
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Failed to add note", nil)
	}

	err = tx.Commit()
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error committing transaction for Automotive claim reversal payment update", nil)
	}

	return http.StatusOK, map[string]interface{}{"id": claimToReverse.ID}
}

// ClaimAdjustment processes reversed automotive claims
//   - Creates intacct bill request with adjustment amount
func ClaimAdjustment(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	ctx := req.Context()
	txn := newrelic.FromContext(ctx)

	var claimToAdjust struct {
		ID               int             `json:"id"`
		AdjustmentAmount decimal.Decimal `json:"adjustment_amount"`
		Reason           string          `json:"reason"`
	}

	dec := json.NewDecoder(req.Body)
	err := dec.Decode(&claimToAdjust)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Malformed data for claim reverse", nil)
	}

	claim, err := ClaimSelect(txn, req, strconv.Itoa(claimToAdjust.ID))
	if err != nil {
		handlers.ReportError(req, err)
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Invalid claim id", nil)
	}

	totalPaidAmount := claimToAdjust.AdjustmentAmount.Add(claim.Estimate)
	tx, err := db.Get().Beginx()
	if err != nil {
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error beginning transaction for Automotive claim reversal payment update", nil)
	}

	claim.Estimate = claimToAdjust.AdjustmentAmount

	// Update claim status
	claim.Status = db.AutoClaimStatusAdjusted
	_, err = tx.ExecContext(ctx, `update automotive_claims set status = $1 where id = $2`, claim.Status, claim.ID)
	if err != nil {
		_ = tx.Rollback()
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error updating claim status", nil)
	}

	// Add note on successful new claim creation
	note := RecordNotePayload{}
	note.AutomotiveClaimID = claim.ID
	note.IsManual = false
	note.CreatedByUserID = user.ID
	note.NotesText = "Adjusted claim with amount : $" + claim.Estimate.String()
	_, err = InsertRecordNote(ctx, &note, tx)
	if err != nil {
		_ = tx.Rollback()
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Failed to add note", nil)
	}

	// Claim updated
	err = ClaimUpdated(ctx, tx, claim.ID, user.ID)
	if err != nil {
		_ = tx.Rollback()
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error inserting automotive_claim_updates", nil)
	}

	billMemo := claim.ContractNumber + " " + claim.CustomerName + " " + claimToAdjust.Reason
	insertQuery := `insert into automotive_claim_payments(automotive_claim_id, amount, bill_memo, updated_at, claim_payment_type) values($1, $2, $3, now() at time zone 'utc', $4)`
	_, err = tx.ExecContext(ctx, insertQuery, claim.ID, claim.Estimate, billMemo, db.AutoClaimAdjustPaymentType)
	if err != nil {
		_ = tx.Rollback()
		err = errors.Wrap(err, "error inserting claim payments")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error submitting negative claim", nil)
	}

	err = updateClaims(ctx, tx, totalPaidAmount, claim.ID)
	if err != nil {
		_ = tx.Rollback()
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error updating automotie claims.", nil)
	}
	err = tx.Commit()
	if err != nil {
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error committing transaction for Automotive claim reversal payment update", nil)
	}

	return http.StatusOK, map[string]interface{}{"id": claimToAdjust.ID}
}

func updateClaims(ctx context.Context, tx *sqlx.Tx, amount decimal.Decimal, claimID int) error {
	claim := struct {
		Estimate decimal.Decimal `db:"estimate"`
		Status   string          `db:"status"`
		ID       int             `db:"id"`
	}{
		Estimate: amount,
		Status:   db.AutoClaimStatusAdjusted,
		ID:       claimID,
	}
	query := `update automotive_claims
			set estimate =:estimate,
				status =:status
			where id =:id`

	stmt, err := tx.PrepareNamedContext(ctx, query)
	if err != nil {
		return errors.Wrap(err, "Error updating Claim, PrepareNamed failed")
	}
	defer func() { _ = stmt.Close() }()
	_, err = stmt.ExecContext(ctx, claim)
	if err != nil {
		return errors.Wrap(err, "Error updating Claim, database error")
	}

	return nil
}
