package auto

import (
	"net/http"
	"net/http/httptest"
	"regexp"
	"testing"
	"time"

	"phizz/db"
	"phizz/handlers"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/go-chi/chi"
	"github.com/jmoiron/sqlx"
)

func TestReconciliationsIndex(t *testing.T) {
	mdb, mock, err := sqlmock.New()
	if err != nil {
		t.Fatal(err)
	}
	dbx := sqlx.NewDb(mdb, "sqlmock")
	db.DB = dbx
	q := regexp.QuoteMeta

	selectQuery := `select
		claim.id as id,
		claim.estimate as estimate,
		claim.contract_number as contract_number,
		claim.ro as ro,
		case when payment.amount is null then 0.0 else payment.amount end as statement_amount,
		payment.paid_date as date_of_payment_received,
		facility.name as facility_name,
		facility.vendor_id as vendor_id
	from automotive_claims claim
		join automotive_claim_payments payment on claim.id = payment.automotive_claim_id
		join automotive_facilities facility on claim.facility_id = facility.id
	where claim.is_reconciled = false
		and claim.status = 'CCPaid' order by date_of_payment_received desc`

	mock.ExpectPrepare(q(selectQuery))
	mock.ExpectQuery(q(selectQuery)).
		WillReturnRows(sqlmock.NewRows([]string{"id", "estimate", "contract_number", "ro", "statement_amount", "date_of_payment_received", "facility_name", "vendor_id"}).
			AddRow(1, "100", "954535", 121212, "100", time.Time{}, "FAC", "VEN"))

	countQuery := `select count(*)  from automotive_claims claim
		join automotive_claim_payments payment on claim.id = payment.automotive_claim_id
		join automotive_facilities facility on claim.facility_id = facility.id  
	where claim.is_reconciled = false 
		and claim.status = 'CCPaid' `

	mock.ExpectPrepare(q(countQuery))
	mock.ExpectQuery(q(countQuery)).
		WillReturnRows(sqlmock.NewRows([]string{"count"}).
			AddRow(1))

	req, err := http.NewRequest("GET", "/api/automotive-claims/reconciliations", nil)
	if err != nil {
		t.Fatal(err)
	}

	rr := httptest.NewRecorder()
	handler := http.HandlerFunc(handlers.APIHandler(ReconciliationsIndex))

	r := chi.NewRouter()
	r.HandleFunc("/api/automotive-claims/reconciliations", handler)
	r.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v",
			status, http.StatusOK)
	}

	expected := `{"counts":1,"reconciliations":[{"id":1,"estimate":"100","date_of_payment_received":"0001-01-01T00:00:00Z","vendor_id":{"String":"VEN","Valid":true},"facility_name":"FAC","contract_number":"954535","ro":"121212","statement_amount":"100","is_reconciled":false}]}`
	if rr.Body.String() != expected {
		t.Errorf("handler returned unexpected body: got %v want %v",
			rr.Body.String(), expected)
	}

	if err := mock.ExpectationsWereMet(); err != nil {
		t.Errorf("there were unfulfilled expectations: %s", err)
	}
}

func TestReconciliationsHistory(t *testing.T) {
	mdb, mock, err := sqlmock.New()
	if err != nil {
		t.Fatal(err)
	}
	dbx := sqlx.NewDb(mdb, "sqlmock")
	db.DB = dbx
	q := regexp.QuoteMeta

	selectQuery := `select
		claim.id as id,
		claim.estimate as estimate,
		claim.contract_number as contract_number,
		claim.ro as ro,
		case when payment.amount is null then 0.0 else payment.amount end as statement_amount,
		payment.paid_date as date_of_payment_received,
		facility.name as facility_name,
		facility.vendor_id as vendor_id
	from automotive_claims claim
		join automotive_claim_payments payment on claim.id = payment.automotive_claim_id
		join automotive_facilities facility on claim.facility_id = facility.id
	where claim.is_reconciled = true
		and claim.status = 'CCPaid' order by date_of_payment_received desc`

	mock.ExpectPrepare(q(selectQuery))
	mock.ExpectQuery(q(selectQuery)).
		WillReturnRows(sqlmock.NewRows([]string{"id", "estimate", "contract_number", "ro", "statement_amount", "date_of_payment_received", "facility_name", "vendor_id"}).
			AddRow(1, "100", "954535", 121212, "100", time.Time{}, "FAC", "VEN"))

	countQuery := `select count(*)  from automotive_claims claim
		join automotive_claim_payments payment on claim.id = payment.automotive_claim_id
		join automotive_facilities facility on claim.facility_id = facility.id  
	where claim.is_reconciled = true 
		and claim.status = 'CCPaid' `

	mock.ExpectPrepare(q(countQuery))
	mock.ExpectQuery(q(countQuery)).
		WillReturnRows(sqlmock.NewRows([]string{"count"}).
			AddRow(1))

	req, err := http.NewRequest("GET", "/api/automotive-claims/reconciliations/history", nil)
	if err != nil {
		t.Fatal(err)
	}

	rr := httptest.NewRecorder()
	handler := http.HandlerFunc(handlers.APIHandler(ReconciliationsHistory))

	r := chi.NewRouter()
	r.HandleFunc("/api/automotive-claims/reconciliations/history", handler)
	r.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v",
			status, http.StatusOK)
	}

	expected := `{"counts":1,"reconciliations":[{"id":1,"estimate":"100","date_of_payment_received":"0001-01-01T00:00:00Z","vendor_id":{"String":"VEN","Valid":true},"facility_name":"FAC","contract_number":"954535","ro":"121212","statement_amount":"100","is_reconciled":false}]}`
	if rr.Body.String() != expected {
		t.Errorf("handler returned unexpected body: got %v want %v",
			rr.Body.String(), expected)
	}

	if err := mock.ExpectationsWereMet(); err != nil {
		t.Errorf("there were unfulfilled expectations: %s", err)
	}
}
