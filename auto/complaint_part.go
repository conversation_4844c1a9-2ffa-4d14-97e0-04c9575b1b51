package auto

import (
	"database/sql"
	"encoding/json"
	"net/http"
	"strconv"
	"strings"

	"phizz/db"
	"phizz/handlers"

	"github.com/go-chi/chi"
	"github.com/jmoiron/sqlx"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
)

type partPayload struct {
	ID                         int             `json:"id" db:"id"`
	AutomotiveClaimComplaintID int             `json:"automotive_claim_complaint_id" db:"automotive_claim_complaint_id"`
	PartNumber                 string          `json:"part_number" db:"part_number"`
	Description                string          `json:"description" db:"description"`
	Quantity                   int             `json:"quantity" db:"quantity"`
	MSRP                       decimal.Decimal `json:"msrp" db:"msrp"`
	Cost                       decimal.Decimal `json:"cost" db:"cost"`
	Requested                  decimal.Decimal `json:"requested" db:"requested"`
	Approved                   decimal.Decimal `json:"approved" db:"approved"`
	Notes                      string          `json:"notes" db:"notes"`
}

// PartCreate creates a new part in complaint
func PartCreate(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	complaintIDStr := chi.URLParam(req, "complaint_id")
	complaintID, err := strconv.Atoi(complaintIDStr)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Number conversion error for complaintID", nil)
	}
	complaintExist, err := complaintExists(complaintID)
	if !complaintExist || err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Could not get complaint", nil)
	}
	part, err := partFromReq(req)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Malformed Part data for create.", nil)
	}
	part.AutomotiveClaimComplaintID = complaintID
	cleanPart(part)
	formErrors, err := validatePart(part)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(errors.Wrap(err, "Error validating Part"),
			"An error occurred validating the form values.", nil)
	}

	if len(formErrors) > 0 {
		return http.StatusBadRequest, handlers.ErrorMessage(nil, "Form validations errors.",
			map[string]interface{}{"errors": formErrors})
	}

	tx, err := db.Get().Beginx()
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error beginning transaction for Auto claim update", nil)
	}

	partID, err := insertPart(tx, part)
	if err != nil {
		_ = tx.Rollback()
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error inserting Part", nil)
	}

	err = tx.Commit()
	if err != nil {
		err = errors.Wrap(err, "Database error committing transaction for Auto claim update")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error updating Auto claim", nil)
	}

	return http.StatusOK, map[string]interface{}{"part_id": partID}
}

func insertPart(tx *sqlx.Tx, part *partPayload) (int, error) {
	insertQuery := `insert into automotive_claim_complaint_parts(automotive_claim_complaint_id, part_number, description,
	 quantity, cost, msrp, requested, approved, notes)
	 values (:automotive_claim_complaint_id,:part_number, :description, :quantity, :cost, :msrp, :requested, :approved, :notes) returning id`
	id := 0

	stmt, err := tx.PrepareNamed(insertQuery)
	if err != nil {
		return id, errors.Wrap(err, "An error occurred in PrepareNamed function while adding Part.")
	}
	defer func() { _ = stmt.Close() }()

	err = stmt.Get(&id, part)
	if err != nil {
		return id, errors.Wrap(err, "An error occurred while trying to add the Part to the database.")
	}

	return id, err
}

func partFromReq(req *http.Request) (*partPayload, error) {
	part := partPayload{}

	dec := json.NewDecoder(req.Body)
	err := dec.Decode(&part)

	return &part, errors.Wrap(err, "decoding Part request failed")
}

func cleanPart(part *partPayload) {
	part.Description = strings.TrimSpace(part.Description)
	part.Notes = strings.TrimSpace(part.Notes)
	part.PartNumber = strings.TrimSpace(part.PartNumber)
}

func validatePart(part *partPayload) (map[string]string, error) {
	formErrors := map[string]string{}

	// TODO
	return formErrors, nil
}

func parts(complaintID int) ([]partPayload, error) {
	exists, err := complaintExists(complaintID)
	if err != nil {
		return nil, errors.Wrap(err, "Error getting complaint from database")
	}
	if !exists {
		return nil, errors.Wrap(err, "Complaint does not exist")
	}

	ids := []int{}
	err = db.Get().Select(&ids, `select id from automotive_claim_complaint_parts where automotive_claim_complaint_id = $1 order by id asc`, complaintID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, errors.Wrap(err, "error loading parts list ")
	}

	parts := make([]partPayload, len(ids))
	for i, id := range ids {
		part, err := partByID(id)
		if err != nil {
			return parts, errors.Wrap(err, "Error getting parts data")
		}
		parts[i] = *part
	}

	return parts, nil
}

// PartShow returns detail of parts for given part_od
func PartShow(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	partIDStr := chi.URLParam(req, "part_id")
	partID, err := strconv.Atoi(partIDStr)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(nil, "Number conversion error for partID", nil)
	}
	part, err := partByID(partID)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Could not get part", nil)
	}
	if part == nil {
		return http.StatusNotFound, handlers.ErrorMessage(err, "Part not found", nil)
	}
	return http.StatusOK, map[string]interface{}{"part": part}
}

func partByID(ID int) (*partPayload, error) {
	partFromDB := struct {
		ID                         int                 `json:"id" db:"id"`
		AutomotiveClaimComplaintID int                 `json:"automotive_claim_complaint_id" db:"automotive_claim_complaint_id"`
		PartNumber                 sql.NullString      `json:"part_number" db:"part_number"`
		Description                sql.NullString      `json:"description" db:"description"`
		Quantity                   int                 `json:"quantity" db:"quantity"`
		Cost                       decimal.Decimal     `json:"cost" db:"cost"`
		MSRP                       decimal.NullDecimal `json:"msrp" db:"msrp"`
		Requested                  decimal.NullDecimal `json:"requested" db:"requested"`
		Approved                   decimal.NullDecimal `json:"approved" db:"approved"`
		Notes                      sql.NullString      `json:"notes" db:"notes"`
	}{}

	err := db.Get().Unsafe().Get(&partFromDB, `select * from automotive_claim_complaint_parts where id = $1`, ID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, errors.Wrap(err, "error loading parts")

	}
	part := partPayload{}

	part.ID = partFromDB.ID
	part.AutomotiveClaimComplaintID = partFromDB.AutomotiveClaimComplaintID

	if partFromDB.PartNumber.Valid {
		part.PartNumber = partFromDB.PartNumber.String
	}
	if partFromDB.Description.Valid {
		part.Description = partFromDB.Description.String
	}
	part.Quantity = partFromDB.Quantity
	part.Cost = partFromDB.Cost
	if partFromDB.MSRP.Valid {
		part.MSRP = partFromDB.MSRP.Decimal
	}
	if partFromDB.Requested.Valid {
		part.Requested = partFromDB.Requested.Decimal
	}
	if partFromDB.Approved.Valid {
		part.Approved = partFromDB.Approved.Decimal
	}
	if partFromDB.Notes.Valid {
		part.Notes = partFromDB.Notes.String
	}
	return &part, nil
}

func updatePart(tx *sqlx.Tx, part *partPayload) error {

	query := `update automotive_claim_complaint_parts
    set part_number = :part_number,
    description = :description,
    quantity = :quantity,
    cost = :cost,
    msrp = :msrp,
    requested = :requested,
    approved = :approved,
    notes = :notes
    where id = :id`

	stmt, err := tx.PrepareNamed(query)
	if err != nil {
		return errors.Wrap(err, "Error updating Part, PrepareNamed failed")
	}
	defer func() { _ = stmt.Close() }()

	_, err = stmt.Exec(part)
	if err != nil {
		return errors.Wrap(err, "Error updating Part, database error")
	}
	return nil
}

// PartDelete deletes part for given part_id
func PartDelete(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	id := 0
	err := db.Get().Get(&id, `select id from automotive_claim_complaint_parts where id = $1`, chi.URLParam(req, "part_id"))
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage(err, "Part not found", nil)
		}
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "error loading parts", nil)
	}
	_, err = db.Get().Exec(`delete from automotive_claim_complaint_parts where id = $1`, id)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Could not delete part", nil)
	}
	return http.StatusOK, map[string]interface{}{"id": id}
}

func deletePartsByComplaintID(tx *sqlx.Tx, complaintID int) error {
	_, err := tx.Exec(`delete from automotive_claim_complaint_parts where automotive_claim_complaint_id = $1`, complaintID)
	if err != nil {
		return errors.Wrap(err, "Could not delete parts")
	}
	return nil
}
