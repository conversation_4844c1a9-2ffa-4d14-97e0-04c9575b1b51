package auto

import (
	"context"
	"database/sql"
	"encoding/json"
	"net/http"
	"strconv"
	"strings"

	"phizz/db"
	"phizz/handlers"

	"github.com/go-chi/chi"
	"github.com/jmoiron/sqlx"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
)

type rentalPayload struct {
	ID                         int             `json:"id" db:"id"`
	AutomotiveClaimComplaintID int             `json:"automotive_claim_complaint_id" db:"automotive_claim_complaint_id"`
	Description                string          `json:"description" db:"description"`
	Requested                  decimal.Decimal `json:"requested" db:"requested"`
	Approved                   decimal.Decimal `json:"approved" db:"approved"`
	Notes                      string          `json:"notes" db:"notes"`
}

// RentalCreate creates a new rental in complaint
func RentalCreate(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	ctx := req.Context()

	complaintIDStr := chi.URLParam(req, "complaint_id")
	complaintID, err := strconv.Atoi(complaintIDStr)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error converting complaint id"))
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Number conversion error for complaintID", nil)
	}

	complaintExist, err := complaintExists(complaintID)
	if !complaintExist || err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error getting complaint"))
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Could not get complaint", nil)
	}

	rental, err := rentalFromReq(req)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "malformed data"))
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Malformed Rental data for create.", nil)
	}
	rental.AutomotiveClaimComplaintID = complaintID
	cleanRentals(rental)

	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error creating database transaction"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error beginning transaction for Auto claim update", nil)
	}

	rentalID, err := insertRental(ctx, tx, rental)
	if err != nil {
		_ = tx.Rollback()
		handlers.ReportError(req, errors.Wrap(err, "error inserting rental details in database"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error inserting Rental", nil)
	}

	err = tx.Commit()
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "database error committing transaction for Auto claim update"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error updating Auto claim", nil)
	}

	return http.StatusOK, map[string]interface{}{"rental_id": rentalID}
}

func insertRental(ctx context.Context, tx *sqlx.Tx, rental *rentalPayload) (int, error) {
	insertQuery := `insert into automotive_claim_complaint_rentals(automotive_claim_complaint_id, description, requested, approved, notes)
	 values (:automotive_claim_complaint_id, :description, :requested, :approved, :notes) returning id`
	id := 0

	stmt, err := tx.PrepareNamedContext(ctx, insertQuery)
	if err != nil {
		return id, errors.Wrap(err, "an error occurred in PrepareNamed function while adding Context.")
	}
	defer func() { _ = stmt.Close() }()

	err = stmt.GetContext(ctx, &id, rental)
	if err != nil {
		return id, errors.Wrap(err, "an error occurred while trying to add the Part to the database.")
	}

	return id, err
}

func rentalFromReq(req *http.Request) (*rentalPayload, error) {
	rental := rentalPayload{}

	dec := json.NewDecoder(req.Body)
	err := dec.Decode(&rental)

	return &rental, errors.Wrap(err, "decoding Rental request failed")
}

func cleanRentals(rental *rentalPayload) {
	rental.Description = strings.TrimSpace(rental.Description)
	rental.Notes = strings.TrimSpace(rental.Notes)
}

func rentals(ctx context.Context, complaintID int) ([]rentalPayload, error) {
	exists, err := complaintExists(complaintID)
	if err != nil {
		return nil, errors.Wrap(err, "error getting complaint from database")
	}
	if !exists {
		return nil, errors.Wrap(err, "complaint does not exist")
	}

	ids := []int{}
	err = db.Get().SelectContext(ctx, &ids, `select id from automotive_claim_complaint_rentals where automotive_claim_complaint_id = $1 order by id asc`, complaintID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, errors.Wrap(err, "error loading rentals list ")
	}

	rentals := make([]rentalPayload, len(ids))
	for i, id := range ids {
		rental, err := rentalByID(ctx, id)
		if err != nil {
			return rentals, errors.Wrap(err, "error getting rentals data")
		}
		rentals[i] = *rental
	}

	return rentals, nil
}

// RentalShow returns detail of rentals for given rental_id
func RentalShow(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	ctx := req.Context()

	rentalIDStr := chi.URLParam(req, "rental_id")
	rentalID, err := strconv.Atoi(rentalIDStr)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error comverting rental id"))
		return http.StatusBadRequest, handlers.ErrorMessage(nil, "Number conversion error for rentalID", nil)
	}
	rental, err := rentalByID(ctx, rentalID)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Could not get rental", nil)
	}
	if rental == nil {
		return http.StatusNotFound, handlers.ErrorMessage(err, "Rental not found", nil)
	}
	return http.StatusOK, map[string]interface{}{"rental": rental}
}

func rentalByID(ctx context.Context, ID int) (*rentalPayload, error) {
	rentalFromDB := struct {
		ID                         int                 `json:"id" db:"id"`
		AutomotiveClaimComplaintID int                 `json:"automotive_claim_complaint_id" db:"automotive_claim_complaint_id"`
		Description                sql.NullString      `json:"description" db:"description"`
		Requested                  decimal.NullDecimal `json:"requested" db:"requested"`
		Approved                   decimal.NullDecimal `json:"approved" db:"approved"`
		Notes                      sql.NullString      `json:"notes" db:"notes"`
	}{}

	err := db.Get().Unsafe().Get(&rentalFromDB, `select * from automotive_claim_complaint_rentals where id = $1`, ID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, errors.Wrap(err, "error loading rentals")

	}
	rental := rentalPayload{}

	rental.ID = rentalFromDB.ID
	rental.AutomotiveClaimComplaintID = rentalFromDB.AutomotiveClaimComplaintID

	if rentalFromDB.Requested.Valid {
		rental.Requested = rentalFromDB.Requested.Decimal
	}
	if rentalFromDB.Approved.Valid {
		rental.Approved = rentalFromDB.Approved.Decimal
	}
	if rentalFromDB.Notes.Valid {
		rental.Notes = rentalFromDB.Notes.String
	}
	if rentalFromDB.Description.Valid {
		rental.Description = rentalFromDB.Description.String
	}
	return &rental, nil
}

func updateRental(ctx context.Context, tx *sqlx.Tx, rental *rentalPayload) error {

	query := `update automotive_claim_complaint_rentals
    set description = :description,
    requested = :requested,
    approved = :approved,
    notes = :notes
    where id = :id`

	stmt, err := tx.PrepareNamedContext(ctx, query)
	if err != nil {
		return errors.Wrap(err, "error updating Rental, PrepareNamed failed")
	}
	defer func() { _ = stmt.Close() }()

	_, err = stmt.ExecContext(ctx, rental)
	if err != nil {
		return errors.Wrap(err, "error updating rental, database error")
	}
	return nil
}

// RentalDelete deletes rental for given part_id
func RentalDelete(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	ctx := req.Context()
	id := 0
	err := db.Get().GetContext(ctx, &id, `select id from automotive_claim_complaint_rentals where id = $1`, chi.URLParam(req, "rental_id"))
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error getting rental for delete"))
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage(err, "Rental not found", nil)
		}
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error loading rentals", nil)
	}
	_, err = db.Get().ExecContext(ctx, `delete from automotive_claim_complaint_rentals where id = $1`, id)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error deleting rental"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Could not delete rental", nil)
	}
	return http.StatusOK, map[string]interface{}{"id": id}
}

func deleteRentalByComplaintID(tx *sqlx.Tx, complaintID int) error {
	_, err := tx.Exec(`delete from automotive_claim_complaint_rentals where automotive_claim_complaint_id = $1`, complaintID)
	if err != nil {
		return errors.Wrap(err, "could not delete rentals")
	}
	return nil
}
