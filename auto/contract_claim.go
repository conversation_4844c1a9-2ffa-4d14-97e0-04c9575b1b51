package auto

import (
	"database/sql"
	"net/http"

	"github.com/shopspring/decimal"

	"phizz/db"
	"phizz/handlers"
	"phizz/types"

	"github.com/go-chi/chi"
	"github.com/pkg/errors"
	"gopkg.in/guregu/null.v3"
)

// ContractClaims used for showing claims info in contract page, search is based on contract number
func ContractClaims(res http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {

	productCode := chi.URLParam(req, "product_code")
	contractNumber := chi.URLParam(req, "contract_number")
	var selectClause, fromClause string
	args := []interface{}{contractNumber}

	switch productCode {

	case db.ProductCodeGap:
		selectClause = ` select gc.id, gc.date_of_claim_received claim_date, gc.valuation_report_mileage odometer, 
	gc.case_reserve claim_amount, gcp.paid_date claim_paid_date, gc.status claim_status, gc.date_of_loss claim_dol, gcp.check_number::text`
		fromClause = ` from gap_claims gc left join gap_claim_payments gcp on gcp.gap_claim_id = gc.id
	where contract_number = $1`

	case db.ProductCodeTheftRegistration:
		selectClause = ` select vc.id, vc.date_of_claim_received claim_date,
	vc.case_reserve claim_amount, vcp.paid_date claim_paid_date, vc.status claim_status, vcp.check_number::text`
		fromClause = ` from vta_claims vc left join vta_claim_payments vcp on vcp.vta_claim_id = vc.id
	where contract_number = $1`

	case db.ProductCodeService, db.ProductCodeMaintenance, db.ProductCodeCentury, db.ProductCodePaintlessDentRepair, db.ProductCodeDrivePur, db.ProductCodeKey,
		db.ProductCodeKeyReplacement, db.ProductCodeTireWheel, db.ProductCodeToyotaTireWheel, db.ProductCodeAppearanceProtection:
		selectClause = ` select ac.id, ac.date_of_claim_received claim_date, ac.ro_mileage odometer, ac.ro repair_order_number,
	case when ac.status in('CheckWritten', 'Reversed', 'CCPaid', 'WaitingForReversed', 'WaitingForCheck', 'Adjusted', 'Chargeback') 
		then coalesce(acpc.check_amount, acp.amount, ac.estimate) else ac.estimate end claim_amount, acpc.paid_date claim_paid_date, ac.status claim_status,
	case when acpc.check_number is null then '' else acpc.check_number end, array_to_string(array_agg(repair_code),',') failed_components,
	coalesce(acp.amount, ac.estimate) as authorized_amount, acp.id payment_id`
		fromClause = ` from automotive_claims ac left join automotive_claim_complaints acc on acc.automotive_claim_id = ac.id
					left join automotive_claim_payments acp on ac.id = acp.automotive_claim_id
  					left join automotive_claim_payment_checks acpc on acp.id = acpc.automotive_claim_payments_id
	where contract_number = $1 and product_code = $2 group by ac.id,ac.date_of_claim_received,ac.ro_mileage,ac.ro,ac.estimate,acpc.paid_date,ac.advisor,ac.status,acpc.check_number, acp.amount, acpc.check_amount, acp.id`

		args = append(args, productCode)

	case db.ProductCodeLeaseWearTear:
		selectClause = ` select lc.id, lc.date_of_claim_received claim_date, 
				lc.approved_amount + coalesce((select sum(amount) from lwt_claim_adjustments where lwt_claim_id = lc.id), 0) claim_amount,
			lcp.paid_date claim_paid_date, lc.status claim_status, lcp.check_number::text`
		fromClause = ` from lwt_claims lc left join lwt_claim_payments lcp on lcp.lwt_claim_id = lc.id
	where contract_number = $1 and (lcp.valid is null or lcp.valid = true)`

	default:
		return http.StatusOK, map[string]interface{}{"count": 0, "automotive_claims": nil}

	}

	listQuery := selectClause + fromClause
	countQuery := `select count(*)` + fromClause
	var claims []struct {
		ID                int                `json:"id" db:"id"`
		ClaimDate         types.JSPQNullDate `json:"claim_date" db:"claim_date"`
		Odometer          int                `json:"odometer" db:"odometer"`
		FailedComponents  string             `json:"failed_components" db:"failed_components"`
		RepairOrderNumber string             `json:"repair_order_number" db:"repair_order_number"`
		ClaimAmount       null.Float         `json:"claim_amount" db:"claim_amount"`
		ClaimPaidDate     types.JSPQNullDate `json:"claim_paid_date" db:"claim_paid_date"`
		ClaimStatus       string             `json:"claim_status" db:"claim_status"`
		CheckNumber       null.String        `json:"check_number" db:"check_number"`
		AuthorizedAmount  decimal.Decimal    `json:"authorized_amount" db:"authorized_amount"`
		ClaimDOL          types.JSPQNullDate `json:"claim_dol" db:"claim_dol"`
		PaymentID         null.Int           `json:"payment_id" db:"payment_id"`
	}

	err := db.Get().Select(&claims, listQuery, args...)
	if err != nil {
		err = errors.Wrap(err, "Database error getting claims for contract")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting claims", nil)
	}

	count := 0
	err = db.Get().Get(&count, countQuery, args...)
	if err != nil && err != sql.ErrNoRows {
		err = errors.Wrap(err, "Database error getting count of claims for contract")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting claims count", nil)
	}

	return http.StatusOK, map[string]interface{}{"count": count, "automotive_claims": claims}
}
