package auto

import (
	"database/sql"
	"encoding/json"
	"net/http"
	"strconv"
	"strings"

	"phizz/db"
	"phizz/handlers"

	"github.com/go-chi/chi"
	"github.com/jmoiron/sqlx"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
)

type laborPayload struct {
	ID                         int             `json:"id" db:"id"`
	AutomotiveClaimComplaintID int             `json:"automotive_claim_complaint_id" db:"automotive_claim_complaint_id"`
	LaborDescription           string          `json:"labor_description" db:"labor_description"`
	Requested                  decimal.Decimal `json:"requested" db:"requested"`
	Hours                      decimal.Decimal `json:"hours" db:"hours"`
	Rate                       decimal.Decimal `json:"rate" db:"rate"`
	Billed                     decimal.Decimal `json:"billed" db:"billed"`
	Approved                   decimal.Decimal `json:"approved" db:"approved"`
	Notes                      string          `json:"notes" db:"notes"`
}

// LaborCreate creates a new labor in complaint
func LaborCreate(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	complaintIDStr := chi.URLParam(req, "complaint_id")
	complaintID, err := strconv.Atoi(complaintIDStr)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Number conversion error for complaintID", nil)
	}
	complaintExist, err := complaintExists(complaintID)
	if !complaintExist || err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Could not get complaint", nil)
	}
	labor, err := laborFromReq(req)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Malformed Labor data for create.", nil)
	}
	labor.AutomotiveClaimComplaintID = complaintID
	cleanLabor(labor)
	formErrors, err := validateLabor(labor)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(errors.Wrap(err, "Error validating Labor"),
			"An error occurred validating the form values.", nil)
	}

	if len(formErrors) > 0 {
		return http.StatusBadRequest, handlers.ErrorMessage(nil, "Form validations errors.",
			map[string]interface{}{"errors": formErrors})
	}

	tx, err := db.Get().Beginx()
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error beginning transaction for Auto claim update", nil)
	}

	laborID, err := insertLabor(tx, labor)
	if err != nil {
		_ = tx.Rollback()
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error inserting Labor", nil)
	}

	err = tx.Commit()
	if err != nil {
		err = errors.Wrap(err, "Database error committing transaction for Auto claim update")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error updating Auto claim", nil)
	}
	return http.StatusOK, map[string]interface{}{"labor_id": laborID}
}

func insertLabor(tx *sqlx.Tx, labor *laborPayload) (int, error) {
	insertQuery := `insert into automotive_claim_complaint_labors(automotive_claim_complaint_id,hours,labor_description,approved,billed,requested)
	 values (:automotive_claim_complaint_id,:hours,:labor_description,:approved,:billed,:requested) returning id`
	id := 0

	stmt, err := tx.PrepareNamed(insertQuery)
	if err != nil {
		return id, errors.Wrap(err, "An error occurred in PrepareNamed function while adding Labor.")
	}
	defer func() { _ = stmt.Close() }()

	err = stmt.Get(&id, labor)
	if err != nil {
		return id, errors.Wrap(err, "An error occurred while trying to add the Labor to the database.")
	}

	return id, err
}

func laborFromReq(req *http.Request) (*laborPayload, error) {
	labor := laborPayload{}

	dec := json.NewDecoder(req.Body)
	err := dec.Decode(&labor)

	return &labor, errors.Wrap(err, "decoding Labor request failed")
}

func cleanLabor(labor *laborPayload) {
	labor.LaborDescription = strings.TrimSpace(labor.LaborDescription)
	labor.Notes = strings.TrimSpace(labor.Notes)

}

func validateLabor(labor *laborPayload) (map[string]string, error) {
	formErrors := map[string]string{}

	// TODO
	return formErrors, nil
}

func labors(complaintID int) ([]laborPayload, error) {
	exists, err := complaintExists(complaintID)
	if err != nil {
		return nil, errors.Wrap(err, "Error getting complaint from database")
	}
	if !exists {
		return nil, errors.Wrap(err, "Complaint does not exist")
	}

	ids := []int{}
	err = db.Get().Select(&ids, `select id from automotive_claim_complaint_labors where automotive_claim_complaint_id = $1 order by id asc`, complaintID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, errors.Wrap(err, "error loading labors list ")
	}

	labors := make([]laborPayload, len(ids))
	for i, id := range ids {
		labor, err := laborByID(id)
		if err != nil {
			return labors, errors.Wrap(err, "Error getting labor data")
		}
		labors[i] = *labor
	}

	return labors, nil
}

// LaborShow returns details of single labor record
func LaborShow(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	laborIDStr := chi.URLParam(req, "labor_id")
	laborID, err := strconv.Atoi(laborIDStr)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Number conversion error for laborID", nil)
	}
	labor, err := laborByID(laborID)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Could not get labor", nil)
	}
	if labor == nil {
		return http.StatusNotFound, handlers.ErrorMessage(err, "Labor not found", nil)
	}
	return http.StatusOK, map[string]interface{}{"labor": labor}
}

func laborByID(ID int) (*laborPayload, error) {
	laborFromDB := struct {
		ID                         int                 `json:"id" db:"id"`
		AutomotiveClaimComplaintID int                 `json:"automotive_claim_complaint_id" db:"automotive_claim_complaint_id"`
		LaborDescription           sql.NullString      `json:"labor_description" db:"labor_description"`
		Requested                  decimal.NullDecimal `json:"requested" db:"requested"`
		Hours                      decimal.NullDecimal `json:"hours" db:"hours"`
		Rate                       decimal.NullDecimal `json:"rate" db:"rate"`
		Billed                     decimal.NullDecimal `json:"billed" db:"billed"`
		Approved                   decimal.NullDecimal `json:"approved" db:"approved"`
		Notes                      sql.NullString      `json:"notes" db:"notes"`
	}{}

	err := db.Get().Unsafe().Get(&laborFromDB, `select * from automotive_claim_complaint_labors where id = $1`, ID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, errors.Wrap(err, "error loading labor")
	}

	labor := laborPayload{}
	labor.ID = laborFromDB.ID
	labor.AutomotiveClaimComplaintID = laborFromDB.AutomotiveClaimComplaintID

	if laborFromDB.LaborDescription.Valid {
		labor.LaborDescription = laborFromDB.LaborDescription.String
	}
	if laborFromDB.Notes.Valid {
		labor.Notes = laborFromDB.Notes.String
	}
	if laborFromDB.Requested.Valid {
		labor.Requested = laborFromDB.Requested.Decimal
	}
	if laborFromDB.Hours.Valid {
		labor.Hours = laborFromDB.Hours.Decimal
	}
	if laborFromDB.Rate.Valid {
		labor.Rate = laborFromDB.Rate.Decimal
	}
	if laborFromDB.Billed.Valid {
		labor.Billed = laborFromDB.Billed.Decimal
	}
	if laborFromDB.Approved.Valid {
		labor.Approved = laborFromDB.Approved.Decimal
	}

	return &labor, nil
}

func updateLabor(tx *sqlx.Tx, labor *laborPayload) error {

	query := `update automotive_claim_complaint_labors
    set labor_description = :labor_description,
    requested = :requested,
    hours = :hours,
    rate = :rate,
    billed = :billed,
    approved = :approved,
    notes = :notes
    where id = :id`

	stmt, err := tx.PrepareNamed(query)
	if err != nil {
		return errors.Wrap(err, "Error updating Part, PrepareNamed failed")
	}
	defer func() { _ = stmt.Close() }()

	_, err = stmt.Exec(labor)
	if err != nil {
		return errors.Wrap(err, "Error updating Part, database error")
	}
	return nil
}

// LaborDelete deletes a labor record for given labor_id
func LaborDelete(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	id := 0
	err := db.Get().Get(&id, `select id from automotive_claim_complaint_labors where id = $1`, chi.URLParam(req, "labor_id"))
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage(err, "Labor not found", nil)
		}
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "error loading labor", nil)
	}
	_, err = db.Get().Exec(`delete from automotive_claim_complaint_labors where id = $1`, id)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Could not delete labor", nil)
	}
	return http.StatusOK, map[string]interface{}{"id": id}
}

func deleteLaborsByComplaintID(tx *sqlx.Tx, complaintID int) error {
	_, err := tx.Exec(`delete from automotive_claim_complaint_labors where automotive_claim_complaint_id = $1`, complaintID)
	if err != nil {
		return errors.Wrap(err, "Could not delete labors")
	}
	return nil
}
