package auto

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"phizz/db"
	"phizz/handlers"

	"github.com/go-chi/chi"
	"github.com/jmoiron/sqlx"
	"github.com/lib/pq"
	newrelic "github.com/newrelic/go-agent"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
)

type lcaClaim struct {
	ID                  int             `json:"id" db:"id"`
	CustomerName        string          `json:"customer_name" db:"customer_name"`
	ContractNumber      string          `json:"contract_number" db:"contract_number"`
	VIN                 string          `json:"vin" db:"vin"`
	Status              string          `json:"status" db:"status"`
	RequestedTotal      decimal.Decimal `json:"requested_total" db:"requested_total"`
	Estimate            decimal.Decimal `json:"estimate" db:"estimate"`
	RO                  string          `json:"ro" db:"ro"`
	Attachments         int             `json:"attachments" db:"attachments"`
	ROOpenedDate        pq.NullTime     `json:"ro_opened_date" db:"ro_opened_date"`
	DateOfClaimReceived time.Time       `json:"date_of_claim_received" db:"date_of_claim_received"`
	ProductCode         string          `json:"product_code" db:"product_code"`
	CheckNumber         string          `json:"check_number" db:"-"`
}

// LCAClaimIndex returns a list of claims
// This function returns all automotive claims if no query parameters are provided
// The claims can be filtered by following query parameters
// q = filter by firstname, lastname, contract_number, vin or ro
// status - return claims with the given status
// store_id - return claims only for given store_id
func LCAClaimIndex(res http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	ctx := req.Context()

	var whereClauses []string // where clause
	args := struct {
		SearchQuery string `db:"search_query"`
		Status      string `db:"status"`
		StoreID     int    `db:"store_id"`
	}{}

	q := req.FormValue("q")
	if q != "" {
		whereClauses = append(whereClauses, `(concat(first_name, ' ', last_name) ilike :search_query or
		concat(last_name, ' ', first_name) ilike :search_query
		or business_name ilike :search_query
		or contract_number ilike :search_query
		or vin ilike :search_query
		or ro ilike :search_query)
		or acpa.pre_auth_number ilike :search_query`)
		args.SearchQuery = "%" + strings.Join(strings.Fields(strings.TrimSpace(q)), " ") + "%"
	}

	status := req.FormValue("status")
	if status != "" && isValidClaimStatus(status) {
		whereClauses = append(whereClauses, "ac.status = :status")
		args.Status = status
	}

	storeIDStr := req.FormValue("store_id")
	if storeIDStr == "" {
		return http.StatusBadRequest, handlers.ErrorMessage(errors.New("store_id is missing"), "store_id is missing", nil)
	}
	storeID := 0
	err := db.Get().GetContext(ctx, &storeID, "select id from stores where id = $1", storeIDStr)
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage(err, "store_id not found", nil)
		}
		handlers.ReportError(req, errors.Wrap(err, "Database error in getting store id"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "error in getting store id", nil)
	}
	args.StoreID = storeID
	whereClauses = append(whereClauses,
		`(
			(((extract(epoch from age(date_of_claim_received))/3600)/24)/121 <= 1) 
		or `+
			fmt.Sprintf("ac.status in ('%s', '%s', '%s', '%s', '%s', '%s')",
				db.AutoClaimStatusNeedClosedAccountingRO,
				db.AutoClaimStatusNeedProofOfDeductibleReimbursement,
				db.AutoClaimStatusNeedRentalBill,
				db.AutoClaimStatusNeedSMToCall,
				db.AutoClaimStatusNeedSubletBill,
				db.AutoClaimStatusOpen)+
			`)`)

	wipDashBoard := req.FormValue("wip_dashboard")

	// for wipdashboard in default mode, skip maintenance and drive pur products
	if wipDashBoard != "" && q == "" {
		skipProducts := fmt.Sprintf("product_code not in ('%s','%s')", db.ProductCodeMaintenance, db.ProductCodeDrivePur)
		whereClauses = append(whereClauses, skipProducts)
	}

	wh := ""
	if len(whereClauses) > 0 {
		wh = "where " + strings.Join(whereClauses, " and ")
	}

	sortBy := req.FormValue("sort_by")
	if sortBy == "" && wipDashBoard == "" {
		sortBy = " ro "
	} else {
		sortBy = mapLCAClaimSort(sortBy)
	}
	sortOrder := req.FormValue("sort_order")
	if sortOrder == "" || sortOrder == "asc" {
		sortOrder = " asc "
	} else {
		sortOrder = " desc "
	}

	// wip dashboard default sort should be based on claim status
	if wipDashBoard != "" {
		if sortBy == "" {
			sortBy = sortByClaimStatus()
		}
	}

	// default order should be ro asc
	orderBy := "order by " + sortBy + " " + sortOrder + " "
	selectClause := `ac.id,
	case when c.is_business and (c.first_name!='' or c.last_name!='') then c.last_name || ',' || c.first_name || '/' || c.business_name
            when c.is_business and c.first_name='' and c.last_name='' then c.business_name
            else c.last_name || ',' || c.first_name end customer_name,
		contract_number, vin,
		status, ro_opened_date, date_of_claim_received, (requested_total + total_tax) as requested_total, estimate, product_code, case when ro is null then '' else ro end`
	fromClause := `automotive_claims ac join customers c on (ac.customer_id = c.id
				   and ac.facility_id in (select id from automotive_facilities af where af.store_id = :store_id))
				   left join automotive_claim_pre_auth_numbers acpa on acpa.automotive_claim_id = ac.id`

	countQuery := "select count(*) from " + fromClause + " " + wh

	// handle pagination
	p := req.FormValue("page")
	var listQuery string
	var n int
	if n, err = strconv.Atoi(p); err == nil && n > 0 {
		listQuery = fmt.Sprintf("select %s from %s %s %s limit %d offset %d", selectClause, fromClause, wh, orderBy, handlers.PerPageEntries, (n-1)*handlers.PerPageEntries)
	} else { // if page number is not provided, return all values
		listQuery = fmt.Sprintf("select %s from %s %s %s", selectClause, fromClause, wh, orderBy)
	}

	stmt, err := db.Get().PrepareNamedContext(ctx, listQuery)
	if err != nil {
		err = errors.Wrap(err, "Database error preparing get claims list query")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting claims", nil)
	}
	defer func() { _ = stmt.Close() }()

	claims := []lcaClaim{}
	err = stmt.SelectContext(ctx, &claims, args)
	if err != nil {
		err = errors.Wrap(err, "Database error getting Auto claims lists")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting Auto claims lists data", nil)
	}

	for i := range claims {
		err = db.Get().GetContext(ctx, &claims[i].Attachments,
			"select count(*) from automotive_claim_documents where automotive_claim_id = $1 and document_type <> $2",
			claims[i].ID, db.ClaimDocumentTypeInspectionDocument)
		if err != nil {
			handlers.ReportError(req, errors.Wrap(err, "Database error while getting attachment count for claim"))
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error while getting attachment count for claim", nil)
		}

		checkNumberQuery := `select case when acpc.check_number is null then '' else acpc.check_number end 
                           from automotive_claim_payment_checks acpc join automotive_claim_payments acp on acpc.automotive_claim_payments_id=acp.id
                           where acp.automotive_claim_id = $1 order by acp.paid_date ASC LIMIT 1`

		err = db.Get().GetContext(ctx, &claims[i].CheckNumber, checkNumberQuery, claims[i].ID)

		if err != nil && err != sql.ErrNoRows {
			handlers.ReportError(req, errors.Wrap(err, "Database error while getting check_number for claim"))
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error while getting check_number for claim", nil)
		}
	}

	stmt2, err := db.Get().PrepareNamedContext(ctx, countQuery)
	if err != nil {
		err = errors.Wrap(err, "Database error preparing automotive claims count query")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting claims count", nil)
	}
	defer func() { _ = stmt2.Close() }()
	count := 0
	err = stmt2.GetContext(ctx, &count, args)
	if err != nil {
		err = errors.Wrap(err, "Database error getting Auto claims lists count")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting Auto claims lists data", nil)
	}

	return http.StatusOK, map[string]interface{}{"count": count, "automotive_claims": claims}
}

// this would be used in sql sort
func mapLCAClaimSort(input string) string {
	switch input {
	case "customer_name":
		return "customer_name"
	case "contract_number":
		return "contract_number"
	case "vin":
		return "vin"
	case "status":
		return "status"
	case "ro_opened_date":
		return "ro_opened_date"
	case "date_of_claim_received":
		return "date_of_claim_received"
	case "estimate":
		return "estimate"
	case "ro":
		return "ro"
	case "requested_total":
		return "requested_total"
	}
	return ""
}

func sortByClaimStatus() string {
	str := fmt.Sprintf(`case status 
		when '%s' then 1 
		when '%s' then 2 
		when '%s' then 3 
		when '%s' then 4 
		when '%s' then 5 
		when '%s' then 6 
		when '%s' then 7 
		when '%s' then 8 
		when '%s' then 9 
		when '%s' then 10 
		when '%s' then 11 
		when '%s' then 12
		when '%s' then 13 
		end`, db.AutoClaimStatusReturned,
		db.AutoClaimStatusNeedClosedAccountingRO,
		db.AutoClaimStatusNeedProofOfDeductibleReimbursement,
		db.AutoClaimStatusNeedRentalBill,
		db.AutoClaimStatusNeedSMToCall,
		db.AutoClaimStatusNeedSubletBill,
		db.AutoClaimStatusOpen,
		db.AutoClaimStatusPreAuth,
		db.AutoClaimStatusApproved,
		db.AutoClaimStatusCheckWritten,
		db.AutoClaimStatusDenied,
		db.AutoClaimStatusPayable,
		db.AutoClaimStatusWaitingForCheck)
	return str
}

// LCAClaimSaveDocument saves document to s3
func LCAClaimSaveDocument(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	// Get facility user
	ctx := req.Context()
	var user db.User
	err := db.Get().Get(&user.ID, "select id from users where first_name = $1 limit 1", db.FacilityUserName)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "Database error while getting facility user"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error while getting facility user", nil)
	}

	documentPayload, err := getDocumentFromPayload(req.Body)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Bad request", nil)
	}

	documentPayload.clean()
	documentPayload.CreatedByUserID = user.ID
	formErrs, err := documentPayload.validate()
	if err != nil {
		err = errors.Wrap(err, "error validating document")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error during validation", nil)
	}
	if len(formErrs) > 0 {
		return http.StatusBadRequest, map[string]interface{}{
			"validation_errors": formErrs,
		}
	}
	txn := w.(newrelic.Transaction)
	documentID, err := saveDocument(ctx, &documentPayload, txn, user)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error in saving document", nil)
	}

	q, args, err := sqlx.In("update automotive_claims set status = ? where id = ? and status in (?)",
		db.AutoClaimStatusOpen, documentPayload.AutomotiveClaimID,
		[]string{db.AutoClaimStatusNeedRentalBill, db.AutoClaimStatusNeedSubletBill, db.AutoClaimStatusNeedSMToCall,
			db.AutoClaimStatusNeedClosedAccountingRO, db.AutoClaimStatusNeedProofOfDeductibleReimbursement,
			db.AutoClaimStatusReturned})

	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error update status for save document in LCA claim, In query failed", nil)
	}
	query := db.Get().Rebind(q)

	_, err = db.Get().Exec(query, args...)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "Database error in updating status of WIP claim"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error in updating status of WIP claim", nil)
	}

	return http.StatusOK, map[string]interface{}{"automotive_claim_document": documentID}
}

// LCAClaimDocumentIndex wrapper over DocumentIndex
func LCAClaimDocumentIndex(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {

	storeIDStr := req.FormValue("store_id")
	if storeIDStr == "" {
		return http.StatusBadRequest, handlers.ErrorMessage(errors.New("store_id is missing"), "store_id is missing", nil)
	}
	storeID := 0
	err := db.Get().Get(&storeID, "select id from stores where id = $1", storeIDStr)
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage(err, "store_id not found", nil)
		}
		handlers.ReportError(req, errors.Wrap(err, "Database error in getting store id"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "error in getting store id", nil)
	}

	claimID, err := strconv.Atoi(chi.URLParam(req, "id"))
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Bad request", nil)
	}

	claimStoreID := 0
	query := `select store_id from automotive_facilities where id =
				(select facility_id from automotive_claims where id = $1)`

	err = db.Get().Get(&claimStoreID, query, claimID)
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage(err, "store_id for claim not found", nil)
		}
		handlers.ReportError(req, errors.Wrap(err, "Database error in getting store id for claim"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "error in getting store id for claim", nil)
	}

	if storeID != claimStoreID {
		err = errors.New("Store not authorized to access documents for the claim " + strconv.Itoa(claimID))
		handlers.ReportError(req, err)
		return http.StatusUnauthorized, handlers.ErrorMessage(err, "Not authorized to access docs for claim", nil)
	}

	return DocumentIndex(w, req)
}

// LCADocumentDownload returns pre-signed URL for the LCA auto document
func LCADocumentDownload(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	storeIDStr := req.FormValue("store_id")
	if storeIDStr == "" {
		return http.StatusBadRequest, handlers.ErrorMessage(errors.New("store_id is missing"), "store_id is missing", nil)
	}
	storeID := 0
	err := db.Get().Get(&storeID, "select id from stores where id = $1", storeIDStr)
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage(err, "store_id not found", nil)
		}
		handlers.ReportError(req, errors.Wrap(err, "Database error in getting store id"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "error in getting store id", nil)
	}

	documentID, err := strconv.Atoi(chi.URLParam(req, "id"))
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Bad request documentID is required", nil)
	}

	claimStoreID := 0
	query := `select store_id from automotive_facilities where id =
				(select facility_id from automotive_claims where id =
					(select automotive_claim_id from automotive_claim_documents where id = $1))`

	err = db.Get().Get(&claimStoreID, query, documentID)
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage(err, "store_id for claim not found", nil)
		}
		handlers.ReportError(req, errors.Wrap(err, "Database error in getting store id for claim"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "error in getting store id for claim", nil)
	}

	if storeID != claimStoreID {
		err = errors.New("Store not authorized to access documents for the claim " + strconv.Itoa(documentID))
		handlers.ReportError(req, err)
		return http.StatusUnauthorized, handlers.ErrorMessage(err, "Not authorized to access docs for claim", nil)
	}
	url, err := documentDownload(documentID)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "Error in downloading document"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error in downloading document", nil)
	}
	return http.StatusOK, map[string]interface{}{"url": url}
}

// LCAClaimCreate creates LCA claim, used for Add Maintenance, Add Application and LCA -> WIP claim
func LCAClaimCreate(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	ctx := req.Context()
	storeIDStr := req.FormValue("store_id")
	if storeIDStr == "" {
		return http.StatusBadRequest, handlers.ErrorMessage(errors.New("store_id is missing"), "store_id is missing", nil)
	}
	storeID := 0
	err := db.Get().GetContext(ctx, &storeID, "select id from stores where id = $1", storeIDStr)
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage(err, "store_id not found", nil)
		}
		handlers.ReportError(req, errors.Wrap(err, "Database error in getting store id"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "error in getting store id", nil)
	}

	var claim newClaimPayloadDB
	dec := json.NewDecoder(req.Body)
	err = dec.Decode(&claim)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Malformed Automotive Claim data for create.", nil)
	}

	faciltyQ := `select id as facility_id, name as facility_name, address as facility_address, city as facility_city,
		state_code as facility_state_code, postal_code as facility_postal_code,
		case when phone is null then '' else phone end as facility_phone,
		case when fax is null then '' else fax end as facility_fax
		from automotive_facilities where store_id = $1`
	err = db.Get().GetContext(ctx, &claim, faciltyQ, storeID)
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage(err, "facility_id for store not found", nil)
		}
		handlers.ReportError(req, errors.Wrap(err, "Database error in getting facility_id"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "error in getting facility", nil)
	}

	activeRates, err := getFacilityActiveRates(ctx, claim.FacilityID, claim.RoOpenedDate.Time)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "failed to get facility details"))
		return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Database error updating claim", nil)
	}

	for _, v := range activeRates {
		if v.RateType == LaborRate {
			claim.LaborRate = v.EffectiveRate
		}
		if v.RateType == LaborTax {
			claim.TaxLabor = v.EffectiveRate
		}
		if v.RateType == PartsTax {
			claim.TaxParts = v.EffectiveRate
		}
	}

	prevContracts := []string{}
	err = db.Get().SelectContext(ctx, &prevContracts, `select contract_number
		from automotive_claims where ro = $1 and facility_id = $2 and status != $3 and product_code = $4`,
		claim.RO, claim.FacilityID, db.AutoClaimStatusDeactivated, claim.ProductCode)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "Database error in checking existing ro"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "error in checking existing ro", nil)
	}
	if len(prevContracts) > 0 {
		msg := fmt.Sprintf("RO has already been used on Contract {Contract# %s}. For questions, call TCA Claims Department.", strings.Join(prevContracts, " "))
		return http.StatusBadRequest, handlers.ErrorMessage(err, msg, nil)
	}

	if claim.ContractStoreID == 0 {
		err = db.Get().GetContext(ctx, &claim.ContractStoreID, "select id from stores where code = $1", claim.ContractStoreCode)
		if err != nil {
			if err == sql.ErrNoRows {
				return http.StatusNotFound, handlers.ErrorMessage(err, "Issuing dealer for store not found", nil)
			}
			handlers.ReportError(req, errors.Wrap(err, "Database error in contract_store_id store id"))
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error in getting issuing dealer", nil)
		}
	}

	// claim for maintenance and DP will have approved status, these are added from 'Add Maintenance' and 'Add Application' respectively
	if claim.Status == "" {
		claim.Status = db.AutoClaimStatusOpen
	}
	claim.Maintenance = claim.Term + " " + claim.ContractStatus
	claim.Deductible = claim.ContractDeductible

	// For maintenance we need to set up coverage name
	if claim.ProductCode == db.ProductCodeMaintenance {
		claim.Coverage = claim.ProductVariantDisplayName
	}

	ownerID := 0
	ownerQ := `select owner_id from automotive_facility_zones where id in (select zone_id from automotive_facilities where store_id = $1)`
	err = db.Get().GetContext(ctx, &ownerID, ownerQ, storeID)
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage(err, "owner_id for automotive_facility_zones not found for facility "+strconv.Itoa(claim.FacilityID), nil)
		}
		handlers.ReportError(req, errors.Wrap(err, "Database error in getting zone owner id"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "error in getting zone owner id", nil)
	}

	claim.CreatedByUserID = ownerID
	claim.OwnerID = ownerID

	// Maintenance and DP should be assigned to facility
	if claim.ProductCode == db.ProductCodeMaintenance || claim.ProductCode == db.ProductCodeDrivePur {
		facilityUserID := 0
		err = db.Get().GetContext(ctx, &facilityUserID, "select id from users where first_name = $1 limit 1", db.FacilityUserName)
		if err != nil {
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error while fetching Facility User", nil)
		}
		claim.OwnerID = facilityUserID
	}

	err = insertLCAClaim(ctx, &claim)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error in adding automotive claim", nil)
	}

	return http.StatusOK, map[string]interface{}{"id": claim.ID}
}

func insertLCAClaim(ctx context.Context, claim *newClaimPayloadDB) error {
	claimInsert := `insert into automotive_claims (
		vin,
		ro,
		ro_mileage,
		contract_number,
		contract_status,
		date_of_claim_received,
		status,
		created_by_user_id,
		owner_id,
		customer_id,
		model,
		make,
		year,
		beginning_miles,
		ending_miles,
		effective_date,
		expiration_date,
		coverage,
		deductible,
		maintenance,
		term,
		product_code,
		contract_store_id,
		contract_deductible,
		claim_type,
		facility_id,
		facility_name,
		facility_address,
		facility_postal_code,
		facility_city,
		facility_state_code,
		facility_phone,
		facility_fax,
		labor_rate,
		tax_labor,
		tax_parts,
		ro_opened_date,
		estimate,
		pay_type,
		auto_approved,
		total_parts,
		total_labor)

		values (:vin,
		:ro,
		:ro_mileage,
		:contract_number,
		:contract_status,
		now() at time zone 'utc',
		:status,
		:created_by_user_id,
		:owner_id,
		:customer_id,
		:model,
		:make,
		:year,
		:beginning_miles,
		:ending_miles,
		:effective_date,
		:expiration_date,
		:coverage,
		:deductible,
		:maintenance,
		:term,
		:product_code,
		:contract_store_id,
		:contract_deductible,
		:claim_type,
		:facility_id,
		:facility_name,
		:facility_address,
		:facility_postal_code,
		:facility_city,
		:facility_state_code,
		:facility_phone,
		:facility_fax,
		:labor_rate,
		:tax_labor,
		:tax_parts,
		:ro_opened_date,
		:estimate,
		:pay_type,
		:auto_approved,
		:total_parts,
		:total_labor
		) returning id`

	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		return errors.Wrap(err, "database error beginning transaction for Automotive claim create")
	}

	err = addClaimCustomer(tx, claim)
	if err != nil {
		_ = tx.Rollback()
		return errors.Wrap(err, "error creating customer")
	}

	// Prepare and execute
	stmt, err := tx.PrepareNamedContext(ctx, claimInsert)
	if err != nil {
		_ = tx.Rollback()
		return errors.Wrap(err, "prepareNamed failed")
	}
	defer func() { _ = stmt.Close() }()
	err = stmt.GetContext(ctx, &claim.ID, claim)
	if err != nil {
		_ = tx.Rollback()
		return errors.Wrap(err, "scan error on ID after creating Atuomotive claim")
	}

	// Add coverage flags
	err = addCoverageFlags(tx, claim)
	if err != nil {
		return errors.Wrap(err, "error while adding coverage flags for the claim")
	}

	// Add note on successful new claim creation
	note := RecordNotePayload{}
	note.AutomotiveClaimID = claim.ID
	note.IsManual = false

	if claim.ClaimType == db.ClaimTypeSB || claim.ClaimType == db.ClaimTypeLCA {
		facilityUserID := 0
		err = db.Get().GetContext(ctx, &facilityUserID, "select id from users where first_name = $1 limit 1", db.FacilityUserName)
		if err != nil {
			return errors.Wrap(err, "error while fetching Facility User")
		}
		note.CreatedByUserID = facilityUserID
	} else {
		note.CreatedByUserID = claim.CreatedByUserID
	}

	// for claim create, note text should be New claim started
	note.NotesText = db.AutomotiveRecordNoteDescription[db.AutoClaimStatusNew]

	if claim.ProductCode == db.ProductCodeMaintenance {
		if claim.IsROLookup {
			note.NotesText = db.AutomotiveRecordNoteDescription[db.AutoClaimStatusNewLCAWithROLookup]
		} else {
			note.NotesText = db.AutomotiveRecordNoteDescription[db.AutoClaimStatusNewLCAWithManual]
		}
	}

	_, err = InsertRecordNote(ctx, &note, tx)
	if err != nil {
		_ = tx.Rollback()
		return errors.Wrap(err, "failed to add note")
	}

	err = ClaimUpdated(ctx, tx, claim.ID, claim.CreatedByUserID)
	if err != nil {
		_ = tx.Rollback()
		return errors.Wrap(err, "error inserting automotive_claim_updates")
	}

	err = tx.Commit()
	if err != nil {
		return errors.Wrap(err, "database error committing transaction for Auto claim insert")
	}
	return nil
}

// LCAClaimShow returns claim, api used for LCA Wip View Claim
func LCAClaimShow(res http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	ctx := req.Context()
	storeIDStr := req.FormValue("store_id")
	if storeIDStr == "" {
		return http.StatusBadRequest, handlers.ErrorMessage(errors.New("store_id is missing"), "store_id is missing", nil)
	}
	storeID := 0
	err := db.Get().GetContext(ctx, &storeID, "select id from stores where id = $1", storeIDStr)
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage(err, "store_id not found", nil)
		}
		handlers.ReportError(req, errors.Wrap(err, "Database error in getting store id"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "error in getting store id", nil)
	}

	id := chi.URLParam(req, "id")
	// Get the auto claim from database
	txn := res.(newrelic.Transaction)
	autoClaim, err := ClaimSelect(txn, req, id)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error getting auto claim from database"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting Auto claim from database", nil)
	}

	return http.StatusOK, map[string]interface{}{"claim": autoClaim}
}
