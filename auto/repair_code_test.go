package auto

import (
	"net/http"
	"net/http/httptest"
	"regexp"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/go-chi/chi"
	"github.com/jmoiron/sqlx"

	"phizz/db"
	"phizz/handlers"
)

// disabling this test-case as repair-code now have external API dependency
func DISABLEDTestRepairCodeIndexSearchByCode(t *testing.T) {
	mdb, mock, err := sqlmock.New()
	if err != nil {
		t.Fatal(err)
	}
	dbx := sqlx.NewDb(mdb, "sqlmock")
	db.DB = dbx
	q := regexp.QuoteMeta

	mock.ExpectQuery(q(`select id, code, description from automotive_complaint_repair_codes where code ilike $1 or description ilike $1`)).
		WithArgs("%991%").
		WillReturnRows(sqlmock.NewRows([]string{"id", "code", "description"}).
			AddRow(1, "9910", "Engine-short Block"))

	req, err := http.NewRequest("GET", "/repair-codes?q=991", nil)
	if err != nil {
		t.Fatal(err)
	}

	rr := httptest.NewRecorder()
	handler := http.HandlerFunc(handlers.APIHandler(RepairCodeIndex))

	r := chi.NewRouter()
	r.HandleFunc("/repair-codes", handler)
	r.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v",
			status, http.StatusOK)
	}

	expected := `{"repair_codes":[{"id":1,"code":"9910","description":"Engine-short Block"}]}`
	if rr.Body.String() != expected {
		t.Errorf("handler returned unexpected body: got %v want %v",
			rr.Body.String(), expected)
	}

	if err := mock.ExpectationsWereMet(); err != nil {
		t.Errorf("there were unfulfilled expectations: %s", err)
	}
}

// disabling this test-case  as repair-code now have external API dependency
func DISABLEDTestRepairCodeIndexSearchByDescription(t *testing.T) {
	mdb, mock, err := sqlmock.New()
	if err != nil {
		t.Fatal(err)
	}
	dbx := sqlx.NewDb(mdb, "sqlmock")
	db.DB = dbx
	q := regexp.QuoteMeta

	mock.ExpectQuery(q(`select id, code, description from automotive_complaint_repair_codes where code ilike $1 or description ilike $1`)).
		WithArgs("%engine%").
		WillReturnRows(sqlmock.NewRows([]string{"id", "code", "description"}).
			AddRow(1, "9910", "Engine-short Block"))

	req, err := http.NewRequest("GET", "/repair-codes?q=engine", nil)
	if err != nil {
		t.Fatal(err)
	}

	rr := httptest.NewRecorder()
	handler := http.HandlerFunc(handlers.APIHandler(RepairCodeIndex))

	r := chi.NewRouter()
	r.HandleFunc("/repair-codes", handler)
	r.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v",
			status, http.StatusOK)
	}

	expected := `{"repair_codes":[{"id":1,"code":"9910","description":"Engine-short Block"}]}`
	if rr.Body.String() != expected {
		t.Errorf("handler returned unexpected body: got %v want %v",
			rr.Body.String(), expected)
	}

	if err := mock.ExpectationsWereMet(); err != nil {
		t.Errorf("there were unfulfilled expectations: %s", err)
	}
}
