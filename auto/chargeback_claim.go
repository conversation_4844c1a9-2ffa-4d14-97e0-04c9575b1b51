package auto

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"phizz/db"
	"phizz/handlers"

	"github.com/go-chi/chi"
	"github.com/jmoiron/sqlx"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
	"gopkg.in/guregu/null.v3"
)

type chargeBackClaim struct {
	ID                      int             `db:"id"`
	OwnerID                 int             `db:"owner_id"`
	ProductCode             string          `db:"product_code"`
	Status                  string          `db:"status"`
	DateOfClaimReceived     time.Time       `db:"date_of_claim_received"`
	FacilityID              sql.NullInt64   `db:"facility_id"`
	FacilityName            string          `db:"facility_name"`
	FacilityAddress         string          `db:"facility_address"`
	FacilityPostalCode      string          `db:"facility_postal_code"`
	FacilityCity            string          `db:"facility_city"`
	FacilityStateCode       string          `db:"facility_state_code"`
	FacilityCountry         string          `db:"facility_country"`
	FacilityPhone           string          `db:"facility_phone"`
	FacilityFax             string          `db:"facility_fax"`
	Estimate                decimal.Decimal `db:"estimate"`
	RequestedTotal          decimal.Decimal `db:"requested_total"`
	ClaimType               string          `db:"claim_type"`
	ContractStoreID         int             `db:"contract_store_id"`
	CreatedByUserID         int             `json:"created_by_user_id" db:"created_by_user_id"`
	VIN                     string          `json:"vin" db:"vin"`
	RO                      string          `json:"ro" db:"ro"`
	ContractNumber          string          `json:"contract_number" db:"contract_number"`
	ContractStatus          string          `json:"contract_status" db:"contract_status"`
	CustomerName            string          `json:"customer_name" db:"customer_name"`
	CustomerID              int             `json:"customer_id" db:"customer_id"`
	EmailAddress            string          `json:"email_address" db:"email_address"`
	AltPhoneNumber          string          `json:"alternate_phone_number" db:"alternate_phone_number"`
	StreetAddress           string          `json:"street_address" db:"street_address"`
	PhoneNumber             string          `json:"phone_number" db:"phone_number"`
	City                    string          `json:"city" db:"city"`
	State                   string          `json:"state" db:"state"`
	PostalCode              string          `json:"postal_code" db:"postal_code"`
	CustomerContactMethod   string          `json:"best_contact_method" db:"best_contact_method"`
	CustomerPayeeVendorID   string          `json:"customer_payee_vendor_id" db:"customer_payee_vendor_id"`
	Model                   string          `json:"model" db:"model"`
	Make                    string          `json:"make" db:"make"`
	Year                    string          `json:"year" db:"year"`
	FacilityCode            string          `json:"facility_code" db:"-"`
	PayType                 string          `json:"pay_type" db:"pay_type"`
	ChargeBack              bool            `json:"chargeback" db:"chargeback"`
	Note                    string          `json:"note" db:"note"`
	ChargebackCollectedNote null.String     `json:"chargeback_collected_note"`
	ParentClaimID           int             `json:"parent_claim_id" db:"parent_claim_id"`
	BeginningMiles          int             `json:"beginning_miles" db:"beginning_miles"`
	Mileage                 int             `json:"mileage" db:"mileage"`
	ROMileage               int             `json:"ro_mileage" db:"ro_mileage"`
	Deductible              decimal.Decimal `json:"deductible" db:"deductible"`
}

type chargeBackPayload struct {
	ID                      int               `json:"id" db:"id"`
	ParentClaimID           int               `json:"parent_claim_id" db:"parent_claim_id"`
	FacilityID              null.Int          `json:"facility_id" db:"facility_id"`
	FacilityName            null.String       `json:"facility_name" db:"facility_name"`
	FacilityAddress         null.String       `json:"facility_address" db:"facility_address"`
	FacilityPostalCode      null.String       `json:"facility_postal_code" db:"facility_postal_code"`
	FacilityCity            null.String       `json:"facility_city" db:"facility_city"`
	FacilityStateCode       null.String       `json:"facility_state_code" db:"facility_state_code"`
	FacilityCountry         null.String       `json:"facility_country" db:"facility_country"`
	FacilityPhone           null.String       `json:"facility_phone" db:"facility_phone"`
	FacilityFax             null.String       `json:"facility_fax" db:"facility_fax"`
	FacilityCode            string            `json:"facility_code" db:"facility_code"`
	Amount                  decimal.Decimal   `json:"amount" db:"amount"`
	Notes                   null.String       `json:"notes" db:"notes"`
	Status                  string            `json:"status" db:"status"`
	OwnerID                 int               `json:"owner_id" db:"owner_id"`
	PayType                 string            `json:"pay_type" db:"pay_type"`
	CustomerName            string            `json:"customer_name" db:"customer_name"`
	CustomerPayeeVendorID   string            `json:"customer_payee_vendor_id" db:"customer_payee_vendor_id"`
	StreetAddress           string            `json:"street_address" db:"street_address"`
	State                   string            `json:"state" db:"state"`
	PostalCode              string            `json:"postal_code" db:"postal_code"`
	City                    string            `json:"city" db:"city"`
	Documents               []documentPayload `json:"documents" db:"-"`
	ChargebackCollectedNote null.String       `json:"chargeback_collected_note" db:"chargeback_collected_note"`
	ChargebackCheckNumber   null.String       `json:"chargeback_check_number" db:"chargeback_check_number"`
	ChargebackPaidDate      null.Time         `json:"chargeback_paid_date" db:"chargeback_paid_date"`
}

func chargeBackClaimFromReq(chgbkClaim *chargeBackPayload, req *http.Request) error {
	dec := json.NewDecoder(req.Body)
	err := dec.Decode(&chgbkClaim)
	return errors.Wrap(err, "decoding chargeback Claim request failed")
}

func validateChargeBackClaim(claim chargeBackPayload) map[string]string {
	formErrors := map[string]string{}

	if claim.Status == db.AutoClaimStatusApproved && claim.Amount.Equal(decimal.Zero) {
		formErrors["amount"] = "Claim amount can not be zero"
	}

	if claim.PayType != db.PayTypeCustomer && claim.Status == db.AutoClaimStatusApproved && claim.FacilityID.ValueOrZero() == 0 {
		formErrors["facility"] = "Claim facility can not be empty"
	}

	if claim.Notes.String == "" {
		formErrors["notes"] = "Claim notes can not be empty"
	}
	if claim.Status == db.AutoClaimStatusChargebackCollected {
		if claim.ChargebackCollectedNote.String == "" {
			formErrors["chargeback_collected_notes"] = "Charegeback collected notes cannot be empty"
		}
		if claim.ChargebackCheckNumber.String == "" {
			formErrors["chargeback_check_number"] = "Chargeback check number cannot be empty"
		}
		if claim.ChargebackPaidDate.Time.IsZero() {
			formErrors["chargeback_paid_date"] = "Chargeback paid date cannot be empty"
		}

	}
	return formErrors
}

// ChargeBackClaimCreate creates new chargeback claims with given lwt contract details
func ChargeBackClaimCreate(_ http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	ctx := req.Context()
	claimID := chi.URLParam(req, "id")

	var parentClaim ClaimPayload
	query := `select ac.vin, ac.contract_number, ac.contract_status, ac.model, ac.make, ac.year, ac.contract_store_id,
       				ac.claim_type, ac.product_code, ac.customer_id, ac.pay_type, ac.id,ac. ro, ac.beginning_miles, ac.ro_mileage,
       				ac.customer_payee_vendor_id, c.first_name || ' ' || c.last_name as customer_name,
					c.city, c.state, c.postal_code, c.street_address
				from automotive_claims ac join customers c on ac.customer_id = c.id 
				where ac.id = $1 limit 1`
	err := db.Get().GetContext(ctx, &parentClaim, query, claimID)
	if err != nil {
		err = errors.Wrap(err, "error getting parent claim")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting parent claim from database", nil)
	}

	cbClaim := chargeBackClaim{
		VIN:                   parentClaim.VIN,
		ContractNumber:        parentClaim.ContractNumber,
		ContractStatus:        parentClaim.ContractStatus,
		OwnerID:               user.ID,
		CreatedByUserID:       user.ID,
		Model:                 parentClaim.Model,
		Make:                  parentClaim.Make,
		Year:                  parentClaim.Year,
		ContractStoreID:       parentClaim.ContractStoreID,
		ClaimType:             parentClaim.ClaimType,
		Status:                db.AutoClaimStatusOpen,
		ProductCode:           parentClaim.ProductCode,
		CustomerID:            parentClaim.CustomerID,
		CustomerName:          parentClaim.CustomerName,
		City:                  parentClaim.City,
		StreetAddress:         parentClaim.StreetAddress,
		State:                 parentClaim.State,
		PostalCode:            parentClaim.PostalCode,
		PayType:               parentClaim.PayType,
		ChargeBack:            true,
		ParentClaimID:         parentClaim.ID,
		BeginningMiles:        parentClaim.BeginningMiles,
		ROMileage:             parentClaim.RoMileage,
		Mileage:               parentClaim.RoMileage,
		RO:                    parentClaim.RO,
		Deductible:            decimal.Zero,
		CustomerPayeeVendorID: parentClaim.CustomerPayeeVendorID,
	}

	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error creating transaction"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error in beginning transaction for chargeback claim", nil)
	}

	// insert new claim
	id, err := insertChargebackClaim(ctx, tx, cbClaim)
	if err != nil {
		_ = tx.Rollback()
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, err.Error(), nil)
	}

	// add record notes for new claim
	err = addClaimNote(ctx, tx, id, user.ID, db.AutomotiveChargebackRecordNoteDescription[db.AutoClaimStatusNew])
	if err != nil {
		_ = tx.Rollback()
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error adding notes for charge back claim", nil)
	}

	// add record notes for status
	err = addClaimNote(ctx, tx, id, user.ID, db.AutomotiveChargebackRecordNoteDescription[db.AutoClaimStatusOpen])
	if err != nil {
		_ = tx.Rollback()
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error adding notes for charge back claim", nil)
	}

	if !cbClaim.Estimate.Equals(decimal.Zero) {
		err = addClaimNote(ctx, tx, id, user.ID, fmt.Sprintf("Requested total amount is changed from $0 to $%s", cbClaim.Estimate))
		if err != nil {
			_ = tx.Rollback()
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error adding notes for charge back claim", nil)
		}
	}

	// Add entry in update table for audit trail
	err = ClaimUpdated(ctx, tx, id, user.ID)
	if err != nil {
		_ = tx.Rollback()
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "claim updated entry for audit trail failed", nil)
	}

	err = tx.Commit()
	if err != nil {
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error creating charge back claim", nil)
	}

	claim := chargeBackPayload{
		ID:            id,
		ParentClaimID: cbClaim.ParentClaimID,
		Status:        cbClaim.Status,
		OwnerID:       cbClaim.OwnerID,
		CustomerName:  cbClaim.CustomerName,
		City:          cbClaim.City,
		StreetAddress: cbClaim.StreetAddress,
		State:         cbClaim.State,
		PostalCode:    cbClaim.PostalCode,
	}
	return http.StatusOK, map[string]interface{}{"claim": claim}
}

// ChargeBackClaimShow returns details of chargeback claims for given claim id
func ChargeBackClaimShow(_ http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	ctx := req.Context()
	// get lwt claim id
	id := chi.URLParam(req, "id")

	//get charge back claim from database
	claim, err := getChargeBackClaimByParentID(ctx, id)
	if err != nil {
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting charge back claim from database", nil)
	}

	// We dont need to show amount as negative on UI
	claim.Amount = claim.Amount.Abs()
	return http.StatusOK, map[string]interface{}{"claim": claim}
}

// ChargeBackClaimUpdate updates the existing claim for given claim id
func ChargeBackClaimUpdate(_ http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	ctx := req.Context()

	claimID, err := strconv.Atoi(chi.URLParam(req, "id"))
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Error getting claim id", nil)
	}

	//Get the charge back claim from database
	claim, err := getChargeBackClaimByID(ctx, claimID)
	if err != nil {
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting charge back claim from database", nil)
	}
	claimStatusBeforeUpdate := claim.Status
	claimAmountBeforeUpdate := claim.Amount

	if claimStatusBeforeUpdate == db.AutoClaimStatusChargebackCollected {
		return http.StatusBadRequest, handlers.ErrorMessage(errors.New("Cannot update claim in this status"), "Cannot update claim in this status", nil)
	}

	err = chargeBackClaimFromReq(&claim, req)
	if err != nil {
		handlers.ReportError(req, err)
		return http.StatusBadRequest, handlers.ErrorMessage(nil, "Malformed charge back Claim data for update.", nil)
	}

	// validate claim is approved
	if claim.Status == db.AutoClaimStatusApproved || claim.Status == db.AutoClaimStatusChargebackCollected {
		formErrors := validateChargeBackClaim(claim)
		if len(formErrors) > 0 {
			return http.StatusBadRequest, handlers.ErrorMessage(err, "Form validations errors.",
				map[string]interface{}{"errors": formErrors})
		}

		if claim.PayType == db.PayTypeCustomer {
			var parentClaim ClaimPayload
			query := `select facility_id, facility_state_code, facility_postal_code,
       					facility_phone, facility_name, facility_fax, facility_country, facility_city, facility_address
					from automotive_claims 
					where id = $1`
			err := db.Get().GetContext(ctx, &parentClaim, query, claim.ParentClaimID)
			if err != nil {
				err = errors.Wrap(err, "error getting parent claim")
				handlers.ReportError(req, err)
				return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting parent claim from database", nil)
			}

			claim.FacilityID = null.IntFrom(int64(parentClaim.FacilityID))
			claim.FacilityStateCode = null.StringFrom(parentClaim.FacilityStateCode)
			claim.FacilityPostalCode = null.StringFrom(parentClaim.FacilityPostalCode)
			claim.FacilityPhone = null.StringFrom(parentClaim.FacilityPhone)
			claim.FacilityName = null.StringFrom(parentClaim.FacilityName)
			claim.FacilityFax = null.StringFrom(parentClaim.FacilityFax)
			claim.FacilityCountry = null.StringFrom(parentClaim.FacilityCountry)
			claim.FacilityCity = null.StringFrom(parentClaim.FacilityCity)
			claim.FacilityAddress = null.StringFrom(parentClaim.FacilityAddress)
		}
	}

	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		err = errors.Wrap(err, "database error beginning transaction for charge back claim update")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error creating charge back claim update", nil)
	}

	if claim.Amount.GreaterThan(decimal.Zero) {
		claim.Amount = claim.Amount.Neg()
	}

	err = chargeBackClaimUpdate(ctx, tx, claim)
	if err != nil {
		_ = tx.Rollback()
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error updating Chargeback Claim", nil)
	}

	if !claimAmountBeforeUpdate.Equals(claim.Amount) {
		err = addClaimNote(ctx, tx, claimID, user.ID, fmt.Sprintf("Requested total amount is changed from $%s to $%s", claimAmountBeforeUpdate, claim.Amount))
		if err != nil {
			_ = tx.Rollback()
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error adding notes for charge back claim", nil)
		}
	}

	if claimStatusBeforeUpdate != claim.Status && (claim.Status == db.AutoClaimStatusApproved || claim.Status == db.AutoClaimStatusDeactivated) {
		err := addClaimNote(ctx, tx, claimID, user.ID, db.AutomotiveChargebackRecordNoteDescription[claim.Status])
		if err != nil {
			_ = tx.Rollback()
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error adding notes for charge back claim", nil)
		}
	}

	if claimStatusBeforeUpdate == db.AutoClaimStatusDeactivated && claim.Status == db.AutoClaimStatusOpen {
		err := addClaimNote(ctx, tx, claimID, user.ID, db.AutomotiveChargebackRecordNoteDescription[db.AutoClaimStatusOpen])
		if err != nil {
			_ = tx.Rollback()
			handlers.ReportError(req, err)
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error adding notes for charge back claim", nil)
		}
	}

	// Add entry in update table for audit trail
	err = ClaimUpdated(ctx, tx, claimID, user.ID)
	if err != nil {
		_ = tx.Rollback()
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error inserting lwt claim update", nil)
	}

	err = tx.Commit()
	if err != nil {
		err = errors.Wrap(err, "Database error committing transaction for charge back claim update")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error committing charge back claim", nil)
	}

	return http.StatusOK, map[string]interface{}{"claim_id": claimID}
}

func insertChargebackClaim(ctx context.Context, tx *sqlx.Tx, claim chargeBackClaim) (int, error) {
	// Prepare and execute
	insertQuery := `insert into automotive_claims (
		vin,
        ro,
		contract_number,
		contract_status,
		date_of_claim_received,
		status,
		created_by_user_id,
		owner_id,
		customer_id,
		model,
		make,
		year,
		contract_store_id,
		claim_type,
		product_code,
		pay_type,
		chargeback,
        parent_claim_id,
    	beginning_miles,
        ro_mileage,
        mileage,
		deductible)
	
		values (:vin,
		:ro,
		:contract_number,
		:contract_status,
		now() at time zone 'utc',
		:status,
		:created_by_user_id,
		:owner_id,
		:customer_id,
		:model,
		:make,
		:year,
		:contract_store_id,
		:claim_type,
		:product_code,
		:pay_type,
		:chargeback,
		:parent_claim_id,
		:beginning_miles,
		:ro_mileage,
		:mileage,
		:deductible) returning id`

	stmt, err := tx.PrepareNamedContext(ctx, insertQuery)
	if err != nil {
		return 0, errors.Wrap(err, "prepareNamed failed")
	}
	defer func() { _ = stmt.Close() }()
	id := 0
	err = stmt.GetContext(ctx, &id, claim)
	if err != nil {
		return id, errors.Wrap(err, "scan error on ID after creating charge back claim")
	}
	return id, err
}

func getChargeBackClaimByParentID(ctx context.Context, parentClaimID string) (chargeBackPayload, error) {
	var chargeBackClaimID int
	query := `select id from automotive_claims where parent_claim_id = $1`
	err := db.Get().GetContext(ctx, &chargeBackClaimID, query, parentClaimID)
	if err != nil {
		if err == sql.ErrNoRows {
			return chargeBackPayload{}, errors.Wrap(err, "the charge back claim was not found")
		}
		return chargeBackPayload{}, errors.Wrap(err, "error loading charge back claim from database.")
	}

	return getChargeBackClaimByID(ctx, chargeBackClaimID)
}

func getChargeBackClaimByID(ctx context.Context, id int) (chargeBackPayload, error) {
	var claim chargeBackPayload
	query := `select ac.*, ac.estimate as amount, first_name || ' ' || last_name as customer_name,
					c.city, c.state, c.postal_code, c.street_address,
					acpc.paid_date::date chargeback_paid_date,
					acpc.check_number chargeback_check_number
				from automotive_claims ac
					join customers c on ac.customer_id = c.id
					left join automotive_claim_payments acp on acp.automotive_claim_id = ac.id
					left join automotive_claim_payment_checks acpc on acpc.automotive_claim_payments_id = acp.id
				where ac.id = $1 limit 1`
	err := db.Get().Unsafe().GetContext(ctx, &claim, query, id)
	if err != nil {
		if err == sql.ErrNoRows {
			return claim, errors.Wrap(err, "the charge back claim was not found")
		}
		return claim, errors.Wrap(err, "error loading charge back claim from database.")
	}

	documentQuery := `select * from automotive_claim_documents where automotive_claim_id = $1 and deleted_at is null`
	err = db.Get().Unsafe().SelectContext(ctx, &claim.Documents, documentQuery, id)
	if err != nil && err != sql.ErrNoRows {
		return claim, errors.Wrap(err, "error loading charge back claim from database.")
	}
	return claim, err
}

func chargeBackClaimUpdate(ctx context.Context, tx *sqlx.Tx, claim chargeBackPayload) error {
	updateQuery := `update automotive_claims
					set facility_id = :facility_id,
						facility_address = :facility_address,
						facility_city = :facility_city,
						facility_country = :facility_country,
						facility_fax = :facility_fax,
						facility_name = :facility_name,
						facility_phone = :facility_phone,
    					facility_postal_code = :facility_postal_code,
    					facility_state_code = :facility_state_code,
    					estimate = :amount,
    					requested_total = :amount,
    					status = :status,
    					notes = :notes,
						chargeback_collected_note = :chargeback_collected_note,
					    pay_type= :pay_type
					where id = :id`

	stmt, err := tx.PrepareNamedContext(ctx, updateQuery)
	if err != nil {
		return errors.Wrap(err, "error updating charge back claim, PrepareNamed failed")
	}
	defer func() { _ = stmt.Close() }()

	_, err = stmt.ExecContext(ctx, claim)
	if err != nil {
		return errors.Wrap(err, "error updating charge back claim, database error")
	}

	if claim.Status == db.AutoClaimStatusChargebackCollected {
		var autoClaimPaymentID int
		insertQuery := `insert into automotive_claim_payments(
		automotive_claim_id) values($1) returning id`
		err = tx.GetContext(ctx, &autoClaimPaymentID, insertQuery, claim.ID)
		if err != nil {
			_ = tx.Rollback()
			err = errors.Wrap(err, "error inserting claim payments")
			return errors.Wrap(err, "database error updating charge back claim")
		}

		_, err = tx.ExecContext(ctx, `insert into automotive_claim_payment_checks(
		automotive_claim_payments_id,
		check_amount,
		check_number,
		paid_date,
		updated_at) values ($1,$2,$3,$4,now() at time zone 'utc')`,
			autoClaimPaymentID, claim.Amount, claim.ChargebackCheckNumber, claim.ChargebackPaidDate)
		if err != nil {
			_ = tx.Rollback()
			err = errors.Wrap(err, "error inserting automotive_claim_payment_checks")
			return errors.Wrap(err, "database error updating charge back claim")
		}
	}

	return nil
}
