package auto

import (
	"context"
	"database/sql"
	"encoding/json"
	"net/http"
	"strconv"
	"strings"

	"phizz/db"
	"phizz/handlers"

	"github.com/go-chi/chi"
	"github.com/jmoiron/sqlx"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
)

type miscPayload struct {
	ID                         int             `json:"id" db:"id"`
	AutomotiveClaimComplaintID int             `json:"automotive_claim_complaint_id" db:"automotive_claim_complaint_id"`
	Description                string          `json:"description" db:"description"`
	Requested                  decimal.Decimal `json:"requested" db:"requested"`
	Approved                   decimal.Decimal `json:"approved" db:"approved"`
	Notes                      string          `json:"notes" db:"notes"`
}

// MiscCreate creates a new misc in complaint
func MiscCreate(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	ctx := req.Context()

	complaintIDStr := chi.URLParam(req, "complaint_id")
	complaintID, err := strconv.Atoi(complaintIDStr)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error deleting rental"))
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Number conversion error for complaintID", nil)
	}

	complaintExist, err := complaintExists(complaintID)
	if !complaintExist || err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error getting complaint"))
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Could not get complaint", nil)
	}

	misc, err := miscFromReq(req)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error malformed misc data"))
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Malformed Misc data for create.", nil)
	}
	misc.AutomotiveClaimComplaintID = complaintID
	cleanMiscs(misc)

	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error creating database transaction"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error beginning transaction for Auto claim update", nil)
	}

	miscID, err := insertMisc(ctx, tx, misc)
	if err != nil {
		_ = tx.Rollback()
		handlers.ReportError(req, errors.Wrap(err, "error inserting misc details"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error inserting Misc", nil)
	}

	err = tx.Commit()
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "database error committing transaction for Auto claim update"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error updating Auto claim", nil)
	}

	return http.StatusOK, map[string]interface{}{"misc_id": miscID}
}

// MiscShow returns detail of misc for given misc_id
func MiscShow(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	ctx := req.Context()

	miscIDStr := chi.URLParam(req, "misc_id")
	miscID, err := strconv.Atoi(miscIDStr)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error converting misc id"))
		return http.StatusBadRequest, handlers.ErrorMessage(nil, "Number conversion error for miscID", nil)
	}
	misc, err := miscByID(ctx, miscID)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error getting misc details"))
		return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Could not get misc", nil)
	}
	if misc == nil {
		handlers.ReportError(req, errors.Wrap(err, "error misc not found"))
		return http.StatusNotFound, handlers.ErrorMessage(err, "Misc not found", nil)
	}
	return http.StatusOK, map[string]interface{}{"misc": misc}
}

// MiscDelete deletes misc for given misc_id
func MiscDelete(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	ctx := req.Context()
	id := 0
	err := db.Get().GetContext(ctx, &id, `select id from automotive_claim_complaint_miscs where id = $1`, chi.URLParam(req, "misc_id"))
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error loading misc details from database"))
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage(err, "Misc not found", nil)
		}
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "error loading miscs", nil)
	}
	_, err = db.Get().ExecContext(ctx, `delete from automotive_claim_complaint_miscs where id = $1`, id)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error deleting misc details from database"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Could not delete misc", nil)
	}
	return http.StatusOK, map[string]interface{}{"id": id}
}

func miscFromReq(req *http.Request) (*miscPayload, error) {
	misc := miscPayload{}

	dec := json.NewDecoder(req.Body)
	err := dec.Decode(&misc)

	return &misc, errors.Wrap(err, "decoding Misc request failed")
}

func cleanMiscs(misc *miscPayload) {
	misc.Description = strings.TrimSpace(misc.Description)
	misc.Notes = strings.TrimSpace(misc.Notes)
}

func insertMisc(ctx context.Context, tx *sqlx.Tx, misc *miscPayload) (int, error) {
	insertQuery := `insert into automotive_claim_complaint_miscs(automotive_claim_complaint_id, description, requested, approved, notes)
	 values (:automotive_claim_complaint_id, :description, :requested, :approved, :notes) returning id`
	id := 0

	stmt, err := tx.PrepareNamedContext(ctx, insertQuery)
	if err != nil {
		return id, errors.Wrap(err, "an error occurred in PrepareNamed function while adding Context.")
	}
	defer func() { _ = stmt.Close() }()

	err = stmt.GetContext(ctx, &id, misc)
	if err != nil {
		return id, errors.Wrap(err, "an error occurred while trying to add the Part to the database.")
	}

	return id, err
}

func miscs(ctx context.Context, complaintID int) ([]miscPayload, error) {
	exists, err := complaintExists(complaintID)
	if err != nil {
		return nil, errors.Wrap(err, "error getting complaint from database")
	}
	if !exists {
		return nil, errors.Wrap(err, "complaint does not exist")
	}

	ids := []int{}
	err = db.Get().SelectContext(ctx, &ids, `select id from automotive_claim_complaint_miscs where automotive_claim_complaint_id = $1 order by id asc`, complaintID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, errors.Wrap(err, "error loading miscs list ")
	}

	miscs := make([]miscPayload, len(ids))
	for i, id := range ids {
		misc, err := miscByID(ctx, id)
		if err != nil {
			return miscs, errors.Wrap(err, "error getting miscs data")
		}
		miscs[i] = *misc
	}

	return miscs, nil
}

func miscByID(ctx context.Context, ID int) (*miscPayload, error) {
	miscFromDB := struct {
		ID                         int                 `json:"id" db:"id"`
		AutomotiveClaimComplaintID int                 `json:"automotive_claim_complaint_id" db:"automotive_claim_complaint_id"`
		Description                sql.NullString      `json:"description" db:"description"`
		Requested                  decimal.NullDecimal `json:"requested" db:"requested"`
		Approved                   decimal.NullDecimal `json:"approved" db:"approved"`
		Notes                      sql.NullString      `json:"notes" db:"notes"`
	}{}

	err := db.Get().Unsafe().Get(&miscFromDB, `select * from automotive_claim_complaint_miscs where id = $1`, ID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, errors.Wrap(err, "error loading miscs")

	}
	misc := miscPayload{}

	misc.ID = miscFromDB.ID
	misc.AutomotiveClaimComplaintID = miscFromDB.AutomotiveClaimComplaintID

	if miscFromDB.Requested.Valid {
		misc.Requested = miscFromDB.Requested.Decimal
	}
	if miscFromDB.Approved.Valid {
		misc.Approved = miscFromDB.Approved.Decimal
	}
	if miscFromDB.Notes.Valid {
		misc.Notes = miscFromDB.Notes.String
	}
	if miscFromDB.Description.Valid {
		misc.Description = miscFromDB.Description.String
	}
	return &misc, nil
}

func updateMisc(ctx context.Context, tx *sqlx.Tx, misc *miscPayload) error {

	query := `update automotive_claim_complaint_miscs
    set description = :description,
    requested = :requested,
    approved = :approved,
    notes = :notes
    where id = :id`

	stmt, err := tx.PrepareNamedContext(ctx, query)
	if err != nil {
		return errors.Wrap(err, "error updating Misc, PrepareNamed failed")
	}
	defer func() { _ = stmt.Close() }()

	_, err = stmt.ExecContext(ctx, misc)
	if err != nil {
		return errors.Wrap(err, "error updating misc, database error")
	}
	return nil
}

func deleteMiscByComplaintID(tx *sqlx.Tx, complaintID int) error {
	_, err := tx.Exec(`delete from automotive_claim_complaint_miscs where automotive_claim_complaint_id = $1`, complaintID)
	if err != nil {
		return errors.Wrap(err, "could not delete miscs")
	}
	return nil
}
