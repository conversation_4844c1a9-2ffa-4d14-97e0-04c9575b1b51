package auto

import (
	"context"
	"database/sql"
	"encoding/json"
	"net/http"
	"strconv"
	"strings"
	"time"

	"phizz/db"
	"phizz/dms"
	"phizz/handlers"

	"github.com/go-chi/chi"
	"github.com/jmoiron/sqlx"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
	"gopkg.in/guregu/null.v3"
)

type complaintPayload struct {
	ID                  int             `json:"id" db:"id"`
	ComplaintDate       time.Time       `json:"complaint_date" db:"complaint_date"`
	AutomotiveClaimID   int             `json:"automotive_claim_id" db:"automotive_claim_id"`
	Complaint           string          `json:"complaint" db:"complaint"`
	TechID              string          `json:"tech_id" db:"tech_id"`
	Cause               string          `json:"cause" db:"cause"`
	Correction          string          `json:"correction" db:"correction"`
	RepairCode          string          `json:"repair_code" db:"repair_code"`
	AddLineFlag         bool            `json:"add_line_flag" db:"add_line_flag"`
	GoodwillFlag        bool            `json:"goodwill_flag" db:"goodwill_flag"`
	GoodwillDescription string          `json:"goodwill_description" db:"goodwill_description"`
	GoodwillAmount      decimal.Decimal `json:"goodwill_amount" db:"goodwill_amount"`
	Status              string          `json:"status" db:"status"`
	PartsTotal          decimal.Decimal `json:"parts_total" db:"parts_total"`
	LaborTotal          decimal.Decimal `json:"labor_total" db:"labor_total"`
	TowingsTotal        decimal.Decimal `json:"towings_total" db:"towings_total"`
	RentalsTotal        decimal.Decimal `json:"rentals_total" db:"rentals_total"`
	SubletsTotal        decimal.Decimal `json:"sublets_total" db:"sublets_total"`
	MiscsTotal          decimal.Decimal `json:"miscs_total" db:"miscs_total"`
	Towings             []towingPayload `json:"towings" db:"-"`
	Rentals             []rentalPayload `json:"rentals" db:"-"`
	Sublets             []subletPayload `json:"sublets" db:"-"`
	Miscs               []miscPayload   `json:"miscs" db:"-"`
	Parts               []partPayload   `json:"parts" db:"-"`
	Labor               []laborPayload  `json:"labors" db:"-"`
	IsManual            bool            `json:"is_manual" db:"is_manual"`
	LineCode            string          `json:"line_code" db:"line_code"`
	UnidataRepairNumber int             `json:"_" db:"unidata_repair_number"`
	StatusReason        null.String     `json:"status_reason" db:"status_reason"`
	StatusOtherReason   null.String     `json:"status_other_reason" db:"status_other_reason"`
	VCRepairCode        null.Int        `json:"vc_repair_code" db:"vc_repair_code"`
}

// ComplaintShow returns single complaint data
func ComplaintShow(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	ctx := req.Context()
	complaintIDStr := chi.URLParam(req, "complaint_id")
	complaintID, err := strconv.Atoi(complaintIDStr)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Number conversion error for complaintID", nil)
	}
	complaint, err := complaintByID(ctx, complaintID)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Could not get complaint", nil)
	}
	return http.StatusOK, map[string]interface{}{"complaint": complaint}
}

func complaintExists(complaintID int) (bool, error) {
	id := 0
	err := db.Get().Get(&id, `select id from automotive_claim_complaints where id = $1`, complaintID)
	if err != nil {
		if err == sql.ErrNoRows {
			return false, nil
		}
		return false, err
	}
	return true, nil
}

// ComplaintCreate creates a new complaint
func ComplaintCreate(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	ctx := req.Context()
	claimIDStr := chi.URLParam(req, "claim_id")
	claimID, err := strconv.Atoi(claimIDStr)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Number conversion error for claimID", nil)
	}
	claimExist, err := claimExists(claimID)
	if !claimExist || err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Could not get claim", nil)
	}
	complaint, err := complaintFromReq(req)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Malformed Complaint data for create.", nil)
	}

	complaint.AutomotiveClaimID = claimID
	complaint.Status = db.AutoClaimComplaintStatusOpen
	complaint.Status = db.AutoClaimStatusOpen
	complaint.IsManual = true

	cleanComplaint(complaint)
	// TODO: Initially these fields are not available, finalize proper flow when create complaint
	//formErrors, err := validateComplaint(complaint)
	//if err != nil {
	//	return http.StatusInternalServerError, controller.ErrorMessage(errors.Wrap(err, "Error validating Complaint"),
	//		"An error occurred validating the form values.", nil)
	//}
	//
	//if len(formErrors) > 0 {
	//	return http.StatusBadRequest, controller.ErrorMessage(nil, "Form validations errors.",
	//		map[string]interface{}{"errors": formErrors})
	//}
	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error beginning transaction for Auto claim update", nil)
	}

	complaintID, err := insertComplaint(tx, complaint)
	if err != nil {
		_ = tx.Rollback()
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error inserting Complaint", nil)
	}

	// Add note after creating new complaint successfully
	_, err = InsertRecordNote(ctx, &RecordNotePayload{
		AutomotiveClaimID: claimID,
		IsManual:          false,
		NotesText:         "New complaint added",
		CreatedByUserID:   user.ID,
	}, tx)

	if err != nil {
		_ = tx.Rollback()
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error inserting Complaint : Failed to add system note", nil)
	}

	err = tx.Commit()
	if err != nil {
		err = errors.Wrap(err, "Database error committing transaction for Auto claim update")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error updating Auto claim", nil)
	}

	return http.StatusOK, map[string]interface{}{"complaint_id": complaintID}
}

func insertComplaint(tx *sqlx.Tx, complaint *complaintPayload) (int, error) {
	insertQuery := `insert into automotive_claim_complaints(
		automotive_claim_id,
		complaint,
		cause,
		tech_id,
		complaint_date,
		status,
		is_manual,
		add_line_flag,
		correction,
		line_code, 
        status_reason,
        status_other_reason,
		vc_repair_code)
	 	values (
		:automotive_claim_id,
		:complaint,
		:cause,
		:tech_id,
		now() at time zone 'utc',
		:status,
		:is_manual,
		:add_line_flag,
		:correction,
		:line_code, 
		:status_reason,
		:status_other_reason,
		:vc_repair_code) returning id`
	id := 0

	stmt, err := tx.PrepareNamed(insertQuery)
	if err != nil {
		return id, errors.Wrap(err, "An error occurred in PrepareNamed function while adding Complaint.")
	}
	defer func() { _ = stmt.Close() }()

	err = stmt.Get(&id, complaint)
	if err != nil {
		return id, errors.Wrap(err, "An error occurred while trying to add the complaint to the database.")
	}

	return id, err
}

func complaintFromReq(req *http.Request) (*complaintPayload, error) {
	recordNote := complaintPayload{}

	dec := json.NewDecoder(req.Body)
	err := dec.Decode(&recordNote)

	return &recordNote, errors.Wrap(err, "decoding Part request failed")
}

func cleanComplaint(complaint *complaintPayload) {
	// TODO
}

func validateComplaints(complaints []complaintPayload, formErrors map[string]string) map[string]string {
	// Validate complaints in the claim
	for _, complaint := range complaints {
		formErrors = validateComplaint(&complaint, formErrors)
		if complaint.Status == db.AutoClaimComplaintStatusOpen {
			formErrors["complaint_status"] = "All complaints needs to be either in Payable or Denied status"
		}
	}
	return formErrors
}

func validateComplaint(complaint *complaintPayload, formErrors map[string]string) map[string]string {
	if complaint.Status == db.AutoClaimComplaintStatusPayable {
		if complaint.Complaint == "" {
			formErrors["complaint"] = "Complaint is required."
		}
		if complaint.Cause == "" {
			formErrors["cause"] = "Cause is required."
		}
		if complaint.Correction == "" {
			formErrors["correction"] = "Correction is required."
		}
		if complaint.RepairCode == "" {
			formErrors["repair_code"] = "Repair code is required."
		}
		for _, labor := range complaint.Labor {
			if strings.TrimSpace(labor.LaborDescription) == "" {
				formErrors["labor_description"] = "Labor description is required."
			}
		}
	} else if complaint.Status == db.AutoClaimComplaintStatusDenied &&
		len(complaint.StatusReason.String) == 0 {
		formErrors["complaint"] = "Status reason is required."
	} else if complaint.Status == db.AutoClaimComplaintStatusDenied &&
		complaint.StatusReason.String == db.AutoClaimComplaintStatusReasonOther &&
		len(complaint.StatusOtherReason.String) == 0 {
		formErrors["complaint"] = "Denial note is required."
	}

	return formErrors
}

func complaints(ctx context.Context, claimID int, orderBy string) ([]complaintPayload, error) {
	exists, err := claimExists(claimID)
	if err != nil {
		return nil, errors.Wrap(err, "Error getting Automotive claim from database")
	}
	if !exists {
		return nil, errors.Wrap(err, "Automotive claim does not exist")
	}

	ids := []int{}
	err = db.Get().Unsafe().Select(&ids, `select id from automotive_claim_complaints where automotive_claim_id = $1 order by `+orderBy+` asc`, claimID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, errors.Wrap(err, "error loading complaint list ")
	}
	complaints := make([]complaintPayload, len(ids))
	for i, id := range ids {
		complaint, err := complaintByID(ctx, id)
		if err != nil {
			return complaints, errors.Wrap(err, "Error getting complaint data")
		}
		complaints[i] = *complaint
	}
	return complaints, nil
}

func complaintByID(ctx context.Context, ID int) (*complaintPayload, error) {
	complaintFromDB := struct {
		ID                  int                 `json:"id" db:"id"`
		ComplaintDate       time.Time           `json:"complaint_date" db:"complaint_date"`
		AutomotiveClaimID   int                 `json:"automotive_claim_id" db:"automotive_claim_id"`
		Complaint           sql.NullString      `json:"complaint" db:"complaint"`
		TechID              null.String         `json:"tech_id" db:"tech_id"`
		Cause               sql.NullString      `json:"cause" db:"cause"`
		Correction          sql.NullString      `json:"correction" db:"correction"`
		RepairCode          sql.NullString      `json:"repair_code" db:"repair_code"`
		AddLineFlag         null.Bool           `json:"add_line_flag" db:"add_line_flag"`
		GoodwillFlag        null.Bool           `json:"goodwill_flag" db:"goodwill_flag"`
		GoodwillDescription sql.NullString      `json:"goodwill_description" db:"goodwill_description"`
		GoodwillAmount      decimal.NullDecimal `json:"goodwill_amount" db:"goodwill_amount"`
		Status              null.String         `json:"status" db:"status"`
		PartsTotal          decimal.NullDecimal `json:"parts_total" db:"parts_total"`
		LaborTotal          decimal.NullDecimal `json:"labor_total" db:"labor_total"`
		TowingsTotal        decimal.NullDecimal `json:"towings_total" db:"towings_total"`
		RentalsTotal        decimal.NullDecimal `json:"rentals_total" db:"rentals_total"`
		SubletsTotal        decimal.NullDecimal `json:"sublets_total" db:"sublets_total"`
		MiscsTotal          decimal.NullDecimal `json:"miscs_total" db:"miscs_total"`
		IsManual            null.Bool           `json:"is_manual" db:"is_manual"`
		Parts               []partPayload       `json:"parts" db:"-"`
		Labors              []laborPayload      `json:"labors" db:"-"`
		LineCode            null.String         `json:"line_code" db:"line_code"`
		UnidataRepairNumber int                 `json:"_" db:"unidata_repair_number"`
		StatusReason        null.String         `json:"status_reason" db:"status_reason"`
		StatusOtherReason   null.String         `json:"status_other_reason" db:"status_other_reason"`
		VCRepairCode        null.Int            `json:"vc_repair_code" db:"vc_repair_code"`
	}{}

	err := db.Get().Unsafe().Get(&complaintFromDB, `select * from automotive_claim_complaints where id = $1`, ID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, errors.Wrap(err, "error loading complaint")
	}
	complaint := complaintPayload{}

	complaint.ID = complaintFromDB.ID
	complaint.ComplaintDate = complaintFromDB.ComplaintDate
	complaint.AutomotiveClaimID = complaintFromDB.AutomotiveClaimID
	if complaintFromDB.LineCode.Valid {
		complaint.LineCode = complaintFromDB.LineCode.String
	}
	if complaintFromDB.Complaint.Valid {
		complaint.Complaint = complaintFromDB.Complaint.String
	}
	if complaintFromDB.Cause.Valid {
		complaint.Cause = complaintFromDB.Cause.String
	}
	if complaintFromDB.Correction.Valid {
		complaint.Correction = complaintFromDB.Correction.String
	}

	if complaintFromDB.RepairCode.Valid {
		complaint.RepairCode = complaintFromDB.RepairCode.String
	}

	if complaintFromDB.AddLineFlag.Valid {
		complaint.AddLineFlag = complaintFromDB.AddLineFlag.Bool
	}
	if complaintFromDB.GoodwillFlag.Valid {
		complaint.GoodwillFlag = complaintFromDB.GoodwillFlag.Bool
	}
	if complaintFromDB.GoodwillDescription.Valid {
		complaint.GoodwillDescription = complaintFromDB.GoodwillDescription.String
	}
	if complaintFromDB.GoodwillAmount.Valid {
		complaint.GoodwillAmount = complaintFromDB.GoodwillAmount.Decimal
	}
	if complaintFromDB.Status.Valid {
		complaint.Status = complaintFromDB.Status.String
	}
	if complaintFromDB.PartsTotal.Valid {
		complaint.PartsTotal = complaintFromDB.PartsTotal.Decimal
	}
	if complaintFromDB.LaborTotal.Valid {
		complaint.LaborTotal = complaintFromDB.LaborTotal.Decimal
	}

	if complaintFromDB.TowingsTotal.Valid {
		complaint.TowingsTotal = complaintFromDB.TowingsTotal.Decimal
	}

	if complaintFromDB.RentalsTotal.Valid {
		complaint.RentalsTotal = complaintFromDB.RentalsTotal.Decimal
	}

	if complaintFromDB.SubletsTotal.Valid {
		complaint.SubletsTotal = complaintFromDB.SubletsTotal.Decimal
	}

	if complaintFromDB.MiscsTotal.Valid {
		complaint.MiscsTotal = complaintFromDB.MiscsTotal.Decimal
	}

	if complaintFromDB.IsManual.Valid {
		complaint.IsManual = complaintFromDB.IsManual.Bool
	}
	complaint.UnidataRepairNumber = complaintFromDB.UnidataRepairNumber
	if complaintFromDB.TechID.Valid {
		complaint.TechID = complaintFromDB.TechID.String
	}

	complaint.Parts, err = parts(complaintFromDB.ID)
	if err != nil {
		return nil, errors.Wrap(err, "Error getting parts for complaint")
	}
	complaint.Labor, err = labors(complaintFromDB.ID)
	if err != nil {
		return nil, errors.Wrap(err, "Error getting labors for complaint")
	}
	complaint.Towings, err = towings(ctx, complaintFromDB.ID)
	if err != nil {
		return nil, errors.Wrap(err, "Error getting towings for complaint")
	}
	complaint.Rentals, err = rentals(ctx, complaintFromDB.ID)
	if err != nil {
		return nil, errors.Wrap(err, "Error getting rentals for complaint")
	}
	complaint.Sublets, err = sublets(ctx, complaintFromDB.ID)
	if err != nil {
		return nil, errors.Wrap(err, "Error getting sublets for complaint")
	}
	complaint.Miscs, err = miscs(ctx, complaintFromDB.ID)
	if err != nil {
		return nil, errors.Wrap(err, "Error getting miscs for complaint")
	}
	complaint.StatusReason = complaintFromDB.StatusReason
	complaint.StatusOtherReason = complaintFromDB.StatusOtherReason
	complaint.VCRepairCode = complaintFromDB.VCRepairCode

	return &complaint, nil
}

func updateComplaint(tx *sqlx.Tx, complaint *complaintPayload) error {
	validateError := make(map[string]string)
	validateError = validateComplaint(complaint, validateError)
	if err, ok := validateError["complaint"]; ok {
		return errors.New(err)
	}

	// The repair code in automotive claims is of the form `10001 Engine Code`.
	// where the numeric part is representing the code column in whiz.vehicle_components.
	// this part (10001) will now be stored in vc_repair_code column
	// vc_repair_code column will be used by BI team for their queries
	if complaint.RepairCode != "" {
		parts := strings.Split(complaint.RepairCode, " ")
		if len(parts) > 1 {
			code, err := strconv.Atoi(parts[0])
			if err != nil {
				return errors.Wrap(err, "error in parsing repair code")
			}
			if code > 0 {
				complaint.VCRepairCode.SetValid(int64(code))
			}
		}
	}

	query := `update automotive_claim_complaints
		set complaint_date = :complaint_date,
		complaint = :complaint,
		tech_id = :tech_id,
		cause = :cause,
		correction = :correction,
		repair_code = :repair_code,
		add_line_flag = :add_line_flag,
		goodwill_flag = :goodwill_flag,
		goodwill_description = :goodwill_description,
		goodwill_amount = :goodwill_amount,
		status = :status,
		parts_total = :parts_total,
		labor_total = :labor_total,
		towings_total = :towings_total,
		rentals_total = :rentals_total,
		sublets_total = :sublets_total,
		miscs_total = :miscs_total,
		status_reason = :status_reason,
        status_other_reason = :status_other_reason,
		vc_repair_code = :vc_repair_code
	where id = :id`

	stmt, err := tx.PrepareNamed(query)
	if err != nil {
		return errors.Wrap(err, "Error updating Complaint, PrepareNamed failed")
	}
	defer func() { _ = stmt.Close() }()

	_, err = stmt.Exec(complaint)
	if err != nil {
		return errors.Wrap(err, "Error updating Complaint, database error")
	}
	return nil
}

func updateLineCode(ctx context.Context, tx *sqlx.Tx, id int, lineCode string) error {
	query := `update automotive_claim_complaints set line_code = $1  where id = $2`
	_, err := tx.ExecContext(ctx, query, lineCode, id)
	return err
}

func complaintCreateRO(tx *sqlx.Tx, roLines []*dms.ROLine, claimID int, productCode string, roDate time.Time, dmsProvider string) ([]complaintPayload, error) {
	var newComplaints []complaintPayload

	cdkLaborTypes := db.CDKLaborTypes
	if productCode == db.ProductCodeMaintenance {
		cdkLaborTypes = append(cdkLaborTypes, db.CDKLaborTypeCELCXMaintOnly, db.CDKLaborTypeWLCXMaintOnly)
	}

	for _, roLine := range roLines {
		isValidROLine := true
		if dmsProvider == db.DMSProviderCDK {
			isValidROLine = isRelevantCDKComplaint(cdkLaborTypes, roLine.LaborTypes)
		}
		if isValidROLine {
			complaint := complaintPayload{}
			complaint.AutomotiveClaimID = claimID
			complaint.ComplaintDate = roDate
			complaint.LineCode = roLine.LineCode
			complaint.Cause = roLine.Cause
			complaint.Correction = roLine.Correction
			complaint.Complaint = roLine.LineCode + " - " + roLine.ComplaintDescription
			if roLine.AddOnFlag == "Y" {
				complaint.AddLineFlag = true
			}
			complaint.IsManual = false
			complaint.Status = db.AutoClaimComplaintStatusOpen
			complaint.TechID = roLine.TechID

			complaintID, err := insertComplaint(tx, &complaint)
			if err != nil {
				return nil, errors.Wrap(err, "Error while creating CDK complaints")
			}

			for _, roLabor := range roLine.Labors {
				isValidLaborType := true
				if dmsProvider == db.DMSProviderCDK {
					isValidLaborType = contains(cdkLaborTypes, roLabor.LaborType)
				}
				if isValidLaborType {
					labor := laborPayload{}
					labor.AutomotiveClaimComplaintID = complaintID
					labor.Requested = roLabor.SoldHours
					labor.Billed = roLabor.LaborSale
					_, err = insertLabor(tx, &labor)
					if err != nil {
						return nil, errors.Wrap(err, "Error while creating CDK complaint labor")
					}
					complaint.Labor = append(complaint.Labor, labor)
				}
			}

			for _, roPart := range roLine.Parts {
				part := partPayload{}
				part.AutomotiveClaimComplaintID = complaintID
				part.Cost = roPart.Cost
				part.MSRP = roPart.List
				part.Approved = roPart.List // Initially set Approved to MSRP from RO, user can overwrite the value later
				part.Description = roPart.Description
				part.PartNumber = roPart.PartNumber
				part.Quantity = roPart.Quantity
				part.Requested = roPart.PartsSale
				_, err = insertPart(tx, &part)
				if err != nil {
					return nil, errors.Wrap(err, "Error while creating CDK complaint parts")
				}
				complaint.Parts = append(complaint.Parts, part)
			}
			newComplaints = append(newComplaints, complaint)
		}
	}
	return newComplaints, nil
}

// check if any of labor type matches with any of valid labortype
func isRelevantCDKComplaint(cdkLaborTypes []string, laborTypes []string) bool {
	for _, laborType := range laborTypes {
		if contains(cdkLaborTypes, laborType) {
			return true
		}
	}
	return false
}

func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

func getGoodWillAmount(tx *sqlx.Tx, claimID int) (decimal.Decimal, error) {
	var goodwillAmount sql.NullFloat64
	query := `select sum(goodwill_amount) from automotive_claim_complaints where automotive_claim_id = $1`
	err := tx.Get(&goodwillAmount, query, claimID)
	return decimal.NewFromFloat(goodwillAmount.Float64), err
}

// ComplaintDelete Deletes given complaint
func ComplaintDelete(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	ctx := req.Context()
	complaintID, err := strconv.Atoi(chi.URLParam(req, "id"))
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(errors.New("In valid complaint id"), "Failed to delete complaint", nil)
	}

	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error beginning transaction for deleting complaint", nil)
	}

	claimID, err := deleteComplaint(tx, complaintID)
	if err != nil {
		_ = tx.Rollback()
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error deleting Complaint", nil)
	}

	// Add note after deleting complaint successfully
	_, err = InsertRecordNote(ctx, &RecordNotePayload{
		AutomotiveClaimID: claimID,
		IsManual:          false,
		NotesText:         "Complaint deleted",
		CreatedByUserID:   user.ID,
	}, tx)

	if err != nil {
		_ = tx.Rollback()
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error deleting Complaint : Failed to add system note", nil)
	}

	err = tx.Commit()
	if err != nil {
		err = errors.Wrap(err, "Database error committing transaction for complaint delete")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error deleting complaint", nil)
	}

	return http.StatusOK, map[string]interface{}{"complaint_id": complaintID}
}

func deleteComplaint(tx *sqlx.Tx, complaintID int) (int, error) {
	var autoClaimID int
	err := deletePartsByComplaintID(tx, complaintID)
	if err != nil {
		return autoClaimID, errors.Wrap(err, "error deleting parts ")
	}
	err = deleteLaborsByComplaintID(tx, complaintID)
	if err != nil {
		return autoClaimID, errors.Wrap(err, "error deleting labors ")
	}
	err = deleteTowingByComplaintID(tx, complaintID)
	if err != nil {
		return autoClaimID, errors.Wrap(err, "error deleting towings ")
	}
	err = deleteSubletByComplaintID(tx, complaintID)
	if err != nil {
		return autoClaimID, errors.Wrap(err, "error deleting sublets ")
	}
	err = deleteRentalByComplaintID(tx, complaintID)
	if err != nil {
		return autoClaimID, errors.Wrap(err, "error deleting rentals ")
	}
	err = deleteMiscByComplaintID(tx, complaintID)
	if err != nil {
		return autoClaimID, errors.Wrap(err, "error deleting miscs ")
	}
	err = tx.Get(&autoClaimID, `delete from automotive_claim_complaints where id = $1 returning automotive_claim_id`, complaintID)
	if err != nil {
		return autoClaimID, errors.Wrap(err, "Could not delete complaint")
	}
	return autoClaimID, nil
}
