package auto

import (
	"github.com/go-chi/chi"
	"net/http"
	"net/http/httptest"
	"regexp"
	"testing"
	"time"

	"phizz/db"
	"phizz/handlers"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/jmoiron/sqlx"
)

func TestFacilityZoneIndex(t *testing.T) {
	mdb, mock, err := sqlmock.New()
	if err != nil {
		t.Fatal(err)
	}
	dbx := sqlx.NewDb(mdb, "sqlmock")
	db.DB = dbx
	q := regexp.QuoteMeta

	mock.ExpectQuery(q(`select automotive_facility_zones.id, name, owner_id, first_name || ' ' || last_name as owner_name, on_hold from automotive_facility_zones join users on owner_id = users.id order by name asc`)).
		WillReturnRows(sqlmock.NewRows([]string{"id", "name", "owner_id", "owner_name", "on_hold"}).
			AddRow(1, "Zone1", 5850, "<PERSON>", false))

	mock.ExpectQuery(q(`select facility_code from automotive_facilities where zone_id = $1`)).
		WithArgs(1).
		WillReturnRows(sqlmock.NewRows([]string{"facility_code"}).AddRow("ABCAZ"))

	req, err := http.NewRequest("GET", "/api/zones", nil)
	if err != nil {
		t.Fatal(err)
	}

	rr := httptest.NewRecorder()
	handler := http.HandlerFunc(handlers.APIHandler(ZoneIndex))

	r := chi.NewRouter()
	r.HandleFunc("/api/zones", handler)
	r.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v",
			status, http.StatusOK)
	}

	expected := `{"count":1,"zones":[{"id":1,"name":"Zone1","owner_id":5850,"owner_name":"Bob Jones","on_hold":false,"on_hold_start":"0001-01-01T00:00:00Z","on_hold_end":"0001-01-01T00:00:00Z","temporary_owner_id":0,"temporary_owner_name":"","facilities":["ABCAZ"]}]}`
	if rr.Body.String() != expected {
		t.Errorf("handler returned unexpected body: got %v want %v",
			rr.Body.String(), expected)
	}

	if err := mock.ExpectationsWereMet(); err != nil {
		t.Errorf("there were unfulfilled expectations: %s", err)
	}
}

func TestFacilityZoneIndexOnHold(t *testing.T) {
	mdb, mock, err := sqlmock.New()
	if err != nil {
		t.Fatal(err)
	}
	dbx := sqlx.NewDb(mdb, "sqlmock")
	db.DB = dbx
	q := regexp.QuoteMeta

	mock.ExpectQuery(q(`select automotive_facility_zones.id, name, owner_id, first_name || ' ' || last_name as owner_name, on_hold from automotive_facility_zones join users on owner_id = users.id order by name asc`)).
		WillReturnRows(sqlmock.NewRows([]string{"id", "name", "owner_id", "owner_name", "on_hold"}).
			AddRow(1, "Zone1", 5850, "Bob Jones", true))

	mock.ExpectQuery(q(`select temporary_owner_id, users2.first_name || ' ' || users2.last_name as temporary_owner_name, users1.time_off_start as on_hold_start, users1.time_off_end as on_hold_end from automotive_facility_zones join users as users1 on owner_id = users1.id join users as users2 on temporary_owner_id = users2.id where automotive_facility_zones.id=$1 order by name asc`)).
		WithArgs(1).
		WillReturnRows(sqlmock.NewRows([]string{"temporary_owner_id", "temporary_owner_name", "on_hold_start", "on_hold_end"}).
			AddRow(1, "owner", time.Time{}, time.Time{}))

	mock.ExpectQuery(q(`select facility_code from automotive_facilities where zone_id = $1`)).
		WithArgs(1).
		WillReturnRows(sqlmock.NewRows([]string{"facility_code"}).AddRow("ABCAZ"))

	req, err := http.NewRequest("GET", "/api/zones", nil)
	if err != nil {
		t.Fatal(err)
	}

	rr := httptest.NewRecorder()
	handler := http.HandlerFunc(handlers.APIHandler(ZoneIndex))

	r := chi.NewRouter()
	r.HandleFunc("/api/zones", handler)
	r.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v",
			status, http.StatusOK)
	}

	expected := `{"count":1,"zones":[{"id":1,"name":"Zone1","owner_id":5850,"owner_name":"Bob Jones","on_hold":true,"on_hold_start":"0001-01-01T00:00:00Z","on_hold_end":"0001-01-01T00:00:00Z","temporary_owner_id":1,"temporary_owner_name":"owner","facilities":["ABCAZ"]}]}`
	if rr.Body.String() != expected {
		t.Errorf("handler returned unexpected body: got %v want %v",
			rr.Body.String(), expected)
	}

	if err := mock.ExpectationsWereMet(); err != nil {
		t.Errorf("there were unfulfilled expectations: %s", err)
	}
}

func TestUnassignedAgents(t *testing.T) {
	mdb, mock, err := sqlmock.New()
	if err != nil {
		t.Fatal(err)
	}
	dbx := sqlx.NewDb(mdb, "sqlmock")
	db.DB = dbx
	q := regexp.QuoteMeta

	mock.ExpectQuery(q(`select id, first_name || ' ' || last_name as agent_name from users where id not in (select owner_id from automotive_facility_zones) and exist(roles, 'auto_claims')`)).
		WillReturnRows(sqlmock.NewRows([]string{"id", "agent_name"}).
			AddRow(1, "Bob Jones"))

	req, err := http.NewRequest("GET", "/api/automotive-claims/unassigned-agents", nil)
	if err != nil {
		t.Fatal(err)
	}

	rr := httptest.NewRecorder()
	handler := http.HandlerFunc(handlers.APIHandler(UnassignedAgents))

	r := chi.NewRouter()
	r.HandleFunc("/api/automotive-claims/unassigned-agents", handler)
	r.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v",
			status, http.StatusOK)
	}

	expected := `{"agents":[{"id":1,"agent_name":"Bob Jones"}],"count":1}`
	if rr.Body.String() != expected {
		t.Errorf("handler returned unexpected body: got %v want %v",
			rr.Body.String(), expected)
	}

	if err := mock.ExpectationsWereMet(); err != nil {
		t.Errorf("there were unfulfilled expectations: %s", err)
	}
}
