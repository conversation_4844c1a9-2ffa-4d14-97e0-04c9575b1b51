package auto

import (
	"context"
	"net/http"
	"strconv"

	"phizz/db"
	"phizz/handlers"
	"phizz/intacct"

	"github.com/go-chi/chi"
	"github.com/jung-kurt/gofpdf"
	newrelic "github.com/newrelic/go-agent"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
)

// ClaimPdf generates the pdf for auto claim
func ClaimPdf(w http.ResponseWriter, req *http.Request, _ db.User) {

	id := chi.URLParam(req, "id")
	txn := w.(newrelic.Transaction)
	autoClaim, err := ClaimSelect(txn, req, id)
	if err != nil {
		_ = handlers.Renderer().Text(w, http.StatusInternalServerError, "Error getting Auto claim from database")
		handlers.ReportError(req, errors.Wrap(err, "error getting auto claim from database"))
		return
	}

	ctx := req.Context()
	var paymentDetails, negativeClaims []paymentPayload
	if isPayableStatus(autoClaim.Status) {
		paymentDetails, negativeClaims, err = getPaymentDetailsByClaimID(ctx, autoClaim.ID, autoClaim.Status)
		if err != nil {
			_ = handlers.Renderer().Text(w, http.StatusInternalServerError, "Error getting Auto claim payment from database")
			handlers.ReportError(req, errors.Wrap(err, "error getting auto claim payment from database"))
			return
		}
	}

	fpdf := gofpdf.New("Portrait", "in", "Letter", "")

	if autoClaim.ChargeBack {
		claimToPdfChargeback(fpdf, autoClaim, paymentDetails)
	} else {
		claimToPdf(ctx, fpdf, autoClaim, paymentDetails, negativeClaims)
	}

	w.Header().Set("Content-Type", "application/pdf")
	w.WriteHeader(http.StatusOK)
	err = fpdf.Output(w)
	if err != nil {
		err = errors.Wrap(err, "error writing auto claim pdf")
		_ = handlers.Renderer().Text(w, http.StatusBadRequest, "Error writing auto claim pdf")
		handlers.ReportError(req, err)
		return
	}
}

// claimToPdf generates the pdf for auto claim
func claimToPdf(ctx context.Context, fpdf *gofpdf.Fpdf, autoClaim *ClaimPayload, paymentDetail, negativeClaims []paymentPayload) {
	const dateFormat = "2006/01/02"
	const invoiceDateFormat = "01/02/2006"
	const yesFlag = "[Y]"
	const noFlag = "[N]"
	printFlag := func(flag bool) {
		flagStr := noFlag
		if flag {
			flagStr = yesFlag
		}
		fpdf.CellFormat(0.15, 0.15, flagStr, "", 0, "CM", true, 0, "")
	}

	fpdf.SetFillColor(211, 211, 211)
	fpdf.AddPage()
	fpdf.SetFont("Helvetica", "B", 16)
	fpdf.CellFormat(7, 0.2, autoClaim.ContractNumber, "", 0, "C", false, 0, "")
	fpdf.Ln(0.3)
	if !autoClaim.InvoicedAt.Time.IsZero() {
		fpdf.SetFont("Helvetica", "B", 8)
		fpdf.CellFormat(7, 0.2, "Invoice Date:"+autoClaim.InvoicedAt.Time.Local().Format(invoiceDateFormat), "", 0, "C", false, 0, "")
	}
	fpdf.Ln(0.3)
	fpdf.SetFont("Helvetica", "B", 16)
	fpdf.CellFormat(7, 0.2, autoClaim.Status, "", 0, "C", false, 0, "")
	fpdf.Ln(0.3)

	startY := fpdf.GetY()
	// 1st column
	fpdf.SetFont("Helvetica", "B", 8)
	fpdf.Cell(1, 0.2, autoClaim.CustomerName)
	fpdf.SetFont("Helvetica", "", 8)
	fpdf.Ln(-1)
	fpdf.Cell(1, 0.2, autoClaim.StreetAddress)
	fpdf.Ln(-1)
	fpdf.Cell(2, 0.2, autoClaim.City+", "+autoClaim.State+" "+autoClaim.PostalCode)
	fpdf.Ln(-1)
	fpdf.Cell(2, 0.2, autoClaim.PhoneNumber)
	fpdf.Ln(-1)
	fpdf.SetFont("Helvetica", "B", 8)
	fpdf.Cell(1, 0.2, autoClaim.VIN)
	fpdf.Ln(-1)
	fpdf.SetFont("Helvetica", "", 8)
	fpdf.Cell(1, 0.2, autoClaim.Year+" "+autoClaim.Make+" "+autoClaim.Model)
	fpdf.Ln(-1)
	fpdf.SetFont("Helvetica", "B", 8)
	fpdf.Cell(1, 0.2, autoClaim.ReviewInspection)
	// 2nd Column
	fpdf.SetXY(2.2, startY)
	fpdf.SetFont("Helvetica", "B", 8)
	fpdf.MultiCell(0.75, 0.2, "Originating Dealership", "", "", false)
	fpdf.SetFont("Helvetica", "", 8)
	fpdf.SetXY(3, startY)
	fpdf.MultiCell(1, 0.2, autoClaim.OriginatingDealership, "", "", false)
	fpdf.Ln(-1)

	fpdf.SetX(2.2)
	fpdf.SetFont("Helvetica", "B", 8)
	fpdf.Cell(0.75, 0.2, "Miles")
	fpdf.SetFont("Helvetica", "", 8)
	fpdf.Cell(1, 0.2, strconv.Itoa(autoClaim.BeginningMiles)+" to "+strconv.Itoa(int(autoClaim.EndingMiles.ValueOrZero())))
	fpdf.Ln(-1)
	fpdf.SetX(2.2)
	fpdf.SetFont("Helvetica", "B", 8)
	fpdf.Cell(0.75, 0.2, "Dates")
	fpdf.SetFont("Helvetica", "", 8)
	fpdf.Cell(1, 0.2, autoClaim.EffectiveDate.Time.Format(dateFormat)+" to "+autoClaim.ExpirationDate.Time.Format(dateFormat))
	fpdf.Ln(-1)
	fpdf.SetX(2.2)
	fpdf.SetFont("Helvetica", "B", 8)
	fpdf.Cell(0.75, 0.2, "Coverage")
	fpdf.SetFont("Helvetica", "", 8)
	fpdf.Cell(1, 0.2, autoClaim.Coverage)
	fpdf.Ln(-1)
	fpdf.SetX(2.2)
	for i := range autoClaim.CoverageList {

		fpdf.SetX(2.2)
		fpdf.SetFont("Helvetica", "", 6)
		printFlag(autoClaim.CoverageList[i].Value)
		fpdf.SetFont("Helvetica", "B", 8)
		fpdf.Cell(0.75, 0.2, autoClaim.CoverageList[i].Flag)
		fpdf.Ln(-1)
	}

	// 3rd Column
	fpdf.SetXY(4.4, startY)
	fpdf.SetFont("Helvetica", "B", 8)
	fpdf.Cell(0.5, 0.2, "Facility")
	fpdf.SetXY(5, startY)
	fpdf.SetFont("Helvetica", "", 8)
	fpdf.MultiCell(1, 0.2, autoClaim.FacilityName, "", "", false)
	fpdf.Ln(-1)
	fpdf.SetX(4.4)
	fpdf.SetFont("Helvetica", "B", 8)
	fpdf.Cell(0.5, 0.2, "Address")
	fpdf.SetFont("Helvetica", "", 8)
	fpdf.MultiCell(1.4, 0.2, autoClaim.FacilityAddress, "", "", false)
	fpdf.Ln(-1)
	fpdf.SetX(4.4)
	fpdf.Cell(0.5, 0.2, autoClaim.FacilityCity)
	fpdf.Ln(-1)
	fpdf.SetX(4.4)
	fpdf.Cell(0.5, 0.2, autoClaim.FacilityStateCode)
	fpdf.Ln(-1)
	fpdf.SetX(4.4)
	fpdf.Cell(0.5, 0.2, autoClaim.FacilityPostalCode)
	fpdf.Ln(-1)
	fpdf.SetX(4.4)
	fpdf.Cell(0.5, 0.2, autoClaim.FacilityCountry)
	fpdf.Ln(-1)
	fpdf.SetX(4.4)
	fpdf.SetFont("Helvetica", "B", 8)
	fpdf.Cell(0.5, 0.2, "Phone")
	fpdf.SetFont("Helvetica", "", 8)
	fpdf.Cell(1.4, 0.2, autoClaim.FacilityPhone)
	fpdf.Ln(-1)
	fpdf.SetX(4.4)
	fpdf.SetFont("Helvetica", "B", 8)
	fpdf.Cell(0.5, 0.2, "Fax")
	fpdf.SetFont("Helvetica", "", 8)
	fpdf.Cell(1.4, 0.2, autoClaim.FacilityFax)
	fpdf.Ln(-1)
	fpdf.SetX(4.4)
	fpdf.SetFont("Helvetica", "B", 8)
	fpdf.Cell(0.5, 0.2, "Email")

	// 4th Column
	fpdf.SetXY(6.5, startY)
	fpdf.SetFont("Helvetica", "B", 8)
	fpdf.Cell(1, 0.2, "RO")
	fpdf.SetFont("Helvetica", "", 8)
	fpdf.Cell(0.5, 0.2, autoClaim.RO)
	fpdf.Ln(-1)
	fpdf.SetX(6.5)
	fpdf.SetFont("Helvetica", "B", 8)
	fpdf.Cell(1, 0.2, "Opened")
	fpdf.SetFont("Helvetica", "", 8)
	fpdf.Cell(0.5, 0.2, autoClaim.RoOpenedDate.Format(dateFormat))
	fpdf.Ln(-1)
	fpdf.SetX(6.5)
	fpdf.SetFont("Helvetica", "B", 8)
	fpdf.Cell(1, 0.2, "Mileage")
	fpdf.SetFont("Helvetica", "", 8)
	fpdf.Cell(0.5, 0.2, strconv.Itoa(autoClaim.RoMileage))
	fpdf.Ln(-1)
	fpdf.SetX(6.5)
	fpdf.SetFont("Helvetica", "B", 8)
	fpdf.Cell(1, 0.2, "Advisor")
	fpdf.SetFont("Helvetica", "", 8)
	fpdf.Cell(0.75, 0.2, autoClaim.Advisor)
	fpdf.Ln(-1)
	fpdf.SetX(6.5)
	fpdf.SetFont("Helvetica", "B", 8)
	fpdf.Cell(1, 0.2, "Labor Rate")
	fpdf.SetFont("Helvetica", "", 8)
	fpdf.Cell(0.75, 0.2, "$ "+autoClaim.LaborRate.StringFixed(2))
	fpdf.Ln(-1)
	fpdf.SetX(6.5)
	fpdf.SetFont("Helvetica", "B", 8)
	fpdf.Cell(1, 0.2, "Tax Parts")
	fpdf.SetFont("Helvetica", "", 8)
	fpdf.Cell(0.5, 0.2, autoClaim.TaxParts.StringFixed(2)+" %")
	fpdf.Ln(-1)
	fpdf.SetX(6.5)
	fpdf.SetFont("Helvetica", "B", 8)
	fpdf.Cell(1, 0.2, "Tax Labor")
	fpdf.SetFont("Helvetica", "", 8)
	fpdf.Cell(0.5, 0.2, autoClaim.TaxLabor.StringFixed(2)+" %")
	fpdf.Ln(-1)
	fpdf.SetX(6.5)
	fpdf.SetFont("Helvetica", "B", 8)
	fpdf.Cell(1, 0.2, "Pre Auth")
	fpdf.SetFont("Helvetica", "", 8)
	fpdf.Cell(0.5, 0.2, "$ "+autoClaim.PreAuthAmount.StringFixed(2))
	fpdf.Ln(-1)
	fpdf.SetX(6.5)
	fpdf.SetFont("Helvetica", "B", 8)
	fpdf.Cell(1, 0.2, "Pre-Auth#")
	if autoClaim.PreAuthNumber.Valid {
		fpdf.SetFont("Helvetica", "", 8)
		fpdf.Cell(0.5, 0.2, autoClaim.PreAuthNumber.String)
	}
	fpdf.Ln(-1)
	fpdf.SetX(6.5)
	fpdf.SetFont("Helvetica", "B", 8)
	fpdf.Cell(1, 0.2, "Repairing Facility Labor Rate")
	fpdf.Ln(-1)
	fpdf.SetX(6.5)
	fpdf.SetFont("Helvetica", "", 8)
	fpdf.Cell(0.75, 0.2, "$ "+autoClaim.RepairingFacilityLaborRate.StringFixed(2))

	fpdf.Ln(-1)
	fpdf.SetY(5)
	var partsTotal, laborTotal, towingTotal, rentalTotal, subletTotal, miscTotal, goodWillTotal, grandTotal decimal.Decimal

	for i := range autoClaim.Complaints {
		fpdf.SetFont("Helvetica", "B", 8)
		fpdf.CellFormat(0.75, 0.2, "Complaint "+strconv.Itoa(i+1), "", 0, "", true, 0, "")
		fpdf.CellFormat(4.0, 0.2, "", "", 0, "", true, 0, "")
		fpdf.CellFormat(2, 0.2, autoClaim.Complaints[i].Status+" - "+autoClaim.Complaints[i].ComplaintDate.Format(dateFormat), "", 0, "R", true, 0, "")
		fpdf.Ln(-1)
		fpdf.SetFont("Helvetica", "B", 8)
		fpdf.Cell(0.75, 0.2, "Tech ID")
		fpdf.SetFont("Helvetica", "", 8)
		fpdf.Cell(0.5, 0.2, autoClaim.Complaints[i].TechID)
		fpdf.Ln(-1)
		fpdf.SetFont("Helvetica", "B", 8)
		fpdf.Cell(0.75, 0.2, "Complaint")
		fpdf.SetFont("Helvetica", "", 8)
		fpdf.MultiCell(6, 0.2, autoClaim.Complaints[i].Complaint, "", "", false)
		fpdf.Ln(-1)
		fpdf.SetFont("Helvetica", "B", 8)
		fpdf.Cell(0.75, 0.2, "Cause")
		fpdf.SetFont("Helvetica", "", 8)
		fpdf.MultiCell(6, 0.2, autoClaim.Complaints[i].Cause, "", "", false)
		fpdf.Ln(-1)
		fpdf.SetFont("Helvetica", "B", 8)
		fpdf.Cell(0.75, 0.2, "Correction")
		fpdf.SetFont("Helvetica", "", 8)
		fpdf.MultiCell(6, 0.2, autoClaim.Complaints[i].Correction, "", "", false)
		fpdf.Ln(-1)
		fpdf.SetFont("Helvetica", "B", 8)
		fpdf.Cell(0.75, 0.2, "Repair Code")
		fpdf.SetFont("Helvetica", "", 8)
		fpdf.MultiCell(5, 0.2, autoClaim.Complaints[i].RepairCode, "", "", false)

		addLineStr := "False"
		if autoClaim.Complaints[i].AddLineFlag {
			addLineStr = "True"
		}
		fpdf.SetFont("Helvetica", "B", 8)
		fpdf.Cell(0.75, 0.2, "Add Line:")
		fpdf.SetFont("Helvetica", "", 8)
		fpdf.Cell(0.5, 0.2, addLineStr)

		goodWillStr := "False"
		if autoClaim.Complaints[i].GoodwillFlag {
			goodWillStr = "True"
		}
		fpdf.SetFont("Helvetica", "B", 8)
		fpdf.Cell(0.75, 0.2, "Exception:")
		fpdf.SetFont("Helvetica", "", 8)
		fpdf.Cell(0.5, 0.2, goodWillStr)

		if autoClaim.Complaints[i].GoodwillFlag {
			fpdf.Cell(1.5, 0.2, autoClaim.Complaints[i].GoodwillDescription)
			fpdf.Cell(0.5, 0.2, "$ "+autoClaim.Complaints[i].GoodwillAmount.StringFixed(2))
		}

		fpdf.Ln(0.3)

		fpdf.SetFont("Helvetica", "B", 8)
		fpdf.CellFormat(0.75, 0.2, "Part Number", "", 0, "", true, 0, "")
		fpdf.CellFormat(2.75, 0.2, "Description", "", 0, "", true, 0, "")
		fpdf.CellFormat(0.75, 0.2, "Quantity", "", 0, "", true, 0, "")
		fpdf.CellFormat(0.5, 0.2, "Cost", "", 0, "", true, 0, "")
		fpdf.CellFormat(0.5, 0.2, "MSRP", "", 0, "", true, 0, "")
		fpdf.CellFormat(0.75, 0.2, "Requested", "", 0, "", true, 0, "")
		fpdf.CellFormat(0.75, 0.2, "Approved", "", 0, "", true, 0, "")

		fpdf.Ln(-1)
		fpdf.SetFont("Helvetica", "", 8)
		for j := range autoClaim.Complaints[i].Parts {
			fpdf.Cell(0.75, 0.2, autoClaim.Complaints[i].Parts[j].PartNumber)
			fpdf.Cell(2.75, 0.2, autoClaim.Complaints[i].Parts[j].Description)
			fpdf.Cell(0.75, 0.2, strconv.Itoa(autoClaim.Complaints[i].Parts[j].Quantity))
			fpdf.Cell(0.5, 0.2, "$ "+autoClaim.Complaints[i].Parts[j].Cost.StringFixed(2))
			fpdf.Cell(0.5, 0.2, "$ "+autoClaim.Complaints[i].Parts[j].MSRP.StringFixed(2))
			fpdf.Cell(0.75, 0.2, "$ "+autoClaim.Complaints[i].Parts[j].Requested.StringFixed(2))
			fpdf.Cell(0.75, 0.2, "$ "+autoClaim.Complaints[i].Parts[j].Approved.StringFixed(2))
			fpdf.Ln(-1)
			if autoClaim.Complaints[i].Parts[j].Approved.GreaterThan(autoClaim.Complaints[i].Parts[j].Requested) {
				fpdf.SetTextColor(255, 0, 0)
				fpdf.CellFormat(7, 0.2, "Approved amount is greater than requested amount.", "", 0, "R", false, 0, "")
				fpdf.SetTextColor(0, 0, 0)
				fpdf.Ln(-1)
			} else if autoClaim.Complaints[i].Parts[j].Requested.GreaterThan(autoClaim.Complaints[i].Parts[j].Approved) {
				fpdf.Cell(7, 0.2, autoClaim.Complaints[i].Parts[j].Notes)
				fpdf.Ln(-1)
			}

			if autoClaim.Complaints[i].Status == db.AutoClaimComplaintStatusPayable {
				partsTotal = partsTotal.Add(autoClaim.Complaints[i].Parts[j].Approved)
			}
		}
		fpdf.Ln(-1)
		fpdf.SetFont("Helvetica", "B", 8)
		fpdf.CellFormat(3, 0.2, "Labor Description", "", 0, "", true, 0, "")
		fpdf.CellFormat(0.75, 0.2, "Requested", "", 0, "", true, 0, "")
		fpdf.CellFormat(0.75, 0.2, "Hours", "", 0, "", true, 0, "")
		fpdf.CellFormat(0.75, 0.2, "Rate", "", 0, "", true, 0, "")
		fpdf.CellFormat(0.75, 0.2, "Billed", "", 0, "", true, 0, "")
		fpdf.CellFormat(0.75, 0.2, "Approved", "", 0, "", true, 0, "")
		fpdf.Ln(-1)
		fpdf.SetFont("Helvetica", "", 8)
		for k := range autoClaim.Complaints[i].Labor {
			fpdf.Cell(3, 0.2, autoClaim.Complaints[i].Labor[k].LaborDescription)
			fpdf.Cell(0.75, 0.2, autoClaim.Complaints[i].Labor[k].Requested.StringFixed(2))
			fpdf.Cell(0.75, 0.2, autoClaim.Complaints[i].Labor[k].Hours.StringFixed(2))
			fpdf.Cell(0.75, 0.2, "$ "+autoClaim.LaborRate.StringFixed(2))
			fpdf.Cell(0.75, 0.2, "$ "+autoClaim.Complaints[i].Labor[k].Billed.StringFixed(2))
			fpdf.Cell(0.75, 0.2, "$ "+autoClaim.Complaints[i].Labor[k].Approved.StringFixed(2))
			fpdf.Ln(-1)
			if autoClaim.Complaints[i].Labor[k].Approved.GreaterThan(autoClaim.Complaints[i].Labor[k].Billed) {
				fpdf.SetTextColor(255, 0, 0)
				fpdf.CellFormat(7, 0.2, "Approved amount is greater than billed amount.", "", 0, "R", false, 0, "")
				fpdf.SetTextColor(0, 0, 0)
				fpdf.Ln(-1)
			} else if autoClaim.Complaints[i].Labor[k].Billed.GreaterThan(autoClaim.Complaints[i].Labor[k].Approved) {
				fpdf.Cell(7, 0.2, autoClaim.Complaints[i].Labor[k].Notes)
				fpdf.Ln(-1)
			}
			if autoClaim.Complaints[i].Status == db.AutoClaimComplaintStatusPayable {
				laborTotal = laborTotal.Add(autoClaim.Complaints[i].Labor[k].Approved)
			}
		}

		fpdf.Ln(-1)
		fpdf.SetFont("Helvetica", "B", 8)
		fpdf.CellFormat(3.75, 0.2, "Towing Description", "", 0, "", true, 0, "")
		fpdf.CellFormat(1.50, 0.2, "Requested", "", 0, "", true, 0, "")
		fpdf.CellFormat(1.50, 0.2, "Approved", "", 0, "", true, 0, "")
		fpdf.Ln(-1)
		fpdf.SetFont("Helvetica", "", 8)
		for _, towing := range autoClaim.Complaints[i].Towings {
			fpdf.Cell(3.75, 0.2, towing.Description)
			fpdf.Cell(1.50, 0.2, towing.Requested.StringFixed(2))
			fpdf.Cell(1.50, 0.2, "$ "+towing.Approved.StringFixed(2))
			fpdf.Ln(-1)
			if towing.Approved.GreaterThan(towing.Approved) {
				fpdf.SetTextColor(255, 0, 0)
				fpdf.CellFormat(7, 0.2, "Approved amount is greater than requested amount.", "", 0, "R", false, 0, "")
				fpdf.SetTextColor(0, 0, 0)
				fpdf.Ln(-1)
			} else if towing.Requested.GreaterThan(towing.Approved) {
				fpdf.Cell(7, 0.2, towing.Notes)
				fpdf.Ln(-1)
			}
			if autoClaim.Complaints[i].Status == db.AutoClaimComplaintStatusPayable {
				towingTotal = towingTotal.Add(towing.Approved)
			}
		}

		fpdf.Ln(-1)
		fpdf.SetFont("Helvetica", "B", 8)
		fpdf.CellFormat(3.75, 0.2, "Rental Description", "", 0, "", true, 0, "")
		fpdf.CellFormat(1.50, 0.2, "Requested", "", 0, "", true, 0, "")
		fpdf.CellFormat(1.50, 0.2, "Approved", "", 0, "", true, 0, "")
		fpdf.Ln(-1)
		fpdf.SetFont("Helvetica", "", 8)
		for _, rental := range autoClaim.Complaints[i].Rentals {
			fpdf.Cell(3.75, 0.2, rental.Description)
			fpdf.Cell(1.50, 0.2, rental.Requested.StringFixed(2))
			fpdf.Cell(1.50, 0.2, "$ "+rental.Approved.StringFixed(2))
			fpdf.Ln(-1)
			if rental.Approved.GreaterThan(rental.Approved) {
				fpdf.SetTextColor(255, 0, 0)
				fpdf.CellFormat(7, 0.2, "Approved amount is greater than requested amount.", "", 0, "R", false, 0, "")
				fpdf.SetTextColor(0, 0, 0)
				fpdf.Ln(-1)
			} else if rental.Requested.GreaterThan(rental.Approved) {
				fpdf.Cell(7, 0.2, rental.Notes)
				fpdf.Ln(-1)
			}
			if autoClaim.Complaints[i].Status == db.AutoClaimComplaintStatusPayable {
				rentalTotal = rentalTotal.Add(rental.Approved)
			}
		}

		fpdf.Ln(-1)
		fpdf.SetFont("Helvetica", "B", 8)
		fpdf.CellFormat(3.75, 0.2, "Sublet Description", "", 0, "", true, 0, "")
		fpdf.CellFormat(1.50, 0.2, "Requested", "", 0, "", true, 0, "")
		fpdf.CellFormat(1.50, 0.2, "Approved", "", 0, "", true, 0, "")
		fpdf.Ln(-1)
		fpdf.SetFont("Helvetica", "", 8)
		for _, sublet := range autoClaim.Complaints[i].Sublets {
			fpdf.Cell(3.75, 0.2, sublet.Description)
			fpdf.Cell(1.50, 0.2, sublet.Requested.StringFixed(2))
			fpdf.Cell(1.50, 0.2, "$ "+sublet.Approved.StringFixed(2))
			fpdf.Ln(-1)
			if sublet.Approved.GreaterThan(sublet.Approved) {
				fpdf.SetTextColor(255, 0, 0)
				fpdf.CellFormat(7, 0.2, "Approved amount is greater than requested amount.", "", 0, "R", false, 0, "")
				fpdf.SetTextColor(0, 0, 0)
				fpdf.Ln(-1)
			} else if sublet.Requested.GreaterThan(sublet.Approved) {
				fpdf.Cell(7, 0.2, sublet.Notes)
				fpdf.Ln(-1)
			}
			if autoClaim.Complaints[i].Status == db.AutoClaimComplaintStatusPayable {
				subletTotal = subletTotal.Add(sublet.Approved)
			}
		}

		fpdf.Ln(-1)
		fpdf.SetFont("Helvetica", "B", 8)
		fpdf.CellFormat(3.75, 0.2, "Misc Description", "", 0, "", true, 0, "")
		fpdf.CellFormat(1.50, 0.2, "Requested", "", 0, "", true, 0, "")
		fpdf.CellFormat(1.50, 0.2, "Approved", "", 0, "", true, 0, "")
		fpdf.Ln(-1)
		fpdf.SetFont("Helvetica", "", 8)
		for _, misc := range autoClaim.Complaints[i].Miscs {
			fpdf.Cell(3.75, 0.2, misc.Description)
			fpdf.Cell(1.50, 0.2, misc.Requested.StringFixed(2))
			fpdf.Cell(1.50, 0.2, "$ "+misc.Approved.StringFixed(2))
			fpdf.Ln(-1)
			if misc.Approved.GreaterThan(misc.Approved) {
				fpdf.SetTextColor(255, 0, 0)
				fpdf.CellFormat(7, 0.2, "Approved amount is greater than requested amount.", "", 0, "R", false, 0, "")
				fpdf.SetTextColor(0, 0, 0)
				fpdf.Ln(-1)
			} else if misc.Requested.GreaterThan(misc.Approved) {
				fpdf.Cell(7, 0.2, misc.Notes)
				fpdf.Ln(-1)
			}
			if autoClaim.Complaints[i].Status == db.AutoClaimComplaintStatusPayable {
				miscTotal = miscTotal.Add(misc.Approved)
			}
		}

		fpdf.Ln(-1)
		fpdf.SetFont("Helvetica", "B", 8)
		fpdf.Cell(0.5, 0.2, "Parts")
		fpdf.SetFont("Helvetica", "", 8)
		fpdf.Cell(0.5, 0.2, "$ "+autoClaim.Complaints[i].PartsTotal.StringFixed(2))
		fpdf.SetFont("Helvetica", "B", 8)
		fpdf.Cell(0.5, 0.2, "Labor")
		fpdf.SetFont("Helvetica", "", 8)
		fpdf.Cell(0.5, 0.2, "$ "+autoClaim.Complaints[i].LaborTotal.StringFixed(2))
		fpdf.SetFont("Helvetica", "B", 8)
		fpdf.Cell(0.5, 0.2, "Towing")
		fpdf.SetFont("Helvetica", "", 8)
		fpdf.Cell(0.5, 0.2, "$ "+autoClaim.Complaints[i].TowingsTotal.StringFixed(2))
		fpdf.SetFont("Helvetica", "B", 8)
		fpdf.Cell(0.5, 0.2, "Rental")
		fpdf.SetFont("Helvetica", "", 8)
		fpdf.Cell(0.5, 0.2, "$ "+autoClaim.Complaints[i].RentalsTotal.StringFixed(2))
		fpdf.SetFont("Helvetica", "B", 8)
		fpdf.Cell(0.5, 0.2, "Sublet")
		fpdf.SetFont("Helvetica", "", 8)
		fpdf.Cell(0.5, 0.2, "$ "+autoClaim.Complaints[i].SubletsTotal.StringFixed(2))
		fpdf.SetFont("Helvetica", "B", 8)
		fpdf.Cell(0.5, 0.2, "Misc")
		fpdf.SetFont("Helvetica", "", 8)
		fpdf.Cell(0.5, 0.2, "$ "+autoClaim.Complaints[i].MiscsTotal.StringFixed(2))

		complaintTotal := autoClaim.Complaints[i].PartsTotal.Add(autoClaim.Complaints[i].LaborTotal).
			Add(autoClaim.Complaints[i].TowingsTotal).Add(autoClaim.Complaints[i].RentalsTotal).Add(autoClaim.Complaints[i].SubletsTotal)
		fpdf.SetFont("Helvetica", "B", 8)
		fpdf.Cell(0.5, 0.2, "Total")
		fpdf.SetFont("Helvetica", "", 8)
		fpdf.Cell(0.2, 0.2, "$ "+complaintTotal.StringFixed(2))
		fpdf.Ln(-1)
		fpdf.SetFont("Helvetica", "B", 8)
		fpdf.CellFormat(6, 0.2, "Status ", "", 0, "R", false, 0, "")
		fpdf.SetFont("Helvetica", "", 8)
		fpdf.Cell(0.75, 0.2, autoClaim.Complaints[i].Status)
		fpdf.Ln(-1)
		fpdf.SetFont("Helvetica", "B", 8)
		fpdf.CellFormat(6, 0.2, "Reason ", "", 0, "R", false, 0, "")
		fpdf.SetFont("Helvetica", "", 8)
		fpdf.Cell(0.75, 0.2, autoClaim.Complaints[i].StatusReason.String)
		fpdf.Ln(-1)
		fpdf.SetFont("Helvetica", "B", 8)
		fpdf.CellFormat(6, 0.2, "Denial Note ", "", 0, "R", false, 0, "")
		fpdf.SetFont("Helvetica", "", 8)
		fpdf.Cell(0.75, 0.2, autoClaim.Complaints[i].StatusOtherReason.String)
		fpdf.Ln(-1)
		fpdf.Line(0.3, fpdf.GetY(), 7.2, fpdf.GetY())
		fpdf.Ln(0.3)

		if autoClaim.Complaints[i].Status == db.AutoClaimComplaintStatusPayable {
			goodWillTotal = goodWillTotal.Add(autoClaim.Complaints[i].GoodwillAmount)
		}

	}
	grandTotal = partsTotal.Add(laborTotal).Add(towingTotal).Add(rentalTotal).Add(subletTotal).Add(miscTotal).Add(autoClaim.TotalTax).Sub(autoClaim.Deductible)

	payTypes := map[string]string{
		db.PayTypeCreditCard: "Credit Card",
		db.PayTypeCustomer:   "Customer",
		db.PayTypeStore:      "Store",
	}

	fpdf.Ln(-1)

	fpdf.SetFont("Helvetica", "B", 8)
	fpdf.Cell(0.5, 0.2, "Claim Summary")
	summaryY := fpdf.GetY()
	fpdf.Ln(-1)
	fpdf.SetFont("Helvetica", "B", 8)
	fpdf.Cell(0.75, 0.2, "Pay Type")
	fpdf.SetFont("Helvetica", "", 8)
	fpdf.Cell(0.5, 0.2, payTypes[autoClaim.PayType])
	fpdf.Ln(-1)
	if autoClaim.PayType == db.PayTypeCreditCard || autoClaim.PayType == db.PayTypeStore {
		fpdf.Cell(2, 0.2, autoClaim.FacilityName)
		fpdf.Ln(-1)
		fpdf.Cell(2, 0.2, autoClaim.FacilityAddress)
		fpdf.Ln(-1)
		fpdf.Cell(3, 0.2, autoClaim.FacilityCity+", "+autoClaim.FacilityStateCode+" "+autoClaim.FacilityPostalCode+" "+autoClaim.FacilityCountry)
	} else {
		if autoClaim.CustomerPayeeVendorID == "" {
			fpdf.Cell(1, 0.2, autoClaim.CustomerName)
			fpdf.SetFont("Helvetica", "", 8)
			fpdf.Ln(-1)
			fpdf.Cell(1, 0.2, autoClaim.StreetAddress)
			fpdf.Ln(-1)
			fpdf.Cell(2, 0.2, autoClaim.City+", "+autoClaim.State+" "+autoClaim.PostalCode)
			fpdf.Ln(-1)
			fpdf.Cell(2, 0.2, "Vendor #")
		} else {
			// get vendor query payload
			vendorQuery := intacct.VendorQueryPayload{}.VendorID(autoClaim.CustomerPayeeVendorID)
			// get vendors from intacct
			vendors, err := intacct.GetVendors(ctx, vendorQuery)
			if err != nil || len(vendors) == 0 {
				// can't get vendor information, leave the payee information blank
			} else {
				fpdf.Cell(1, 0.2, vendors[0].Name)
				fpdf.Ln(-1)
				fpdf.Cell(1, 0.2, vendors[0].Address1)
				fpdf.Ln(-1)
				fpdf.Cell(1, 0.2, vendors[0].City+", "+vendors[0].State)
				fpdf.Ln(-1)
				fpdf.Cell(1, 0.2, vendors[0].Zip)
				fpdf.Ln(-1)
				fpdf.Cell(1, 0.2, "Vendor # "+autoClaim.CustomerPayeeVendorID)
			}
		}
	}
	fpdf.SetXY(5, summaryY+0.2) // 3rd column
	fpdf.SetFont("Helvetica", "B", 8)
	fpdf.Cell(0.5, 0.2, "Status")
	fpdf.SetFont("Helvetica", "", 8)
	fpdf.Cell(0.7, 0.2, autoClaim.Status)
	fpdf.Ln(-1)
	fpdf.SetX(5)
	fpdf.SetFont("Helvetica", "B", 8)
	fpdf.Cell(0.5, 0.2, "Reason")
	fpdf.SetFont("Helvetica", "", 8)
	fpdf.Cell(0.7, 0.2, autoClaim.CanceledReason)
	fpdf.Ln(-1)
	fpdf.SetX(5)
	fpdf.SetFont("Helvetica", "B", 8)
	fpdf.Cell(0.7, 0.2, "Denial Note")
	fpdf.SetFont("Helvetica", "", 8)
	fpdf.Cell(1.5, 0.2, autoClaim.CanceledOtherReason.String)
	fpdf.Ln(-1)
	statusY := fpdf.GetY()

	fpdf.SetFont("Helvetica", "B", 8)
	fpdf.SetXY(2.5, summaryY+0.2)
	fpdf.Cell(1, 0.2, "Parts")
	fpdf.SetFont("Helvetica", "", 8)
	fpdf.Cell(0.5, 0.2, "$ "+partsTotal.StringFixed(2))
	fpdf.Ln(-1)
	fpdf.SetFont("Helvetica", "B", 8)
	fpdf.SetX(2.5)
	fpdf.Cell(1, 0.2, "Labor")
	fpdf.SetFont("Helvetica", "", 8)
	fpdf.Cell(0.5, 0.2, "$ "+laborTotal.StringFixed(2))
	fpdf.Ln(-1)
	fpdf.SetFont("Helvetica", "B", 8)
	fpdf.SetX(2.5)
	fpdf.Cell(1, 0.2, "Towing")
	fpdf.SetFont("Helvetica", "", 8)
	fpdf.Cell(0.5, 0.2, "$ "+towingTotal.StringFixed(2))
	fpdf.Ln(-1)
	fpdf.SetFont("Helvetica", "B", 8)
	fpdf.SetX(2.5)
	fpdf.Cell(1, 0.2, "Rental")
	fpdf.SetFont("Helvetica", "", 8)
	fpdf.Cell(0.5, 0.2, "$ "+rentalTotal.StringFixed(2))
	fpdf.Ln(-1)
	fpdf.SetFont("Helvetica", "B", 8)
	fpdf.SetX(2.5)
	fpdf.Cell(1, 0.2, "Sublet")
	fpdf.SetFont("Helvetica", "", 8)
	fpdf.Cell(0.5, 0.2, "$ "+subletTotal.StringFixed(2))
	fpdf.Ln(-1)
	fpdf.SetFont("Helvetica", "B", 8)
	fpdf.SetX(2.5)
	fpdf.Cell(1, 0.2, "Misc")
	fpdf.SetFont("Helvetica", "", 8)
	fpdf.Cell(0.5, 0.2, "$ "+miscTotal.StringFixed(2))
	fpdf.Ln(-1)
	fpdf.SetFont("Helvetica", "B", 8)
	fpdf.SetX(2.5)
	fpdf.Cell(1, 0.2, "Tax")
	fpdf.SetFont("Helvetica", "", 8)
	fpdf.Cell(0.5, 0.2, "$ "+autoClaim.TotalTax.StringFixed(2))
	fpdf.Ln(-1)

	partsTax := partsTotal.Mul(autoClaim.TaxParts).Div(decimal.NewFromFloat(100.0))
	laborTax := laborTotal.Mul(autoClaim.TaxLabor).Div(decimal.NewFromFloat(100.0))
	calculatedTotalTax := partsTax.Add(laborTax).Round(2)
	if !autoClaim.TotalTax.Round(2).Equal(calculatedTotalTax) {
		fpdf.SetX(2.5)
		fpdf.SetTextColor(255, 0, 0)
		fpdf.Cell(1, 0.2, "Calculated total tax is $ "+calculatedTotalTax.StringFixed(2))
		fpdf.SetTextColor(0, 0, 0)
		fpdf.Ln(-1)
	}

	fpdf.SetFont("Helvetica", "B", 8)
	fpdf.SetX(2.5)
	fpdf.Cell(1, 0.2, "Exception")
	fpdf.SetFont("Helvetica", "", 8)
	fpdf.Cell(0.5, 0.2, "$ "+goodWillTotal.StringFixed(2))
	fpdf.Ln(-1)
	fpdf.SetFont("Helvetica", "B", 8)
	fpdf.SetX(2.5)
	fpdf.Cell(1, 0.2, "Deductible")
	fpdf.SetFont("Helvetica", "", 8)
	fpdf.Cell(0.5, 0.2, "$ "+autoClaim.Deductible.StringFixed(2))
	fpdf.Ln(-1)
	fpdf.SetFont("Helvetica", "", 8)
	fpdf.SetX(2.5)
	printFlag(autoClaim.DeductibleCollect)
	fpdf.SetFont("Helvetica", "B", 8)
	fpdf.Cell(0.5, 0.2, "Deductible not collected")
	fpdf.Ln(-1)
	fpdf.SetFont("Helvetica", "B", 8)
	fpdf.SetX(2.5)
	fpdf.Cell(1, 0.2, "Requested Total")
	fpdf.SetFont("Helvetica", "", 8)
	fpdf.Cell(0.5, 0.2, "$ "+autoClaim.RequestedTotal.Add(calculatedTotalTax).StringFixed(2))
	fpdf.Ln(-1)
	fpdf.SetFont("Helvetica", "B", 8)
	fpdf.SetX(2.5)
	fpdf.Cell(1, 0.2, "Total Claim Paid")
	fpdf.SetFont("Helvetica", "", 8)
	fpdf.Cell(0.5, 0.2, "$ "+grandTotal.StringFixed(2))
	fpdf.Ln(-1)
	fpdf.SetX(2.5)
	fpdf.SetFont("Helvetica", "B", 8)
	fpdf.Cell(1, 0.2, "Payment Adj")
	fpdf.SetFont("Helvetica", "", 8)
	fpdf.Cell(0.5, 0.2, "$ "+autoClaim.PaymentAdjustment.StringFixed(2))
	fpdf.Ln(-1)
	fpdf.SetX(2.5)
	fpdf.SetFont("Helvetica", "B", 8)
	fpdf.Cell(1, 0.2, "Actual Paid Amt")
	fpdf.SetFont("Helvetica", "", 8)
	fpdf.Cell(0.5, 0.2, "$ "+autoClaim.ActualPaidAmount.StringFixed(2))
	fpdf.Ln(-1)

	for _, pmt := range paymentDetail {
		fpdf.SetXY(5, statusY)
		fpdf.Cell(1, 0.2, "Authorization #")
		fpdf.Cell(1, 0.2, strconv.Itoa(pmt.AuthorizationNumber))
		fpdf.Ln(-1)
		fpdf.SetX(5)
		fpdf.Cell(1, 0.2, "Bill #")
		fpdf.MultiCell(2, 0.2, pmt.BillNumber.String, "", "", false)
		fpdf.Ln(-1)
		fpdf.SetX(5)
		fpdf.Cell(1, 0.2, "Batch #")
		fpdf.Cell(1, 0.2, strconv.Itoa(int(pmt.BatchKey.Int64)))
		fpdf.Ln(-1)
		for _, c := range pmt.CheckDetails {
			fpdf.SetX(5)
			fpdf.Cell(1, 0.2, "Check #")
			fpdf.Cell(1, 0.2, c.CheckNumber)
			fpdf.Ln(-1)
			fpdf.SetX(5)
			fpdf.Cell(1, 0.2, "Amount")
			fpdf.Cell(1, 0.2, c.Amount.StringFixed(2))
			fpdf.Ln(-1)
			fpdf.SetX(5)
			fpdf.Cell(1, 0.2, "Paid Date")
			fpdf.Cell(1, 0.2, c.PaidDate.Format(dateFormat))
		}
	}
	if len(negativeClaims) > 0 {
		fpdf.Ln(-1)
		fpdf.SetX(5)
		fpdf.SetFont("Helvetica", "B", 8)
		fpdf.Cell(1, 0.2, "Negative Claim")
		fpdf.Ln(-1)
		fpdf.SetFont("Helvetica", "", 8)
		for _, pmt := range negativeClaims {
			fpdf.SetX(5)
			fpdf.Cell(1, 0.2, "Authorization #")
			fpdf.Cell(1, 0.2, strconv.Itoa(pmt.AuthorizationNumber))
			fpdf.Ln(-1)
			fpdf.SetX(5)
			fpdf.Cell(1, 0.2, "Bill #")
			fpdf.Cell(1, 0.2, pmt.BillNumber.String)
			fpdf.Ln(-1)
			fpdf.SetX(5)
			fpdf.Cell(1, 0.2, "Batch #")
			fpdf.Cell(1, 0.2, strconv.Itoa(int(pmt.BatchKey.Int64)))
			fpdf.Ln(-1)
			for _, c := range pmt.CheckDetails {
				fpdf.SetX(5)
				fpdf.Cell(1, 0.2, "Check #")
				fpdf.Cell(1, 0.2, c.CheckNumber)
				fpdf.Ln(-1)
				fpdf.SetX(5)
				fpdf.Cell(1, 0.2, "Amount")
				fpdf.Cell(1, 0.2, c.Amount.StringFixed(2))
				fpdf.Ln(-1)
				fpdf.SetX(5)
				fpdf.Cell(1, 0.2, "Paid Date")
				fpdf.Cell(1, 0.2, c.PaidDate.Format(dateFormat))
			}
		}
	}
}

// claimToPdfChargeback generates the pdf for auto claim chargeback claims
func claimToPdfChargeback(fpdf *gofpdf.Fpdf, autoClaim *ClaimPayload, paymentDetail []paymentPayload) {
	const dateFormat = "2006/01/02"

	fpdf.SetFillColor(211, 211, 211)
	fpdf.AddPage()
	fpdf.SetFont("Helvetica", "B", 16)
	fpdf.CellFormat(7, 0.2, autoClaim.ContractNumber, "", 0, "C", false, 0, "")
	fpdf.Ln(0.3)
	fpdf.CellFormat(7, 0.2, autoClaim.Status, "", 0, "C", false, 0, "")
	fpdf.Ln(0.6)

	startY := fpdf.GetY()

	// Facility Column
	fpdf.SetFont("Helvetica", "B", 8)
	fpdf.Cell(0.5, 0.2, "Facility")
	fpdf.SetFont("Helvetica", "", 8)
	fpdf.Cell(3, 0.2, autoClaim.FacilityName)
	fpdf.Ln(0.3)

	fpdf.SetFont("Helvetica", "B", 8)
	fpdf.Cell(0.5, 0.2, "Address")
	fpdf.SetFont("Helvetica", "", 8)
	fpdf.Cell(3, 0.2, autoClaim.FacilityAddress)
	fpdf.Ln(-1)
	fpdf.SetX(0.9)
	fpdf.Cell(0.5, 0.2, autoClaim.FacilityCity)
	fpdf.Ln(-1)
	fpdf.SetX(0.9)
	fpdf.Cell(0.5, 0.2, autoClaim.FacilityStateCode)
	fpdf.Ln(-1)
	fpdf.SetX(0.9)
	fpdf.Cell(0.5, 0.2, autoClaim.FacilityPostalCode)
	fpdf.Ln(-1)
	fpdf.SetX(0.9)
	fpdf.Cell(0.5, 0.2, autoClaim.FacilityCountry)
	fpdf.Ln(0.3)

	fpdf.SetFont("Helvetica", "B", 8)
	fpdf.Cell(0.5, 0.2, "Phone")
	fpdf.SetFont("Helvetica", "", 8)
	fpdf.Cell(1.4, 0.2, autoClaim.FacilityPhone)
	fpdf.Ln(-1)

	fpdf.SetFont("Helvetica", "B", 8)
	fpdf.Cell(0.5, 0.2, "Fax")
	fpdf.SetFont("Helvetica", "", 8)
	fpdf.Cell(1.4, 0.2, autoClaim.FacilityFax)
	fpdf.Ln(-1)

	fpdf.SetFont("Helvetica", "B", 8)
	fpdf.Cell(0.5, 0.2, "Email")

	// 2nd Column
	fpdf.SetXY(4.2, startY)
	fpdf.SetFont("Helvetica", "", 8)
	fpdf.Cell(0.75, 0.2, "Amount")
	fpdf.SetFont("Helvetica", "", 8)
	fpdf.Cell(0.5, 0.2, "$ "+autoClaim.Estimate.StringFixed(2))
	fpdf.Ln(-1)
	fpdf.SetX(4.2)
	fpdf.Cell(0.75, 0.2, "Note")
	fpdf.SetFont("Helvetica", "", 8)
	fpdf.MultiCell(3, 0.2, autoClaim.Notes, "", "", false)
	fpdf.Ln(-1)
	fpdf.SetX(4.2)
	fpdf.Cell(0.75, 0.2, "Status")
	fpdf.SetFont("Helvetica", "", 8)
	fpdf.Cell(0.5, 0.2, autoClaim.Status)
	fpdf.Ln(0.4)

	for _, pmt := range paymentDetail {

		fpdf.Cell(1, 0.2, "Authorization #")
		fpdf.Cell(1, 0.2, strconv.Itoa(pmt.AuthorizationNumber))
		fpdf.Ln(-1)

		fpdf.Cell(1, 0.2, "Bill #")
		fpdf.Cell(1, 0.2, pmt.BillNumber.String)
		fpdf.Ln(-1)

		fpdf.Cell(1, 0.2, "Batch #")
		fpdf.Cell(1, 0.2, strconv.Itoa(pmt.AutomotiveIntacctBatchID))
		fpdf.Ln(-1)

		fpdf.Cell(1, 0.2, "Reason")
		fpdf.Cell(3, 0.2, pmt.BillMemo.String)
		fpdf.Ln(-1)

		for _, c := range pmt.CheckDetails {

			fpdf.Cell(1, 0.2, "Check #")
			fpdf.Cell(1, 0.2, c.CheckNumber)
			fpdf.Ln(-1)

			fpdf.Cell(1, 0.2, "Amount")
			fpdf.Cell(1, 0.2, "$ "+c.Amount.StringFixed(2))
			fpdf.Ln(-1)

			fpdf.Cell(1, 0.2, "Paid Date")
			fpdf.Cell(1, 0.2, c.PaidDate.Format(dateFormat))
		}
	}
}
