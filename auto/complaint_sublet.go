package auto

import (
	"context"
	"database/sql"
	"encoding/json"
	"net/http"
	"strconv"
	"strings"

	"phizz/db"
	"phizz/handlers"

	"github.com/go-chi/chi"
	"github.com/jmoiron/sqlx"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
)

type subletPayload struct {
	ID                         int             `json:"id" db:"id"`
	AutomotiveClaimComplaintID int             `json:"automotive_claim_complaint_id" db:"automotive_claim_complaint_id"`
	Description                string          `json:"description" db:"description"`
	Requested                  decimal.Decimal `json:"requested" db:"requested"`
	Approved                   decimal.Decimal `json:"approved" db:"approved"`
	Notes                      string          `json:"notes" db:"notes"`
}

// SubletCreate creates a new sublet in complaint
func SubletCreate(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	ctx := req.Context()

	complaintIDStr := chi.URLParam(req, "complaint_id")
	complaintID, err := strconv.Atoi(complaintIDStr)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error deleting rental"))
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Number conversion error for complaintID", nil)
	}

	complaintExist, err := complaintExists(complaintID)
	if !complaintExist || err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error getting complaint"))
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Could not get complaint", nil)
	}

	sublet, err := subletFromReq(req)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error malformed sublet data"))
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Malformed Sublet data for create.", nil)
	}
	sublet.AutomotiveClaimComplaintID = complaintID
	cleanSublets(sublet)

	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error creating database transaction"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error beginning transaction for Auto claim update", nil)
	}

	subletID, err := insertSublet(ctx, tx, sublet)
	if err != nil {
		_ = tx.Rollback()
		handlers.ReportError(req, errors.Wrap(err, "error inserting sublet details"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error inserting Sublet", nil)
	}

	err = tx.Commit()
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "database error committing transaction for Auto claim update"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error updating Auto claim", nil)
	}

	return http.StatusOK, map[string]interface{}{"sublet_id": subletID}
}

func insertSublet(ctx context.Context, tx *sqlx.Tx, sublet *subletPayload) (int, error) {
	insertQuery := `insert into automotive_claim_complaint_sublets(automotive_claim_complaint_id, description, requested, approved, notes)
	 values (:automotive_claim_complaint_id, :description, :requested, :approved, :notes) returning id`
	id := 0

	stmt, err := tx.PrepareNamedContext(ctx, insertQuery)
	if err != nil {
		return id, errors.Wrap(err, "an error occurred in PrepareNamed function while adding Context.")
	}
	defer func() { _ = stmt.Close() }()

	err = stmt.GetContext(ctx, &id, sublet)
	if err != nil {
		return id, errors.Wrap(err, "an error occurred while trying to add the Part to the database.")
	}

	return id, err
}

func subletFromReq(req *http.Request) (*subletPayload, error) {
	sublet := subletPayload{}

	dec := json.NewDecoder(req.Body)
	err := dec.Decode(&sublet)

	return &sublet, errors.Wrap(err, "decoding Sublet request failed")
}

func cleanSublets(sublet *subletPayload) {
	sublet.Description = strings.TrimSpace(sublet.Description)
	sublet.Notes = strings.TrimSpace(sublet.Notes)
}

func sublets(ctx context.Context, complaintID int) ([]subletPayload, error) {
	exists, err := complaintExists(complaintID)
	if err != nil {
		return nil, errors.Wrap(err, "error getting complaint from database")
	}
	if !exists {
		return nil, errors.Wrap(err, "complaint does not exist")
	}

	ids := []int{}
	err = db.Get().SelectContext(ctx, &ids, `select id from automotive_claim_complaint_sublets where automotive_claim_complaint_id = $1 order by id asc`, complaintID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, errors.Wrap(err, "error loading sublets list ")
	}

	sublets := make([]subletPayload, len(ids))
	for i, id := range ids {
		sublet, err := subletByID(ctx, id)
		if err != nil {
			return sublets, errors.Wrap(err, "error getting sublets data")
		}
		sublets[i] = *sublet
	}

	return sublets, nil
}

// SubletShow returns detail of sublet for given sublet_id
func SubletShow(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	ctx := req.Context()

	subletIDStr := chi.URLParam(req, "sublet_id")
	subletID, err := strconv.Atoi(subletIDStr)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error converting sublet id"))
		return http.StatusBadRequest, handlers.ErrorMessage(nil, "Number conversion error for subletID", nil)
	}
	sublet, err := subletByID(ctx, subletID)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error getting sublet details"))
		return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Could not get sublet", nil)
	}
	if sublet == nil {
		handlers.ReportError(req, errors.Wrap(err, "error sublet not found"))
		return http.StatusNotFound, handlers.ErrorMessage(err, "Sublet not found", nil)
	}
	return http.StatusOK, map[string]interface{}{"sublet": sublet}
}

func subletByID(ctx context.Context, ID int) (*subletPayload, error) {
	subletFromDB := struct {
		ID                         int                 `json:"id" db:"id"`
		AutomotiveClaimComplaintID int                 `json:"automotive_claim_complaint_id" db:"automotive_claim_complaint_id"`
		Description                sql.NullString      `json:"description" db:"description"`
		Requested                  decimal.NullDecimal `json:"requested" db:"requested"`
		Approved                   decimal.NullDecimal `json:"approved" db:"approved"`
		Notes                      sql.NullString      `json:"notes" db:"notes"`
	}{}

	err := db.Get().Unsafe().Get(&subletFromDB, `select * from automotive_claim_complaint_sublets where id = $1`, ID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, errors.Wrap(err, "error loading sublets")

	}
	sublet := subletPayload{}

	sublet.ID = subletFromDB.ID
	sublet.AutomotiveClaimComplaintID = subletFromDB.AutomotiveClaimComplaintID

	if subletFromDB.Requested.Valid {
		sublet.Requested = subletFromDB.Requested.Decimal
	}
	if subletFromDB.Approved.Valid {
		sublet.Approved = subletFromDB.Approved.Decimal
	}
	if subletFromDB.Notes.Valid {
		sublet.Notes = subletFromDB.Notes.String
	}
	if subletFromDB.Description.Valid {
		sublet.Description = subletFromDB.Description.String
	}
	return &sublet, nil
}

func updateSublet(ctx context.Context, tx *sqlx.Tx, sublet *subletPayload) error {
	query := `update automotive_claim_complaint_sublets
    set description = :description,
    requested = :requested,
    approved = :approved,
    notes = :notes
    where id = :id`

	stmt, err := tx.PrepareNamedContext(ctx, query)
	if err != nil {
		return errors.Wrap(err, "error updating Sublet, PrepareNamed failed")
	}
	defer func() { _ = stmt.Close() }()

	_, err = stmt.ExecContext(ctx, sublet)
	if err != nil {
		return errors.Wrap(err, "error updating sublet, database error")
	}
	return nil
}

// SubletDelete deletes sublet for given part_id
func SubletDelete(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	ctx := req.Context()
	id := 0
	err := db.Get().GetContext(ctx, &id, `select id from automotive_claim_complaint_sublets where id = $1`, chi.URLParam(req, "sublet_id"))
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error loading sublet details from database"))
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage(err, "Sublet not found", nil)
		}
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "error loading sublets", nil)
	}
	_, err = db.Get().ExecContext(ctx, `delete from automotive_claim_complaint_sublets where id = $1`, id)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error deleting sublet details from database"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Could not delete sublet", nil)
	}
	return http.StatusOK, map[string]interface{}{"id": id}
}

func deleteSubletByComplaintID(tx *sqlx.Tx, complaintID int) error {
	_, err := tx.Exec(`delete from automotive_claim_complaint_sublets where automotive_claim_complaint_id = $1`, complaintID)
	if err != nil {
		return errors.Wrap(err, "could not delete sublets")
	}
	return nil
}
