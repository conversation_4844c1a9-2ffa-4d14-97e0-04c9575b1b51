package auto

import (
	"crypto/sha512"
	"encoding/hex"
	"encoding/json"
	"io"
	"log"
	"net/http"
	"strconv"
	"strings"
	"time"

	"phizz/conf"
	"phizz/handlers"
	"phizz/nr"

	"github.com/go-chi/chi"
	"github.com/newrelic/go-agent"
	"gopkg.in/guregu/null.v3"
)

// RepairCodeIndex returns repair code ( concatenation of code, group and description) for matching criteria
func RepairCodeIndex(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	code := chi.URLParam(req, "code")
	if code == "" {
		return http.StatusBadRequest, handlers.ErrorMessage(nil, "Contract code is missing", nil)
	}

	url := conf.Get().Whiz.BaseURL + "/ext/vehicle-components/" + code

	if conf.Get().Whiz.Log {
		log.Println("[Whiz-vehicle-components] request URL:", url)
	}

	whizReq, err := http.NewRequest("GET", url, nil)
	if err != nil {
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "could not create Whiz-vehicle-components request", nil)
	}

	whizReq.Header.Set("Content-Type", "application/json")
	whizReq.Header.Set("Accept", "application/json")
	salt := []byte(conf.Get().Whiz.AuthSalt)
	checksum := sha512.Sum512(salt)
	whizReq.Header.Set("Whiz-Checksum", hex.EncodeToString(checksum[:]))

	txn := w.(newrelic.Transaction)

	client := http.Client{Timeout: time.Second * 30}
	resp, err := nr.External(txn, client, whizReq)
	if err != nil {
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Invalid response for Whiz-vehicle-components", nil)
	}
	defer func() { _ = resp.Body.Close() }()

	bodyBytes := make([]byte, 1024000) // 1000KB buffer for response
	n, err := io.ReadFull(resp.Body, bodyBytes)
	if err != nil && err != io.EOF && err != io.ErrUnexpectedEOF {
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Invalid response for Whiz-vehicle-components", nil)
	}
	bodyBytes = bodyBytes[:n]
	if conf.Get().Whiz.Log {
		log.Println("[Whiz-vehicle-components] response status:", resp.Status, "body:", string(bodyBytes))
	}

	if resp.StatusCode != http.StatusOK {
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Invalid response for Whiz-vehicle-components", nil)
	}

	type repairCode struct {
		ID                int         `json:"id" db:"-"`
		Code              int         `json:"code" db:"-"`
		GroupName         string      `json:"group_name" db:"-"`
		Description       string      `json:"description" db:"-"`
		RepairCode        string      `json:"repair_code" db:"-"`
		RepairCodeLabel   string      `json:"repair_code_label" db:"-"`
		Covered           string      `json:"covered" db:"-" `
		ProductTypeID     int         `json:"product_type_id" db:"-"`
		ProductName       string      `json:"product_name" db:"-"`
		OptionCode        null.String `json:"option_code" db:"_"`
		UpdatedOptionCode null.String `json:"updated_option_code" db:"_"`
	}

	repairCodes := struct {
		Codes []repairCode `json:"vehicle_components"`
	}{}
	err = json.Unmarshal(bodyBytes, &repairCodes)
	if err != nil {
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Invalid response for Whiz-vehicle-components", nil)
	}
	for i, code := range repairCodes.Codes {
		coverage := ""
		if code.Covered == "Y" {
			coverage = "(Yes)"
		} else if code.Covered == "N" {
			coverage = "(No)"
		} else if code.Covered == "C" {
			coverage = "(Call TCA)"
		}

		repairCodes.Codes[i].RepairCode = strings.Join(
			[]string{strconv.Itoa(repairCodes.Codes[i].Code),
				repairCodes.Codes[i].GroupName,
				repairCodes.Codes[i].Description}, " ")

		if coverage != "" {
			repairCodes.Codes[i].RepairCodeLabel = strings.Join(
				[]string{coverage, repairCodes.Codes[i].RepairCode}, "-")
		}
	}

	return http.StatusOK, map[string]interface{}{"repair_codes": repairCodes.Codes}
}
