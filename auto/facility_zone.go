package auto

import (
	"database/sql"
	"encoding/json"
	"net/http"
	"strings"
	"time"

	"phizz/db"
	"phizz/handlers"

	"github.com/go-chi/chi"
	"github.com/jmoiron/sqlx"
	"github.com/pkg/errors"
	"strconv"
)

// ZoneIndex returns a list of stores
func ZoneIndex(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	zones := []struct {
		ID                 int       `json:"id" db:"id"`
		Name               string    `json:"name" db:"name"`
		OwnerID            int       `json:"owner_id" db:"owner_id"`
		OwnerName          string    `json:"owner_name" db:"owner_name"`
		OnHold             bool      `json:"on_hold" db:"on_hold"`
		OnHoldStart        time.Time `json:"on_hold_start" db:"on_hold_start"`
		OnHoldEnd          time.Time `json:"on_hold_end" db:"on_hold_end"`
		TemporaryOwnerID   int       `json:"temporary_owner_id" db:"temporary_owner_id"`
		TemporaryOwnerName string    `json:"temporary_owner_name" db:"temporary_owner_name"`
		Facilities         []string  `json:"facilities" db:"facilities"`
	}{}

	query := `select automotive_facility_zones.id, name, owner_id, first_name || ' ' || last_name as
	owner_name, on_hold from automotive_facility_zones join users on owner_id = users.id order by name asc`
	err := db.Get().Select(&zones, query)
	if err != nil {
		err = errors.Wrap(err, "Database error getting zones")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting zones data", nil)
	}

	facilityQuery := `select facility_code from automotive_facilities where zone_id = $1`
	for i, zone := range zones {
		if zone.OnHold {
			holdQuery := `select temporary_owner_id, users2.first_name || ' ' || users2.last_name as temporary_owner_name,
			users1.time_off_start as on_hold_start, users1.time_off_end as on_hold_end
			from automotive_facility_zones join users as users1 on owner_id = users1.id
			join users as users2 on temporary_owner_id = users2.id
			where automotive_facility_zones.id=$1 order by name asc`

			err = db.Get().Get(&zones[i], holdQuery, zone.ID)
			if err != nil {
				err = errors.Wrap(err, "Database error getting zones in hold info")
				return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting zones data for onHold info", nil)
			}
		}
		err = db.Get().Select(&zones[i].Facilities, facilityQuery, zone.ID)
		if err != nil {
			err = errors.Wrap(err, "Database error getting zones")
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting zones data in facilities", nil)
		}
	}
	return http.StatusOK, map[string]interface{}{"zones": zones, "count": len(zones)}
}

type zonePayload struct {
	ID              int    `json:"id" db:"id"`
	Name            string `json:"name" db:"name"`
	OwnerID         int    `json:"owner_id" db:"owner_id"`
	UpdatedByUserID int    `json:"updated_by_user_id" db:"updated_by_user_id"`
}

// ZoneCreate creates a new zone
func ZoneCreate(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	zone, err := zoneFromReq(req)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(nil, "Malformed zone data for create.", nil)
	}

	zone.UpdatedByUserID = user.ID

	cleanZone(zone)
	formErrors, err := validateZone(zone)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(errors.Wrap(err, "Error validating zone"),
			"An error occurred validating the form values.", nil)
	}

	if len(formErrors) > 0 {
		return http.StatusBadRequest, handlers.ErrorMessage(nil, "Form validations errors.",
			map[string]interface{}{"errors": formErrors})
	}

	id, err := insertZone(zone)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error inserting zone", nil)
	}

	return http.StatusOK, map[string]interface{}{"zone_id": id}
}

func insertZone(zone *zonePayload) (int, error) {
	insertQuery := `insert into automotive_facility_zones (
	name,
	owner_id,
	updated_at,
	updated_by_user_id
	) values (
	:name,
	:owner_id,
	now() at time zone 'utc',
	:updated_by_user_id) returning id`

	id := 0
	stmt, err := db.Get().PrepareNamed(insertQuery)
	if err != nil {
		return id, errors.Wrap(err, "An error occurred in PrepareNamed function while adding zone.")
	}
	defer func() { _ = stmt.Close() }()

	err = stmt.Get(&id, zone)
	if err != nil {
		return id, errors.Wrap(err, "An error occurred while trying to add the zone to the database.")
	}

	return id, err
}

func zoneFromReq(req *http.Request) (*zonePayload, error) {
	zone := zonePayload{}

	dec := json.NewDecoder(req.Body)
	err := dec.Decode(&zone)

	return &zone, errors.Wrap(err, "decoding zone request failed")
}

// cleanZone cleans up leading and trailing white-space
func cleanZone(zone *zonePayload) {
	zone.Name = strings.TrimSpace(zone.Name)
}

// validateZone validates a Zone record for correctness
func validateZone(zone *zonePayload) (map[string]string, error) {
	formErrors := map[string]string{}

	if zone.Name == "" {
		formErrors["name"] = "name is required"
	}
	if zone.OwnerID == 0 {
		formErrors["owner_id"] = "owner_id is required"
	}

	return formErrors, nil
}

// UnassignedAgents returns list of unassigned agents, not assigned to any zone
func UnassignedAgents(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	agents := []struct {
		ID        int    `json:"id" db:"id"`
		AgentName string `json:"agent_name" db:"agent_name"`
	}{}

	query := `select id, first_name || ' ' || last_name as agent_name from users where id not in
	(select owner_id from automotive_facility_zones) and exist(roles, 'auto_claims')`

	err := db.Get().Select(&agents, query)
	if err != nil {
		err = errors.Wrap(err, "Database error getting zones")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting zones data", nil)
	}
	return http.StatusOK, map[string]interface{}{"agents": agents, "count": len(agents)}
}

// ZoneUpdate updates the zone name and zone user/agent
func ZoneUpdate(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	zoneInfoBefore := struct {
		ID      int  `json:"id" db:"id"`
		OwnerID int  `json:"owner_id" db:"owner_id"`
		OnHold  bool `json:"on_hold" db:"on_hold"`
	}{}

	err := db.Get().Get(&zoneInfoBefore, `select id, owner_id, on_hold from automotive_facility_zones where id = $1`, chi.URLParam(req, "id"))
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage(err, "Zone not found", nil)
		}
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error while loading zone", nil)
	}

	zone, err := zoneFromReq(req)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(nil, "Malformed zone data for update.", nil)
	}

	zone.ID = zoneInfoBefore.ID
	zone.UpdatedByUserID = user.ID

	cleanZone(zone)
	formErrors, err := validateZone(zone)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(errors.Wrap(err, "Error validating zone"),
			"An error occurred validating the form values.", nil)
	}

	if len(formErrors) > 0 {
		return http.StatusBadRequest, handlers.ErrorMessage(nil, "Form validations errors.",
			map[string]interface{}{"errors": formErrors})
	}

	if zoneInfoBefore.OnHold && zoneInfoBefore.OwnerID != zone.OwnerID {
		// if the zone is on hold and the owner is changed, unhold the zone and then update the zone, return
		tx, err := db.Get().Beginx()
		if err != nil {
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error updating zone, unhold begin transaction failed", nil)
		}
		err = unholdZone(tx, zoneInfoBefore.ID)
		if err != nil {
			_ = tx.Rollback()
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error updating zone, unhold transaction failed", nil)
		}
		err = updateZone(tx, zone)
		if err != nil {
			_ = tx.Rollback()
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error updating zone, update transaction failed", nil)
		}
		err = tx.Commit()
		if err != nil {
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error updating zone, transaction commit failed", nil)
		}

		return http.StatusOK, map[string]interface{}{"zone_id": zoneInfoBefore.ID}
	}
	// update zone and return
	tx, err := db.Get().Beginx()
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error updating zone, update begin transaction failed", nil)
	}
	err = updateZone(tx, zone)
	if err != nil {
		_ = tx.Rollback()
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error updating zone", nil)
	}
	err = tx.Commit()
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error updating zone, update transaction commit failed", nil)
	}

	return http.StatusOK, map[string]interface{}{"zone_id": zoneInfoBefore.ID}
}

func updateZone(tx *sqlx.Tx, zone *zonePayload) error {
	updateQuery := `update automotive_facility_zones
	set name = :name,
	owner_id = :owner_id,
	updated_at = now() at time zone 'utc',
	updated_by_user_id = :updated_by_user_id
	where id = :id`

	stmt, err := tx.PrepareNamed(updateQuery)
	if err != nil {
		return errors.Wrap(err, "An error occurred in PrepareNamed function while updating zone.")
	}
	defer func() { _ = stmt.Close() }()

	_, err = stmt.Exec(zone)
	if err != nil {
		_ = tx.Rollback()
		return errors.Wrap(err, "An error occurred while trying to update the zone in the database.")
	}

	return err
}

// UpdateZoneFacilities updates the facilities to point to a new zone
func UpdateZoneFacilities(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	id := 0
	if chi.URLParam(req, "id") != "0" { // zone=0 is used to move facilities to zone 0 i.e. zone_id null
		err := db.Get().Get(&id, `select id from automotive_facility_zones where id = $1`, chi.URLParam(req, "id"))
		if err != nil {
			if err == sql.ErrNoRows {
				return http.StatusNotFound, handlers.ErrorMessage(err, "Zone not found", nil)
			}
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error while loading zone", nil)
		}
	}

	facilities := struct {
		Facilities []string `json:"facilities"`
	}{}
	dec := json.NewDecoder(req.Body)
	err := dec.Decode(&facilities)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error while getting facilities", nil)
	}

	err = updateZoneFacilties(facilities.Facilities, id)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error while adding facilities to zone", nil)
	}
	return http.StatusOK, map[string]interface{}{"zone_id": id}

}

func updateZoneFacilties(facilities []string, zoneID int) error {
	if len(facilities) == 0 {
		return nil
	}
	var count int
	query := `select count(*) from automotive_facilities where facility_code in (?)`
	query, args, err := sqlx.In(query, facilities)
	if err != nil {
		err = errors.Wrap(err, `error generating "in" query for validation of UpdateFacilityZone`)
		return err
	}
	query = db.Get().Rebind(query)
	err = db.Get().Get(&count, query, args...)
	if err != nil {
		err = errors.Wrap(err, `error validating facilities IDs in UpdateFacilityZone`)
		return err
	}
	if count != len(facilities) {
		return errors.New("One or more invalid facilities")
	}

	if zoneID == 0 {
		query = `update automotive_facilities set zone_id = null where facility_code in (?)`
		query, args, err = sqlx.In(query, facilities)
	} else {
		query = `update automotive_facilities set zone_id = ? where facility_code in (?)`
		query, args, err = sqlx.In(query, zoneID, facilities)
	}

	if err != nil {
		err = errors.Wrap(err, "error in sqlx.In")
		return errors.Wrap(err, "Error updating facilities")
	}
	query = db.Get().Rebind(query)
	_, err = db.Get().Exec(query, args...)
	if err != nil {
		err = errors.Wrap(err, "error updating zone_id for facilities")
		return errors.Wrap(err, "Error updating facilities")
	}
	return nil
}

// ZoneDelete deletes the zone, marks the facilities unassigned
func ZoneDelete(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	id := 0
	err := db.Get().Get(&id, `select id from automotive_facility_zones where id = $1`, chi.URLParam(req, "id"))
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage(err, "Zone not found", nil)
		}
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error while loading zone", nil)
	}

	err = deleteZone(id)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error deleting zone", nil)
	}

	return http.StatusOK, map[string]interface{}{"zone_id": id}
}

func deleteZone(zoneID int) error {

	tx, err := db.Get().Beginx()
	if err != nil {
		return errors.Wrap(err, "Database error beginning transaction for delete zone")
	}

	query := `update automotive_facilities set zone_id = null where zone_id=$1`
	_, err = tx.Exec(query, zoneID)
	if err != nil {
		_ = tx.Rollback()
		err = errors.Wrap(err, "error updating zone_id for facilities")
		return errors.Wrap(err, "Error in deleteZone")
	}

	deleteZone := `delete from automotive_facility_zones where id = $1`
	_, err = tx.Exec(deleteZone, zoneID)
	if err != nil {
		_ = tx.Rollback()
		err = errors.Wrap(err, "error deleting zone")
		return errors.Wrap(err, "Error in deleteZone")
	}
	err = tx.Commit()
	if err != nil {
		return errors.Wrap(err, "Database error committing transaction for delete zone")
	}
	return nil
}

type holdZonePayload struct {
	ID               int       `json:"id" db:"id"`
	OwnerID          int       `json:"owner_id" db:"owner_id"`
	OnHold           bool      `json:"on_hold" db:"on_hold"`
	TemporaryOwnerID int       `json:"temporary_owner_id" db:"temporary_owner_id"`
	HoldStartDate    time.Time `json:"hold_start_date" db:"hold_start_date"`
	HoldEndDate      time.Time `json:"hold_end_date" db:"hold_end_date"`
}

// ZoneHold holds the zone for given date range
func ZoneHold(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	holdZoneData := holdZonePayload{}
	err := db.Get().Get(&holdZoneData, `select id, owner_id, on_hold from automotive_facility_zones where id = $1`, chi.URLParam(req, "id"))
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage(err, "Zone not found", nil)
		}
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error while loading zone", nil)
	}
	if holdZoneData.OnHold {
		err = errors.New("Invalid request, zone is already on hold")
		return http.StatusBadRequest, handlers.ErrorMessage(err, "The zone is already on hold", nil)
	}

	dec := json.NewDecoder(req.Body)
	err = dec.Decode(&holdZoneData)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error while getting holdzone data", nil)
	}

	err = holdZone(&holdZoneData)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error while updating holdzone data", nil)
	}

	return http.StatusOK, map[string]interface{}{"zone_id": holdZoneData.ID}
}

func holdZone(holdZoneData *holdZonePayload) error {
	tx, err := db.Get().Beginx()
	if err != nil {
		return errors.Wrap(err, "Database error beginning transaction for hold zone")
	}

	query := `update automotive_facility_zones set on_hold = true, temporary_owner_id = $1, updated_at = now() at time zone 'utc' where owner_id = $2`
	_, err = tx.Exec(query, holdZoneData.TemporaryOwnerID, holdZoneData.OwnerID)
	if err != nil {
		_ = tx.Rollback()
		err = errors.Wrap(err, "error updating holdzone information")
		return errors.Wrap(err, "Error in holdZone")
	}
	updateUsers := `update users set time_off_start = $1, time_off_end = $2 where id = $3`
	_, err = tx.Exec(updateUsers, holdZoneData.HoldStartDate, holdZoneData.HoldEndDate, holdZoneData.OwnerID)
	if err != nil {
		_ = tx.Rollback()
		err = errors.Wrap(err, "error hold zone")
		return errors.Wrap(err, "Error in holdZone")
	}
	err = tx.Commit()
	if err != nil {
		return errors.Wrap(err, "Database error committing transaction for hold zone")
	}
	return nil
}

// ZoneUnhold unholds the zone
func ZoneUnhold(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	unholdZoneData := struct {
		ID     int  `json:"id" db:"id"`
		OnHold bool `json:"on_hold" db:"on_hold"`
	}{}
	err := db.Get().Get(&unholdZoneData, `select id, on_hold from automotive_facility_zones where id = $1`, chi.URLParam(req, "id"))
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage(err, "Zone not found", nil)
		}
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error while loading zone", nil)

	}
	if !unholdZoneData.OnHold {
		err = errors.New("Invalid request, zone is not on hold")
		return http.StatusBadRequest, handlers.ErrorMessage(err, "The zone is not on hold", nil)
	}
	tx, err := db.Get().Beginx()
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error beginning transaction for unhold zone", nil)
	}
	err = unholdZone(tx, unholdZoneData.ID)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error in unhold zone", nil)
	}
	err = tx.Commit()
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error committing transaction for unhold zone", nil)
	}

	return http.StatusOK, map[string]interface{}{"zone_id": unholdZoneData.ID}
}

// unholdZone will unhold the zone. In actual, the user is on hold, hence when the zone is unhold, the user is unhold,
// effectively all the zones where this user is owner are unhold
func unholdZone(tx *sqlx.Tx, zoneID int) error {

	userID := 0
	err := tx.Get(&userID, `select owner_id from automotive_facility_zones where id = $1`, zoneID)
	if err != nil {
		_ = tx.Rollback()
		return errors.Wrap(err, "Error in getting zone owner in unholdZone")
	}

	updateFacility := `update automotive_facility_zones set on_hold = false,
	temporary_owner_id = null, updated_at = now() at time zone 'utc'
	where owner_id=$1`
	_, err = tx.Exec(updateFacility, userID)
	if err != nil {
		_ = tx.Rollback()
		err = errors.Wrap(err, "error updating holdzone information")
		return errors.Wrap(err, "Error in unholdZone")
	}

	updateUsers := `update users set time_off_start = null, time_off_end = null where id = $1`
	_, err = tx.Exec(updateUsers, userID)
	if err != nil {
		_ = tx.Rollback()
		err = errors.Wrap(err, "error in unhold zone")
		return errors.Wrap(err, "Error in unholdZone")
	}
	return nil
}

// UnholdZones function to be invoked by scheduled job
func UnholdZones() error {
	users := []struct {
		ID         int       `json:"id" db:"id"`
		TimeOffEnd time.Time `json:"time_off_end" db:"time_off_end"`
	}{}

	getUsers := `select id, time_off_end from users where time_off_end is not null`
	err := db.Get().Select(&users, getUsers)
	if err != nil {
		return errors.Wrap(err, "Database error getting users")
	}

	for _, user := range users {
		if user.TimeOffEnd.Before(time.Now()) {
			zoneID := 0
			err = db.Get().Get(&zoneID, `select id from automotive_facility_zones where owner_id = $1`, user.ID)
			if err != nil {
				if err == sql.ErrNoRows {
					return errors.Wrap(err, "No zones found to be unhold")
				}
				return errors.Wrap(err, "Error while loading zone data")

			}
			tx, err := db.Get().Beginx()
			if err != nil {
				return errors.Wrap(err, "Database error beginning transaction for unhold zone")
			}
			err = unholdZone(tx, zoneID)
			if err != nil {
				return errors.Wrap(err, "Database error in unhold zone")
			}
			err = tx.Commit()
			if err != nil {
				return errors.Wrap(err, "Database error committing transaction for unhold zone")
			}
		}
	}
	return nil
}

// ZoneFacilityIndex returns the list of facilities for given zone id
func ZoneFacilityIndex(_ http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	// Get facilities by zone
	zone := chi.URLParam(req, "id")
	n, err := strconv.Atoi(zone)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error converting zone id. Invalid zone", nil)
	}
	facilities, err := getFacilitiesByZone(n, req.FormValue("search"))
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting facilities by zone", nil)
	}
	return http.StatusOK, map[string]interface{}{"facilities": facilities, "count": len(facilities)}
}
