package auto

import (
	"database/sql"
	"net/http"
	"net/http/httptest"
	"os"
	"reflect"
	"regexp"
	"testing"
	"time"

	"phizz/db"
	"phizz/handlers"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/go-chi/chi"
	"github.com/jmoiron/sqlx"
	"github.com/shopspring/decimal"
	null "gopkg.in/guregu/null.v3"
)

func skipCI(t *testing.T) {
	if os.Getenv("CI") != "" {
		t.Skip("Skipping testing in CI environment")
	}
}

func TestAutoWorklistClaim_ToSlice(t *testing.T) {
	claimDate, _ := time.Parse(time.RFC3339, "2017-02-10T00:00:00+00:00")
	facility := struct {
		String string
		Valid  bool
	}{"UTTCA", true}
	claim := claimListItem{
		ID:                  1,
		CustomerName:        "<PERSON>,<PERSON>",
		ContractNumber:      "SC123456",
		Status:              "Open",
		PreAuthNumber:       null.String{sql.NullString{"PA1", true}},
		RO:                  "12345",
		DateOfClaimReceived: claimDate,
		VIN:                 "3HF34TXY99",
		Facility:            facility,
		Estimate:            decimal.NewFromFloat(3000.5),
		VendorID:            "VEN_00001",
		Adjustments:         decimal.NewFromFloat(100.25),
	}
	expected := []string{"Sutton,Robert", "SC123456", "3HF34TXY99", "Open", "PA1", "12345", "2017-02-10", facility.String, "3000.5", "VEN_00001", "100.25"}
	result := claim.slice()
	if !reflect.DeepEqual(result, expected) {
		t.Error("ToSlice failed to convert object to slice :expected value:", expected)
	}
}

func DISABLEDTestAutoClaimIndexCSV(t *testing.T) {
	mdb, mock, err := sqlmock.New()
	if err != nil {
		t.Fatal(err)
	}
	dbx := sqlx.NewDb(mdb, "sqlmock")
	db.DB = dbx
	q := regexp.QuoteMeta

	mock.ExpectPrepare(q(`select ac.id, last_name || ',' || first_name as customer_name, contract_number, vin, status, ro, date_of_claim_received, facility_code as facility, estimate, acp.amount,reassignment_status, owner_id, reassigned_owner_id, ac.chargeback, ac.parent_claim_id, acpa.pre_auth_number from automotive_claims ac join customers c on ac.customer_id = c.id left join automotive_facilities af on ac.facility_id = af.id left join automotive_claim_payments acp on acp.automotive_claim_id = ac.id left join automotive_claim_pre_auth_numbers acpa on acpa.automotive_claim_id = ac.id order by date_of_claim_received desc`))
	mock.ExpectQuery(q(`select ac.id, last_name || ',' || first_name as customer_name, contract_number, vin, status, ro, date_of_claim_received, facility_code as facility, estimate, acp.amount,reassignment_status, owner_id, reassigned_owner_id, ac.chargeback, ac.parent_claim_id, acpa.pre_auth_number from automotive_claims ac join customers c on ac.customer_id = c.id left join automotive_facilities af on ac.facility_id = af.id left join automotive_claim_payments acp on acp.automotive_claim_id = ac.id left join automotive_claim_pre_auth_numbers acpa on acpa.automotive_claim_id = ac.id order by date_of_claim_received desc`)).
		WillReturnRows(sqlmock.NewRows([]string{"id", "customer_name", "contract_number", "vin", "status", "ro", "date_of_claim_received", "estimate", "reassignment_status"}).
			AddRow(15, "Lopez,Paul", "SC123456", "3H2345FTY3UX", "Open", "12345", time.Time{}, 4004.04, ""))

	mock.ExpectPrepare(q(`select count(distinct ac.id) from automotive_claims ac join customers c on ac.customer_id = c.id left join automotive_facilities af on ac.facility_id = af.id left join automotive_claim_payments acp on acp.automotive_claim_id = ac.id`))
	mock.ExpectQuery(q(`select count(distinct ac.id) from automotive_claims ac join customers c on ac.customer_id = c.id left join automotive_facilities af on ac.facility_id = af.id left join automotive_claim_payments acp on acp.automotive_claim_id = ac.id`)).
		WillReturnRows(sqlmock.NewRows([]string{"count"}).
			AddRow(1))

	req, err := http.NewRequest("GET", "/api/automotive-claims?csv=true", nil)
	if err != nil {
		t.Fatal(err)
	}

	rr := httptest.NewRecorder()
	handler := http.HandlerFunc(handlers.APIHandler(ClaimIndex))

	r := chi.NewRouter()
	r.HandleFunc("/api/automotive-claims", handler)
	r.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v",
			status, http.StatusOK)
	}

	expected := `{"automotive_claims":"Contract Holder,Contract#,VIN,Status,PreAuth#,RO,Opened,Facility,$ Estimate\n\"Lopez,Paul\",SC123456,3H2345FTY3UX,Open,,12345,0001-01-01,,4004.04\n","count":1}`
	if rr.Body.String() != expected {
		t.Errorf("handler returned unexpected body: got %v want %v",
			rr.Body.String(), expected)
	}

	if err := mock.ExpectationsWereMet(); err != nil {
		t.Errorf("there were unfulfilled expectations: %s", err)
	}
}

func TestAutoClaimShow(t *testing.T) {
	// This test includes dependency on the whiz api that is why we are skipping this test on CI environment
	skipCI(t)

	mdb, mock, err := sqlmock.New()
	if err != nil {
		t.Fatal(err)
	}
	dbx := sqlx.NewDb(mdb, "sqlmock")
	db.DB = dbx
	q := regexp.QuoteMeta

	mock.ExpectQuery(q(`select ac.*, first_name || ' ' || last_name as customer_name, email_address, c.phone_number, c.city, c.state, c.postal_code, c.street_address, c.alternate_phone_number, c.best_contact_method from automotive_claims ac join customers c on ac.customer_id = c.id where ac.id = $1 limit 1`)).
		WithArgs("1").
		WillReturnRows(sqlmock.NewRows([]string{"id", "vin", "contract_number", "status", "claim_type", "customer_name", "email_address", "phone_number", "city", "state", "postal_code", "street_address", "alternate_phone_number", "best_contact_method", "originating_dealership", "customer_payee_vendor_id"}).
			AddRow(1, "3FVH2345FT23", "SC12345", "Open", "TCA", "Robert Sutton", "<EMAIL>", "9545251358", "California", "AG", "444101", "ASD", "9545251358", "D", "LHM Avondale", ""))

	mock.ExpectQuery(q(`select automotive_claim_updates.updated_at as last_updated_at, first_name || ' ' || last_name as last_updated_by from automotive_claim_updates join users on updated_by_user_id = users.id where automotive_claim_id = $1 order by last_updated_at desc limit 1`)).
		WithArgs("1").
		WillReturnRows(sqlmock.NewRows([]string{"last_updated_at", "last_updated_by"}).
			AddRow(time.Time{}, "Some name"))

	// Get complaints
	mock.ExpectQuery(q(`select id from automotive_claims where id = $1`)).
		WithArgs(1).
		WillReturnRows(sqlmock.NewRows([]string{"id"}).
			AddRow(1))

	mock.ExpectQuery(q(`select id from automotive_claim_complaints where automotive_claim_id = $1`)).
		WithArgs(1).
		WillReturnRows(sqlmock.NewRows([]string{"id"}).
			AddRow(1))

	mock.ExpectQuery(q(`select * from automotive_claim_complaints where id = $1`)).
		WithArgs(1).
		WillReturnRows(sqlmock.NewRows([]string{"id", "complaint_date", "automotive_claim_id", "complaint", "cause", "correction", "repair_code", "add_line_flag", "goodwill_flag", "goodwill_description", "goodwill_amount", "status", "parts_total", "labor_total", "towing", "rental", "sublet"}).
			AddRow(1, time.Time{}, 1, "complaint", "cause", "correction", "repair_code", false, false, "goodwill_description", 10.25, "Open", 10.0, 12.0, 2.0, 1.0, 0.5))

	// Get Parts
	mock.ExpectQuery(q(`select id from automotive_claim_complaints where id = $1`)).
		WithArgs(1).
		WillReturnRows(sqlmock.NewRows([]string{"id"}).
			AddRow(1))

	mock.ExpectQuery(q(`select id from automotive_claim_complaint_parts where automotive_claim_complaint_id = $1`)).
		WithArgs(1).
		WillReturnRows(sqlmock.NewRows([]string{"id"}).
			AddRow(1))

	mock.ExpectQuery(q(`select * from automotive_claim_complaint_parts where id = $1`)).
		WithArgs(1).
		WillReturnRows(sqlmock.NewRows([]string{"id"}).
			AddRow(1))

	// Get Labor
	mock.ExpectQuery(q(`select id from automotive_claim_complaints where id = $1`)).
		WithArgs(1).
		WillReturnRows(sqlmock.NewRows([]string{"id"}).
			AddRow(1))

	mock.ExpectQuery(q(`select id from automotive_claim_complaint_labors where automotive_claim_complaint_id = $1`)).
		WithArgs(1).
		WillReturnRows(sqlmock.NewRows([]string{"id"}).
			AddRow(1))

	mock.ExpectQuery(q(`select * from automotive_claim_complaint_labors where id = $1`)).
		WithArgs(1).
		WillReturnRows(sqlmock.NewRows([]string{"id"}).
			AddRow(1))

	req, err := http.NewRequest("GET", "/automotive-claims/1", nil)
	if err != nil {
		t.Fatal(err)
	}

	rr := httptest.NewRecorder()
	handler := http.HandlerFunc(handlers.APIHandler(ClaimShow))

	r := chi.NewRouter()
	r.HandleFunc("/automotive-claims/{id:[0-9]+}", handler)
	r.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v",
			status, http.StatusOK)
	}

	expected := `{"auto_claim":{"id":1,"vin":"3FVH2345FT23","contract_number":"SC12345","contract_status":"","reassignment_status":"","date_of_claim_received":"0001-01-01T00:00:00Z","status":"Open","customer_name":"Robert Sutton","customer_id":0,"email_address":"<EMAIL>","alternate_phone_number":"9545251358","street_address":"ASD","phone_number":"9545251358","city":"California","state":"AG","postal_code":"444101","best_contact_method":"D","is_ro_customer_valid":"","is_ro_auto":false,"ro_selected_date":"0001-01-01T00:00:00Z","updated_by_user_id":0,"last_updated_by":"Some name","last_updated_at":"0001-01-01T00:00:00Z","complaints":[{"id":1,"complaint_date":"0001-01-01T00:00:00Z","automotive_claim_id":1,"complaint":"complaint","tech_id":"","cause":"cause","correction":"correction","repair_code":"repair_code","add_line_flag":false,"goodwill_flag":false,"goodwill_description":"goodwill_description","goodwill_amount":"10.25","status":"Open","parts_total":"10","labor_total":"12","towing":"2","rental":"1","sublet":"0.5","parts":[{"id":1,"automotive_claim_complaint_id":0,"part_number":"","description":"","quantity":0,"msrp":"0","cost":"0","requested":"0","approved":"0","notes":""}],"labors":[{"id":1,"automotive_claim_complaint_id":0,"labor_description":"","requested":"0","hours":"0","rate":"0","billed":"0","approved":"0","notes":""}],"is_manual":false,"line_code":"","_":0}],"model":"","make":"","year":"","beginning_miles":0,"ending_miles":0,"effective_date":"0001-01-01T00:00:00Z","expiration_date":"0001-01-01T00:00:00Z","coverage":"","coverage_list":null,"deductible":"0","maintenance":"","ro":"","ro_opened_date":"0001-01-01T00:00:00Z","ro_mileage":0,"facility_code":"","facility_id":0,"facility_name":"","facility_address":"","facility_postal_code":"","facility_city":"","facility_state_code":"","facility_country":"","facility_phone":"","facility_fax":"","labor_rate":"0","tax_labor":"0","tax_parts":"0","advisor":"","term":"","product_code":"","estimate":"0","pay_type":"","tax_adjustment":"0","owner_id":0,"auto_approved":false,"pre_auth_amount":"0","is_auto_save":false,"total_tax":"0","requested_total":"0","canceled_reason":"","claim_type":"TCA","sb_record_key":"","contract_store_id":0,"originating_dealership":"LHM Avondale","customer_payee_vendor_id":""}}`
	if rr.Body.String() != expected {
		t.Errorf("handler returned unexpected body: got %v want %v",
			rr.Body.String(), expected)
	}

	if err := mock.ExpectationsWereMet(); err != nil {
		t.Errorf("there were unfulfilled expectations: %s", err)
	}
}
