package auto

import (
	"context"
	"database/sql"
	"encoding/json"
	"net/http"
	"strconv"
	"strings"

	"phizz/db"
	"phizz/handlers"

	"github.com/go-chi/chi"
	"github.com/jmoiron/sqlx"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
)

type towingPayload struct {
	ID                         int             `json:"id" db:"id"`
	AutomotiveClaimComplaintID int             `json:"automotive_claim_complaint_id" db:"automotive_claim_complaint_id"`
	Description                string          `json:"description" db:"description"`
	Requested                  decimal.Decimal `json:"requested" db:"requested"`
	Approved                   decimal.Decimal `json:"approved" db:"approved"`
	Notes                      string          `json:"notes" db:"notes"`
}

// TowingCreate creates a new towing in complaint
func TowingCreate(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	ctx := req.Context()

	complaintIDStr := chi.URLParam(req, "complaint_id")
	complaintID, err := strconv.Atoi(complaintIDStr)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error converting towing id"))
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Number conversion error for complaintID", nil)
	}

	complaintExist, err := complaintExists(complaintID)
	if !complaintExist || err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error getting complaint id"))
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Could not get complaint", nil)
	}

	towing, err := towingFromReq(req)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error malformed towing data"))
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Malformed Towing data for create.", nil)
	}
	towing.AutomotiveClaimComplaintID = complaintID
	cleanTowing(towing)

	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error creating database transaction"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error beginning transaction for Auto claim update", nil)
	}

	towingID, err := insertTowing(ctx, tx, towing)
	if err != nil {
		_ = tx.Rollback()
		handlers.ReportError(req, errors.Wrap(err, "error inserting towing details"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error inserting Towing", nil)
	}

	err = tx.Commit()
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "database error committing transaction for Auto claim update"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error updating Auto claim", nil)
	}

	return http.StatusOK, map[string]interface{}{"towing_id": towingID}
}

func insertTowing(ctx context.Context, tx *sqlx.Tx, towing *towingPayload) (int, error) {
	insertQuery := `insert into automotive_claim_complaint_towings(automotive_claim_complaint_id, description, requested, approved, notes)
	 values (:automotive_claim_complaint_id, :description, :requested, :approved, :notes) returning id`
	id := 0

	stmt, err := tx.PrepareNamedContext(ctx, insertQuery)
	if err != nil {
		return id, errors.Wrap(err, "an error occurred in PrepareNamed function while adding Context.")
	}
	defer func() { _ = stmt.Close() }()

	err = stmt.GetContext(ctx, &id, towing)
	if err != nil {
		return id, errors.Wrap(err, "an error occurred while trying to add the Part to the database.")
	}

	return id, err
}

func towingFromReq(req *http.Request) (*towingPayload, error) {
	towing := towingPayload{}

	dec := json.NewDecoder(req.Body)
	err := dec.Decode(&towing)

	return &towing, errors.Wrap(err, "decoding Towing request failed")
}

func cleanTowing(towing *towingPayload) {
	towing.Description = strings.TrimSpace(towing.Description)
	towing.Notes = strings.TrimSpace(towing.Notes)
}

func towings(ctx context.Context, complaintID int) ([]towingPayload, error) {
	exists, err := complaintExists(complaintID)
	if err != nil {
		return nil, errors.Wrap(err, "error getting complaint from database")
	}
	if !exists {
		return nil, errors.Wrap(err, "complaint does not exist")
	}

	ids := []int{}
	err = db.Get().SelectContext(ctx, &ids, `select id from automotive_claim_complaint_towings where automotive_claim_complaint_id = $1 order by id asc`, complaintID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, errors.Wrap(err, "error loading towings list ")
	}

	towings := make([]towingPayload, len(ids))
	for i, id := range ids {
		towing, err := towingByID(ctx, id)
		if err != nil {
			return towings, errors.Wrap(err, "error getting towings data")
		}
		towings[i] = *towing
	}

	return towings, nil
}

// TowingShow returns detail of towing for given towing_id
func TowingShow(w http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	ctx := req.Context()

	towingIDStr := chi.URLParam(req, "towing_id")
	towingID, err := strconv.Atoi(towingIDStr)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error converting towing id"))
		return http.StatusBadRequest, handlers.ErrorMessage(nil, "Number conversion error for towingID", nil)
	}
	towing, err := towingByID(ctx, towingID)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error getting towin details"))
		return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Could not get towing", nil)
	}
	if towing == nil {
		return http.StatusNotFound, handlers.ErrorMessage(err, "Towing not found", nil)
	}
	return http.StatusOK, map[string]interface{}{"towing": towing}
}

func towingByID(ctx context.Context, ID int) (*towingPayload, error) {
	towingFromDB := struct {
		ID                         int                 `json:"id" db:"id"`
		AutomotiveClaimComplaintID int                 `json:"automotive_claim_complaint_id" db:"automotive_claim_complaint_id"`
		Description                sql.NullString      `json:"description" db:"description"`
		Requested                  decimal.NullDecimal `json:"requested" db:"requested"`
		Approved                   decimal.NullDecimal `json:"approved" db:"approved"`
		Notes                      sql.NullString      `json:"notes" db:"notes"`
	}{}

	err := db.Get().Unsafe().Get(&towingFromDB, `select * from automotive_claim_complaint_towings where id = $1`, ID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, errors.Wrap(err, "error loading towings")

	}
	towing := towingPayload{}

	towing.ID = towingFromDB.ID
	towing.AutomotiveClaimComplaintID = towingFromDB.AutomotiveClaimComplaintID

	if towingFromDB.Requested.Valid {
		towing.Requested = towingFromDB.Requested.Decimal
	}
	if towingFromDB.Approved.Valid {
		towing.Approved = towingFromDB.Approved.Decimal
	}
	if towingFromDB.Notes.Valid {
		towing.Notes = towingFromDB.Notes.String
	}
	if towingFromDB.Description.Valid {
		towing.Description = towingFromDB.Description.String
	}
	return &towing, nil
}

func updateTowing(ctx context.Context, tx *sqlx.Tx, towing *towingPayload) error {

	query := `update automotive_claim_complaint_towings
    set description = :description,
    requested = :requested,
    approved = :approved,
    notes = :notes
    where id = :id`

	stmt, err := tx.PrepareNamedContext(ctx, query)
	if err != nil {
		return errors.Wrap(err, "error updating Towing, PrepareNamed failed")
	}
	defer func() { _ = stmt.Close() }()

	_, err = stmt.ExecContext(ctx, towing)
	if err != nil {
		return errors.Wrap(err, "error updating towing, database error")
	}
	return nil
}

// TowingDelete deletes towing for given part_id
func TowingDelete(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	ctx := req.Context()
	id := 0
	err := db.Get().GetContext(ctx, &id, `select id from automotive_claim_complaint_towings where id = $1`, chi.URLParam(req, "towing_id"))
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "database error getting towing details for deleting"))
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage(err, "Towing not found", nil)
		}
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error loading towings", nil)
	}
	_, err = db.Get().ExecContext(ctx, `delete from automotive_claim_complaint_towings where id = $1`, id)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "database error deleting towings details"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Could not delete towing", nil)
	}
	return http.StatusOK, map[string]interface{}{"id": id}
}

func deleteTowingByComplaintID(tx *sqlx.Tx, complaintID int) error {
	_, err := tx.Exec(`delete from automotive_claim_complaint_towings where automotive_claim_complaint_id = $1`, complaintID)
	if err != nil {
		return errors.Wrap(err, "could not delete towings")
	}
	return nil
}
