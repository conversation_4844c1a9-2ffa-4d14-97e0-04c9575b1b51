package dms

import (
	"time"

	"github.com/shopspring/decimal"
)

const (
	dateFormat = "2006-01-02"
)

// JSONDate embeds time.Time for and implements MarshalJSON to format
type JSONDate struct {
	time.Time
}

// MarshalJSON implements the ability to format the embedded time.Time for JSON
func (d *JSONDate) MarshalJSON() ([]byte, error) {
	return []byte(`"` + d.Time.Format(dateFormat) + `"`), nil
}

const (
	// PaymentTypeCash is the string representation of a cash payment type
	PaymentTypeCash = "Cash"
	// PaymentTypeLoan is the string representation of a loan payment type
	PaymentTypeLoan = "Loan"
	// PaymentTypeLease is the string representation of a lease payment type
	PaymentTypeLease = "Lease"
	// VehicleConditionNew is the string representation of a new vehicle condition
	VehicleConditionNew = "New"
	// VehicleConditionUsed is the string representation of a used vehicle condition
	VehicleConditionUsed = "Used"

	// SaleTypeFinanceDeal is the string representation for a vehicle sale
	SaleTypeFinanceDeal = "Finance Deal"
	// SaleTypeServiceRO is the string representation for a vehicle service
	SaleTypeServiceRO = "Service RO"
)

var (
	// PaymentTypes is a slice of acceptable payment types
	PaymentTypes = []string{PaymentTypeCash, PaymentTypeLease, PaymentTypeLoan}
	// VehicleConditions is a slice of acceptable vehicle conditions
	VehicleConditions = []string{VehicleConditionNew, VehicleConditionUsed}
	// SaleTypes is a slice of acceptable sale types
	SaleTypes = []string{SaleTypeFinanceDeal, SaleTypeServiceRO}
)

// Deal is the common structure of a deal after fetching from a DMS API.
type Deal struct {
	PaymentType string       `json:"payment_type"`
	Date        JSONDate     `json:"date"`
	Vehicle     DealVehicle  `json:"vehicle"`
	Customer    Customer     `json:"customer"`
	FinanceInfo *FinanceInfo `json:"finance_info"`
	Prices      []Price      `json:"prices"`
	CoCustomer  *Customer    `json:"cocustomer,omitempty"`
}

// DealVehicle is the common structure of a deal's vehicle after fetching from a DMS API.
type DealVehicle struct {
	DMSStockNumber string          `json:"dms_stock_number"`
	VIN            string          `json:"vin"`
	Odometer       int             `json:"odometer"`
	Condition      string          `json:"condition"`
	MSRP           decimal.Decimal `json:"msrp"`
	Price          decimal.Decimal `json:"price"`
}

// Customer is the common structure of a deal or RO's customer after fetching from a DMS API.
type Customer struct {
	DMSCustomerNumber string `json:"dms_customer_number"`
	IsBusiness        bool   `json:"is_business"`
	BusinessName      string `json:"business_name"`
	FirstName         string `json:"first_name"`
	LastName          string `json:"last_name"`
	Address           string `json:"address"`
	City              string `json:"city"`
	State             string `json:"state"`
	PostalCode        string `json:"postal_code"`
	Phone             string `json:"phone"`
	Cellular          string `json:"cellular"`
	Email             string `json:"email"`
}

// FinanceInfo is the common structure of a deal's finance info after fetching from a DMS API.
type FinanceInfo struct {
	Term             int             `json:"term"`
	APR              decimal.Decimal `json:"apr"`
	Amount           decimal.Decimal `json:"amount"`
	MonthlyPayment   decimal.Decimal `json:"monthly_payment"`
	FirstPaymentDate JSONDate        `json:"first_payment_date"`
}

// Price is the common structure of each deal's product type after fetching from a DMS API.
type Price struct {
	ProductTypeCode string          `json:"product_type_code"`
	Price           decimal.Decimal `json:"price"`
}

// RO is the common structure of an RO after fetching from a DMS API.
type RO struct {
	Date     JSONDate  `json:"date"`
	Vehicle  ROVehicle `json:"vehicle"`
	Customer Customer  `json:"customer"`
}

// ROVehicle is the common structure of an RO's vehicle after fetching from a DMS API.
type ROVehicle struct {
	VIN      string `json:"vin"`
	Odometer int    `json:"odometer"`
}

// ROLabor to store the labor details of RO
type ROLabor struct {
	SoldHours decimal.Decimal `json:"sold_hours"`
	LaborSale decimal.Decimal `json:"labor_sale"`
	MiscSale  decimal.Decimal `json:"misc_sale"`
	LaborType string          `json:"labor_type"`
}

// ROPart to store parts details of RO
type ROPart struct {
	Cost        decimal.Decimal `json:"cost"`
	List        decimal.Decimal `json:"list"`
	Quantity    int             `json:"quantity"`
	Description string          `json:"description"`
	PartNumber  string          `json:"part_number"`
	PartsSale   decimal.Decimal `json:"parts_sale"`
}

// ROLine to store one line ( A, B, C etc. ) of an RO
type ROLine struct {
	LineCode             string    `json:"line_code"`
	OpCode               string    `json:"op_code"`
	ComplaintCode        string    `json:"complaint_code"`
	ComplaintDescription string    `json:"complaint_description"`
	TechID               string    `json:"tech_id"`
	Cause                string    `json:"cause"`
	Correction           string    `json:"correction"`
	AddOnFlag            string    `json:"add_on_flag"`
	LaborTypes           []string  `json:"labor_type"`
	Labors               []ROLabor `json:"labors"`
	Parts                []ROPart  `json:"parts"`
}

// RODetail is to store RO details as Parts and Labors after fetching from a DMS API.
type RODetail struct {
	Date           time.Time `json:"date"`
	Vehicle        ROVehicle `json:"vehicle"`
	ROLines        []*ROLine `json:"ro_lines"`
	Customer       Customer  `json:"customer"`
	ServiceAdvisor string    `json:"service_advisor"`
}
