// const exec = require("child_process").exec;
const webpack = require('webpack');
const autoprefixer = require('autoprefixer');
const MiniCssExtractPlugin = require("mini-css-extract-plugin");

const ManifestBuildPlugin = function (options) { };

//process.traceDeprecation = true; // to trace deprecations

ManifestBuildPlugin.prototype.apply = function (compiler) {
  compiler.hooks.done.tap("npm run manifest", () => {
    console.log("After build: `gulp manifest`");
    // exec("npm run manifest");
  });
};

module.exports = {
  entry: {
    vendor: [
      "./node_modules/bootstrap/dist/css/bootstrap.css",
      "./node_modules/@fortawesome/fontawesome-free/css/all.css",
      "./node_modules/@fortawesome/fontawesome-free/css/v4-shims.css",
      "./node_modules/react-datepicker/dist/react-datepicker.css",
      "./node_modules/react-select/dist/react-select.css",
      "./node_modules/react-s-alert/dist/s-alert-default.css",
      "./node_modules/react-s-alert/dist/s-alert-css-effects/slide.css"
    ],
    app: [
      './assets/js/app.jsx',
      './assets/sass/app.scss'
    ]
  },
  output: {
    path: __dirname + '/public/',
    filename: 'js/[name].js',
    sourceMapFilename: '[file].map'
  },
  devtool: 'source-map',
  module: {
    rules: [
      {
        test: /\.(ttf|eot|svg|woff)(\?v=[0-9]\.[0-9]\.[0-9])?$/,
        use: [
          {
            loader: 'url-loader',
            options: { limit: 100000 },
          },
        ],
      },
      {
        test: /\.s?css$/,
        use: [
          MiniCssExtractPlugin.loader,
          { loader: 'css-loader', options: { sourceMap: true } },
          { loader: 'sass-loader', options: { sourceMap: true } },
        ]
      },
      {
        test: /\.jsx?$/,
        loader: 'babel-loader',
        exclude: /(node_modules|bower_components)/,
        options: {
          presets: [
            ["@babel/preset-env", {
              modules: false,
              //  debug: true,
            }
            ],
            ['@babel/react', {
              development: process.env.NODE_ENV === 'development'
            }
            ]
          ]
        }
      },
    ]
  },
  optimization: {
    runtimeChunk: false,
  },
  plugins: [
    new webpack.DefinePlugin({
      'process.env': {
        'NODE_ENV': JSON.stringify(process.env.NODE_ENV)
      }
    }),
    new webpack.ProvidePlugin({
      $: 'jquery',
      jQuery: 'jquery',
      'window.jQuery': 'jquery',
      Popper: ['popper.js', 'default'],
      Util: "exports-loader?Util!bootstrap/js/dist/util",
      Dropdown: 'exports-loader?Dropdown!bootstrap/js/dist/dropdown',
      Tooltip: "exports-loader?Tooltip!bootstrap/js/dist/tooltip"
    }),
    new ManifestBuildPlugin(),
    new MiniCssExtractPlugin({
      filename: 'css/[name].css'
    }),
    new webpack.LoaderOptionsPlugin({
      options: {
        postcss: [
          autoprefixer()
        ]
      }
    })
  ],
  devServer: {
    static: __dirname + "/public",
    headers: {
      "Access-Control-Allow-Origin": "*",
    }
  },
  resolve: {
    modules: ['node_modules', 'assets/js'],
    extensions: ['.js', '.jsx'],
  }
};