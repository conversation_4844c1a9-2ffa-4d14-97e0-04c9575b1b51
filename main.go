package main

import (
	"context"
	"encoding/base64"
	"flag"
	"fmt"
	"log"
	"net/http"
	_ "net/http/pprof"
	"os"
	"time"

	"phizz/conf"
	"phizz/handlers"
	phizzMiddleware "phizz/middleware"
	"phizz/routes"
	"phizz/session"
	"phizz/tasks"

	"github.com/go-chi/chi"
	"github.com/go-chi/chi/middleware"
	"github.com/stvp/rollbar"
	_ "golang.org/x/lint" // golint from this package is used from the cli, not from code. It needs to be imported from any go file to ensure that `go tidy` doesn't remove this dependency from go.mod.
	"gopkg.in/tylerb/graceful.v1"
)

func main() {
	ctx := context.Background()
	log.SetOutput(os.Stdout) // this is for when the app is run under realize

	config := conf.Get()

	// Check for alternate tasks which don't involve launching the service
	var jobName string
	tasks.CreateJobFlag(&jobName)
	flag.Parse()
	if jobName != "" {
		tasks.Run(ctx, jobName)
		return
	}

	rollbar.Environment = config.AppEnv
	rollbar.Token = config.RollbarToken

	cookieEncKey, err := base64.StdEncoding.DecodeString(config.CookieStoreEncKey)
	if err != nil {
		log.Println("Error decoding cookie store enc key")
		panic(err)
	}

	r := chi.NewRouter()
	r.Use(middleware.RequestID)
	r.Use(middleware.RealIP)
	r.Use(session.Middleware(
		"_phizzsession",
		cookieEncKey,
	))
	r.Use(middleware.Logger)    // TODO: Need custom logger?
	r.Use(middleware.Recoverer) // TODO: Rollbar recovery
	r.Use(middleware.DefaultCompress)
	r.Use(phizzMiddleware.SecureHeaders)

	routes.APIHandlers(r)
	routes.DownloadHandlers(r)
	routes.SessionHandlers(r)
	routes.StaticHandlers(r)
	routes.ExtHandlers(r)
	routes.TemplateHandlers(r) // Last because of route for `/*`

	//NOTE: print routes
	//var printRoutes func(routes chi.Routes, indent string)
	//printRoutes = func(routes chi.Routes, indent string) {
	//	for _, route := range routes.Routes() {
	//		fmt.Printf("%s%s\n", indent, route.Pattern)
	//		if route.SubRoutes != nil {
	//			printRoutes(route.SubRoutes, indent+"  ")
	//		}
	//	}
	//}
	//printRoutes(r, "")

	// Pprof server.
	go func() {
		port := 3030
		log.Printf("Starting Pprof server on port %d", port)
		log.Fatal(http.ListenAndServe(fmt.Sprintf("localhost:%d", port), nil))
	}()

	if config.HTTPSRedirector {
		go (func() {
			s := &http.Server{
				Addr:           ":8081",
				Handler:        http.HandlerFunc(handlers.HTTPSRedirector),
				ReadTimeout:    1 * time.Second,
				WriteTimeout:   1 * time.Second,
				MaxHeaderBytes: 1 << 20,
			}
			log.Fatal(s.ListenAndServe())
		})()
	}

	port := os.Getenv("PORT")
	if port == "" {
		port = "3000"
	}
	serverStr := ":" + port
	server := &http.Server{
		Addr:              serverStr,
		Handler:           r,
		ReadHeaderTimeout: 10 * time.Second,
		ReadTimeout:       120 * time.Second,
		WriteTimeout:      120 * time.Second,
		IdleTimeout:       10 * time.Second,
	}
	if config.AppEnv == "development" {
		log.Printf("Listening on '%s'...\n", serverStr)
		log.Fatal(server.ListenAndServe())
	} else {
		log.Printf("Listening on '%s' with graceful shutdown...\n", serverStr)
		log.Fatal(graceful.ListenAndServe(server, 2*time.Second))
	}
}
