package intacct

import (
	"bytes"
	"context"
	"encoding/xml"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"phizz/conf"
	"phizz/db"
	"phizz/util"

	"github.com/jmoiron/sqlx"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
)

const (
	// PaymentStateComplete intacct payment state complete
	PaymentStateComplete = "Complete"
	// ResultFailure intacct result failure
	ResultFailure = "failure"
	// ResultSuccess intacct result success
	ResultSuccess = "success"
	// BillPaymentStateComplete bill payment state complete
	BillPaymentStateComplete = "C"
	// IntacctReversedClaimErrorCode for already reversed claim in Intacct
	IntacctReversedClaimErrorCode = "BL03000201"
)

// Bill payload for intacct bill
type Bill struct {
	AccountLabel string
	Amount       decimal.Decimal
	Memo         string
	LocationID   int
	Key          string
	TotalPaid    decimal.Decimal
	TotalDue     decimal.Decimal
	ProjectID    string
	VendorID     string
	TermName     string
	BatchKey     int
	BillNo       string
	LoanNumber   string
	Description  string
	CustomerID   string
}

// ErrorIntacct is the custom error from Intacct
type ErrorIntacct struct {
	ErrorMessage string
	Correction   string
}

func (e *ErrorIntacct) Error() string {
	return fmt.Sprintf("Error: %s, Correction: %s", e.ErrorMessage, e.Correction)
}

// BillResponse payload
type BillResponse struct {
	BillKey int
	Amount  decimal.Decimal
}

// APPayment payload
type APPayment struct {
	Bills       []BillResponse
	VendorID    string
	Description string
}

// Control section establishes API credentials in order to allow access to the web services API gateway
type controlXML struct {
	XMLName    xml.Name `xml:"control"`
	SenderID   string   `xml:"senderid"`
	Password   string   `xml:"password"`
	ControlID  string   `xml:"controlid"`
	UniqueID   bool     `xml:"uniqueid"`
	DTDVersion string   `xml:"dtdversion"`
}

func getControlXML(controlID string) *controlXML {
	control := controlXML{
		SenderID:   conf.Get().Intacct.SenderID,
		Password:   conf.Get().Intacct.SenderPassword,
		ControlID:  controlID,
		DTDVersion: "3.0",
	}
	return &control
}

type authenticationXML struct {
	XMLName   xml.Name `xml:"authentication"`
	UserID    string   `xml:"login>userid"`
	CompanyID string   `xml:"login>companyid"`
	Password  string   `xml:"login>password"`
}

func getAuthenticationXML() *authenticationXML {
	authentication := authenticationXML{
		UserID:    conf.Get().Intacct.UserID,
		CompanyID: conf.Get().Intacct.CompanyID,
		Password:  conf.Get().Intacct.Password,
	}
	return &authentication
}

// Request function sends request XML to intacct webservice
func Request(ctx context.Context, requestXML []byte) ([]byte, error) {
	body := url.Values{}
	body.Set("xmlrequest", string(requestXML))
	resp, err := http.Post(
		conf.Get().Intacct.Host,
		"application/x-www-form-urlencoded",
		bytes.NewBufferString(body.Encode()))

	if err != nil {
		return nil, errors.Wrap(err, "Intacct server communication error")
	}

	defer resp.Body.Close()

	response, _ := ioutil.ReadAll(resp.Body)
	logRequestResponse(ctx, string(requestXML), string(response))
	return response, nil
}

type request struct {
	XMLName        xml.Name           `xml:"request"`
	Control        *controlXML        `xml:"control"`
	Authentication *authenticationXML `xml:"operation>authentication"`
	Function       interface{}        `xml:"operation>content>function"`
}

// ErrPaymentNotComplete intacct error payment not complete
var ErrPaymentNotComplete = errors.New("inacct: payment is not complete")

// CreateBillBatchRequest function generates XML for create_billBatch api
// This is sample xml
// <function controlid="create_billbatch">
//
//	<create_billbatch>
//	  <batchtitle>MM/DD/YYYY</batchtitle>
//	</create_billbatch>
//
// </function>
func CreateBillBatchRequest(batchTitle string) ([]byte, error) {

	type createBillBatchRequestFunction struct {
		XMLName    xml.Name `xml:"function"`
		ControlID  string   `xml:"controlid,attr"`
		BatchTitle string   `xml:"create_billbatch>batchtitle"`
	}

	requestFunction := createBillBatchRequestFunction{
		ControlID:  "create_billbatch",
		BatchTitle: batchTitle}

	billBatchRequest := request{}
	billBatchRequest.Authentication = getAuthenticationXML()
	billBatchRequest.Function = &requestFunction
	billBatchRequest.Control = getControlXML("createBatchRequest")

	createBillBatchXML, err := xml.Marshal(billBatchRequest)
	if err != nil {
		return nil, errors.Wrap(err, "creating bill batch request failed")
	}

	return createBillBatchXML, nil
}

// CreateBillRequest function generates XML for create_bill api
// <function controlid="create_bill">
//
//	<create_bill>
//	  <vendorid>VEN_001719</vendorid>
//	  <datecreated>
//	    <year>2016</year>
//	    <month>11</month>
//	    <day>1</day>
//	  </datecreated>
//	  <dateposted>
//	    <year>2016</year>
//	    <month>11</month>
//	    <day>1</day>
//	  </dateposted>
//	  <datedue>
//	    <year>2016</year>
//	    <month>11</month>
//	    <day>30</day>
//	  </datedue>
//	  <termname>Net 30</termname>
//	  <batchkey>6480</batchkey>
//	  <billno>123</billno>
//	  <description> GAP TESTING Uttam ON November 1 </description>
//	  <customfields>
//	    <customfield>
//	      <customfieldname>Loan Number</customfieldname>
//	      <customfieldvalue>********</customfieldvalue>
//	    </customfield>
//	  </customfields>
//	  <billitems>
//	    <lineitem>
//	      <accountlabel>70100</accountlabel>
//	      <amount>3450.00</amount>
//	      <memo>GAP TEsting by Uttam</memo>
//	      <locationid>200</locationid>
//	      <key>212121</key>
//	      <totalpaid>0</totalpaid>
//	      <totaldue>3450.00</totaldue>
//	      <projectid>LCC_1300</projectid>
//	      <customerid>cus_000032</customerid>
//	      <vendorid>VEN_001719</vendorid>
//	    </lineitem>
//	  </billitems>
//	</create_bill>
//
// </function>
func CreateBillRequest(billDetails Bill) ([]byte, error) {

	type lineItem struct {
		XMLName      xml.Name        `xml:"lineitem"`
		AccountLabel string          `xml:"accountlabel"`
		Amount       decimal.Decimal `xml:"amount"`
		Memo         string          `xml:"memo"`
		LocationID   int             `xml:"locationid"`
		Key          string          `xml:"key"`
		TotalPaid    decimal.Decimal `xml:"totalpaid"`
		TotalDue     decimal.Decimal `xml:"totaldue"`
		ProjectID    string          `xml:"projectid"`
		CustomerID   string          `xml:"customerid"`
		VendorID     string          `xml:"vendorid"`
	}

	lineItems := lineItem{
		AccountLabel: billDetails.AccountLabel,
		Amount:       billDetails.Amount,
		Memo:         billDetails.Memo,
		LocationID:   billDetails.LocationID,
		Key:          billDetails.Key,
		TotalPaid:    billDetails.TotalPaid,
		TotalDue:     billDetails.TotalDue,
		ProjectID:    billDetails.ProjectID,
		VendorID:     billDetails.VendorID,
		CustomerID:   billDetails.CustomerID}

	type createBillRequestFunction struct {
		XMLName          xml.Name   `xml:"function"`
		ControlID        string     `xml:"controlid,attr"`
		VendorID         string     `xml:"create_bill>vendorid"`
		CreatedDateYear  int        `xml:"create_bill>datecreated>year"`
		CreatedDateMonth int        `xml:"create_bill>datecreated>month"`
		CreatedDateDay   int        `xml:"create_bill>datecreated>day"`
		PostedDateYear   int        `xml:"create_bill>dateposted>year"`
		PostedDateMonth  int        `xml:"create_bill>dateposted>month"`
		PostedDateDay    int        `xml:"create_bill>dateposted>day"`
		DueDateYear      int        `xml:"create_bill>datedue>year"`
		DueDateMonth     int        `xml:"create_bill>datedue>month"`
		DueDateDay       int        `xml:"create_bill>datedue>day"`
		TermName         string     `xml:"create_bill>termname"`
		BatchKey         int        `xml:"create_bill>batchkey"`
		BillNo           string     `xml:"create_bill>billno"`
		Description      string     `xml:"create_bill>description"`
		CustomFieldName  string     `xml:"create_bill>customfields>customfield>customfieldname"`
		CustomFieldValue string     `xml:"create_bill>customfields>customfield>customfieldvalue"`
		BillItems        []lineItem `xml:"create_bill>billitems>lineitem"`
	}

	currentDate := time.Now().UTC()
	dueDate := currentDate.AddDate(0, 0, 30) // Due date is 30 days after creation date

	requestFunction := createBillRequestFunction{
		ControlID:        "create_bill",
		VendorID:         billDetails.VendorID,
		CreatedDateYear:  currentDate.Year(),
		CreatedDateMonth: int(currentDate.Month()),
		CreatedDateDay:   currentDate.Day(),
		PostedDateYear:   currentDate.Year(),
		PostedDateMonth:  int(currentDate.Month()),
		PostedDateDay:    currentDate.Day(),
		DueDateYear:      dueDate.Year(),
		DueDateMonth:     int(dueDate.Month()),
		DueDateDay:       dueDate.Day(),
		TermName:         billDetails.TermName,
		BatchKey:         billDetails.BatchKey,
		BillNo:           billDetails.BillNo,
		Description:      billDetails.Description,
		CustomFieldName:  "Loan Number",
		CustomFieldValue: billDetails.LoanNumber,
		BillItems:        []lineItem{lineItems}}

	billRequest := request{}

	billRequest.Authentication = getAuthenticationXML()
	billRequest.Function = &requestFunction
	billRequest.Control = getControlXML("createBillRequest")

	createBillBatchXML, err := xml.Marshal(billRequest)
	if err != nil {
		return nil, errors.Wrap(err, "creating bill request failed")
	}

	return createBillBatchXML, nil
}

func logRequestResponse(ctx context.Context, request string, response string) {
	if conf.Get().Intacct.Log {
		util.LogMessagef(ctx, "[Intacct] Request: %s Response: %s", request, response)
	}
}

// DefaultPageSize is max number of results GetReadByQueryXML function will return
const DefaultPageSize = 1000

// GetReadByQueryXML function generates XML for readByQuery api
// This is sample xml
// <function controlid="get_vendors">
// <readByQuery>
// <object> object </object>
// <fields> fields separated by comma </fields>
// <pagesize>3</pagesize>
// <query> query </query>
// <returnFormat>xml</returnFormat>
// </readByQuery>
// </function>
func GetReadByQueryXML(query, object, fields string) ([]byte, error) {

	type readByQueryRequestFunction struct {
		XMLName     xml.Name `xml:"function"`
		ControlID   string   `xml:"controlid,attr"`
		ReadByQuery string   `xml:"readByQuery>object"`
		Fields      string   `xml:"readByQuery>fields"`
		PageSize    int      `xml:"readByQuery>pagesize"`
		Query       string   `xml:"readByQuery>query"`
	}

	requestFunction := readByQueryRequestFunction{
		ControlID:   "test_controlid",
		ReadByQuery: object,
		Fields:      fields,
		PageSize:    DefaultPageSize,
		Query:       query,
	}

	readByQueryRequest := request{}
	readByQueryRequest.Authentication = getAuthenticationXML()
	readByQueryRequest.Function = &requestFunction
	readByQueryRequest.Control = getControlXML("readByQuery")

	requestXML, err := xml.Marshal(readByQueryRequest)
	if err != nil {
		return nil, errors.Wrap(err, "creating readByQuery for object "+object+" failed")
	}
	return requestXML, nil
}

// GetReversePaymentXML function generates XML for reverse_payment api
// This is sample xml
// <function controlid="reverse_payment">
// <reverse_appayment key="1234">
// <datereversed>
// <year>2015</year>
// <month>06</month>
// <day>30</day>
// </datereversed>
// </reverse_appayment>
// </function>
func GetReversePaymentXML(key int, reverseDate time.Time, description string) ([]byte, error) {

	type reverseApPayment struct {
		Key         int    `xml:"key,attr"`
		Year        int    `xml:"datereversed>year"`
		Month       int    `xml:"datereversed>month"`
		Day         int    `xml:"datereversed>day"`
		Description string `xml:"description"`
	}

	type reverseAppaymentFunction struct {
		XMLName          xml.Name         `xml:"function"`
		ControlID        string           `xml:"controlid,attr"`
		ReverseAppayment reverseApPayment `xml:"reverse_appayment"`
	}

	apPayment := reverseApPayment{
		Key:         key,
		Year:        reverseDate.Year(),
		Month:       int(reverseDate.Month()),
		Day:         reverseDate.Day(),
		Description: description,
	}
	requestFunction := reverseAppaymentFunction{
		ControlID:        "reverse payment",
		ReverseAppayment: apPayment,
	}

	reversePaymentRequest := request{}
	reversePaymentRequest.Authentication = getAuthenticationXML()
	reversePaymentRequest.Function = &requestFunction
	reversePaymentRequest.Control = getControlXML("reverse_payment")

	requestXML, err := xml.Marshal(reversePaymentRequest)
	if err != nil {
		return nil, errors.Wrap(err, "creating reverse_payment for key "+strconv.Itoa(key)+" failed")
	}
	return requestXML, nil
}

// GetReverseBillXML function generates XML for reverse_bill api
// This is sample xml
// <function controlid="reverse_bill">
// <reverse_bill key="1234">
// <datereversed>
// <year>2015</year>
// <month>06</month>
// <day>30</day>
// </datereversed>
// </reverse_bill>
// </function>
func GetReverseBillXML(key int, reverseDate time.Time, description string) ([]byte, error) {

	type reverseApBill struct {
		Key         int    `xml:"key,attr"`
		Year        int    `xml:"datereversed>year"`
		Month       int    `xml:"datereversed>month"`
		Day         int    `xml:"datereversed>day"`
		Description string `xml:"description"`
	}

	type reverseApBillFunction struct {
		XMLName       xml.Name      `xml:"function"`
		ControlID     string        `xml:"controlid,attr"`
		ReverseApBill reverseApBill `xml:"reverse_bill"`
	}

	apBill := reverseApBill{
		Key:         key,
		Year:        reverseDate.Year(),
		Month:       int(reverseDate.Month()),
		Day:         reverseDate.Day(),
		Description: description,
	}
	requestFunction := reverseApBillFunction{
		ControlID:     "reverse bill",
		ReverseApBill: apBill,
	}

	reverseBillRequest := request{}
	reverseBillRequest.Authentication = getAuthenticationXML()
	reverseBillRequest.Function = &requestFunction
	reverseBillRequest.Control = getControlXML("reverse_bill")

	requestXML, err := xml.Marshal(reverseBillRequest)
	if err != nil {
		return nil, errors.Wrap(err, "creating reverse_bill for key "+strconv.Itoa(key)+" failed")
	}
	return requestXML, nil
}

// CreateAPPaymentRequest function prepares XML header for create_paymentrequest api from
// intacct web services
// Following is a expected DTD for create_paymentrequest function
// <function controlid="create_paymentrequest">
//
//	<create_paymentrequest>
//	  <bankaccountid><!--Bank code --></bankaccountid>
//	  <vendorid><!-- Vendor ID --></vendorid>
//	  <memo><!-- Gap Contract Number LastName,FirstName --></memo>
//	  <paymentmethod>Printed Check</paymentmethod>
//	 <paymentdate>
//	   <year><!-- Current year --></year>
//	   <month><!-- Current month --></month>
//	   <day><!-- Todays date--></day>
//	 </paymentdate>
//	 <paymentrequestitems>
//	    <paymentrequestitem>
//	     <key><!-- Bill Key --></key>
//	     <paymentamount><!-- Payment amount--></paymentamount>
//	   </pamentrequestitem>
//	 </paymentrequestitems>
//	 <paymentdescription><!-- Gap claim for certificate "GAP Contract #" Acct # "Account #", First Name Last Name --></paymetdescription>
//	</create_paymentrequest>
//
// </function>
func CreateAPPaymentRequest(paymentDetails APPayment, intacctAccountDetails conf.IntacctAccountDetails) ([]byte, error) {
	type paymentRequestItem struct {
		XMLName       xml.Name        `xml:"paymentrequestitem"`
		Key           int             `xml:"key"`
		PaymentAmount decimal.Decimal `xml:"paymentamount"`
	}

	type createAPPaymentRequestFunction struct {
		XMLName             xml.Name             `xml:"function"`
		ControlID           string               `xml:"controlid,attr"`
		BankAccountID       string               `xml:"create_paymentrequest>bankaccountid"`
		VendorID            string               `xml:"create_paymentrequest>vendorid"`
		PaymentMethod       string               `xml:"create_paymentrequest>paymentmethod"`
		PaymentDateYear     int                  `xml:"create_paymentrequest>paymentdate>year"`
		PaymentDateMonth    int                  `xml:"create_paymentrequest>paymentdate>month"`
		PaymentDateDay      int                  `xml:"create_paymentrequest>paymentdate>day"`
		PaymentRequestItems []paymentRequestItem `xml:"create_paymentrequest>paymentrequestitems>paymentrequestitem"`
		PaymentDescription  string               `xml:"create_paymentrequest>paymentdescription"`
	}

	prItems := []paymentRequestItem{}
	for _, bill := range paymentDetails.Bills {
		prItems = append(prItems, paymentRequestItem{Key: bill.BillKey,
			PaymentAmount: bill.Amount})
	}
	currentDate := time.Now().UTC()

	requestFunction := createAPPaymentRequestFunction{
		ControlID:           "create_paymentrequest",
		BankAccountID:       intacctAccountDetails.BankID,
		VendorID:            paymentDetails.VendorID,
		PaymentMethod:       "Printed Check",
		PaymentDateYear:     currentDate.Year(),
		PaymentDateMonth:    int(currentDate.Month()),
		PaymentDateDay:      currentDate.Day(),
		PaymentRequestItems: prItems,
		PaymentDescription:  paymentDetails.Description}

	paymentRequest := request{}
	paymentRequest.Authentication = getAuthenticationXML()
	paymentRequest.Function = &requestFunction
	paymentRequest.Control = getControlXML("createPaymentRequest")

	createPaymentRequestXML, err := xml.Marshal(paymentRequest)
	if err != nil {
		return nil, errors.Wrap(err, "creating payment request failed")
	}
	return createPaymentRequestXML, nil
}

// CustomerID function verifies if customer record exists in intacct
func CustomerID(ctx context.Context, name string) (string, error) {
	query := "name like '%(" + strings.ToUpper(name) + ")%'"

	fields := "CUSTOMERID, NAME"
	customerXML, err := GetReadByQueryXML(query, "customer", fields)
	if err != nil {
		return "", errors.Wrap(err, "Creating XML request for getting customer failed")
	}

	resp, err := Request(ctx, customerXML)
	if err != nil {
		return "", errors.Wrap(err, "Intacct Server communication error")
	}

	type Customer struct {
		CustomerID string `xml:"CUSTOMERID" json:"customer_id"`
		Name       string `xml:"NAME" json:"name"`
	}

	resCustomer := struct {
		ResultStatus string     `xml:"operation>result>status"`
		Customers    []Customer `xml:"operation>result>data>customer"`
	}{}

	resBuf := bytes.NewBuffer(resp)
	err = xml.NewDecoder(resBuf).Decode(&resCustomer)

	if resCustomer.ResultStatus != ResultSuccess || len(resCustomer.Customers) != 1 {
		return "", errors.Wrap(err, "Failed to get customer from Intacct")
	}
	return resCustomer.Customers[0].CustomerID, nil
}

// ToTime convert intacct dateTime (11/10/2016) to golang Time
func ToTime(intacctDate string) time.Time {
	dateStr := strings.Split(intacctDate, "/")
	month, _ := strconv.Atoi(dateStr[0])
	day, _ := strconv.Atoi(dateStr[1])
	year, _ := strconv.Atoi(dateStr[2])
	return time.Date(year, time.Month(month), day, 0, 0, 0, 0, time.UTC)
}

// SubmitBill function submits the gap/vta claim to the intacct ap payment service
// This function creates a batch key for the given day if it does not exist already
// then using this batch key and other details, it creates a bill in intacct system
// This bill will be used by finance team to create a payment request from intacct WebUI
func SubmitBill(ctx context.Context, tx *sqlx.Tx, claimDetails ClaimPayload, billNumber string, billMemo string, description string) (*ResponsePayload, error) {

	claimPayment := ResponsePayload{}
	intacctResult := struct {
		AuthStatus   string `xml:"operation>authentication>status"`
		ResultStatus string `xml:"operation>result>status"`
		Key          int    `xml:"operation>result>key"`
		Error        []struct {
			ErrorMessage string `xml:"description2"`
			Correction   string `xml:"correction"`
		} `xml:"operation>result>errormessage>error"`
	}{}

	// Intacct uses Pacific timezone in billbatch, it's important to use the same timezone to create the bill batch
	// If we use UTC, there is difference of 8 hours, Hence at 4 PM- 02/27/17 Pacific time, the UTC time will be 00:00 Hrs
	// 02/28/17. So batch title in our system will be 02/28/17, which was generated on 02/27/17 Pacific time in Intacct
	// On next day, our system won't generate new batch as the date is still 02/28/17 in UTC. And Intacct rejects this key
	// because it was generated on 02/27/17 and a batchkey is valid only for that day
	// To avoid this issue, the timezone to calculate the date of batch should be what Intacct is using
	// we are using LoadLocation to take care of DST changes

	// loading this way the program won't start if the needed time zone data isn't available
	var loc = loadLocOrPanic("America/Los_Angeles")
	currentDate := time.Now().In(loc)

	batchTitle := strings.Join(
		[]string{strconv.Itoa(int(currentDate.Month())), strconv.Itoa(currentDate.Day()), strconv.Itoa(currentDate.Year())}, "/")

	batchKey, err := db.GetBatchKey(batchTitle)
	if err != nil {
		return nil, errors.Wrap(err, "Getting batch key failed")
	}

	claimPayment.BatchKey = batchKey

	if claimPayment.BatchKey == 0 {
		batchKeyXML, err := CreateBillBatchRequest(batchTitle)
		if err != nil {
			return nil, errors.Wrap(err, "Creating batch request failed")
		}

		batchKeyResponse, err := Request(ctx, batchKeyXML)
		if err != nil {
			return nil, errors.Wrap(err, "Intacct Server communication error")
		}

		batchResBuf := bytes.NewBuffer(batchKeyResponse)
		err = xml.NewDecoder(batchResBuf).Decode(&intacctResult)
		if err != nil {
			return nil, errors.Wrap(err, "Failed to docode batch key response")
		}

		if intacctResult.ResultStatus != ResultSuccess {
			return nil, errors.New("Failed to create bill batch")
		}

		claimPayment.BatchKey = intacctResult.Key
		err = db.StoreBatchKey(tx, batchTitle, claimPayment.BatchKey)
		if err != nil {
			return nil, errors.Wrap(err, "Storing batch key failed")
		}
	}

	billDetails := claimDetails.GetBill(billNumber, billMemo, batchKey, description)

	billXML, err := CreateBillRequest(billDetails)
	if err != nil {
		return nil, errors.Wrap(err, "Creating bill request failed")
	}

	billResponse, err := Request(ctx, billXML)
	if err != nil {
		return nil, errors.Wrap(err, "Intacct Server communication error")
	}

	billResBuf := bytes.NewBuffer(billResponse)
	err = xml.NewDecoder(billResBuf).Decode(&intacctResult)
	if err != nil {
		return nil, errors.Wrap(err, "Failed to decode create bill response")
	}

	if intacctResult.ResultStatus != ResultSuccess {
		// If error is provided in response then return that
		if len(intacctResult.Error) > 0 {
			return nil, &ErrorIntacct{ErrorMessage: intacctResult.Error[0].ErrorMessage, Correction: intacctResult.Error[0].Correction}
		}
		return nil, errors.New("Failed to create bill")
	}
	claimPayment.BillKey = intacctResult.Key

	return &claimPayment, nil
}

// APBillPaymentType payload for intacct apbillpayment need to be exported as GetPaymentKeys returns it
type APBillPaymentType struct {
	PaymentKey       int             `xml:"PAYMENTKEY"`
	Amount           decimal.Decimal `xml:"AMOUNT"`
	TrxAmount        decimal.Decimal `xml:"TRX_AMOUNT"`
	PaymentDate      string          `xml:"PAYMENTDATE"`
	State            string          `xml:"STATE"`
	ParentPaymentKey int             `xml:"PARENTPYMT"`
}

// GetPaymentKeys gets all payment key based on billKey
func GetPaymentKeys(ctx context.Context, billKey int) ([]APBillPaymentType, error) {
	billKeyStr := strconv.Itoa(billKey)
	fields := "PAYMENTKEY, AMOUNT, STATE, TRX_AMOUNT, PAYMENTDATE, PARENTPYMT"
	query := "RECORDKEY = " + billKeyStr
	requestXML, err := GetReadByQueryXML(query, "apbillpayment", fields)
	if err != nil {
		return nil, errors.Wrap(err, "Creating XML request for getting apbillpayment failed")
	}

	response, err := Request(ctx, requestXML)
	if err != nil {
		return nil, errors.Wrap(err, "Intacct Server communication error")
	}

	type data struct {
		Count         int                 `xml:"count,attr"`
		ApBillPayment []APBillPaymentType `xml:"apbillpayment"`
	}
	type result struct {
		Status string `xml:"status"`
		Data   data   `xml:"data"`
	}
	type operation struct {
		Result result `xml:"result"`
	}
	resApPayment := struct {
		Operation operation `xml:"operation"`
	}{}

	apResBuf := bytes.NewBuffer(response)
	err = xml.NewDecoder(apResBuf).Decode(&resApPayment)
	if err != nil {
		return nil, errors.Wrap(err, "Failed to decode getPaymentKey response")
	}

	if resApPayment.Operation.Result.Status != ResultSuccess {
		return nil, errors.New("Failed to get appayment from Intacct")
	}

	if resApPayment.Operation.Result.Data.Count == 0 {
		return nil, errors.Wrapf(errors.New("APBillPayment Not found"),
			"ApPayment with bill %d not found in intacct system", billKey)
	}
	return resApPayment.Operation.Result.Data.ApBillPayment, nil
}

// GetPaymentKey get payment key based on billKey and amount
func GetPaymentKey(ctx context.Context, billKey int, amount decimal.Decimal) (int, error) {
	billKeyStr := strconv.Itoa(billKey)
	fields := "PAYMENTKEY, AMOUNT"
	query := "RECORDKEY = " + billKeyStr
	requestXML, err := GetReadByQueryXML(query, "apbillpayment", fields)
	if err != nil {
		return 0, errors.Wrap(err, "Creating XML request for getting apbillpayment failed")
	}

	response, err := Request(ctx, requestXML)
	if err != nil {
		return 0, errors.Wrap(err, "Intacct Server communication error")
	}

	type apBillPaymentType struct {
		PaymentKey int             `xml:"PAYMENTKEY"`
		Amount     decimal.Decimal `xml:"AMOUNT"`
	}
	type data struct {
		Count         int                 `xml:"count,attr"`
		ApBillPayment []apBillPaymentType `xml:"apbillpayment"`
	}
	type result struct {
		Status string `xml:"status"`
		Data   data   `xml:"data"`
	}
	type operation struct {
		Result result `xml:"result"`
	}
	resApPayment := struct {
		Operation operation `xml:"operation"`
	}{}

	apResBuf := bytes.NewBuffer(response)
	err = xml.NewDecoder(apResBuf).Decode(&resApPayment)
	if err != nil {
		return 0, errors.Wrap(err, "Failed to decode getPaymentKey response")
	}

	if resApPayment.Operation.Result.Status != ResultSuccess {
		return 0, errors.New("Failed to get appayment from Intacct")
	}
	var apBillPayment apBillPaymentType
	if resApPayment.Operation.Result.Data.Count == 0 {
		return 0, errors.Wrapf(errors.New("APBillPayment Not found"),
			"ApPayment with bill %d not found in intacct system", billKey)
	} else if resApPayment.Operation.Result.Data.Count == 1 {
		apBillPayment = resApPayment.Operation.Result.Data.ApBillPayment[0]
	} else if resApPayment.Operation.Result.Data.Count > 1 {
		for _, apBillPayment = range resApPayment.Operation.Result.Data.ApBillPayment {
			if apBillPayment.Amount.Equals(amount) {
				break
			}
		}
	}

	if apBillPayment.PaymentKey == 0 {
		return 0, errors.Wrapf(ErrPaymentNotComplete, "Payment not complete for bill %d", billKey)
	}
	return apBillPayment.PaymentKey, nil
}

// PaidInfo returns checkNumber, paidDate and paidAmount
func PaidInfo(ctx context.Context, paymentKey int) (*ResponsePayload, error) {
	fields := "STATE, TRX_TOTALPAID, DOCUMENTNUMBER, PAYMENTDATE"
	query := " RECORDNO = " + strconv.Itoa(paymentKey)
	requestXML, err := GetReadByQueryXML(query, "appayment", fields)
	if err != nil {
		return nil, errors.Wrap(err, "Creating XML request for getting payment details failed")
	}

	response, err := Request(ctx, requestXML)
	if err != nil {
		return nil, errors.Wrap(err, "Intacct Server communication error")
	}

	type apPaymentType struct {
		State          string          `xml:"STATE"`
		TrxTotalPaid   decimal.Decimal `xml:"TRX_TOTALPAID"`
		DocumentNumber string          `xml:"DOCUMENTNUMBER"`
		PaymentDate    string          `xml:"PAYMENTDATE"`
	}
	type data struct {
		Count     int             `xml:"count,attr"`
		ApPayment []apPaymentType `xml:"appayment"`
	}
	type result struct {
		Status string `xml:"status"`
		Data   data   `xml:"data"`
	}
	type operation struct {
		Result result `xml:"result"`
	}
	resApPayment := struct {
		Operation operation `xml:"operation"`
	}{}

	apResBuf := bytes.NewBuffer(response)
	err = xml.NewDecoder(apResBuf).Decode(&resApPayment)
	if err != nil {
		return nil, errors.Wrap(err, "Failed to decode PaidInfo response")
	}

	if resApPayment.Operation.Result.Status != ResultSuccess {
		return nil, errors.New("Failed to get appayment from Intacct")
	}
	var apPayment apPaymentType
	if resApPayment.Operation.Result.Data.Count == 0 {
		return nil, errors.Wrapf(errors.New("APPayment Not found"),
			"ApPayment with paymentKey %d not found in intacct system", paymentKey)
	}

	apPayment = resApPayment.Operation.Result.Data.ApPayment[0]

	if apPayment.State != PaymentStateComplete {
		return nil, errors.Wrapf(ErrPaymentNotComplete, "Payment not complete for paymentKey %d", paymentKey)
	}

	billResponse := ResponsePayload{}
	billResponse.CheckNumber, err = strconv.Atoi(apPayment.DocumentNumber)
	if err != nil {
		return nil, errors.Wrapf(err, "Error converting documentNumber for %d", paymentKey)
	}
	billResponse.PaidDate = ToTime(apPayment.PaymentDate)
	billResponse.Amount = apPayment.TrxTotalPaid
	return &billResponse, nil
}

// LoadLocOrPanic will provide locale time otherwise it will throw error
func loadLocOrPanic(l string) *time.Location {
	loc, err := time.LoadLocation(l)
	if err != nil {
		panic(err)
	}
	return loc
}

// VoidIntacctPayment first reverse the payment and then reverse the bill.
// It returns true in case if check is already voided in Intacct, else false.
func VoidIntacctPayment(ctx context.Context, billKey int, amount decimal.Decimal, reason string) (bool, error) {
	paymentKey, err := GetPaymentKey(ctx, billKey, amount)
	if err != nil {
		return false, errors.Wrap(err, "could not get payment key")
	}

	var loc = loadLocOrPanic("America/Los_Angeles")
	currentDate := time.Now().In(loc)

	// Reverse payment
	reversePaymentXML, err := GetReversePaymentXML(paymentKey, currentDate, reason)
	if err != nil {
		return false, errors.Wrap(err, "creating XML request for reverse_payment failed")
	}

	response, err := Request(ctx, reversePaymentXML)
	if err != nil {
		return false, errors.Wrap(err, "intacct Server communication error")
	}

	intacctResult := struct {
		AuthStatus   string `xml:"operation>authentication>status"`
		ResultStatus string `xml:"operation>result>status"`
		Key          int    `xml:"operation>result>key"`
		ErrorCode    string `xml:"operation>result>errormessage>error>errorno"`
	}{}

	apResBufReversePayment := bytes.NewBuffer(response)
	err = xml.NewDecoder(apResBufReversePayment).Decode(&intacctResult)
	if err != nil {
		return false, errors.Wrap(err, "failed to decode reverse_payment response")
	}

	if intacctResult.ResultStatus != ResultSuccess {
		if intacctResult.ErrorCode == IntacctReversedClaimErrorCode {
			return true, errors.New("already reversed in Intacct")
		}
		return false, errors.New("failed to reverse payment")
	}

	// Reverse bill
	requestXML, err := GetReverseBillXML(billKey, currentDate, reason)
	if err != nil {
		return false, errors.Wrap(err, "creating XML request for reverse_bill failed")
	}

	response, err = Request(ctx, requestXML)
	if err != nil {
		return false, errors.Wrap(err, "intacct Server communication error")
	}

	apResBufReverseBill := bytes.NewBuffer(response)
	err = xml.NewDecoder(apResBufReverseBill).Decode(&intacctResult)
	if err != nil {
		return false, errors.Wrap(err, "failed to decode reverse_bill response")
	}

	if intacctResult.ResultStatus != ResultSuccess {
		if intacctResult.ErrorCode == IntacctReversedClaimErrorCode {
			return true, errors.New("already reversed in Intacct")
		}
		return false, errors.New("failed to reverse bill")
	}

	return false, nil
}
