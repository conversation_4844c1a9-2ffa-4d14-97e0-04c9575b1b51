package intacct

import (
	"strings"
	"time"

	"github.com/shopspring/decimal"
)

// ResponsePayload struct contains information about payment
type ResponsePayload struct {
	CheckNumber int
	Amount      decimal.Decimal
	PaidDate    time.Time
	BatchKey    int
	BillKey     int
}

// ClaimPayload interface will be required in SubmitBill api,
// Tt will be required to make sure claimPayload object should implement this field
type ClaimPayload interface {
	GetBill(billNumber string, billMemo string, batchKey int, description string) Bill
}

// Vendor contains vendor information like VendorID, Name, Address1, Address2, City, State, Zip and Country
type Vendor struct {
	VendorID string `xml:"VENDORID" json:"vendor_id"`
	Name     string `xml:"DISPLAYCONTACT.COMPANYNAME" json:"name"`
	Address1 string `xml:"DISPLAYCONTACT.MAILADDRESS.ADDRESS1" json:"address1"`
	Address2 string `xml:"DISPLAYCONTACT.MAILADDRESS.ADDRESS2" json:"address2"`
	City     string `xml:"DISPLAYCONTACT.MAILADDRESS.CITY" json:"city"`
	State    string `xml:"DISPLAYCONTACT.MAILADDRESS.STATE" json:"state"`
	Zip      string `xml:"DISPLAYCONTACT.MAILADDRESS.ZIP" json:"zip"`
	Country  string `xml:"DISPLAYCONTACT.MAILADDRESS.COUNTRY" json:"country"`
}

// VendorQueryPayload is required to generate query for vendor intacct search
type VendorQueryPayload struct {
	query string
}

// Name receiver will add name condition in vendor intacct query
func (payload VendorQueryPayload) Name(name string) VendorQueryPayload {
	if payload.query != "" {
		payload.query += ` or `
	}
	payload.query += " name like '%" + strings.ToUpper(name) + "%'"
	return payload
}

// VendorID receiver will add VendorId condition in vendor intacct query
func (payload VendorQueryPayload) VendorID(vendorID string) VendorQueryPayload {
	if payload.query != "" {
		payload.query += ` or `
	}
	payload.query += " VENDORID = '" + strings.ToUpper(vendorID) + "'"
	return payload
}

// Query returns existing query based of name and vendor id
func (payload VendorQueryPayload) Query() string {
	return payload.query
}
