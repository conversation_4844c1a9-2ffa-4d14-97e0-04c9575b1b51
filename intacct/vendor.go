package intacct

import (
	"bytes"
	"context"
	"encoding/xml"

	"github.com/pkg/errors"
)

// GetVendors make called to intacct based on vendroQuery params
// it will returns vendors collection
func GetVendors(ctx context.Context, vendorQuery VendorQueryPayload) ([]Vendor, error) {
	fields := "VENDORID, DISPLAYCONTACT.COMPANYNAME, DISPLAYCONTACT.MAILADDRESS.ADDRESS1, DISPLAYCONTACT.MAILADDRESS.ADDRESS2, DISPLAYCONTACT.MAILADDRESS.CITY, DISPLAYCONTACT.MAILADDRESS.STATE, DISPLAYCONTACT.MAILADDRESS.ZIP, DISPLAYCONTACT.MAILADDRESS.COUNTRY"
	vendorXML, err := GetReadByQueryXML(vendorQuery.Query(), "vendor", fields)
	if err != nil {
		return nil, errors.Wrap(err, "Creating XML request for getting vendor failed")
	}
	vendorResponse, err := Request(ctx, vendorXML)
	if err != nil {
		return nil, errors.Wrap(err, "Intacct Server communication error")
	}
	resVendor := struct {
		ResultStatus string   `xml:"operation>result>status"`
		Vendors      []Vendor `xml:"operation>result>data>vendor"`
	}{}
	vendorResBuf := bytes.NewBuffer(vendorResponse)
	err = xml.NewDecoder(vendorResBuf).Decode(&resVendor)
	if err != nil {
		return nil, errors.Wrap(err, "Failed to decode vendor")
	}
	if resVendor.ResultStatus != ResultSuccess {
		return nil, errors.Wrap(err, "Failed to get vendors from Intacct")
	}
	return resVendor.Vendors, err
}
