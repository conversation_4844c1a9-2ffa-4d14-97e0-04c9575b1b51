package util

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"phizz/conf"
	"phizz/nr"
	"time"

	newrelic "github.com/newrelic/go-agent"
	"github.com/pkg/errors"
)

const (
	userAgent = "TCAClient"
)

// RemovePasswordFunc is a function that removes password information from a string.
type RemovePasswordFunc func(string) string

// SetAuthorizationFunc is a function that sets the Authorization header on an HTTP request.
type SetAuthorizationFunc func(context.Context, *http.Request) (*http.Request, error)

// SetHeadersFunc is a function that sets headers on an HTTP request.
type SetHeadersFunc func(context.Context, *http.Request) (*http.Request, error)

// GetLogLevelFunc is a function that returns the log level for external API calls.
type GetLogLevelFunc func() conf.ExternalAPILogLevel

// Request makes an HTTP request to the given URL with the given method, query parameters, and body content.
func Request(ctx context.Context, dmsProvider string, url string, method string,
	queryParams *url.Values,
	bodyContent []byte,
	setAuthorization SetAuthorizationFunc,
	setHeaders SetHeadersFunc,
	removePassword RemovePasswordFunc,
	getLogLevel GetLogLevelFunc) ([]byte, int, error) {

	txn := newrelic.FromContext(ctx)

	logLevel := getLogLevel()

	if logLevel != conf.LogLevelNone {
		LogMessagef(ctx, "[%s] URL: %s, Method: %s", dmsProvider, url, method)
	}

	var body io.Reader
	if method == http.MethodPost || method == http.MethodPut {
		body = bytes.NewReader(bodyContent)
	}

	req, err := http.NewRequest(method, url, body)
	if err != nil {
		return nil, 0, err
	}

	if queryParams != nil {
		req.URL.RawQuery = queryParams.Encode()
	}

	req, err = setAuthorization(ctx, req)
	if err != nil {
		err = errors.WithMessagef(err, "[%s] error setting authorization", dmsProvider)
		return nil, 0, err
	}

	req, err = setHeaders(ctx, req)
	if err != nil {
		err = errors.WithMessagef(err, "[%s] error setting headers", dmsProvider)
		return nil, 0, err
	}

	// Set the User-Agent
	req.Header.Set("User-Agent", userAgent)

	client := http.Client{Timeout: time.Second * 60}

	resp, err := nr.External(txn, client, req)
	if err != nil {
		// We want all details if error happened in request.
		status := "nil"
		if resp != nil {
			status = resp.Status
		}

		return nil, http.StatusInternalServerError, errors.Wrap(err, fmt.Sprintf("[%s]URL: %s, PARAMS: %v, BODY: %s, Response Status: %s", dmsProvider, url, queryParams, bodyContent, status))
	}
	defer func() { _ = resp.Body.Close() }()

	bodyBytes, n, err := ReadResponse(resp.Body)
	if err != nil && err != io.EOF && err != io.ErrUnexpectedEOF {
		return nil, resp.StatusCode, errors.Wrap(err, fmt.Sprintf("[%s]URL: %s PARAMS: %v REQUEST BODY: %s Response Status: %s", dmsProvider, url, queryParams, bodyContent, resp.Status))
	}

	logResponse(ctx, dmsProvider, getLogLevel(), url, resp.Status, int(n), bodyBytes, removePassword)

	return bodyBytes, resp.StatusCode, nil
}

// logResponse logs a response if logging is enabled.
// The full response body may be logged if full logging is enabled or if the  Response payload included a error
// Otherwise, only the response status and the length of the response body is logged.
func logResponse(ctx context.Context, dmsProvider string, logLevel conf.ExternalAPILogLevel, url string, respStatus string, length int, respBody []byte, removePassword RemovePasswordFunc) {
	if logLevel == conf.LogLevelNone {
		return
	}

	// Determine if we need to log the full response body
	logFullBody := false
	if logLevel == conf.LogLevelFull {
		logFullBody = true
	}

	if logFullBody {
		logFullResponse(ctx, dmsProvider, url, respStatus, length, respBody, removePassword)
	} else {
		logSummaryResponse(ctx, dmsProvider, url, respStatus, length)
	}
}

func logFullResponse(ctx context.Context, dmsProvider string, url string, respStatus string, length int, respBody []byte, removePassword RemovePasswordFunc) {
	// Remove the Password information from the response body if it's there.
	bodyStr := string(respBody)
	if removePassword != nil {
		bodyStr = removePassword(bodyStr)
	}
	LogMessagef(ctx, "[%s] URL: %s, Response Status: %s, Response Body Length: %d, Response Formatted: %s", dmsProvider, url, respStatus, length, bodyStr)
}

func logSummaryResponse(ctx context.Context, dmsProvider string, url string, respStatus string, length int) {
	LogMessagef(ctx, "[%s] URL: %s, Response Status: %s, Response Body Length: %d", dmsProvider, url, respStatus, length)
}
