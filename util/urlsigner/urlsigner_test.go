package urlsigner

import (
	"errors"
	"fmt"
	"net/url"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestSigner(t *testing.T) {
	inputs := []struct {
		name         string
		unsigned     string
		signUserID   int
		verifyUserID int
		alterWith    string // alter the signed url with this value before verifying
		expectedErr  error
	}{
		{
			name:         "with query",
			unsigned:     "https://example.com/a/b/c?foo=bar",
			signUserID:   1,
			verifyUserID: 1,
			expectedErr:  nil,
		},
		{
			name:         "without query",
			unsigned:     "https://example.com/a/b/c",
			signUserID:   1,
			verifyUserID: 1,
			expectedErr:  nil,
		},
		{
			name:         "with only question mark",
			unsigned:     "https://example.com/a/b/c?",
			signUserID:   1,
			verifyUserID: 1,
			expectedErr:  nil,
		},
		{
			name:         "absolute path",
			unsigned:     "/a/b/c",
			signUserID:   1,
			verifyUserID: 1,
			expectedErr:  nil,
		},
		{
			name:         "invalid signature",
			unsigned:     "https://example.com/a/b/c?foo=bar",
			signUserID:   1,
			verifyUserID: 1,
			alterWith:    "&page_num=3&page_size=20",
			expectedErr:  ErrLinkModified,
		},
		{
			name:         "userID mismatch",
			unsigned:     "https://example.com/a/b/c?foo=bar",
			signUserID:   1,
			verifyUserID: 2,
			expectedErr:  ErrInvalidUser,
		},
	}

	for _, tt := range inputs {
		signer := New([]byte("lets test"))
		t.Run(tt.name, func(t *testing.T) {
			signed, err := signer.Sign(tt.unsigned, tt.signUserID, time.Second*10)
			require.NoError(t, err, "Sign must not return error")

			// ensure that the signed url is valid
			parsedURL, err := url.Parse(signed)
			require.NoError(t, err, "signed url must be valid")

			// validate signature
			signed += tt.alterWith

			err = signer.Verify(signed, tt.verifyUserID)
			// If error was not expected, then ensure that one was not returned
			if tt.expectedErr == nil {
				require.NoError(t, err, "no error expected from verify")
			}

			// Ensure the correct error type was returned
			assert.True(t, errors.Is(err, tt.expectedErr), "unexpected error type")

			query := parsedURL.Query()

			assert.True(t, query.Has("signature"), "Signed url does not have signature")
			assert.True(t, query.Has("expires"), "Signed url does not have expires")
			userID := query.Get("user_id")
			assert.Equal(t, userID, fmt.Sprintf("%d", tt.signUserID), "signed url user ID does not match")
		})
	}
}
