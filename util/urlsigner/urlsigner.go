package urlsigner

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"fmt"

	"net/url"
	"strconv"
	"time"

	"github.com/pkg/errors"
)

var (
	// ErrExpired is returned when a signed link is expired
	ErrExpired = fmt.Errorf("link is expired")
	// ErrInvalidUser is returned a signed link was created for a different user
	ErrInvalidUser = fmt.Errorf("link for other user")
	// ErrLinkModified is returned when a signed link does not match the signature that was generated for it
	ErrLinkModified = fmt.Errorf("link has been modified")
	// ErrParseURL is returned when a signed link could not be parsed
	ErrParseURL = fmt.E<PERSON>rf("could not parse URL")
)

// URLSigner is used to sign URLs and verify signed URLs
type URLSigner struct {
	key []byte
}

// New constructs a new URL Signer
func New(key []byte) *URLSigner {
	return &URLSigner{
		key: key,
	}
}

// Sign signs a URL so that any change to the URL can be detected
func (s URLSigner) Sign(unsigned string, userID int, lifespan time.Duration) (string, error) {
	u, err := url.Parse(unsigned)
	if err != nil {
		return "", errors.Wrapf(err, "could not parse URL '%s'", unsigned)
	}

	query := u.Query()

	// Add an expiration time to the URL
	expires := strconv.FormatInt(time.Now().Add(lifespan).Unix(), 10)
	query.Set("expires", expires)

	// Add the user id to the URL
	query.Set("user_id", fmt.Sprintf("%d", userID))

	// Preserve the added query parameters in RawQuery
	// before calculating the signature
	u.RawQuery = query.Encode()

	signature := s.calcSignature(*u)

	// Add the signature as a parameter to the URL
	query.Set("signature", signature)
	u.RawQuery = query.Encode()

	return u.String(), nil
}

// Verify ensures that a signed URL has not been modified
func (s URLSigner) Verify(signed string, userID int) error {
	u, err := url.Parse(signed)
	if err != nil {
		return errors.Wrapf(ErrParseURL, err.Error())
	}

	query := u.Query()
	signature := query.Get("signature")
	expires := query.Get("expires")
	expectedUserIDParam := query.Get("user_id")

	// Check if the signature has expired
	expiresAt, err := strconv.ParseInt(expires, 10, 64)
	if err != nil {
		return errors.Wrapf(ErrParseURL, "could not convert value for expires: %s", expires)
	}

	expiresTime := time.Unix(expiresAt, 0)
	if expiresTime.Before(time.Now()) {
		// No Wrap needed. This error will not be logged
		return ErrExpired
	}

	// Check if the userID matches
	expectedUserID, err := strconv.ParseInt(expectedUserIDParam, 10, 64)
	if err != nil {
		// No Wrap needed. This error will not be logged
		return ErrInvalidUser
	}

	if expectedUserID != int64(userID) {
		// No Wrap needed. This error will not be logged
		return ErrInvalidUser
	}

	// Compare the provided signature with the recreated signature
	expectedSignature := s.calcSignature(*u)
	if signature != expectedSignature {
		return ErrLinkModified
	}

	return nil
}

func (s URLSigner) calcSignature(u url.URL) string {
	// Remove the signature before validating
	query := u.Query()
	query.Del("signature")
	u.RawQuery = query.Encode()

	// Don't include host and scheme in signature
	u.Host = ""
	u.Scheme = ""
	mac := hmac.New(sha256.New, s.key)
	mac.Write([]byte(u.String()))
	return base64.URLEncoding.EncodeToString(mac.Sum(nil))
}
