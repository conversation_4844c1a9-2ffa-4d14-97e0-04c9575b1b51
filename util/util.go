package util

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"log"
	"net/http"
	"strconv"

	"phizz/conf"
	"phizz/db"

	"github.com/go-chi/chi/middleware"
	"github.com/stvp/rollbar"
)

// cSpell: Ignore rollbar, phizz, stvp

type contextKey string

// contextKeyCurrentUser is the context key for current user
// Warning: duplicated from session.ContextKeyCurrentUser in order to avoid an import cycle
const contextKeyCurrentUser = contextKey("CurrentUser")

// ReportError reports errors to Sentry and New Relic.
func ReportError(req *http.Request, err error) {
	LogError(req.Context(), err)

	ctx := req.Context()

	reqID := middleware.GetReqID(ctx)
	reqField := rollbar.Field{Name: "request_id", Data: reqID}
	personField := rollbar.Field{Name: "person", Data: map[string]string{"id": "unknown"}}
	if val := ctx.Value(contextKeyCurrentUser); val != nil {
		if user := val.(*db.User); user != nil {
			personField.Data = map[string]string{
				"id":    strconv.Itoa(user.ID),
				"email": user.Email,
			}
		}
	}
	rollbar.RequestErrorWithStackSkip(rollbar.ERR, req, err, 1, &reqField, &personField)
}

// LogError logs an error but does not report it to Sentry.io
func LogError(ctx context.Context, err error) {
	reqID := middleware.GetReqID(ctx)
	if conf.Get().AppEnv == "development" {
		log.Printf("[%s] %s: %+v\n", reqID, rollbar.ERR, err)
	} else {
		log.Printf("[%s] %s: %v\n", reqID, rollbar.ERR, err)
	}
}

// LogErrorMessage logs a message as an error but does not report it to Rollbar
func LogErrorMessage(ctx context.Context, msg interface{}) {
	reqID := middleware.GetReqID(ctx)
	log.Printf("[%s] %s: %v\n", reqID, rollbar.ERR, msg)
}

// LogWarning logs a warning but does not report it to Rollbar
func LogWarning(ctx context.Context, err error) {
	reqID := middleware.GetReqID(ctx)
	if conf.Get().AppEnv == "development" {
		log.Printf("[%s] %s: %+v\n", reqID, rollbar.WARN, err)
	} else {
		log.Printf("[%s] %s: %v\n", reqID, rollbar.WARN, err)
	}
}

// LogMessage logs a message
func LogMessage(ctx context.Context, msg interface{}) {
	reqID := middleware.GetReqID(ctx)
	log.Printf("[%s]: %v", reqID, msg)
}

// LogMessagef logs a formatted message
func LogMessagef(ctx context.Context, msg string, args ...interface{}) {
	reqID := middleware.GetReqID(ctx)
	log.Printf("[%s]: %s", reqID, fmt.Sprintf(msg, args...))
}

// ContentTypeByExtension returns content type based on file extension
func ContentTypeByExtension(ext string) string {
	contentType := ""
	switch ext {
	case ".mp4":
		contentType = "video/mp4"
	case ".webm":
		contentType = "video/webm"
	case ".mov":
		// Workaround to make browser play mov files
		contentType = "video/mp4"
	case ".pptx":
		contentType = "application/vnd.openxmlformats-officedocument.presentationml.presentation"
	case ".ppt":
		contentType = "application/vnd.ms-powerpoint"
	case ".pdf":
		contentType = "application/pdf"
	case ".png":
		contentType = "image/x-png"
	case ".jpg", ".jpeg":
		contentType = "image/jpeg"
	default:
		contentType = ""
	}
	return contentType
}

// ReadResponse reads response from stream
func ReadResponse(stream io.Reader) ([]byte, int64, error) {
	buf := new(bytes.Buffer)
	n, err := buf.ReadFrom(stream)
	return buf.Bytes(), n, err
}
