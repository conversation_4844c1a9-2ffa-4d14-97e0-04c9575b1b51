package credit

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"database/sql"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"

	"phizz/db"
	"phizz/handlers"

	"github.com/jmoiron/sqlx"
	"github.com/pkg/errors"
)

const (
	mask       = "XXXXX"
	encryptKey = "K6mVg0MmuwmdRNQuoIAt9SN8EGF5DS8T2drTtZjgwRM="
)

// CardPayload card payload needed exported for CardDetails()
type CardPayload struct {
	ID              int       `json:"id" db:"id"`
	NameOnCard      string    `json:"name_on_card" db:"name_on_card"`
	CardNumber      string    `json:"card_number" db:"card_number"`
	ExpirationMonth int       `json:"expiration_month" db:"expiration_month"`
	ExpirationYear  int       `json:"expiration_year" db:"expiration_year"`
	ManagerName     string    `json:"manager_name" db:"manager_name"`
	ManagerPhone    string    `json:"manager_phone" db:"manager_phone"`
	ManagerFax      string    `json:"manager_fax" db:"manager_fax"`
	UpdatedBy       int       `json:"_" db:"updated_by_user_id"`
	UpdatedAt       time.Time `json:"_" db:"updated_at"`
}

func (p *CardPayload) encryptCardNumber() error {
	key, err := base64.StdEncoding.DecodeString(encryptKey)
	if err != nil {
		return err
	}
	c, err := aes.NewCipher([]byte(key))
	if err != nil {
		return err
	}

	gcm, err := cipher.NewGCM(c)
	if err != nil {
		return err
	}

	nonce := make([]byte, gcm.NonceSize())
	if _, err = io.ReadFull(rand.Reader, nonce); err != nil {
		return err
	}

	p.CardNumber = base64.StdEncoding.EncodeToString(gcm.Seal(nonce, nonce, []byte(p.CardNumber), nil))

	return nil
}

func (p *CardPayload) decryptCardNumber() error {
	key, err := base64.StdEncoding.DecodeString(encryptKey)
	if err != nil {
		return err
	}
	c, err := aes.NewCipher(key)
	if err != nil {
		return err
	}

	gcm, err := cipher.NewGCM(c)
	if err != nil {
		return err
	}

	nonceSize := gcm.NonceSize()
	if len(p.CardNumber) < nonceSize {
		return errors.New("ciphertext too short")
	}

	cardNumber, err := base64.StdEncoding.DecodeString(p.CardNumber)
	if err != nil {
		return err
	}

	nonce, ciphertext := cardNumber[:nonceSize], cardNumber[nonceSize:]
	plaintText, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return err
	}

	p.CardNumber = string(plaintText)
	return nil
}

// Luhn algorithm to validate card number
func (p *CardPayload) validateCardNumber() bool {
	var sum int
	var toggle bool
	cardLen := len(p.CardNumber)
	for i := cardLen - 1; i > -1; i-- {
		mod, _ := strconv.Atoi(string(p.CardNumber[i]))
		if toggle {
			mod *= 2
			if mod > 9 {
				mod = (mod % 10) + 1
			}
		}
		toggle = !toggle
		sum += mod
	}
	return sum%10 == 0
}

func (p *CardPayload) maskCardNumber() {
	cardNumber := p.CardNumber[0:6] + mask + p.CardNumber[11:]
	p.CardNumber = cardNumber
}

func (p *CardPayload) isMaskNumber() bool {
	cardNumber := p.CardNumber[0:6] + mask + p.CardNumber[11:]
	return p.CardNumber == cardNumber
}

func (p *CardPayload) validate() map[string]string {
	formErrors := map[string]string{}
	if p.NameOnCard == "" {
		formErrors["invalid_name_on_card"] = "Please enter name on card"
	}
	if p.ManagerPhone == "" {
		formErrors["invalid_manager_phone"] = "Please enter manager phone number"
	}
	if p.ManagerName == "" {
		formErrors["invalid_manager_name"] = "Please enter manager name"
	}
	if p.ManagerFax == "" {
		formErrors["invalid_manager_fax"] = "Please enter manager fax"
	}
	if p.ExpirationMonth < 0 || p.ExpirationMonth > 12 {
		formErrors["invalid_expiration_month"] = "Month is invalid."
	}
	if len(strconv.Itoa(p.ExpirationYear)) < 4 {
		formErrors["invalid_expiration_year"] = "Please enter expiration year"
	}
	if p.ExpirationYear < time.Now().UTC().Year() {
		formErrors["credit_card_expired"] = "Credit card has expired."
	}
	if p.ExpirationYear == time.Now().UTC().Year() && p.ExpirationMonth < int(time.Now().UTC().Month()) {
		formErrors["credit_card_expired"] = "Credit card has expired."
	}
	if p.CardNumber == "" || len(p.CardNumber) < 13 || len(p.CardNumber) > 19 || (!p.validateCardNumber() && !p.isMaskNumber()) {
		formErrors["invalid_card_number"] = "Card number is invalid"
	}
	return formErrors
}

func (p *CardPayload) clean() {
	p.CardNumber = strings.TrimSpace(p.CardNumber)
	p.NameOnCard = strings.TrimSpace(p.NameOnCard)
	p.ManagerFax = strings.TrimSpace(p.ManagerFax)
	p.ManagerName = strings.TrimSpace(p.ManagerName)
	p.ManagerPhone = strings.TrimSpace(p.ManagerPhone)
}

func checkNotes(reqPayload *CardPayload, dbPayload *CardPayload, isMaskNumber bool) []string {
	var notes []string
	if dbPayload.ID == 0 {
		notes = append(notes, "Created card is added")
	} else {
		if reqPayload.ExpirationMonth != dbPayload.ExpirationMonth {
			notes = append(notes,
				fmt.Sprintf("Expiration month fax is changed from %s to %s",
					strconv.Itoa(dbPayload.ExpirationMonth), strconv.Itoa(reqPayload.ExpirationMonth)))
		}
		if reqPayload.ManagerFax != dbPayload.ManagerFax {
			notes = append(notes,
				fmt.Sprintf("Manager fax is changed from %s to %s", dbPayload.ManagerFax, reqPayload.ManagerFax))
		}
		if reqPayload.NameOnCard != dbPayload.NameOnCard {
			notes = append(notes,
				fmt.Sprintf("Name on card is changed from %s to %s", dbPayload.NameOnCard, reqPayload.NameOnCard))
		}
		if reqPayload.ManagerName != dbPayload.ManagerName {
			notes = append(notes,
				fmt.Sprintf("Manager Name is changed from %s to %s", dbPayload.ManagerName, reqPayload.ManagerName))
		}
		if reqPayload.ExpirationYear != dbPayload.ExpirationYear {
			notes = append(notes,
				fmt.Sprintf("Expiration year is changed from %s to %s",
					strconv.Itoa(dbPayload.ExpirationYear), strconv.Itoa(reqPayload.ExpirationYear)))
		}
		if reqPayload.ManagerPhone != dbPayload.ManagerPhone {
			notes = append(notes,
				fmt.Sprintf("Manager phone is changed from %s to %s", dbPayload.ManagerPhone, reqPayload.ManagerPhone))
		}
		dbPayload.decryptCardNumber()
		reqPayload.decryptCardNumber()
		if !isMaskNumber {
			if reqPayload.CardNumber != dbPayload.CardNumber {
				notes = append(notes, "Card number is changed")
			}
		}
	}
	return notes
}

// CardShow show card details
func CardShow(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	payload, err := CardDetails()
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage(err,
				"Record not found", nil)
		}
		return http.StatusInternalServerError, handlers.ErrorMessage(err,
			"Error in getting credit card details", nil)
	}

	// mask number
	payload.maskCardNumber()

	return http.StatusOK, map[string]interface{}{"card": payload}
}

// CardDetails will return card details
func CardDetails() (*CardPayload, error) {
	payload := CardPayload{}
	query := `select id, name_on_card, card_number, expiration_month, expiration_year, manager_name, manager_phone, manager_fax 
			from credit_cards limit 1`
	err := db.Get().Get(&payload, query)
	if err != nil {
		return nil, err
	}
	// decrypt number
	err = payload.decryptCardNumber()
	return &payload, err
}

// CardUpdate updates card details
func CardUpdate(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	payload := &CardPayload{}
	err := cardDetailsFromReq(payload, req)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Malformed Credit Card data for update.", nil)
	}
	payload.clean()
	formErrors := payload.validate()
	if len(formErrors) > 0 {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Form validations errors.",
			map[string]interface{}{"errors": formErrors})
	}
	isMaskNumber := payload.isMaskNumber()
	err = payload.encryptCardNumber()
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error in encrypting card number", nil)
	}

	tx, err := db.Get().Beginx()
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error in initializing traction", nil)
	}
	payload.UpdatedBy = user.ID

	var upInsertQuery string
	query := `select id, name_on_card, card_number, expiration_month, expiration_year, manager_name, manager_phone, manager_fax
		from credit_cards limit 1`
	var dbPayload CardPayload
	err = tx.Get(&dbPayload, query)
	if err != nil {
		if err == sql.ErrNoRows {
			// in case of first time creation, if someone has put mast number, it should not be inter
			if isMaskNumber {
				formErrors["invalid_card_number"] = "Card number is invalid"
				return http.StatusInternalServerError, handlers.ErrorMessage(err,
					"Invalid Credit Card", map[string]interface{}{"errors": formErrors})
			}
			upInsertQuery = `insert into credit_cards (name_on_card, card_number, expiration_month,
				expiration_year, manager_name, manager_phone, manager_fax, updated_by_user_id, updated_at)
			values(:name_on_card, :card_number, :expiration_month, :expiration_year,
				:manager_name, :manager_phone, :manager_fax, :updated_by_user_id, now() at time zone 'utc')`
		} else {
			return http.StatusInternalServerError, handlers.ErrorMessage(err,
				"Error in getting credit card details from db", nil)
		}
	} else {
		upInsertQuery = `update credit_cards set 
			name_on_card = :name_on_card,
			expiration_month = :expiration_month,
			expiration_year = :expiration_year,
			manager_name = :manager_name,
			manager_phone = :manager_phone,
			manager_fax = :manager_fax,
			updated_by_user_id = :updated_by_user_id,
			updated_at = now() at time zone 'utc'`

		// if request send mask number, don't update card number
		if !isMaskNumber {
			upInsertQuery += ", card_number = :card_number;"
		}
	}

	stmt, err := tx.PrepareNamed(upInsertQuery)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err,
			"Error in preparing update/insert statement for credit card", nil)
	}
	_, err = stmt.Exec(&payload)
	if err != nil {
		tx.Rollback()
		return http.StatusInternalServerError, handlers.ErrorMessage(err,
			"Error in updating credit card information", nil)
	}

	err = updateRecordNotes(payload, dbPayload, isMaskNumber, user.ID, tx)
	if err != nil {
		tx.Rollback()
		return http.StatusInternalServerError, handlers.ErrorMessage(err,
			"Error in updating credit card record notes", nil)
	}

	err = tx.Commit()
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error committing transaction for credit card", nil)
	}
	return http.StatusOK, map[string]interface{}{}
}

func updateRecordNotes(payload *CardPayload, dbPayload CardPayload, isMaskNumber bool, userID int, tx *sqlx.Tx) error {
	notes := checkNotes(payload, &dbPayload, isMaskNumber)
	for _, note := range notes {
		_, err := insertCardNote(&cardNotePayload{
			NotesText:       note,
			CreatedByUserID: userID,
		}, tx)
		if err != nil {
			return err
		}
	}
	return nil
}

func cardDetailsFromReq(reqPay *CardPayload, req *http.Request) error {
	dec := json.NewDecoder(req.Body)
	err := dec.Decode(&reqPay)
	return errors.Wrap(err, "decoding credit cared request failed")
}
