package credit

import (
	"net/http"
	"time"

	"phizz/db"
	"phizz/handlers"

	"github.com/jmoiron/sqlx"
	"github.com/pkg/errors"
)

type cardNotePayload struct {
	ID              int       `db:"id"`
	NotesText       string    `db:"notes_text"`
	CreatedAt       time.Time `db:"created_at"`
	CreatedByUserID int       `db:"created_by_user_id"`
}

// insertCardNote inserts card notes
func insertCardNote(recordNote *cardNotePayload, tx *sqlx.Tx) (int, error) {
	insertQuery := `insert into credit_cards_record_notes (created_at, notes_text, created_by_user_id)
			values (now() at time zone 'utc', :notes_text, :created_by_user_id) returning id`
	id := 0

	var stmt *sqlx.NamedStmt
	var err error
	stmt, err = tx.PrepareNamed(insertQuery)
	if err != nil {
		return id, errors.Wrap(err, "An error occurred in PrepareNamed function while adding <PERSON>Note.")
	}

	defer func() { _ = stmt.Close() }()

	err = stmt.Get(&id, recordNote)
	if err != nil {
		return id, errors.Wrap(err, "An error occurred while trying to add the RecordNote to the database.")
	}

	return id, err
}

// RecordNoteIndex returns a list of record notes for credit cards
func RecordNoteIndex(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	var recordNotes []struct {
		NotesText       string    `json:"notes_text" db:"notes_text"`
		CreatedAt       time.Time `json:"created_at" db:"created_at"`
		CreatedByUserID int       `json:"created_by_user_id" db:"created_by_user_id"`
		FirstName       string    `json:"first_name" db:"first_name"`
		LastName        string    `json:"last_name" db:"last_name"`
	}

	listQuery, countQuery := handlers.ListQueries(
		"notes_text, credit_cards_record_notes.created_at, created_by_user_id, first_name, last_name",
		"credit_cards_record_notes, users",
		"where created_by_user_id = users.id",
		"order by created_at desc",
		handlers.PerPageEntries,
		handlers.GetPage(req),
	)

	err := db.Get().Select(&recordNotes, listQuery)
	if err != nil {
		err = errors.Wrap(err, "Database error getting record notes lists for credit card")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting record notes lists data", nil)
	}

	count := 0
	err = db.Get().Get(&count, countQuery)
	if err != nil {
		err = errors.Wrap(err, "Database error getting record notes lists count")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting record notes lists data", nil)
	}
	return http.StatusOK, map[string]interface{}{"record_notes": recordNotes, "count": count}
}
