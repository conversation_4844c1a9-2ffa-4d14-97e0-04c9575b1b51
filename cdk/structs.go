package cdk

type vehicleSales struct {
	VehicleSale  *vehicleSale
	ErrorCode    int
	ErrorMessage string
}

type vehicleSale struct {
	VIN               string
	VehicleMileage    string
	ContractDate      string
	SaleType          string // Type of sale: Cash, Finance, or Lease.
	DealType          string
	Term              string
	APR               string
	PaymentAmt        string
	FirstPayDate      string
	FinanceAmt        string
	CostPrice         string
	MSRP              string
	CustNo            string
	CoCustNo          string
	Name1             string
	ZipOrPostalCode   string
	Address           string
	City              string
	State             string
	HomePhone         string
	BusinessPhone     string
	Email1            string
	FinanceSource     string
	LienHolderName    string
	LienHolderAddress string
	LienHolderCity    string
	LienHolderState   string
	LienHolderZip     string
	LienHolderPhone   string
	MBIName           string
	MBIFee            string
	Insurance3Name    string
	Ins3Fee           string
	Insurance2Name    string
	Ins2Fee           string
	StockNo           string
}

type weOwes struct {
	WeOwe        []weOwe
	ErrorCode    int
	ErrorMessage string
}

type weOwe struct {
	WeOweCode  string
	SaleAmount string
}

type serviceSalesOpen struct {
	ServiceSalesOpen *serviceSales
	ErrorCode        int
	ErrorMessage     string
}

type serviceSalesClosed struct {
	ServiceSalesClosed []serviceSales
	ErrorCode          int
	ErrorMessage       string
}

type serviceSales struct {
	OpenDate       string
	VIN            string
	Mileage        string
	CustNo         string
	HostItemID     string
	ServiceAdvisor int
}

type helpCustomer struct {
	Customer     *customer
	ErrorCode    int
	ErrorMessage string
}

type customer struct {
	CustNo          string
	FirstName       string
	LastName        string
	ZipOrPostalCode string
	Address         string
	City            string
	State           string
	HomePhone       string
	Telephone       string
	Email           string
}

type serviceSalesDetails struct {
	RONumber          string
	ComplaintCode     string
	ServiceRequest    string
	OpCode            string
	OpCodeDescription string
	LineCode          string
	LaborType         string
	TechNo            string
	SoldHours         float64
	LaborSale         float64
	MiscSale          float64
	AddOnFlag         string
	HostItemID        string
}

type story struct {
	HostItemID string
	StoryText  string
}

type serviceSalesParts struct {
	LineCode          string
	List              float64
	Cost              float64
	QuantitySold      int
	Description       string
	PartNumber        string
	PartsExtendedSale float64
	HostItemID        string
}

type serviceSalesClosedDetails struct {
	ServiceSalesDetailsClosed []serviceSalesDetails
	ErrorCode                 int
	ErrorMessage              string
}

type serviceSalesOpenDetails struct {
	ServiceSalesDetailsOpen []serviceSalesDetails
	ErrorCode               int
	ErrorMessage            string
}

type serviceSalesClosedNotes struct {
	Story        []story `xml:"STORY"`
	ErrorCode    int
	ErrorMessage string
}

type serviceSalesClosedParts struct {
	ServiceSalesPartsClosed []serviceSalesParts
	ErrorCode               int
	ErrorMessage            string
}

type serviceSalesOpenParts struct {
	ServiceSalesPartsOpen []serviceSalesParts
	ErrorCode             int
	ErrorMessage          string
}
