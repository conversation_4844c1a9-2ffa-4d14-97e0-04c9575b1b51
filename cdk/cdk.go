package cdk

import (
	"context"
	"encoding/xml"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/url"
	"phizz/conf"
	"sort"
	"strconv"
	"strings"
	"time"

	"phizz/db"
	"phizz/dms"
	"phizz/nr"

	newrelic "github.com/newrelic/go-agent"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
)

const (
	userAgent         = "TCAClient"
	dateFormat        = "2006-01-02"
	notFoundErrorCode = 11500103
)

var (
	weOweCodes = map[string]string{
		"VTR":      "VTR",
		"KEY":      "KEY",
		"TW":       "TW",
		"WC":       "LWT",
		"DRIVEPUR": "DP",
		"CAPP":     "AP",
		"CPDR":     "PDR",
	}
)

type notFoundError struct {
	error
}

func (e notFoundError) IsNotFound() bool {
	return true
}

// Deal fetches the deal from CDK and maps to dms.Deal
func Deal(ctx context.Context, dealerID string, dealNumber string) (*dms.Deal, error) {
	vs, err := fetchDeal(ctx, dealerID, dealNumber)
	if err != nil {
		return nil, errors.Wrap(err, "CDK Fetch Deal error")
	}
	if vs.ErrorCode != 0 {
		if vs.ErrorCode == notFoundErrorCode {
			return nil, notFoundError{
				fmt.Errorf("CDK Deal Lookup error: [%d] %s", vs.ErrorCode, vs.ErrorMessage),
			}
		}
		return nil, fmt.Errorf("CDK Deal Lookup error: [%d] %s", vs.ErrorCode, vs.ErrorMessage)
	}
	if vs.VehicleSale == nil {
		return nil, errors.New("CDK Deal Lookup missing VehicleSale")
	}

	wo, err := fetchWeOwe(ctx, dealerID, dealNumber)
	if err != nil {
		return nil, errors.Wrap(err, "CDK Fetch Deal We Owe error")
	}
	if wo.ErrorCode != 0 {
		return nil, fmt.Errorf("CDK WeOwe Lookup error: [%d] %sn", vs.ErrorCode, vs.ErrorMessage)
	}

	hc, err := fetchCustomer(ctx, dealerID, vs.VehicleSale.CustNo)
	if err != nil {
		return nil, errors.Wrap(err, "CDK Deal Customer Lookup fetch error")
	}
	if hc.ErrorCode != 0 {
		return nil, fmt.Errorf("CDK Deal Customer Lookup response error: [%d] %s", hc.ErrorCode, hc.ErrorMessage)
	}
	if hc.Customer == nil {
		return nil, errors.New("CDK Deal Customer not found")
	}

	var hcc *helpCustomer
	if vs.VehicleSale.CoCustNo != "" {
		hcc, err = fetchCustomer(ctx, dealerID, vs.VehicleSale.CoCustNo)
		if err != nil {
			return nil, errors.Wrap(err, "CDK Deal Customer Lookup fetch error")
		}
		if hcc.ErrorCode != 0 {
			return nil, fmt.Errorf("CDK Deal Customer Lookup response error: [%d] %s", hcc.ErrorCode, hcc.ErrorMessage)
		}
		if hcc.Customer == nil {
			return nil, errors.New("CDK Deal Customer not found")
		}
	}

	if hcc != nil {
		return mapDeal(*vs.VehicleSale, wo.WeOwe, *hc.Customer, hcc.Customer)
	}

	return mapDeal(*vs.VehicleSale, wo.WeOwe, *hc.Customer, nil)
}

var (
	paymentTypeMap = map[string]string{
		"Cash":    dms.PaymentTypeCash,
		"Finance": dms.PaymentTypeLoan,
		"Lease":   dms.PaymentTypeLease,
	}
	vehicleConditionMap = map[string]string{
		"New":  dms.VehicleConditionNew,
		"Used": dms.VehicleConditionUsed,
	}
)

func mapDeal(vs vehicleSale, wo []weOwe, c customer, cc *customer) (*dms.Deal, error) {
	paymentType, ok := paymentTypeMap[vs.SaleType]
	if !ok {
		return nil, fmt.Errorf("Invalid SaleType: %s", vs.SaleType)
	}

	date, err := time.Parse(dateFormat, vs.ContractDate)
	if err != nil {
		return nil, errors.Wrapf(err, "Invalid ContractDate: %s", vs.ContractDate)
	}

	vehicleOdometer, err := strconv.Atoi(vs.VehicleMileage)
	if err != nil {
		return nil, errors.Wrapf(err, "Invalid VehicleMileage: %s", vs.VehicleMileage)
	}

	vehicleCondition, ok := vehicleConditionMap[vs.DealType]
	if !ok {
		return nil, fmt.Errorf("Invalid DealType: %s", vs.DealType)
	}

	vehicleMSRP := decimal.Decimal{}
	if vs.MSRP != "" {
		vehicleMSRP, err = decimal.NewFromString(vs.MSRP)
		if err != nil {
			return nil, errors.Wrapf(err, "Invalid MSRP: %s", vs.MSRP)
		}
	}

	vehiclePrice := decimal.Decimal{}
	if vs.CostPrice != "" {
		vehiclePrice, err = decimal.NewFromString(vs.CostPrice)
		if err != nil {
			return nil, errors.Errorf("Invalid Vehicle Price: %s", vs.CostPrice)
		}
	}

	names := strings.Split(vs.Name1, ",")
	if len(names) != 2 {
		return nil, fmt.Errorf("Invalid Name1: %s", vs.Name1)
	}

	var financeInfo *dms.FinanceInfo
	if paymentType == dms.PaymentTypeLoan || paymentType == dms.PaymentTypeLease {
		term, err := strconv.Atoi(vs.Term)
		if err != nil {
			return nil, errors.Wrapf(err, "Invalid Term: %s", vs.Term)
		}

		apr, err := decimal.NewFromString(vs.APR)
		if err != nil {
			// Just want it to be zero on error
			apr = decimal.Zero
		}

		amount, err := decimal.NewFromString(vs.FinanceAmt)
		if err != nil {
			return nil, errors.Wrapf(err, "Invalid FinanceAmt(: %s", vs.FinanceAmt)
		}

		monthlyPayment, err := decimal.NewFromString(vs.PaymentAmt)
		if err != nil {
			return nil, errors.Wrapf(err, "Invalid PaymentAmt: %s", vs.PaymentAmt)
		}

		firstPaymentDate, err := time.Parse(dateFormat, vs.FirstPayDate)
		if err != nil {
			return nil, errors.Wrapf(err, "Invalid FirstPayDate: %s", vs.FirstPayDate)
		}

		financeInfo = &dms.FinanceInfo{
			Term:             term,
			APR:              apr.Round(2),
			Amount:           amount.Round(2),
			MonthlyPayment:   monthlyPayment.Round(2),
			FirstPaymentDate: dms.JSONDate{Time: firstPaymentDate},
		}
	}

	prices := []dms.Price{}

	if vs.MBIFee != "" {
		price, err := decimal.NewFromString(vs.MBIFee)
		if err != nil {
			return nil, errors.Wrapf(err, "Invalid MBIFee: %s", vs.MBIFee)
		}
		prices = append(prices, dms.Price{
			ProductTypeCode: "VSC",
			Price:           price.Round(2),
		})
	}

	if vs.Ins3Fee != "" {
		price, err := decimal.NewFromString(vs.Ins3Fee)
		if err != nil {
			return nil, errors.Wrapf(err, "Invalid Ins3Fee: %s", vs.Ins3Fee)
		}
		prices = append(prices, dms.Price{
			ProductTypeCode: "MNT",
			Price:           price.Round(2),
		})
	}

	if vs.Ins2Fee != "" {
		price, err := decimal.NewFromString(vs.Ins2Fee)
		if err != nil {
			return nil, errors.Wrapf(err, "Invalid Ins2Fee: %s", vs.Ins2Fee)
		}
		prices = append(prices, dms.Price{
			ProductTypeCode: "GAP",
			Price:           price.Round(2),
		})
	}

	for _, oneWeOwe := range wo {
		code, ok := weOweCodes[oneWeOwe.WeOweCode]
		if !ok {
			continue
		}
		price, err := decimal.NewFromString(oneWeOwe.SaleAmount)
		if err != nil {
			return nil, errors.Wrapf(err, "Invalid WeOwe: %s = %s", code, oneWeOwe.SaleAmount)
		}
		prices = append(prices, dms.Price{
			ProductTypeCode: code,
			Price:           price.Round(2),
		})
	}

	d := &dms.Deal{
		PaymentType: paymentType,
		Date:        dms.JSONDate{Time: date},
		Vehicle: dms.DealVehicle{
			DMSStockNumber: vs.StockNo,
			VIN:            vs.VIN,
			Odometer:       vehicleOdometer,
			Condition:      vehicleCondition,
			MSRP:           vehicleMSRP.Round(2),
			Price:          vehiclePrice.Round(2),
		},
		Customer:    mapCustomer(c),
		FinanceInfo: financeInfo,
		Prices:      prices,
	}

	if cc != nil {
		cocust := mapCustomer(*cc)
		d.CoCustomer = &cocust
	}

	return d, nil
}

func mapCustomer(c customer) dms.Customer {
	phone := c.Telephone
	if phone == "" {
		phone = c.HomePhone
	}
	return dms.Customer{
		DMSCustomerNumber: c.CustNo,
		FirstName:         c.FirstName,
		LastName:          c.LastName,
		Address:           c.Address,
		City:              c.City,
		State:             c.State,
		PostalCode:        c.ZipOrPostalCode,
		Phone:             phone,
		Email:             c.Email,
	}
}

func mapCustomers(hps []helpCustomer, cust string) *dms.Customer {
	for _, hp := range hps {
		if hp.Customer != nil && hp.Customer.CustNo == cust {
			customer := mapCustomer(*hp.Customer)
			return &customer
		}
	}
	return nil
}

func fetchDeal(ctx context.Context, dealerID string, dealNumber string) (*vehicleSales, error) {
	body, err := request(
		ctx,
		db.FISalesOpenPath,
		&url.Values{
			"qparamDealNo": {dealNumber},
			"dealerId":     {dealerID},
			"queryId":      {db.FISalesOpenQuery},
		},
	)
	if err != nil {
		return nil, err
	}

	vs := new(vehicleSales)
	err = xml.Unmarshal(body, &vs)
	if err != nil {
		return nil, err
	}

	return vs, nil
}

func fetchWeOwe(ctx context.Context, dealerID string, dealNumber string) (*weOwes, error) {
	body, err := request(
		ctx,
		db.WeOweExtractByDealNumberPath,
		&url.Values{
			"qparamDealNo": {dealNumber},
			"dealerId":     {dealerID},
			"queryId":      {db.WeOweExtractByDealNumberQuery},
		},
	)
	if err != nil {
		return nil, err
	}

	wo := new(weOwes)
	err = xml.Unmarshal(body, &wo)
	if err != nil {
		return nil, err
	}

	return wo, nil
}

func fetchCustomer(ctx context.Context, dealerID string, customerNumber string) (*helpCustomer, error) {
	body, err := request(
		ctx,
		db.CustomerExtractPath,
		&url.Values{
			"qparamCustNo": {customerNumber},
			"dealerId":     {dealerID},
			"queryId":      {db.CustomerExtractQuery},
		},
	)
	if err != nil {
		return nil, err
	}

	hc := new(helpCustomer)
	err = xml.Unmarshal(body, &hc)
	if err != nil {
		return nil, err
	}

	return hc, nil
}

// RO fetches the RO from CDK and maps to dms.RO
func RO(ctx context.Context, dealerID string, roNumber string) (*dms.RO, error) {
	// Try Open
	sso, err := fetchOpenRO(ctx, dealerID, roNumber)
	if err != nil {
		return nil, errors.Wrap(err, "CDK RO Open Lookup fetch error")
	}
	if sso.ErrorCode != 0 {
		return nil, fmt.Errorf("CDK RO Open Lookup response error: [%d] %s", sso.ErrorCode, sso.ErrorMessage)
	}
	if sso.ServiceSalesOpen != nil {
		hc, err := fetchCustomer(ctx, dealerID, sso.ServiceSalesOpen.CustNo)
		if err != nil {
			return nil, errors.Wrap(err, "CDK RO Open Customer Lookup fetch error")
		}
		if hc.ErrorCode != 0 {
			return nil, fmt.Errorf("CDK RO Open Customer Lookup response error: [%d] %s", hc.ErrorCode, hc.ErrorMessage)
		}
		if hc.Customer == nil {
			return nil, errors.New("CDK RO Open Customer not found")
		}
		return mapRO(*sso.ServiceSalesOpen, *hc.Customer)
	}

	// Try Closed
	ssc, err := fetchClosedRO(ctx, dealerID, roNumber)
	if err != nil {
		return nil, errors.Wrap(err, "CDK RO Closed Lookup fetch error")
	}
	if ssc.ErrorCode != 0 {
		return nil, fmt.Errorf("CDK RO Closed Lookup response error: [%d] %s", ssc.ErrorCode, ssc.ErrorMessage)
	}
	if len(ssc.ServiceSalesClosed) == 0 {
		return nil, notFoundError{
			errors.New("CDK RO Closed Lookup missing VehicleSale"),
		}
	}
	ss := ssc.ServiceSalesClosed[0]
	lastDate, err := time.Parse(dateFormat, ss.OpenDate)
	if err != nil {
		return nil, errors.Wrap(err, "CDK RO Closed Lookup date parse error")
	}
	for _, v := range ssc.ServiceSalesClosed[1:] {
		curDate, err := time.Parse(dateFormat, v.OpenDate)
		if err != nil {
			return nil, errors.Wrap(err, "CDK RO Closed Lookup date parse error")
		}
		if curDate.After(lastDate) {
			ss = v
			lastDate = curDate
		}
	}

	hc := &helpCustomer{
		Customer: &customer{},
	}
	if ss.CustNo != "" {
		hc, err = fetchCustomer(ctx, dealerID, ss.CustNo)
		if err != nil {
			return nil, errors.Wrap(err, "CDK RO Closed Customer Lookup fetch error")
		}
		if hc.ErrorCode != 0 {
			return nil, fmt.Errorf("CDK RO Closed Customer Lookup response error: [%d] %s", hc.ErrorCode, hc.ErrorMessage)
		}
		if hc.Customer == nil {
			return nil, errors.New("CDK RO Closed Customer not found")
		}
	}

	return mapRO(ss, *hc.Customer)
}

// ROCustomer fetches Service Sales Closed RO details from CDK and maps to dms.RO
func ROCustomer(ctx context.Context, dealerID string, roNumber string) (*dms.RO, error) {
	ssc, err := fetchClosedRO(ctx, dealerID, roNumber)
	if err != nil {
		return nil, errors.Wrap(err, "CDK RO Closed Lookup fetch error")
	}
	if ssc.ErrorCode != 0 {
		return nil, fmt.Errorf("CDK RO Closed Lookup response error: [%d] %s", ssc.ErrorCode, ssc.ErrorMessage)
	}
	if len(ssc.ServiceSalesClosed) == 0 {
		return nil, notFoundError{
			errors.New("CDK RO Closed Lookup missing VehicleSale"),
		}
	}
	ss := ssc.ServiceSalesClosed[0]
	lastDate, err := time.Parse(dateFormat, ss.OpenDate)
	if err != nil {
		return nil, errors.Wrap(err, "CDK RO Closed Lookup date parse error")
	}
	for _, v := range ssc.ServiceSalesClosed[1:] {
		curDate, err := time.Parse(dateFormat, v.OpenDate)
		if err != nil {
			return nil, errors.Wrap(err, "CDK RO Closed Lookup date parse error")
		}
		if curDate.After(lastDate) {
			ss = v
			lastDate = curDate
		}
	}

	hc := &helpCustomer{
		Customer: &customer{},
	}
	if ss.CustNo != "" {
		hc, err = fetchCustomer(ctx, dealerID, ss.CustNo)
		if err != nil {
			return nil, errors.Wrap(err, "CDK RO Closed Customer Lookup fetch error")
		}
		if hc.ErrorCode != 0 {
			return nil, fmt.Errorf("CDK RO Closed Customer Lookup response error: [%d] %s", hc.ErrorCode, hc.ErrorMessage)
		}
		if hc.Customer == nil {
			return nil, errors.New("CDK RO Closed Customer not found")
		}
	}

	return mapRO(ss, *hc.Customer)
}

const (
	closedRO        = "SERVICESALESCLOSED"
	closedRODetails = "SERVICESALESDETAILSCLOSED"
	closedROParts   = "SERVICESALESPARTSCLOSED"
	closedROStory   = "STORY"
	openRO          = "SERVICESALESOPEN"
	openRODetails   = "SERVICESALESDETAILSOPEN"
	openROParts     = "SERVICESALESPARTSOPEN"
)

type cdkRequest struct {
	path    string
	params  *url.Values
	cdkType string
}

type cdkResponse struct {
	body    []byte
	err     error
	cdkType string
}

type cdkCustResponse struct {
	helpCustomer *helpCustomer
	err          error
}

var errTimeout = errors.New("Request timeout")

func cdkData(ctx context.Context, dealerID string, roNumber string) ([]*cdkResponse, error) {
	cdkRequests := []cdkRequest{
		{
			path: db.ServiceSalesClosedExtPath,
			params: &url.Values{
				"qparamRONumber": {roNumber},
				"dealerId":       {dealerID},
				"queryId":        {db.ServiceSalesClosedExtQueryByRO},
			},
			cdkType: closedRO,
		},
		{
			path: db.ServiceSalesDetailsClosedExtPath,
			params: &url.Values{
				"qparamRONumber": {roNumber},
				"dealerId":       {dealerID},
				"queryId":        {db.ServiceSalesDetailsClosedExtQueryByRO},
			},
			cdkType: closedRODetails,
		},
		{
			path: db.ServiceSalesPartsClosedExtPath,
			params: &url.Values{
				"qparamRONumber": {roNumber},
				"dealerId":       {dealerID},
				"queryId":        {db.ServiceSalesPartsClosedExtQueryByRO},
			},
			cdkType: closedROParts,
		},
		{
			path: db.ServiceSalesClosedStoryPath,
			params: &url.Values{
				"qparamRONum": {roNumber},
				"dealerId":    {dealerID},
				"queryId":     {db.ServiceSalesClosedStoryQuery},
			},
			cdkType: closedROStory,
		},
		{
			path: db.ServiceSalesOpenExtPath,
			params: &url.Values{
				"qparamRONumber": {roNumber},
				"dealerId":       {dealerID},
				"queryId":        {db.ServiceSalesOpenExtQueryByRO},
			},
			cdkType: openRO,
		},
		{
			path: db.ServiceSalesDetailsOpenExtPath,
			params: &url.Values{
				"qparamRONumber": {roNumber},
				"dealerId":       {dealerID},
				"queryId":        {db.ServiceSalesDetailsOpenExtQueryByRO},
			},
			cdkType: openRODetails,
		},
		{
			path: db.ServiceSalesPartsOpenExtPath,
			params: &url.Values{
				"qparamRONumber": {roNumber},
				"dealerId":       {dealerID},
				"queryId":        {db.ServiceSalesPartsOpenExtQueryByRO},
			},
			cdkType: openROParts,
		},
	}
	ch := make(chan *cdkResponse)
	var responses []*cdkResponse

	for _, req := range cdkRequests {
		go func(req cdkRequest) {
			body, err := request(
				ctx,
				req.path,
				req.params,
			)

			ch <- &cdkResponse{
				body:    body,
				err:     err,
				cdkType: req.cdkType,
			}
		}(req)
	}

	for {
		select {
		case res := <-ch:
			responses = append(responses, res)
			if len(responses) == len(cdkRequests) {
				return responses, nil
			}
		case <-time.After(120 * time.Second):
			return nil, errTimeout
		}
	}
}

func cdkCustomers(ctx context.Context, dealerID string, customers []string) ([]*cdkCustResponse, error) {
	ch := make(chan *cdkCustResponse)
	var responses []*cdkCustResponse
	for _, cust := range customers {
		go func(customerNumber string) {
			hc, err := fetchCustomer(ctx, dealerID, customerNumber)
			ch <- &cdkCustResponse{
				helpCustomer: hc,
				err:          err,
			}
		}(cust)
	}
	for {
		select {
		case res := <-ch:
			responses = append(responses, res)
			if len(responses) == len(customers) {
				return responses, nil
			}
		case <-time.After(120 * time.Second):
			return nil, errTimeout
		}
	}
}

func stringInSlice(str string, list []string) bool {
	for _, v := range list {
		if v == str {
			return true
		}
	}
	return false
}

// RODetail fetches the RO from CDK and maps to dms.RODetail
func RODetail(ctx context.Context, dealerID string, roNumber string) ([]dms.RODetail, error) {
	responses, err := cdkData(ctx, dealerID, roNumber)
	var custResponses []*cdkCustResponse
	if err != nil {
		return nil, errors.Wrap(err, "CDK RO Closed Lookup fetch error")
	}
	var ssc serviceSalesClosed
	var sscd serviceSalesClosedDetails
	var sspc serviceSalesClosedParts
	var sscn serviceSalesClosedNotes
	var sso *serviceSalesOpen
	var ssod serviceSalesOpenDetails
	var ssop serviceSalesOpenParts

	for _, response := range responses {
		if response.err != nil {
			return nil, errors.Wrap(response.err, "CDK RO Closed Lookup fetch error")
		}
		switch response.cdkType {
		case closedRO:
			err = xml.Unmarshal(response.body, &ssc)
			if err != nil {
				return nil, err
			}
			if ssc.ErrorCode != 0 {
				return nil, fmt.Errorf("CDK RO Closed Lookup response error: [%d] %s", ssc.ErrorCode, ssc.ErrorMessage)
			}
			if len(ssc.ServiceSalesClosed) > 0 {
				var customers []string
				for _, serviceSales := range ssc.ServiceSalesClosed {
					if !stringInSlice(serviceSales.CustNo, customers) {
						customers = append(customers, serviceSales.CustNo)
					}
				}
				customerRes, err := cdkCustomers(ctx, dealerID, customers)
				if err != nil {
					return nil, errors.Wrap(err, "CDK RO Closed Customer Lookup fetch error")
				}
				custResponses = append(custResponses, customerRes...)
			}
		case closedRODetails:
			err = xml.Unmarshal(response.body, &sscd)
			if err != nil {
				return nil, err
			}
			if sscd.ErrorCode != 0 {
				return nil, fmt.Errorf("CDK RO Closed Detail Lookup response error: [%d] %s", sscd.ErrorCode, sscd.ErrorMessage)
			}
		case closedROParts:
			err = xml.Unmarshal(response.body, &sspc)
			if err != nil {
				return nil, err
			}
			if sspc.ErrorCode != 0 {
				return nil, fmt.Errorf("CDK RO Closed Parts Lookup response error: [%d] %s", sspc.ErrorCode, sspc.ErrorMessage)
			}
		case closedROStory:
			err = xml.Unmarshal(response.body, &sscn)
			if err != nil {
				return nil, err
			}
			if sscn.ErrorCode != 0 {
				return nil, fmt.Errorf("CDK RO Closed Story Lookup response error: [%d] %s", sscn.ErrorCode, sscn.ErrorMessage)
			}
		case openRODetails:
			err = xml.Unmarshal(response.body, &ssod)
			if err != nil {
				return nil, err
			}
			if sscd.ErrorCode != 0 {
				return nil, fmt.Errorf("CDK RO Open Detail Lookup response error: [%d] %s", ssod.ErrorCode, ssod.ErrorMessage)
			}
		case openRO:
			err = xml.Unmarshal(response.body, &sso)
			if err != nil {
				return nil, err
			}
			if sso.ErrorCode != 0 {
				return nil, fmt.Errorf("CDK RO Open Lookup response error: [%d] %s", ssc.ErrorCode, ssc.ErrorMessage)
			}
			if sso.ServiceSalesOpen != nil {
				hc, err := fetchCustomer(ctx, dealerID, sso.ServiceSalesOpen.CustNo)
				if err != nil {
					return nil, errors.Wrap(err, "CDK RO Open Customer Lookup fetch error")
				}
				if hc.ErrorCode != 0 {
					return nil, fmt.Errorf("CDK RO Open Customer Lookup response error: [%d] %s", hc.ErrorCode, hc.ErrorMessage)
				}
				if hc.Customer == nil {
					return nil, errors.New("CDK RO Open Customer not found")
				}

				var customers []string
				if !stringInSlice(sso.ServiceSalesOpen.CustNo, customers) {
					customers = append(customers, sso.ServiceSalesOpen.CustNo)
				}

				customerRes, err := cdkCustomers(ctx, dealerID, customers)
				if err != nil {
					return nil, errors.Wrap(err, "CDK RO Open Customer Lookup fetch error")
				}
				custResponses = append(custResponses, customerRes...)
			}
		case openROParts:
			err = xml.Unmarshal(response.body, &ssop)
			if err != nil {
				return nil, err
			}
			if sspc.ErrorCode != 0 {
				return nil, fmt.Errorf("CDK RO Open Parts Lookup response error: [%d] %s", ssop.ErrorCode, ssop.ErrorMessage)
			}
		}
	}
	var hcs []helpCustomer
	for _, custResp := range custResponses {
		if custResp.err != nil {
			return nil, errors.Wrap(err, "CDK RO Closed Customer Lookup fetch error")
		}
		if custResp.helpCustomer.ErrorCode != 0 {
			return nil, fmt.Errorf("CDK RO Closed Customer Lookup response error: [%d] %s", custResp.helpCustomer.ErrorCode, custResp.helpCustomer.ErrorMessage)
		}
		hcs = append(hcs, *custResp.helpCustomer)
	}
	return mapRODetails(ssc, &sscd, &sspc, &sscn, sso, &ssod, &ssop, hcs, roNumber)
}

func mapRO(ss serviceSales, c customer) (*dms.RO, error) {
	date, err := time.Parse(dateFormat, ss.OpenDate)
	if err != nil {
		return nil, errors.Wrapf(err, "Invalid OpenDate: %s", ss.OpenDate)
	}

	vehicleOdometer, err := strconv.Atoi(ss.Mileage)
	if err != nil {
		return nil, errors.Wrapf(err, "Invalid Mileage: %s", ss.Mileage)
	}

	return &dms.RO{
		Date: dms.JSONDate{Time: date},
		Vehicle: dms.ROVehicle{
			VIN:      ss.VIN,
			Odometer: vehicleOdometer,
		},
		Customer: mapCustomer(c),
	}, nil
}

func mapRODetails(ssc serviceSalesClosed, sscd *serviceSalesClosedDetails, sscp *serviceSalesClosedParts,
	sscn *serviceSalesClosedNotes, sso *serviceSalesOpen, ssod *serviceSalesOpenDetails, ssop *serviceSalesOpenParts, hcs []helpCustomer, ro string) ([]dms.RODetail, error) {
	var roDetails []dms.RODetail
	for _, ss := range ssc.ServiceSalesClosed {
		date, err := time.Parse(dateFormat, ss.OpenDate)
		if err != nil {
			return nil, errors.Wrapf(err, "Invalid OpenDate: %s", ss.OpenDate)
		}

		vehicleOdometer, err := strconv.Atoi(ss.Mileage)
		if err != nil {
			return nil, errors.Wrapf(err, "Invalid Mileage: %s", ss.Mileage)
		}
		customer := mapCustomers(hcs, ss.CustNo)
		if customer == nil {
			return nil, errors.New("Customer not found")
		}
		roDetails = append(roDetails, dms.RODetail{
			Date: date,
			Vehicle: dms.ROVehicle{
				VIN:      ss.VIN,
				Odometer: vehicleOdometer,
			},
			ROLines:        mapROLines(ss, sscd, sscp, sscn, ro),
			Customer:       *customer,
			ServiceAdvisor: strconv.Itoa(ss.ServiceAdvisor),
		})
	}

	// We want to get open RO details along with closed RO details if both of them exists
	if sso != nil && sso.ServiceSalesOpen != nil {
		date, err := time.Parse(dateFormat, sso.ServiceSalesOpen.OpenDate)
		if err != nil {
			return nil, errors.Wrapf(err, "Invalid OpenDate: %s", sso.ServiceSalesOpen.OpenDate)
		}
		vehicleOdometer, err := strconv.Atoi(sso.ServiceSalesOpen.Mileage)
		if err != nil {
			return nil, errors.Wrapf(err, "Invalid Mileage: %s", sso.ServiceSalesOpen.Mileage)
		}
		customer := mapCustomers(hcs, sso.ServiceSalesOpen.CustNo)
		if customer == nil {
			return nil, errors.New("Customer not found")
		}
		roDetails = append(roDetails, dms.RODetail{
			Date: date,
			Vehicle: dms.ROVehicle{
				VIN:      sso.ServiceSalesOpen.VIN,
				Odometer: vehicleOdometer,
			},
			ROLines:        mapOpenROLines(sso.ServiceSalesOpen, ssod, ssop),
			Customer:       *customer,
			ServiceAdvisor: strconv.Itoa(sso.ServiceSalesOpen.ServiceAdvisor),
		})
	}
	return roDetails, nil
}

func mapOpenROLines(ss *serviceSales, ssod *serviceSalesOpenDetails, ssop *serviceSalesOpenParts) []*dms.ROLine {
	var rolines map[string]*dms.ROLine
	var lineCodes []string
	var sortedROLines []*dms.ROLine
	rolines = make(map[string]*dms.ROLine)
	for _, service := range ssod.ServiceSalesDetailsOpen {
		if !strings.Contains(service.HostItemID, ss.HostItemID) {
			continue
		}
		rolabor := dms.ROLabor{
			SoldHours: decimal.NewFromFloat(service.SoldHours),
			LaborSale: decimal.NewFromFloat(service.LaborSale),
			MiscSale:  decimal.NewFromFloat(service.MiscSale),
			LaborType: service.LaborType,
		}
		if _, ok := rolines[service.LineCode]; !ok {
			lineCodes = append(lineCodes, service.LineCode)
			rolines[service.LineCode] = &dms.ROLine{LineCode: service.LineCode}
		}
		rolines[service.LineCode].ComplaintCode = service.ComplaintCode
		if rolines[service.LineCode].ComplaintDescription == "" {
			rolines[service.LineCode].ComplaintDescription = service.ServiceRequest
		}
		// if complaint has technician id, don't update tech id again
		if rolines[service.LineCode].TechID == "" {
			rolines[service.LineCode].TechID = service.TechNo
		}

		// if any of the AddOnFlag is Y, make that complaint as AddOnFlag -- true
		if rolines[service.LineCode].AddOnFlag != "Y" {
			rolines[service.LineCode].AddOnFlag = service.AddOnFlag
		}
		rolines[service.LineCode].Labors = append(rolines[service.LineCode].Labors, rolabor)
		rolines[service.LineCode].LaborTypes = append(rolines[service.LineCode].LaborTypes, service.LaborType)

		// TODO: There is no CDK API endpoint for open story, so not sure what would be the correction
		rolines[service.LineCode].Correction = ""
	}

	for _, part := range ssop.ServiceSalesPartsOpen {
		if !strings.Contains(part.HostItemID, ss.HostItemID) {
			continue
		}
		roPart := dms.ROPart{
			Cost:        decimal.NewFromFloat(part.Cost),
			List:        decimal.NewFromFloat(part.List),
			Quantity:    part.QuantitySold,
			PartNumber:  part.PartNumber,
			Description: part.Description,
			PartsSale:   decimal.NewFromFloat(part.PartsExtendedSale),
		}
		rolines[part.LineCode].Parts = append(rolines[part.LineCode].Parts, roPart)
	}

	// sort ROLines by line code
	sort.Strings(lineCodes)
	for _, lineCode := range lineCodes {
		sortedROLines = append(sortedROLines, rolines[lineCode])
	}
	return sortedROLines
}

func correction(sscn *serviceSalesClosedNotes, lineCode string, roNum string) string {
	prefix := fmt.Sprintf("%s*%s*", roNum, lineCode)
	var stories []string
	for _, note := range sscn.Story {
		if strings.HasPrefix(note.HostItemID, prefix) {
			stories = append(stories, note.StoryText)
		}
	}
	return strings.Join(stories, " ")
}

func mapROLines(ss serviceSales, sscd *serviceSalesClosedDetails, sscp *serviceSalesClosedParts, sscn *serviceSalesClosedNotes, ro string) []*dms.ROLine {
	var rolines map[string]*dms.ROLine
	var lineCodes []string
	var sortedROLines []*dms.ROLine
	rolines = make(map[string]*dms.ROLine)
	for _, service := range sscd.ServiceSalesDetailsClosed {
		if !strings.Contains(service.HostItemID, ss.HostItemID) {
			continue
		}

		rolabor := dms.ROLabor{
			SoldHours: decimal.NewFromFloat(service.SoldHours),
			LaborSale: decimal.NewFromFloat(service.LaborSale),
			MiscSale:  decimal.NewFromFloat(service.MiscSale),
			LaborType: service.LaborType,
		}
		if _, ok := rolines[service.LineCode]; !ok {
			lineCodes = append(lineCodes, service.LineCode)
			rolines[service.LineCode] = &dms.ROLine{LineCode: service.LineCode}
		}
		rolines[service.LineCode].ComplaintCode = service.ComplaintCode
		rolines[service.LineCode].OpCode = service.OpCode
		if rolines[service.LineCode].ComplaintDescription == "" {
			rolines[service.LineCode].ComplaintDescription = service.ServiceRequest
		}
		// if complaint has technician id, don't update tech id again
		if rolines[service.LineCode].TechID == "" {
			rolines[service.LineCode].TechID = service.TechNo
		}

		// if any of the AddOnFlag is Y, make that complaint as AddOnFlag -- true
		if rolines[service.LineCode].AddOnFlag != "Y" {
			rolines[service.LineCode].AddOnFlag = service.AddOnFlag
		}
		rolines[service.LineCode].Labors = append(rolines[service.LineCode].Labors, rolabor)
		rolines[service.LineCode].LaborTypes = append(rolines[service.LineCode].LaborTypes, service.LaborType)
		if rolines[service.LineCode].Correction == "" {
			rolines[service.LineCode].Correction = correction(sscn, service.LineCode, ro)
		}
	}

	for _, part := range sscp.ServiceSalesPartsClosed {
		if !strings.Contains(part.HostItemID, ss.HostItemID) {
			continue
		}
		roPart := dms.ROPart{
			Cost:        decimal.NewFromFloat(part.Cost),
			List:        decimal.NewFromFloat(part.List),
			Quantity:    part.QuantitySold,
			PartNumber:  part.PartNumber,
			Description: part.Description,
			PartsSale:   decimal.NewFromFloat(part.PartsExtendedSale),
		}
		rolines[part.LineCode].Parts = append(rolines[part.LineCode].Parts, roPart)
	}
	// sort ROLines by line code
	sort.Strings(lineCodes)
	for _, lineCode := range lineCodes {
		sortedROLines = append(sortedROLines, rolines[lineCode])
	}
	return sortedROLines
}

func fetchOpenRO(ctx context.Context, dealerID string, roNumber string) (*serviceSalesOpen, error) {
	body, err := request(
		ctx,
		db.ServiceSalesOpenExtPath,
		&url.Values{
			"qparamRONumber": {roNumber},
			"dealerId":       {dealerID},
			"queryId":        {db.ServiceSalesOpenExtQueryByRO},
		},
	)
	if err != nil {
		return nil, err
	}

	sso := new(serviceSalesOpen)
	err = xml.Unmarshal(body, &sso)
	if err != nil {
		return nil, err
	}

	return sso, nil
}

func fetchClosedRO(ctx context.Context, dealerID string, roNumber string) (*serviceSalesClosed, error) {
	body, err := request(
		ctx,
		db.ServiceSalesClosedExtPath,
		&url.Values{
			"qparamRONumber": {roNumber},
			"dealerId":       {dealerID},
			"queryId":        {db.ServiceSalesClosedExtQueryByRO},
		},
	)
	if err != nil {
		return nil, err
	}

	ssc := new(serviceSalesClosed)
	err = xml.Unmarshal(body, &ssc)
	if err != nil {
		return nil, err
	}

	return ssc, nil
}

var request = func(ctx context.Context, path string, params *url.Values) ([]byte, error) {
	txn := newrelic.FromContext(ctx)
	conf := conf.Get()
	url := "https://" + conf.CDK.Host + path
	if conf.CDK.Log {
		log.Println("[DMS-CDK] request URL:", url, "METHOD: POST PARAMS:", params, "BASIC-AUTH USERNAME:", conf.CDK.Username)
	}
	body := strings.NewReader(params.Encode())
	req, err := http.NewRequest("POST", url, body)
	if err != nil {
		return nil, err
	}
	req.SetBasicAuth(conf.CDK.Username, conf.CDK.Password)
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("User-Agent", userAgent)

	client := http.Client{Timeout: time.Second * 60}

	resp, err := nr.External(txn, client, req)
	if err != nil {
		return nil, err
	}
	defer func() { _ = resp.Body.Close() }()
	bodyBytes := make([]byte, 102400) // 100KB buffer for response
	_, err = io.ReadFull(resp.Body, bodyBytes)
	if err != nil && err != io.EOF && err != io.ErrUnexpectedEOF {
		return nil, err
	}
	if conf.CDK.Log {
		log.Println("[DMS-CDK] RESPONSE CODE:", resp.StatusCode, "RESPONSE BODY: ", string(bodyBytes))
	}
	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("CDK bad response status: %s", resp.Status)
	}

	return bodyBytes, nil
}
