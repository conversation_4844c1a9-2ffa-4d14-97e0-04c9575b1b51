module phizz

go 1.16

require (
	github.com/BurntSushi/toml v0.3.1 // indirect
	github.com/DATA-DOG/go-sqlmock v1.2.0
	github.com/DavidHuie/gomigrate v0.0.0-20160809001028-4004e6142040
	github.com/aws/aws-sdk-go-v2 v1.16.16
	github.com/aws/aws-sdk-go-v2/config v1.17.7
	github.com/aws/aws-sdk-go-v2/service/s3 v1.27.11
	github.com/aws/smithy-go v1.13.3
	github.com/chmike/securecookie v1.3.2
	github.com/eknkc/amber v0.0.0-20171010120322-cdade1c07385 // indirect
	github.com/fatih/camelcase v0.0.0-20160318181535-f6a740d52f96 // indirect
	github.com/fatih/structs v1.1.0 // indirect
	github.com/go-chi/chi v3.3.2+incompatible
	github.com/go-sql-driver/mysql v1.6.0 // indirect
	github.com/google/uuid v1.6.0
	github.com/gorilla/context v0.0.0-20160226214623-1ea25387ff6f // indirect
	github.com/gorilla/csrf v1.0.3-0.20170912155303-8aae08ff9fb9
	github.com/jmoiron/sqlx v1.3.4
	github.com/jordan-wright/email v0.0.0-20180115032944-94ae17dedda2
	github.com/jung-kurt/gofpdf v1.0.0
	github.com/koding/multiconfig v0.0.0-20171124222453-69c27309b2d7
	github.com/lib/pq v1.2.0
	github.com/mattn/go-sqlite3 v1.14.8 // indirect
	github.com/newrelic/go-agent v2.16.3+incompatible
	github.com/pkg/errors v0.8.1
	github.com/shopspring/decimal v0.0.0-20170728191253-3c692774ac4c
	github.com/stretchr/testify v1.8.2
	github.com/stvp/rollbar v0.5.1
	golang.org/x/crypto v0.13.0
	golang.org/x/lint v0.0.0-20210508222113-6edffad5e616
	golang.org/x/tools v0.13.0 // indirect
	gopkg.in/guregu/null.v3 v3.3.0
	gopkg.in/tylerb/graceful.v1 v1.2.15
	gopkg.in/unrolled/render.v1 v1.0.0-20171102162132-65450fb6b2d3
)
