package unidata

import (
	"time"

	"gopkg.in/guregu/null.v3"

	"github.com/lib/pq"
	"github.com/shopspring/decimal"
)

// ClaimHistoryPayload payload for contract claim history
type ClaimHistoryPayload struct {
	Date               pq.NullTime     `json:"date" db:"date"`
	Miles              string          `json:"miles" db:"miles"`
	Service            null.String     `json:"service" db:"service"`
	RO                 string          `json:"ro" db:"ro"`
	FailedComponents   string          `json:"failed_components" db:"failed_components"`
	Amount             decimal.Decimal `json:"amount" db:"amount"`
	Advisor            string          `json:"advisor" db:"advisor"`
	Status             string          `json:"status" db:"status"`
	PayType            string          `json:"pay_type" db:"pay_type"`
	PaymentDates       []time.Time     `json:"payment_dates" db:"payment_dates"`
	FacilityCode       null.String     `json:"facility_code" db:"facility_code"`
	AuthorizedAmount   decimal.Decimal `json:"authorized_amount" db:"-"`
	AuthorizationDate  pq.NullTime     `json:"authorization_date" db:"authorization_date"`
	CheckNumber        string          `json:"check_number" db:"check_number"`
	ClaimID            int             `json:"claim_id" db:"claim_id"`
	UnidataClaimNumber int             `json:"unidata_claim_number" db:"unidata_claim_number"`
	ChargeBack         bool            `json:"chargeback" db:"chargeback"`
	ParentClaimID      null.Int        `json:"parent_claim_id" db:"parent_claim_id"`
}

// ClaimHistoryList list of claim history
type ClaimHistoryList []ClaimHistoryPayload
