version: 2.1
orbs:
  slack: circleci/slack@4.12.5
jobs:
  build-go:
    docker:
      - image: cimg/go:1.20
    working_directory: "/home/<USER>/go/src/phizz"
    environment:
      GOOS: "linux"
      GOARCH: "amd64"
      OUTPUT_DIR: "/tmp/output"
      ARTIFACTS_DIR: "deploy"
    steps:
      - slack/notify:
          event: always
          template: SLACK_BUILD_START_TEMPLATE
      - checkout
      - run: mkdir -p "/home/<USER>/go/pkg"
      - restore_cache:
          name: Restore Go Cache
          keys:
            - v1-go-mod-phizz-{{ .Branch }}-{{ checksum "go.sum" }}
            - v1-go-mod-phizz-{{ .Branch }}
            - v1-go-mod-phizz
      - run:
          name: Download go modules in to modules cache
          command: go mod download

      - run:
          name: Lint
          command: .circleci/check-linting.sh
      - run:
          name: Check Format
          command: .circleci/check-fmt.sh
      - run: 
          name: Run Tests
          command: CI=true go test -v ./...

      - run:
          name: Build Binaries
          command: |
            GOOS=${GOOS} GOARCH=${GOARCH} go build -o ${OUTPUT_DIR}/phizz
            GOOS=${GOOS} GOARCH=${GOARCH} go build -o ${OUTPUT_DIR}/dpmigrate cmd/dpmigrate/main.go
      
      - save_cache:
          name: Save Go Cache
          key: v1-go-mod-phizz-{{ .Branch }}-{{ checksum "go.sum" }}
          paths:
            - "/home/<USER>/go/pkg"

      - run:
          name: Creating deploy folder
          command: |
            rm -rf ${ARTIFACTS_DIR}
            mkdir -p ${ARTIFACTS_DIR}
      - run:
          name: Copy artifacts
          command: |
            echo "Copying binaries"
            cp -r ${OUTPUT_DIR}/* ${ARTIFACTS_DIR}
            echo "Copying migrations"
            cp -r migrations ${ARTIFACTS_DIR}/migrations
      - run:
          name: Package artifacts
          command: tar -cvzf go_artifacts.tzf ${ARTIFACTS_DIR}
      - store_artifacts:
          path: go_artifacts.tzf
     
      # Slack Notitfications
      - slack/notify:
          event: fail
          mentions: '@marc, @uttam'
          template: SLACK_FAILED_TEMPLATE
      - slack/notify:
          event: pass
          template: SLACK_SUCCEED_TEMPLATE

  build-node:
    docker:
      - image: cimg/node:16.14
    working_directory: ~/phizz
    environment:
      ARTIFACTS_DIR: "deploy"
    steps:
      - slack/notify:
          event: always
          template: SLACK_BUILD_START_TEMPLATE
      - checkout
      - restore_cache:
          name: Restore Yarn Package Cache
          keys:
            - v1-yarn-packages-phizz-{{ checksum "yarn.lock" }}
            - v1-yarn-packages-phizz-{{ .Branch }}
            - v1-yarn-packages-phizz
      - run:
          name: Install Dependencies
          command: yarn install --frozen-lockfile
      - save_cache:
          name: Save Yarn Package Cache
          key: v1-yarn-packages-phizz-{{ .Branch }}-{{ checksum "yarn.lock" }}
          paths:
            - ~/.cache/yarn
      - run: npx eslint --ext=js,jsx assets/js

      - run:
          name: Creating deploy folder
          command: |
            rm -rf deploy
            mkdir -p deploy
      - run: npx webpack build --env prod --env min
      - run:
          name: Copy artifacts
          command: cp -r public ${ARTIFACTS_DIR}/public
      - run:
          name: Compress artifacts
          command: tar -cvzf node_artifacts.tzf ${ARTIFACTS_DIR}
      - store_artifacts:
          path: node_artifacts.tzf

      # Slack Notitfications
      - slack/notify:
          event: fail
          mentions: '@marc, @uttam'
          template: SLACK_FAILED_TEMPLATE
      - slack/notify:
          event: pass
          template: SLACK_SUCCEED_TEMPLATE


workflows:
  ci:
    jobs:
      - build-go:
          context:
            - slack
      - build-node:
          context:
            - slack