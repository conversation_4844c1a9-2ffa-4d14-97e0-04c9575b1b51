#!/usr/bin/env bash

set -euo pipefail

go install golang.org/x/lint/golint

go list ./... | grep -v /vendor/ | xargs -L1 golint -set_exit_status

# TODO: start using golangci-lint for additional static analysis
# # Get the the version of golangci-lint if it is installed
# installed_version=""
# if command -v golangci-lint >/dev/null 2>&1
# then
#     installed_version=$(golangci-lint --version)
# fi


# # Install the correct version of golangci-lint if the correct version is not already installed
# golangci_lint_version=1.24.0
# if [[ ! $installed_version =~ ${golangci_lint_version} ]]
# then
#     curl -sSfL https://raw.githubusercontent.com/golangci/golangci-lint/master/install.sh | sh -s -- -b $(go env GOPATH)/bin v${golangci_lint_version}
# fi

# # Only check for lint errors introduced in the latest commit
# golangci-lint run --new-from-rev=HEAD~1