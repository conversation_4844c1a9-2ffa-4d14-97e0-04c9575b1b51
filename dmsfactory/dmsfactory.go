package dmsfactory

import (
	"context"
	"phizz/cdk"
	"phizz/db"
	"phizz/dms"
	"phizz/tekion"
	"phizz/ucs"

	"github.com/pkg/errors"
)

type notFoundError interface {
	error
	IsNotFound() bool
}

type userError interface {
	error
	UserErrorMessage() string
}

// RODetail will look for a RO given the store and the RO number.
func RODetail(ctx context.Context, store *db.Store, roNumber string) ([]dms.RODetail, error) {
	var roDetails []dms.RODetail
	var err error
	switch store.DMSProvider {
	case db.DMSProviderCDK:
		roDetails, err = cdk.RODetail(ctx, store.DMSProviderParameters.Map["dealer_id"].String, roNumber)
	case db.DMSProviderUCS:
		roDetails, err = ucs.RODetail(ctx, store.DMSProviderParameters.Map["dealer_number"].String, store.DMSProviderParameters.Map["store_number"].String, store.DMSProviderParameters.Map["area_number"].String, roNumber)
	case db.DMSProviderTekion:
		roDetails, err = tekion.RODetail(ctx, store.DMSProviderParameters.Map["dealer_id"].String, roNumber)
	}
	if err != nil {
		if nfErr, ok := err.(notFoundError); ok && nfErr.IsNotFound() {
			return roDetails, errors.Wrap(err, "RO Number not found")
		}
		if userErr, ok := err.(userError); ok {
			return roDetails, errors.Wrap(err, userErr.UserErrorMessage())
		}
		return roDetails, errors.Wrap(err, "RO Number not found")
	}

	return roDetails, nil
}

// ROCustomer will look for an RO with customer data given the store and the RO number.
func ROCustomer(ctx context.Context, store *db.Store, roNumber string) (*dms.RO, error) {
	var ro *dms.RO
	var err error
	switch store.DMSProvider {
	case db.DMSProviderCDK:
		ro, err = cdk.ROCustomer(ctx, store.DMSProviderParameters.Map["dealer_id"].String, roNumber)
	case db.DMSProviderUCS:
		ro, err = ucs.ROCustomer(ctx, store.DMSProviderParameters.Map["dealer_number"].String, store.DMSProviderParameters.Map["store_number"].String, store.DMSProviderParameters.Map["area_number"].String, roNumber)
	case db.DMSProviderTekion:
		ro, err = tekion.ROCustomer(ctx, store.DMSProviderParameters.Map["dealer_id"].String, roNumber)
	}
	if err != nil {
		if nfErr, ok := err.(notFoundError); ok && nfErr.IsNotFound() {
			return ro, errors.Wrap(err, "RO Number not found")
		}
		if userErr, ok := err.(userError); ok {
			return ro, errors.Wrap(err, userErr.UserErrorMessage())
		}
		return ro, errors.Wrap(err, "RO Number not found")
	}
	return ro, nil
}
