package conf

import (
	"fmt"
	"net/smtp"
	"time"

	"github.com/koding/multiconfig"
)

// ExternalAPILogLevel controls how much logging should be done for third party interactions with connect for requests and responses
type ExternalAPILogLevel int

// Defines the different logging levels for third party interactions with connect
const (
	// No logging
	LogLevelNone ExternalAPILogLevel = iota
	// Log all request bodies and only response bodies when external interaction with connect reports an error in the request/response body
	// Does not log response bodies for successful responses.
	LogLevelRequestAndError
	// Log all request and response bodies
	LogLevelFull
)

// App is the root-level configuration data for the app
type App struct {
	Database                  DB                    `required:"true"`
	CookieStoreAuthKey        string                `required:"true"`
	CookieStoreEncKey         string                `required:"true"`
	CSRF                      CSRF                  `required:"true"`
	AppEnv                    string                `default:"development"`
	S3Bucket                  string                `required:"true"`
	AppURL                    string                `required:"true"`
	RollbarToken              string                `required:"true"`
	Email                     Email                 `required:"true"`
	GoogleAnalyticsTrackingID string                `required:"true"`
	Unidata                   Unidata               `required:"true"`
	Intacct                   Intacct               `required:"true"`
	IntacctGap                IntacctAccountDetails `required:"true"`
	IntacctService            IntacctAccountDetails `required:"true"`
	IntacctTireWheel          IntacctAccountDetails `required:"true"`
	IntacctMaintenance        IntacctAccountDetails `required:"true"`
	IntacctAPP                IntacctAccountDetails `required:"true"`
	IntacctPDR                IntacctAccountDetails `required:"true"`
	IntacctKey                IntacctAccountDetails `required:"true"`
	IntacctDrivePur           IntacctAccountDetails `required:"true"`
	IntacctVTA                IntacctAccountDetails `required:"true"`
	IntacctLWT                IntacctAccountDetails `required:"true"`
	AWS                       AWS                   `required:"false"`
	CDK                       CDK                   `required:"true"`
	AutoEmail                 AutoEmail             `required:"true"`
	GapEmail                  GapEmail              `required:"true"`
	LWTEmail                  LWTEmail              `required:"true"`
	AutoClaims                AutoClaims            `required:"true"`
	AuthSalt                  string                `required:"true"`
	WebPackDev                WebPackDev
	HTTPSRedirector           bool
	NewRelic                  newRelic
	UCS                       struct {
		Log      bool `default:"false"`
		LogLevel ExternalAPILogLevel
		Host     string `required:"true"`
		Username string `required:"true"`
		Password string `required:"true"`
	}
	Whiz struct {
		Log              bool
		BaseURL          string `required:"true"`
		AuthSalt         string `required:"true"`
		ConnectionString string `required:"false"`
	} `required:"true"`
	Session struct {
		Secure bool `default:"true"`
	}
	S3ReverseProxy struct {
		EncKey                    string        `required:"true"`
		PathPrefix                string        `required:"true"`
		DefaultLinkTimeoutMinutes time.Duration `required:"true"`
	}
	Tekion struct {
		LogLevel  ExternalAPILogLevel
		Host      string `required:"true"`
		AccessKey string `required:"true"`
		SecretKey string `required:"true"`
		ClientID  string `required:"true"`
	}
}

// WebPackDev is configuration for webpack dev server
// This is only needed for development
type WebPackDev struct {
	BaseURL string `default:"http://localhost:8081"`
}

// DB contains the configuration data for the database
type DB struct {
	ConnectionString string `required:"true"`
	Debug            bool
}

// Email contains the configuration data for sending Email
type Email struct {
	UseLog   bool
	Host     string `required:"true"`
	Port     int    `required:"true"`
	Username string `required:"true"`
	Password string `required:"true"`
}

// GapEmail contains the configuration data for sending Email
type GapEmail struct {
	From string `default:"<EMAIL>"`
}

// AutoEmail contains the configuration data for sending Email
type AutoEmail struct {
	From            string   `default:"<EMAIL>"`
	FacilityManager []string `required:"true"`
}

// LWTEmail contains the configuration data for sending Email
type LWTEmail struct {
	From string `default:"<EMAIL>"`
}

// Server returns the Email confiugration host and port combined in one string
func (e *Email) Server() string {
	return fmt.Sprintf("%s:%d", e.Host, e.Port)
}

// SMTPAuth returns a PlainAuth from the Email configuration data
func (e *Email) SMTPAuth() smtp.Auth {
	return smtp.PlainAuth("", e.Username, e.Password, e.Host)
}

// CSRF contains the configuration for CSRF protection
type CSRF struct {
	Key    string `required:"true"`
	Secure bool
	MaxAge int `default:"43200"` // 12 hours
}

// Unidata contains the configuration for Unidata host server
type Unidata struct {
	Host string `required:"true"`
}

// Intacct contains the configuration for Intacct server authentication
type Intacct struct {
	Host           string `required:"true"`
	SenderID       string `required:"true"`
	SenderPassword string `required:"true"`
	UserID         string `required:"true"`
	Password       string `required:"true"`
	CompanyID      string `required:"true"`
	Log            bool
}

// IntacctAccountDetails contains the configuration for Intacct server account details
type IntacctAccountDetails struct {
	AccountLabel string `required:"true"`
	LocationID   int    `required:"true"`
	ProjectID    string `required:"true"`
	TermName     string `required:"true"`
	BankID       string `required:"true"`
}

// AWS contains the configuration data for AWS
type AWS struct {
	MaxSize int `default:"********"` // 30MiB 1024 * 1024 * 30
}

// CDK contains the configuration data for CDK
type CDK struct {
	Log      bool
	Host     string `required:"true"`
	Username string `required:"true"`
	Password string `required:"true"`
}

// AutoClaims contains credit card threshold value
type AutoClaims struct {
	CCThreshold float64 `required:"true"`
}

type newRelic struct {
	Enabled bool
	Token   string
}

var app *App

// Get either returns an already-loaded configuration or loads config.toml
var Get = func() *App {
	if app == nil {
		appConf := new(App)
		confLoader := multiconfig.DefaultLoader{
			Loader: multiconfig.MultiLoader(
				&multiconfig.TagLoader{},
				&multiconfig.TOMLLoader{Path: "config.toml"},
				&multiconfig.EnvironmentLoader{},
			),
			Validator: multiconfig.MultiValidator(&multiconfig.RequiredValidator{}),
		}
		confLoader.MustLoad(appConf)
		app = appConf
	}

	return app
}
