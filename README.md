# phizz

## Prerequisites

- Go (1.7+)
- NodeJS
- PostgreSQL

## Clone

    $ git clone  **************:Asbury-IT/phizz.git $GOPATH/src/phizz

## Config

On first clone, copy the sample config file:

    $ cp config-sample.toml config.toml

The `config.toml` needs to match the struct in [conf](conf/conf.go).

You may have to change the connection string in config.toml as 

connectionstring="user=phizz dbname=phizz sslmode=disable"

## PSQL 

The server should be run using

    $ postgres -D /usr/local/var/postgres

Create DB and user phizz

    $ createdb phizz

    $ createuser phizz

If there is an error related to hstore, you need to create using

    $ psql phizz -c 'create extension hstore;'


## Dev Server

 To run the web server:

     $ go run main.go
Add a comment to this line

 For live-reloading the Go web application, realize works well:

     $ go get -u -v github.com/tockins/realize

Add a comment to this line
 Run the following command to run the dev web server. Accessible at http://localhost:3000

     $ realize run

## wkhtmltopdf
Install __wkhtmltopdf__ from site [wkhtmltopdf](https://wkhtmltopdf.org/), 
it will be required to convert html to pdf

## Dep

We use Dep for our vendor management. To install: https://github.com/golang/dep

## Database Migrations

We use gomigrate for our schema migration.
The [migrations](migrations) directory will hold the up and down migrations.
The pattern for the migration files is `{{ id }}_{{ name }}_{{ "up" or "down" }}.sql` (ref: https://github.com/DavidHuie/gomigrate#migration-files).
Use a `yyyymmddhhmmss` timestamp for the `id`.

To run all migrations (i.e. up):

    $ go run cmd/dpmigrate/main.go

To rollback the most recent migration executed (i.e. down):

    $ go run cmd/dpmigrate/main.go rollback

## Assets

Install all dependent node modules and globally install webpack for command-line use.

    $ npm install
    $ npm install -g webpack

By default, the Webpack Dev Server is expected to be used.

    $ npm install -g webpack-dev-server
    $ webpack-dev-server

If you are not interested in using Webpack Dev Server, you will need to override `config.toml`:

```
[webpackdev]
baseurl="http://localhost:3000"
```