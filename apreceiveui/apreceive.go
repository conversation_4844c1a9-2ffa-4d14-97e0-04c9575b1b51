package apreceiveui

import (
	"log"
	"net/http"

	"phizz/auto"
	"phizz/db"
	"phizz/gap"
	"phizz/lwt"
	"phizz/vta"
)

// APReceive runs apreceive for claims
func APReceive(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	ctx := req.Context()
	var errs []string
	gapCount, err := gap.ReceiveFromIntacct(ctx)
	if err != nil {
		log.Printf("Error in executing ReceiveFromIntacct for GAP claims: %+v\n", err)
		errs = append(errs, err.Error())
	}

	var autoCount, autoMaintCount, lwtCount, vtaCount int

	autoCount, err = auto.ReceiveFromIntacct(ctx)
	if err != nil {
		log.Printf("Error in executing ReceiveFromIntacct for Auto claims: %+v\n", err)
		errs = append(errs, err.Error())
	}

	autoMaintCount, err = auto.ReceiveMCBatchesFromIntacct(ctx)
	if err != nil {
		log.Printf("Error in executing ReceiveFromIntacct for maintenance claims: %+v\n", err)
		errs = append(errs, err.Error())
	}

	lwtCount, err = lwt.ReceiveFromIntacct(ctx)
	if err != nil {
		log.Printf("Error in executing ReceiveFromIntacct for LWT claims: %+v\n", err)
		errs = append(errs, err.Error())
	}

	vtaCount, err = vta.ReceiveFromIntacct(ctx)
	if err != nil {
		log.Printf("Error in executing ReceiveFromIntacct for VTA claims: %+v\n", err)
		errs = append(errs, err.Error())
	}

	return http.StatusOK, map[string]interface{}{
		"errors":           errs,
		"gap":              gapCount,
		"auto":             autoCount,
		"auto_maintenance": autoMaintCount,
		"lwt":              lwtCount,
		"vta":              vtaCount,
	}
}
