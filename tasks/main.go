package tasks

import (
	"context"
	"flag"
	"log"
	"phizz/db"
)

// CreateJobFlag defines that flag that is used to indicate that
// a task should run instead of the main service.
func CreateJobFlag(dest *string) {
	flag.StringVar(dest, "job", "", `Run a helper job

Options: 
	trim-intacct-request-response-table - Deletes old rows from the intacct_request_response_table.
`)
}

// Run invokes the specified task
func Run(ctx context.Context, jobName string) {
	if jobName == "trim-intacct-request-response-table" {
		err := db.TrimIntacctRequestResponseTable(ctx)
		if err != nil {
			log.Println("error trimming intacct_request_response table")
			log.Fatalf("%+v\n", err)
		}
	} else if jobName != "" {
		log.Fatalf("Unrecognized job: %s", jobName)
	}
}
