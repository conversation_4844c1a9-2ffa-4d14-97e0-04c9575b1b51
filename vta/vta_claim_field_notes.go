package vta

import (
	"encoding/json"
	"net/http"
	"strings"
	"time"

	"phizz/db"
	"phizz/handlers"

	"database/sql"
	"github.com/go-chi/chi"
	"github.com/jmoiron/sqlx"
	"github.com/pkg/errors"
)

type fieldNotePayload struct {
	FieldID         int       `json:"field_id" db:"field_id"`
	VTAClaimID      int       `json:"vta_claim_id" db:"vta_claim_id"`
	NotesText       string    `json:"notes_text" db:"notes_text"`
	CreatedAt       time.Time `json:"created_at" db:"created_at"`
	CreatedByUserID int       `json:"created_by_user_id" db:"created_by_user_id"`
	FirstName       string    `json:"first_name" db:"first_name"`
	LastName        string    `json:"last_name" db:"last_name"`
}

type fieldNote struct {
	FieldID int                `json:"field_id" db:"-"`
	Count   int                `json:"count" db:"-"`
	Notes   []fieldNotePayload `json:"notes" db:"-"`
}

// FieldNoteCreate creates an item note for VTA Claim's specific field e.g. Settlement check
func FieldNoteCreate(_ http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	fieldNote, err := fieldNoteFromReq(req)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(nil, "Malformed Field Note data for create.", nil)
	}
	fieldNote.CreatedByUserID = user.ID

	cleanFieldNote(fieldNote)
	formErrors, err := validateFieldNote(fieldNote)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(errors.Wrap(err, "Error validating Record Note"),
			"An error occurred validating the form values.", nil)
	}

	if len(formErrors) > 0 {
		return http.StatusBadRequest, handlers.ErrorMessage(nil, "Form validations errors.",
			map[string]interface{}{"errors": formErrors})
	}

	id, err := createFieldNote(fieldNote, nil)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(errors.Wrap(err, "Database error in adding note"),
			"Database error in adding note", nil)
	}

	return http.StatusOK, map[string]interface{}{"field_note_id": id}
}

func fieldNoteFromReq(req *http.Request) (*fieldNotePayload, error) {
	fieldNote := fieldNotePayload{}

	dec := json.NewDecoder(req.Body)
	err := dec.Decode(&fieldNote)

	return &fieldNote, errors.Wrap(err, "decoding Field Note request failed")
}

// cleanRecordNote cleans up leading and trailing white-space etc...
func cleanFieldNote(fieldNote *fieldNotePayload) {
	fieldNote.NotesText = strings.TrimSpace(fieldNote.NotesText)
}

// validateRecordNote validates a NewClaim record for correctness
func validateFieldNote(fieldNote *fieldNotePayload) (map[string]string, error) {
	formErrors := map[string]string{}

	if fieldNote.VTAClaimID == 0 {
		formErrors["vta_claim_id"] = "VTA claim id is required"
	}
	if fieldNote.FieldID == 0 {
		formErrors["field_id"] = "Field id is required"
	}
	if fieldNote.NotesText == "" {
		formErrors["notes_text"] = "Notes text is required"
	}
	if fieldNote.CreatedByUserID == 0 {
		formErrors["user_id"] = "User ID is required"
	}
	return formErrors, nil
}

// FieldNoteIndex field notes and contract field notes for given claim
func FieldNoteIndex(_ http.ResponseWriter, req *http.Request, _ db.User) (int, map[string]interface{}) {
	id := chi.URLParam(req, "id")
	// Get field notes
	getFieldNotesQuery := `select vta_claim_id, field_id, notes_text, created_by_user_id, vta_claim_field_notes.created_at, first_name, last_name
	 from vta_claim_field_notes join users on created_by_user_id = users.id
	 where vta_claim_id = $1 order by field_id, vta_claim_field_notes.created_at desc`

	var fieldNotes []fieldNotePayload

	err := db.Get().Select(&fieldNotes, getFieldNotesQuery, id)
	if err != nil && err != sql.ErrNoRows {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error loading VTA claim field notes from database.", nil)
	}

	var fieldNotesWithCount []fieldNote
	currentFieldID := 0
	numberOfFieldID := 0
	for _, currentResultObject := range fieldNotes {
		if currentResultObject.FieldID != currentFieldID {
			currentFieldID = currentResultObject.FieldID
			numberOfFieldID = numberOfFieldID + 1
			fieldNotesWithCount = append(fieldNotesWithCount, fieldNote{})
		}
		fieldNotesWithCount[numberOfFieldID-1].FieldID = currentFieldID
		fieldNotesWithCount[numberOfFieldID-1].Notes = append(fieldNotesWithCount[numberOfFieldID-1].Notes, currentResultObject)
		fieldNotesWithCount[numberOfFieldID-1].Count = fieldNotesWithCount[numberOfFieldID-1].Count + 1
	}

	return http.StatusOK, map[string]interface{}{"field_notes": fieldNotesWithCount}
}

func createFieldNote(fieldNote *fieldNotePayload, tx *sqlx.Tx) (int, error) {

	insertQuery := `insert into vta_claim_field_notes (created_at,vta_claim_id, field_id, notes_text,created_by_user_id)
		values (now() at time zone 'utc',:vta_claim_id,:field_id,:notes_text,:created_by_user_id) returning id`

	var stmt *sqlx.NamedStmt
	var err error
	if tx != nil {
		stmt, err = tx.PrepareNamed(insertQuery)
	} else {
		stmt, err = db.Get().PrepareNamed(insertQuery)
	}

	if err != nil {
		return 0, errors.Wrap(err, "Error inserting Field Note, error occurred in PrepareNamed function while adding Field Note.")
	}
	defer func() { _ = stmt.Close() }()

	id := 0
	err = stmt.Get(&id, fieldNote)
	if err != nil {
		return id, errors.Wrap(err, "Error inserting Field Note, error occurred while trying to add the Field Note to the database.")
	}

	return id, nil
}
