package vta

import (
	"net/http"
	"net/http/httptest"
	"phizz/db"
	"phizz/handlers"
	"regexp"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/go-chi/chi"
	"github.com/jmoiron/sqlx"
)

func DISABLEDTestClaimIndex(t *testing.T) {
	mdb, mock, err := sqlmock.New()
	if err != nil {
		t.Fatal(err)
	}
	dbx := sqlx.NewDb(mdb, "sqlmock")
	db.DB = dbx

	q := regexp.QuoteMeta

	mock.ExpectPrepare(q(`select vta_claims.id, customers.last_name || ' ' || customers.first_name as insured_name, contract_number, status, vta_claims.date_of_claim_received, vta_claims.date_of_last_in, users.first_name || ' ' || users.last_name as assigned_to from vta_claims join customers on customer_id = customers.id join users on owner_id = users.id where vta_claims.owner_id = ? order by date_of_claim_received desc limit 20 offset 0`))
	mock.ExpectQuery(`select vta_claims.id, last_name || ',' || first_name as customer_name, contract_number, vin, status, date_of_claim_received, facility_code as facility, estimate, reassignment_status, owner_id, reassigned_owner_id from vta_claims join customers on vta_claims.customer_id = customers.id left join vta_facilities on vta_claims.facility_id = vta_facilities.id where vta_claims.owner_id = ? order by date_of_claim_received desc limit 20 offset 0`).
		WillReturnRows(sqlmock.NewRows([]string{"id", "insured_name", "contract_number", "assigned_to", "status", "date_of_claim_received", "date_of_last_in"}).
			AddRow(3, "Lopez,Paul", "VTRC123456", "Mike", "Open", time.Time{}, time.Time{}))

	mock.ExpectPrepare(q(`select count(*) from vta_claims join customers on customer_id = customers.id join users on owner_id = users.id where vta_claims.owner_id = ?`))
	mock.ExpectQuery(q(`select count(*) from vta_claims join customers on customer_id = customers.id join users on owner_id = users.id where vta_claims.owner_id = ?`)).WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))

	req, err := http.NewRequest("GET", "/api/vta-claims?user_id=5103&page=1", nil)
	if err != nil {
		t.Fatal(err)
	}

	rr := httptest.NewRecorder()
	handler := http.HandlerFunc(handlers.APIHandler(ClaimIndex))

	r := chi.NewRouter()
	r.HandleFunc("/api/vta-claims", handler)
	r.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v",
			status, http.StatusOK)
	}

	expected := `{"count":1,"vta_claims":[{"id":3,"insured_name":"Lopez,Paul","contract_number":"VTRC123456","assigned_to":"Mike","status":"Open","date_of_claim_received":"0001-01-01T00:00:00Z","date_of_last_in":"0001-01-01T00:00:00Z"}]}`
	if rr.Body.String() != expected {
		t.Errorf("handler returned unexpected body: got %v want %v",
			rr.Body.String(), expected)
	}

	if err := mock.ExpectationsWereMet(); err != nil {
		t.Errorf("there were unfulfilled expectations: %s", err)
	}
}

func TestClaimCountsWithStatusCount(t *testing.T) {
	mdb, mock, err := sqlmock.New()
	if err != nil {
		t.Fatal(err)
	}
	dbx := sqlx.NewDb(mdb, "sqlmock")
	db.DB = dbx

	queryParser := regexp.QuoteMeta

	mock.ExpectQuery(queryParser(`select * from ( select (case when (status = 'I') then 'In Inquiry' when (status in ('P','RO-RP','PD','RC', 'RP')) then 'In Process' when (status = 'WA') then 'In Review' when (status in ('WC','WP')) then 'In Finance' end) as name, count(*) FROM vta_claims WHERE status in ($1,$2,$3,$4,$5,$6,$7,$8,$9) GROUP BY (case when (status = 'I') then 'In Inquiry' when (status in ('P','RO-RP','PD','RC', 'RP')) then 'In Process' when (status = 'WA') then 'In Review' when (status in ('WC','WP')) then 'In Finance' end)) x order by (case when name = 'In Inquiry' then 1 when name = 'In Process' then 2 when name = 'In Review' then 3 when name = 'In Finance' then 4 end) ASC;`)).
		WillReturnRows(sqlmock.NewRows([]string{"id", "name", "count"}).
			AddRow(0, "In Progress", 3))

	req, err := http.NewRequest("GET", "/api/vta-claims/count/status", nil)
	if err != nil {
		t.Fatal(err)
	}

	rr := httptest.NewRecorder()
	handler := http.HandlerFunc(handlers.APIHandler(ClaimCounts))

	r := chi.NewRouter()
	r.HandleFunc("/api/vta-claims/count/{countBy:[a-z]+}", handler)
	r.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v",
			status, http.StatusOK)
	}

	expected := `{"vta_claims":[{"name":"In Progress","count":3,"id":0}]}`
	if rr.Body.String() != expected {
		t.Errorf("handler returned unexpected body: got %v want %v",
			rr.Body.String(), expected)
	}

	if err := mock.ExpectationsWereMet(); err != nil {
		t.Errorf("there were unfulfilled expectations: %s", err)
	}
}

func TestClaimCountsWithAgentCount(t *testing.T) {
	mdb, mock, err := sqlmock.New()
	if err != nil {
		t.Fatal(err)
	}
	dbx := sqlx.NewDb(mdb, "sqlmock")
	db.DB = dbx

	queryParser := regexp.QuoteMeta

	mock.ExpectQuery(queryParser(`select first_name || ' ' || last_name as name, count(*), users.id from vta_claims left join users on vta_claims.owner_id = users.id where status in ($1,$2,$3,$4,$5,$6,$7,$8) group by users.id ORDER BY count DESC`)).
		WillReturnRows(sqlmock.NewRows([]string{"id", "name", "count"}).
			AddRow(0, "In Progress", 3))

	req, err := http.NewRequest("GET", "/api/vta-claims/count/agent", nil)
	if err != nil {
		t.Fatal(err)
	}

	rr := httptest.NewRecorder()
	handler := http.HandlerFunc(handlers.APIHandler(ClaimCounts))

	r := chi.NewRouter()
	r.HandleFunc("/api/vta-claims/count/{countBy:[a-z]+}", handler)
	r.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v",
			status, http.StatusOK)
	}

	expected := `{"vta_claims":[{"name":"In Progress","count":3,"id":0}]}`
	if rr.Body.String() != expected {
		t.Errorf("handler returned unexpected body: got %v want %v",
			rr.Body.String(), expected)
	}

	if err := mock.ExpectationsWereMet(); err != nil {
		t.Errorf("there were unfulfilled expectations: %s", err)
	}
}

func TestClaimCountsWithAgeCount(t *testing.T) {
	mdb, mock, err := sqlmock.New()
	if err != nil {
		t.Fatal(err)
	}
	dbx := sqlx.NewDb(mdb, "sqlmock")
	db.DB = dbx

	queryParser := regexp.QuoteMeta

	mock.ExpectQuery(queryParser(`select first_name || ' ' || last_name as name, count(*), users.id from vta_claims left join users on vta_claims.owner_id = users.id where status in ($1,$2,$3,$4,$5,$6,$7,$8) group by users.id ORDER BY count DESC`)).
		WillReturnRows(sqlmock.NewRows([]string{"id", "name", "count"}).
			AddRow(0, "In Progress", 3))

	req, err := http.NewRequest("GET", "/api/vta-claims/count/agent", nil)
	if err != nil {
		t.Fatal(err)
	}

	rr := httptest.NewRecorder()
	handler := http.HandlerFunc(handlers.APIHandler(ClaimCounts))

	r := chi.NewRouter()
	r.HandleFunc("/api/vta-claims/count/{countBy:[a-z]+}", handler)
	r.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v",
			status, http.StatusOK)
	}

	expected := `{"vta_claims":[{"name":"In Progress","count":3,"id":0}]}`
	if rr.Body.String() != expected {
		t.Errorf("handler returned unexpected body: got %v want %v",
			rr.Body.String(), expected)
	}

	if err := mock.ExpectationsWereMet(); err != nil {
		t.Errorf("there were unfulfilled expectations: %s", err)
	}
}

func DISABLEDTestClaimShow(t *testing.T) {
	mdb, mock, err := sqlmock.New()
	if err != nil {
		t.Fatal(err)
	}
	dbx := sqlx.NewDb(mdb, "sqlmock")
	db.DB = dbx

	queryParser := regexp.QuoteMeta

	mock.ExpectQuery(queryParser(`select vta_claims.*, customers.* from vta_claims join customers on vta_claims.customer_id = customers.id where vta_claims.id = $1 limit 1`)).
		WillReturnRows(sqlmock.NewRows([]string{"id", "vin", "created_by_user_id", "status", "denied_reason",
			"date_of_claim_received", "date_of_last_in", "customer_id", "first_name", "last_name", "state",
			"city", "postal_code", "street_address", "email_address", "phone_number", "contract_number", "is_police_report_available",
			"police_report_available_manager_flag", "has_settlement_check", "settlement_check_value", "settlement_check_manager_flag",
			"has_original_financing", "original_financing_value", "original_financing_manager_flag", "has_vta_contract", "vta_contract_manager_flag",
			"has_insurance_not_recovered", "insurance_not_recovered_manager_flag", "owner_id", "updated_at", "updated_by_user_id", "updated_by_user_name"}).
			AddRow(3, "2FMHK6CC3CBD00460", 6245, "I", "denied_reason", time.Time{}, time.Time{},
				4, "CATHERINE", "BONDS", "CO", "AURORA", 80013, "15462 E EVANS AVE #308", "<EMAIL>", 7202454151, "TRS1048278",
				false, false, false, "10", false, false, 10.0, false, false, false, false, false, 6245, time.Time{},
				6245, "Mehul Recovery"))

	mock.ExpectQuery(queryParser(`select vta_claim_updates.updated_at, updated_by_user_id, first_name, last_name from vta_claim_updates join users on updated_by_user_id = users.id where vta_claim_id = $1 order by updated_at desc limit 1`)).
		WillReturnRows(sqlmock.NewRows([]string{"updated_at", "updated_by_user_id", "first_name", "last_name"}).
			AddRow(time.Time{}, 6245, "Durga", "Budhwani"))

	req, err := http.NewRequest("GET", "/api/vta-claims/3", nil)
	if err != nil {
		t.Fatal(err)
	}

	rr := httptest.NewRecorder()
	handler := http.HandlerFunc(handlers.APIHandler(ClaimShow))

	r := chi.NewRouter()
	r.HandleFunc("/api/vta-claims/{id:[0-9]+}", handler)
	r.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v",
			status, http.StatusOK)
	}

	expected := `{"vta_claim":{"id":3,"vin":"2FMHK6CC3CBD00460","has_vendor_id":false,"vendor_id_manager_flag":false,"created_by_user_id":6245,"status":"I","denied_reason":"denied_reason","date_of_claim_received":"0001-01-01T00:00:00Z","date_of_last_in":"0001-01-01T00:00:00Z","case_reserve":"0","has_case_reserve":false,"case_reserve_manager_flag":false,"customer_id":4,"first_name":"CATHERINE","last_name":"BONDS","state":"CO","city":"AURORA","postal_code":"80013","street_address":"15462 E EVANS AVE #308","email_address":"<EMAIL>","phone_number":"7202454151","contract_number":"TRS1048278","is_police_report_available":false,"police_report_available_manager_flag":false,"has_settlement_check":false,"settlement_check_value":"10","settlement_check_manager_flag":false,"has_original_financing":false,"original_financing_value":"10","original_financing_manager_flag":false,"has_vta_contract":false,"vta_contract_manager_flag":false,"has_insurance_not_recovered":false,"insurance_not_recovered_manager_flag":false,"owner_id":6245,"updated_at":"0001-01-01T00:00:00Z","updated_by_user_id":6245,"updated_by_user_name":"Durga Budhwani","is_in_progress":false,"vendor_id":""}}`
	if rr.Body.String() != expected {
		t.Errorf("handler returned unexpected body: got %v want %v",
			rr.Body.String(), expected)
	}

	if err := mock.ExpectationsWereMet(); err != nil {
		t.Errorf("there were unfulfilled expectations: %s", err)
	}
}

func DISABLEDTestClaimByContract(t *testing.T) {
	mdb, mock, err := sqlmock.New()
	if err != nil {
		t.Fatal(err)
	}
	dbx := sqlx.NewDb(mdb, "sqlmock")
	db.DB = dbx

	queryParser := regexp.QuoteMeta

	mock.ExpectQuery(queryParser(`select * from vta_claims where contract_number = $1`)).
		WillReturnRows(sqlmock.NewRows([]string{"id", "vin", "created_by_user_id", "status", "denied_reason",
			"date_of_claim_received", "date_of_last_in", "customer_id", "first_name", "last_name", "state",
			"city", "postal_code", "street_address", "email_address", "phone_number", "contract_number", "is_police_report_available",
			"police_report_available_manager_flag", "has_settlement_check", "settlement_check_value", "settlement_check_manager_flag",
			"has_original_financing", "original_financing_value", "original_financing_manager_flag", "has_vta_contract", "vta_contract_manager_flag",
			"has_insurance_not_recovered", "insurance_not_recovered_manager_flag", "owner_id", "updated_at", "updated_by_user_id", "updated_by_user_name"}).
			AddRow(3, "2FMHK6CC3CBD00460", 6245, "I", "denied_reason", time.Time{}, time.Time{},
				4, "CATHERINE", "BONDS", "CO", "AURORA", 80013, "15462 E EVANS AVE #308", "<EMAIL>", 7202454151, "TRS1048278",
				false, false, false, "10", false, false, 10.0, false, false, false, false, false, 6245, time.Time{},
				6245, "Mehul Recovery"))

	req, err := http.NewRequest("GET", "/api/vta-claims/contract/xyz", nil)
	if err != nil {
		t.Fatal(err)
	}

	rr := httptest.NewRecorder()
	handler := http.HandlerFunc(handlers.APIHandler(ClaimByContract))

	r := chi.NewRouter()
	r.HandleFunc("/api/vta-claims/contract/{id}", handler)
	r.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v",
			status, http.StatusOK)
	}

	expected := `{"vta_claim":{"id":3,"vin":"2FMHK6CC3CBD00460","has_vendor_id":false,"vendor_id_manager_flag":false,"created_by_user_id":6245,"status":"I","denied_reason":"denied_reason","date_of_claim_received":"0001-01-01T00:00:00Z","date_of_last_in":"0001-01-01T00:00:00Z","case_reserve":"0","has_case_reserve":false,"case_reserve_manager_flag":false,"customer_id":4,"first_name":"CATHERINE","last_name":"BONDS","state":"CO","city":"AURORA","postal_code":"80013","street_address":"15462 E EVANS AVE #308","email_address":"<EMAIL>","phone_number":"7202454151","contract_number":"TRS1048278","is_police_report_available":false,"police_report_available_manager_flag":false,"has_settlement_check":false,"settlement_check_value":"10","settlement_check_manager_flag":false,"has_original_financing":false,"original_financing_value":"10","original_financing_manager_flag":false,"has_vta_contract":false,"vta_contract_manager_flag":false,"has_insurance_not_recovered":false,"insurance_not_recovered_manager_flag":false,"owner_id":6245,"updated_at":"0001-01-01T00:00:00Z","updated_by_user_id":6245,"updated_by_user_name":"Mehul Recovery","is_in_progress":false,"vendor_id":""}}`
	if rr.Body.String() != expected {
		t.Errorf("handler returned unexpected body: got %v want %v",
			rr.Body.String(), expected)
	}

	if err := mock.ExpectationsWereMet(); err != nil {
		t.Errorf("there were unfulfilled expectations: %s", err)
	}
}
