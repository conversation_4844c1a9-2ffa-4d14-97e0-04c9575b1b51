package vta

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"phizz/db"
	"phizz/handlers"
	"phizz/intacct"
	"strconv"
	"time"

	"github.com/go-chi/chi"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
)

// ReceiveFromIntacct function receives the vta claim paid info from intacct ap payment service
// the bill and payment request is generated by calling Intacct APIs
// This function will update the check details in vta_claim_payments table, if only the appayment
// request is found in intacct and has a status 'Complete'
// This function is invoked by cmd/apreceive binary, which is scheduled to run as a cronjob on server
func ReceiveFromIntacct(ctx context.Context) (int, error) {
	var pendingPayments []paymentInfo
	checkCount := 0
	query := `select vta_claim_payments.id, authorization_number,
		vta_claim_id, date_of_claim_received, case_reserve,
		case when payment_key is null then 0 else payment_key end as payment_key,
		vta_claims.owner_id, vta_claims.contract_number, bill_key, status
	from vta_claims join vta_claim_payments on vta_claims.id = vta_claim_id
	where check_number = 0 and bill_key is not null`
	err := db.Get().Select(&pendingPayments, query)
	if err != nil {
		if err == sql.ErrNoRows {
			return checkCount, nil
		}
		return checkCount, errors.Wrap(err, "Error loading vta claim payment from database.")
	}

	for _, payment := range pendingPayments {
		// if payment key is 0 then get payment key from intacct
		if payment.PaymentKey == 0 {
			payment.PaymentKey, err = intacct.GetPaymentKey(ctx, payment.BillKey, payment.CaseReserve)
			if err != nil {
				log.Println(err)
				continue
			}
			// save payment key once it will be available
			err = updatePaymentKey(payment.PaymentKey, payment.ID)
			if err != nil {
				log.Println("Error in updaing payment key ", payment.PaymentKey, err)
				continue
			}
		}

		// get paidInfo from intacct
		paidInfo, err := intacct.PaidInfo(ctx, payment.PaymentKey)
		if err != nil {
			log.Println(err)
			continue
		}

		// update to unidata
		payment.PaidDate = paidInfo.PaidDate
		payment.CheckNumber = paidInfo.CheckNumber
		payment.Amount = paidInfo.Amount

		// update payments table with payment info and vta claims table with status
		err = updateClaimPaid(strconv.Itoa(payment.ID), strconv.Itoa(payment.ClaimID), paidInfo)
		if err != nil {
			log.Println("Error updating paid info in vta_claim_payments", err)
			continue
		}

		// cancel contract for checkWritten claims
		note := "Claim is paid by Check# " + strconv.Itoa(payment.CheckNumber) + "On Date:" + payment.PaidDate.String()
		handlers.ContractExpire(payment.ContractNumber, note)
		checkCount++
	}
	return checkCount, err
}

// updatePaymentKey update payment key received from intacct
func updatePaymentKey(paymentKey int, claimID int) error {
	updatePaymentKey := `update vta_claim_payments set payment_key = $1, updated_at = now() at time zone 'utc' where id = $2`
	_, err := db.Get().Exec(updatePaymentKey, paymentKey, claimID)
	return err
}

// updateClaimPaid update payments table with payment info and vta claims table with status
func updateClaimPaid(claimPaymentID string, claimID string, claimPayment *intacct.ResponsePayload) error {
	tx, err := db.Get().Beginx()
	if err != nil {
		return errors.Wrap(err, "Database error beginning transaction for vta claim payment update")
	}

	acpQuery := `update vta_claim_payments set check_number = $1, amount = $2, paid_date = $3, updated_at = now() at time zone 'utc' where id = $4`
	_, err = tx.Exec(acpQuery, claimPayment.CheckNumber, claimPayment.Amount, claimPayment.PaidDate, claimPaymentID)
	if err != nil {
		_ = tx.Rollback()
		return errors.Wrap(err, "Error updating vta claim paid info")
	}

	acQuery := `update vta_claims set status = $1 where id = $2`
	_, err = tx.Exec(acQuery, db.VtaClaimStatusCheckWritten, claimID)
	if err != nil {
		_ = tx.Rollback()
		return errors.Wrap(err, "Error updating vta claim paid info")
	}
	err = tx.Commit()
	return err
}

func getClaimPayment(claim *vtaClaimPayload) (*paymentInfo, error) {
	var query string
	if claim.Status == db.VtaClaimStatusWaitingForCheck {
		query = "select authorization_number, bill_number from vta_claim_payments where vta_claim_id = $1 order by id desc limit 1"
	} else if claim.Status == db.VtaClaimStatusCheckWritten || claim.Status == db.VtaClaimStatusCheckVoided {
		query = "select authorization_number, bill_number, check_number, amount, paid_date from vta_claim_payments where vta_claim_id = $1 order by id desc limit 1"
	} else {
		return nil, nil
	}

	vtaClaimPayment := paymentInfo{}
	err := db.Get().Get(&vtaClaimPayment, query, claim.ID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return &vtaClaimPayment, errors.Wrap(err, "error loading vta claim payments from database.")
	}
	return &vtaClaimPayment, nil
}

// VerifyClaimVoid checks whether the particular claim is void in intacct
func VerifyClaimVoid(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	ctx := req.Context()
	claim := struct {
		ID             int    `json:"id" db:"id"`
		Status         string `json:"status" db:"status"`
		ContractNumber string `json:"-" db:"contract_number"`
	}{}

	err := db.Get().GetContext(ctx, &claim, `select id, status, contract_number from vta_claims where id=$1`, chi.URLParam(req, "id"))
	if err != nil {
		handlers.ReportError(req, err)
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage(err, "The VTA claim was not found", nil)
		}
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Could not get claim data", nil)
	}
	if claim.Status != db.VtaClaimStatusCheckWritten {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Only checkWritten claim can be voided", nil)
	}

	paymentInfo := struct {
		CheckNumber int             `db:"check_number"`
		Amount      decimal.Decimal `db:"amount"`
		PaidDate    time.Time       `db:"paid_date"`
		BillKey     int             `db:"bill_key"`
	}{}
	authQuery := "select check_number, amount, paid_date, bill_key from vta_claim_payments where vta_claim_id = $1 order by id desc limit 1"
	err = db.Get().GetContext(ctx, &paymentInfo, authQuery, claim.ID)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "could not get claim payment data"))
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage(err, "The VTA claim authorization was not found", nil)
		}
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Could not get claim payment data", nil)
	}
	claimReason := req.FormValue("reason")

	if claimReason == "" {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Invalid request for void payment, reason is needed", nil)
	}

	reversedClaim, err := intacct.VoidIntacctPayment(ctx, paymentInfo.BillKey, paymentInfo.Amount, claimReason)
	if err != nil && !reversedClaim {
		handlers.ReportError(req, errors.Wrap(err, "cannot reverse/void payment in Intacct for contract#:"+claim.ContractNumber))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Cannot reverse/void payment in Intacct for contract#: "+claim.ContractNumber, nil)
	}
	if reversedClaim {
		return http.StatusOK, map[string]interface{}{"checkNumber": paymentInfo.CheckNumber}
	}
	return http.StatusOK, map[string]interface{}{"checkNumber": -1}
}

// ClaimVoid voids the payment in intacct
func ClaimVoid(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	ctx := req.Context()

	var claim struct {
		ID             int    `json:"id" db:"id"`
		Status         string `json:"status" db:"status"`
		ContractNumber string `json:"-" db:"contract_number"`
	}
	err := db.Get().GetContext(ctx, &claim, `select id, status, contract_number from vta_claims where id=$1`, chi.URLParam(req, "id"))
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage(err, "The VTA claim was not found", nil)
		}
		handlers.ReportError(req, errors.Wrap(err, "could not get claim data"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Could not get claim data", nil)
	}
	if claim.Status != db.VtaClaimStatusCheckWritten {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Only checkWritten claim can be voided", nil)
	}

	var paymentInfo struct {
		CheckNumber int             `db:"check_number"`
		Amount      decimal.Decimal `db:"amount"`
		PaidDate    time.Time       `db:"paid_date"`
		BillKey     int             `db:"bill_key"`
	}
	authQuery := `select check_number, amount, paid_date, bill_key 
					from vta_claim_payments 
					where vta_claim_id = $1 
					order by id desc limit 1`
	err = db.Get().GetContext(ctx, &paymentInfo, authQuery, claim.ID)
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage(err, "The VTA claim authorization was not found", nil)
		}
		handlers.ReportError(req, errors.Wrap(err, "could not get claim payment data"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Could not get claim payment data", nil)
	}

	var voidClaim struct {
		Reason string `json:"reason"`
	}
	dec := json.NewDecoder(req.Body)
	err = dec.Decode(&voidClaim)
	if err != nil || voidClaim.Reason == "" {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Invalid request for void payment, reason is needed", nil)
	}

	reversedClaim, err := intacct.VoidIntacctPayment(ctx, paymentInfo.BillKey, paymentInfo.Amount, voidClaim.Reason)
	if err != nil && !reversedClaim {
		handlers.ReportError(req, errors.Wrap(err, "cannot reverse/void payment in Intacct for contract#: "+claim.ContractNumber))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Cannot reverse/void payment in Intacct for contract#: "+claim.ContractNumber, nil)
	}

	// transaction
	tx, err := db.Get().BeginTxx(ctx, nil)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "database error beginning transaction for void payment"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error beginning transaction for void payment", nil)
	}

	// change status
	updateStatus := `update vta_claims set status = $1 where id = $2`
	_, err = tx.ExecContext(ctx, updateStatus, db.VtaClaimStatusWaitingForAuthorization, claim.ID)
	if err != nil {
		_ = tx.Rollback()
		handlers.ReportError(req, errors.Wrap(err, "error updating VTA claim status"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error updating VTA claim status", nil)
	}

	// update payment to voided
	updatePayment := `update vta_claim_payments
		set voided_at = $1,
		voided_by_user_id = $2
		where id = (
			select id
			from vta_claim_payments
			where vta_claim_id = $3
			order by id desc
			limit 1)`
	_, err = tx.ExecContext(ctx, updatePayment, time.Now().UTC(), user.ID, claim.ID)
	if err != nil {
		_ = tx.Rollback()
		handlers.ReportError(req, errors.Wrap(err, "error updating VTA claim payment to voided"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error updating VTA claim status", nil)
	}

	// insert into vta_voided_transactions, the amount should be negative hence multiplying by -1
	transactionQuery := `insert into vta_voided_transactions (date_of_void, vta_claim_id,check_amount,check_number) 
							values (now() at time zone 'utc',$1,$2,$3)`
	_, err = tx.ExecContext(ctx, transactionQuery, claim.ID, paymentInfo.Amount.Mul(decimal.NewFromFloat(-1)), paymentInfo.CheckNumber)
	if err != nil {
		_ = tx.Rollback()
		handlers.ReportError(req, errors.Wrap(err, "error inserting the transaction for void payment"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error inserting the transaction for void payment", nil)
	}

	// insert note
	recordNote := `insert into vta_record_notes (created_at,vta_claim_id,notes_text,created_by_user_id) 
					values (now() at time zone 'utc',$1,$2,$3) returning id`
	note := fmt.Sprintf("The check with Number: %d Amount: %s Date: %s  is voided for reason '%s'",
		paymentInfo.CheckNumber, paymentInfo.Amount.String(), paymentInfo.PaidDate.Format("2006-01-02"), voidClaim.Reason)
	recordNoteID := 0
	err = tx.GetContext(ctx, &recordNoteID, recordNote, claim.ID, note, user.ID)
	if err != nil {
		_ = tx.Rollback()
		handlers.ReportError(req, errors.Wrap(err, "error inserting the note for void payment"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error inserting the note for void payment", nil)
	}

	// commit
	err = tx.Commit()
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "database error committing transaction for void payment"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error committing transaction for void payment", nil)
	}
	return http.StatusOK, map[string]interface{}{"id": claim.ID}
}
