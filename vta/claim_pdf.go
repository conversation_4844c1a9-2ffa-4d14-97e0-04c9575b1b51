package vta

import (
	"net/http"
	"strconv"

	"phizz/db"
	"phizz/handlers"

	"github.com/go-chi/chi"
	"github.com/jung-kurt/gofpdf"
	"github.com/pkg/errors"
)

// ClaimPdf generates the pdf for covered components
func ClaimPdf(w http.ResponseWriter, req *http.Request, user db.User) {
	id := chi.URLParam(req, "id")

	// get vta claim from database
	vtaClaimFromDB, err := getVtaClaimByID(id)
	if err != nil {
		_ = handlers.Renderer().Text(w, http.StatusInternalServerError, "Error getting VTA claim from database")
		handlers.ReportError(req, errors.Wrap(err, "error getting VTA claim from database"))
		return
	}

	vtaClaim := vtaClaimFromDB.getVtaClaimPayload()

	fpdf := gofpdf.New("Portrait", "in", "Letter", "")

	vtaClaimPayment, err := getClaimPayment(vtaClaim)
	if err != nil {
		_ = handlers.Renderer().Text(w, http.StatusInternalServerError, "Error getting VTA claim payment from database")
		handlers.ReportError(req, errors.Wrap(err, "error getting VTA claim payment from database"))
	}
	claimToPdf(fpdf, vtaClaim, vtaClaimPayment)

	w.Header().Set("Content-Type", "application/pdf")
	w.WriteHeader(http.StatusOK)
	err = fpdf.Output(w)
	if err != nil {
		err = errors.Wrap(err, "error writing contract coverage components")
		_ = handlers.Renderer().Text(w, http.StatusBadRequest, "error writing contract coverage components")
		handlers.ReportError(req, err)
		return
	}
}

// coverageComponentsPDF generates the pdf for covered components
func claimToPdf(fpdf *gofpdf.Fpdf, vtaClaim *vtaClaimPayload, vtaClaimPayment *paymentInfo) {
	const dateFormat = "2006/01/02"
	const yesFlag = "[Y]"
	const noFlag = "[N]"

	vtaStatusMap := map[string]string{
		db.VtaClaimStatusNew:                     "New",
		db.VtaClaimStatusInquiry:                 "Inquiry",
		db.VtaClaimStatusPending:                 "Pending",
		db.VtaClaimStatusReadyToProcess:          "Ready to process",
		db.VtaClaimStatusWaitingForAuthorization: "Waiting for authorization",
		db.VtaClaimStatusPendingDenial:           "Pending denial",
		db.VtaClaimStatusWaitingForCheck:         "Authorization",
		db.VtaClaimStatusReturnedForCorrections:  "Returned",
		db.VtaClaimStatusCheckWritten:            "Check Written",
		db.VtaClaimStatusDeny:                    "Denied",
		db.VtaClaimStatusPendingReopened:         "Pending Reopened",
		db.VtaClaimStatusWaitingForPayment:       "Waiting for payment",
		db.VtaClaimStatusClosedNoResponse:        "Closed No-Response",
	}

	printFlag := func(flag bool) {
		flagStr := noFlag
		if flag == true {
			flagStr = yesFlag
		}
		fpdf.CellFormat(0.15, 0.15, flagStr, "", 0, "CM", true, 0, "")
	}

	fpdf.SetFillColor(211, 211, 211)
	fpdf.AddPage()
	fpdf.SetFont("Helvetica", "B", 16)
	fpdf.CellFormat(7, 0.2, vtaClaim.ContractNumber+" "+vtaClaim.Status, "", 0, "C", false, 0, "")
	fpdf.Ln(0.3)

	fpdf.CellFormat(7, 0.2, "Reserve: $ "+vtaClaim.CaseReserve.StringFixed(2), "", 0, "C", false, 0, "")
	fpdf.Ln(0.3)
	fpdf.SetFont("Helvetica", "", 8)

	printFlag(vtaClaim.IsInProgress)

	fpdf.Cell(1, 0.2, "In Progress")
	fpdf.Ln(-1)

	printFlag(vtaClaim.IsPoliceReportAvailable)
	fpdf.Cell(1, 0.2, "Police report:")
	fpdf.Ln(-1)

	printFlag(vtaClaim.HasSettlementCheck)
	fpdf.Cell(2, 0.2, "Settlement check:")
	fpdf.Cell(1, 0.2, "$ "+vtaClaim.SettlementCheck.StringFixed(2))
	fpdf.Ln(-1)

	printFlag(vtaClaim.HasOriginalFinancing)
	fpdf.Cell(2, 0.2, "Original financing:")
	fpdf.Cell(1, 0.2, "$ "+vtaClaim.OriginalFinancingValue.StringFixed(2))
	fpdf.Ln(-1)

	printFlag(vtaClaim.HasVtaContract)
	fpdf.Cell(1, 0.2, "VTA Contract")
	fpdf.Ln(-1)

	printFlag(vtaClaim.HasInsuranceNotCovered)
	fpdf.Cell(2, 0.2, "Insurance - not recovered")
	fpdf.Ln(-1)

	fpdf.Cell(2, 0.2, "Vendor #:")
	fpdf.Cell(1, 0.2, vtaClaim.VendorID)
	fpdf.Ln(-1)

	fpdf.Cell(2, 0.2, "Customer Information:")

	fpdf.Cell(1, 0.2, vtaClaim.FirstName+" "+vtaClaim.LastName)
	fpdf.Ln(-1)

	fpdf.Cell(2, 0.2, "")
	fpdf.Cell(1, 0.2, vtaClaim.StreetAddress)
	fpdf.Ln(-1)

	fpdf.Cell(2, 0.2, "")
	fpdf.Cell(1, 0.2, vtaClaim.City+", "+vtaClaim.State+" "+vtaClaim.PostalCode)
	fpdf.Ln(-1)

	fpdf.Cell(2, 0.2, "Status:")
	fpdf.Cell(1, 0.2, vtaStatusMap[vtaClaim.Status])
	fpdf.Ln(-1)

	if vtaClaimPayment != nil {
		fpdf.Cell(2, 0.2, "Authorization #:")
		fpdf.SetX(2.5)
		fpdf.Cell(1, 0.2, strconv.Itoa(vtaClaimPayment.AuthorizationNumber))
		fpdf.Ln(-1)
		fpdf.Cell(2, 0.2, "Bill Number:")
		fpdf.SetX(2.5)
		fpdf.Cell(1, 0.2, vtaClaimPayment.BillNumber)
		fpdf.Ln(-1)

		if vtaClaimPayment.CheckNumber > 0 {
			fpdf.Cell(2, 0.2, "Check #:")
			fpdf.Cell(1, 0.2, strconv.Itoa(vtaClaimPayment.CheckNumber))
			fpdf.Ln(-1)
			fpdf.Cell(2, 0.2, "Amount:")
			fpdf.Cell(1, 0.2, "$ "+vtaClaimPayment.Amount.StringFixed(2))
			fpdf.Ln(-1)
			fpdf.Cell(2, 0.2, "Paid date:")
			fpdf.Cell(1, 0.2, vtaClaimPayment.PaidDate.Format(dateFormat))
			fpdf.Ln(-1)
		}
	}

}
