package vta

import (
	"bytes"
	"database/sql"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"phizz/conf"
	"phizz/db"
	"phizz/handlers"
	"phizz/s3util"
	"phizz/util"

	"github.com/go-chi/chi"
	"github.com/jmoiron/sqlx"
	newrelic "github.com/newrelic/go-agent"
	"github.com/pkg/errors"
)

type documentPayload struct {
	ID              int    `json:"-" db:"id"`
	VTAClaimID      int    `json:"vta_claim_id" db:"vta_claim_id"`
	FieldID         int    `json:"field_id" db:"field_id"`
	FileContent     string `json:"file_content" db:"-"`
	CreatedByUserID int    `json:"-" db:"created_by_user_id"`
	FileName        string `json:"file_name" db:"file_name"`
	FileType        string `json:"file_type" db:"-"`
	S3Bucket        string `json:"-" db:"s3_bucket"`
}

func (p documentPayload) validate() (map[string]string, error) {

	v := map[string]string{}
	if p.CreatedByUserID == 0 {
		v["created_by_user_id"] = "CreatedByUserID is required"
	}
	if p.VTAClaimID == 0 {
		v["vta_claim_id"] = "VTA claim ID is required."
	}
	if len(p.FileName) < 1 {
		v["file_name"] = "File name is required."
	}
	if len(p.FileType) < 1 {
		v["file_type"] = "File type is required."
	} else if !handlers.IsValidFileType(p.FileType) {
		v["file_type"] = "File type is not valid"
	}
	if len(p.FileContent) < 1 {
		v["file_content"] = "File content is required"
	} else if len(p.FileContent) > conf.Get().AWS.MaxSize {
		v["file_content"] = "File content size is more than " + strconv.Itoa(conf.Get().AWS.MaxSize)
	}
	if p.FieldID <= 0 {
		v["field_id"] = "Invalid Field ID"
	}
	return v, nil
}

// SaveDocument saves document to s3 and returns documentID from the vta_claim_documents table
func SaveDocument(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	p, err := getDocumentFromPayload(req.Body)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Bad request", nil)
	}

	p.clean()
	p.CreatedByUserID = user.ID
	formErrs, err := p.validate()
	if err != nil {
		err = errors.Wrap(err, "error validating document")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error during validation", nil)
	}
	if len(formErrs) > 0 {
		return http.StatusBadRequest, map[string]interface{}{
			"validation_errors": formErrs,
		}
	}
	txn, ok := w.(newrelic.Transaction)
	if !ok {
		err = errors.New("could not get New Relic transaction")
		handlers.ReportError(req, err)
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error uploading file", nil)
	}
	documentID, err := saveDocument(&p, txn)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error in saving document", nil)
	}
	return http.StatusOK, map[string]interface{}{"vta_claim_document": documentID}

}

func saveDocument(p *documentPayload, txn newrelic.Transaction) (int, error) {
	const errTxt = "Error saving Document"

	contractNumber := ""
	documentID := 0
	err := db.Get().Get(&contractNumber, "select contract_number from vta_claims where id = $1", p.VTAClaimID)
	if err != nil {
		if err == sql.ErrNoRows {
			return documentID, errors.Wrap(err, "The vta claim was not found")
		}
		return documentID, errors.Wrap(err, "Error loading vta claim from database.")
	}

	tx, err := db.Get().Beginx()
	if err != nil {
		err = errors.Wrap(err, "error starting document upload transaction")
		return documentID, errors.Wrap(err, errTxt)
	}

	if len(p.FileContent) > 0 {
		bucket := s3util.Bucket()
		p.S3Bucket = bucket
		name := "vta-claims/" + contractNumber + "/" + p.FileName + p.FileType
		p.FileName = name
		data, err := base64.StdEncoding.DecodeString(p.FileContent)
		if err != nil {
			_ = tx.Rollback()
			err = errors.Wrap(err, "error base64 decoding file data")
			return documentID, errors.Wrap(err, errTxt)
		}
		err = s3util.Put(txn, bytes.NewReader(data), s3util.DefaultRegion, p.S3Bucket, p.FileName)
		if err != nil {
			_ = tx.Rollback()
			err = errors.Wrap(err, "error uploading document file to s3")
			return documentID, errors.Wrap(err, errTxt)
		}
	}

	documentID, err = insertDocumentEntry(p, tx)
	if err != nil {
		_ = tx.Rollback()
		return documentID, errors.Wrap(err, errTxt)
	}

	err = tx.Commit()
	if err != nil {
		err = errors.Wrap(err, "error committing document transaction")
		return documentID, errors.Wrap(err, errTxt)
	}

	return documentID, nil
}

func insertDocumentEntry(payload *documentPayload, tx *sqlx.Tx) (int, error) {
	insertQuery := `insert into vta_claim_documents (vta_claim_id, field_id, s3_bucket, file_name, created_by_user_id, created_at) values
	 (:vta_claim_id, :field_id, :s3_bucket, :file_name, :created_by_user_id, now() at time zone 'utc') returning id`
	documentID := 0
	stmt, err := tx.PrepareNamed(insertQuery)
	if err != nil {
		err = errors.Wrap(err, "error creating document creating statement")
		return documentID, err
	}
	defer func() { _ = stmt.Close() }()

	err = stmt.Get(&documentID, payload)
	return documentID, err
}

func (p *documentPayload) clean() {
	s := strings.TrimSpace

	p.FileName = s(p.FileName)
	p.FileType = s(p.FileType)
}

func getDocumentFromPayload(reader io.Reader) (documentPayload, error) {
	var p documentPayload

	dec := json.NewDecoder(reader)
	err := dec.Decode(&p)
	if err != nil {
		err = errors.Wrap(err, "error decoding document payload")
		return p, err
	}

	return p, nil
}

// DocumentDownload returns a pre-signed S3 URL for a the document
func DocumentDownload(w http.ResponseWriter, req *http.Request, user db.User) {
	documentID, err := strconv.Atoi(chi.URLParam(req, "id"))
	if err != nil {
		fmt.Fprint(w, "Bad request. Could not read document ID.")
		return
	}

	cf := struct {
		S3Bucket string `db:"s3_bucket"`
		S3Key    string `db:"s3_key"`
	}{}
	err = db.Get().Get(&cf, `select s3_bucket, file_name s3_key from vta_claim_documents where deleted_at is null and id = $1`, documentID)
	if err != nil {
		handlers.ReportError(req, errors.Wrapf(err, "could not find vta_claim_documents with id %d", documentID))
		if err == sql.ErrNoRows {
			fmt.Fprint(w, "The document not found")
			return
		}
		fmt.Fprint(w, "Error loading document data for download")
		return
	}

	_, fileName := filepath.Split(cf.S3Key)
	extn := filepath.Ext(cf.S3Key)
	contentType := util.ContentTypeByExtension(extn)

	reverseProxy := s3util.GetS3ReverseProxy()
	signedURL, err := reverseProxy.GetSecureURL(
		s3util.DefaultRegion, cf.S3Bucket, url.PathEscape(cf.S3Key),
		url.PathEscape(fileName), contentType, user,
		time.Minute*conf.Get().S3ReverseProxy.DefaultLinkTimeoutMinutes)
	if err != nil {
		handlers.ReportError(req, err)
		fmt.Fprint(w, "Error in downloading document")
		return
	}

	http.Redirect(w, req, signedURL, http.StatusTemporaryRedirect)
}

// DocumentDelete deletes S3 object
func DocumentDelete(_ http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	id, err := strconv.Atoi(chi.URLParam(req, "id"))
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Bad request", nil)
	}

	err = documentDelete(id, user.ID)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Could not delete document", nil)
	}
	return http.StatusOK, map[string]interface{}{"vta_claim_document": id}
}

// documentDelete
func documentDelete(documentID, userID int) error {
	const errTxt = "Error deleting Document"

	documentData := struct {
		VTAClaimID int    `db:"vta_claim_id"`
		FileName   string `db:"file_name"`
		FieldID    int    `db:"field_id"`
	}{}
	err := db.Get().Get(&documentData, `select vta_claim_id, file_name, field_id from vta_claim_documents where deleted_at is null and id = $1`, documentID)
	if err != nil {
		if err == sql.ErrNoRows {
			return errors.Wrap(err, "The document not found")
		}
		return errors.Wrap(err, "error loading document data for delete")
	}

	tx, err := db.Get().Beginx()
	if err != nil {
		err = errors.Wrap(err, "error starting document delete transaction")
		return errors.Wrap(err, errTxt)
	}

	// delete document
	query := `update vta_claim_documents set deleted_at=now() at time zone 'utc', deleted_by_user_id=$1 where id = $2`
	_, err = tx.Exec(query, userID, documentID)
	if err != nil {
		_ = tx.Rollback()
		return errors.Wrap(err, "Error deleting document")
	}

	// add record note
	var recordNote recordNotePayload
	recordNote.ID = documentData.VTAClaimID
	recordNote.CreatedByUserID = userID
	recordNote.CreatedAt = time.Now()
	recordNote.NotesText = "Deleted the attached file " + documentData.FileName + " to the " + db.VTAFieldRecordNoteDescription[documentData.FieldID]
	_, err = insertRecordNote(&recordNote, nil)
	if err != nil {
		_ = tx.Rollback()
		return errors.Wrap(err, "Error adding notes for delete document")
	}

	err = tx.Commit()
	if err != nil {
		err = errors.Wrap(err, "error committing document transaction")
		return errors.Wrap(err, errTxt)
	}

	return nil
}

type fieldDocument struct {
	FieldID   int `json:"field_id" db:"-"`
	Count     int `json:"count" db:"-"`
	Documents []struct {
		ID        int       `db:"id" json:"id"`
		FieldID   int       `db:"field_id" json:"field_id"`
		FileName  string    `db:"file_name" json:"file_name"`
		CreatedAt time.Time `db:"created_at" json:"created_at"`
	} `json:"documents" db:"-"`
}

func fieldDocuments(vtaClaimID int) ([]fieldDocument, error) {
	exists, err := vtaClaimExists(vtaClaimID)
	if err != nil {
		return nil, errors.Wrap(err, "Error getting vta claim from database")
	}
	if !exists {
		return nil, errors.Wrap(err, "vta claim does not exist")
	}

	var fieldDocumentList []struct {
		ID        int       `db:"id" json:"id"`
		FieldID   int       `db:"field_id" json:"field_id"`
		FileName  string    `db:"file_name" json:"file_name"`
		CreatedAt time.Time `db:"created_at" json:"created_at"`
	}
	err = db.Get().Select(&fieldDocumentList, `select id, field_id, file_name, created_at
	from vta_claim_documents where deleted_at is null and vta_claim_id = $1 and field_id > 0 order by field_id asc`, vtaClaimID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.Wrap(err, "No field documents found for claim ")
		}
		return nil, errors.Wrap(err, "error loading field document list ")
	}

	var fieldDocuments []fieldDocument

	currentFieldID := 0
	numberOfFieldID := 0
	for _, currentResultObject := range fieldDocumentList {
		if currentResultObject.FieldID != currentFieldID {
			currentFieldID = currentResultObject.FieldID
			numberOfFieldID = numberOfFieldID + 1
			fieldDocuments = append(fieldDocuments, fieldDocument{})
		}
		fieldDocuments[numberOfFieldID-1].FieldID = currentFieldID
		fieldDocuments[numberOfFieldID-1].Documents = append(fieldDocuments[numberOfFieldID-1].Documents, currentResultObject)
		fieldDocuments[numberOfFieldID-1].Count = fieldDocuments[numberOfFieldID-1].Count + 1
	}

	return fieldDocuments, nil
}

// DocumentIndex returns all documents for given claim
func DocumentIndex(_ http.ResponseWriter, req *http.Request, _ db.User) (int, map[string]interface{}) {
	id, err := strconv.Atoi(chi.URLParam(req, "id"))
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Bad request", nil)
	}

	docs := struct {
		FieldDocuments []fieldDocument `json:"field_documents"`
	}{}
	docs.FieldDocuments, err = fieldDocuments(id)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting field document list", nil)
	}

	return http.StatusOK, map[string]interface{}{"docs": docs}
}

func addDocs(docs []documentPayload, claimID int, userID int, fieldID int, noteText string, tx *sqlx.Tx) error {
	for _, attachment := range docs {
		attachment.VTAClaimID = claimID
		attachment.FieldID = fieldID
		attachment.CreatedByUserID = userID
		_, err := insertDocumentEntry(&attachment, tx)
		if err != nil {
			return err
		}

		// add note record for file inserted
		_, err = insertRecordNote(&recordNotePayload{
			ID:              claimID,
			CreatedByUserID: userID,
			CreatedAt:       time.Now(),
			NotesText:       fmt.Sprintf(noteText, attachment.FileName),
		}, tx)

		if err != nil {
			return err
		}
	}
	return nil
}
