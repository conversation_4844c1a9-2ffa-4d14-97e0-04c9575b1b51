package vta

import (
	"database/sql"
	"time"

	"phizz/conf"
	"phizz/intacct"

	"github.com/shopspring/decimal"
	"gopkg.in/guregu/null.v3"
)

type worklistClaim struct {
	ID                  int       `json:"id" db:"id"`
	InsuredName         string    `json:"insured_name" db:"insured_name"`
	ContractNumber      string    `json:"contract_number" db:"contract_number"`
	AssignedTo          string    `json:"assigned_to" db:"assigned_to"`
	Status              string    `json:"status" db:"status"`
	DateOfClaimReceived time.Time `json:"date_of_claim_received" db:"date_of_claim_received"`
	DateOfLastIn        time.Time `json:"date_of_last_in" db:"date_of_last_in"`
}

type vtaClaimBase struct {
	ID                               int             `json:"id" db:"id"`
	VIN                              string          `json:"vin" db:"vin"`
	HasVendorID                      bool            `json:"has_vendor_id" db:"has_vendor_id"`
	VendorIDManagerFlag              bool            `json:"vendor_id_manager_flag" db:"vendor_id_manager_flag"`
	CreatedByUserID                  int             `json:"created_by_user_id" db:"created_by_user_id"`
	Status                           string          `json:"status" db:"status"`
	DeniedReason                     string          `json:"denied_reason" db:"denied_reason"`
	DateOfClaimReceived              time.Time       `json:"date_of_claim_received" db:"date_of_claim_received"`
	DateOfLastIn                     time.Time       `json:"date_of_last_in" db:"date_of_last_in"`
	CaseReserve                      decimal.Decimal `json:"case_reserve" db:"case_reserve"`
	HasCaseReserve                   bool            `json:"has_case_reserve" db:"has_case_reserve"`
	CaseReserveManagerFlag           bool            `json:"case_reserve_manager_flag" db:"case_reserve_manager_flag"`
	CustomerID                       int             `json:"customer_id" db:"customer_id"`
	FirstName                        string          `json:"first_name" db:"first_name"`
	LastName                         string          `json:"last_name" db:"last_name"`
	IsBusiness                       bool            `json:"is_business" db:"is_business"`
	BusinessName                     null.String     `json:"business_name" db:"business_name"`
	State                            string          `json:"state" db:"state"`
	City                             string          `json:"city" db:"city"`
	PostalCode                       string          `json:"postal_code" db:"postal_code"`
	StreetAddress                    string          `json:"street_address" db:"street_address"`
	EmailAddress                     string          `json:"email_address" db:"email_address"`
	PhoneNumber                      string          `json:"phone_number" db:"phone_number"`
	ContractNumber                   string          `json:"contract_number" db:"contract_number"`
	IsPoliceReportAvailable          bool            `json:"is_police_report_available" db:"is_police_report_available"`
	PoliceReportAvailableManagerFlag bool            `json:"police_report_available_manager_flag" db:"police_report_available_manager_flag"`
	HasSettlementCheck               bool            `json:"has_settlement_check" db:"has_settlement_check"`
	SettlementCheck                  decimal.Decimal `json:"settlement_check_value" db:"settlement_check_value"`
	SettlementCheckManagerFlag       bool            `json:"settlement_check_manager_flag" db:"settlement_check_manager_flag"`
	HasOriginalFinancing             bool            `json:"has_original_financing" db:"has_original_financing"`
	OriginalFinancingValue           decimal.Decimal `json:"original_financing_value" db:"original_financing_value"`
	OriginalFinancingManagerFlag     bool            `json:"original_financing_manager_flag" db:"original_financing_manager_flag"`
	HasVtaContract                   bool            `json:"has_vta_contract" db:"has_vta_contract"`
	VtaContractManagerFlag           bool            `json:"vta_contract_manager_flag" db:"vta_contract_manager_flag"`
	HasInsuranceNotCovered           bool            `json:"has_insurance_not_recovered" db:"has_insurance_not_recovered"`
	InsuranceNotCoveredManagerFlag   bool            `json:"insurance_not_recovered_manager_flag" db:"insurance_not_recovered_manager_flag"`
	OwnerID                          int             `json:"owner_id" db:"owner_id"`
	UpdatedAt                        time.Time       `json:"updated_at" db:"updated_at"`
	UpdatedByUserID                  int             `json:"updated_by_user_id" db:"updated_by_user_id"`
	UpdatedByUserName                string          `json:"updated_by_user_name" db:"updated_by_user_name"`
	IsInProgress                     bool            `json:"is_in_progress" db:"is_in_progress"`
}

type vtaClaimDB struct {
	vtaClaimBase
	VendorID sql.NullString `json:"vendor_id" db:"vendor_id"`
}

type vtaClaimPayload struct {
	vtaClaimBase
	VendorID string `json:"vendor_id" db:"vendor_id"`
}

func (claimDB vtaClaimDB) getVtaClaimPayload() *vtaClaimPayload {
	return &vtaClaimPayload{claimDB.vtaClaimBase, claimDB.VendorID.String}
}

// GetBill implement GetBill function of intacct ClaimPayload interface
func (claimDetails vtaClaimPayload) GetBill(billNumber string, billMemo string, batchKey int, description string) intacct.Bill {
	return intacct.Bill{
		VendorID:     claimDetails.VendorID,
		AccountLabel: conf.Get().IntacctVTA.AccountLabel,
		Amount:       claimDetails.CaseReserve,
		Memo:         billMemo,
		LocationID:   conf.Get().IntacctVTA.LocationID,
		TotalPaid:    decimal.NewFromFloat(0),
		TotalDue:     claimDetails.CaseReserve,
		ProjectID:    conf.Get().IntacctVTA.ProjectID,
		TermName:     conf.Get().IntacctVTA.TermName,
		BatchKey:     batchKey,
		BillNo:       billNumber,
		LoanNumber:   "",
		// Description text format : VTA claim for certificate "VTA Contract #" Acct # "Account Number", Last Name, First Name
		Description: description,
	}
}

// paymentInfo stores the information to vta_payment_information
type paymentInfo struct {
	ID                  int             `db:"id"`
	ClaimID             int             `db:"vta_claim_id"`
	PaymentKey          int             `db:"payment_key"`
	ContractNumber      string          `db:"contract_number"`
	ClaimDate           time.Time       `db:"date_of_claim_received"`
	Status              string          `db:"status"`
	AuthorizationNumber int             `db:"authorization_number"`
	CheckNumber         int             `db:"check_number"`
	CaseReserve         decimal.Decimal `db:"case_reserve"`
	Amount              decimal.Decimal `db:"amount"`
	PaidDate            time.Time       `db:"paid_date"`
	BillKey             int             `db:"bill_key"`
	BillNumber          string          `db:"bill_number"`
	UserID              string          `db:"owner_id"`
}
