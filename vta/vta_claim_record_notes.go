package vta

import (
	"encoding/json"
	"net/http"
	"strings"
	"time"

	"phizz/db"
	"phizz/handlers"

	"github.com/go-chi/chi"
	"github.com/jmoiron/sqlx"
	"github.com/pkg/errors"
)

type recordNotePayload struct {
	ID              int       `json:"id" db:"vta_claim_id"`
	NotesText       string    `json:"notes_text" db:"notes_text"`
	CreatedAt       time.Time `json:"created_at" db:"created_at"`
	CreatedByUserID int       `json:"created_by_user_id" db:"created_by_user_id"`
}

// RecordNoteCreate creates a new VTA Claim
func RecordNoteCreate(_ http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	recordNote, err := recordNoteFromReq(req)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(nil, "Malformed Record Note data for create.", nil)
	}

	cleanRecordNote(recordNote)
	formErrors, err := validateRecordNote(recordNote)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(errors.Wrap(err, "Error validating Record Note"),
			"An error occurred validating the form values.", nil)
	}

	if len(formErrors) > 0 {
		return http.StatusBadRequest, handlers.ErrorMessage(nil, "Form validations errors.",
			map[string]interface{}{"errors": formErrors})
	}

	recordNote.CreatedByUserID = user.ID
	recordNote.CreatedAt = time.Now()

	noteID, err := insertRecordNote(recordNote, nil)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error inserting RecordNote", nil)
	}

	return http.StatusOK, map[string]interface{}{"record_note_id": noteID}
}

func insertRecordNote(recordNote *recordNotePayload, tx *sqlx.Tx) (int, error) {
	insertQuery := `insert into vta_record_notes (created_at,vta_claim_id,notes_text,created_by_user_id) values (now() at time zone 'utc',:vta_claim_id,:notes_text,:created_by_user_id) returning id`
	id := 0

	var stmt *sqlx.NamedStmt
	var err error
	if tx != nil {
		stmt, err = tx.PrepareNamed(insertQuery)
	} else {
		stmt, err = db.Get().PrepareNamed(insertQuery)
	}
	if err != nil {
		return id, errors.Wrap(err, "An error occurred in PrepareNamed function while adding RecordNote.")
	}
	defer func() { _ = stmt.Close() }()

	err = stmt.Get(&id, recordNote)
	if err != nil {
		return id, errors.Wrap(err, "An error occurred while trying to add the RecordNote to the database.")
	}

	return id, err
}

// RecordNoteIndex returns a list of record notes for given contract number
func RecordNoteIndex(_ http.ResponseWriter, req *http.Request, _ db.User) (int, map[string]interface{}) {
	claimID := chi.URLParam(req, "vta_claim_id")
	var recordNotes []struct {
		VTAClaimID      int       `json:"vta_claim_id" db:"vta_claim_id"`
		NotesText       string    `json:"notes_text" db:"notes_text"`
		CreatedAt       time.Time `json:"created_at" db:"created_at"`
		CreatedByUserID int       `json:"created_by_user_id" db:"created_by_user_id"`
		FirstName       string    `json:"first_name" db:"first_name"`
		LastName        string    `json:"last_name" db:"last_name"`
	}

	var args []interface{}
	args = append(args, claimID)

	listQuery, countQuery := handlers.ListQueries(
		"vta_claim_id, notes_text, vta_record_notes.created_at, created_by_user_id, first_name, last_name",
		"vta_record_notes, users",
		"where vta_record_notes.created_by_user_id = users.id and vta_claim_id = $1",
		"order by created_at desc",
		handlers.PerPageEntries,
		handlers.GetPage(req),
	)

	err := db.Get().Select(&recordNotes, listQuery, args...)
	if err != nil {
		err = errors.Wrap(err, "Database error getting record notes lists")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting record notes lists data", nil)
	}

	count := 0
	err = db.Get().Get(&count, countQuery, args...)
	if err != nil {
		err = errors.Wrap(err, "Database error getting record notes lists count")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting record notes lists data", nil)
	}
	return http.StatusOK, map[string]interface{}{"record_notes": recordNotes, "count": count}
}

func recordNoteFromReq(req *http.Request) (*recordNotePayload, error) {
	recordNote := recordNotePayload{}

	dec := json.NewDecoder(req.Body)
	err := dec.Decode(&recordNote)

	return &recordNote, errors.Wrap(err, "decoding RecordNote request failed")
}

// cleanRecordNote cleans up leading and trailing white-space etc...
func cleanRecordNote(recordNote *recordNotePayload) {
	recordNote.NotesText = strings.TrimSpace(recordNote.NotesText)
}

// validateRecordNote validates a NewClaim record for correctness
func validateRecordNote(recordNote *recordNotePayload) (map[string]string, error) {
	formErrors := map[string]string{}

	if recordNote.ID == 0 {
		formErrors["vta_claim_id"] = "VTA claim id is required"
	}
	return formErrors, nil
}
