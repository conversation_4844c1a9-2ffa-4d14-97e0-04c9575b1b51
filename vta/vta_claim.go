package vta

import (
	"bytes"
	"context"
	"crypto/sha512"
	"database/sql"
	"encoding/csv"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"strconv"
	"strings"
	"time"

	"phizz/conf"
	"phizz/db"
	"phizz/handlers"
	"phizz/intacct"
	"phizz/nr"
	"phizz/types"

	"github.com/go-chi/chi"
	"github.com/jmoiron/sqlx"
	newrelic "github.com/newrelic/go-agent"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
	"gopkg.in/guregu/null.v3"
)

func (claim *worklistClaim) slice() []string {
	result := make([]string, 6)
	result[0] = claim.InsuredName
	result[1] = claim.ContractNumber
	result[2] = claim.Status
	result[3] = claim.AssignedTo
	result[4] = claim.DateOfClaimReceived.Format("2006-01-02")
	result[5] = claim.DateOfLastIn.Format("2006-01-02")
	return result
}

type claimListSlice []worklistClaim

func (claims claimListSlice) csv() string {
	result := new(bytes.Buffer)
	w := csv.NewWriter(result)
	w.Write([]string{"Insured Name", "Contract#", "Status", "Assigned To", "Opened", "Last In"})
	for _, claim := range claims {
		w.Write(claim.slice())
	}
	w.Flush()
	return result.String()
}

// ClaimIndex returns list of vta claims
func ClaimIndex(_ http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	claims := claimListSlice{}

	var whereClauses []string
	args := struct {
		SearchQuery string `db:"search_query"`
		OwnerID     int    `db:"owner_id"`
	}{}

	nameContractVin := req.FormValue("q")

	if nameContractVin != "" {
		whereClauses = append(whereClauses, `concat(c.first_name, ' ', c.last_name) ilike :search_query
		or concat(c.last_name, ' ', c.first_name) ilike :search_query
		or c.business_name ilike :search_query
		or contract_number ilike :search_query or vin ilike :search_query`)
		args.SearchQuery = "%" + strings.Join(strings.Fields(strings.TrimSpace(nameContractVin)), " ") + "%"
	}

	userID := req.FormValue("user_id")
	if userID != "" {
		ownerID, err := strconv.Atoi(userID)
		if err == nil {
			whereClauses = append(whereClauses, " vta_claims.owner_id = :owner_id")
			args.OwnerID = ownerID

		}
	}

	status := req.FormValue("status")
	if status != "" {
		statusQuery := mapVtaStatus(status)
		whereClauses = append(whereClauses, statusQuery)
	}

	age := req.FormValue("age")
	if age != "" && age != "all" {
		switch age {
		case "LessThan1Week":
			whereClauses = append(whereClauses, "(vta_claims.date_of_claim_received > NOW() at time zone 'utc' - interval '7 days')")
		case "OneToTwoWeeks":
			whereClauses = append(whereClauses, "(vta_claims.date_of_claim_received BETWEEN timezone('utc', now()) - interval '14 days' AND timezone('utc', now()) - interval '7 days')")
		case "TwoToThreeWeeks":
			whereClauses = append(whereClauses, "(vta_claims.date_of_claim_received BETWEEN timezone('utc', now()) - interval '21 days' AND timezone('utc', now()) - interval '14 days')")
		case "ThreeToFourWeeks":
			whereClauses = append(whereClauses, "(vta_claims.date_of_claim_received BETWEEN timezone('utc', now()) - interval '1 month' AND timezone('utc', now()) - interval '21 days')")
		case "GreaterThan1Month":
			whereClauses = append(whereClauses, "(vta_claims.date_of_claim_received < NOW() at time zone 'utc' - '1 month'::::interval)")
		default:
			return http.StatusBadRequest, handlers.ErrorMessage(errors.New("Invalid age filter"), "Error getting Vta claims lists data - Invalid age filter", nil)
		}
	}

	orderBy := "date_of_claim_received desc"
	sortBy := req.FormValue("sort_by")
	if sortBy != "" {
		orderBy = mapVtaSortByColumn(sortBy)
	}

	sortOrder := req.FormValue("sort_order")
	if sortBy != "" && sortOrder == "asc" || sortOrder == "desc" {
		orderBy = orderBy + " " + sortOrder
	}

	wh := ""
	if len(whereClauses) > 0 {
		wh = "where " + strings.Join(whereClauses, " and ")
	}

	selectClause := `vta_claims.id,
	case when c.is_business and (c.first_name!='' or c.last_name!='') then c.last_name || ',' || c.first_name || '/' || c.business_name
	when c.is_business and c.first_name='' and c.last_name='' then c.business_name
	else c.last_name || ',' || c.first_name end insured_name,
	contract_number, status,
	vta_claims.date_of_claim_received, vta_claims.date_of_last_in, 
	users.first_name || ' ' || users.last_name as assigned_to `

	fromClause := "vta_claims join customers c on customer_id = c.id join users on owner_id = users.id"

	countQuery := "select count(*) from " + fromClause + " " + wh

	// handle pagination
	p := req.FormValue("page")
	var listQuery string

	if n, err := strconv.Atoi(p); err == nil && n > 0 {
		listQuery = fmt.Sprintf("select %s from %s %s order by %s limit %d offset %d", selectClause, fromClause, wh, orderBy, handlers.PerPageEntries, (n-1)*handlers.PerPageEntries)
	} else { // if page number is not provided, return all values
		listQuery = fmt.Sprintf("select %s from %s %s order by %s", selectClause, fromClause, wh, orderBy)
	}

	stmt, err := db.Get().PrepareNamed(listQuery)
	if err != nil {
		err = errors.Wrap(err, "Database error preparing get claims list query")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting claims", nil)
	}
	defer func() { _ = stmt.Close() }()
	err = stmt.Select(&claims, args)
	if err != nil {
		err = errors.Wrap(err, "Database error getting Vta claims lists")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting vta claims lists data", nil)
	}

	stmt2, err := db.Get().PrepareNamed(countQuery)
	if err != nil {
		err = errors.Wrap(err, "Database error preparing automotive claims count query")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting claims count", nil)
	}
	defer func() { _ = stmt2.Close() }()
	count := 0
	err = stmt2.Get(&count, args)
	if err != nil {
		err = errors.Wrap(err, "Database error getting Vta claims lists count")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting Vta claims lists data", nil)
	}

	csv := req.FormValue("csv")
	if csv == "true" {
		claimsCsv := claims.csv()
		today := time.Now()
		fmt.Println("Worksheet_" + today.Format("2006_01_02T15_04_05") + ".csv")
		claims = nil
		return http.StatusOK, map[string]interface{}{"file": handlers.FilePayload{
			FileName: "Worksheet_" + today.Format("2006_01_02T15_04_05") + ".csv",
			Content:  claimsCsv,
		}}
	}

	return http.StatusOK, map[string]interface{}{"count": count, "vta_claims": claims}

}

// ClaimByContract returns the vtaclaim matching VTAContract#
func ClaimByContract(_ http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	contractNumber := chi.URLParam(req, "id")
	// Get the vta claim from database
	query := "select * from vta_claims where contract_number = $1"
	vtaClaim := vtaClaimDB{}

	err := db.Get().Unsafe().Get(&vtaClaim, query, contractNumber)
	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusNotFound, handlers.ErrorMessage(err, "The vta claim was not found", nil)
		}
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error loading VTA claim from database.", nil)
	}

	return http.StatusOK, map[string]interface{}{"vta_claim": vtaClaim.getVtaClaimPayload()}
}

// ClaimCounts returns count of claims for given filter
func ClaimCounts(_ http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	var claimsCount []struct {
		Name  string `json:"name" db:"name"`
		Count int    `json:"count" db:"count"`
		ID    int    `json:"id" db:"id"`
	}

	countBy := chi.URLParam(req, "countBy")

	countQuery := ""
	avgWaitingQuery := ""
	var args []interface{}
	switch countBy {
	case "status":
		countQuery = `select * from ( select (case
				when ` + mapVtaStatus("In Inquiry") + `
             			then 'In Inquiry'
				when ` + mapVtaStatus("In Process") + `
             			then 'In Process'
				when ` + mapVtaStatus("In Review") + `
             			then 'In Review'
				when ` + mapVtaStatus("In Finance") + `
             			then 'In Finance' end) as name, count(*)
             		      FROM vta_claims WHERE status in ($1,$2,$3,$4,$5,$6,$7,$8,$9)
             		      GROUP BY (case
				when ` + mapVtaStatus("In Inquiry") + `
             			then 'In Inquiry'
				when ` + mapVtaStatus("In Process") + `
             			then 'In Process'
				when ` + mapVtaStatus("In Review") + `
             			then 'In Review'
				when ` + mapVtaStatus("In Finance") + `
             			then 'In Finance' end)) x
             		      order by (case
             		        when name = 'In Inquiry' then 1
               			when name = 'In Process' then 2
				when name = 'In Review' then 3
               			when name = 'In Finance' then 4 end) ASC;`
		args = append(args,
			db.VtaClaimStatusInquiry,
			db.VtaClaimStatusPending,
			db.VtaClaimStatusReadyToProcess,
			db.VtaClaimStatusPendingReopened,
			db.VtaClaimStatusPendingDenial,
			db.VtaClaimStatusReturnedForCorrections,
			db.VtaClaimStatusWaitingForCheck,
			db.VtaClaimStatusWaitingForPayment,
			db.VtaClaimStatusWaitingForAuthorization)
	case "agent":
		countQuery = `select first_name || ' ' || last_name as name, count(*), users.id from vta_claims left join users on vta_claims.owner_id = users.id where status in ($1,$2,$3,$4,$5,$6,$7,$8) group by users.id ORDER BY count DESC`
		args = append(args,
			db.VtaClaimStatusPending,
			db.VtaClaimStatusReadyToProcess,
			db.VtaClaimStatusPendingReopened,
			db.VtaClaimStatusPendingDenial,
			db.VtaClaimStatusReturnedForCorrections,
			db.VtaClaimStatusWaitingForCheck,
			db.VtaClaimStatusWaitingForPayment,
			db.VtaClaimStatusWaitingForAuthorization)
	case "age":
		countQuery = `select * from (select (case
			  	when ((extract(epoch from age(vta_claims.date_of_claim_received))/3600)/24)/7 <= 1 then '< 1 Week'
				when ((extract(epoch from age(vta_claims.date_of_claim_received))/3600)/24)/7 > 1 and ((extract(epoch from age(vta_claims.date_of_claim_received))/3600)/24)/7 <= 2 then '1-2 Weeks'
				when ((extract(epoch from age(vta_claims.date_of_claim_received))/3600)/24)/7 > 2 and ((extract(epoch from age(vta_claims.date_of_claim_received))/3600)/24)/7 <= 3 then '2-3 Weeks'
				when ((extract(epoch from age(vta_claims.date_of_claim_received))/3600)/24)/7 > 3 and ((extract(epoch from age(vta_claims.date_of_claim_received))/3600)/24)/7 <= 4 then '3-4 Weeks'
				when ((extract(epoch from age(vta_claims.date_of_claim_received))/3600)/24)/7 > 4 then '> Month' end) as name, count(*)
  			      from vta_claims where vta_claims.status in ($1,$2,$3,$4,$5,$6,$7,$8)
 			      group by (case
			  	when ((extract(epoch from age(vta_claims.date_of_claim_received))/3600)/24)/7 <= 1 then '< 1 Week'
				when ((extract(epoch from age(vta_claims.date_of_claim_received))/3600)/24)/7 > 1 and ((extract(epoch from age(vta_claims.date_of_claim_received))/3600)/24)/7 <= 2 then '1-2 Weeks'
				when ((extract(epoch from age(vta_claims.date_of_claim_received))/3600)/24)/7 > 2 and ((extract(epoch from age(vta_claims.date_of_claim_received))/3600)/24)/7 <= 3 then '2-3 Weeks'
				when ((extract(epoch from age(vta_claims.date_of_claim_received))/3600)/24)/7 > 3 and ((extract(epoch from age(vta_claims.date_of_claim_received))/3600)/24)/7 <= 4 then '3-4 Weeks'
				when ((extract(epoch from age(vta_claims.date_of_claim_received))/3600)/24)/7 > 4 then '> Month' end)) x
             		      order by (case
             		        when name = '< 1 Week' then 1
               			when name = '1-2 Weeks' then 2
				when name = '2-3 Weeks' then 3
               			when name = '3-4 Weeks' then 4
               			when name = '> Month' then 5 end) ASC`
		avgWaitingQuery = `select ((extract(epoch from avg(AGE(now(),date_of_claim_received)))/3600)/24)/7 from vta_claims where vta_claims.status in ($1,$2,$3,$4,$5,$6,$7,$8)	`
		args = append(args,
			db.VtaClaimStatusPending,
			db.VtaClaimStatusReadyToProcess,
			db.VtaClaimStatusPendingReopened,
			db.VtaClaimStatusPendingDenial,
			db.VtaClaimStatusReturnedForCorrections,
			db.VtaClaimStatusWaitingForCheck,
			db.VtaClaimStatusWaitingForPayment,
			db.VtaClaimStatusWaitingForAuthorization)
	default:
		return http.StatusBadRequest, handlers.ErrorMessage(nil, "Invalid data passed", nil)
	}

	err := db.Get().Select(&claimsCount, countQuery, args...)
	if err != nil {
		err = errors.Wrap(err, "Database error getting vta claims lists count")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting vta claims lists count data for "+countBy, nil)
	}

	if avgWaitingQuery != "" {
		var avgWaitingPeriod sql.NullFloat64
		err = db.Get().Get(&avgWaitingPeriod, avgWaitingQuery, args...)
		if err != nil {
			err = errors.Wrap(err, "Database error getting vta claims average waiting count")
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting vta claims lists count data for "+countBy, nil)
		}
		claimAvgWaiting := decimal.NewFromFloat(avgWaitingPeriod.Float64).Ceil().String()
		return http.StatusOK, map[string]interface{}{"vta_claims": claimsCount, "average_waiting_period": claimAvgWaiting}
	}

	return http.StatusOK, map[string]interface{}{"vta_claims": claimsCount}
}

func mapVtaSortByColumn(input string) string {
	switch input {
	case "insured_name":
		return "insured_name"
	case "contract_number":
		return "contract_number"
	case "status":
		return "status"
	case "assigned_to":
		return "assigned_to"
	case "opened":
		return "date_of_claim_received"
	case "last_in":
		return "date_of_last_in"
	}
	return ""
}

func mapVtaStatus(status string) string {
	queryString := ""
	switch status {
	case "In Inquiry":
		queryString += fmt.Sprintf(" (status = '%s') ", db.VtaClaimStatusInquiry)
	case "In Process":
		queryString += fmt.Sprintf(" (status in ('%s','%s','%s','%s', '%s')) ",
			db.VtaClaimStatusPending, db.VtaClaimStatusPendingReopened, db.VtaClaimStatusPendingDenial,
			db.VtaClaimStatusReturnedForCorrections, db.VtaClaimStatusReadyToProcess)
	case "In Review":
		queryString += fmt.Sprintf(" (status = '%s') ", db.VtaClaimStatusWaitingForAuthorization)
	case "In Finance":
		queryString += fmt.Sprintf(" (status in ('%s','%s')) ", db.VtaClaimStatusWaitingForCheck,
			db.VtaClaimStatusWaitingForPayment)
	case "Closed":
		queryString += fmt.Sprintf(" (status in ('%s','%s','%s','%s')) ", db.VtaClaimStatusDeny, db.VtaClaimStatusCheckWritten, db.VtaClaimStatusCheckVoided, db.VtaClaimStatusClosedNoResponse)
	case "All Active":
		queryString += fmt.Sprintf(" (status not in ('%s','%s','%s')) ", db.VtaClaimStatusInquiry,
			db.VtaClaimStatusDeny, db.VtaClaimStatusCheckWritten)
	}
	return queryString
}

// ClaimShow returns details of vta claims for given claim id
func ClaimShow(_ http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	// get vta claim id
	id := chi.URLParam(req, "id")

	// get vta claim from database
	vtaClaimFromDB, err := getVtaClaimByID(id)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting VTA claim from database", nil)
	}

	vtaClaim := vtaClaimFromDB.getVtaClaimPayload()
	return http.StatusOK, map[string]interface{}{"vta_claim": vtaClaim}
}

// ClaimPayment returns the payment details for the vta claim
func ClaimPayment(_ http.ResponseWriter, req *http.Request) (int, map[string]interface{}) {
	id := chi.URLParam(req, "id")
	claimFromDB, err := getVtaClaimByID(id)

	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "VTA claim is not valid", nil)
	}

	query := ""
	if claimFromDB.Status == db.VtaClaimStatusWaitingForCheck {
		query = "select authorization_number, bill_number from vta_claim_payments where vta_claim_id = $1 order by id desc limit 1"
	} else if claimFromDB.Status == db.VtaClaimStatusCheckWritten || claimFromDB.Status == db.VtaClaimStatusCheckVoided {
		query = "select authorization_number, bill_number, check_number, amount, paid_date from vta_claim_payments where vta_claim_id = $1 order by id desc limit 1"
	} else {
		return http.StatusBadRequest, handlers.ErrorMessage(errors.New("Invalid payment request for vta claim"), "Invalid request", nil)
	}
	// Get the vta claim payment information from database
	vtaClaimPayments := struct {
		ID                  int             `json:"id" db:"id"`
		AuthorizationNumber int             `json:"authorization_number" db:"authorization_number"`
		BillNumber          string          `json:"bill_number" db:"bill_number"`
		CheckNumber         int             `json:"check_number" db:"check_number"`
		Amount              decimal.Decimal `json:"amount" db:"amount"`
		PaidDate            time.Time       `json:"paid_date" db:"paid_date"`
		BatchKey            int             `json:"batch_key" db:"batch_key"`
		BillKey             int             `json:"bill_key" db:"bill_key"`
		BillMemo            string          `json:"bill_memo" db:"bill_memo"`
	}{}
	err = db.Get().Get(&vtaClaimPayments, query, id)

	if err != nil {
		if err == sql.ErrNoRows {
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "The VTA claim payment was not found", nil)
		}
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error loading VTA claim payments from database.", nil)
	}

	return http.StatusOK, map[string]interface{}{"vta_claim_payment": vtaClaimPayments}
}

// get vta claim from database by vta claim id
func getVtaClaimByID(id string) (*vtaClaimDB, error) {
	query := "select vta_claims.*, customers.* " +
		"from vta_claims join customers on vta_claims.customer_id = customers.id " +
		"where vta_claims.id = $1 limit 1"

	vtaClaim := vtaClaimDB{}

	err := db.Get().Unsafe().Get(&vtaClaim, query, id)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.Wrap(err, "The VTA claim was not found")
		}
		return nil, errors.Wrap(err, "Error loading VTA claim from database.")
	}

	vtaClaim.ID, err = strconv.Atoi(id)
	if err != nil {
		return nil, errors.Wrap(err, "Converting VTA claim id to number failed")
	}

	lastUpdateQuery := `select vta_claim_updates.updated_at, updated_by_user_id, first_name, last_name
	 from vta_claim_updates join users on updated_by_user_id = users.id
	 where vta_claim_id = $1 order by updated_at desc limit 1`
	lastUpdate := struct {
		UpdatedAt       time.Time `db:"updated_at"`
		UpdatedByUserID int       `db:"updated_by_user_id"`
		FirstName       string    `db:"first_name"`
		LastName        string    `db:"last_name"`
	}{}
	err = db.Get().Get(&lastUpdate, lastUpdateQuery, id)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.Wrap(err, "Update for the VTA claim was not found")
		}
		return nil, errors.Wrap(err, "Error loading VTA claim updates from database.")
	}

	vtaClaim.UpdatedByUserID = lastUpdate.UpdatedByUserID
	vtaClaim.UpdatedAt = lastUpdate.UpdatedAt
	vtaClaim.UpdatedByUserName = strings.Join([]string{lastUpdate.FirstName, lastUpdate.LastName}, " ")

	return &vtaClaim, err
}

// ClaimCreate creates new vta claims with given vta contract details
func ClaimCreate(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	ctx := req.Context()
	vtaClaimCreatePayload := struct {
		ID             int    `json:"id"`
		ContractNumber string `json:"contract_number"`
		ProductCode    string `json:"product_code"`
	}{}
	dec := json.NewDecoder(req.Body)
	err := dec.Decode(&vtaClaimCreatePayload)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(nil, "Malformed VTA Claim data for create.", nil)
	}

	txn := w.(newrelic.Transaction)
	vtaClaim, err := getVtaClaimFromContractDetails(txn, vtaClaimCreatePayload.ID)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Error in getting contract details", nil)
	}
	cleanVtaClaimData(vtaClaim)

	formErrors, err := validateVtaClaim(ctx, vtaClaim, nil, db.ClaimValidateCreate)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err, err.Error(), nil)
	}
	if len(formErrors) > 0 {
		return http.StatusBadRequest, handlers.ErrorMessage(errors.New("Invalid input"), "Form validations errors.",
			map[string]interface{}{"errors": formErrors})
	}

	// Default values for fields
	vtaClaim.OwnerID = user.ID
	vtaClaim.CreatedByUserID = user.ID
	vtaClaim.Status = db.VtaClaimStatusInquiry
	vtaClaim.DeniedReason = ""

	tx, err := db.Get().Beginx()
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "error in beginning transaction for vta claim",
			nil)
	}

	// insert new claim
	id, err := insertVtaClaim(vtaClaim, tx)
	if err != nil {
		_ = tx.Rollback()
		return http.StatusInternalServerError, handlers.ErrorMessage(err, err.Error(), nil)
	}

	// add record notes for new claim
	_, err = insertRecordNote(&recordNotePayload{
		ID:              id,
		CreatedByUserID: user.ID,
		CreatedAt:       time.Now(),
		NotesText:       db.VTARecordNoteDescription[db.VtaClaimStatusNew],
	}, tx)
	if err != nil {
		_ = tx.Rollback()
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error adding notes for vta claim", nil)
	}

	// Add entry in update table for audit trail
	err = claimUpdated(id, vtaClaim.CreatedByUserID, tx)
	if err != nil {
		_ = tx.Rollback()
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "claim updated entry for audit trail failed", nil)
	}

	err = tx.Commit()
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error creating VTA claim", nil)
	}
	return http.StatusOK, map[string]interface{}{"id": id}
}
func insertVtaClaim(vtaClaim *vtaClaimPayload, tx *sqlx.Tx) (int, error) {
	insertQuery := `insert into vta_claims (
		vin,
		contract_number,
		status,
		denied_reason,
		date_of_claim_received,
		date_of_last_in,
		customer_id,
		created_by_user_id,
		is_police_report_available,
		has_settlement_check,
		settlement_check_value,
		has_original_financing,
		original_financing_value,
		has_vta_contract,
		has_insurance_not_recovered,
		owner_id)
		
		values (
		:vin,
		:contract_number,
		:status,
		:denied_reason,
		now() at time zone 'utc',
		now() at time zone 'utc',
		:customer_id,
		:created_by_user_id,
		:is_police_report_available,
		:has_settlement_check,
		:settlement_check_value,
		:has_original_financing,
		:original_financing_value,
		:has_vta_contract,
		:has_insurance_not_recovered,
		:owner_id
		) returning id`

	customerInsert := `insert into customers (
		first_name,
		last_name,
		is_business,
		business_name,
		email_address,
		phone_number,
		street_address,
		city,
		state,
		postal_code)

		values (
		:first_name,
		:last_name,
		:is_business,
		:business_name,
		:email_address,
		:phone_number,
		:street_address,
		:city,
		:state,
		:postal_code) returning id`

	// Prepare and execute customer info
	customerInsertStmt, err := tx.PrepareNamed(customerInsert)
	if err != nil {
		return 0, errors.Wrap(err, "PrepareNamed failed for customer")
	}
	defer func() { _ = customerInsertStmt.Close() }()
	err = customerInsertStmt.Get(&vtaClaim.CustomerID, vtaClaim)
	if err != nil {
		return 0, errors.Wrap(err, "Error creating customer")
	}

	// Prepare and execute
	stmt, err := tx.PrepareNamed(insertQuery)
	if err != nil {
		return 0, errors.Wrap(err, "PrepareNamed failed")
	}
	defer func() { _ = stmt.Close() }()
	id := 0
	err = stmt.Get(&id, vtaClaim)
	if err != nil {
		return id, errors.Wrap(err, "Scan error on ID after creating VTA claim")
	}
	return id, err
}

func validateVtaClaim(ctx context.Context, vtaClaim *vtaClaimPayload, vtaClaimDB *vtaClaimDB, requestType db.ClaimRequestType) (map[string]string, error) {
	formErrors := map[string]string{}
	var err error
	switch requestType {
	case db.ClaimValidateCreate:
		if vtaClaim.ContractNumber == "" {
			formErrors["contract_number"] = "Contract number is required"
		}
		if vtaClaim.VIN == "" {
			formErrors["vin"] = "VIN is required"
		}

		id := 0
		err := db.Get().Get(&id, `select id from vta_claims where contract_number = $1`, vtaClaim.ContractNumber)
		if err != nil && err != sql.ErrNoRows {
			return formErrors, errors.Wrap(err, "Database error in verifying uniqueness of claim")
		}
		if id != 0 {
			formErrors["claim_exists"] = "An earlier claim exists for this Contract."
		}
	case db.ClaimValidateDeny:
		if vtaClaim.DeniedReason == "" {
			formErrors["denied_reason"] = "Reason for denial is required"
		}
	case db.ClaimValidateSubmit:
		if vtaClaim.OwnerID == 0 {
			formErrors["owner_id"] = "OwnerID is required"
		}
		if vtaClaim.VendorID == "" {
			formErrors["vendor_id"] = "Vendor is required"
		}
		if vtaClaim.CaseReserve == decimal.Zero {
			formErrors["case_reserve"] = "CaseReserve is required"
		}
		// validate vendor id
		if vtaClaim.VendorID != "" {
			// if vendorId is present, validate vendor_id
			validVendor, venErr := isValidVendor(ctx, vtaClaim.VendorID)
			if venErr != nil {
				err = errors.Wrap(venErr, "Failed to validate vendor id from intacct")
			}
			if !validVendor {
				formErrors["vendor_id"] = "Invalid Vendor ID"
			}
		}
	case db.ClaimValidateAuthorize:
		if vtaClaim.Status != db.VtaClaimStatusWaitingForAuthorization {
			formErrors["status"] = "The status should be WA ( Waiting for Authorization )"
		}
	}
	return formErrors, err
}

func cleanVtaClaimData(vtaClaim *vtaClaimPayload) {
	vtaClaim.VIN = strings.TrimSpace(vtaClaim.VIN)
	vtaClaim.Status = strings.TrimSpace(vtaClaim.Status)
	vtaClaim.City = strings.TrimSpace(vtaClaim.City)
	vtaClaim.ContractNumber = strings.TrimSpace(vtaClaim.ContractNumber)
	vtaClaim.DeniedReason = strings.TrimSpace(vtaClaim.DeniedReason)
	vtaClaim.EmailAddress = strings.TrimSpace(vtaClaim.EmailAddress)
	vtaClaim.FirstName = strings.TrimSpace(vtaClaim.FirstName)
	vtaClaim.LastName = strings.TrimSpace(vtaClaim.LastName)
	vtaClaim.PhoneNumber = strings.TrimSpace(vtaClaim.PhoneNumber)
	vtaClaim.PostalCode = strings.TrimSpace(vtaClaim.PostalCode)
	vtaClaim.State = strings.TrimSpace(vtaClaim.State)
	vtaClaim.StreetAddress = strings.TrimSpace(vtaClaim.StreetAddress)
}

func getVtaClaimFromContractDetails(txn newrelic.Transaction, contractID int) (*vtaClaimPayload, error) {

	url := conf.Get().Whiz.BaseURL + "/ext/contracts/" + strconv.Itoa(contractID)

	if conf.Get().Whiz.Log {
		log.Println("[Whiz-contract-detail] request URL:", url)
	}

	whizReq, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, errors.Wrap(err, "could not create Whiz-contract-detail request")
	}

	whizReq.Header.Set("Content-Type", "application/json")
	whizReq.Header.Set("Accept", "application/json")
	salt := []byte(conf.Get().Whiz.AuthSalt)
	checksum := sha512.Sum512(salt)
	whizReq.Header.Set("Whiz-Checksum", hex.EncodeToString(checksum[:]))

	client := http.Client{Timeout: time.Second * 30}
	resp, err := nr.External(txn, client, whizReq)
	if err != nil {
		return nil, errors.Wrap(err, "Invalid response for Whiz-contract-detail")
	}
	defer resp.Body.Close()

	bodyBytes := make([]byte, 102400) // 100KB buffer for response
	n, err := io.ReadFull(resp.Body, bodyBytes)
	if err != nil && err != io.EOF && err != io.ErrUnexpectedEOF {
		return nil, errors.Wrap(err, "Invalid response for Whiz-contract-detail")
	}
	bodyBytes = bodyBytes[:n]
	if conf.Get().Whiz.Log {
		log.Println("[Whiz-contract-detail] response status:", resp.Status, "body:", string(bodyBytes))
	}

	if resp.StatusCode != http.StatusOK {
		return nil, errors.Wrap(err, "Invalid response for Whiz-contract-detail")
	}

	type data struct {
		Contract struct {
			Code            string `json:"code"`
			ProductTypeCode string `json:"product_type_code"`
		} `json:"contract"`
		CustomerDetails struct {
			ID           int         `json:"id"`
			FirstName    string      `json:"first_name"`
			LastName     string      `json:"last_name"`
			IsBusiness   bool        `json:"is_business"`
			BusinessName null.String `json:"business_name"`
			Address      string      `json:"address"`
			City         string      `json:"city"`
			StateCode    string      `json:"state_code"`
			PostalCode   string      `json:"postal_code"`
			Phone        string      `json:"phone"`
			Email        string      `json:"email"`
		} `json:"customer_details"`
		VehicleDetails struct {
			VIN  string `json:"vin"`
			Year string `json:"year"`
		} `json:"vehicle_details"`
		FinancingDetails struct {
			FinanceAmount types.JSNullDecimal `json:"finance_amount"`
		} `json:"financing_details"`
	}

	contractData := struct {
		Data data `json:"data"`
	}{}

	err = json.Unmarshal(bodyBytes, &contractData)
	if err != nil {
		return nil, errors.Wrap(err, "Invalid response for Whiz-contracts")
	}

	vtaClaimBase := vtaClaimBase{
		ContractNumber: contractData.Data.Contract.Code,
		VIN:            contractData.Data.VehicleDetails.VIN,
		FirstName:      contractData.Data.CustomerDetails.FirstName,
		LastName:       contractData.Data.CustomerDetails.LastName,
		IsBusiness:     contractData.Data.CustomerDetails.IsBusiness,
		BusinessName:   contractData.Data.CustomerDetails.BusinessName,
		State:          contractData.Data.CustomerDetails.StateCode,
		City:           contractData.Data.CustomerDetails.City,
		PostalCode:     contractData.Data.CustomerDetails.PostalCode,
		StreetAddress:  contractData.Data.CustomerDetails.Address,
		EmailAddress:   contractData.Data.CustomerDetails.Email,
		PhoneNumber:    contractData.Data.CustomerDetails.Phone,
	}
	if contractData.Data.FinancingDetails.FinanceAmount.Valid {
		vtaClaimBase.OriginalFinancingValue = contractData.Data.FinancingDetails.FinanceAmount.Decimal
	}

	vtaClaimPayload := vtaClaimPayload{vtaClaimBase: vtaClaimBase}
	return &vtaClaimPayload, nil
}

// ClaimUpdate updates vta claim details
func ClaimUpdate(_ http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	ctx := req.Context()
	claimID := chi.URLParam(req, "id")
	claimFromDB, err := getVtaClaimByID(claimID)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting VTA claim from database", nil)
	}

	claimStatusBeforeUpdate := claimFromDB.Status

	claimDetails := claimFromDB.getVtaClaimPayload()

	err = vtaClaimFromReq(claimDetails, req)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(nil, "Malformed VTA Claim data for update.", nil)
	}

	formErrors, err := validateVtaClaim(ctx, claimDetails, claimFromDB, db.VTAClaimValidationTypes[claimDetails.Status])
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err, err.Error(), nil)
	}
	if len(formErrors) > 0 {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Form validations errors.",
			map[string]interface{}{"errors": formErrors})
	}

	// return request, RC
	if claimDetails.Status == db.VtaClaimStatusReturnedForCorrections && claimStatusBeforeUpdate != db.VtaClaimStatusReturnedForCorrections {
		// In VTA claim return, the owner is changed from the current owner to the one who submitted claim
		// to "VtaClaimStatusWaitingForAuthorization" status
		lastOwner, err := getLastSubmittedByUser(claimDetails.ID)
		if err != nil {
			return http.StatusBadRequest, handlers.ErrorMessage(err, "Owner reassigning error", nil)
		}
		claimDetails.OwnerID = lastOwner
	}

	// deny request, D
	if claimDetails.Status == db.VtaClaimStatusDeny {
		formErrors, err := validateVtaClaim(ctx, claimDetails, claimFromDB, db.VTAClaimValidationTypes[claimDetails.Status])
		if err != nil {
			return http.StatusBadRequest, handlers.ErrorMessage(err, err.Error(), nil)
		}
		if len(formErrors) > 0 {
			return http.StatusBadRequest, handlers.ErrorMessage(err, "Form validations errors.",
				map[string]interface{}{"errors": formErrors})
		}
	}

	if claimDetails.Status == db.VtaClaimStatusClosedNoResponse {
		if claimStatusBeforeUpdate == db.VtaClaimStatusCheckWritten || claimStatusBeforeUpdate == db.VtaClaimStatusCheckVoided ||
			claimStatusBeforeUpdate == db.VtaClaimStatusDeny {
			return http.StatusBadRequest, handlers.ErrorMessage(errors.New("Can't change status to Closed-No Response from paid or closed"), "Can't change status to Closed-No Response from paid or closed", nil)
		}
	}

	tx, err := db.Get().Beginx()
	if err != nil {
		err = errors.Wrap(err, "Database error beginning transaction for VTA claim update")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error creating VTA claim update", nil)
	}

	var managerFlagUpdates string
	if claimStatusBeforeUpdate == db.VtaClaimStatusWaitingForAuthorization || claimStatusBeforeUpdate == db.VtaClaimStatusPendingDenial {
		managerFlagUpdates = `police_report_available_manager_flag = :police_report_available_manager_flag,
						settlement_check_manager_flag = :settlement_check_manager_flag,
						original_financing_manager_flag = :original_financing_manager_flag,
						vta_contract_manager_flag = :vta_contract_manager_flag,
						insurance_not_recovered_manager_flag = :insurance_not_recovered_manager_flag,
						vendor_id_manager_flag = :vendor_id_manager_flag, 
						case_reserve_manager_flag = :case_reserve_manager_flag,`
	}

	updateQuery := `update vta_claims
					set ` + managerFlagUpdates + `
					status = :status,
					settlement_check_value = :settlement_check_value,
					original_financing_value = :original_financing_value,
					denied_reason = :denied_reason,
					owner_id = :owner_id,
					is_police_report_available = :is_police_report_available,
					has_settlement_check = :has_settlement_check,
					has_original_financing = :has_original_financing,
					has_vta_contract = :has_vta_contract,
					has_insurance_not_recovered = :has_insurance_not_recovered,
					vendor_id = :vendor_id, 
					has_vendor_id = :has_vendor_id, 
					case_reserve = :case_reserve,
					has_case_reserve = :has_case_reserve,
					is_in_progress = :is_in_progress
					where id = :id`

	stmt, err := tx.PrepareNamed(updateQuery)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error updating VTA claim, PrepareNamed failed", nil)
	}
	defer func() { _ = stmt.Close() }()

	_, err = stmt.Exec(claimDetails)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error updating VTA claim, database error", nil)
	}

	// Add entry in update table for audit trail
	err = claimUpdated(claimDetails.ID, user.ID, tx)
	if err != nil {
		_ = tx.Rollback()
		return http.StatusInternalServerError, handlers.ErrorMessage(err,
			"claim updated entry for audit trail failed", nil)
	}

	if claimStatusBeforeUpdate != claimDetails.Status {
		recordNote := recordNotePayload{}
		recordNote.ID = claimDetails.ID
		recordNote.CreatedByUserID = user.ID
		recordNote.CreatedAt = time.Now()
		recordNote.NotesText = db.VTARecordNoteDescription[claimDetails.Status]
		_, err := insertRecordNote(&recordNote, tx)
		if err != nil {
			_ = tx.Rollback()
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error adding notes for vta claim", nil)
		}
	}

	// if vendor id is added or changed, add record notes
	if claimFromDB.VendorID.String != claimDetails.VendorID {
		recordNote := recordNotePayload{ID: claimDetails.ID, CreatedByUserID: user.ID, CreatedAt: time.Now()}
		if claimFromDB.VendorID.String == "" {
			recordNote.NotesText = fmt.Sprintf("Added Vendor Id - %s", claimDetails.VendorID)
		} else if claimDetails.VendorID == "" {
			recordNote.NotesText = fmt.Sprintf("Removed Vendor Id - %s", claimFromDB.VendorID.String)
		} else {
			recordNote.NotesText = fmt.Sprintf("Vendor Id is updated from %s to %s", claimFromDB.VendorID.String, claimDetails.VendorID)
		}
		_, err := insertRecordNote(&recordNote, tx)
		if err != nil {
			_ = tx.Rollback()
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error adding notes for vta claim", nil)
		}
	}

	// authorize request, submitToIntacct, WC
	if claimDetails.Status == db.VtaClaimStatusWaitingForCheck && claimStatusBeforeUpdate != db.VtaClaimStatusWaitingForCheck {
		err = vtaClaimAuthorize(ctx, tx, claimDetails, user.ID)
		if err != nil {
			_ = tx.Rollback()
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error in creating  VTA claim authorization request", nil)
		}
	}

	err = tx.Commit()
	if err != nil {
		err = errors.Wrap(err, "Database error committing transaction for VTA claim update")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database error creating VTA claim authorization", nil)
	}

	return http.StatusOK, map[string]interface{}{"vta_claim": claimID}
}

// isValidVendor will make intacct api call and return true if vendor is exists
func isValidVendor(ctx context.Context, vendorID string) (bool, error) {
	vendorQuery := intacct.VendorQueryPayload{}.VendorID(vendorID)
	vendors, err := intacct.GetVendors(ctx, vendorQuery)
	return len(vendors) == 1, err
}

// vtaClaimAuthorize will generate bill request to intacct and update payment table
func vtaClaimAuthorize(ctx context.Context, tx *sqlx.Tx, vtaClaim *vtaClaimPayload, userID int) error {

	authQuery := `insert into vta_claim_payments(vta_claim_id) values($1) returning authorization_number`
	row := tx.QueryRow(authQuery, vtaClaim.ID)
	authNumber := 0
	err := row.Scan(&authNumber)
	if err != nil {
		return errors.Wrap(err, "Database error inserting new payment request")
	}

	billMemo := vtaClaim.ContractNumber + " " + vtaClaim.LastName + "," + vtaClaim.FirstName
	description := "VTA claim for certificate VTA Contract #" + vtaClaim.ContractNumber + " " +
		vtaClaim.LastName + "," + vtaClaim.FirstName
	billNumber := vtaClaim.ContractNumber + "_" + strconv.Itoa(authNumber)
	vtaClaimPayment, err := intacct.SubmitBill(ctx, tx, vtaClaim, billNumber, billMemo, description)
	if err != nil {
		return errors.Wrap(err, "Submit to Intacct failed")
	}

	updateQuery := `update vta_claim_payments set batch_key = $1, bill_key = $2, bill_memo = $3, bill_number = $4  where authorization_number = $5`
	_, err = tx.Exec(
		updateQuery,
		vtaClaimPayment.BatchKey,
		vtaClaimPayment.BillKey,
		billMemo,
		vtaClaim.ContractNumber, // Send Contract Number as bill Number
		authNumber,
	)
	if err != nil {
		return errors.Wrap(err, "Database error updating vta payment details")
	}

	updateClaimStatus := `update vta_claims set status = $1 where id = $2`
	_, err = tx.Exec(updateClaimStatus, db.VtaClaimStatusWaitingForCheck, vtaClaim.ID)
	if err != nil {
		return errors.Wrap(err, "Error updating vta claim status")
	}

	if err != nil {
		return errors.Wrap(err, "Database error committing transaction for VTA claim authorization")
	}
	return err
}

func vtaClaimFromReq(vtaClaim *vtaClaimPayload, req *http.Request) error {
	dec := json.NewDecoder(req.Body)
	err := dec.Decode(&vtaClaim)
	return errors.Wrap(err, "decoding VTA Claim request failed")
}

func vtaClaimExists(vtrClaimID int) (bool, error) {
	id := 0
	err := db.Get().Get(&id, `select id from vta_claims where id=$1`, vtrClaimID)
	if err != nil {
		if err == sql.ErrNoRows {
			return false, nil
		}
		return false, err
	}
	return true, nil
}

func getLastSubmittedByUser(claimID int) (int, error) {
	userID := 0
	query := `select created_by_user_id from vta_record_notes where vta_claim_id = $1 and (notes_text = $2 or notes_text = $3 or notes_text = $4 or notes_text = $5 or notes_text = $6) order by created_at desc limit 1`
	err := db.Get().Get(&userID,
		query,
		claimID,
		db.VTARecordNoteDescription[db.VtaClaimStatusWaitingForAuthorization],
		db.VTARecordNoteDescription[db.VtaClaimStatusPendingDenial],
		db.VTARecordNoteDescription[db.VtaClaimStatusClosedNoResponse],
		db.VTARecordNoteDescription[db.VtaClaimStatusPending],
		db.VTARecordNoteDescription[db.VtaClaimStatusNew],
	)
	if err != nil {
		err = errors.Wrap(err, "Database error getting record notes for claim")
	}
	return userID, err
}

func gapClaimsByVIN(vin string) ([]int, error) {
	var gapClaimIDs []int
	err := db.Get().Select(&gapClaimIDs, `select id from gap_claims where vin = $1 and is_child = false`, vin)
	return gapClaimIDs, err
}

// UpdateFromGap function syncs vta claim with gap
// police report and settlement details are copied from gap to vta
func UpdateFromGap(_ http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	claimID := chi.URLParam(req, "id")
	claimDetails, err := getVtaClaimByID(claimID)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting VTA claim from database", nil)
	}

	gapClaimIDs, err := gapClaimsByVIN(claimDetails.VIN)

	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "error getting gap claim", nil)
	}

	if len(gapClaimIDs) > 1 {
		return http.StatusInternalServerError, handlers.ErrorMessage(errors.New("Multiple Gap claims found"), "Multiple Gap claims found", nil)
	} else if len(gapClaimIDs) == 0 {
		return http.StatusNotFound, handlers.ErrorMessage(errors.New("Gap claim not available against vin"), "Gap claim not available against vin", nil)
	}

	gapID := gapClaimIDs[0]

	var gapSettlementAmount decimal.Decimal
	err = db.Get().Get(&gapSettlementAmount, `select settlement_amount from gap_claims where id = $1`, gapID)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "error getting settlement amount", nil)
	}

	var policeReportDocs []documentPayload
	var settlementDocs []documentPayload
	// Get attachments from gap
	gapDocumentsQuery := `select file_name, s3_bucket from gap_claim_documents
                              where gap_claim_id = $1
                                and gap_claim_documents.field_id = $2
                                and deleted_at is null
                                and file_name NOT IN
                                    (select vta_claim_documents.file_name
                                     from vta_claim_documents
				     where vta_claim_documents.field_id = $3
                                       and vta_claim_documents.vta_claim_id = $4
                                       and vta_claim_documents.deleted_at isnull);`

	err = db.Get().Select(&policeReportDocs, gapDocumentsQuery, gapID, db.FieldIDPoliceReport, db.VTAFieldIDPoliceReport, claimID)
	if err != nil && err != sql.ErrNoRows {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "error loading police report document list ", nil)
	}

	err = db.Get().Select(&settlementDocs, gapDocumentsQuery, gapID, db.FieldIDSettlementAmount, db.VTAFieldIDSettlementCheck, claimID)
	if err != nil && err != sql.ErrNoRows {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "error loading settlement amount document list ", nil)
	}

	tx, err := db.Get().Beginx()
	if err != nil {
		err = errors.Wrap(err, "error starting document upload transaction")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "error starting update claim transaction", nil)
	}

	id, _ := strconv.Atoi(claimID)

	err = addDocs(policeReportDocs, id, user.ID, db.VTAFieldIDPoliceReport, "Police report attachment %s synced from gap", tx)
	if err != nil {
		_ = tx.Rollback()
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "error updating settlement check attachments from gap", nil)
	}

	err = addDocs(settlementDocs, id, user.ID, db.VTAFieldIDSettlementCheck, "Settlement check attachment %s synced from gap", tx)
	if err != nil {
		_ = tx.Rollback()
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "error updating settlement check attachments from gap", nil)
	}

	// if SettlementCheck value is changed, then update SettlementCheck change in vta claim
	if !claimDetails.SettlementCheck.Equal(gapSettlementAmount) {
		_, err = tx.Exec(`update vta_claims set settlement_check_value=$1 where id =$2`, gapSettlementAmount, claimID)
		if err != nil {
			_ = tx.Rollback()
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Update of settlement amount failed", nil)
		}

		// add record note
		_, err = insertRecordNote(&recordNotePayload{
			ID:              id,
			CreatedByUserID: user.ID,
			CreatedAt:       time.Now(),
			NotesText:       "Settlement check value synced from gap",
		}, tx)
		if err != nil {
			_ = tx.Rollback()
			return http.StatusInternalServerError, handlers.ErrorMessage(err, "Update of settlement amount failed", nil)
		}
	}

	err = claimUpdated(id, user.ID, tx)
	if err != nil {
		_ = tx.Rollback()
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Claim updated entry for audit trail failed", nil)
	}

	err = tx.Commit()
	if err != nil {
		err = errors.Wrap(err, "error committing document transaction")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Failed to commit gap sync", nil)
	}

	return http.StatusOK, map[string]interface{}{"vta_claim": claimID}
}

// GapClaimExist return a success status if same vin has gap claim and vta claim
func GapClaimExist(w http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	claimID := chi.URLParam(req, "id")
	claimDetails, err := getVtaClaimByID(claimID)
	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Error getting VTA claim from database", nil)
	}

	gapClaimIDs, err := gapClaimsByVIN(claimDetails.VIN)

	if err != nil {
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "error getting gap claim", nil)
	}

	if len(gapClaimIDs) > 1 {
		return http.StatusInternalServerError, handlers.ErrorMessage(errors.New("Multiple Gap claims found"), "Multiple Gap claims found", nil)
	} else if len(gapClaimIDs) == 0 {
		return http.StatusNotFound, handlers.ErrorMessage(errors.New("Gap claim not available against vin"), "Gap claim not available against vin", nil)
	}

	return http.StatusOK, map[string]interface{}{}
}

// ClaimReopen reopen claim and change the status to inquiry
func ClaimReopen(_ http.ResponseWriter, req *http.Request, user db.User) (int, map[string]interface{}) {
	claimID, err := strconv.Atoi(chi.URLParam(req, "vta_claim_id"))
	if err != nil || claimID == 0 {
		return http.StatusBadRequest, handlers.ErrorMessage(nil, "Claim Id is invalid", nil)
	}
	status := ""
	claimQuery := "select status from vta_claims where id = $1"
	err = db.Get().Get(&status, claimQuery, claimID)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error while getting claim status"))
		return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Database update failed", nil)
	}

	if status != db.VtaClaimStatusDeny {
		return http.StatusBadRequest, handlers.ErrorMessage(nil, "Invalid Claim Status", nil)
	}

	// if no attachment then status will be `VtaClaimStatusInquiry` else it will be `VtaClaimStatusPending`
	count := 0
	countQuery := "select count(*) from vta_claim_documents where vta_claim_id = $1"
	err = db.Get().Get(&count, countQuery, claimID)
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "error while fetching documents counts"))
		return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Database update failed", nil)
	}

	newStatus := db.VtaClaimStatusInquiry
	if count > 0 {
		newStatus = db.VtaClaimStatusPending
	}

	lastOwnerID, err := getLastSubmittedByUser(claimID)
	if err != nil {
		return http.StatusBadRequest, handlers.ErrorMessage(err, "Owner reassigning error", nil)
	}

	tx, err := db.Get().Beginx()
	if err != nil {
		handlers.ReportError(req, errors.Wrap(err, "database error beginning transaction for Auto claim reassignment"))
		return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Database update failed", nil)
	}
	updateQuery := "update vta_claims set owner_id = $1, status = $2 where id = $3"
	_, err = tx.Exec(updateQuery, lastOwnerID, newStatus, claimID)
	if err != nil {
		_ = tx.Rollback()
		handlers.ReportError(req, errors.Wrap(err, "database error - failed to update claim status"))
		return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Database update failed", nil)
	}

	// Claim updated history
	err = claimUpdated(claimID, user.ID, tx)
	if err != nil {
		_ = tx.Rollback()
		handlers.ReportError(req, errors.Wrap(err, "database error - inserting automotive_claim_updates"))
		return http.StatusInternalServerError, handlers.ErrorMessage(nil, "Database update failed", nil)
	}

	// add record notes for new claim
	_, err = insertRecordNote(&recordNotePayload{
		ID:              claimID,
		CreatedByUserID: user.ID,
		CreatedAt:       time.Now(),
		NotesText:       db.VTARecordNoteDescription[newStatus],
	}, tx)

	if err != nil {
		_ = tx.Rollback()
		handlers.ReportError(req, errors.Wrap(err, "database error - inserting automotive_claim_updates"))
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database update failed", nil)
	}

	err = tx.Commit()
	if err != nil {
		err = errors.Wrap(err, "Database error committing transaction for Auto claim reassign")
		return http.StatusInternalServerError, handlers.ErrorMessage(err, "Database update failed", nil)
	}

	return http.StatusOK, map[string]interface{}{}
}

// claimUpdated : Add entry in update table for audit trail
func claimUpdated(claimID int, updatedByUserID int, tx *sqlx.Tx) error {
	updateInsert := `insert into vta_claim_updates(vta_claim_id, updated_by_user_id, updated_at) values ($1, $2, now() AT TIME ZONE 'utc')`
	_, err := tx.Exec(updateInsert, claimID, updatedByUserID)
	return err
}
