#!/bin/sh

. ./dump_config.sh

if [ -z "$db_host" ]
then
  echo 'Missing db_host. Make sure to `cp dump_config-sample.sh dump_config.sh`'
  exit 1
fi
if [ -z "$db_user" ]
then
  echo 'Missing db_user. Make sure to `cp dump_config-sample.sh dump_config.sh`'
  exit 1
fi
if [ -z "$db_name" ]
then
  echo 'Missing db_name. Make sure to `cp dump_config-sample.sh dump_config.sh`'
  exit 1
fi
if [ -z "$db_pass" ]
then
  echo 'Missing db_pass. Make sure to `cp dump_config-sample.sh dump_config.sh`'
  exit 1
fi
if [ -z "$shallow_dump_file_name" ]
then
  echo 'Missing shallow_dump_file_name. Make sure to `cp dump_config-sample.sh dump_config.sh`'
  exit 1
fi

echo "dumping the database schema..."
pg_dump -U $db_user "****************************************************************" --clean --no-owner --no-privileges --schema-only > $shallow_dump_file_name

echo "dumping non-growth data"
pg_dump -U $db_user "****************************************************************" --data-only \
-t automotive_claim_complaints \
 -t gap_claims \
 -t credit_cards \
 -t credit_cards_record_notes \
 -t automotive_claim_coverage \
 -t gap_claim_field_notes \
 -t automotive_complaint_repair_codes \
 -t user_approved_limits \
 -t automotive_claims \
 -t customers \
 -t automotive_claim_payees \
 -t automotive_claim_payment_checks \
 -t automotive_facilities \
 -t automotive_claim_payments \
 -t gap_claim_recoveries \
 -t vta_record_notes \
 -t users \
 -t vta_claim_field_notes \
 -t vta_claim_documents \
 -t vin_records \
 -t email_templates \
 -t stores \
 -t intacct_bill_batches \
 -t gap_recovery_comments \
 -t automotive_claim_documents \
 -t automotive_claim_complaint_labors \
 -t automotive_facility_zones \
 -t automotive_claim_complaint_parts \
 -t gap_claim_contracts \
 -t gap_claim_contract_field_notes \
 -t companies \
 -t gomigrate \
 -t gap_intacct_batches \
 -t gap_intacct_batch_details \
 -t gap_claim_documents \
 -t vta_claim_updates \
 -t banks \
 -t gap_claim_updates \
 -t automotive_claim_updates \
 -t record_notes \
 -t gap_claim_payments \
 -t automotive_record_notes \
 -t email_template_updates \
 -t automotive_intacct_batches \
 -t automotive_intacct_batch_details \
 -t automotive_intacct_bill_batches \
 -t gap_insurance_companies \
 -t vta_claims \
 -t customers_record_notes \
 -t vta_claim_payments \
 -t automotive_facility_record_notes \
 -t customer_users \
 -t customer_claim_ratings \
 -t automotive_facilities_stage  >> $shallow_dump_file_name