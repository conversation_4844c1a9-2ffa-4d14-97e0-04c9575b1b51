#!/bin/bash

########################################################################################
########################################################################################
#
# Code Changes Audit Script
#
# This is for generating the repot for Audit Control [TCA.APP.MC4.1]
# This report is to be generated once a month using the last deploy tag from the previous
# month as the starting point.
#
# previous_tag - Is the last Code Deploy tag for the previous audit period. You can get
#				 this tag from going to the Jira Releases and looking at the most recent
#				 releases of the previous month for the last time this repo was deployed
#				 to production.

previous_tag=$1
previous_tag_found=0

if [ -z "$previous_tag" ]
then
	echo 'Missing previous_tag. The previous code deploy tag needs to be passed in when executing this script.'
	exit 1
fi

echo "Fetching git changes"
git fetch origin
echo "Checking out master branch"
git checkout master
echo "Pulling latest changes on master branch"
git pull
echo "Getting recent tags"
TAGS=$(git --no-pager tag -n1 --merged | sort -r | head -20 | awk '{print $1}' | sort)

for tag in $TAGS; do 
	if [ "${previous_tag_found}" -ne 1 ]; then
		if [ "${previous_tag}" == "${tag}" ]; then
			echo "Previous tag (${previous_tag}) found."
			previous_tag_found=1
		fi
		continue
	fi

	filename=phizz_changes_${tag}_$( date '+%Y-%m-%d').csv
	echo "Generating audit log for tag (${tag})..."
	echo "Date,Author,Commit,Subject" > ${filename}
	git --no-pager \
	    log \
	    --full-history \
	    --decorate --pretty=tformat:"\"%ad\"%x2c\"%an\"%x2c\"%h\"%x2c\"%d %s\"" \
	    ${previous_tag}..${tag} \
	    | tee -a ${filename} | wc -l | xargs -I{} echo "Captured {} change(s) in ${filename}."
	echo "Generated audit log for tag (${tag})..."
	previous_tag=$tag
	echo "New Previous Tag is: ${previous_tag}"
done

echo "Bundling audit files into whiz_changes.zip..."
zip phizz_changes_$( date '+%Y-%m-%d').zip phizz_changes_*.csv

echo "Cleaning up audit files..."
rm phizz_changes_*.csv

