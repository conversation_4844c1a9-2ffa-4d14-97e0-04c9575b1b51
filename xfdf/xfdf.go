package xfdf

import (
	"encoding/xml"

	"github.com/pkg/errors"
)

type xmlStr struct {
	XMLName  xml.Name  `xml:"xfdf"`
	XMLNS    string    `xml:"xmlns,attr"`
	XMLSpace string    `xml:"xml:space,attr"`
	Fields   xmlFields `xml:"fields"`
}

type xmlFields struct {
	Fields []xmlField `xml:"field"`
}

type xmlField struct {
	Name  string `xml:"name,attr"`
	Value string `xml:"value"`
}

// Format will use the given fields and return a byte-slice for XFDF.
func Format(fields map[string]string) ([]byte, error) {
	xfdf := xmlStr{
		XMLNS:    "http://ns.adobe.com/xfdf/",
		XMLSpace: "preserve",
		Fields: xmlFields{
			Fields: []xmlField{},
		},
	}
	for name, value := range fields {
		xfdf.Fields.Fields = append(xfdf.<PERSON>.<PERSON>, xmlField{Name: name, Value: value})
	}
	b, err := xml.Marshal(xfdf)
	if err != nil {
		return nil, errors.Wrap(err, "error marshaling XML for XFDF")
	}
	return append([]byte(xml.Header), b...), nil
}
