module.exports = {
  "parser": "@babel/eslint-parser",
  "env": {
    "browser": true,
    "commonjs": true,
    "es6": true
  },
  "extends": [
    "eslint:recommended",
    "plugin:react/recommended",
    "plugin:cypress/recommended",
  ],
  "parserOptions": {
    "ecmaFeatures": {
      "experimentalObjectRestSpread": true,
      "jsx": true
    },
    "sourceType": "module",
    "requireConfigFile": false,
    "babelOptions": {
      "presets": ["@babel/preset-react"]
    }
  },
  "settings": {
    "react": {
      "version": "detect"
    }
  },
  "plugins": [
    "react",
    "react-hooks",
    "cypress",
    "chai-friendly"
  ],
  "rules": {
    "no-console": ["error"],
    "no-unused-vars": ["error", { "vars": "all", "args": "none" }],
    "indent": ["error", 2 ],
    "linebreak-style": ["error", "unix"],
    "semi": ["error", "always"],
    "strict": ["error", "global"],
    "react-hooks/rules-of-hooks": "error",
    "react/prop-types": ["warn"],
    "no-prototype-builtins": ["warn"],
  },
  "overrides": [{
    "files": ["cypress/**/*.js"],
    "rules": {
      "semi": ["off"],
      "cypress/no-unnecessary-waiting": ["off"],
      "no-unused-expressions": ["off"],
      "chai-friendly/no-unused-expressions": ["error"]
    }
  }]
};
