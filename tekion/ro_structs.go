package tekion

import (
	"time"
)

const (
	customerCustomerTypeIndividual = "INDIVIDUAL"
	customerCustomerTypeBusiness   = "BUSINESS"

	phoneTypeWork = "WORK"
	phoneTypeHome = "HOME"
	phoneTypeCell = "CELL"
)

type roRequest struct {
	request
	RONumber int `json:"roNo"`
}

type roResponse struct {
	Meta responseMeta `json:"meta"`
	Data []*roData    `json:"data"`
}

type roData struct {
	ID                   string      `json:"id"`
	RepairOrderNumber    string      `json:"repairOrderNumber,omitempty"`
	CreatedTimeEpoch     int64       `json:"createdTime,omitempty"`
	ModifiedTimeEpoch    int64       `json:"modifiedTime,omitempty"`
	IsDeleted            bool        `json:"isDeleted,omitempty"`
	LastStatus           string      `json:"lastStatus,omitempty"`
	TagNumber            string      `json:"tagNumber,omitempty"`
	Type                 string      `json:"type,omitempty"`
	AdvisorID            string      `json:"advisorId,omitempty"`
	PrimaryAdvisor       []*employee `json:"primaryAdvisor,omitempty"`
	Status               string      `json:"status,omitempty"`
	AppointmentID        string      `json:"appointmentId,omitempty"`
	AppointmentNumber    string      `json:"appointmentNumber,omitempty"`
	TransportType        string      `json:"transportType,omitempty"`
	DepartmentID         string      `json:"departmentId,omitempty"`
	TeamID               string      `json:"teamId,omitempty"`
	IsHold               bool        `json:"isHold,omitempty"`
	HoldReason           string      `json:"holdReason,omitempty"`
	HoldTimeEpoch        int64       `json:"holdTime,omitempty"`
	UnHoldTimeEpoch      int64       `json:"unHoldTime,omitempty"`
	UnHoldBy             string      `json:"unHoldBy,omitempty"`
	Priority             int         `json:"priority,omitempty"`
	CalculatedPriority   int         `json:"calculatePriority,omitempty"`
	IsPriorityOverridden bool        `json:"isPriorityOverridden,omitempty"`
	CheckInTimeEpoch     int64       `json:"checkinTime,omitempty"`
	PromiseTimeEpoch     int64       `json:"promiseTime,omitempty"`
	Fees                 []*roFee    `json:"fees"`
	// Coupons is defined as an array of objects, but no information on what the object structure is,
	// so commenting out the definition of the Coupons field for now.
	// Coupons []
	Customer          *roCustomer `json:"customer"`
	DropOffCustomer   *roCustomer `json:"dropOffCustomer"`
	BillingCustomer   *roCustomer `json:"billingCustomer"`
	Vehicle           *roVehicle  `json:"vehicle"`
	ReopenReason      string      `json:"reopenReason,omitempty"`
	ReopenedBy        string      `json:"reopenedBy,omitempty"`
	ReopenedTimeEpoch int64       `json:"reopenedTime,omitempty"`
	VoidReason        string      `json:"voidReason,omitempty"`
	VoidedBy          string      `json:"voidedBy,omitempty"`
	VoidedTime        string      `json:"voidedTime,omitempty"`
	DeclinedBy        string      `json:"declinedBy,omitempty"`
	DeclineReason     string      `json:"declineReason,omitempty"`
	DeclinedTime      int64       `json:"declinedTime,omitempty"`
	ClosedBy          string      `json:"closedBy,omitempty"`
	ClosedTimeEpoch   int64       `json:"closedTime,omitempty"`
	InternalNotes     string      `json:"internalNotes,omitempty"`
	ExternalNotes     string      `json:"externalNotes,omitempty"`
	Invoice           *invoice    `json:"invoice"`
	Jobs              []*job      `json:"jobs"`
	Department        *department `json:"department,omitempty"`
}

// getCreatedTime will return the time.Time representation of the created time and a boolean indicating if the time is valid.
func (r *roData) getCreatedTime() (time.Time, bool) {
	return getTimeFromEpoch(r.CreatedTimeEpoch)
}

// getReopenedTime will return the time.Time representation of the reopened time and a boolean indicating if the time is valid.
func (r *roData) getReopenedTime() (time.Time, bool) {
	return getTimeFromEpoch(r.ReopenedTimeEpoch)
}

type department struct {
	ID   string   `json:"id,omitempty"`
	Name string   `json:"name,omitempty"`
	Type []string `json:"type,omitempty"`
	Code []string `json:"code,omitempty"`
}

type employee struct {
	ID                    string `json:"id,omitempty"`
	DisplayName           string `json:"displayName,omitempty"`
	Email                 string `json:"email,omitempty"`
	FirstName             string `json:"firstName,omitempty"`
	LastName              string `json:"lastName,omitempty"`
	EmployeeID            string `json:"employeeId,omitempty"`
	EmployeeDisplayNumber string `json:"employeeDisplayNumber,omitempty"`
}

type roFee struct {
	ID           string   `json:"id"`
	FeeCode      string   `json:"feeCode"`
	Description  string   `json:"description"`
	Type         string   `json:"type"`
	Amount       string   `json:"amount"`
	IsOverridden bool     `json:"isOverridden"`
	MinAmount    float32  `json:"minAmount"`
	MaxAmount    float32  `json:"maxAmount"`
	SalePricing  *pricing `json:"salePricing"`
	CostPricing  *pricing `json:"costPricing"`
}

type pricing struct {
	MinAmount   float32 `json:"minAmount"`
	MaxAmount   float32 `json:"maxAmount"`
	Percentage  int     `json:"percentage"`
	FlatPrice   string  `json:"flatPrice"`
	PricingType string  `json:"pricingType"`
}

type roCustomer struct {
	ID                         string     `json:"id"`
	ArcID                      string     `json:"arcId"`
	CustomerType               string     `json:"customerType"`
	FirstName                  string     `json:"firstName"`
	MiddleName                 string     `json:"middleName"`
	LastName                   string     `json:"lastName"`
	CompanyName                string     `json:"companyName"`
	Status                     string     `json:"status"`
	Note                       string     `json:"note,omitempty"`
	PreferredCommunicationMode string     `json:"preferredCommunicationMode,omitempty"`
	Phones                     []*roPhone `json:"phones"`
	PreferredContactType       string     `json:"preferredContactType"`
	Email                      string     `json:"email"`
	Address                    *roAddress `json:"address"`
}

func (rc *roCustomer) getHomePhone() string {
	for _, phone := range rc.Phones {
		if phone.PhoneType == phoneTypeHome {
			return phone.Number
		}
	}
	return ""
}

func (rc *roCustomer) getWorkPhone() string {
	for _, phone := range rc.Phones {
		if phone.PhoneType == phoneTypeWork {
			return phone.Number
		}
	}
	return ""
}

func (rc *roCustomer) getCellPhone() string {
	for _, phone := range rc.Phones {
		if phone.PhoneType == phoneTypeCell {
			return phone.Number
		}
	}
	return ""
}

type roPhone struct {
	PhoneType string `json:"phoneType"`
	Number    string `json:"number"`
	IsPrimary bool   `json:"isPrimary"`
}

type roAddress struct {
	Address    string `json:"line1"`
	Address2   string `json:"line2"`
	City       string `json:"city"`
	County     string `json:"county"`
	State      string `json:"state"`
	PostalCode string `json:"zip"`
	Country    string `json:"country"`
}

type roVehicle struct {
	ID                   string     `json:"id,omitempty"`
	VIN                  string     `json:"vin,omitempty"`
	Year                 string     `json:"year"`
	Make                 string     `json:"make"`
	Model                string     `json:"model"`
	Color                string     `json:"color,omitempty"`
	MileageIn            *unitValue `json:"mileageIn,omitempty"`
	MileageOut           *unitValue `json:"mileageOut,omitempty"`
	LastServiceDateEpoch int64      `json:"lastServiceDate,omitempty"`
	License              *roVehicle `json:"license,omitempty"`
}

// getLastServiceDate will return the time.Time representation of the last service date and a boolean indicating if the time is valid.
func (r *roVehicle) getLastServiceDate() (time.Time, bool) {
	return getTimeFromEpoch(r.LastServiceDateEpoch)
}

type roVehicleLicense struct {
	Plate   string `json:"plate,omitempty"`
	State   string `json:"state"`
	Country string `json:"country"`
}

type job struct {
	ID                 string      `json:"id"`
	CreatedTime        int64       `json:"createdTime"`
	ModifiedTimeEpoch  int64       `json:"modifiedTime"`
	VoidedTimeEpoch    int64       `json:"voidedTime"`
	IsDeleted          bool        `json:"isDeleted"`
	Type               string      `json:"type"`
	PayType            string      `json:"payType"`
	IsSublet           bool        `json:"isSublet"`
	Concern            string      `json:"concern"`
	Causes             []*cause    `json:"causes"`
	TechIDs            []string    `json:"techIds"`
	TechnicianDetails  []*employee `json:"technicianDetails"`
	Status             string      `json:"status"`
	PartStatus         string      `json:"partStatus"`
	JobNumber          string      `json:"jobNumber"`
	IsHold             bool        `json:"isHold"`
	CompletedTimeEpoch int64       `json:"completedTime"`
	// SubletJobs is defined as an array of objects, but no information on what the object structure is,
	// so commenting out the definition of the SubletJobs field for now.
	// SubletJobs []
	Notes            string       `json:"notes"`
	TotalLaborAmount string       `json:"totalLaborAmount"`
	Operations       []*operation `json:"operations"`
}

type operation struct {
	ID                   string       `json:"id"`
	CreatedTimeEpoch     int64        `json:"createdTime,omitempty"`
	ModifiedTimeEpoch    int64        `json:"modifiedTime,omitempty"`
	IsDeleted            bool         `json:"isDeleted,omitempty"`
	OpCode               string       `json:"opcode,omitempty"`
	OpCodeDescription    string       `json:"opcodeDescription,omitempty"`
	Category             string       `json:"category,omitempty"`
	Source               string       `json:"source,omitempty"`
	Type                 string       `json:"type,omitempty"`
	EstimatedAmount      string       `json:"estimatedAmount,omitempty"`
	BillingRate          float64      `json:"billingRate,omitempty"`
	BillingTimeInSeconds int          `json:"billingTimeInSeconds,omitempty"`
	PayType              string       `json:"payType,omitempty"`
	LaborTimeInSeconds   int          `json:"laborTimeInSeconds,omitempty"`
	WarrantyOemOpCode    string       `json:"warrantyOemOpCode,omitempty"`
	StoryLines           []*storyLine `json:"storyLines"`
	Coupons              string       `json:"coupons,omitempty"`
	Parts                []*roPart    `json:"parts"`
}

type storyLine struct {
	ModifiedTime int64  `json:"modifiedTime,omitempty"`
	DealerID     string `json:"dealerId,omitempty"`
	Source       string `json:"source,omitempty"`
	Type         string `json:"type,omitempty"`
	OpCode       string `json:"opcode,omitempty"`
	RoID         string `json:"roId,omitempty"`
	UserID       string `json:"userId,omitempty"`
	JobID        string `json:"jobId,omitempty"`
	Deleted      bool   `json:"deleted,omitempty"`
	OperationID  string `json:"operationId,omitempty"`
	SiteID       string `json:"siteId,omitempty"`
	CreatedTime  int64  `json:"createdTime,omitempty"`
	VIN          string `json:"vin,omitempty"`
	ID           string `json:"id,omitempty"`
	Text         string `json:"text,omitempty"`
	Status       string `json:"status,omitempty"`
}

type roPart struct {
	ID                       int    `json:"id"`
	CreatedTimeEpoch         int64  `json:"createdTime"`
	ModifiedTimeEpoch        int64  `json:"modifiedTime"`
	IsDeleted                bool   `json:"isDeleted"`
	PartNumber               string `json:"partNumber"`
	PartDescription          string `json:"partDescription"`
	Unit                     string `json:"unit"`
	Status                   string `json:"status"`
	Type                     string `json:"type"`
	IsEligibleForClaim       bool   `json:"isEligibleForClaim"`
	ApprovedQuantity         int    `json:"approvedQty"`
	HoldQuantity             int    `json:"holdQty"`
	DeliveredQuantity        int    `json:"deliveredQty"`
	SorQuantity              int    `json:"sorQty"`
	ReturnQuantity           int    `json:"returnQty"`
	ReservedQuantity         int    `json:"reservedQuantity"`
	ShortageQuantity         int    `json:"shortageQty"`
	SellingPricePerPart      string `json:"sellingPricePerPart"`
	ListPricePerPart         string `json:"listPricePerPart,omitempty"`
	CostPricePerPart         string `json:"costPricePerPart,omitempty"`
	TotalPrice               string `json:"totalPrice"`
	TradePricePerPart        string `json:"tradePricePerPart,omitempty"`
	OEMAdjustmentCostPerPart string `json:"oemAdjustmentCostPerPart,omitempty"`
}

type cause struct {
	ID        string `json:"id"`
	CauseText string `json:"causeText"`
}

type invoice struct {
	ID                string        `json:"id,omitempty"`
	CreatedTimeEpoch  int64         `json:"createdTime,omitempty"`
	ModifiedTimeEpoch int64         `json:"modifiedTime,omitempty"`
	IsDeleted         bool          `json:"isDeleted,omitempty"`
	InvoiceNumber     string        `json:"invoiceNumber,omitempty"`
	InvoiceAmount     string        `json:"invoiceAmount,omitempty"`
	CustomerPay       *pay          `json:"customerPay,omitempty"`
	InsurancePay      *insurancePay `json:"insurancePay,omitempty"`
	WarrantyPay       *pay          `json:"warrantyPay,omitempty"`
	InternalPay       *pay          `json:"internalPay,omitempty"`
}

type pay struct {
	AdjustedLaborCostAmount                 string `json:"adjustedLaborCostAmount,omitempty"`
	AdjustedPartCostAmount                  string `json:"adjustedPartCostAmount,omitempty"`
	Amount                                  string `json:"amount,omitempty"`
	ClosedBy                                string `json:"closedBy,omitempty"`
	ClosedTimeEpoch                         int64  `json:"closedTime,omitempty"`
	DeductibleAmount                        string `json:"deductibleAmount,omitempty"`
	DeductibleTax                           string `json:"deductibleTax,omitempty"`
	DueAmount                               string `json:"dueAmount,omitempty"`
	EstimateAmount                          string `json:"estimateAmount,omitempty"`
	FlatSplitAmount                         string `json:"flatSplitAmount,omitempty"`
	FlatSpliteAmountTax                     string `json:"flatSpliteAmountTax,omitempty"`
	InitialLaborCostAmount                  string `json:"initialLaborCostAmount,omitempty"`
	LaborAmount                             string `json:"laborAmount,omitempty"`
	LaborCostAmount                         string `json:"laborCostAmount,omitempty"`
	PartCoreAmount                          string `json:"partCoreAmount,omitempty"`
	PartCoreReturnAmount                    string `json:"partCoreReturnAmount,omitempty"`
	PartCostAmount                          string `json:"partCostAmount,omitempty"`
	PartCouponsAmount                       string `json:"partCouponsAmount,omitempty"`
	PartCouponsAmountForGrossCalculation    string `json:"partCouponsAmountForGrossCalculation,omitempty"`
	PartFeeAmount                           string `json:"partFeeAmount,omitempty"`
	PartFeeCostAmount                       string `json:"partFeeCostAmount,omitempty"`
	PartFeeTax                              string `json:"partFeeTax,omitempty"`
	PartTax                                 string `json:"partTax,omitempty"`
	PartsAmount                             string `json:"partsAmount,omitempty"`
	PartsTotalAmount                        string `json:"partsTotalAmount,omitempty"`
	PostTaxTotal                            string `json:"postTaxTotal,omitempty"`
	PreTaxTotal                             string `json:"preTaxTotal,omitempty"`
	ResidueTax                              string `json:"residueTax,omitempty"`
	RoCouponTax                             string `json:"roCouponTax,omitempty"`
	RoCouponsAmount                         string `json:"roCouponsAmount,omitempty"`
	RoFeeAmount                             string `json:"roFeeAmount,omitempty"`
	RoFeeCostAmount                         string `json:"roFeeCostAmount,omitempty"`
	ServiceCouponsAmount                    string `json:"serviceCouponsAmount,omitempty"`
	ServiceCouponsAmountForGrossCalculation string `json:"serviceCouponsAmountForGrossCalculation,omitempty"`
	ServiceFeeAmount                        string `json:"serviceFeeAmount,omitempty"`
	ServiceFeeCostAmount                    string `json:"serviceFeeCostAmount,omitempty"`
	ServiceFeeTax                           string `json:"serviceFeeTax,omitempty"`
	ServiceTax                              string `json:"serviceTax,omitempty"`
	ServiceTotalAmount                      string `json:"serviceTotalAmount,omitempty"`
	Status                                  string `json:"status,omitempty"`
	SubletCostAmount                        string `json:"subletCostAmount,omitempty"`
	SubletLaborCostAmount                   string `json:"subletLaborCostAmount,omitempty"`
	SubletLaborSaleAmount                   string `json:"subletLaborSaleAmount,omitempty"`
	SubletLaborTax                          string `json:"subletLaborTax,omitempty"`
	SubletPartCostAmount                    string `json:"subletPartCostAmount,omitempty"`
	SubletPartSaleAmount                    string `json:"subletPartSaleAmount,omitempty"`
	SubletPartTax                           string `json:"subletPartTax,omitempty"`
	SubletSaleAmount                        string `json:"subletSaleAmount,omitempty"`
	SubletTax                               string `json:"subletTax,omitempty"`
	ThirdPartyTax                           string `json:"thirdPartyTax,omitempty"`
	TotalBillingTimeInSeconds               int    `json:"totalBillingTimeInSeconds,omitempty"`
	TotalDiscount                           string `json:"totalDiscount,omitempty"`
	TotalTax                                string `json:"totalTax,omitempty"`
}

type insurancePay struct {
	Status    string `json:"status,omitempty"`
	Amount    string `json:"amount,omitempty"`
	DueAmount string `json:"dueAmount,omitempty"`
}
