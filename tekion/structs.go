package tekion

type request struct {
	NextFetch<PERSON>ey string `json:"nextFetchKey,omitempty"`
}
type errorResponse struct {
	Status       string `json:"status"`
	ErrorDetails struct {
		Code    string   `json:"errorCode"`
		Message string   `json:"debugMessage"`
		Params  []string `json:"params"`
		Key     string   `json:"key"`
	} `json:"errorDetails"`
}

type responseMeta struct {
	Status       string `json:"status"`
	Count        int    `json:"count"`
	Total        int    `json:"total"`
	Pages        int    `json:"pages"`
	CurrentPage  int    `json:"currentPage"`
	NextFetchKey string `json:"nextFetchKey"`
}

type unitValue struct {
	Value float32 `json:"value,omitempty"`
	Unit  string  `json:"unit,omitempty"`
}
type authRequest struct {
	AccessKey string `json:"access-key"`
	SecretKey string `json:"secret-key"`
}

type authResponse struct {
	AccessToken string `json:"access_token"`
	ExpiresIn   int    `json:"expires_in"`
	TokenType   string `json:"token_type"`
}
