package tekion

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"phizz/conf"
	"phizz/db"
	"phizz/dms"
	"phizz/util"
	"time"

	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
)

type key string

const (
	urlAuth = "/auth/v1/oauth2/token"
	urlDeal = "/api/v2.2/deal"
	urlRo   = "/api/v2/repairorder"

	contextDealerIDKey key = "dealerID"
)

func readErrorResponse(resp []byte) (*errorResponse, error) {
	var errResp errorResponse

	err := json.Unmarshal(resp, &errResp)
	if err != nil {
		err = errors.WithMessagef(err, "[%s] error unmarshaling error response", db.DMSProviderTekion)
		return nil, err
	}

	return &errResp, nil
}

func execRequest(ctx context.Context, dealerID string, path string, params *url.Values) ([]byte, error) {
	tCtx := context.WithValue(ctx, contextDealerIDKey, dealerID)

	resp, status, err := util.Request(tCtx, db.DMSProviderTekion, path, http.MethodGet, params, nil, setRequestAuthorization, setRequestHeaders, removePassword, getLogLevel)
	if err != nil {
		err = errors.WithMessagef(err, "[%s] error executing request", db.DMSProviderTekion)
		return nil, err
	}

	if status != http.StatusOK {
		errResp, err := readErrorResponse(resp)
		if err != nil {
			err = errors.WithMessagef(err, "[%s] error reading error response", db.DMSProviderTekion)
			return nil, err
		}
		err = errors.WithMessagef(err, "[%s] received non-success response. Tekion Status: %s, Error Message: %s, Error Code: %s, Key: %s", db.DMSProviderTekion, errResp.Status, errResp.ErrorDetails.Message, errResp.ErrorDetails.Code, errResp.ErrorDetails.Key)
		return nil, err
	}

	return resp, nil
}

func retrieveRO(ctx context.Context, dealerID string, roNumber string) (*roResponse, error) {
	c := conf.Get().Tekion
	uri := fmt.Sprintf("%s%s", c.Host, urlRo)
	params := &url.Values{
		"roNo": {roNumber},
	}

	resp, err := execRequest(ctx, dealerID, uri, params)
	if err != nil {
		err = errors.WithMessagef(err, "[%s] error getting RO %s for dealer %s", db.DMSProviderTekion, roNumber, dealerID)
		return nil, err
	}

	var ro roResponse
	err = json.Unmarshal(resp, &ro)
	if err != nil {
		err = errors.Wrapf(err, "[%s] error unmarshaling RO response", db.DMSProviderTekion)
		return nil, err
	}

	return &ro, nil
}

func mapROCustomer(customerData *roCustomer) *dms.Customer {
	if customerData == nil {
		return &dms.Customer{}
	}

	cust := &dms.Customer{
		DMSCustomerNumber: customerData.ID,
	}

	if customerData.CustomerType != customerCustomerTypeIndividual {
		cust.IsBusiness = true
		if customerData.CompanyName != "" {
			cust.BusinessName = customerData.CompanyName
		} else {
			cust.BusinessName = customerData.LastName
		}
		cust.Phone = customerData.getWorkPhone()
		if cust.Phone == "" {
			cust.Phone = customerData.getHomePhone()
		}
	} else {
		cust.FirstName = customerData.FirstName
		cust.LastName = customerData.LastName
		cust.Phone = customerData.getHomePhone()
	}

	if customerData.Address != nil {
		addr := customerData.Address
		cust.Address = addr.Address
		if addr.Address2 != "" {
			cust.Address += ", " + addr.Address2
		}
		cust.City = addr.City
		cust.State = addr.State
		cust.PostalCode = addr.PostalCode
	}

	cust.Cellular = customerData.getCellPhone()
	cust.Email = customerData.Email

	return cust
}

func mapROVehicle(vehicleData *roVehicle) *dms.ROVehicle {
	if vehicleData == nil {
		return &dms.ROVehicle{}
	}

	vehicle := &dms.ROVehicle{
		VIN: vehicleData.VIN,
	}

	if vehicleData.MileageIn != nil && vehicleData.MileageIn.Value > 0 {
		vehicle.Odometer = int(vehicleData.MileageIn.Value)
	} else if vehicleData.MileageOut != nil && vehicleData.MileageOut.Value > 0 {
		vehicle.Odometer = int(vehicleData.MileageOut.Value)
	}

	return vehicle
}

func mapROLines(ctx context.Context, ro *roData) []*dms.ROLine {
	if ro == nil {
		return nil
	}
	var roLines []*dms.ROLine
	for _, job := range ro.Jobs {
		if job.IsDeleted {
			continue
		}
		var labors []dms.ROLabor
		var parts []dms.ROPart
		for _, operation := range job.Operations {
			if operation.IsDeleted {
				continue
			}
			var billingHours float64
			if operation.BillingTimeInSeconds > 0 {
				billingHours = float64(float64(operation.BillingTimeInSeconds) / 3600.00)
			}
			labors = append(labors, dms.ROLabor{
				SoldHours: decimal.NewFromFloat(billingHours),
				LaborSale: decimal.NewFromFloat(billingHours * operation.BillingRate),
				LaborType: ro.Jobs[0].Operations[0].Category,
			})
			for _, part := range operation.Parts {
				if part.ApprovedQuantity == 0 || part.IsDeleted {
					continue
				}
				cost, err := decimal.NewFromString(part.CostPricePerPart)
				if err != nil {
					err = errors.WithMessagef(err, "[%s] error converting CostPricePerPart %s to decimal", db.DMSProviderTekion, part.CostPricePerPart)
					util.LogError(ctx, err)
					return roLines
				}
				partSale, err := decimal.NewFromString(part.TotalPrice)
				if err != nil {
					err = errors.WithMessagef(err, "[%s] error converting TotalPrice %s to decimal", db.DMSProviderTekion, part.TotalPrice)
					util.LogError(ctx, err)
					return roLines
				}
				parts = append(parts, dms.ROPart{
					PartNumber:  part.PartNumber,
					Description: part.PartDescription,
					Quantity:    part.ApprovedQuantity,
					PartsSale:   partSale,
					Cost:        cost,
				})
			}
		}

		var causeText string
		if len(job.Causes) > 0 {
			causeText = job.Causes[0].CauseText
		}
		var techID string
		if len(job.TechnicianDetails) > 0 {
			techID = job.TechnicianDetails[0].EmployeeDisplayNumber
		}
		roLines = append(roLines, &dms.ROLine{
			LineCode:             job.JobNumber,
			TechID:               techID,
			ComplaintDescription: job.Concern,
			Cause:                causeText,
			Labors:               labors,
			Parts:                parts,
		})
	}
	return roLines
}

func mapRODetails(ctx context.Context, ro *roData) []dms.RODetail {
	var roDetails []dms.RODetail

	date, ok := getTimeFromEpoch(ro.CreatedTimeEpoch)
	if !ok {
		util.LogError(ctx, errors.Errorf("Invalid OpenDate: %d", ro.CreatedTimeEpoch))
		return roDetails
	}
	var advisorNumber string
	if len(ro.PrimaryAdvisor) > 0 {
		advisorNumber = ro.PrimaryAdvisor[0].EmployeeDisplayNumber
	}
	roDetails = append(roDetails, dms.RODetail{
		Date:           date,
		Vehicle:        *mapROVehicle(ro.Vehicle),
		ROLines:        mapROLines(ctx, ro),
		Customer:       *mapROCustomer(ro.Customer),
		ServiceAdvisor: advisorNumber,
	})
	return roDetails
}

// RODetail fetches the RO from CDK and maps to dms.RODetail
func RODetail(ctx context.Context, dealerID string, roNumber string) ([]dms.RODetail, error) {
	roResponse, err := retrieveRO(ctx, dealerID, roNumber)
	if err != nil {
		err = errors.WithMessagef(err, "[%s] error getting RO %s for dealer %s", db.DMSProviderTekion, roNumber, dealerID)
		return nil, err
	}

	if roResponse.Data == nil || len(roResponse.Data) == 0 {
		return nil, errors.Errorf("[%s] ro %s not found for dealer %s", db.DMSProviderTekion, roNumber, dealerID)
	}
	return mapRODetails(ctx, roResponse.Data[0]), nil
}

// ROCustomer fetches the RO from CDK and maps to dms.RO
func ROCustomer(ctx context.Context, dealerID string, roNumber string) (*dms.RO, error) {
	roResponse, err := retrieveRO(ctx, dealerID, roNumber)
	if err != nil {
		err = errors.WithMessagef(err, "[%s] error getting RO %s for dealer %s", db.DMSProviderTekion, roNumber, dealerID)
		return nil, err
	}

	if roResponse.Data == nil || len(roResponse.Data) == 0 {
		return nil, errors.Errorf("[%s] ro %s not found for dealer %s", db.DMSProviderTekion, roNumber, dealerID)
	}
	var dmsRO dms.RO
	dmsRO.Customer = *mapROCustomer(roResponse.Data[0].Customer)
	return &dmsRO, nil
}

func setAuthRequestAuthorization(ctx context.Context, req *http.Request) (*http.Request, error) {
	return req, nil
}

func setAuthRequestHeaders(ctx context.Context, req *http.Request) (*http.Request, error) {
	c := conf.Get().Tekion
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("client_id", c.ClientID)
	return req, nil
}

func setRequestAuthorization(ctx context.Context, req *http.Request) (*http.Request, error) {
	token, err := execAuthRequest(ctx)
	if err != nil {
		err = errors.WithMessagef(err, "[%s] error getting auth token", db.DMSProviderTekion)
		return nil, err
	}

	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", token))

	return req, nil
}

func setRequestHeaders(ctx context.Context, req *http.Request) (*http.Request, error) {
	c := conf.Get().Tekion
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("client_id", c.ClientID)

	if ctx.Value(contextDealerIDKey) == nil || ctx.Value(contextDealerIDKey).(string) == "" {
		err := errors.Errorf("[%s] %s not found in context", db.DMSProviderTekion, contextDealerIDKey)
		return nil, err
	}

	req.Header.Set("dealerId", ctx.Value(contextDealerIDKey).(string))

	return req, nil
}

func removePassword(s string) string {
	return s
}

func getLogLevel() conf.ExternalAPILogLevel {
	return conf.Get().Tekion.LogLevel
}

// getTimeFromEpoch will convert the epoch time to time.Time and return
// the time and a boolean value indicating if the conversion was successful
func getTimeFromEpoch(epoch int64) (time.Time, bool) {
	if epoch == 0 {
		return time.Time{}, false
	}
	t := time.Unix(0, epoch*int64(time.Millisecond))
	return t, !t.IsZero()
}

func execAuthRequest(ctx context.Context) (string, error) {
	var token string

	c := conf.Get().Tekion
	uri := fmt.Sprintf("%s%s", c.Host, urlAuth)
	body := &url.Values{
		"access-key": {c.AccessKey},
		"secret-key": {c.SecretKey},
	}

	resp, status, err := util.Request(ctx, db.DMSProviderTekion, uri, http.MethodPost, nil, []byte(body.Encode()), setAuthRequestAuthorization, setAuthRequestHeaders, removePassword, getLogLevel)
	if err != nil {
		err = errors.WithMessagef(err, "[%s} error getting auth token", db.DMSProviderTekion)
		return "", err
	}

	if status != http.StatusOK {
		errResp, err := readErrorResponse(resp)
		if err != nil {
			err = errors.WithMessagef(err, "[%s] error reading error response", db.DMSProviderTekion)
			return "", err
		}
		err = errors.WithMessagef(err, "[%s] error getting auth token. Tekion Status: %s, Error Message: %s, Error Code: %s, Key: %s", db.DMSProviderTekion, errResp.Status, errResp.ErrorDetails.Message, errResp.ErrorDetails.Code, errResp.ErrorDetails.Key)
		return "", err
	}

	var authResp authResponse
	err = json.Unmarshal(resp, &authResp)
	if err != nil {
		err = errors.Wrapf(err, "[%s] error unmarshaling auth response", db.DMSProviderTekion)
		return "", err
	}

	token = authResp.AccessToken

	return token, nil
}
