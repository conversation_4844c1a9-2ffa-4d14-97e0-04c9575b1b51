package request

import (
	"net/http"

	chimiddleware "github.com/go-chi/chi/middleware"
)

// ReqIDHeader is a middleware that sets the Request-ID header
// to the value of ReqId from the request context
func ReqIDHeader(next http.Handler) http.Handler {
	fn := func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()
		reqID := chimiddleware.GetReqID(ctx)
		if reqID != "" {
			r.Header.Set("Request-ID", reqID)
		}
		next.ServeHTTP(w, r)
	}

	return http.HandlerFunc(fn)
}
