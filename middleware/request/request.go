package request

import (
	"context"
	"net/http"
)

// Key to use when setting the request
type ctxKeyRequest string

// RequestKey is a value for use with context.WithValue. It is used
// to store a pointer to the http.Request that context came from.
// This facilitates logging additional information about the request
// further down the call stack.
const RequestKey ctxKeyRequest = "httpRequest"

// Request is a middleware that injects the request into the context of each
// request so that additional information about the request can be available
// further down the call stack and can be used to add additional information
// when logging/reporting errors.
func Request(next http.Handler) http.Handler {
	fn := func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()
		ctx = context.WithValue(ctx, RequestKey, r)
		next.ServeHTTP(w, r.WithContext(ctx))
	}

	return http.HandlerFunc(fn)
}

// FromContext returns original http.Request from the context if on is present.
// Returns nil if a request cannot be found.
func FromContext(ctx context.Context) *http.Request {
	if ctx == nil {
		return nil
	}
	if req, ok := ctx.Value(RequestKey).(*http.Request); ok {
		return req
	}
	return nil
}
