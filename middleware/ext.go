package middleware

import (
	"crypto/sha512"
	"encoding/hex"
	"net/http"

	"phizz/conf"
	"phizz/handlers"

	"github.com/pkg/errors"
)

// ExternalAuthenticate is chi middleware for checking if the request has Phizz-Checksum header set
func ExternalAuthenticate(next http.Handler) http.Handler {
	fn := func(w http.ResponseWriter, req *http.Request) {
		givenChecksum := req.Header.Get("Phizz-Checksum")
		salt := []byte(conf.Get().AuthSalt)
		calcChecksum := sha512.Sum512(salt)
		if givenChecksum != hex.EncodeToString(calcChecksum[:]) {
			handlers.Renderer().JSON(w, http.StatusBadRequest, handlers.ErrorMessage(errors.New("Invalid data (bad checksum)"), "Invalid data (bad checksum)", nil))
			return
		}
		next.ServeHTTP(w, req)
	}
	return http.HandlerFunc(fn)
}
