package middleware

import (
	"fmt"
	"log"
	"net/http"

	"github.com/stvp/rollbar"
)

// ReportRecovery is a middleware for recovering from a panic
func ReportRecovery(rw http.ResponseWriter, r *http.Request, next http.HandlerFunc) {
	defer func() {
		if err := recover(); err != nil {

			stack := rollbar.BuildStack(3)
			person := &rollbar.Field{Name: "person", Data: map[string]string{"id": "unknown"}}
			//if user, err := session.CurrentUser(r); err == nil {
			//	person.Data = map[string]string{"id": strconv.Itoa(user.ID), "email": user.Email}
			//}
			rollbar.RequestErrorWithStack(rollbar.CRIT, r, fmt.Errorf("%s", err), stack, person)
			var str string
			for _, f := range stack {
				str += fmt.Sprintf("File \"%s\" line %d in %s\n", f.Filename, f.Line, f.Method)
			}

			log.Printf("PANIC: %s\n%s", err, str)

			rw.WriteHeader(http.StatusInternalServerError)
			rw.Write([]byte("500 Internal Server Error"))
		}
	}()

	next(rw, r)
}
