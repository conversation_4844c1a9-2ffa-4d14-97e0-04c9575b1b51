package middleware

import (
	"net/http"
	"net/url"
	"strings"
	"time"

	"phizz/conf"
	"phizz/db"
	"phizz/handlers"
	"phizz/session"
	"phizz/util"

	"github.com/pkg/errors"
)

const (
	expireDuration = 6 * time.Hour
)

// Authenticate authentication function
func Authenticate(next http.Handler) http.Handler {
	fn := func(w http.ResponseWriter, req *http.Request) {
		user, ok := req.Context().Value(session.ContextKeyCurrentUser).(*db.User)
		if !ok || user == nil {
			handlers.PrepareReauthenticate(w, req)
			handlers.Renderer().JSON(w, http.StatusForbidden, handlers.ErrorMessage(nil, "Session error", nil))

			return
		}

		next.ServeHTTP(w, req)
	}
	return http.HandlerFunc(fn)
}

// Active is chi middleware for checking if the current session is expired
func Active(next http.Handler) http.Handler {
	fn := func(w http.ResponseWriter, req *http.Request) {
		now := time.Now()

		lastActive, err := session.GetLastActive(req)
		if err != nil {
			handlers.PrepareReauthenticate(w, req)
			session.ReportError(req, err)
			handlers.Renderer().JSON(w, http.StatusBadRequest, handlers.ErrorMessage(nil, "Session error", nil))
			return
		}
		if lastActive.Add(expireDuration).Before(now) {
			handlers.PrepareReauthenticate(w, req)
			handlers.Renderer().JSON(w, http.StatusBadRequest, handlers.ErrorMessage(nil, "Session expired", nil))
			return
		}
		err = session.SetLastActive(req, now)
		if err != nil {
			err = errors.Wrap(err, "Set last active error")
			session.ReportError(req, err)
		}

		next.ServeHTTP(w, req)
	}
	return http.HandlerFunc(fn)
}

// SecureHeaders sets security headers in response header
func SecureHeaders(next http.Handler) http.Handler {

	fn := func(w http.ResponseWriter, req *http.Request) {
		config := conf.Get()
		ctx := req.Context()
		serverURL, err := url.Parse(config.AppURL)
		if err != nil {
			util.LogError(ctx, errors.New("error in parsing server URL"+config.AppURL))
		}

		whizURL, err := url.Parse(config.Whiz.BaseURL)
		if err != nil {
			util.LogError(ctx, errors.New("error in parsing phizz server URL"+config.Whiz.BaseURL))
		}
		webPackStr := ""
		if config.AppEnv == "development" {
			webPackURL, err := url.Parse(config.WebPackDev.BaseURL)
			if err != nil {
				util.LogError(ctx, errors.New("error in parsing webpack URL"+config.WebPackDev.BaseURL))
			}
			webPackStr = webPackURL.Host
		}

		serverStr := strings.Join([]string{serverURL.Host, whizURL.Host, webPackStr}, " ")

		cspStr := `script-src __server__  blob:;
		style-src __server__ data: __server__ 'unsafe-inline';
		object-src 'none'; base-uri 'self'`

		cspStr = strings.ReplaceAll(cspStr, "__server__", serverStr)
		w.Header().Set("Content-Security-Policy", cspStr)
		w.Header().Set("Strict-Transport-Security", "max-age=31536000; includeSubDomains; preload")
		w.Header().Set("X-Frame-Options", "DENY")
		w.Header().Set("X-Content-Type-Options", "nosniff")
		w.Header().Set("Cross-Origin-Resource-Policy", "cross-origin")
		w.Header().Set("Cross-Origin-Opener-Policy", "same-origin-allow-popups")
		w.Header().Set("Access-Control-Allow-Origin", serverStr)
		next.ServeHTTP(w, req)
	}
	return http.HandlerFunc(fn)
}
